#pragma once

#include <string>

#include "Cryptography.h"
#include "Logger.h"
#include "YServerTypes.h"

DECLARE_LOG_CATEGORY(LogLoggerInstance, Normal);
DECLARE_LOG_CATEGORY(LogViewer, Normal);
DECLARE_LOG_CATEGORY(LogStaticViewer, Normal);

namespace yserver
{
struct YAuthKey
{
	std::string Public;
	std::string Secret;
	std::string Tag;

	inline bool Valid() const { return !Public.empty(); }

	std::string Sign(EHashAlgorithm algo, const std::string& data) const;
};

struct YEndpoint
{
	std::string Address;
	std::string Challenge;
	YAuthKey Key;
	uint64_t Created = 0U;
	web::QueryString Params;
	web::QueryString ExtraParams;
	std::shared_ptr<boost::asio::steady_timer> TimeoutTask;

	bool CheckAuth(EHashAlgorithm algorithm, const std::string& signature) const;
	std::string Signature(EHashAlgorithm algorithm) const;
	bool IsValid() const;

	virtual ~YEndpoint() {}
};

class YEndpointError : public std::runtime_error
{
   private:
	uint16_t mErrorCode;

   public:
	YEndpointError(yprotocol::YProtocolError error, const std::string& what);
	YEndpointError(YError error, const std::string& what);

	uint16_t ErrorCode() const;
};

class YEndpointAPI;

enum class EInstanceEndpointAction : uint8_t
{
	KeepUntilTimeout,
	KeepForever,
	Delete
};

}    // namespace yserver
