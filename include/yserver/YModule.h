#pragma once

#include <oneapi/tbb/parallel_for_each.h>

#include <concepts>
#include <optional>
#include <thread>

#include "AsyncTaskRunner.h"
#include "TConfiguration.h"
#include "YModuleContainerBase.h"
#include "YPlayer.h"
#include "YUtils.h"
#include "api/YViewerAPI.h"
#include "web/WebUtils.h"

template <typename Iterator, typename Body>
void parallel_for_each_checked(size_t parallelThreshold, Iterator first, Iterator last, const Body& body)
{
	if (std::abs(std::distance(first, last)) > static_cast<ssize_t>(parallelThreshold))
	{
		tbb::parallel_for_each(first, last, body);
	}
	else
	{
		for (Iterator it = first; it != last; it++) body(*it);
	}
}


namespace yserver
{
const std::string ExportedSettingFlag = "export";

struct FModuleStatusInfo
{
	EModuleStatus Status = EModuleStatus::Disabled;
	std::string Description;
	std::map<std::string, ModuleRuntimeError> RuntimeErrors;

	bool operator==(const FModuleStatusInfo& other) const = default;
};

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnReloaded, std::shared_ptr<YModule>);
DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnStatusChanged, const FModuleStatusInfo&, const FModuleStatusInfo&);
DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnServiceStatusChanged, bool);

struct FPolledWorkerThreadInfo
{
	ThreadSafeProperty<bool, std::mutex> bShouldQuit { false };
	std::condition_variable ShouldQuitCVar;
	std::thread Thread;
	std::function<void(bool)> Worker;
	uint64_t Interval = 0;
	uint64_t Variance = 0;

	void Start(const std::string& threadName);
	void Stop();
};

struct FModuleDescriptorOptions
{
	bool bAdmin = false;
	bool bIncludeRawConfig = false;
	std::optional<ELanguage> Language = {};
};

class YPlayerList : public std::map<std::string, PlayerPtr>
{
   public:
	size_t ForEachPlayer(const std::function<void(Player&)>& worker, uint32_t onHostID, std::optional<uint32_t> onProviderID) const;
};

class YViewerList : public std::map<std::string, std::shared_ptr<ViewerInstance>>
{
   public:
	size_t ForEachViewer(const std::function<void(ViewerInstance&)>& worker) const;
};

typedef std::function<void(const imaxa_connection_ptr&, const web::QueryString&)> YModuleQueryHandler;

class ViewerInstance;

class YModule :
    public TConfiguration,
    public yprotocol::EventInterface,
    public YGameClientModuleRequestHandler,
    public LoggingComponent,
    virtual public AsyncTaskRunner,
    public yprotocol::UniquelyIdentifiableObject,
    public std::enable_shared_from_this<YModule>
{
   public:
	typedef std::function<json(const yprotocol::Request&, const PlayerPtr&, const json&, const YAuthKey&)> AdminActionHandler;
	struct BoundAdminAction : public AdminAction
	{
		BoundAdminAction(const AdminAction& action, const AdminActionHandler& handler) : AdminAction(action), Handler(handler) {}
		AdminActionHandler Handler;
	};

   private:
	const std::string mName;
	std::string mDomain;
	std::string mOwner;

	uint64_t mServiceReconnectInterval;

	std::shared_ptr<const EventTemplate> ModuleEvent;
	std::shared_ptr<const EventTemplate> ModuleStatusEvent;
	std::shared_ptr<const EventTemplate> ModuleReconfiguredEvent;

	uint32_t mID;

	uint32_t mMaxPlayers;

	mutable std::condition_variable_any mStatusCV;
	mutable ThreadSafeProperty<EModuleStatus, std::shared_mutex> mStatus = EModuleStatus(EModuleStatus::Disabled);
	mutable std::string mStatusDescription;
	bool SetStatus(EModuleStatus status, const std::string& desc = {}) const;

	FModuleStatusInfo GetStatus_AssumeLocked() const;

	std::pair<FModuleStatusInfo, FModuleStatusInfo> SetStatus_AssumeLocked(EModuleStatus status, const std::string& desc) const;

	bool HandleStatusChanged(const FModuleStatusInfo& oldStatus, const FModuleStatusInfo& newStatus) const;

	void ReconnectThread();

	void StartPoll();
	void EndPoll();

	bool IsFull_AssumeLocked() const;

	YModuleContainerBase* const mContainer;

	std::thread mServiceReconnectThread;

	std::map<std::string, FPolledWorkerThreadInfo> PolledWorkers;

	std::filesystem::path mStorageDir;

	std::optional<ConfigError> mError;

	bool bEnabled = true;

	std::map<std::string, BoundAdminAction> mAdminActions;

	// should be accessed only ba having locked mStatus - use that as a thread guard for this property
	std::map<std::string, ModuleRuntimeError> mModuleRuntimeErrors;

	YAuthKey mAuthKey;

   protected:
	ThreadSafeProperty<YPlayerList, std::shared_mutex> mPlayers;
	ThreadSafeProperty<YViewerList, std::shared_mutex> mViewers;

	YResourcePath mRootPath;

	std::map<std::string, YModuleQueryHandler> mQueryHandlers;

	bool bRequiresService = true;

	void BroadcastEventToPlayers(const yprotocol::Event& event) const;

	void AddPollThread(const std::string& name, uint64_t interval, const std::function<void(bool)>& worker, uint64_t intervalVariance = 0);

	virtual void OnConfigLoaded(const std::filesystem::path& filename) override;

	void ServiceDisconnected(const std::string& error) const;

	FOnServiceStatusChanged OnServiceStatusChanged;

	// Called when a shutdown request is received
	virtual void OnShutdown(EModuleStatus preShutdownStatus);

	// Sets the domain of this module
	virtual void SetDomain(const std::string& domain);

	// the prefix to append to logs created by this module
	virtual std::string LogPrefix() const override;

	virtual bool CheckIsReady(std::string& outNotReadyReason) const { return true; }

	void ReadyStateChanged() const;

	void RegisterAdminAction(const std::string& action, EModuleAdminActionTarget target, const AdminActionHandler& handler,
	                         const std::optional<JsonSchema>& parameterSchema = {});

	// begin EventInterface
	virtual void OnEventTriggered(const yprotocol::Event& event) override;
	void BroadcastEventConditional(const yprotocol::Event& event, const std::function<bool(const Player&)>& condition);
	// end EventInterface

	void OnModuleReconfigured();

	void RaiseRuntimeError(const std::string& errorID, ModuleRuntimeError err);

	void ClearRuntimeError(const std::string& errorID);

   public:
	YModule(YModuleContainerBase* container, const std::string& name);
	YModule(const YModule& copy) = delete;
	virtual ~YModule();

	virtual void Destroy();
	virtual void PostLoad();

	const std::string& Owner() const;

	YModule& operator=(const YModule& rhs) = delete;

	bool SafePlayerAccess(const std::string& uid, const std::function<void(Player&)>& worker = std::function<void(Player&)>()) const;

	// Same as SafePlayerAccess, but for all sessions of a player
	size_t SafePlayerAccess(const AccountID& ID, const std::function<void(Player&)>& worker = std::function<void(Player&)>()) const;

	void BroadcastEventToViewers(const yprotocol::Event& event, const std::string& gameKey = {});

	const std::filesystem::path& StorageDir() const;

	void DropPlayer(Player& player) noexcept;
	bool AddPlayer(const PlayerPtr& player) noexcept;

	void DropViewer(const ViewerInstance& viewer) noexcept;
	bool AddViewer(const std::shared_ptr<ViewerInstance>& viewer) noexcept;

	const YModuleQueryHandler* GetQueryHandler(const std::string& resource) const noexcept;

	void ForEachPlayer(const std::function<void(Player&)>& worker, uint32_t onHostID = 0, uint32_t onProviderID = 0) const;
	void ForEachPlayer(const std::function<void(Player&)>& worker, std::shared_ptr<gamehost::TGameHost> host, std::shared_ptr<provider::TCasinoProvider> provider) const;
	void ForEachViewer(const std::function<void(ViewerInstance&)>& worker) const;

	std::weak_ptr<Player> GetPlayer(const std::string& uid) const;

	virtual void OnRoundFailureResolved(Player& player) noexcept {};
	virtual void OnPlayerGone(Player& player) noexcept {}
	virtual void OnPlayerAdded(Player& player, bool bReconnected) noexcept {}

	virtual void OnViewerGone(ViewerInstance& viewer) noexcept {}
	virtual void OnViewerAdded(ViewerInstance& viewer) noexcept {}

	virtual void OnGameRoundBegin(YGameClient& client) {}
	virtual void OnGameRoundEnd(YGameClient& client, const GameRoundSnapshot& round) {}
	virtual void OnAccountingRoundBegin(YGameClient& client) {}
	virtual void OnAccountingRoundEnd(YGameClient& client, const AccountingRoundSnapshot& round) {}
	virtual void OnTransaction(YGameClient& client, const YTransaction& tr) {}

	size_t NumPlayers() const;

	virtual const char* TypeStr() const { return "unknown"; }

	const YAuthKey& Authorization() const;

	// returns the ID of this module
	uint32_t ID() const;

	FModuleStatusInfo Status() const;

	// Name of this module
	const std::string& Name() const;

	// The directory all relative filenames are resolved with
	const YResourcePath& RootDir() const;

	bool Enabled() const;

	void StartService() noexcept;

	std::optional<ConfigError> GetConfigError() const;

	void SetError(const std::optional<ConfigError>& err);

	// Called when an ID is assigned to this module
	void SetID(uint32_t ID) noexcept;

	void SetOwner(const std::string& owner) noexcept;

	void SetStorageDir(const std::filesystem::path& dir) noexcept;

	// Returns true if an ID has been assigned to this module
	bool IsInitialized() const;

	const std::map<std::string, BoundAdminAction>& GetAdminActions() const;

	const std::map<std::string, ModuleRuntimeError>& GetRuntimeErrors() const;

	// virtual method to override when implementing service connections for modules (eg. casino backend)
	virtual bool ConnectToService(std::string& outError) { return true; }

	virtual bool DisconnectService() { return false; }
	// returns a descriptor with public information about this module
	virtual json GetDescriptor(const FModuleDescriptorOptions& options) const;
	// Called when a player has successfully connected to yserver
	virtual json OnRegister(const YServerClient& client);
	// Called when a player disconnects
	virtual void OnPlayerDisconnected(Player& player) {}

	virtual const JsonSchema& GetStaticConfigSchema() const;

	virtual std::map<std::string, std::string> Versions() const { return {}; }

	// Attempt a connection to this module's service
	void TryServiceConnectNow() noexcept;

	// return true if this module is online
	bool IsOnline() const;

	// return true if this module cannot accept any more players
	bool IsFull() const;

	// A module's domain is a unique identifier in the scope of modules of the same type. Eg. modules of type Roulette with different domain names are guaranteed to be
	// unique - thus two modules cannot have the same domain if they are of the same type
	const std::string& Domain() const;

	// Shutdown this module with the specified message
	void Shutdown(const std::string& msg, bool bReload = false);

	// Return true if a shutdown was requested on this module
	bool IsShuttingDown() const;

	// Return the container in which this module resides
	YModuleContainerBase* Container() const;

	// Set a parameter in persistent storage
	void SetParam(const std::string& name, const std::string& value) const;

	// load a parameter from persistent storage
	std::string GetParam(const std::string& name) const;

	// load a parameter from persistent storage, or create one with the given value if it doesn't exist
	std::string GetOrSetParam(const std::string& name, const std::string& valueToSetIfNotExists) const;

	void DeleteParam(const std::string& name);

	FOnReloaded OnReloaded;
	FOnStatusChanged OnStatusChanged;
};

template <std::derived_from<IntervalSnapshot> RoundSnapshotType_>
using TUnfinishedRoundMap = std::map<std::shared_ptr<PlayerEndpoint>, RoundSnapshotType_>;

template <std::derived_from<IntervalSnapshot> RoundSnapshotType_>
class TUnfinishedRoundMap_Lockable : public TUnfinishedRoundMap<RoundSnapshotType_>, public Lockable<std::shared_mutex>
{
};

template <typename BranchType, std::derived_from<IntervalSnapshot> RoundSnapshotType>
class YModuleBranchContainer;

template <typename Typ_, std::derived_from<IntervalSnapshot> RoundSnapshotType_>
class YModuleBranch : public YModule
{
   public:
	typedef Typ_ BranchType;
	typedef RoundSnapshotType_ RoundSnapshotType;
	typedef TUnfinishedRoundMap<RoundSnapshotType_> UnfinishedRoundMap;
	typedef TUnfinishedRoundMap_Lockable<RoundSnapshotType_> UnfinishedRoundMap_Lockable;
	typedef YModuleBranchContainer<Typ_, RoundSnapshotType_> ContainerType;

   private:
	const Typ_ mType;
	std::shared_ptr<UnfinishedRoundMap_Lockable> mUnfinishedRounds;

   protected:
	virtual void SetDomain(const std::string& domain) override;
	virtual void OnTransaction(YGameClient& client, const YTransaction& tr) override;
	virtual json GetDescriptor(const FModuleDescriptorOptions& options) const override;
	inline virtual const char* TypeStr() const override { return mType._to_string(); }
	virtual void PlayerRoundContinuedOnAnotherEndpoint(Player& player) {};

	UnfinishedRoundMap GetUnfinishedRoundsMap() const;

   public:
	YModuleBranch(YModuleContainerBase* container, const std::string& name, Typ_ type);

	virtual ~YModuleBranch() {}

	static std::string GenUID(BranchType type, const std::string& domain) { return std::string(type._to_string()) + "|" + domain; }

	virtual bool RoundExists(const YPlayerAccount& account, const typename RoundSnapshotType::RoundIDType& roundID);

	virtual void TryRoundRestore(YGameClient& client, const RoundSnapshotType& snap) {}

	virtual void TryRoundResume(Player& player, const RoundSnapshotType& snap);

	const TConfiguration& GetStaticConfig() const;

	// The type of this module
	Typ_ Type() const;

	static const RoundSnapshotType& GetPlayerRoundSnapshot(const YGameClient& player);    // implemented in TCasinoProvider.cpp and TGameHost.cpp
	static const std::string& GetModuleUID(const PlayerEndpoint& endpoint);    // implemented in TCasinoProvider.cpp and TGameHost.cpp

	typename RoundSnapshotType::RoundIDType FindUnfinishedRound(const std::function<bool(const RoundSnapshotType&)>& filter) const;    // implemented in YModuleBranch.cpp
	bool GetUnfinishedRound(const std::shared_ptr<PlayerEndpoint>& endpoint, RoundSnapshotType& outSnapshot) const;    // implemented in YModuleBranch.cpp
	void SaveUnfinishedRound(const Player& player);    // implemented in YModuleBranch.cpp
	void SaveUnfinishedRound(const std::string& endpoint, const std::string& account_uid, const RoundSnapshotType& round);    // implemented in YModuleBranch.cpp
	void ClearUnfinishedRound(const std::shared_ptr<PlayerEndpoint>& playerEndpoint, const RoundSnapshotType& finalResult,
	                          bool bDeleteFromIndex);    // implemented in YModuleBranch.cpp
	void ClearUnfinishedRound(const Player& player);    // implemented in YModuleBranch.cpp
	void ClearUnfinishedRound(const Player& player, const RoundSnapshotType& finalResult);    // implemented in YModuleBranch.cpp
	bool MoveUnfinishedRoundToEndpoint(const typename RoundSnapshotType::RoundIDType& roundID,
	                                   const std::shared_ptr<PlayerEndpoint>& endpoint);    // implemented in YModuleBranch.cpp
	// end of unfinished round stuff
};

}    // namespace yserver
