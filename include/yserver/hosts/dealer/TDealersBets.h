//
// Created by <PERSON><PERSON><PERSON>j on 8. 12. 24.
//

#pragma once

#include "Logger.h"
#include "YSharedTypes.h"
#include "yserver/hosts/TBetsSharedTypes.h"
#include "yserver/hosts/dealer/TDealersHostSharedTypes.h"
#include "yserver/hosts/dealer/TDealersStake.h"

DECLARE_LOG_CATEGORY(LogDealersBets, Normal)

namespace yserver::gamehost
{
BETTER_ENUM(EPlayboardMode, int, Commission, NoCommission);

class TDealersBets : public TConfiguration
{
   protected:
	std::vector<std::shared_ptr<TDealersStake>> mStakes;
	std::map<uint32_t, std::map<std::string, double>> mRTPs;

	bool bOpenVersion = false;

   public:
	virtual ~TDealersBets() = default;
	TDealersBets(bool isOpenVersion = false);

	void OnConfigLoaded(const std::filesystem::path& filename) override;
	int AddStake(const TDealersStake& stake);
	void ClearStakes();

	virtual void VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result, ECardRule cardRule) const;
	virtual FBetVerifyResult VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets, ECardRule cardRule) const;
	virtual double GetMultiplier(std::string betType, int stakeID, ECardRule cardRule, std::string multiplierType = "Default") const;

	static json StakeAsJson(const TDealersStake& stake, uint32_t multiplier);

	const std::vector<std::shared_ptr<TDealersStake>>& GetAllStakes() const;
	const std::vector<std::shared_ptr<TDealersStake>> GetValidStakes() const;
	std::shared_ptr<TDealersStake> GetStake(uint32_t StakeID) const;
	virtual json GetBetTypes() const = 0;
	virtual FRTPInfo GetTotalRTP(uint8_t stakeID, uint8_t numOfDecks, EPlayboardMode playboardMode) const = 0;
	virtual double_t GetVolatility(uint8_t stakeID, uint8_t numOfDecks) const = 0;
};
}    // namespace yserver::gamehost
