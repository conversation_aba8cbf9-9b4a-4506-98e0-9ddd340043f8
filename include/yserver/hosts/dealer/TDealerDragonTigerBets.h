//
// Created by <PERSON><PERSON><PERSON> on 10. 12. 24.
//

#pragma once

#include "yserver/hosts/dealer/TDealersBets.h"
#include "yserver/hosts/dragontiger/TDragonTigerGameLogic.h"

DECLARE_LOG_CATEGORY(LogDealersDragonTigerBets, Normal)

namespace yserver::gamehost
{

class TDealerDragonTigerBets : public TDealersBets
{
   private:
	void CalculateOpenBetsWins(const BetAmounts& firstRoundBets, const BetAmounts& bets, const FDealersStakeInfo& stake,
	                           const dragontiger::DragonTigerGameLogic& gameLogic, CalculateCardGameWinsResult& result) const;

	void CalculateMagicTie(uint64_t& win, uint64_t betOnField, const FDealersStakeInfo& stake, const dragontiger::DragonTigerGameLogic& gameLogic,
	                       const EDragonTigerBetType betType) const;

   public:
	TDealerDragonTigerBets(bool isOpenVersion = false);

	void VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result, ECardRule cardRule) const override;
	FBetVerifyResult VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets, ECardRule cardRule) const override;
	CalculateCardGameWinsResult CalculateWins(BetAmounts Bets, FDealersStakeInfo Stake, const dragontiger::DragonTigerGameLogic& gameLogic,
	                                          const std::optional<BetAmounts>& LastBets) const noexcept;
	double GetMultiplier(std::string betType, int stakeID, ECardRule cardRule, std::string multiplierType = "Default") const override;

	json GetBetTypes() const override;

	FRTPInfo GetTotalRTP(uint8_t stakeID, uint8_t numOfDecks, EPlayboardMode playboardMode) const override;
	double_t GetVolatility(uint8_t stakeID, uint8_t numOfDecks) const override;

	static const JsonSchema& RTPSchema();
};
}    // namespace yserver::gamehost
