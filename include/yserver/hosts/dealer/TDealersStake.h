//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#pragma once

#include "TConfiguration.h"
#include "TDealerGamesExtraData.h"
#include "hosts/TBetsSharedTypes.h"

namespace yserver::gamehost
{
class TDealersStake : public TConfiguration
{
   private:
	std::vector<std::string> mBetTypes;
	std::map<std::string, FieldLimit> mLimitsTable;

	void OnConfigLoaded(const std::filesystem::path& filename) override;
	void ValidateMultiplierConfiguration(const std::string& betTypeName, const json& betTypeConfig) const;

   public:
	TDealersStake();

	void SetMaxBet(std::string fieldType, uint64_t maxBet);
	void SetMinBet(std::string fieldType, uint64_t minBet);
	void SetMultiple(std::string fieldType, uint64_t multiple);
	void SetFieldLimit(std::string fieldType, const FieldLimit& limit);

	FieldLimit GetFieldLimit(std::string fieldType) const;
	std::vector<std::string> GetBetTypes() const;

	uint64_t PlayboardLimitMin;
	uint64_t PlayboardLimitMax;
	unsigned int BetsCountMax;
	uint64_t MaxTableWinLimit;
	std::map<ECardRule, std::map<std::string, BetMultiplier>> MultiplierOverrides;
	std::array<uint64_t, 6> ChipValues;

	void SetSchema(const JsonSchema& schema);
	bool IsValid() const;
	bool IsMultiplierValid(uint64_t chipValue) const;
};
}    // namespace yserver::gamehost
