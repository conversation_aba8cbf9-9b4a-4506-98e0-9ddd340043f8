//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#prag<PERSON> once

#include "hosts/TBetsSharedTypes.h"
#include "hosts/baccarat/TBaccaratGameLogic.h"
#include "hosts/baccarat/TBaccaratStake.h"
#include "yserver/TDealerGamesExtraData.h"
#include "yserver/hosts/dealer/TDealersBets.h"

DECLARE_LOG_CATEGORY(LogDealersBaccaratBets, Normal)

namespace yserver::gamehost
{

BETTER_ENUM(EBaccaratBetProbabilityType, uint8_t, PlayerWin, Banker<PERSON>in, <PERSON>ie, BankerWinExcept6, BankerWinOn6, PlayerPair, BankerPair, Lucky6TwoCard, Lucky6ThreeCards,
            PlayerWin1, BankerWin1, PlayerWin2, BankerWin2, PlayerWin3, BankerWin3, PlayerWin4, Banker<PERSON>in4, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>,
            <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>8, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>);

class TDealerBaccaratBets : public TDealersBets
{
	bool isCommission = false;
	std::map<uint8_t, std::map<EBaccaratBetProbabilityType, double>> mBetProbabilities;    // Number of cards in deck and their probabilities

	uint64_t CalculateWinWithDeductedCommission(uint64_t betOnField, uint32_t multiplier, double percent) const;
	void CalculateSidebetWins(const BetAmounts& bets, const FDealersStakeInfo& stake, const baccarat::BaccaratGameLogic& gameLogic,
	                          CalculateCardGameWinsResult& result) const;
	void CalculateAnteWins(const BetAmounts& bets, const FDealersStakeInfo& stake, const baccarat::BaccaratGameLogic& gameLogic,
	                       CalculateCardGameWinsResult& result) const;
	void CalculateOpenBetsWins(const BetAmounts& firstRoundBets, const BetAmounts& bets, const FDealersStakeInfo& stake, const baccarat::BaccaratGameLogic& gameLogic,
	                           CalculateCardGameWinsResult& result) const;
	std::optional<EBaccaratBetProbabilityType> GetProbabilityType(EBaccaratBetType fieldType, baccarat::EBaccaratSide side) const;

	double_t GetRTP(EBaccaratBetType betType, BetMultiplier betMultiplier, uint8_t numOfDecks, EPlayboardMode playboardMode) const;

   public:
	TDealerBaccaratBets(bool isOpenVersion = false);

	void VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result, ECardRule cardRule) const override;
	FBetVerifyResult VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets, ECardRule cardRule) const override;
	CalculateCardGameWinsResult CalculateWins(BetAmounts Bets, FDealersStakeInfo Stake, const baccarat::BaccaratGameLogic& gameLogic,
	                                          const std::optional<BetAmounts>& FirstBets) const noexcept;
	double GetMultiplier(std::string betType, int stakeID, ECardRule cardRule, std::string multiplierType = "Default") const override;

	json GetBetTypes() const override;
	FRTPInfo GetTotalRTP(uint8_t stakeID, uint8_t numOfDecks, EPlayboardMode playboardMode) const override;
	double_t GetVolatility(uint8_t stakeID, uint8_t numOfDecks) const override;

	static const JsonSchema& RTPSchema();
};
}    // namespace yserver::gamehost
