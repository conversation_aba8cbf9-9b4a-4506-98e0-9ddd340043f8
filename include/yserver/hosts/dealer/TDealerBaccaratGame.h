//
// Created by <PERSON><PERSON><PERSON>nej on 8. 12. 24.
//

#pragma once

#include "hosts/baccarat/TBaccaratBets.h"
#include "hosts/baccarat/TBaccaratGameLogic.h"
#include "hosts/dealer/TDealersGame.h"

namespace yserver
{
namespace gamehost
{
namespace baccarat
{
class TDealerBaccaratGame : public TDealersGame
{
   private:
	BaccaratGameLogic mGameLogic;
	bool mCommission = false;
	bool bShowCardFaces = true;

   public:
	TDealerBaccaratGame(const std::string& host_uid, const GameInformation& info, ECardRule cardRule);

	bool IsCommission() const;
	EBaccaratPlayboardMode GetPlayboardMode() const;
	void AddCard(uint32_t card, uint8_t position) override;
	void AddCutCard(uint32_t position);
	uint8_t GetDealingPhase() const override;
	void FinishGameRound() override;

	void ConfirmBets(const BetAmounts& placedBet) override;
	BetAmounts GetConfirmedBets() const override;
	void ResetConfirmedBets() override;

	std::vector<uint8_t> Evaluate() override;
	json GetGameResult() const;
	json GetCurrentStateOfCards() const override;
	void SetCurrentStateOfCards(const json& cards) override;
	std::vector<uint8_t> GetWinners() const override;
	ECardRule GetCardRule() const override;

	json GetGameRoundData() const override;
	const BaccaratGameLogic& GetGameLogic() const;
	dealer_assist::GameRecordDto GetGameRecord() const override;
	void Unregister(bool bClearWins) override;
	bool IsLastCard() override;
	uint32_t GenerateGoldenCard() override;
	std::optional<uint32_t> GetGoldenCard() override;
	bool ShouldDealtGoldenCard() override;
	uint32_t GetNumberOfCardsOnTable() override;

	bool IsPlayerDecisionNeeded() override;
	json GetGameExtraData() const override;
};
}    // namespace baccarat
}    // namespace gamehost
}    // namespace yserver
