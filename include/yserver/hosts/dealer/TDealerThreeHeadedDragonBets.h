//
// Created by <PERSON><PERSON><PERSON> on 6. 1. 25.
//

#pragma once

#include "yserver/hosts/dealer/TDealersBets.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"

DECLARE_LOG_CATEGORY(LogDealersThreeHeadedDragonBets, Normal)

namespace yserver::gamehost
{

class TDealerThreeHeadedDragonBets : public TDealersBets
{
	void CalculateDragonBetsWins(const BetAmounts& firstRoundBets, const BetAmounts& bets, const FDealersStakeInfo& stake,
	                             const threeheadeddragon::ThreeHeadedDragonGameLogic& gameLogic, CalculateCardGameWinsResult& result) const;
	void CalculateMagicTieWin(const BetAmounts& bets, const FDealersStakeInfo& stake, const threeheadeddragon::ThreeHeadedDragonGameLogic& gameLogic,
	                          CalculateCardGameWinsResult& result) const;

	void Calculate3HeadedDragonSidebetW<PERSON>(const BetAmounts& bets, const FDealersStakeInfo& stake, const threeheadeddragon::ThreeHeadedDragonGameLogic& gameLogic,
	                                      CalculateCardGameWinsResult& result) const;


   public:
	TDealerThreeHeadedDragonBets(bool isOpenVersion = false);

	void VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result, ECardRule cardRule) const override;
	FBetVerifyResult VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets, ECardRule cardRule) const override;
	CalculateCardGameWinsResult CalculateWins(BetAmounts Bets, FDealersStakeInfo Stake, const threeheadeddragon::ThreeHeadedDragonGameLogic& gameLogic,
	                                          const std::optional<BetAmounts>& LastBets) const noexcept;
	double GetMultiplier(std::string betType, int stakeID, ECardRule cardRule, std::string multiplierType = "Default") const override;

	json GetBetTypes() const override;
	FRTPInfo GetTotalRTP(uint8_t stakeID, uint8_t numOfDecks, EPlayboardMode playboardMode) const override;
	double_t GetVolatility(uint8_t stakeID, uint8_t numOfDecks) const override;

	static const JsonSchema& RTPSchema();
};
}    // namespace yserver::gamehost
