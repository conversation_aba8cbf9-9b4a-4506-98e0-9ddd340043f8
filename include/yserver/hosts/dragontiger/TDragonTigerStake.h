//
// Created by <PERSON><PERSON><PERSON> on 30. 8. 24.
//

#pragma once

#include "TConfiguration.h"
#include "TDealerGamesExtraData.h"
#include "hosts/TBetsSharedTypes.h"
#include "hosts/cards/TCardBetSecurityManager.h"
#include "hosts/dragontiger/TDragonTigerHostSharedTypes.h"

namespace yserver::gamehost::dragontiger
{

class TDragonTigerStake : public TConfiguration
{
   private:
	std::array<FieldLimit, EDragonTigerBetType::_size()> mLimitsTable;
	CardBetSecurityManager mSecurityManager;

	void OnConfigLoaded(const std::filesystem::path& filename) override;
	void SetCardBetSecuritySettings(EDragonTigerBetType fieldType, const CardGameBetSecuritySettings& securitySettings);

	static const JsonSchema& MysticWinMultiplierSchema();
	static const JsonSchema& MysticWinWesternMultiplierSchema();
	static const JsonSchema& MagicTieMultiplierSchema();
	static const JsonSchema& OpenWinMultiplierSchema();
	static const JsonSchema& OpenWinWesternMultiplierSchema();

   public:
	TDragonTigerStake();

	void SetMaxBet(EDragonTigerBetType fieldType, uint64_t maxBet);
	void SetMinBet(EDragonTigerBetType fieldType, uint64_t minBet);
	void SetMultiple(EDragonTigerBetType fieldType, uint64_t multiple);
	void SetFieldLimit(EDragonTigerBetType fieldType, const FieldLimit& limit);

	FieldLimit GetFieldLimit(EDragonTigerBetType fieldType) const;
	std::optional<CardGameBetSecuritySettings> GetCardBetSecuritySettings(EDragonTigerBetType fieldType) const;

	uint64_t PlayboardLimitMin;
	uint64_t PlayboardLimitMax;
	unsigned int BetsCountMax;
	uint64_t MaxTableWinLimit;
	std::map<std::string, BetMultiplier> MultiplierOverrides;
	std::array<uint64_t, 6> ChipValues;

	bool IsValid() const;
	bool IsMultiplierValid(uint64_t chipValue) const;

	json BetSecuritySettingsToJson() const;
	json MultiplierOverridesToJson() const;

	static const JsonSchema& StakeSchema();
	static const JsonSchema& OpenStakeSchema();
};


}    // namespace yserver::gamehost::dragontiger
