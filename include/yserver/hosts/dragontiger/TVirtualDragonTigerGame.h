//
// Created by <PERSON><PERSON><PERSON> on 29. 8. 24.
//

#pragma once
#include "YServerTypes.h"
#include "hosts/TBaseVirtualCardGame.h"
#include "hosts/cards/CardGameSharedTypes.h"
#include "hosts/dragontiger/TDragonTigerBets.h"
#include "hosts/dragontiger/TDragonTigerGameLogic.h"

DECLARE_LOG_CATEGORY(LogVirGameDragonTiger, Normal)

namespace yserver
{
namespace gamehost
{
namespace dragontiger
{
class TVirtualDragonTigerHost;

class VirtualDragonTigerGame : public TBaseVirtualCardGame
{
	bool mOpenVersion = false;
	DragonTigerGameLogic mGameLogic;
	std::shared_ptr<const EventTemplate> CardsShuffleEvent;
	uint32_t LockState = 0;
	std::optional<EDragonTigerSide> mRandomSide;

	DragonTigerBetAmounts mConfirmedBets;
	DragonTigerBetAmounts mPlacedBet;

	json mCardDealingConfig;

	bool bInitialGamePlayed = false;
	bool bShowCardFaces = true;
	uint8_t mTigerTailLength;

	void LoadDealingConfiguration(const GameInformation& info);

   public:
	VirtualDragonTigerGame(const std::string& host_uid, const GameInformation& info);

	FStakeInfo StakeInfo;
	uint32_t CreditMultiplier = 1;

	void PlaceBets(const DragonTigerBetAmounts& placedBet);
	void ConfirmedBets(const DragonTigerBetAmounts& confirmedBet);
	void ClearConfirmedBets();
	uint32_t DrawAndAddCard(EDragonTigerSide side, uint32_t position);
	void AddCard(uint32_t card, EDragonTigerSide side);
	void AddCutCard(uint32_t position);
	void FinishGameRound();

	EDragonTigerSide GetRandomSide();
	DragonTigerBetAmounts GetPlacedBets() const;
	DragonTigerBetAmounts GetConfirmedBets() const;
	json GetCurrentStateOfCards() const;
	EDragonTigerWinner Evaluate();
	json GetGameResult();
	std::vector<json> GetCards() const;
	EDragonTigerWinner GetWinner() const;
	uint32_t GetGoldenCard();
	uint8_t GetTigerTailLength() const;
	uint8_t GetNumberOfCardsOnTable() const;

	bool IsOpenVersion() const;
	bool CanPlayFreeHand() const override;
	json PlayFreeHandRound() override;

	const DragonTigerGameLogic& GetGameLogic() const;
	const json& GetCardDealingConfig() const;

	json GetGameRoundData() const override;
	void Restore(const GameRoundSnapshot& snap) override;

	void OnDealFirstCards(const boost::system::error_code& ec, const std::shared_ptr<TVirtualDragonTigerHost>& host, const std::shared_ptr<YGameClient>& client);
	bool IsPlayerDecisionNeeded() override;
};
}    // namespace dragontiger
}    // namespace gamehost
}    // namespace yserver
