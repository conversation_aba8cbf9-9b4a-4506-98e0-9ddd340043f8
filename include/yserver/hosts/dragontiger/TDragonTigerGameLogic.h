//
// Created by <PERSON><PERSON><PERSON> on 20. 8. 24.
//

#pragma once
#include <optional>

#include "Enums.h"
#include "Json.h"
#include "Logger.h"
#include "TDealerGamesExtraData.h"
#include "dealer-assist/dto/GameRecordDto.h"
#include "hosts/cards/TBaseGameLogic.h"
#include "yserver/hosts/cards/CardGameSharedTypes.h"
#include "yserver/hosts/dragontiger/TDragonTigerHostSharedTypes.h"

DECLARE_LOG_CATEGORY(LogDragonTigerGameLogic, Normal)

namespace yserver
{
namespace gamehost
{
namespace dragontiger
{
class DragonTigerGameSide : public GameSide
{
   public:
	DragonTigerGameSide() = default;

	static uint32_t CardValue(uint32_t card, ECardRule cardRule = ECardRule::Asian);
	uint32_t HandValue(ECardRule cardRule) const;
};

class DragonTigerGameLogic : public TBaseGameLogic
{
	std::vector<DragonTigerGameSide> mGameSide;

	EDragonTigerWinner mWinner = EDragonTigerWinner::Null;

	std::optional<uint32_t> mGoldenTieCard;

	int32_t mCutCardPosition = -1;
	bool bShowCutCard = false;

   public:
	DragonTigerGameLogic();
	DragonTigerGameLogic(ECardRule cardRule);
	DragonTigerGameLogic(bool showCutCard, ECardRule cardRule);

	void ClearGame() override;
	void ClearCards() override;
	void AddCard(uint32_t card, uint8_t side) override;
	void AddCutCard(uint32_t position) override;
	void AddOrReplaceCard(uint32_t card, uint8_t side, uint32_t position) override;
	void ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards) override;
	uint32_t GenerateGoldenCard();
	std::optional<uint32_t> GetGoldenCard() const;
	uint8_t GetDealingPhase() const override;
	std::optional<uint8_t> GetWinner() const override;
	std::vector<uint8_t> GetWinners() const override;
	std::vector<uint8_t> Evaluate() override;
	uint8_t GetNumberOfCardsOnTable() const;
	json GetGameResult() const override;
	json GetGameRecordJson() const;
	dealer_assist::GameRecordDto GetGameRecord() const override;
	json GetFreeHandGameResult(bool showCardFaces) const;
	std::vector<json> GetCards(bool showCardFaces) const;
	json GetCards() const;

	bool IsGameFinished() const override;

	void SetCurrentStateOfCards(const json& cards) override;
	json GetCurrentStateOfCards() const override;

	bool IsUnsuitedTie() const;
	bool IsSuitedTie() const;
	bool IsMagicSuitedTie() const;
	bool IsMagicUnsuitedTie() const;
	bool IsMysteryWin(EDragonTigerBetMultiplierType type) const;
	uint32_t GetHandValue(uint8_t side) const override;
	bool HasFaceDownCard(EDragonTigerSide side) const;

	bool HasFaceDownCard(uint32_t side) const override;

	std::unordered_map<std::string, uint32_t> GetHandValues() const;
};
}    // namespace dragontiger
}    // namespace gamehost
}    // namespace yserver
