//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#pragma once

#include "YServerTypes.h"
#include "hosts/dealer/TDealersGame.h"
#include "hosts/dragontiger/TDragonTigerGameLogic.h"

DECLARE_LOG_CATEGORY(LogDragonTigerGame, Normal)

namespace yserver
{
namespace gamehost
{
namespace dragontiger
{
class TDragonTigerGame : public TDealersGame
{
   private:
	DragonTigerGameLogic mGameLogic;
	uint32_t mDragonTailLength = 0;

   public:
	TDragonTigerGame(const std::string& host_uid, const GameInformation& info, ECardRule);

	void SetCurrentStateOfCards(const json& cards) override;
	json GetCurrentStateOfCards() const override;

	void AddCard(uint32_t card, uint8_t position) override;
	uint8_t GetDealingPhase() const override;
	std::vector<uint8_t> Evaluate() override;
	std::vector<uint8_t> GetWinn<PERSON>() const override;
	ECardRule GetCardRule() const override;
	dealer_assist::GameRecordDto GetGameRecord() const override;
	void FinishGameRound() override;
	const DragonTigerGameLogic& GetGameLogic() const;
	void Unregister(bool bClearWins) override;

	void ConfirmBets(const BetAmounts& placedBet) override;
	BetAmounts GetConfirmedBets() const override;
	void ResetConfirmedBets() override;

	bool IsLastCard() override;
	uint32_t GenerateGoldenCard() override;
	std::optional<uint32_t> GetGoldenCard() override;
	bool ShouldDealtGoldenCard() override;
	uint32_t GetNumberOfCardsOnTable() override;

	bool IsPlayerDecisionNeeded() override;
	json GetGameExtraData() const override;
	json GetGameRoundData() const override;
};
}    // namespace dragontiger
}    // namespace gamehost
}    // namespace yserver
