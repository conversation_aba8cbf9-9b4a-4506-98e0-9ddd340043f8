//
// Created by <PERSON><PERSON><PERSON> on 12. 12. 23.
//
#pragma once

#include "YGameClient.h"
#include "YServerTypes.h"
#include "hosts/baccarat/TBaccaratBets.h"
#include "hosts/baccarat/TBaccaratGameLogic.h"

DECLARE_LOG_CATEGORY(LogBaseCardGame, Normal)

namespace yserver
{
namespace gamehost
{
class TBaseVirtualCardGame : public GameInstance
{
   protected:
	uint32_t mNumbersOfDecks = 6;
	uint32_t mMinPercentOfCards = 30;
	uint32_t mMaxPercentOfCards = 70;
	uint32_t mReshuffleThreshold;
	uint32_t mFreeHandsAtStart = 0;
	int32_t mFreeHands = -1;

	uint32_t mFreeHandsCount = 0;
	uint32_t mFreeHandsAtStartCount = 0;

	// Number of played round with current deck
	uint32_t mNumberOfPlayedRounds = 0;
	ECardRule mCardRule = ECardRule::Asian;

	const uint32_t mNumberOfCardsInDeck = 52;

	std::vector<uint32_t> mDeck;
	EReshuffleMode mReshuffleMode = EReshuffleMode::CutCard;

	void ShuffleCards();
	void CalculateShuffleThreshold();
	void InsertCardToDeck(uint32_t card, uint32_t position);

   public:
	TBaseVirtualCardGame(const std::string& host_uid, const GameInformation& info);

	uint32_t DrawCard();
	uint32_t NumberOfCardsLeft() const;
	uint32_t NumberOfCardsInShoe() const;
	uint32_t NumberOfFreeHandsAtStart() const;
	int32_t NumberOfFreeHands() const;
	uint32_t GetNumberOfPlayedRounds() const;
	uint32_t GetNumberOfDecks() const;

	dealer_assist::EDealerAssistPhase GamePhase = dealer_assist::EDealerAssistPhase::NewRoundPreparation;

	void CheckReshuffle();
	void PlayerRequestedReshuffle();
	ECardRule GetCardRule() const;

	static const JsonSchema& BaseVirtualCardGameSchema();

	virtual bool CanPlayFreeHand() const = 0;
	virtual json PlayFreeHandRound() = 0;
};
}    // namespace gamehost
}    // namespace yserver
