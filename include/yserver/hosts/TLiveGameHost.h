#pragma once

#include "TCroupierDatabase.h"
#include "TGameHost.h"
#include "web/SingleWebsocketClient.h"

namespace yserver::gamehost
{
class TLiveGameHost : public virtual TGameHost
{
   protected:
	struct BetsCloseBehavior
	{
		int ReduceBetTimeByMs = 0;
	};

   private:
	std::shared_ptr<const EventTemplate> CroupierChangeEvent;

	std::string CroupierDatabase;
	bool bRequireTableLogin = false;
	CroupierTableSchedule mCroupierTableSchedule;
	ThreadSafeProperty<std::pair<std::string, FCroupier>, std::shared_mutex> LoggedInCroupier;
	std::shared_ptr<web::SingleWebsocketClient> mCroupierTableClient;

	bool ConnectToServiceCroupierTable();

	json GetNextCroupierInfo() const;

   protected:
	virtual void on_croupier_table_msg(const std::string& msg);

   public:
	TLiveGameHost();

	void SetLoggedInCroupier(const std::string& croupierBadgeID);

	bool LogoutCroupier(const std::string& reason, bool bSwappingCroupiers);
	bool LogoutCroupier(ScopedLock<std::shared_mutex>& lock, const std::string& reason, bool bSwappingCroupiers);

	bool RequiresCroupier() const { return bRequireTableLogin; }

	uint32_t GetNumPlayersWithMoney() const;

	virtual bool CheckIsReady(std::string& outNotReadyReason) const override;

	virtual json GetGameState(const std::shared_ptr<const YGameClient>& client) const override;

	virtual void OnConfigLoaded(const std::filesystem::path& filename) override;
	virtual void PostLoad() override;

	virtual bool ConnectToService(std::string& outError) override;

	virtual bool DisconnectService() override;

	virtual json GetDescriptor(const FModuleDescriptorOptions& options) const override;

	virtual void VoidLiveGame(const std::string& reason);
};
};    // namespace yserver::gamehost
