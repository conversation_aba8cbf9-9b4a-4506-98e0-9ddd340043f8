#pragma once    // prevent a header file from being included multiple times

#include "YServerTypes.h"
#include "hosts/TBaseVirtualCardGame.h"
#include "hosts/baccarat/TBaccaratBets.h"
#include "hosts/baccarat/TBaccaratGameLogic.h"
#include "hosts/cards/CardGameSharedTypes.h"
#include "yserver/YGameClient.h"

DECLARE_LOG_CATEGORY(LogVirGameBaccarat, Normal)

namespace yserver
{
namespace gamehost
{
namespace baccarat
{
class TVirtualBaccaratHost;

class VirtualBaccaratGame : public TBaseVirtualCardGame
{
	BaccaratGameLogic mGameLogic;
	std::shared_ptr<const EventTemplate> CardsShuffleEvent;
	uint32_t LockState = 0;
	BaccaratBetAmounts mPlacedBet;

	json mCardDealingConfig;

	bool mOpenBaccarat = false;
	bool mCommission = false;
	bool bInitialGamePlayed = false;
	bool bShowCardFaces = true;

	std::optional<EBaccaratSide> mRandomSide;

	void LoadDealingConfiguration(const GameInformation& info);

   public:
	VirtualBaccaratGame(const std::string& host_uid, const GameInformation& info);

	FStakeInfo StakeInfo;
	uint32_t CreditMultiplier = 1;
	BaccaratBetAmounts ConfirmedBets;

	bool IsCommission() const;
	bool IsOpenBaccarat() const;

	void PlaceBets(BaccaratBetAmounts placedBet);
	uint32_t DrawAndAddCard(EBaccaratSide side, uint32_t position);
	void AddCard(uint32_t card, EBaccaratSide side);
	void AddCutCard(uint32_t position);
	bool ShouldPlayerDrawThirdCard() const;
	bool ShouldBankerDrawThirdCard() const;
	void FinishGameRound();

	uint32_t GetGoldenCard();
	BaccaratBetAmounts GetPlacedBets() const;
	EBaccaratWinner Evaluate();
	json GetGameResult() const;
	std::vector<json> GetCards();
	EBaccaratWinner GetWinner() const;

	// void SetLock(Player& player, ERouletteLockReasons lock, bool bActive);
	bool CanPlayFreeHand() const override;
	json PlayFreeHandRound() override;

	const BaccaratGameLogic& GetGameLogic() const;
	const json& GetCardDealingConfig() const;

	json GetGameRoundData() const override;
	void Restore(const GameRoundSnapshot& snap) override;
	EBaccaratSide GetRandomSide();
	uint8_t GetNumberOfCardsOnTable() const;

	void OnDealFirstCards(const boost::system::error_code& ec, const std::shared_ptr<TVirtualBaccaratHost>& host, const std::shared_ptr<YGameClient>& client);
	bool IsPlayerDecisionNeeded() override;
};
}    // namespace baccarat
}    // namespace gamehost
}    // namespace yserver
