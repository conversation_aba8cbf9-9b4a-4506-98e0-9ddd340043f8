//
// Created by <PERSON><PERSON><PERSON> on 16. 11. 23.
//

#pragma once

#include "TConfiguration.h"
#include "hosts/TBetsSharedTypes.h"
#include "hosts/baccarat/TBaccaratGameLogic.h"
#include "hosts/cards/TCardBetSecurityManager.h"

namespace yserver::gamehost::baccarat
{
struct Multiplier
{
	EBaccaratBetType BetType = EBaccaratBetType::Banker;
	double Value = 0.0;
	uint16_t Numerator = 0;
	uint16_t Denominator = 0;

	Multiplier() = default;    // Default constructor
	Multiplier(EBaccaratBetType betType, double value, uint16_t numerator, uint16_t denominator) :
	    BetType(betType), Value(value), Numerator(numerator), Denominator(denominator) {};
};

class TBaccaratStake : public TConfiguration
{
   public:
	struct FieldLimit
	{
		uint64_t Min = 0;
		uint64_t Max = 0;
		uint64_t Multiple = 0;

		FieldLimit& operator*=(uint64_t multiply);
		FieldLimit operator*(uint64_t multiply) const;
	};

   private:
	CardBetSecurityManager mSecurityManager;
	std::array<FieldLimit, EBaccaratBetType::_size()> LimitsTable;

	void OnConfigLoaded(const std::filesystem::path& filename) override;
	void SetCardBetSecuritySettings(EBaccaratBetType betType, const CardGameBetSecuritySettings& securitySettings);

   public:
	TBaccaratStake();

	void SetMaxBet(EBaccaratBetType fieldType, uint64_t maxBet);
	void SetMinBet(EBaccaratBetType fieldType, uint64_t minBet);
	void SetMultiple(EBaccaratBetType fieldType, uint64_t multiple);
	void SetFieldLimit(EBaccaratBetType fieldType, const FieldLimit& limit);

	FieldLimit GetFieldLimit(EBaccaratBetType fieldType) const;
	std::optional<CardGameBetSecuritySettings> GetCardBetSecuritySettings(EBaccaratBetType betType) const;

	uint64_t PlayboardLimitMin = 0;
	uint64_t PlayboardLimitMax = 0;
	uint32_t BetsCountMax = 0;
	uint64_t MaxTableWinLimit = 0;
	std::map<EBaccaratBetType, BetMultiplier> Multipliers;
	std::array<uint64_t, 6> ChipValues;

	bool IsValid(const bool isCommission = false) const;
	bool IsMultiplierValid(const uint64_t chipValue) const;

	json BetSecuritySettingsToJson() const;
	static const JsonSchema& StakeSchema();
	static const JsonSchema& LiveStakeSchema();
};
}    // namespace yserver::gamehost::baccarat
