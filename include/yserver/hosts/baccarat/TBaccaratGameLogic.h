#pragma once    // prevent a header file from being included multiple times
#include <optional>

#include "BaccaratHostSharedTypes.h"
#include "Enums.h"
#include "Json.h"
#include "Logger.h"
#include "dealer-assist/dto/GameRecordDto.h"
#include "yserver/TDealerGamesExtraData.h"
#include "yserver/hosts/cards/CardGameSharedTypes.h"
#include "yserver/hosts/cards/TBaseGameLogic.h"

DECLARE_LOG_CATEGORY(LogBaccaratGameLogic, Normal)

namespace yserver
{
namespace gamehost
{

namespace baccarat
{

class BaccaratGameSide : public GameSide
{
   public:
	BaccaratGameSide() = default;

	static uint32_t CardValue(uint32_t card);
	uint32_t HandValue() const;
};

class BaccaratGameLogic : public TBaseGameLogic
{
	std::vector<BaccaratGameSide> mGameSide;
	EBaccaratWinner mWinner = EBaccaratWinner::Null;
	int32_t mCutCardPosition = -1;
	bool bShowCutCard;
	std::optional<uint32_t> mGoldenTieCard;

	bool IsSuitedPair(EBaccaratSide side) const;
	bool IsFourCardSameRank() const;
	bool HasSuitedAndOffSuitedPairs() const;
	bool HasSuitedPairs() const;

   public:
	BaccaratGameLogic();
	BaccaratGameLogic(ECardRule cardRule);
	BaccaratGameLogic(bool showCutCard, ECardRule cardRule);
	void ClearGame() override;
	void ClearCards() override;
	bool ShouldPlayerDrawThirdCard() const;
	bool ShouldBankerDrawThirdCard() const;
	bool IsNatural(EBaccaratSide side) const;
	void AddCard(uint32_t card, uint8_t side) override;
	void AddCutCard(uint32_t position) override;
	void AddOrReplaceCard(uint32_t card, uint8_t side, uint32_t position) override;
	void ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards) override;
	uint8_t GetDealingPhase() const override;
	std::optional<uint8_t> GetWinner() const override;
	std::vector<uint8_t> GetWinners() const override;
	std::vector<uint8_t> Evaluate() override;
	uint32_t GetGoldenCard();
	json GetCards() const;
	json GetGameResult() const override;
	dealer_assist::GameRecordDto GetGameRecord() const override;
	std::vector<json> GetCards(bool showCardFaces);
	void SetCurrentStateOfCards(const json& cards) override;
	json GetCurrentStateOfCards() const override;
	json GetFreeHandGameResult(bool showCardFaces);
	bool IsBetTypeWon(EBaccaratBetType type, EBaccaratBetMultiplierType multiplierType = EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Default)) const;
	bool IsPair(EBaccaratSide side) const;
	uint32_t GetHandValue(uint8_t side) const override;
	uint32_t GetNumberOfCardsOnTable() const;
	bool HasFaceDownCard(EBaccaratSide side) const;

	bool HasFaceDownCard(uint32_t side) const override;
	bool IsGameFinished() const override;


	std::unordered_map<std::string, uint32_t> GetHandValues() const;
};
}    // namespace baccarat
}    // namespace gamehost
}    // namespace yserver