#pragma once

#include "jackpot/client/JackpotAdminClient.h"
#include "jackpot/dto/JackpotLevelInfoBase.h"
#include "yserver/TroniusJackpotModule.h"
#include "yserver/hosts/roulette/dto/RouletteJackpotConfigDto.h"

DECLARE_LOG_CATEGORY2(LogRouletteJackpot, Debug, Debug)

namespace yserver
{
namespace gamehost
{
class TRouletteHost;

namespace roulette
{

BETTER_ENUM(ERouletteJackpotType, uint8_t, LuckyNumber, FireballDeluxe);

class RouletteJackpot : public jackpot::TroniusJackpotModuleBase, public std::enable_shared_from_this<RouletteJackpot>
{
	const std::string mGameKey;
	std::weak_ptr<provider::TCasinoProvider> mProvider;
	bool bDemo = true;

	void updateUID();
	DelegateHandle mProviderUIDUpdateHandle;
	DelegateHandle mProviderReloadedHandle;
	DelegateHandle mHostUIDUpdateHandle;

	std::shared_mutex BetsMutex;
	std::list<JackpotLevelInfoBase> WonJackpotLevels;    // the jackpot levels that currently won
	struct BatchedBetReqParams
	{
		boost::promise<double> Promise;
		std::string PlayerName;
		AccountID PlayerRef;
		std::string SessionRef;
		double BetAmount = 0.0;
	};
	/**
	 * The pending bets that are waiting to be commited to jackpot (at bets closed)
	 * - key: the bet reference (normally player.UniqueIdentifier(), which is session ID)
	 * - value: the BatchedBetReqParams instance
	 */
	std::map<std::string, BatchedBetReqParams> PendingBets;

	struct BetResult
	{
		std::string BetID;
		std::string PlayerRef;
		std::string PlayerName;
		double WonAmount = 0.0;
	};
	std::unordered_map<std::string, std::unordered_map<std::string, BetResult>> BetRefToBetResultPerLevel;

	void setProvider(const std::shared_ptr<provider::TCasinoProvider>& provider, bool bDemo);

	boost::future<void> ResolvePendingWinBetsAsLost(const std::string& levelId) const;

	void ValidateClientLevels();
	void UpdateJackpotLevelRoundProbability(const JackpotLevelInfoBase& level);

	void PrepareJackpotClient();

   protected:
	TRouletteHost* mHost = nullptr;
	RouletteJackpotConfig mConfig;
	ERouletteJackpotType mType;
	std::string PotID;

	ThreadSafeProperty<std::string, std::shared_mutex> clientConfigError;

	struct LevelWinMetadata
	{
		/** The probability that this level will be won in any round */
		double WinProbability;
		/** The probability that this level will be played in any round */
		double RoundProbability;
		/** The average payout of this level */
		double AvgPayout;
		/** The level increment (part of bet that goes to jackpot pot) */
		double Increment;
	};
	/** The levelIds and their win metadata (win and round probability, and avg payout */
	std::unordered_map<std::string, LevelWinMetadata> LevelsWinMetadata;

	std::unique_ptr<JackpotAdminClient> AdminClient;

	std::list<JackpotLevelInfoBase> NotifyExternalTrigger(const std::function<bool(const JackpotLevelDto&)>& levelWon, bool isFlagged = false);
	void SendOutPendingBets(const std::function<void(bool bDoubleZero)>& preProcess, uint64_t roundId);

   public:
	RouletteJackpot(ERouletteJackpotType type, const RouletteJackpotConfig& config, TRouletteHost* host, const std::shared_ptr<provider::TCasinoProvider>& provider,
	                const std::string& gameKey, bool bDemo);
	virtual ~RouletteJackpot() override;

	virtual void shutdown();

	static std::string GetUID(ERouletteJackpotType type, bool doubleZero, const std::string& gameKey, const std::shared_ptr<provider::TCasinoProvider>& provider,
	                          bool bDemo);
	std::shared_ptr<provider::TCasinoProvider> GetProvider() const;
	std::string GetGameKey() const;
	bool IsDemo() const;

	boost::future<double> CommitBetsToPot(YGameClient& player, double totalBetMoney) override;
	virtual void SendOutPendingBets(uint64_t roundId) = 0;

	virtual void ResetRound();

	struct LevelWinInfo
	{
		struct WinnerInfo
		{
			std::string BetRef;
			std::string PlayerRef;
			std::string PlayerName;
			double WonAmount;
		};
		std::list<WinnerInfo> Winners;
		double TotalWon = 0.0;
	};
	LevelWinInfo ProcessPotentialWinners(const std::list<std::string>& winners);
	virtual std::list<JackpotLevelInfoBase> NotifyExternalTrigger(int winNumber, bool isFlagged = false) = 0;

	// history
   private:
	ThreadSafeProperty<json, std::shared_mutex> HistoryJSON;
	std::string GetHistoryKey() const;
	json GetHistoryFromDB(TRouletteHost* host) const;

	static JackpotClientConfig GetConfigFromIdAndProvider(ERouletteJackpotType type, TRouletteHost* host, const std::shared_ptr<provider::TCasinoProvider>& provider,
	                                                      const std::string& gameKey, bool bDemo);

   protected:
	void AddHistory(const json& historyEntry);

	static std::string GetGameTypeStr(ERouletteJackpotType type, bool doubleZero);
	static std::string GetDemoStr(const bool bDemo);

   public:
	json GetHistoryJSON() const;
};
}    // namespace roulette
}    // namespace gamehost
}    // namespace yserver
