#pragma once

#include "yserver/hosts/roulette/RouletteJackpot.h"
#include "yserver/hosts/roulette/dto/LuckyNumberJackpotHistoryDto.h"

namespace yserver
{
namespace gamehost
{
namespace roulette
{

class LuckyNumberJackpot : public RouletteJackpot
{
	/* key: the level ID, value: the lucky number */
	std::unordered_map<std::string, int> JackpotLuckyNumbers;

	void GenerateLuckyRound(bool bDoubleZero);

   public:
	LuckyNumberJackpot(const RouletteJackpotConfig& config, TRouletteHost* host, const std::shared_ptr<provider::TCasinoProvider>& provider, const std::string& gameKey,
	                   bool bDemo);
	~LuckyNumberJackpot() override;

	std::unordered_map<std::string, int> GetLuckyNumbers() const;

	void AddHistory(const LuckyNumberJackpotHistory& historyEntry);

	std::list<JackpotLevelInfoBase> NotifyExternalTrigger(int winNumber, bool isFlagged = false) override;

	void SendOutPendingBets(uint64_t roundId) override;

	void ResetRound() override;

	virtual json GetCurrentState() const override;
};

}    // namespace roulette
}    // namespace gamehost
}    // namespace yserver
