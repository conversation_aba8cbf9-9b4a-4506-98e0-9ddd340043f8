//
// Created by <PERSON><PERSON><PERSON> on 24. 9. 24.
//

#pragma once

#include <optional>

#include "Enums.h"
#include "Logger.h"
#include "TDealerGamesExtraData.h"
#include "dealer-assist/dto/GameRecordDto.h"
#include "hosts/cards/TBaseGameLogic.h"
#include "yserver/hosts/cards/CardGameSharedTypes.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonSharedTypes.h"

DECLARE_LOG_CATEGORY(LogThreeHeadedDragonGameLogic, Normal)

namespace yserver
{
namespace gamehost
{
namespace threeheadeddragon
{
class ThreeHeadedDragonGameSide : public GameSide
{
   public:
	ThreeHeadedDragonGameSide() = default;
	~ThreeHeadedDragonGameSide() override = default;

	static uint32_t CardValue(uint32_t card, ECardRule cardRule);
	uint32_t HandValue(ECardRule cardRule) const;
};

class ThreeHeadedDragonGameLogic : public TBaseGameLogic
{
	std::vector<ThreeHeadedDragonGameSide> mGameSide;
	std::vector<EThreeHeadedDragonWinner> mWinners = { EThreeHeadedDragonWinner::Null };
	int32_t mCutCardPosition = -1;
	std::optional<uint32_t> mGoldenCard;
	bool bShowCutCard;

   public:
	ThreeHeadedDragonGameLogic();
	ThreeHeadedDragonGameLogic(ECardRule cardRule);
	ThreeHeadedDragonGameLogic(bool showCutCard, ECardRule cardRule);
	void ClearGame() override;
	void ClearCards() override;
	void AddCard(uint32_t card, uint8_t side) override;
	void AddCutCard(uint32_t position) override;
	void AddOrReplaceCard(uint32_t card, uint8_t side, uint32_t position) override;
	void ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards) override;

	uint8_t GetDealingPhase() const override;
	std::optional<uint8_t> GetWinner() const override;
	std::vector<uint8_t> GetWinners() const override;
	std::vector<uint8_t> Evaluate() override;
	uint8_t GetNumberOfCardsOnTable() const;

	bool IsGameFinished() const override;

	json GetGameResult() const override;
	json GetFreeHandGameResult(bool showCardFaces) const;
	dealer_assist::GameRecordDto GetGameRecord() const override;

	void SetCurrentStateOfCards(const json& cards) override;
	json GetCurrentStateOfCards() const override;

	uint32_t GenerateGoldenCard();
	std::optional<uint32_t> GetGoldenCard() const;
	bool IsUnsuitedTie(EThreeHeadedDragonSide side) const;
	bool IsSuitedTie(EThreeHeadedDragonSide side) const;
	bool IsMagicSuitedTie(EThreeHeadedDragonSide side) const;
	bool IsMagicUnsuitedTie(EThreeHeadedDragonSide side) const;
	bool Is3HeadedDragonWin() const;
	bool Is3HeadedDragonWinFor(EThreeHeadedDragonSide side, EThreeHeadedDragonMultiplierType type) const;

	bool IsWinner(const EThreeHeadedDragonSide& side) const;
	bool HasFaceDownCard(EThreeHeadedDragonSide side) const;
	bool HasFaceDownCard(uint32_t side) const override;
	uint32_t GetHandValue(uint8_t side) const override;

	std::unordered_map<std::string, uint32_t> GetHandValues() const;
};
};    // namespace threeheadeddragon
};    // namespace gamehost
};    // namespace yserver
