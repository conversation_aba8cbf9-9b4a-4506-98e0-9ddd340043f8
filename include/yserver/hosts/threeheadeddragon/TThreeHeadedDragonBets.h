//
// Created by <PERSON><PERSON><PERSON> on 26. 9. 24.
//

#pragma once

#include "Enums.h"
#include "Logger.h"
#include "yserver/hosts/TBetsSharedTypes.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonSharedTypes.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonStake.h"

DECLARE_LOG_CATEGORY(Log3HeadedDragonBets, Normal)

namespace yserver::gamehost::threeheadeddragon
{
const int MAX_BET_CHIP_VALUE = 33333;
// TODO; rewrite to use DealerGamesExtraData
struct ThreeHeadedDragonBetAmounts
{
   private:
	std::map<EThreeHeadedDragonBetType, uint32_t> mBets;

   public:
	const std::map<EThreeHeadedDragonBetType, uint32_t>& Get() const;
	uint64_t GetBetAmount(EThreeHeadedDragonBetType type) const;

	void Clear() noexcept;
	bool Empty() const noexcept;

	json BetsAsJSON(uint32_t creditMultiplier) const noexcept;

	static ThreeHeadedDragonBetAmounts FromUserJSON(const json& val, uint32_t credit_multiplier);
	static ThreeHeadedDragonBetAmounts FromJSON(const json& val, uint32_t credit_multiplier);
};

struct FStakeInfo
{
	int ID = -1;
	std::shared_ptr<TThreeHeadedDragonStake> Stake;
	uint32_t Multiplier = 1;
};

class TThreeHeadedDragonBets
{
	std::vector<std::shared_ptr<TThreeHeadedDragonStake>> mStakes;
	std::map<uint32_t, std::map<std::string, double>> mRTPs;

	void CheckBetSecurity(const TThreeHeadedDragonStake& stake, const ThreeHeadedDragonBetAmounts& bets, uint32_t numOfRounds, FBetVerifyResult& result) const;
	void VerifyBetRaise(const ThreeHeadedDragonBetAmounts& currentBets, const ThreeHeadedDragonBetAmounts& confirmedBets, FBetVerifyResult& result) const;
	void CalculateMagicTie(uint64_t betOnField, EThreeHeadedDragonBetType betType, const FStakeInfo& stake, const ThreeHeadedDragonGameLogic& gameLogic,
	                       uint64_t& win) const;
	void CalculateThreeHeadedDragonSidebetWin(uint64_t betOnField, EThreeHeadedDragonBetType betType, const FStakeInfo& stake,
	                                          const ThreeHeadedDragonGameLogic& gameLogic, uint64_t& win) const;

   public:
	TThreeHeadedDragonBets();

	int AddStake(const TThreeHeadedDragonStake& stake);
	void ClearStakes();
	FBetVerifyResult VerifyBets(const ThreeHeadedDragonBetAmounts& bets, const FStakeInfo& stake, uint32_t numOfRounds,
	                            const std::optional<ThreeHeadedDragonBetAmounts>& confirmedBets) const;
	CalculateCardGameWinsResult CalculateWins(const ThreeHeadedDragonBetAmounts& bets, const FStakeInfo& stake,
	                                          const ThreeHeadedDragonGameLogic& gameLogic) const noexcept;
	double GetMultiplier(EThreeHeadedDragonBetType type, int stakeID, EThreeHeadedDragonMultiplierType multiplierType = EThreeHeadedDragonMultiplierType::Default) const;

	static json StakeAsJson(const TThreeHeadedDragonStake& stake, uint32_t multiplier);

	const std::vector<std::shared_ptr<TThreeHeadedDragonStake>>& GetAllStakes() const;
	std::vector<std::shared_ptr<TThreeHeadedDragonStake>> GetValidStakes() const;
	std::shared_ptr<TThreeHeadedDragonStake> GetStake(uint32_t stakeID) const;
};
}    // namespace yserver::gamehost::threeheadeddragon
