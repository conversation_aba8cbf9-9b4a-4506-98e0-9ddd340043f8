//
// Created by <PERSON><PERSON><PERSON> on 6. 1. 25.
//

#pragma once

#include "YServerTypes.h"
#include "hosts/dealer/TDealersGame.h"
#include "hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"

DECLARE_LOG_CATEGORY(LogThreeHeadedDragonGame, Normal)

namespace yserver
{
namespace gamehost
{
namespace threeheadeddragon
{
class TThreeHeadedDragonGame : public TDealersGame
{
   private:
	ThreeHeadedDragonGameLogic mGameLogic;
	uint32_t mDragonTailLength;

   public:
	TThreeHeadedDragonGame(const std::string& host_uid, const GameInformation& info, ECardRule cardRule);

	void SetCurrentStateOfCards(const json& cards) override;
	json GetCurrentStateOfCards() const override;

	void AddCard(uint32_t card, uint8_t position) override;
	uint8_t GetDealingPhase() const override;
	std::vector<uint8_t> Evaluate() override;
	std::vector<uint8_t> GetWinners() const override;
	ECardRule GetCardRule() const override;
	dealer_assist::GameRecordDto GetGameRecord() const override;
	void FinishGameRound() override;
	const ThreeHeadedDragonGameLogic& GetGameLogic() const;
	void Unregister(bool bClearWins) override;

	void ConfirmBets(const BetAmounts& placedBet) override;
	BetAmounts GetConfirmedBets() const override;
	void ResetConfirmedBets() override;

	bool IsLastCard() override;
	uint32_t GenerateGoldenCard() override;
	std::optional<uint32_t> GetGoldenCard() override;
	bool ShouldDealtGoldenCard() override;
	uint32_t GetNumberOfCardsOnTable() override;

	bool IsPlayerDecisionNeeded() override;
	json GetGameExtraData() const override;
	json GetGameRoundData() const override;
};
}    // namespace threeheadeddragon
}    // namespace gamehost
}    // namespace yserver
