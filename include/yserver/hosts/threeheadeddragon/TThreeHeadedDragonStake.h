//
// Created by <PERSON><PERSON><PERSON> on 26. 9. 24.
//

#pragma once

#include "TConfiguration.h"
#include "TDealerGamesExtraData.h"
#include "hosts/TBetsSharedTypes.h"
#include "hosts/cards/TCardBetSecurityManager.h"
#include "hosts/threeheadeddragon/TThreeHeadedDragonSharedTypes.h"

namespace yserver::gamehost::threeheadeddragon
{
struct BetMetadata
{
	std::map<EThreeHeadedDragonMultiplierType, uint32_t> Multipliers;
	BetMetadata() = default;
	BetMetadata(const std::map<EThreeHeadedDragonMultiplierType, uint32_t>& items) : Multipliers(items) {};

	json ToJson() const;
	std::optional<uint32_t> GetMultiplier(EThreeHeadedDragonMultiplierType type) const;
};

class TThreeHeadedDragonStake : public TConfiguration
{
   private:
	std::array<FieldLimit, EThreeHeadedDragonBetType::_size()> mLimitsTable;
	CardBetSecurityManager mSecurityManager;

	void SetCardBetSecuritySettings(EThreeHeadedDragonBetType fieldType, const CardGameBetSecuritySettings& security);
	void OnConfigLoaded(const std::filesystem::path& filename) override;

	static const JsonSchema& MagicTieMultiplierSchema();
	static const JsonSchema& ThreeHeadedDragonMultiplierSchema();
	static const JsonSchema& ThreeHeadedDragonWesternMultiplierSchema();
	static const JsonSchema& DragonMultiplierSchema();
	static const JsonSchema& DragonWesternMultiplierSchema();

   public:
	TThreeHeadedDragonStake();

	void SetMaxBet(EThreeHeadedDragonBetType fieldType, uint64_t maxBet);
	void SetMinBet(EThreeHeadedDragonBetType fieldType, uint64_t minBet);
	void SetMultiple(EThreeHeadedDragonBetType fieldType, uint64_t multiple);
	void SetFieldLimit(EThreeHeadedDragonBetType fieldType, const FieldLimit& limit);

	std::optional<CardGameBetSecuritySettings> GetCardBetSecuritySettings(EThreeHeadedDragonBetType fieldType) const;
	FieldLimit GetFieldLimit(EThreeHeadedDragonBetType fieldType) const;

	uint64_t PlayboardLimitMin;
	uint64_t PlayboardLimitMax;
	unsigned int BetsCountMax;
	uint64_t MaxTableWinLimit;
	std::map<std::string, BetMetadata> MultiplierOverrides;
	std::array<uint64_t, 6> ChipValues;

	bool IsValid() const;

	json BetSecuritySettingsToJson() const;
	static const JsonSchema& StakeSchema();
};
}    // namespace yserver::gamehost::threeheadeddragon
