//
// Created by <PERSON><PERSON><PERSON> on 14. 2. 25.
//

#pragma once

#include "Enums.h"
#include "Json.h"
#include "Logger.h"
#include "dealer-assist/dto/GameRecordDto.h"
#include "yserver/hosts/cards/CardGameSharedTypes.h"

namespace yserver
{
namespace gamehost
{
class TBaseGameLogic
{
   protected:
	ECardRule mCardRule = ECardRule::Asian;

   public:
	explicit TBaseGameLogic(const ECardRule cardRule = ECardRule::Asian) : mCardRule(cardRule) {};
	virtual ~TBaseGameLogic() = default;
	virtual void AddCard(uint32_t card, uint8_t side) = 0;
	virtual void AddCutCard(uint32_t position) = 0;
	virtual void AddOrReplaceCard(uint32_t card, uint8_t side, uint32_t position) = 0;
	virtual void ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards) = 0;
	virtual uint8_t GetDealingPhase() const = 0;
	virtual std::optional<uint8_t> GetWinner() const = 0;
	virtual std::vector<uint8_t> GetWinners() const = 0;
	virtual std::vector<uint8_t> Evaluate() = 0;
	virtual uint32_t GetHandValue(uint8_t side) const = 0;
	virtual void ClearGame() = 0;
	virtual void ClearCards() = 0;

	virtual json GetGameResult() const = 0;
	virtual dealer_assist::GameRecordDto GetGameRecord() const = 0;

	virtual bool IsGameFinished() const = 0;
	virtual bool HasFaceDownCard(uint32_t side) const = 0;

	virtual void SetCurrentStateOfCards(const json& cards) = 0;
	virtual json GetCurrentStateOfCards() const = 0;

	ECardRule GetCardRule() const { return mCardRule; }
};
}    // namespace gamehost
}    // namespace yserver
