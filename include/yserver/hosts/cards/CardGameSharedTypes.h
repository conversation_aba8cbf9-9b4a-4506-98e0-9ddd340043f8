//
// Created by <PERSON><PERSON><PERSON> on 20. 8. 24.
//

#pragma once

#include "Enums.h"

namespace yserver
{
namespace gamehost
{

BETTER_ENUM(EReshuffleMode, uint8_t, EveryHand, CutCard, CutCardHidden)
BETTER_ENUM(ECardRule, uint8_t, Asian, Western)
BETTER_ENUM(ECardsDealingRule, uint8_t, PlayersFaceUp, PlayersFaceDown)

struct GameSide
{
   protected:
	std::vector<uint32_t> mHand;

   public:
	virtual ~GameSide() = default;

	std::optional<uint32_t> CardAt(uint32_t index) const;
	uint32_t NumberOfCards() const;
	void ClearCards();
	void AddCard(uint32_t card, uint32_t position = std::numeric_limits<uint32_t>::max());
	void AddOrReplaceCard(uint32_t card, uint32_t position);
	bool HasFaceDownCard() const;
};
}    // namespace gamehost
}    // namespace yserver
