// Copyright (c) 2024 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=9c7d613d1256bd6353f5102ece5a04e572f20627$
//

#ifndef CEF_INCLUDE_CAPI_CEF_COMMAND_HANDLER_CAPI_H_
#define CEF_INCLUDE_CAPI_CEF_COMMAND_HANDLER_CAPI_H_
#pragma once

#include "include/capi/cef_base_capi.h"
#include "include/capi/cef_browser_capi.h"

#ifdef __cplusplus
extern "C" {
#endif

///
/// Implement this structure to handle events related to commands. The functions
/// of this structure will be called on the UI thread.
///
typedef struct _cef_command_handler_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Called to execute a Chrome command triggered via menu selection or
  /// keyboard shortcut. Values for |command_id| can be found in the
  /// cef_command_ids.h file. |disposition| provides information about the
  /// intended command target. Return true (1) if the command was handled or
  /// false (0) for the default implementation. For context menu commands this
  /// will be called after cef_context_menu_handler_t::OnContextMenuCommand.
  /// Only used with Chrome style.
  ///
  int(CEF_CALLBACK* on_chrome_command)(
      struct _cef_command_handler_t* self,
      struct _cef_browser_t* browser,
      int command_id,
      cef_window_open_disposition_t disposition);

  ///
  /// Called to check if a Chrome app menu item should be visible. Values for
  /// |command_id| can be found in the cef_command_ids.h file. Only called for
  /// menu items that would be visible by default. Only used with Chrome style.
  ///
  int(CEF_CALLBACK* is_chrome_app_menu_item_visible)(
      struct _cef_command_handler_t* self,
      struct _cef_browser_t* browser,
      int command_id);

  ///
  /// Called to check if a Chrome app menu item should be enabled. Values for
  /// |command_id| can be found in the cef_command_ids.h file. Only called for
  /// menu items that would be enabled by default. Only used with Chrome style.
  ///
  int(CEF_CALLBACK* is_chrome_app_menu_item_enabled)(
      struct _cef_command_handler_t* self,
      struct _cef_browser_t* browser,
      int command_id);

  ///
  /// Called during browser creation to check if a Chrome page action icon
  /// should be visible. Only called for icons that would be visible by default.
  /// Only used with Chrome style.
  ///
  int(CEF_CALLBACK* is_chrome_page_action_icon_visible)(
      struct _cef_command_handler_t* self,
      cef_chrome_page_action_icon_type_t icon_type);

  ///
  /// Called during browser creation to check if a Chrome toolbar button should
  /// be visible. Only called for buttons that would be visible by default. Only
  /// used with Chrome style.
  ///
  int(CEF_CALLBACK* is_chrome_toolbar_button_visible)(
      struct _cef_command_handler_t* self,
      cef_chrome_toolbar_button_type_t button_type);
} cef_command_handler_t;

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_CEF_COMMAND_HANDLER_CAPI_H_
