// Copyright (c) 2024 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=d98482eba93dcd8b6a6f69b2732162733c73203d$
//

#ifndef CEF_INCLUDE_CAPI_CEF_AUDIO_HANDLER_CAPI_H_
#define CEF_INCLUDE_CAPI_CEF_AUDIO_HANDLER_CAPI_H_
#pragma once

#include "include/capi/cef_base_capi.h"
#include "include/capi/cef_browser_capi.h"

#ifdef __cplusplus
extern "C" {
#endif

///
/// Implement this structure to handle audio events.
///
typedef struct _cef_audio_handler_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Called on the UI thread to allow configuration of audio stream parameters.
  /// Return true (1) to proceed with audio stream capture, or false (0) to
  /// cancel it. All members of |params| can optionally be configured here, but
  /// they are also pre-filled with some sensible defaults.
  ///
  int(CEF_CALLBACK* get_audio_parameters)(struct _cef_audio_handler_t* self,
                                          struct _cef_browser_t* browser,
                                          cef_audio_parameters_t* params);

  ///
  /// Called on a browser audio capture thread when the browser starts streaming
  /// audio. OnAudioStreamStopped will always be called after
  /// OnAudioStreamStarted; both functions may be called multiple times for the
  /// same browser. |params| contains the audio parameters like sample rate and
  /// channel layout. |channels| is the number of channels.
  ///
  void(CEF_CALLBACK* on_audio_stream_started)(
      struct _cef_audio_handler_t* self,
      struct _cef_browser_t* browser,
      const cef_audio_parameters_t* params,
      int channels);

  ///
  /// Called on the audio stream thread when a PCM packet is received for the
  /// stream. |data| is an array representing the raw PCM data as a floating
  /// point type, i.e. 4-byte value(s). |frames| is the number of frames in the
  /// PCM packet. |pts| is the presentation timestamp (in milliseconds since the
  /// Unix Epoch) and represents the time at which the decompressed packet
  /// should be presented to the user. Based on |frames| and the
  /// |channel_layout| value passed to OnAudioStreamStarted you can calculate
  /// the size of the |data| array in bytes.
  ///
  void(CEF_CALLBACK* on_audio_stream_packet)(struct _cef_audio_handler_t* self,
                                             struct _cef_browser_t* browser,
                                             const float** data,
                                             int frames,
                                             int64_t pts);

  ///
  /// Called on the UI thread when the stream has stopped. OnAudioSteamStopped
  /// will always be called after OnAudioStreamStarted; both functions may be
  /// called multiple times for the same stream.
  ///
  void(CEF_CALLBACK* on_audio_stream_stopped)(struct _cef_audio_handler_t* self,
                                              struct _cef_browser_t* browser);

  ///
  /// Called on the UI or audio stream thread when an error occurred. During the
  /// stream creation phase this callback will be called on the UI thread while
  /// in the capturing phase it will be called on the audio stream thread. The
  /// stream will be stopped immediately.
  ///
  void(CEF_CALLBACK* on_audio_stream_error)(struct _cef_audio_handler_t* self,
                                            struct _cef_browser_t* browser,
                                            const cef_string_t* message);
} cef_audio_handler_t;

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_CEF_AUDIO_HANDLER_CAPI_H_
