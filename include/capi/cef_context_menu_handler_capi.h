// Copyright (c) 2024 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool and should not edited
// by hand. See the translator.README.txt file in the tools directory for
// more information.
//
// $hash=3ae7dbb24ec7a95a2f4d4e390c9b6622221c2f42$
//

#ifndef CEF_INCLUDE_CAPI_CEF_CONTEXT_MENU_HANDLER_CAPI_H_
#define CEF_INCLUDE_CAPI_CEF_CONTEXT_MENU_HANDLER_CAPI_H_
#pragma once

#include "include/capi/cef_base_capi.h"
#include "include/capi/cef_browser_capi.h"
#include "include/capi/cef_frame_capi.h"
#include "include/capi/cef_menu_model_capi.h"

#ifdef __cplusplus
extern "C" {
#endif

struct _cef_context_menu_params_t;

///
/// Callback structure used for continuation of custom context menu display.
///
typedef struct _cef_run_context_menu_callback_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Complete context menu display by selecting the specified |command_id| and
  /// |event_flags|.
  ///
  void(CEF_CALLBACK* cont)(struct _cef_run_context_menu_callback_t* self,
                           int command_id,
                           cef_event_flags_t event_flags);

  ///
  /// Cancel context menu display.
  ///
  void(CEF_CALLBACK* cancel)(struct _cef_run_context_menu_callback_t* self);
} cef_run_context_menu_callback_t;

///
/// Callback structure used for continuation of custom quick menu display.
///
typedef struct _cef_run_quick_menu_callback_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Complete quick menu display by selecting the specified |command_id| and
  /// |event_flags|.
  ///
  void(CEF_CALLBACK* cont)(struct _cef_run_quick_menu_callback_t* self,
                           int command_id,
                           cef_event_flags_t event_flags);

  ///
  /// Cancel quick menu display.
  ///
  void(CEF_CALLBACK* cancel)(struct _cef_run_quick_menu_callback_t* self);
} cef_run_quick_menu_callback_t;

///
/// Implement this structure to handle context menu events. The functions of
/// this structure will be called on the UI thread.
///
typedef struct _cef_context_menu_handler_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Called before a context menu is displayed. |params| provides information
  /// about the context menu state. |model| initially contains the default
  /// context menu. The |model| can be cleared to show no context menu or
  /// modified to show a custom menu. Do not keep references to |params| or
  /// |model| outside of this callback.
  ///
  void(CEF_CALLBACK* on_before_context_menu)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame,
      struct _cef_context_menu_params_t* params,
      struct _cef_menu_model_t* model);

  ///
  /// Called to allow custom display of the context menu. |params| provides
  /// information about the context menu state. |model| contains the context
  /// menu model resulting from OnBeforeContextMenu. For custom display return
  /// true (1) and execute |callback| either synchronously or asynchronously
  /// with the selected command ID. For default display return false (0). Do not
  /// keep references to |params| or |model| outside of this callback.
  ///
  int(CEF_CALLBACK* run_context_menu)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame,
      struct _cef_context_menu_params_t* params,
      struct _cef_menu_model_t* model,
      struct _cef_run_context_menu_callback_t* callback);

  ///
  /// Called to execute a command selected from the context menu. Return true
  /// (1) if the command was handled or false (0) for the default
  /// implementation. See cef_menu_id_t for the command ids that have default
  /// implementations. All user-defined command ids should be between
  /// MENU_ID_USER_FIRST and MENU_ID_USER_LAST. |params| will have the same
  /// values as what was passed to on_before_context_menu(). Do not keep a
  /// reference to |params| outside of this callback.
  ///
  int(CEF_CALLBACK* on_context_menu_command)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame,
      struct _cef_context_menu_params_t* params,
      int command_id,
      cef_event_flags_t event_flags);

  ///
  /// Called when the context menu is dismissed irregardless of whether the menu
  /// was canceled or a command was selected.
  ///
  void(CEF_CALLBACK* on_context_menu_dismissed)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame);

  ///
  /// Called to allow custom display of the quick menu for a windowless browser.
  /// |location| is the top left corner of the selected region. |size| is the
  /// size of the selected region. |edit_state_flags| is a combination of flags
  /// that represent the state of the quick menu. Return true (1) if the menu
  /// will be handled and execute |callback| either synchronously or
  /// asynchronously with the selected command ID. Return false (0) to cancel
  /// the menu.
  ///
  int(CEF_CALLBACK* run_quick_menu)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame,
      const cef_point_t* location,
      const cef_size_t* size,
      cef_quick_menu_edit_state_flags_t edit_state_flags,
      struct _cef_run_quick_menu_callback_t* callback);

  ///
  /// Called to execute a command selected from the quick menu for a windowless
  /// browser. Return true (1) if the command was handled or false (0) for the
  /// default implementation. See cef_menu_id_t for command IDs that have
  /// default implementations.
  ///
  int(CEF_CALLBACK* on_quick_menu_command)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame,
      int command_id,
      cef_event_flags_t event_flags);

  ///
  /// Called when the quick menu for a windowless browser is dismissed
  /// irregardless of whether the menu was canceled or a command was selected.
  ///
  void(CEF_CALLBACK* on_quick_menu_dismissed)(
      struct _cef_context_menu_handler_t* self,
      struct _cef_browser_t* browser,
      struct _cef_frame_t* frame);
} cef_context_menu_handler_t;

///
/// Provides information about the context menu state. The functions of this
/// structure can only be accessed on browser process the UI thread.
///
typedef struct _cef_context_menu_params_t {
  ///
  /// Base structure.
  ///
  cef_base_ref_counted_t base;

  ///
  /// Returns the X coordinate of the mouse where the context menu was invoked.
  /// Coords are relative to the associated RenderView's origin.
  ///
  int(CEF_CALLBACK* get_xcoord)(struct _cef_context_menu_params_t* self);

  ///
  /// Returns the Y coordinate of the mouse where the context menu was invoked.
  /// Coords are relative to the associated RenderView's origin.
  ///
  int(CEF_CALLBACK* get_ycoord)(struct _cef_context_menu_params_t* self);

  ///
  /// Returns flags representing the type of node that the context menu was
  /// invoked on.
  ///
  cef_context_menu_type_flags_t(CEF_CALLBACK* get_type_flags)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the URL of the link, if any, that encloses the node that the
  /// context menu was invoked on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_link_url)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the link URL, if any, to be used ONLY for "copy link address". We
  /// don't validate this field in the frontend process.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_unfiltered_link_url)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the source URL, if any, for the element that the context menu was
  /// invoked on. Example of elements with source URLs are img, audio, and
  /// video.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_source_url)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns true (1) if the context menu was invoked on an image which has
  /// non-NULL contents.
  ///
  int(CEF_CALLBACK* has_image_contents)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the title text or the alt text if the context menu was invoked on
  /// an image.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_title_text)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the URL of the top level page that the context menu was invoked
  /// on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_page_url)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the URL of the subframe that the context menu was invoked on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_frame_url)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the character encoding of the subframe that the context menu was
  /// invoked on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_frame_charset)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the type of context node that the context menu was invoked on.
  ///
  cef_context_menu_media_type_t(CEF_CALLBACK* get_media_type)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns flags representing the actions supported by the media element, if
  /// any, that the context menu was invoked on.
  ///
  cef_context_menu_media_state_flags_t(CEF_CALLBACK* get_media_state_flags)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the text of the selection, if any, that the context menu was
  /// invoked on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_selection_text)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns the text of the misspelled word, if any, that the context menu was
  /// invoked on.
  ///
  // The resulting string must be freed by calling cef_string_userfree_free().
  cef_string_userfree_t(CEF_CALLBACK* get_misspelled_word)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns true (1) if suggestions exist, false (0) otherwise. Fills in
  /// |suggestions| from the spell check service for the misspelled word if
  /// there is one.
  ///
  int(CEF_CALLBACK* get_dictionary_suggestions)(
      struct _cef_context_menu_params_t* self,
      cef_string_list_t suggestions);

  ///
  /// Returns true (1) if the context menu was invoked on an editable node.
  ///
  int(CEF_CALLBACK* is_editable)(struct _cef_context_menu_params_t* self);

  ///
  /// Returns true (1) if the context menu was invoked on an editable node where
  /// spell-check is enabled.
  ///
  int(CEF_CALLBACK* is_spell_check_enabled)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns flags representing the actions supported by the editable node, if
  /// any, that the context menu was invoked on.
  ///
  cef_context_menu_edit_state_flags_t(CEF_CALLBACK* get_edit_state_flags)(
      struct _cef_context_menu_params_t* self);

  ///
  /// Returns true (1) if the context menu contains items specified by the
  /// renderer process.
  ///
  int(CEF_CALLBACK* is_custom_menu)(struct _cef_context_menu_params_t* self);
} cef_context_menu_params_t;

#ifdef __cplusplus
}
#endif

#endif  // CEF_INCLUDE_CAPI_CEF_CONTEXT_MENU_HANDLER_CAPI_H_
