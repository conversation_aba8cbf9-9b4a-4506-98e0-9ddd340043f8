/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   											*
 *   <EMAIL>   														*
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
#pragma once

#include "common/TRouletteBaseDBSrv.h"

class RouletteMasterDBSrv : public TRouletteBaseDBSrv
{
   public:
	int GetLastGameInfo(long* pGameId, short* pLastPhase, short* pWinNumber);

	/*---------------------------------------------------------------------------------------------------
	FUNCTION: ExecuteDBService()

	DESC:	executes DBService function on database, used for regular dbservice execution

	PARAMS: none

	RESULT: 0 = OK; 0<> failure
	---------------------------------------------------------------------------------------------------*/
	int ExecuteDBService();
};
