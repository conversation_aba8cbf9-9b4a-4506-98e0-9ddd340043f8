/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
#ifndef __GM_COUNTERS_H__
#define __GM_COUNTERS_H__

namespace gmcounters
{

/* INSTANCE HANDLE */
typedef void* ThInst;

/*---------------------------------------------------------------------------------------------------
FUNCTION: Increment()

DESC:	Increments the counter for the specified values

PARAMS: hInst - instance handle

          CounterIndex - index of counter(1,2,3)

          Counts - number of to increment

RESULT: 0 = OK; 0<> failure;
---------------------------------------------------------------------------------------------------*/
int Increment(ThInst hInst, unsigned short CounterIndex, unsigned long Counts);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Resume()

DESC:	Resumes the counters to start counting - default is always enabled, this should be called if
        Counters were previusly disabled with a call to PauseCounters.

PARAMS: hInst - instance handle

RESULT: 0 = OK; 0<> failure
---------------------------------------------------------------------------------------------------*/
int Resume(ThInst hInst);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Pause()

DESC:	Pauses the counters if they are running. You can restart counters with a call to ResumeCounters()
        or CounterIncrement() or InitDevice()

PARAMS: hInst - instance handle

RESULT: 0 = OK; 0<> failure
---------------------------------------------------------------------------------------------------*/
int Pause(ThInst hInst);

/*---------------------------------------------------------------------------------------------------
FUNCTION: SetPercent()

DESC:

PARAMS: hInst - instance handle
         Percent - value

RESULT: 0 = OK; 0<> failure
---------------------------------------------------------------------------------------------------*/
int SetPercent(ThInst hInst, long Percent);

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetPercent()

DESC:

PARAMS: hInst - instance handle
         Percent - value

RESULT: 0 = OK; 0<> failure
---------------------------------------------------------------------------------------------------*/
int GetPercent(ThInst hInst);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()

DESC:	Creates an gmcounters instance and returns its handle

PARAMS:

RESULT: InstanceHandle, NULL - failure
---------------------------------------------------------------------------------------------------*/
ThInst Init();

/*---------------------------------------------------------------------------------------------------
FUNCTION: Destroy()

DESC:	Destroys the gmcounters instance

PARAMS: hInst-instance handle

RESULT: 0 = OK; 0<> failure
---------------------------------------------------------------------------------------------------*/
int Destroy(ThInst hInst);



}    // namespace gmcounters

#endif /* __GM_COUNTERS_H__ */
