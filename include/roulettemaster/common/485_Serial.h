/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/

#ifndef __485_SERIAL_H__
#define __485_SERIAL_H__


#define MAX_SEND_RETRIES 50
#define MAX_READ_RETRIES 50

#include <termios.h>

namespace serial485
{

typedef void* ThInst;
/*---------------------------------------------------------------------------------------------------
STRUKTURA: TMessage

DESC: 	definicija strukture, za izmenjavo vsebine sporočila

PARAMS:	Commamnd - bajt z ukazom
                pData - kazalec na buffer s podatki
                DataLength - dolžina podatkov v bajtih
---------------------------------------------------------------------------------------------------*/
typedef struct TMessage
{
	char* pData; /* MUST BE ALLOCATED USING esAlocateDataSpaceInMessage AND FREED USING esFreeDataSpaceInMessage */
	unsigned char DataLength;
	unsigned char Address; /* whom to send or who sent */
} TMessage;



/*---------------------------------------------------------------------------------------------------
FUNCTION: CreateMessage()

DESC:	funkcija ustvari strukturo message in rezervira ustrezno število bajtov za pData

PARAMS: Address - komu je paket namenjen
                pData - kazalec na podatke
                DataLength - dolžina podatkov

RESULT: NULL - napaka; kazalec na message
---------------------------------------------------------------------------------------------------*/
TMessage* CreateMessage(char* pData, unsigned char DataLength, unsigned char Address);

/*---------------------------------------------------------------------------------------------------
FUNCTION: DestoryMessage()

DESC:	Funkcija sprosti vse resurse v povezavi s sporočilom

PARAMS: pMessage - kazalec na sporocilo

RESULT: void
---------------------------------------------------------------------------------------------------*/
void DestroyMessage(TMessage* pMessage);

/*---------------------------------------------------------------------------------------------------
FUNCTION: esAlocateDataSpaceInMessage()

DESC:	funkcija alocira prostor za podatke v obstoječem message-u

PARAMS: pMessage - kazalec na messsage
        DataLength - določa dolžino bufferja

RESULT: 0 - ok; vse ostalo je napaka
---------------------------------------------------------------------------------------------------*/
int AlocateDataSpaceInMessage(TMessage* pMessage, unsigned char DataLength);


/*---------------------------------------------------------------------------------------------------
FUNCTION: s485FreeDataSpaceInMessage()

DESC:	funkcija sprosti buffer na katerega kaže pMessage->pData

PARAMS: pMessage - kazalec na messsage

RESULT: 0 - ok; vse ostalo je napaka
---------------------------------------------------------------------------------------------------*/
int FreeDataSpaceInMessage(TMessage* pMessage);


/*---------------------------------------------------------------------------------------------------
FUNCTION: SendMessage()

DESC:	Funkcija zgradi sporocilo za prenos proti napravi in ga pošlje na serijski port

PARAMS: hInst - handle na instanco komunikacijskega driverja
                pMessage - kazalec na strukturo s podatki za sporočilo

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int SendMessage(ThInst hInst, TMessage* pMessage);


/*---------------------------------------------------------------------------------------------------
FUNCTION: ReceiveMessage()

DESC:	Funkcija prebere sporocilo iz naprave, izlušči vsebino in vrne vsebino sporocila v pMessage
            Je tipa locking. Funkcija samodejno alocira potreben prostor za podatke(pMessage->pData) in
            nastavi pMessage->DataLength.
            !!! Po uporabi je potrebno sprostiti resurse s klicem funkcije s485FreeDataSpaceInMessage()
            ali s485DestroyMessage()  !!!

PARAMS: hInst - handle na instanco komunikacijskega driverja
                TMessage - kazalec na strkturo, kamor se bo zapisala vsebina prebranega paketa

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int ReciveMsg(ThInst hInst, TMessage* pMessage);



/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()

DESC:	Funkcija ustvari instanco s485 in vrne handle - konstruktor

PARAMS: pSerialDeviceFileName - pot do datoteke porta(/dev/ttyS0,...)
          SYNC - sync value
          MY_ADDRESS - Which packets do we accept

RESULT: InstanceHandle, NULL - failure
---------------------------------------------------------------------------------------------------*/
ThInst Init(char* pSerialDeviceFileName, unsigned char SYNC, unsigned char MY_ADDRESS);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Destroy()

DESC:	Funkcija izprazni resurse instance es - destruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja
                TMessage - kazalec na strkturo s podatki iz sporočila

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int Destroy(ThInst hInst);

}    // namespace serial485

#endif /*__485_SERIAL_H__*/
