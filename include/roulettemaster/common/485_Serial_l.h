/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
#ifndef __485_SERIAL_L_H__
#define __485_SERIAL_L_H__

#include <asm/ioctl.h>
#include <asm/ioctls.h>
#include <errno.h>
#include <stdlib.h>
#include <sys/ioctl.h>

#include "485_Serial.h"

// TODO: how to drive the rts to enable_line
#include <fcntl.h>
#include <stdio.h>
#include <string.h>
#include <strings.h>
#include <sys/io.h>    //printer port
#include <sys/stat.h>
#include <sys/types.h>
#include <unistd.h>

namespace serial485
{



}    // namespace serial485

#endif /*__485_SERIAL_L_H__*/
