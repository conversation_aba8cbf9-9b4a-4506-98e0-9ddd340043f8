#pragma once

#include "TLadyRoulettePanelBase.h"

class TLadyRoulettePanelIMAXA : public TLadyRoulettePanelBase
{
   public:
	TLadyRoulettePanelIMAXA(Container* parent, float x, float y, float width, float height, const std::string& ComponentName);

	virtual void drawLogic(Graphics* graphics, float deltaTime) override;

	void UpdateLuckyNumber();
	virtual void UpdateJackpotValues() override;

	void OnBetsOpen();
	void OnBetsClose();

   private:
	// lucky number
	TImageList* pilLuckyNumber;
	TImageList* pLuckyNumberLeft;
	TImageList* pLuckyNumberRight;
	Uint64 mUpdateTimer;

	// jackpots
	std::vector<TControlCyphers3*> pJackpotCyphers;
	std::vector<TSpriteAnimation*> pJackpotAnimation;
	std::vector<TGlyphLabel*> pJackpotLabel;
};