// Copyright (c) 2024 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file is generated by the make_colorids_header.py tool.
//

#ifndef CEF_INCLUDE_CEF_COLOR_IDS_H_
#define CEF_INCLUDE_CEF_COLOR_IDS_H_
#pragma once

#include "include/base/cef_build.h"

// Undefine the macros that will be defined in this file.
// This avoids previous definition conflicts with Chromium.
#undef CHROMEOS_ASH_COLOR_IDS
#undef CHROME_COLOR_IDS
#undef CHROME_PLATFORM_SPECIFIC_COLOR_IDS
#undef COLOR_IDS
#undef COMMON_CHROME_COLOR_IDS
#undef COMMON_COMPONENTS_COLOR_IDS
#undef COMPONENTS_COLOR_IDS
#undef CROSS_PLATFORM_COLOR_IDS
#undef PLATFORM_SPECIFIC_COLOR_IDS

// ---------------------------------------------------------------------------
// From ui/color/color_id.h:

#define CROSS_PLATFORM_COLOR_IDS \
  /* UI reference color tokens */ \
  /* Use the 3 param macro so CEF_ColorAccent is set to the correct value. */ \
  E_CPONLY(CEF_ColorRefPrimary0, CEF_UiColorsStart, CEF_UiColorsStart) \
  E_CPONLY(CEF_ColorRefPrimary10) \
  E_CPONLY(CEF_ColorRefPrimary20) \
  E_CPONLY(CEF_ColorRefPrimary25) \
  E_CPONLY(CEF_ColorRefPrimary30) \
  E_CPONLY(CEF_ColorRefPrimary40) \
  E_CPONLY(CEF_ColorRefPrimary50) \
  E_CPONLY(CEF_ColorRefPrimary60) \
  E_CPONLY(CEF_ColorRefPrimary70) \
  E_CPONLY(CEF_ColorRefPrimary80) \
  E_CPONLY(CEF_ColorRefPrimary90) \
  E_CPONLY(CEF_ColorRefPrimary95) \
  E_CPONLY(CEF_ColorRefPrimary99) \
  E_CPONLY(CEF_ColorRefPrimary100) \
  E_CPONLY(CEF_ColorRefSecondary0) \
  E_CPONLY(CEF_ColorRefSecondary10) \
  E_CPONLY(CEF_ColorRefSecondary12) \
  E_CPONLY(CEF_ColorRefSecondary15) \
  E_CPONLY(CEF_ColorRefSecondary20) \
  E_CPONLY(CEF_ColorRefSecondary25) \
  E_CPONLY(CEF_ColorRefSecondary30) \
  E_CPONLY(CEF_ColorRefSecondary35) \
  E_CPONLY(CEF_ColorRefSecondary40) \
  E_CPONLY(CEF_ColorRefSecondary50) \
  E_CPONLY(CEF_ColorRefSecondary60) \
  E_CPONLY(CEF_ColorRefSecondary70) \
  E_CPONLY(CEF_ColorRefSecondary80) \
  E_CPONLY(CEF_ColorRefSecondary90) \
  E_CPONLY(CEF_ColorRefSecondary95) \
  E_CPONLY(CEF_ColorRefSecondary99) \
  E_CPONLY(CEF_ColorRefSecondary100) \
  E_CPONLY(CEF_ColorRefTertiary0) \
  E_CPONLY(CEF_ColorRefTertiary10) \
  E_CPONLY(CEF_ColorRefTertiary20) \
  E_CPONLY(CEF_ColorRefTertiary30) \
  E_CPONLY(CEF_ColorRefTertiary40) \
  E_CPONLY(CEF_ColorRefTertiary50) \
  E_CPONLY(CEF_ColorRefTertiary60) \
  E_CPONLY(CEF_ColorRefTertiary70) \
  E_CPONLY(CEF_ColorRefTertiary80) \
  E_CPONLY(CEF_ColorRefTertiary90) \
  E_CPONLY(CEF_ColorRefTertiary95) \
  E_CPONLY(CEF_ColorRefTertiary99) \
  E_CPONLY(CEF_ColorRefTertiary100) \
  E_CPONLY(CEF_ColorRefError0) \
  E_CPONLY(CEF_ColorRefError10) \
  E_CPONLY(CEF_ColorRefError20) \
  E_CPONLY(CEF_ColorRefError30) \
  E_CPONLY(CEF_ColorRefError40) \
  E_CPONLY(CEF_ColorRefError50) \
  E_CPONLY(CEF_ColorRefError60) \
  E_CPONLY(CEF_ColorRefError70) \
  E_CPONLY(CEF_ColorRefError80) \
  E_CPONLY(CEF_ColorRefError90) \
  E_CPONLY(CEF_ColorRefError95) \
  E_CPONLY(CEF_ColorRefError99) \
  E_CPONLY(CEF_ColorRefError100) \
  E_CPONLY(CEF_ColorRefNeutral0) \
  E_CPONLY(CEF_ColorRefNeutral4) \
  E_CPONLY(CEF_ColorRefNeutral6) \
  E_CPONLY(CEF_ColorRefNeutral8) \
  E_CPONLY(CEF_ColorRefNeutral10) \
  E_CPONLY(CEF_ColorRefNeutral12) \
  E_CPONLY(CEF_ColorRefNeutral15) \
  E_CPONLY(CEF_ColorRefNeutral17) \
  E_CPONLY(CEF_ColorRefNeutral20) \
  E_CPONLY(CEF_ColorRefNeutral22) \
  E_CPONLY(CEF_ColorRefNeutral24) \
  E_CPONLY(CEF_ColorRefNeutral25) \
  E_CPONLY(CEF_ColorRefNeutral30) \
  E_CPONLY(CEF_ColorRefNeutral40) \
  E_CPONLY(CEF_ColorRefNeutral50) \
  E_CPONLY(CEF_ColorRefNeutral60) \
  E_CPONLY(CEF_ColorRefNeutral70) \
  E_CPONLY(CEF_ColorRefNeutral80) \
  E_CPONLY(CEF_ColorRefNeutral87) \
  E_CPONLY(CEF_ColorRefNeutral90) \
  E_CPONLY(CEF_ColorRefNeutral92) \
  E_CPONLY(CEF_ColorRefNeutral94) \
  E_CPONLY(CEF_ColorRefNeutral95) \
  E_CPONLY(CEF_ColorRefNeutral96) \
  E_CPONLY(CEF_ColorRefNeutral98) \
  E_CPONLY(CEF_ColorRefNeutral99) \
  E_CPONLY(CEF_ColorRefNeutral100) \
  E_CPONLY(CEF_ColorRefNeutralVariant0) \
  E_CPONLY(CEF_ColorRefNeutralVariant10) \
  E_CPONLY(CEF_ColorRefNeutralVariant15) \
  E_CPONLY(CEF_ColorRefNeutralVariant20) \
  E_CPONLY(CEF_ColorRefNeutralVariant30) \
  E_CPONLY(CEF_ColorRefNeutralVariant40) \
  E_CPONLY(CEF_ColorRefNeutralVariant50) \
  E_CPONLY(CEF_ColorRefNeutralVariant60) \
  E_CPONLY(CEF_ColorRefNeutralVariant70) \
  E_CPONLY(CEF_ColorRefNeutralVariant80) \
  E_CPONLY(CEF_ColorRefNeutralVariant90) \
  E_CPONLY(CEF_ColorRefNeutralVariant95) \
  E_CPONLY(CEF_ColorRefNeutralVariant99) \
  E_CPONLY(CEF_ColorRefNeutralVariant100) \
  \
  /* UI material system color tokens. Id ordering matches UX design spec. */ \
  E_CPONLY(CEF_ColorSysPrimary) \
  E_CPONLY(CEF_ColorSysOnPrimary) \
  E_CPONLY(CEF_ColorSysPrimaryContainer) \
  E_CPONLY(CEF_ColorSysOnPrimaryContainer) \
  /* Secondary. */ \
  E_CPONLY(CEF_ColorSysSecondary) \
  E_CPONLY(CEF_ColorSysOnSecondary) \
  E_CPONLY(CEF_ColorSysSecondaryContainer) \
  E_CPONLY(CEF_ColorSysOnSecondaryContainer) \
  /* Tertiary. */ \
  E_CPONLY(CEF_ColorSysTertiary) \
  E_CPONLY(CEF_ColorSysOnTertiary) \
  E_CPONLY(CEF_ColorSysTertiaryContainer) \
  E_CPONLY(CEF_ColorSysOnTertiaryContainer) \
  /* Error. */ \
  E_CPONLY(CEF_ColorSysError) \
  E_CPONLY(CEF_ColorSysOnError) \
  E_CPONLY(CEF_ColorSysErrorContainer) \
  E_CPONLY(CEF_ColorSysOnErrorContainer) \
  /* Neutral. */ \
  E_CPONLY(CEF_ColorSysOnSurface) \
  E_CPONLY(CEF_ColorSysOnSurfaceVariant) \
  E_CPONLY(CEF_ColorSysOutline) \
  E_CPONLY(CEF_ColorSysSurfaceVariant) \
  /* Constant. */\
  E_CPONLY(CEF_ColorSysBlack) \
  E_CPONLY(CEF_ColorSysWhite) \
  /* Inverse. */ \
  E_CPONLY(CEF_ColorSysInversePrimary) \
  E_CPONLY(CEF_ColorSysInverseOnSurface) \
  E_CPONLY(CEF_ColorSysInverseSurface) \
  E_CPONLY(CEF_ColorSysInverseSurfacePrimary) \
  /* Surfaces. */ \
  E_CPONLY(CEF_ColorSysSurface) \
  E_CPONLY(CEF_ColorSysSurface1) \
  E_CPONLY(CEF_ColorSysSurface2) \
  E_CPONLY(CEF_ColorSysSurface3) \
  E_CPONLY(CEF_ColorSysSurface4) \
  E_CPONLY(CEF_ColorSysSurface5) \
  E_CPONLY(CEF_ColorSysSurfaceNumberedForeground) \
  /* General. */ \
  E_CPONLY(CEF_ColorSysOnSurfaceSecondary) \
  E_CPONLY(CEF_ColorSysOnSurfaceSubtle) \
  E_CPONLY(CEF_ColorSysOnSurfacePrimary) \
  E_CPONLY(CEF_ColorSysOnSurfacePrimaryInactive) \
  E_CPONLY(CEF_ColorSysTonalContainer) \
  E_CPONLY(CEF_ColorSysOnTonalContainer) \
  E_CPONLY(CEF_ColorSysBaseTonalContainer) \
  E_CPONLY(CEF_ColorSysOnBaseTonalContainer) \
  E_CPONLY(CEF_ColorSysTonalOutline) \
  E_CPONLY(CEF_ColorSysNeutralOutline) \
  E_CPONLY(CEF_ColorSysNeutralContainer) \
  E_CPONLY(CEF_ColorSysDivider) \
  /* Chrome surfaces. */ \
  E_CPONLY(CEF_ColorSysBase) \
  E_CPONLY(CEF_ColorSysBaseContainer) \
  E_CPONLY(CEF_ColorSysBaseContainerElevated) \
  E_CPONLY(CEF_ColorSysHeader) \
  E_CPONLY(CEF_ColorSysHeaderInactive) \
  E_CPONLY(CEF_ColorSysHeaderContainer) \
  E_CPONLY(CEF_ColorSysHeaderContainerInactive) \
  E_CPONLY(CEF_ColorSysOnHeaderDivider) \
  E_CPONLY(CEF_ColorSysOnHeaderDividerInactive) \
  E_CPONLY(CEF_ColorSysOnHeaderPrimary) \
  E_CPONLY(CEF_ColorSysOnHeaderPrimaryInactive) \
  /* States. */ \
  E_CPONLY(CEF_ColorSysStateHoverOnProminent) \
  E_CPONLY(CEF_ColorSysStateHoverOnSubtle) \
  E_CPONLY(CEF_ColorSysStateRippleNeutralOnProminent) \
  E_CPONLY(CEF_ColorSysStateRippleNeutralOnSubtle) \
  E_CPONLY(CEF_ColorSysStateRipplePrimary) \
  E_CPONLY(CEF_ColorSysStateFocusRing) \
  E_CPONLY(CEF_ColorSysStateTextHighlight) \
  E_CPONLY(CEF_ColorSysStateOnTextHighlight) \
  E_CPONLY(CEF_ColorSysStateFocusHighlight) \
  E_CPONLY(CEF_ColorSysStateDisabled) \
  E_CPONLY(CEF_ColorSysStateDisabledContainer) \
  E_CPONLY(CEF_ColorSysStateHoverDimBlendProtection) \
  E_CPONLY(CEF_ColorSysStateHoverBrightBlendProtection) \
  E_CPONLY(CEF_ColorSysStateInactiveRing) \
  E_CPONLY(CEF_ColorSysStateScrim) \
  E_CPONLY(CEF_ColorSysStateOnHeaderHover) \
  E_CPONLY(CEF_ColorSysStateHeaderHover) \
  E_CPONLY(CEF_ColorSysStateHeaderHoverInactive) \
  E_CPONLY(CEF_ColorSysStateHeaderSelect) \
  /* Effects. */ \
  E_CPONLY(CEF_ColorSysShadow) \
  E_CPONLY(CEF_ColorSysGradientPrimary) \
  E_CPONLY(CEF_ColorSysGradientTertiary) \
  /* AI. */ \
  E_CPONLY(CEF_ColorSysAiIllustrationShapeSurface1) \
  E_CPONLY(CEF_ColorSysAiIllustrationShapeSurface2) \
  E_CPONLY(CEF_ColorSysAiIllustrationShapeSurfaceGradientStart) \
  E_CPONLY(CEF_ColorSysAiIllustrationShapeSurfaceGradientEnd) \
  /* Experimentation. */ \
  E_CPONLY(CEF_ColorSysOmniboxContainer) \
  /* Deprecated */ \
  E_CPONLY(CEF_ColorSysStateHover) \
  E_CPONLY(CEF_ColorSysStateFocus) \
  E_CPONLY(CEF_ColorSysStatePressed) \
  /* Core color concepts */ \
  /* CEF_ColorAccent is used in color_provider_css_colors_test.ts. */ \
  /* If changing the variable name, the variable name in the test needs to */ \
  /* be changed as well. */ \
  E_CPONLY(CEF_ColorAccent) \
  E_CPONLY(CEF_ColorAccentWithGuaranteedContrastAtopPrimaryBackground) \
  E_CPONLY(CEF_ColorAlertHighSeverity) \
  E_CPONLY(CEF_ColorAlertLowSeverity) \
  E_CPONLY(CEF_ColorAlertMediumSeverityIcon) \
  E_CPONLY(CEF_ColorAlertMediumSeverityText) \
  E_CPONLY(CEF_ColorDisabledForeground) \
  E_CPONLY(CEF_ColorEndpointBackground) \
  E_CPONLY(CEF_ColorEndpointForeground) \
  E_CPONLY(CEF_ColorItemHighlight) \
  E_CPONLY(CEF_ColorItemSelectionBackground) \
  E_CPONLY(CEF_ColorMenuSelectionBackground) \
  E_CPONLY(CEF_ColorMidground) \
  E_CPONLY(CEF_ColorPrimaryBackground) \
  E_CPONLY(CEF_ColorPrimaryForeground) \
  E_CPONLY(CEF_ColorSecondaryForeground) \
  E_CPONLY(CEF_ColorSubtleAccent) \
  E_CPONLY(CEF_ColorSubtleEmphasisBackground) \
  E_CPONLY(CEF_ColorTextSelectionBackground) \
  E_CPONLY(CEF_ColorTextSelectionForeground) \
  \
  /* Further UI element colors */ \
  E_CPONLY(CEF_ColorAppMenuProfileRowBackground) \
  E_CPONLY(CEF_ColorAppMenuProfileRowChipBackground) \
  E_CPONLY(CEF_ColorAppMenuProfileRowChipHovered) \
  E_CPONLY(CEF_ColorAppMenuRowBackgroundHovered) \
  E_CPONLY(CEF_ColorAppMenuUpgradeRowBackground) \
  E_CPONLY(CEF_ColorAppMenuUpgradeRowSubstringForeground) \
  E_CPONLY(CEF_ColorAvatarHeaderArt) \
  E_CPONLY(CEF_ColorAvatarIconGuest) \
  E_CPONLY(CEF_ColorAvatarIconIncognito) \
  E_CPONLY(CEF_ColorBadgeBackground) \
  E_CPONLY(CEF_ColorBadgeForeground) \
  E_CPONLY(CEF_ColorBadgeInCocoaMenuBackground) \
  E_CPONLY(CEF_ColorBadgeInCocoaMenuForeground) \
  E_CPONLY(CEF_ColorBubbleBackground) \
  E_CPONLY(CEF_ColorBubbleBorder) \
  E_CPONLY(CEF_ColorBubbleBorderShadowLarge) \
  E_CPONLY(CEF_ColorBubbleBorderShadowSmall) \
  E_CPONLY(CEF_ColorBubbleFooterBackground) \
  E_CPONLY(CEF_ColorBubbleFooterBorder) \
  E_CPONLY(CEF_ColorButtonFeatureAttentionHighlight) \
  E_CPONLY(CEF_ColorButtonBackground) \
  E_CPONLY(CEF_ColorButtonBackgroundPressed) \
  E_CPONLY(CEF_ColorButtonBackgroundProminent) \
  E_CPONLY(CEF_ColorButtonBackgroundProminentDisabled) \
  E_CPONLY(CEF_ColorButtonBackgroundProminentFocused) \
  E_CPONLY(CEF_ColorButtonBackgroundTonal) \
  E_CPONLY(CEF_ColorButtonBackgroundTonalDisabled) \
  E_CPONLY(CEF_ColorButtonBackgroundTonalFocused) \
  E_CPONLY(CEF_ColorButtonBackgroundWithAttention) \
  E_CPONLY(CEF_ColorButtonBorder) \
  E_CPONLY(CEF_ColorButtonBorderDisabled) \
  E_CPONLY(CEF_ColorButtonForeground) \
  E_CPONLY(CEF_ColorButtonForegroundDisabled) \
  E_CPONLY(CEF_ColorButtonForegroundProminent) \
  E_CPONLY(CEF_ColorButtonForegroundTonal) \
  E_CPONLY(CEF_ColorButtonHoverBackgroundText) \
  E_CPONLY(CEF_ColorMultitaskMenuNudgePulse) \
  E_CPONLY(CEF_ColorCheckboxCheck) \
  E_CPONLY(CEF_ColorCheckboxCheckDisabled) \
  E_CPONLY(CEF_ColorCheckboxContainer) \
  E_CPONLY(CEF_ColorCheckboxContainerDisabled) \
  E_CPONLY(CEF_ColorCheckboxOutline) \
  E_CPONLY(CEF_ColorCheckboxOutlineDisabled) \
  E_CPONLY(CEF_ColorCheckboxForegroundChecked) \
  E_CPONLY(CEF_ColorCheckboxForegroundUnchecked) \
  E_CPONLY(CEF_ColorChipBackgroundHover) \
  E_CPONLY(CEF_ColorChipBackgroundSelected) \
  E_CPONLY(CEF_ColorChipBorder) \
  E_CPONLY(CEF_ColorChipForeground) \
  E_CPONLY(CEF_ColorChipForegroundSelected) \
  E_CPONLY(CEF_ColorChipIcon) \
  E_CPONLY(CEF_ColorChipIconSelected) \
  E_CPONLY(CEF_ColorComboboxBackground) \
  E_CPONLY(CEF_ColorComboboxBackgroundDisabled) \
  E_CPONLY(CEF_ColorComboboxContainerOutline) \
  E_CPONLY(CEF_ColorComboboxInkDropHovered) \
  E_CPONLY(CEF_ColorComboboxInkDropRipple) \
  /* These colors correspond to the system colors defined in */ \
  /* ui::NativeTheme::SystemThemeColor. They are used to support */ \
  /* CSS system colors. */ \
  E_CPONLY(CEF_ColorCssSystemBtnFace) \
  E_CPONLY(CEF_ColorCssSystemBtnText) \
  E_CPONLY(CEF_ColorCssSystemGrayText) \
  E_CPONLY(CEF_ColorCssSystemHighlight) \
  E_CPONLY(CEF_ColorCssSystemHighlightText) \
  E_CPONLY(CEF_ColorCssSystemHotlight) \
  E_CPONLY(CEF_ColorCssSystemMenuHilight) \
  E_CPONLY(CEF_ColorCssSystemScrollbar) \
  E_CPONLY(CEF_ColorCssSystemWindow) \
  E_CPONLY(CEF_ColorCssSystemWindowText) \
  E_CPONLY(CEF_ColorCustomFrameCaptionForeground) \
  E_CPONLY(CEF_ColorDebugBoundsOutline) \
  E_CPONLY(CEF_ColorDebugContentOutline) \
  E_CPONLY(CEF_ColorDialogBackground) \
  E_CPONLY(CEF_ColorDialogForeground) \
  E_CPONLY(CEF_ColorDropdownBackground) \
  E_CPONLY(CEF_ColorDropdownBackgroundSelected) \
  E_CPONLY(CEF_ColorDropdownForeground) \
  E_CPONLY(CEF_ColorDropdownForegroundSelected) \
  E_CPONLY(CEF_ColorFocusableBorderFocused) \
  E_CPONLY(CEF_ColorFocusableBorderUnfocused) \
  E_CPONLY(CEF_ColorFrameActive) \
  E_CPONLY(CEF_ColorFrameActiveUnthemed) \
  E_CPONLY(CEF_ColorFrameCaptionButtonUnfocused) \
  E_CPONLY(CEF_ColorFrameInactive) \
  E_CPONLY(CEF_ColorHelpIconActive) \
  E_CPONLY(CEF_ColorHelpIconInactive) \
  /* These should be refactored into chrome_color_id or removed once the */ \
  /* history clusters side panel is refactored to use shadow parts. */ \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelDivider) \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelDialogBackground) \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelDialogDivider) \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelDialogPrimaryForeground) \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelDialogSecondaryForeground) \
  E_CPONLY(CEF_ColorHistoryClustersSidePanelCardSecondaryForeground) \
  E_CPONLY(CEF_ColorIcon) \
  E_CPONLY(CEF_ColorIconDisabled) \
  E_CPONLY(CEF_ColorIconSecondary) \
  /* This is declared here so src/components/ can access it, but we expect */ \
  /* this to be set in the embedder. */ \
  E_CPONLY(CEF_ColorInfoBarIcon) \
  E_CPONLY(CEF_ColorLabelForeground) \
  E_CPONLY(CEF_ColorLabelForegroundDisabled) \
  E_CPONLY(CEF_ColorLabelForegroundSecondary) \
  E_CPONLY(CEF_ColorLabelSelectionBackground) \
  E_CPONLY(CEF_ColorLabelSelectionForeground) \
  E_CPONLY(CEF_ColorLinkForeground) \
  E_CPONLY(CEF_ColorLinkForegroundDefault) \
  E_CPONLY(CEF_ColorLinkForegroundDisabled) \
  E_CPONLY(CEF_ColorLinkForegroundOnBubbleFooter) \
  E_CPONLY(CEF_ColorLinkForegroundPressed) \
  E_CPONLY(CEF_ColorLinkForegroundPressedDefault) \
  E_CPONLY(CEF_ColorLinkForegroundPressedOnBubbleFooter) \
  E_CPONLY(CEF_ColorListItemFolderIconBackground) \
  E_CPONLY(CEF_ColorListItemFolderIconForeground) \
  E_CPONLY(CEF_ColorListItemUrlFaviconBackground) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleBackgroundDefault) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleButtonIcon) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleButtonIconDisabled) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleForegroundDefault) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleForegroundSecondary) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleCheckbox) \
  E_CPONLY(CEF_ColorLiveCaptionBubbleLink) \
  E_CPONLY(CEF_ColorLoadingGradientBorder) \
  E_CPONLY(CEF_ColorLoadingGradientEnd) \
  E_CPONLY(CEF_ColorLoadingGradientMiddle) \
  E_CPONLY(CEF_ColorLoadingGradientStart) \
  E_CPONLY(CEF_ColorMenuBackground) \
  E_CPONLY(CEF_ColorMenuBorder) \
  E_CPONLY(CEF_ColorMenuButtonBackground) \
  E_CPONLY(CEF_ColorMenuButtonBackgroundSelected) \
  E_CPONLY(CEF_ColorMenuDropmarker) \
  E_CPONLY(CEF_ColorMenuIcon) \
  E_CPONLY(CEF_ColorMenuIconDisabled) \
  E_CPONLY(CEF_ColorMenuIconOnEmphasizedBackground) \
  E_CPONLY(CEF_ColorMenuItemBackgroundAlertedInitial) \
  E_CPONLY(CEF_ColorMenuItemBackgroundAlertedTarget) \
  E_CPONLY(CEF_ColorMenuItemBackgroundHighlighted) \
  E_CPONLY(CEF_ColorMenuItemBackgroundSelected) \
  E_CPONLY(CEF_ColorMenuItemForeground) \
  E_CPONLY(CEF_ColorMenuItemForegroundDisabled) \
  E_CPONLY(CEF_ColorMenuItemForegroundHighlighted) \
  E_CPONLY(CEF_ColorMenuItemForegroundSecondary) \
  E_CPONLY(CEF_ColorMenuItemForegroundSelected) \
  E_CPONLY(CEF_ColorMenuSeparator) \
  E_CPONLY(CEF_ColorNotificationActionsBackground) \
  E_CPONLY(CEF_ColorNotificationBackgroundActive) \
  E_CPONLY(CEF_ColorNotificationBackgroundInactive) \
  E_CPONLY(CEF_ColorNotificationHeaderForeground) \
  E_CPONLY(CEF_ColorNotificationIconBackground) \
  E_CPONLY(CEF_ColorNotificationIconForeground) \
  E_CPONLY(CEF_ColorNotificationImageBackground) \
  E_CPONLY(CEF_ColorNotificationInputBackground) \
  E_CPONLY(CEF_ColorNotificationInputForeground) \
  E_CPONLY(CEF_ColorNotificationInputPlaceholderForeground) \
  E_CPONLY(CEF_ColorOverlayScrollbarFill) \
  E_CPONLY(CEF_ColorOverlayScrollbarFillHovered) \
  E_CPONLY(CEF_ColorOverlayScrollbarStroke) \
  E_CPONLY(CEF_ColorOverlayScrollbarStrokeHovered) \
  E_CPONLY(CEF_ColorProgressBar) \
  E_CPONLY(CEF_ColorProgressBarBackground) \
  E_CPONLY(CEF_ColorProgressBarPaused) \
  E_CPONLY(CEF_ColorRadioButtonForegroundUnchecked) \
  E_CPONLY(CEF_ColorRadioButtonForegroundDisabled) \
  E_CPONLY(CEF_ColorRadioButtonForegroundChecked) \
  E_CPONLY(CEF_ColorSegmentedButtonBorder) \
  E_CPONLY(CEF_ColorSegmentedButtonFocus) \
  E_CPONLY(CEF_ColorSegmentedButtonForegroundChecked) \
  E_CPONLY(CEF_ColorSegmentedButtonForegroundUnchecked) \
  E_CPONLY(CEF_ColorSegmentedButtonHover) \
  E_CPONLY(CEF_ColorSegmentedButtonRipple) \
  E_CPONLY(CEF_ColorSegmentedButtonChecked) \
  E_CPONLY(CEF_ColorSeparator) \
  E_CPONLY(CEF_ColorShadowBase) \
  E_CPONLY(CEF_ColorShadowValueAmbientShadowElevationFour) \
  E_CPONLY(CEF_ColorShadowValueAmbientShadowElevationSixteen) \
  E_CPONLY(CEF_ColorShadowValueAmbientShadowElevationThree) \
  E_CPONLY(CEF_ColorShadowValueAmbientShadowElevationTwelve) \
  E_CPONLY(CEF_ColorShadowValueAmbientShadowElevationTwentyFour) \
  E_CPONLY(CEF_ColorShadowValueKeyShadowElevationFour) \
  E_CPONLY(CEF_ColorShadowValueKeyShadowElevationSixteen) \
  E_CPONLY(CEF_ColorShadowValueKeyShadowElevationThree) \
  E_CPONLY(CEF_ColorShadowValueKeyShadowElevationTwelve) \
  E_CPONLY(CEF_ColorShadowValueKeyShadowElevationTwentyFour) \
  E_CPONLY(CEF_ColorSidePanelComboboxBorder) \
  E_CPONLY(CEF_ColorSidePanelComboboxBackground) \
  E_CPONLY(CEF_ColorSliderThumb) \
  E_CPONLY(CEF_ColorSliderThumbMinimal) \
  E_CPONLY(CEF_ColorSliderTrack) \
  E_CPONLY(CEF_ColorSliderTrackMinimal) \
  E_CPONLY(CEF_ColorSyncInfoBackground) \
  E_CPONLY(CEF_ColorSyncInfoBackgroundError) \
  E_CPONLY(CEF_ColorSyncInfoBackgroundPaused) \
  E_CPONLY(CEF_ColorTabBackgroundHighlighted) \
  E_CPONLY(CEF_ColorTabBackgroundHighlightedFocused) \
  E_CPONLY(CEF_ColorTabBorderSelected) \
  E_CPONLY(CEF_ColorTabContentSeparator) \
  E_CPONLY(CEF_ColorTabForeground) \
  E_CPONLY(CEF_ColorTabForegroundSelected) \
  E_CPONLY(CEF_ColorTableBackground) \
  E_CPONLY(CEF_ColorTableBackgroundAlternate) \
  E_CPONLY(CEF_ColorTableBackgroundSelectedFocused) \
  E_CPONLY(CEF_ColorTableBackgroundSelectedUnfocused) \
  E_CPONLY(CEF_ColorTableForeground) \
  E_CPONLY(CEF_ColorTableForegroundSelectedFocused) \
  E_CPONLY(CEF_ColorTableForegroundSelectedUnfocused) \
  E_CPONLY(CEF_ColorTableGroupingIndicator) \
  E_CPONLY(CEF_ColorTableHeaderBackground) \
  E_CPONLY(CEF_ColorTableHeaderForeground) \
  E_CPONLY(CEF_ColorTableHeaderSeparator) \
  E_CPONLY(CEF_ColorSuggestionChipBorder) \
  E_CPONLY(CEF_ColorSuggestionChipIcon) \
  E_CPONLY(CEF_ColorTextfieldBackground) \
  E_CPONLY(CEF_ColorTextfieldBackgroundDisabled) \
  E_CPONLY(CEF_ColorTextfieldFilledBackground) \
  E_CPONLY(CEF_ColorTextfieldFilledForegroundInvalid) \
  E_CPONLY(CEF_ColorTextfieldFilledUnderline) \
  E_CPONLY(CEF_ColorTextfieldFilledUnderlineFocused) \
  E_CPONLY(CEF_ColorTextfieldForeground) \
  E_CPONLY(CEF_ColorTextfieldForegroundDisabled) \
  E_CPONLY(CEF_ColorTextfieldForegroundIcon) \
  E_CPONLY(CEF_ColorTextfieldForegroundLabel) \
  E_CPONLY(CEF_ColorTextfieldForegroundPlaceholderInvalid) \
  E_CPONLY(CEF_ColorTextfieldForegroundPlaceholder) \
  E_CPONLY(CEF_ColorTextfieldHover) \
  E_CPONLY(CEF_ColorTextfieldSelectionBackground) \
  E_CPONLY(CEF_ColorTextfieldSelectionForeground) \
  E_CPONLY(CEF_ColorTextfieldOutline) \
  E_CPONLY(CEF_ColorTextfieldOutlineDisabled) \
  E_CPONLY(CEF_ColorTextfieldOutlineInvalid) \
  E_CPONLY(CEF_ColorThemeColorPickerCheckmarkBackground) \
  E_CPONLY(CEF_ColorThemeColorPickerCheckmarkForeground) \
  E_CPONLY(CEF_ColorThemeColorPickerCustomColorIconBackground) \
  E_CPONLY(CEF_ColorThemeColorPickerHueSliderDialogBackground) \
  E_CPONLY(CEF_ColorThemeColorPickerHueSliderDialogForeground) \
  E_CPONLY(CEF_ColorThemeColorPickerHueSliderDialogIcon) \
  E_CPONLY(CEF_ColorThemeColorPickerHueSliderHandle) \
  E_CPONLY(CEF_ColorThemeColorPickerOptionBackground) \
  E_CPONLY(CEF_ColorThrobber) \
  E_CPONLY(CEF_ColorThrobberPreconnect) \
  E_CPONLY(CEF_ColorToastBackground) \
  E_CPONLY(CEF_ColorToastBackgroundProminent) \
  E_CPONLY(CEF_ColorToastButton) \
  E_CPONLY(CEF_ColorToastForeground) \
  E_CPONLY(CEF_ColorToggleButtonHover) \
  E_CPONLY(CEF_ColorToggleButtonPressed) \
  E_CPONLY(CEF_ColorToggleButtonShadow) \
  E_CPONLY(CEF_ColorToggleButtonThumbOff) \
  E_CPONLY(CEF_ColorToggleButtonThumbOffDisabled) \
  E_CPONLY(CEF_ColorToggleButtonThumbOn) \
  E_CPONLY(CEF_ColorToggleButtonThumbOnDisabled) \
  E_CPONLY(CEF_ColorToggleButtonThumbOnHover) \
  E_CPONLY(CEF_ColorToggleButtonTrackOff) \
  E_CPONLY(CEF_ColorToggleButtonTrackOffDisabled) \
  E_CPONLY(CEF_ColorToggleButtonTrackOn) \
  E_CPONLY(CEF_ColorToggleButtonTrackOnDisabled) \
  E_CPONLY(CEF_ColorToolbarSearchFieldBackground) \
  E_CPONLY(CEF_ColorToolbarSearchFieldBackgroundHover) \
  E_CPONLY(CEF_ColorToolbarSearchFieldBackgroundPressed) \
  E_CPONLY(CEF_ColorToolbarSearchFieldForeground) \
  E_CPONLY(CEF_ColorToolbarSearchFieldForegroundPlaceholder) \
  E_CPONLY(CEF_ColorToolbarSearchFieldIcon) \
  E_CPONLY(CEF_ColorTooltipBackground) \
  E_CPONLY(CEF_ColorTooltipForeground) \
  E_CPONLY(CEF_ColorTreeBackground) \
  E_CPONLY(CEF_ColorTreeNodeBackgroundSelectedFocused) \
  E_CPONLY(CEF_ColorTreeNodeBackgroundSelectedUnfocused) \
  E_CPONLY(CEF_ColorTreeNodeForeground) \
  E_CPONLY(CEF_ColorTreeNodeForegroundSelectedFocused) \
  E_CPONLY(CEF_ColorTreeNodeForegroundSelectedUnfocused) \
  /* These colors are used to paint the controls defined in */ \
  /* ui::NativeThemeBase::ControlColorId. */ \
  E_CPONLY(CEF_ColorWebNativeControlAccent) \
  E_CPONLY(CEF_ColorWebNativeControlAccentDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlAccentHovered) \
  E_CPONLY(CEF_ColorWebNativeControlAccentPressed) \
  E_CPONLY(CEF_ColorWebNativeControlAutoCompleteBackground) \
  E_CPONLY(CEF_ColorWebNativeControlBackground) \
  E_CPONLY(CEF_ColorWebNativeControlBackgroundDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlBorder) \
  E_CPONLY(CEF_ColorWebNativeControlBorderDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlBorderHovered) \
  E_CPONLY(CEF_ColorWebNativeControlBorderPressed) \
  E_CPONLY(CEF_ColorWebNativeControlButtonBorder) \
  E_CPONLY(CEF_ColorWebNativeControlButtonBorderDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlButtonBorderHovered) \
  E_CPONLY(CEF_ColorWebNativeControlButtonBorderPressed) \
  E_CPONLY(CEF_ColorWebNativeControlButtonFill) \
  E_CPONLY(CEF_ColorWebNativeControlButtonFillDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlButtonFillHovered) \
  E_CPONLY(CEF_ColorWebNativeControlButtonFillPressed) \
  E_CPONLY(CEF_ColorWebNativeControlFill) \
  E_CPONLY(CEF_ColorWebNativeControlFillDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlFillHovered) \
  E_CPONLY(CEF_ColorWebNativeControlFillPressed) \
  E_CPONLY(CEF_ColorWebNativeControlLightenLayer) \
  E_CPONLY(CEF_ColorWebNativeControlProgressValue) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarArrowBackgroundHovered) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarArrowBackgroundPressed) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarArrowForeground) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarArrowForegroundPressed) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarCorner) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarThumb) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarThumbHovered) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarThumbInactive) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarThumbOverlayMinimalMode) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarThumbPressed) \
  E_CPONLY(CEF_ColorWebNativeControlScrollbarTrack) \
  E_CPONLY(CEF_ColorWebNativeControlSlider) \
  E_CPONLY(CEF_ColorWebNativeControlSliderDisabled) \
  E_CPONLY(CEF_ColorWebNativeControlSliderHovered) \
  E_CPONLY(CEF_ColorWebNativeControlSliderPressed) \
  E_CPONLY(CEF_ColorWindowBackground)

#if defined(OS_CHROMEOS_ASH)
#define CHROMEOS_ASH_COLOR_IDS \
  /* Colors for illustrations */ \
  E_CPONLY(CEF_ColorNativeColor1) \
  E_CPONLY(CEF_ColorNativeColor1Shade1) \
  E_CPONLY(CEF_ColorNativeColor1Shade2) \
  E_CPONLY(CEF_ColorNativeColor2) \
  E_CPONLY(CEF_ColorNativeColor3) \
  E_CPONLY(CEF_ColorNativeColor4) \
  E_CPONLY(CEF_ColorNativeColor5) \
  E_CPONLY(CEF_ColorNativeColor6) \
  E_CPONLY(CEF_ColorNativeBaseColor) \
  E_CPONLY(CEF_ColorNativeSecondaryColor) \
  E_CPONLY(CEF_ColorNativeOnPrimaryContainerColor) \
  E_CPONLY(CEF_ColorNativeAnalogColor) \
  E_CPONLY(CEF_ColorNativeMutedColor) \
  E_CPONLY(CEF_ColorNativeComplementColor) \
  E_CPONLY(CEF_ColorNativeOnGradientColor)
#elif defined(OS_CHROMEOS_LACROS)
#define CHROMEOS_ASH_COLOR_IDS
#endif
#if defined(OS_CHROMEOS)
#define PLATFORM_SPECIFIC_COLOR_IDS \
  CHROMEOS_ASH_COLOR_IDS \
  /* NOTE: Nearly all of the following CrOS color ids will need to be re- */ \
  /* evaluated once CrOS fully supports the color pipeline. */ \
  E_CPONLY(CEF_ColorAshActionLabelFocusRingEdit) \
  E_CPONLY(CEF_ColorAshActionLabelFocusRingError) \
  E_CPONLY(CEF_ColorAshActionLabelFocusRingHover) \
  \
  /* TODO(skau): Remove Compat value when dark/light mode launches. */ \
  E_CPONLY(CEF_ColorAshAppListFocusRingCompat) \
  E_CPONLY(CEF_ColorAshAppListFocusRingNoKeyboard) \
  E_CPONLY(CEF_ColorAshAppListSeparator) \
  E_CPONLY(CEF_ColorAshAppListSeparatorLight) \
  E_CPONLY(CEF_ColorAshArcInputMenuSeparator) \
  E_CPONLY(CEF_ColorAshFocusRing) \
  /* TODO(kylixrd): Determine whether this special color should follow */ \
  /* light/dark mode. Remove if it should equal CEF_ColorAshFocusRing. */ \
  E_CPONLY(CEF_ColorAshInputOverlayFocusRing) \
  E_CPONLY(CEF_ColorAshIconInOobe) \
  \
  /* TODO(crbug/1319917): Remove these when dark light mode is launched. */ \
  E_CPONLY(CEF_ColorAshLightFocusRing) \
  \
  E_CPONLY(CEF_ColorAshOnboardingFocusRing) \
  \
  E_CPONLY(CEF_ColorAshPrivacyIndicatorsBackground) \
  \
  E_CPONLY(CEF_ColorAshSystemUIMenuBackground) \
  E_CPONLY(CEF_ColorAshSystemUIMenuIcon) \
  E_CPONLY(CEF_ColorAshSystemUIMenuItemBackgroundSelected) \
  E_CPONLY(CEF_ColorAshSystemUIMenuSeparator) \
  \
  /* TODO(b/291622042): Delete these colors when Jelly is launched */ \
  E_CPONLY(CEF_ColorHighlightBorderBorder1) \
  E_CPONLY(CEF_ColorHighlightBorderBorder2) \
  E_CPONLY(CEF_ColorHighlightBorderBorder3) \
  E_CPONLY(CEF_ColorHighlightBorderHighlight1) \
  E_CPONLY(CEF_ColorHighlightBorderHighlight2) \
  E_CPONLY(CEF_ColorHighlightBorderHighlight3) \
  \
  E_CPONLY(CEF_ColorCrosSystemHighlight) \
  E_CPONLY(CEF_ColorCrosSystemHighlightBorder) \
  E_CPONLY(CEF_ColorCrosSystemHighlightBorder1) \
  \
  E_CPONLY(CEF_ColorCrosSysPositive) \
  E_CPONLY(CEF_ColorCrosSysComplementVariant)
#elif defined(OS_LINUX)
#define PLATFORM_SPECIFIC_COLOR_IDS \
  E_CPONLY(CEF_ColorNativeButtonBorder)\
  E_CPONLY(CEF_ColorNativeHeaderButtonBorderActive) \
  E_CPONLY(CEF_ColorNativeHeaderButtonBorderInactive) \
  E_CPONLY(CEF_ColorNativeHeaderSeparatorBorderActive) \
  E_CPONLY(CEF_ColorNativeHeaderSeparatorBorderInactive) \
  E_CPONLY(CEF_ColorNativeLabelForeground) \
  E_CPONLY(CEF_ColorNativeTabForegroundInactiveFrameActive) \
  E_CPONLY(CEF_ColorNativeTabForegroundInactiveFrameInactive) \
  E_CPONLY(CEF_ColorNativeTextfieldBorderUnfocused)\
  E_CPONLY(CEF_ColorNativeToolbarBackground)
#elif defined(OS_WIN)
#define PLATFORM_SPECIFIC_COLOR_IDS \
  E_CPONLY(CEF_ColorNative3dDkShadow) \
  E_CPONLY(CEF_ColorNative3dLight) \
  E_CPONLY(CEF_ColorNativeActiveBorder) \
  E_CPONLY(CEF_ColorNativeActiveCaption) \
  E_CPONLY(CEF_ColorNativeAppWorkspace) \
  E_CPONLY(CEF_ColorNativeBackground) \
  E_CPONLY(CEF_ColorNativeBtnFace) \
  E_CPONLY(CEF_ColorNativeBtnHighlight) \
  E_CPONLY(CEF_ColorNativeBtnShadow) \
  E_CPONLY(CEF_ColorNativeBtnText) \
  E_CPONLY(CEF_ColorNativeCaptionText) \
  E_CPONLY(CEF_ColorNativeGradientActiveCaption) \
  E_CPONLY(CEF_ColorNativeGradientInactiveCaption) \
  E_CPONLY(CEF_ColorNativeGrayText) \
  E_CPONLY(CEF_ColorNativeHighlight) \
  E_CPONLY(CEF_ColorNativeHighlightText) \
  E_CPONLY(CEF_ColorNativeHotlight) \
  E_CPONLY(CEF_ColorNativeInactiveBorder) \
  E_CPONLY(CEF_ColorNativeInactiveCaption) \
  E_CPONLY(CEF_ColorNativeInactiveCaptionText) \
  E_CPONLY(CEF_ColorNativeInfoBk) \
  E_CPONLY(CEF_ColorNativeInfoText) \
  E_CPONLY(CEF_ColorNativeMenu) \
  E_CPONLY(CEF_ColorNativeMenuBar) \
  E_CPONLY(CEF_ColorNativeMenuHilight) \
  E_CPONLY(CEF_ColorNativeMenuText) \
  E_CPONLY(CEF_ColorNativeScrollbar) \
  E_CPONLY(CEF_ColorNativeWindow) \
  E_CPONLY(CEF_ColorNativeWindowFrame) \
  E_CPONLY(CEF_ColorNativeWindowText)
#else
#define PLATFORM_SPECIFIC_COLOR_IDS
#endif

#define COLOR_IDS \
  CROSS_PLATFORM_COLOR_IDS \
  PLATFORM_SPECIFIC_COLOR_IDS

// ---------------------------------------------------------------------------
// From components/color/color_id.h:

// Cross-platform IDs should be added here.
#define COMMON_COMPONENTS_COLOR_IDS \

#if !defined(OS_MAC)
#define COMPONENTS_COLOR_IDS COMMON_COMPONENTS_COLOR_IDS \
  /* Eyedropper colors. */ \
  E_CPONLY(CEF_ColorEyedropperBoundary) \
  E_CPONLY(CEF_ColorEyedropperCentralPixelInnerRing) \
  E_CPONLY(CEF_ColorEyedropperCentralPixelOuterRing) \
  E_CPONLY(CEF_ColorEyedropperGrid) \

#else
#define COMPONENTS_COLOR_IDS COMMON_COMPONENTS_COLOR_IDS
#endif

// ---------------------------------------------------------------------------
// From chrome/browser/ui/color/chrome_color_id.h:

#define COMMON_CHROME_COLOR_IDS \
  /* App menu colors. */ \
  /* The CEF_ColorAppMenuHighlightSeverityLow color id is used in */ \
  /* color_provider_css_colors_test.ts. If changing the variable name, the */ \
  /* variable name in the test needs to be changed as well. */ \
  E_CPONLY(CEF_ColorAppMenuHighlightSeverityLow, CEF_ChromeColorsStart, \
           CEF_ChromeColorsStart) \
  E_CPONLY(CEF_ColorAppMenuHighlightSeverityHigh) \
  E_CPONLY(CEF_ColorAppMenuHighlightSeverityMedium) \
  E_CPONLY(CEF_ColorAppMenuHighlightDefault) \
  E_CPONLY(CEF_ColorAppMenuHighlightPrimary) \
  E_CPONLY(CEF_ColorAppMenuExpandedForegroundDefault) \
  E_CPONLY(CEF_ColorAppMenuExpandedForegroundPrimary) \
  E_CPONLY(CEF_ColorAppMenuChipInkDropHover) \
  E_CPONLY(CEF_ColorAppMenuChipInkDropRipple) \
  /* Content settings activity indicators popup dialog colors */ \
  E_CPONLY(CEF_ColorActivityIndicatorForeground) \
  E_CPONLY(CEF_ColorActivityIndicatorSubtitleForeground) \
  /* Avatar colors. */ \
  /* TODO(crbug.com/40259490): Refactor the Avatar Button colors as Profile */ \
  /* Menu Button colors. */ \
  E_CPONLY(CEF_ColorAvatarButtonHighlightDefault) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightNormal) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightSyncError) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightSyncPaused) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightSigninPaused) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightExplicitText) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightIncognito) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightNormalForeground) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightDefaultForeground) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightSyncErrorForeground) \
  E_CPONLY(CEF_ColorAvatarButtonHighlightIncognitoForeground) \
  E_CPONLY(CEF_ColorAvatarButtonIncognitoHover) \
  E_CPONLY(CEF_ColorAvatarButtonNormalRipple) \
  E_CPONLY(CEF_ColorAvatarStrokeLight) \
  E_CPONLY(CEF_ColorAvatarStroke) \
  E_CPONLY(CEF_ColorAvatarFillForContrast) \
  /* Bookmark bar colors. */ \
  E_CPONLY(CEF_ColorBookmarkBarBackground) \
  E_CPONLY(CEF_ColorBookmarkBarForeground) \
  E_CPONLY(CEF_ColorBookmarkBarForegroundDisabled) \
  E_CPONLY(CEF_ColorBookmarkBarSeparator) \
  E_CPONLY(CEF_ColorBookmarkBarSeparatorChromeRefresh) \
  E_CPONLY(CEF_ColorBookmarkButtonIcon) \
  E_CPONLY(CEF_ColorBookmarkDialogTrackPriceIcon) \
  E_CPONLY(CEF_ColorBookmarkDialogProductImageBorder) \
  E_CPONLY(CEF_ColorBookmarkDragImageBackground) \
  E_CPONLY(CEF_ColorBookmarkDragImageCountBackground) \
  E_CPONLY(CEF_ColorBookmarkDragImageCountForeground) \
  E_CPONLY(CEF_ColorBookmarkDragImageForeground) \
  E_CPONLY(CEF_ColorBookmarkDragImageIconBackground) \
  E_CPONLY(CEF_ColorBookmarkFavicon) \
  E_CPONLY(CEF_ColorBookmarkFolderIcon) \
  /* Window caption colors. */ \
  E_CPONLY(CEF_ColorCaptionButtonBackground) \
  /* Captured tab colors. */ \
  E_CPONLY(CEF_ColorCapturedTabContentsBorder) \
  /* Cast dialog colors. */ \
  E_CPONLY(CEF_ColorCastDialogHelpIcon) \
  /* Signin bubble colors. */ \
  E_CPONLY(CEF_ColorChromeSigninBubbleBackground) \
  E_CPONLY(CEF_ColorChromeSigninBubbleInfoBackground) \
  /* Batch Upload colors. */ \
  E_CPONLY(CEF_ColorBatchUploadBackground) \
  E_CPONLY(CEF_ColorBatchUploadDataBackground) \
  /* Compose colors */ \
  E_CPONLY(CEF_ColorComposeDialogBackground) \
  E_CPONLY(CEF_ColorComposeDialogDivider) \
  E_CPONLY(CEF_ColorComposeDialogError) \
  E_CPONLY(CEF_ColorComposeDialogForegroundSubtle) \
  E_CPONLY(CEF_ColorComposeDialogLink) \
  E_CPONLY(CEF_ColorComposeDialogLogo) \
  E_CPONLY(CEF_ColorComposeDialogResultBackground) \
  E_CPONLY(CEF_ColorComposeDialogResultForeground) \
  E_CPONLY(CEF_ColorComposeDialogResultForegroundWhileLoading) \
  E_CPONLY(CEF_ColorComposeDialogResultIcon) \
  E_CPONLY(CEF_ColorComposeDialogResultContainerScrollbarThumb) \
  E_CPONLY(CEF_ColorComposeDialogScrollbarThumb) \
  E_CPONLY(CEF_ColorComposeDialogTitle) \
  E_CPONLY(CEF_ColorComposeDialogTextarea) \
  E_CPONLY(CEF_ColorComposeDialogTextareaOutline) \
  E_CPONLY(CEF_ColorComposeDialogTextareaPlaceholder) \
  E_CPONLY(CEF_ColorComposeDialogTextareaReadonlyBackground) \
  E_CPONLY(CEF_ColorComposeDialogTextareaReadonlyForeground) \
  E_CPONLY(CEF_ColorComposeDialogTextareaIcon) \
  E_CPONLY(CEF_ColorComposeDialogSelectOptionDisabled) \
  /* Desktop media picker view. */ \
  E_CPONLY(CEF_ColorDesktopMediaPickerDescriptionLabel) \
  /* Desktop media tab list colors. */ \
  E_CPONLY(CEF_ColorDesktopMediaTabListBorder) \
  E_CPONLY(CEF_ColorDesktopMediaTabListPreviewBackground) \
  /* Common Download colors. */ \
  E_CPONLY(CEF_ColorDownloadItemIconDangerous) \
  E_CPONLY(CEF_ColorDownloadItemTextDangerous) \
  E_CPONLY(CEF_ColorDownloadItemIconWarning) \
  E_CPONLY(CEF_ColorDownloadItemTextWarning) \
  /* Download bubble colors. */\
  E_CPONLY(CEF_ColorDownloadBubbleInfoBackground) \
  E_CPONLY(CEF_ColorDownloadBubbleInfoIcon) \
  E_CPONLY(CEF_ColorDownloadBubbleRowHover) \
  E_CPONLY(CEF_ColorDownloadBubbleShowAllDownloadsIcon) \
  E_CPONLY(CEF_ColorDownloadBubblePrimaryIcon) \
  /* Download shelf colors. */ \
  E_CPONLY(CEF_ColorDownloadItemForeground) \
  E_CPONLY(CEF_ColorDownloadItemForegroundDangerous) \
  E_CPONLY(CEF_ColorDownloadItemForegroundDisabled) \
  E_CPONLY(CEF_ColorDownloadItemForegroundSafe) \
  E_CPONLY(CEF_ColorDownloadItemProgressRingBackground) \
  E_CPONLY(CEF_ColorDownloadItemProgressRingForeground) \
  E_CPONLY(CEF_ColorDownloadShelfBackground) \
  E_CPONLY(CEF_ColorDownloadShelfButtonBackground) \
  E_CPONLY(CEF_ColorDownloadShelfButtonText) \
  E_CPONLY(CEF_ColorDownloadShelfButtonIcon) \
  E_CPONLY(CEF_ColorDownloadShelfButtonIconDisabled) \
  E_CPONLY(CEF_ColorDownloadShelfContentAreaSeparator) \
  E_CPONLY(CEF_ColorDownloadShelfForeground) \
  E_CPONLY(CEF_ColorDownloadStartedAnimationForeground) \
  E_CPONLY(CEF_ColorDownloadToolbarButtonActive) \
  E_CPONLY(CEF_ColorDownloadToolbarButtonAnimationBackground) \
  E_CPONLY(CEF_ColorDownloadToolbarButtonAnimationForeground) \
  E_CPONLY(CEF_ColorDownloadToolbarButtonInactive) \
  E_CPONLY(CEF_ColorDownloadToolbarButtonRingBackground) \
  /* Extension colors. */ \
  E_CPONLY(CEF_ColorExtensionDialogBackground) \
  E_CPONLY(CEF_ColorExtensionIconBadgeBackgroundDefault) \
  E_CPONLY(CEF_ColorExtensionIconDecorationAmbientShadow) \
  E_CPONLY(CEF_ColorExtensionIconDecorationBackground) \
  E_CPONLY(CEF_ColorExtensionIconDecorationKeyShadow) \
  E_CPONLY(CEF_ColorExtensionMenuIcon) \
  E_CPONLY(CEF_ColorExtensionMenuIconDisabled) \
  E_CPONLY(CEF_ColorExtensionMenuPinButtonIcon) \
  E_CPONLY(CEF_ColorExtensionMenuPinButtonIconDisabled) \
  E_CPONLY(CEF_ColorExtensionsMenuContainerBackground) \
  E_CPONLY(CEF_ColorExtensionsToolbarControlsBackground) \
  E_CPONLY(CEF_ColorExtensionsMenuText) \
  E_CPONLY(CEF_ColorExtensionsMenuSecondaryText) \
  /* Feature Promo bubble colors. */ \
  E_CPONLY(CEF_ColorFeaturePromoBubbleBackground) \
  E_CPONLY(CEF_ColorFeaturePromoBubbleButtonBorder) \
  E_CPONLY(CEF_ColorFeaturePromoBubbleCloseButtonInkDrop) \
  E_CPONLY(CEF_ColorFeaturePromoBubbleDefaultButtonBackground) \
  E_CPONLY(CEF_ColorFeaturePromoBubbleDefaultButtonForeground) \
  E_CPONLY(CEF_ColorFeaturePromoBubbleForeground) \
  E_CPONLY(CEF_ColorFeatureLensPromoBubbleBackground) \
  E_CPONLY(CEF_ColorFeatureLensPromoBubbleForeground) \
  /* Find bar colors. */ \
  E_CPONLY(CEF_ColorFindBarBackground) \
  E_CPONLY(CEF_ColorFindBarButtonIcon) \
  E_CPONLY(CEF_ColorFindBarButtonIconDisabled) \
  E_CPONLY(CEF_ColorFindBarForeground) \
  E_CPONLY(CEF_ColorFindBarMatchCount) \
  /* Flying Indicator colors. */ \
  E_CPONLY(CEF_ColorFlyingIndicatorBackground) \
  E_CPONLY(CEF_ColorFlyingIndicatorForeground) \
  /* Default accessibility focus highlight. */ \
  E_CPONLY(CEF_ColorFocusHighlightDefault) \
  /* Frame caption colors. */ \
  E_CPONLY(CEF_ColorFrameCaptionActive) \
  E_CPONLY(CEF_ColorFrameCaptionInactive) \
  /* History embedding colors. */ \
  E_CPONLY(CEF_ColorHistoryEmbeddingsBackground) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsDivider) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsForeground) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsForegroundSubtle) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsImageBackground) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsImageBackgroundGradientEnd) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsImageBackgroundGradientStart) \
  E_CPONLY(CEF_ColorHistoryEmbeddingsWithAnswersBackground) \
  /* InfoBar colors. */ \
  E_CPONLY(CEF_ColorInfoBarBackground) \
  E_CPONLY(CEF_ColorInfoBarButtonIcon) \
  E_CPONLY(CEF_ColorInfoBarButtonIconDisabled) \
  E_CPONLY(CEF_ColorInfoBarContentAreaSeparator) \
  E_CPONLY(CEF_ColorInfoBarForeground) \
  /* There is also a CEF_ColorInfoBarIcon in /ui/color/color_id.h */ \
  /* Intent Picker colors. */ \
  E_CPONLY(CEF_ColorIntentPickerItemBackgroundHovered) \
  E_CPONLY(CEF_ColorIntentPickerItemBackgroundSelected) \
  /* Hover Button colors */ \
  E_CPONLY(CEF_ColorHoverButtonBackgroundHovered) \
  /* Lens overlay colors. */ \
  E_CPONLY(CEF_ColorLensOverlayToastBackground) \
  E_CPONLY(CEF_ColorLensOverlayToastButtonBorder) \
  E_CPONLY(CEF_ColorLensOverlayToastForeground) \
  /* Location bar colors. */ \
  E_CPONLY(CEF_ColorLocationBarBackground) \
  E_CPONLY(CEF_ColorLocationBarBackgroundHovered) \
  E_CPONLY(CEF_ColorLocationBarBorder) \
  E_CPONLY(CEF_ColorLocationBarBorderOnMismatch) \
  E_CPONLY(CEF_ColorLocationBarBorderOpaque) \
  E_CPONLY(CEF_ColorLocationBarClearAllButtonIcon) \
  E_CPONLY(CEF_ColorLocationBarClearAllButtonIconDisabled) \
  /* Media router colors. */ \
  E_CPONLY(CEF_ColorMediaRouterIconActive) \
  E_CPONLY(CEF_ColorMediaRouterIconWarning) \
  /* New tab button colors. */ \
  E_CPONLY(CEF_ColorNewTabButtonForegroundFrameActive) \
  E_CPONLY(CEF_ColorNewTabButtonForegroundFrameInactive) \
  E_CPONLY(CEF_ColorNewTabButtonBackgroundFrameActive) \
  E_CPONLY(CEF_ColorNewTabButtonBackgroundFrameInactive) \
  E_CPONLY(CEF_ColorNewTabButtonFocusRing) \
  E_CPONLY(CEF_ColorNewTabButtonInkDropFrameActive) \
  E_CPONLY(CEF_ColorNewTabButtonInkDropFrameInactive) \
  E_CPONLY(CEF_ColorTabStripControlButtonInkDrop) \
  E_CPONLY(CEF_ColorTabStripControlButtonInkDropRipple) \
  /* New tab button colors for ChromeRefresh.*/ \
  /* TODO (crbug.com/1399942) remove when theming works */ \
  E_CPONLY(CEF_ColorNewTabButtonCRForegroundFrameActive) \
  E_CPONLY(CEF_ColorNewTabButtonCRForegroundFrameInactive) \
  E_CPONLY(CEF_ColorNewTabButtonCRBackgroundFrameActive) \
  E_CPONLY(CEF_ColorNewTabButtonCRBackgroundFrameInactive) \
  /* New Tab Page colors. */ \
  E_CPONLY(CEF_ColorNewTabPageActionButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageActionButtonBorder) \
  E_CPONLY(CEF_ColorNewTabPageActionButtonBorderHovered) \
  E_CPONLY(CEF_ColorNewTabPageActionButtonForeground) \
  E_CPONLY(CEF_ColorNewTabPageActiveBackground) \
  E_CPONLY(CEF_ColorNewTabPageAddShortcutBackground) \
  E_CPONLY(CEF_ColorNewTabPageAddShortcutForeground) \
  E_CPONLY(CEF_ColorNewTabPageAttributionForeground) \
  E_CPONLY(CEF_ColorNewTabPageBackground) \
  E_CPONLY(CEF_ColorNewTabPageBackgroundOverride) \
  E_CPONLY(CEF_ColorNewTabPageBorder) \
  E_CPONLY(CEF_ColorNewTabPageButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageButtonBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageButtonForeground) \
  E_CPONLY(CEF_ColorNewTabPageCartModuleDiscountChipBackground) \
  E_CPONLY(CEF_ColorNewTabPageCartModuleDiscountChipForeground) \
  E_CPONLY(CEF_ColorNewTabPageChipBackground) \
  E_CPONLY(CEF_ColorNewTabPageChipForeground) \
  E_CPONLY(CEF_ColorNewTabPageControlBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageControlBackgroundSelected) \
  E_CPONLY(CEF_ColorNewTabPageDialogBackground) \
  E_CPONLY(CEF_ColorNewTabPageDialogBackgroundActive) \
  E_CPONLY(CEF_ColorNewTabPageDialogBorder) \
  E_CPONLY(CEF_ColorNewTabPageDialogBorderSelected) \
  E_CPONLY(CEF_ColorNewTabPageDialogControlBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageDialogForeground) \
  E_CPONLY(CEF_ColorNewTabPageDialogSecondaryForeground) \
  E_CPONLY(CEF_ColorNewTabPageFirstRunBackground) \
  E_CPONLY(CEF_ColorNewTabPageFocusRing) \
  E_CPONLY(CEF_ColorNewTabPageHeader) \
  E_CPONLY(CEF_ColorNewTabPageHistoryClustersModuleItemBackground) \
  E_CPONLY(CEF_ColorNewTabPagePromoBackground) \
  E_CPONLY(CEF_ColorNewTabPagePromoImageBackground) \
  E_CPONLY(CEF_ColorNewTabPageIconButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageIconButtonBackgroundActive) \
  E_CPONLY(CEF_ColorNewTabPageLink) \
  E_CPONLY(CEF_ColorNewTabPageLogo) \
  E_CPONLY(CEF_ColorNewTabPageLogoUnthemedDark) \
  E_CPONLY(CEF_ColorNewTabPageLogoUnthemedLight) \
  E_CPONLY(CEF_ColorNewTabPageMenuInnerShadow) \
  E_CPONLY(CEF_ColorNewTabPageMenuOuterShadow) \
  E_CPONLY(CEF_ColorNewTabPageMicBorderColor) \
  E_CPONLY(CEF_ColorNewTabPageMicIconColor) \
  E_CPONLY(CEF_ColorNewTabPageMobilePromoDismissButton) \
  E_CPONLY(CEF_ColorNewTabPageModuleControlBorder) \
  E_CPONLY(CEF_ColorNewTabPageModuleContextMenuDivider) \
  E_CPONLY(CEF_ColorNewTabPageModuleBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleCalendarEventTimeStatusBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleCalendarAttachmentScrollbarThumb) \
  E_CPONLY(CEF_ColorNewTabPageModuleCalendarDividerColor) \
  E_CPONLY(CEF_ColorNewTabPageModuleIconBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleElementDivider) \
  E_CPONLY(CEF_ColorNewTabPageModuleIconContainerBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleItemBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleItemBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageModuleScrollButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageModuleScrollButtonBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageMostVisitedForeground) \
  E_CPONLY(CEF_ColorNewTabPageMostVisitedTileBackground) \
  E_CPONLY(CEF_ColorNewTabPageMostVisitedTileBackgroundThemed) \
  E_CPONLY(CEF_ColorNewTabPageMostVisitedTileBackgroundUnthemed) \
  E_CPONLY(CEF_ColorNewTabPageOnThemeForeground) \
  E_CPONLY(CEF_ColorNewTabPageOverlayBackground) \
  E_CPONLY(CEF_ColorNewTabPageOverlayForeground) \
  E_CPONLY(CEF_ColorNewTabPageOverlaySecondaryForeground) \
  E_CPONLY(CEF_ColorNewTabPagePrimaryForeground) \
  E_CPONLY(CEF_ColorNewTabPageSearchBoxBackground) \
  E_CPONLY(CEF_ColorNewTabPageSearchBoxBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageSearchBoxResultsTextDimmedSelected) \
  E_CPONLY(CEF_ColorNewTabPageSecondaryForeground) \
  E_CPONLY(CEF_ColorNewTabPageSectionBorder) \
  E_CPONLY(CEF_ColorNewTabPageSelectedBackground) \
  E_CPONLY(CEF_ColorNewTabPageSelectedBorder) \
  E_CPONLY(CEF_ColorNewTabPageSelectedForeground) \
  E_CPONLY(CEF_ColorNewTabPageTagBackground) \
  E_CPONLY(CEF_ColorNewTabPageText) \
  E_CPONLY(CEF_ColorNewTabPageTextUnthemed) \
  E_CPONLY(CEF_ColorNewTabPageTextLight) \
  E_CPONLY(CEF_ColorNewTabPageWallpaperSearchButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageWallpaperSearchButtonBackgroundHovered) \
  E_CPONLY(CEF_ColorNewTabPageWallpaperSearchButtonForeground) \
  /* New Tab Page Colors for Doodle Share Button. */ \
  E_CPONLY(CEF_ColorNewTabPageDoodleShareButtonBackground) \
  E_CPONLY(CEF_ColorNewTabPageDoodleShareButtonIcon) \
  /* Omnibox colors. */ \
  E_CPONLY(CEF_ColorOmniboxActionIcon) \
  E_CPONLY(CEF_ColorOmniboxActionIconHover) \
  E_CPONLY(CEF_ColorOmniboxAnswerIconGM3Background) \
  E_CPONLY(CEF_ColorOmniboxAnswerIconGM3Foreground) \
  E_CPONLY(CEF_ColorOmniboxBubbleOutline) \
  E_CPONLY(CEF_ColorOmniboxBubbleOutlineExperimentalKeywordMode) \
  E_CPONLY(CEF_ColorOmniboxChipInUseActivityIndicatorBackground) \
  E_CPONLY(CEF_ColorOmniboxChipInUseActivityIndicatorForeground) \
  E_CPONLY(CEF_ColorOmniboxChipBackground) \
  E_CPONLY(CEF_ColorOmniboxChipBlockedActivityIndicatorBackground) \
  E_CPONLY(CEF_ColorOmniboxChipBlockedActivityIndicatorForeground) \
  E_CPONLY(CEF_ColorOmniboxChipForegroundLowVisibility) \
  E_CPONLY(CEF_ColorOmniboxChipForegroundNormalVisibility) \
  E_CPONLY(CEF_ColorOmniboxChipInkDropHover) \
  E_CPONLY(CEF_ColorOmniboxChipInkDropRipple) \
  E_CPONLY(CEF_ColorOmniboxChipOnSystemBlockedActivityIndicatorBackground) \
  E_CPONLY(CEF_ColorOmniboxChipOnSystemBlockedActivityIndicatorForeground) \
  E_CPONLY(CEF_ColorOmniboxIconBackground) \
  E_CPONLY(CEF_ColorOmniboxIconBackgroundTonal) \
  E_CPONLY(CEF_ColorOmniboxIconForeground) \
  E_CPONLY(CEF_ColorOmniboxIconForegroundTonal) \
  E_CPONLY(CEF_ColorOmniboxIconHover) \
  E_CPONLY(CEF_ColorOmniboxIconPressed) \
  E_CPONLY(CEF_ColorOmniboxIntentChipBackground) \
  E_CPONLY(CEF_ColorOmniboxIntentChipIcon) \
  E_CPONLY(CEF_ColorOmniboxKeywordSelected) \
  E_CPONLY(CEF_ColorOmniboxKeywordSeparator) \
  E_CPONLY(CEF_ColorOmniboxResultsBackground) \
  E_CPONLY(CEF_ColorOmniboxResultsBackgroundHovered) \
  E_CPONLY(CEF_ColorOmniboxResultsBackgroundSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsBackgroundIPH) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonBorder) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonIcon) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonIconSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDrop) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDropRowHovered) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDropRowSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDropSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDropSelectedRowHovered) \
  E_CPONLY(CEF_ColorOmniboxResultsButtonInkDropSelectedRowSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsChipBackground) \
  E_CPONLY(CEF_ColorOmniboxResultsFocusIndicator) \
  E_CPONLY(CEF_ColorOmniboxResultsIcon) \
  E_CPONLY(CEF_ColorOmniboxResultsIconGM3Background) \
  E_CPONLY(CEF_ColorOmniboxResultsIconSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsStarterPackIcon) \
  E_CPONLY(CEF_ColorOmniboxResultsTextAnswer) \
  E_CPONLY(CEF_ColorOmniboxResultsTextDimmed) \
  E_CPONLY(CEF_ColorOmniboxResultsTextDimmedSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsTextNegative) \
  E_CPONLY(CEF_ColorOmniboxResultsTextNegativeSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsTextPositive) \
  E_CPONLY(CEF_ColorOmniboxResultsTextPositiveSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsTextSecondary) \
  E_CPONLY(CEF_ColorOmniboxResultsTextSecondarySelected) \
  E_CPONLY(CEF_ColorOmniboxResultsTextSelected) \
  E_CPONLY(CEF_ColorOmniboxResultsUrl) \
  E_CPONLY(CEF_ColorOmniboxResultsUrlSelected) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipDangerous) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipDangerousBackground) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipDefault) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipInkDropHover) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipInkDropRipple) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipSecure) \
  E_CPONLY(CEF_ColorOmniboxSecurityChipText) \
  E_CPONLY(CEF_ColorOmniboxSelectionBackground) \
  E_CPONLY(CEF_ColorOmniboxSelectionForeground) \
  E_CPONLY(CEF_ColorOmniboxText) \
  E_CPONLY(CEF_ColorOmniboxTextDimmed) \
  /* Page Info colors */ \
  E_CPONLY(CEF_ColorPageInfoChosenObjectDeleteButtonIcon) \
  E_CPONLY(CEF_ColorPageInfoChosenObjectDeleteButtonIconDisabled) \
  E_CPONLY(CEF_ColorPageInfoForeground) \
  E_CPONLY(CEF_ColorPageInfoSubtitleForeground) \
  E_CPONLY(CEF_ColorPageInfoPermissionBlockedOnSystemLevelDisabled) \
  E_CPONLY(CEF_ColorPageInfoPermissionUsedIcon) \
  /* Payments colors. */ \
  E_CPONLY(CEF_ColorPaymentsFeedbackTipBackground) \
  E_CPONLY(CEF_ColorPaymentsFeedbackTipBorder) \
  E_CPONLY(CEF_ColorPaymentsFeedbackTipForeground) \
  E_CPONLY(CEF_ColorPaymentsFeedbackTipIcon) \
  E_CPONLY(CEF_ColorPaymentsGooglePayLogo) \
  E_CPONLY(CEF_ColorPaymentsPromoCodeBackground) \
  E_CPONLY(CEF_ColorPaymentsPromoCodeForeground) \
  E_CPONLY(CEF_ColorPaymentsPromoCodeForegroundHovered) \
  E_CPONLY(CEF_ColorPaymentsPromoCodeForegroundPressed) \
  E_CPONLY(CEF_ColorPaymentsPromoCodeInkDrop) \
  E_CPONLY(CEF_ColorPaymentsRequestBackArrowButtonIcon) \
  E_CPONLY(CEF_ColorPaymentsRequestBackArrowButtonIconDisabled) \
  E_CPONLY(CEF_ColorPaymentsRequestRowBackgroundHighlighted) \
  /* Permission Prompt colors. */ \
  E_CPONLY(CEF_ColorPermissionPromptRequestText) \
   /* Performance Intervention colors. */ \
  E_CPONLY(CEF_ColorPerformanceInterventionButtonIconActive) \
  E_CPONLY(CEF_ColorPerformanceInterventionButtonIconInactive) \
  /* Picture-in-Picture window colors. */ \
  E_CPONLY(CEF_ColorPipWindowBackToTabButtonBackground) \
  E_CPONLY(CEF_ColorPipWindowBackground) \
  E_CPONLY(CEF_ColorPipWindowControlsBackground) \
  E_CPONLY(CEF_ColorPipWindowTopBarBackground) \
  E_CPONLY(CEF_ColorPipWindowForeground) \
  E_CPONLY(CEF_ColorPipWindowForegroundInactive) \
  E_CPONLY(CEF_ColorPipWindowHangUpButtonForeground) \
  E_CPONLY(CEF_ColorPipWindowSkipAdButtonBackground) \
  E_CPONLY(CEF_ColorPipWindowSkipAdButtonBorder) \
  /* Product Specifications colors */ \
  E_CPONLY(CEF_ColorProductSpecificationsButtonBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsCitationBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsDetailChipBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsDisclosureBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsDisclosureForeground) \
  E_CPONLY(CEF_ColorProductSpecificationsDisclosureGradientEnd) \
  E_CPONLY(CEF_ColorProductSpecificationsDisclosureGradientStart) \
  E_CPONLY(CEF_ColorProductSpecificationsDisclosureSummaryBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsDivider) \
  E_CPONLY(CEF_ColorProductSpecificationsGradientIcon) \
  E_CPONLY(CEF_ColorProductSpecificationsHorizontalCarouselScrollbarThumb) \
  E_CPONLY(CEF_ColorProductSpecificationsIcon) \
  E_CPONLY(CEF_ColorProductSpecificationsIconButtonBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsIconButtonHoveredBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsLink) \
  E_CPONLY(CEF_ColorProductSpecificationsPageBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsPrimaryTitle) \
  E_CPONLY(CEF_ColorProductSpecificationsSecondaryTitle) \
  E_CPONLY(CEF_ColorProductSpecificationsSummaryBackground) \
  E_CPONLY(CEF_ColorProductSpecificationsSummaryBackgroundDragging) \
  E_CPONLY(CEF_ColorProductSpecificationsTonalButtonBackground) \
  /* Profile Menu colors. */ \
  E_CPONLY(CEF_ColorProfileMenuBackground) \
  E_CPONLY(CEF_ColorProfileMenuHeaderBackground) \
  E_CPONLY(CEF_ColorProfileMenuIdentityInfoBackground) \
  E_CPONLY(CEF_ColorProfileMenuIdentityInfoTitle) \
  E_CPONLY(CEF_ColorProfileMenuIdentityInfoSubtitle) \
  E_CPONLY(CEF_ColorProfileMenuHeaderLabel) \
  E_CPONLY(CEF_ColorProfileMenuIconButton) \
  E_CPONLY(CEF_ColorProfileMenuIconButtonBackground) \
  E_CPONLY(CEF_ColorProfileMenuIconButtonBackgroundHovered) \
  E_CPONLY(CEF_ColorProfileMenuSyncErrorIcon) \
  E_CPONLY(CEF_ColorProfileMenuSyncIcon) \
  E_CPONLY(CEF_ColorProfileMenuSyncInfoBackground) \
  E_CPONLY(CEF_ColorProfileMenuSyncOffIcon) \
  E_CPONLY(CEF_ColorProfileMenuSyncPausedIcon) \
  /* Profiles colors. */ \
  E_CPONLY(CEF_ColorProfilesReauthDialogBorder) \
  /* PWA colors. */ \
  E_CPONLY(CEF_ColorPwaBackground) \
  E_CPONLY(CEF_ColorPwaMenuButtonIcon) \
  E_CPONLY(CEF_ColorPwaSecurityChipForeground) \
  E_CPONLY(CEF_ColorPwaSecurityChipForegroundDangerous) \
  E_CPONLY(CEF_ColorPwaSecurityChipForegroundPolicyCert) \
  E_CPONLY(CEF_ColorPwaSecurityChipForegroundSecure) \
  E_CPONLY(CEF_ColorPwaTabBarBottomSeparator) \
  E_CPONLY(CEF_ColorPwaTabBarTopSeparator) \
  E_CPONLY(CEF_ColorPwaTheme) \
  E_CPONLY(CEF_ColorPwaToolbarBackground) \
  E_CPONLY(CEF_ColorPwaToolbarButtonIcon) \
  E_CPONLY(CEF_ColorPwaToolbarButtonIconDisabled) \
  /* QR code colors. */ \
  E_CPONLY(CEF_ColorQrCodeBackground) \
  E_CPONLY(CEF_ColorQrCodeBorder) \
  /* Quick Answers colors. */ \
  E_CPONLY(CEF_ColorQuickAnswersReportQueryButtonBackground) \
  E_CPONLY(CEF_ColorQuickAnswersReportQueryButtonForeground) \
    /* Read Anything colors. */ \
  E_CPONLY(CEF_ColorReadAnythingBackground) \
  E_CPONLY(CEF_ColorReadAnythingBackgroundBlue) \
  E_CPONLY(CEF_ColorReadAnythingBackgroundDark) \
  E_CPONLY(CEF_ColorReadAnythingBackgroundLight) \
  E_CPONLY(CEF_ColorReadAnythingBackgroundYellow) \
  E_CPONLY(CEF_ColorReadAnythingCurrentReadAloudHighlight) \
  E_CPONLY(CEF_ColorReadAnythingCurrentReadAloudHighlightBlue) \
  E_CPONLY(CEF_ColorReadAnythingCurrentReadAloudHighlightDark) \
  E_CPONLY(CEF_ColorReadAnythingCurrentReadAloudHighlightLight) \
  E_CPONLY(CEF_ColorReadAnythingCurrentReadAloudHighlightYellow) \
  E_CPONLY(CEF_ColorReadAnythingFocusRingBackground) \
  E_CPONLY(CEF_ColorReadAnythingFocusRingBackgroundBlue) \
  E_CPONLY(CEF_ColorReadAnythingFocusRingBackgroundDark) \
  E_CPONLY(CEF_ColorReadAnythingFocusRingBackgroundLight) \
  E_CPONLY(CEF_ColorReadAnythingFocusRingBackgroundYellow) \
  E_CPONLY(CEF_ColorReadAnythingForeground) \
  E_CPONLY(CEF_ColorReadAnythingForegroundBlue) \
  E_CPONLY(CEF_ColorReadAnythingForegroundDark) \
  E_CPONLY(CEF_ColorReadAnythingForegroundLight) \
  E_CPONLY(CEF_ColorReadAnythingForegroundYellow) \
  E_CPONLY(CEF_ColorReadAnythingSeparator) \
  E_CPONLY(CEF_ColorReadAnythingSeparatorBlue) \
  E_CPONLY(CEF_ColorReadAnythingSeparatorDark) \
  E_CPONLY(CEF_ColorReadAnythingSeparatorLight) \
  E_CPONLY(CEF_ColorReadAnythingSeparatorYellow) \
  E_CPONLY(CEF_ColorReadAnythingDropdownBackground) \
  E_CPONLY(CEF_ColorReadAnythingDropdownBackgroundBlue) \
  E_CPONLY(CEF_ColorReadAnythingDropdownBackgroundDark) \
  E_CPONLY(CEF_ColorReadAnythingDropdownBackgroundLight) \
  E_CPONLY(CEF_ColorReadAnythingDropdownBackgroundYellow) \
  E_CPONLY(CEF_ColorReadAnythingDropdownSelected) \
  E_CPONLY(CEF_ColorReadAnythingDropdownSelectedBlue) \
  E_CPONLY(CEF_ColorReadAnythingDropdownSelectedDark) \
  E_CPONLY(CEF_ColorReadAnythingDropdownSelectedLight) \
  E_CPONLY(CEF_ColorReadAnythingDropdownSelectedYellow) \
  E_CPONLY(CEF_ColorReadAnythingTextSelection) \
  E_CPONLY(CEF_ColorReadAnythingTextSelectionBlue) \
  E_CPONLY(CEF_ColorReadAnythingTextSelectionDark) \
  E_CPONLY(CEF_ColorReadAnythingTextSelectionLight) \
  E_CPONLY(CEF_ColorReadAnythingTextSelectionYellow) \
  E_CPONLY(CEF_ColorReadAnythingLinkDefault) \
  E_CPONLY(CEF_ColorReadAnythingLinkDefaultBlue) \
  E_CPONLY(CEF_ColorReadAnythingLinkDefaultDark) \
  E_CPONLY(CEF_ColorReadAnythingLinkDefaultLight) \
  E_CPONLY(CEF_ColorReadAnythingLinkDefaultYellow) \
  E_CPONLY(CEF_ColorReadAnythingLinkVisited) \
  E_CPONLY(CEF_ColorReadAnythingLinkVisitedBlue) \
  E_CPONLY(CEF_ColorReadAnythingLinkVisitedDark) \
  E_CPONLY(CEF_ColorReadAnythingLinkVisitedLight) \
  E_CPONLY(CEF_ColorReadAnythingLinkVisitedYellow) \
  E_CPONLY(CEF_ColorReadAnythingPreviousReadAloudHighlight) \
  E_CPONLY(CEF_ColorReadAnythingPreviousReadAloudHighlightBlue) \
  E_CPONLY(CEF_ColorReadAnythingPreviousReadAloudHighlightDark) \
  E_CPONLY(CEF_ColorReadAnythingPreviousReadAloudHighlightLight) \
  E_CPONLY(CEF_ColorReadAnythingPreviousReadAloudHighlightYellow) \
  /* Realbox colors. */ \
  E_CPONLY(CEF_ColorSearchboxAnswerIconBackground) \
  E_CPONLY(CEF_ColorSearchboxAnswerIconForeground) \
  E_CPONLY(CEF_ColorSearchboxBackground) \
  E_CPONLY(CEF_ColorSearchboxBackgroundHovered) \
  E_CPONLY(CEF_ColorSearchboxBorder) \
  E_CPONLY(CEF_ColorSearchboxForeground) \
  E_CPONLY(CEF_ColorSearchboxLensVoiceIconBackground) \
  E_CPONLY(CEF_ColorSearchboxPlaceholder) \
  E_CPONLY(CEF_ColorSearchboxResultsActionChip) \
  E_CPONLY(CEF_ColorSearchboxResultsActionChipIcon) \
  E_CPONLY(CEF_ColorSearchboxResultsActionChipFocusOutline) \
  E_CPONLY(CEF_ColorSearchboxResultsBackground) \
  E_CPONLY(CEF_ColorSearchboxResultsBackgroundHovered) \
  E_CPONLY(CEF_ColorSearchboxResultsButtonHover) \
  E_CPONLY(CEF_ColorSearchboxResultsDimSelected) \
  E_CPONLY(CEF_ColorSearchboxResultsFocusIndicator) \
  E_CPONLY(CEF_ColorSearchboxResultsForeground) \
  E_CPONLY(CEF_ColorSearchboxResultsForegroundDimmed) \
  E_CPONLY(CEF_ColorSearchboxResultsIcon) \
  E_CPONLY(CEF_ColorSearchboxResultsIconFocusedOutline) \
  E_CPONLY(CEF_ColorSearchboxResultsIconSelected) \
  E_CPONLY(CEF_ColorSearchboxResultsUrl) \
  E_CPONLY(CEF_ColorSearchboxResultsUrlSelected) \
  E_CPONLY(CEF_ColorSearchboxSearchIconBackground) \
  E_CPONLY(CEF_ColorSearchboxSelectionBackground) \
  E_CPONLY(CEF_ColorSearchboxSelectionForeground) \
  E_CPONLY(CEF_ColorSearchboxShadow) \
  /* The colors used for saved tab group chips on the bookmark bar. */ \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundGrey) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundBlue) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundRed) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundYellow) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundGreen) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundPink) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundPurple) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundCyan) \
  E_CPONLY(CEF_ColorSavedTabGroupForegroundOrange) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineGrey) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineBlue) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineRed) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineYellow) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineGreen) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlinePink) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlinePurple) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineCyan) \
  E_CPONLY(CEF_ColorSavedTabGroupOutlineOrange) \
  /* Screenshot captured bubble colors. */ \
  E_CPONLY(CEF_ColorScreenshotCapturedImageBackground) \
  E_CPONLY(CEF_ColorScreenshotCapturedImageBorder) \
  /* Share-this-tab dialog colors. */ \
  E_CPONLY(CEF_ColorShareThisTabAudioToggleBackground) \
  E_CPONLY(CEF_ColorShareThisTabSourceViewBorder) \
  /* Experimentation */ \
  E_CPONLY(CEF_ColorShoppingPageActionIconBackgroundVariant) \
  E_CPONLY(CEF_ColorShoppingPageActionIconForegroundVariant) \
  /* Side panel colors. */ \
  E_CPONLY(CEF_ColorSidePanelBackground) \
  E_CPONLY(CEF_ColorSidePanelBadgeBackground) \
  E_CPONLY(CEF_ColorSidePanelBadgeBackgroundUpdated) \
  E_CPONLY(CEF_ColorSidePanelBadgeForeground) \
  E_CPONLY(CEF_ColorSidePanelBadgeForegroundUpdated) \
  E_CPONLY(CEF_ColorSidePanelBookmarksSelectedFolderBackground) \
  E_CPONLY(CEF_ColorSidePanelBookmarksSelectedFolderForeground) \
  E_CPONLY(CEF_ColorSidePanelBookmarksSelectedFolderIcon) \
  E_CPONLY(CEF_ColorSidePanelCardBackground) \
  E_CPONLY(CEF_ColorSidePanelCardPrimaryForeground) \
  E_CPONLY(CEF_ColorSidePanelCardSecondaryForeground) \
  E_CPONLY(CEF_ColorSidePanelCommerceGraphAxis) \
  E_CPONLY(CEF_ColorSidePanelCommerceGraphBubbleBackground) \
  E_CPONLY(CEF_ColorSidePanelCommerceGraphLine) \
  E_CPONLY(CEF_ColorSidePanelContentAreaSeparator) \
  E_CPONLY(CEF_ColorSidePanelContentBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeClassicChromeTileBorder) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeCornerNtpBorder) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeCustomOptionBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeCustomOptionForeground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpActiveTab) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpArrowsAndRefreshButton) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpBorder) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpCaron) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpCaronContainer) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpChromeLogo) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpOmnibox) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeMiniNtpTabStripBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeThemeBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeThemeCheckmarkBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeThemeCheckmarkForeground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeThemeSnapshotBackground) \
  E_CPONLY(CEF_ColorSidePanelCustomizeChromeWebStoreBorder) \
  E_CPONLY(CEF_ColorSidePanelDialogBackground) \
  E_CPONLY(CEF_ColorSidePanelDialogDivider) \
  E_CPONLY(CEF_ColorSidePanelDialogPrimaryForeground) \
  E_CPONLY(CEF_ColorSidePanelDialogSecondaryForeground) \
  E_CPONLY(CEF_ColorSidePanelDivider) \
  E_CPONLY(CEF_ColorSidePanelEditFooterBorder) \
  E_CPONLY(CEF_ColorSidePanelComboboxEntryIcon) \
  E_CPONLY(CEF_ColorSidePanelComboboxEntryTitle) \
  E_CPONLY(CEF_ColorSidePanelEntryIcon) \
  E_CPONLY(CEF_ColorSidePanelEntryDropdownIcon) \
  E_CPONLY(CEF_ColorSidePanelEntryTitle) \
  E_CPONLY(CEF_ColorSidePanelFilterChipBorder) \
  E_CPONLY(CEF_ColorSidePanelFilterChipForeground) \
  E_CPONLY(CEF_ColorSidePanelFilterChipForegroundSelected) \
  E_CPONLY(CEF_ColorSidePanelFilterChipIcon) \
  E_CPONLY(CEF_ColorSidePanelFilterChipIconSelected) \
  E_CPONLY(CEF_ColorSidePanelFilterChipBackgroundHover) \
  E_CPONLY(CEF_ColorSidePanelFilterChipBackgroundSelected) \
  E_CPONLY(CEF_ColorSidePanelHeaderButtonIcon) \
  E_CPONLY(CEF_ColorSidePanelHeaderButtonIconDisabled) \
  E_CPONLY(CEF_ColorSidePanelHoverResizeAreaHandle) \
  E_CPONLY(CEF_ColorSidePanelResizeAreaHandle) \
  E_CPONLY(CEF_ColorSidePanelScrollbarThumb) \
  E_CPONLY(CEF_ColorSidePanelTextfieldBorder) \
  E_CPONLY(CEF_ColorSidePanelWallpaperSearchTileBackground) \
  E_CPONLY(CEF_ColorSidePanelWallpaperSearchErrorButtonBackground) \
  E_CPONLY(CEF_ColorSidePanelWallpaperSearchErrorButtonText) \
  E_CPONLY(CEF_ColorSidePanelWallpaperSearchInspirationDescriptors) \
  /* Status bubble colors. */ \
  E_CPONLY(CEF_ColorStatusBubbleBackgroundFrameActive) \
  E_CPONLY(CEF_ColorStatusBubbleBackgroundFrameInactive) \
  E_CPONLY(CEF_ColorStatusBubbleForegroundFrameActive) \
  E_CPONLY(CEF_ColorStatusBubbleForegroundFrameInactive) \
  E_CPONLY(CEF_ColorStatusBubbleShadow) \
  /* Tab alert colors in tab strip. */ \
  E_CPONLY(CEF_ColorTabAlertAudioPlayingActiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertAudioPlayingActiveFrameInactive) \
  E_CPONLY(CEF_ColorTabAlertAudioPlayingInactiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertAudioPlayingInactiveFrameInactive) \
  E_CPONLY(CEF_ColorTabAlertMediaRecordingActiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertMediaRecordingActiveFrameInactive) \
  E_CPONLY(CEF_ColorTabAlertMediaRecordingInactiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertMediaRecordingInactiveFrameInactive) \
  E_CPONLY(CEF_ColorTabAlertPipPlayingActiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertPipPlayingActiveFrameInactive) \
  E_CPONLY(CEF_ColorTabAlertPipPlayingInactiveFrameActive) \
  E_CPONLY(CEF_ColorTabAlertPipPlayingInactiveFrameInactive) \
  /* Tab alert colors in hover cards */ \
  E_CPONLY(CEF_ColorHoverCardTabAlertMediaRecordingIcon) \
  E_CPONLY(CEF_ColorHoverCardTabAlertPipPlayingIcon) \
  E_CPONLY(CEF_ColorHoverCardTabAlertAudioPlayingIcon) \
  /* Tab colors. */ \
  E_CPONLY(CEF_ColorTabBackgroundActiveFrameActive) \
  E_CPONLY(CEF_ColorTabBackgroundActiveFrameInactive) \
  E_CPONLY(CEF_ColorTabBackgroundInactiveFrameActive) \
  E_CPONLY(CEF_ColorTabBackgroundInactiveFrameInactive) \
  E_CPONLY(CEF_ColorTabBackgroundInactiveHoverFrameActive) \
  E_CPONLY(CEF_ColorTabBackgroundInactiveHoverFrameInactive) \
  E_CPONLY(CEF_ColorTabBackgroundSelectedFrameActive) \
  E_CPONLY(CEF_ColorTabBackgroundSelectedFrameInactive) \
  E_CPONLY(CEF_ColorTabBackgroundSelectedHoverFrameActive) \
  E_CPONLY(CEF_ColorTabBackgroundSelectedHoverFrameInactive) \
  E_CPONLY(CEF_ColorTabCloseButtonFocusRingActive) \
  E_CPONLY(CEF_ColorTabCloseButtonFocusRingInactive) \
  E_CPONLY(CEF_ColorTabDiscardRingFrameActive) \
  E_CPONLY(CEF_ColorTabDiscardRingFrameInactive) \
  E_CPONLY(CEF_ColorTabFocusRingActive) \
  E_CPONLY(CEF_ColorTabFocusRingInactive) \
  E_CPONLY(CEF_ColorTabForegroundActiveFrameActive) \
  E_CPONLY(CEF_ColorTabForegroundActiveFrameInactive) \
  E_CPONLY(CEF_ColorTabForegroundInactiveFrameActive) \
  E_CPONLY(CEF_ColorTabForegroundInactiveFrameInactive) \
  E_CPONLY(CEF_ColorTabDividerFrameActive) \
  E_CPONLY(CEF_ColorTabDividerFrameInactive) \
  E_CPONLY(CEF_ColorTabHoverCardBackground) \
  E_CPONLY(CEF_ColorTabHoverCardForeground) \
  E_CPONLY(CEF_ColorTabHoverCardSecondaryText) \
  /* Tab group bookmark bar colors. */ \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarGrey) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarBlue) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarRed) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarYellow) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarGreen) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarPink) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarPurple) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarCyan) \
  E_CPONLY(CEF_ColorTabGroupBookmarkBarOrange) \
  /* The colors used for tab groups in the context submenu. */ \
  E_CPONLY(CEF_ColorTabGroupContextMenuBlue) \
  E_CPONLY(CEF_ColorTabGroupContextMenuCyan) \
  E_CPONLY(CEF_ColorTabGroupContextMenuGreen) \
  E_CPONLY(CEF_ColorTabGroupContextMenuGrey) \
  E_CPONLY(CEF_ColorTabGroupContextMenuOrange) \
  E_CPONLY(CEF_ColorTabGroupContextMenuPink) \
  E_CPONLY(CEF_ColorTabGroupContextMenuPurple) \
  E_CPONLY(CEF_ColorTabGroupContextMenuRed) \
  E_CPONLY(CEF_ColorTabGroupContextMenuYellow) \
  /* The colors used for tab groups in the bubble dialog view. */ \
  E_CPONLY(CEF_ColorTabGroupDialogGrey) \
  E_CPONLY(CEF_ColorTabGroupDialogBlue) \
  E_CPONLY(CEF_ColorTabGroupDialogRed) \
  E_CPONLY(CEF_ColorTabGroupDialogYellow) \
  E_CPONLY(CEF_ColorTabGroupDialogGreen) \
  E_CPONLY(CEF_ColorTabGroupDialogPink) \
  E_CPONLY(CEF_ColorTabGroupDialogPurple) \
  E_CPONLY(CEF_ColorTabGroupDialogCyan) \
  E_CPONLY(CEF_ColorTabGroupDialogOrange) \
  E_CPONLY(CEF_ColorTabGroupDialogIconEnabled) \
  /* The colors used for tab groups in the tabstrip. */ \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveGrey) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveBlue) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveRed) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveYellow) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveGreen) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActivePink) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActivePurple) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveCyan) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameActiveOrange) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveGrey) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveBlue) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveRed) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveYellow) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveGreen) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactivePink) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactivePurple) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveCyan) \
  E_CPONLY(CEF_ColorTabGroupTabStripFrameInactiveOrange) \
  E_CPONLY(CEF_ColorTabStrokeFrameActive) \
  E_CPONLY(CEF_ColorTabStrokeFrameInactive) \
  E_CPONLY(CEF_ColorTabstripLoadingProgressBackground) \
  E_CPONLY(CEF_ColorTabstripLoadingProgressForeground) \
  E_CPONLY(CEF_ColorTabstripScrollContainerShadow) \
  E_CPONLY(CEF_ColorTabThrobber) \
  E_CPONLY(CEF_ColorTabThrobberPreconnect) \
  /* Tab Search colors */ \
  E_CPONLY(CEF_ColorTabSearchButtonBackground) \
  E_CPONLY(CEF_ColorTabSearchBackground) \
  E_CPONLY(CEF_ColorTabSearchButtonCRForegroundFrameActive) \
  E_CPONLY(CEF_ColorTabSearchButtonCRForegroundFrameInactive) \
  E_CPONLY(CEF_ColorTabSearchCardBackground) \
  E_CPONLY(CEF_ColorTabSearchDisabled) \
  E_CPONLY(CEF_ColorTabSearchDisabledContainer) \
  E_CPONLY(CEF_ColorTabSearchDivider) \
  E_CPONLY(CEF_ColorTabSearchFooterBackground) \
  E_CPONLY(CEF_ColorTabSearchImageTabContentBottom) \
  E_CPONLY(CEF_ColorTabSearchImageTabContentTop) \
  E_CPONLY(CEF_ColorTabSearchImageTabText) \
  E_CPONLY(CEF_ColorTabSearchImageWindowFrame) \
  E_CPONLY(CEF_ColorTabSearchMediaIcon) \
  E_CPONLY(CEF_ColorTabSearchMediaRecordingIcon) \
  E_CPONLY(CEF_ColorTabSearchPrimaryForeground) \
  E_CPONLY(CEF_ColorTabSearchSecondaryForeground) \
  E_CPONLY(CEF_ColorTabSearchSelected) \
  E_CPONLY(CEF_ColorTabSearchScrollbarThumb) \
  /* Task Manager colors. */ \
  E_CPONLY(CEF_ColorTaskManagerBackground) \
  /* Thumbnail tab colors. */ \
  E_CPONLY(CEF_ColorThumbnailTabBackground) \
  E_CPONLY(CEF_ColorThumbnailTabForeground) \
  E_CPONLY(CEF_ColorThumbnailTabStripBackgroundActive) \
  E_CPONLY(CEF_ColorThumbnailTabStripBackgroundInactive) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveGrey) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveBlue) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveRed) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveYellow) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveGreen) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActivePink) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActivePurple) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveCyan) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameActiveOrange) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveGrey) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveBlue) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveRed) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveYellow) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveGreen) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactivePink) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactivePurple) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveCyan) \
  E_CPONLY(CEF_ColorThumbnailTabStripTabGroupFrameInactiveOrange) \
  /* Toolbar colors. */ \
  E_CPONLY(CEF_ColorToolbar) \
  E_CPONLY(CEF_ColorToolbarBackgroundSubtleEmphasis) \
  E_CPONLY(CEF_ColorToolbarBackgroundSubtleEmphasisHovered) \
  E_CPONLY(CEF_ColorToolbarButtonBackgroundHighlightedDefault) \
  E_CPONLY(CEF_ColorToolbarButtonBorder) \
  E_CPONLY(CEF_ColorToolbarButtonIcon) \
  E_CPONLY(CEF_ColorToolbarButtonIconDefault) \
  E_CPONLY(CEF_ColorToolbarButtonIconDisabled) \
  E_CPONLY(CEF_ColorToolbarButtonIconHovered) \
  E_CPONLY(CEF_ColorToolbarButtonIconInactive) \
  E_CPONLY(CEF_ColorToolbarButtonIconPressed) \
  E_CPONLY(CEF_ColorToolbarButtonText) \
  E_CPONLY(CEF_ColorToolbarContentAreaSeparator) \
  E_CPONLY(CEF_ColorToolbarExtensionSeparatorDisabled) \
  E_CPONLY(CEF_ColorToolbarExtensionSeparatorEnabled) \
  E_CPONLY(CEF_ColorToolbarFeaturePromoHighlight) \
  E_CPONLY(CEF_ColorToolbarIconContainerBorder) \
  E_CPONLY(CEF_ColorToolbarInkDrop) \
  E_CPONLY(CEF_ColorToolbarInkDropHover) \
  E_CPONLY(CEF_ColorToolbarInkDropRipple) \
  E_CPONLY(CEF_ColorToolbarSeparator) \
  E_CPONLY(CEF_ColorToolbarActionItemEngaged) \
  E_CPONLY(CEF_ColorToolbarSeparatorDefault) \
  E_CPONLY(CEF_ColorToolbarText) \
  E_CPONLY(CEF_ColorToolbarTextDefault) \
  E_CPONLY(CEF_ColorToolbarTextDisabled) \
  E_CPONLY(CEF_ColorToolbarTextDisabledDefault) \
  E_CPONLY(CEF_ColorToolbarTopSeparatorFrameActive) \
  E_CPONLY(CEF_ColorToolbarTopSeparatorFrameInactive) \
  /* WebAuthn colors. */ \
  E_CPONLY(CEF_ColorWebAuthnHoverButtonForeground) \
  E_CPONLY(CEF_ColorWebAuthnHoverButtonForegroundDisabled) \
  E_CPONLY(CEF_ColorWebAuthnBackArrowButtonIcon) \
  E_CPONLY(CEF_ColorWebAuthnBackArrowButtonIconDisabled) \
  E_CPONLY(CEF_ColorWebAuthnIconColor) \
  E_CPONLY(CEF_ColorWebAuthnIconColorDisabled) \
  E_CPONLY(CEF_ColorWebAuthnPinTextfieldBottomBorder) \
  E_CPONLY(CEF_ColorWebAuthnProgressRingBackground) \
  E_CPONLY(CEF_ColorWebAuthnProgressRingForeground) \
  /* Web contents colors. */ \
  E_CPONLY(CEF_ColorWebContentsBackground) \
  E_CPONLY(CEF_ColorWebContentsBackgroundLetterboxing) \
  /* WebUI Tab Strip colors. */ \
  E_CPONLY(CEF_ColorWebUiTabStripBackground) \
  E_CPONLY(CEF_ColorWebUiTabStripFocusOutline) \
  E_CPONLY(CEF_ColorWebUiTabStripIndicatorCapturing) \
  E_CPONLY(CEF_ColorWebUiTabStripIndicatorPip) \
  E_CPONLY(CEF_ColorWebUiTabStripIndicatorRecording) \
  E_CPONLY(CEF_ColorWebUiTabStripScrollbarThumb) \
  E_CPONLY(CEF_ColorWebUiTabStripTabActiveTitleBackground) \
  E_CPONLY(CEF_ColorWebUiTabStripTabActiveTitleContent) \
  E_CPONLY(CEF_ColorWebUiTabStripTabBackground) \
  E_CPONLY(CEF_ColorWebUiTabStripTabBlocked) \
  E_CPONLY(CEF_ColorWebUiTabStripTabLoadingSpinning) \
  E_CPONLY(CEF_ColorWebUiTabStripTabSeparator) \
  E_CPONLY(CEF_ColorWebUiTabStripTabText) \
  E_CPONLY(CEF_ColorWebUiTabStripTabWaitingSpinning) \
  /* Window control button background colors. */ \
  E_CPONLY(CEF_ColorWindowControlButtonBackgroundActive) \
  E_CPONLY(CEF_ColorWindowControlButtonBackgroundInactive) \

#if defined(OS_CHROMEOS)
#define CHROME_PLATFORM_SPECIFIC_COLOR_IDS \
    /* Borealis colors. */ \
    E_CPONLY(CEF_ColorBorealisSplashScreenBackground) \
    E_CPONLY(CEF_ColorBorealisSplashScreenForeground) \
    /* Caption colors. */ \
    E_CPONLY(CEF_ColorCaptionForeground) \
    /* Sharesheet colors. */ \
    E_CPONLY(CEF_ColorSharesheetTargetButtonIconShadow)
#elif defined(OS_WIN)
#define CHROME_PLATFORM_SPECIFIC_COLOR_IDS \
    /* The colors of the 1px border around the window on Windows 10. */ \
    E_CPONLY(CEF_ColorAccentBorderActive) \
    E_CPONLY(CEF_ColorAccentBorderInactive) \
    /* Caption colors. */ \
    E_CPONLY(CEF_ColorCaptionButtonForegroundActive) \
    E_CPONLY(CEF_ColorCaptionButtonForegroundInactive) \
    E_CPONLY(CEF_ColorCaptionCloseButtonBackgroundHovered) \
    E_CPONLY(CEF_ColorCaptionCloseButtonForegroundHovered) \
    E_CPONLY(CEF_ColorCaptionForegroundActive) \
    E_CPONLY(CEF_ColorCaptionForegroundInactive) \
    /* Tab search caption button colors. */ \
    E_CPONLY(CEF_ColorTabSearchCaptionButtonFocusRing)
#else
#define CHROME_PLATFORM_SPECIFIC_COLOR_IDS
#endif  // defined(OS_WIN)

#define CHROME_COLOR_IDS \
    COMMON_CHROME_COLOR_IDS CHROME_PLATFORM_SPECIFIC_COLOR_IDS

#include "include/base/internal/cef_color_id_macros.inc"

#ifdef __cplusplus
extern "C" {
#endif

///
/// All input, intermediary, and output colors known to CEF/Chromium.
/// Clients can optionally extend this enum with additional values.
/// Clients define enum values from CEF_ChromeColorsEnd. Values named
/// beginning with "CEF_Color" represent the actual colors; the rest are
/// markers.
///
typedef enum {
  CEF_UiColorsStart = 0,

  COLOR_IDS

  CEF_UiColorsEnd,

  CEF_ComponentsColorsStart = CEF_UiColorsEnd,

  COMPONENTS_COLOR_IDS

  CEF_ComponentsColorsEnd,

  CEF_ChromeColorsStart = CEF_ComponentsColorsEnd,

  CHROME_COLOR_IDS

  // Clients must start custom color IDs from this value.
  CEF_ChromeColorsEnd,

  // Clients must not assign IDs larger than this value. This is used to
  // verify that color IDs and color set IDs are not interchanged.
  CEF_UiColorsLast = 0xffff
} cef_color_id_t;

#ifdef __cplusplus
}
#endif

// Note that this second include is not redundant. The second inclusion of the
// .inc file serves to undefine the macros the first inclusion defined.
#include "include/base/internal/cef_color_id_macros.inc"

// Undefine the macros that were defined in this file.
#undef CHROMEOS_ASH_COLOR_IDS
#undef CHROME_COLOR_IDS
#undef CHROME_PLATFORM_SPECIFIC_COLOR_IDS
#undef COLOR_IDS
#undef COMMON_CHROME_COLOR_IDS
#undef COMMON_COMPONENTS_COLOR_IDS
#undef COMPONENTS_COLOR_IDS
#undef CROSS_PLATFORM_COLOR_IDS
#undef PLATFORM_SPECIFIC_COLOR_IDS

#endif  // CEF_INCLUDE_CEF_COLOR_IDS_H_
