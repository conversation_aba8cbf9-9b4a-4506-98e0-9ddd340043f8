/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include <SDL2/SDL.h>

#include <cstddef>
#include <filesystem>
#include <fstream>
#include <locale>
#include <string>
#include <vector>

namespace MyUtils
{

/*Numeric conversion separators - set those static variables as needed*/
extern char decimal_separator;
extern char thousand_separator;
extern std::string thousand_grouping;
extern std::string baseLocaleName;
extern int currency_decimal_places;

std::string GetCurrentTimeString();
std::string GetCurrentTimeStampString();
std::string GetCurrentDateString();

/**
 * IsTime checks LastTime and Interval against current time with SDL_GetTicks64() and returns true if more than Interval has passed since LastTime.
 * @param LastTime - This value will be overwritten in case the function returns true or overflow occoured. Set this value to 0 when first calling this function
 * @param Interval - Desired interval
 * @param UpdateLastTime - if the IsTime should automaticaly update LastTime when it returns true
 * @return
 */
bool IsTime(Uint64& LastTime, Uint64 Interval, bool UpdateLastTime = false);

/**
 * CompareTimes compares Time1 against Time2 and returns true if has passed Time2.
 * @param Time1 - First time
 * @param Time2 - Second time
 * @return
 */
bool CompareTimes(Uint64 Time1, Uint64 Time2, Uint64 Diff = 0);

int PostSDLEvent(int UserCode, void* pUserData1, void* pUserData2);

void UpdateLocale(const std::string& baseLocale);

std::string int2str(int64_t val);    // thread safe - transforms using current locale
std::string currency2string(double value);

std::string dtos(double value, int precision = 6, bool useLocale = false);    // thread safe - transforms using locale "C" //uporabljaj za generiranje SQL-ov

double round(double n, unsigned d); /* round number n to d decimal places */

// Function to find the greatest common divisor (GCD) using the Euclidean algorithm
uint32_t greatestCommonDivisor(uint32_t a, uint32_t b);

// Function to convert a decimal to a fraction
void decimalToFraction(double decimal, uint32_t& numerator, uint32_t& denominator);

std::string ToUppercase(const std::string& Input, bool OnlyFirst = false);
std::string ToLowercase(const std::string& Input, bool SkipFirst = false);

uint32_t GetUTF8CodePoint(const char* str, const char** end = NULL);

void NumberToText(const char* only_number /*number in text form - max 60 digits*/, char* outputString);    // converts a number in digits to a number with english words

bool ContainsURL(const std::string& str);

/* funkcija pretvori Data, ki je estnajstiki zapis tevila v string obliki("23fc") v estnajstiki zapis tevila v dvojiki obliki(0x23 0xFC HexLength = 2)
dela na obstoje�m bufferju - Data se spremeni po izvedbi te funkcije!!!zadnji byti se pretvorijo v dvojiki zapis hex.
HexLength - je izhodni parameter in pove dolino binarnega hex zapisa, lahko je NULL */
unsigned char* ASCIIHexToBinHex(const char* pString, unsigned char* pResult, unsigned* HexLength);

/* funkcija pretvori Data, ki je estnajstiki zapis tevila v string obliki("23fc") v estnajstiki zapis tevila v dvojiki obliki(0x23 0xFC HexLength = 2)
dela na obstoje�m bufferju - Data se spremeni po izvedbi te funkcije!!!zadnji byti se pretvorijo v dvojiki zapis hex.
HexLength - je vhodno/izhodni parameter in pove maximalno dolino binarnega hex zapisa, ob izhodu pa koliko bytov je bilo zapisanih v hex */
char* BinHexToASCIIHex(unsigned char* Data, char* pDestination, unsigned* DataLength);

int SystemExec(const std::string& cmd, std::vector<std::string>& outLines);

/* funkcija ki izvede system v drugi nitki .. cmd length = 2048 */
void SystemAsync(const std::string& cmd, bool bAsChild = true);

void StringReplace(std::string& str, const std::string& from, const std::string& to);

#ifdef GRAPHICS    // ---------------------- REQUIRE GRAPHICS ----------------------
SDL_Surface* LoadSurface(const std::string& path, Uint32 transcodeIntoPixelFormat = 0);
#endif    // ------------------ END REQUIRE GRAPHICS ---------------------

bool FolderExists(const std::filesystem::path& path);

bool FileExists(const std::filesystem::path& path, bool checkIfRegFile = true, bool chechIfLink = false);

struct FPartitionInfo
{
	std::filesystem::path MountedPath;
	std::string Label;
	bool bReadOnly = false;
	uint64_t SizeMB = 0;
};

struct FRemovableMediaInformation
{
	std::filesystem::path BlockDevicePath;
	std::string Vendor;
	std::string Model;
	std::filesystem::file_time_type ConnectedTime;
	bool bReadOnly = false;
	uint64_t SizeMB = 0;

	std::vector<FPartitionInfo> Partitions;
};

std::vector<FRemovableMediaInformation> GetAllStorageUSBs();

std::string CreateLoggableTicketNumber(const std::string& ticketValidationNumber);

void UnescapeXml(std::string& xml);

template <int len = 512>
inline std::string Format(const char* format, ...)
{
	va_list args;
	va_start(args, format);
	char buf[len];
	vsnprintf(buf, len - 1, format, args);
	va_end(args);
	return std::string(buf);
}

enum PingResult
{
	PING_OK,
	PING_TIMEOUT,
	PING_BAD_HOST,
	PING_ERROR,
	PING_PACKET_LOSS
};

}    // namespace MyUtils