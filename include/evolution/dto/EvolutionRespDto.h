#pragma once

#include "Json.h"
#include "evolution/EvolutionSharedTypes.h"

class EvolutionRespDto
{
   public:
	virtual ~EvolutionRespDto() = default;
	// members
	/**
	 * @brief Describes status of request. One of the “status” enumerated values.
	 * - If response header is not HTTP 200, it is mapped to TEMPORARY_ERROR.
	 * - If response cannot be parsed, it is mapped to TEMPORARY_ERROR.
	 * - Any values that are not in the list are mapped to UNKNOWN_ERROR
	 */
	EStatus Status = EStatus::OK;

	/**
	 * @brief Unique response id, that identifies StandardResponse. Optional field
	 */
	std::optional<std::string> Sid;

	// constructors
	EvolutionRespDto(EStatus status, const std::optional<std::string>& sid);

	// methods
	virtual json ToJSON() const;
	static EvolutionRespDto FromJSON(const json& val);
	bool operator==(const EvolutionRespDto& other) const = default;
};
