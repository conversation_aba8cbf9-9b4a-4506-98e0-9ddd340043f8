#pragma once

#include "evolution/dto/PromoPayoutReqDto.h"

class PromoPayoutAdminReqDto final : public PromoPayoutReqDto
{
   public:
	// members
	std::string AdminKey;

	// constructors
	PromoPayoutAdminReqDto();
	PromoPayoutAdminReqDto(const PromoPayoutReqDto& promoPayoutReqDto, const std::string& adminKey = {});

	// methods
	const static JsonSchema PromoPayoutAdminReqSchema;
	void OnConfigLoaded(const std::filesystem::path& loc) override;
	json ToJSON() const override;
	static PromoPayoutAdminReqDto FromJSON(const json& val);
};
