#pragma once

#include "Json.h"
#include "JsonSchema.h"

class TransactionDto
{
   public:
	// members
	/** @brief The unique identifier of a transaction (e.g. used to avoid duplicate transactions and for other validations). */
	std::string ID;
	/** @brief Reference identifier for a transaction, to be able to link (correlate) and/or validate CreditRequest/CancelRequest to appropriate DebitRequest. */
	std::string RefID;
	/** @brief Amount of transaction in player’s session currency, rounded to 6 decimal places. */
	double Amount = 0;

	// constructors
	TransactionDto() = default;
	TransactionDto(const std::string& id, const std::string& refId, double amount);

	// methods
	json ToJSON() const;
	static TransactionDto FromJSON(const json& val);
	const static JsonSchema& TransactionSchema();
	bool operator==(const TransactionDto& other) const = default;
};
