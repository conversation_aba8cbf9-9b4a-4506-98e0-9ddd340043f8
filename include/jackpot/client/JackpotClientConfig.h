#pragma once

#include "Currencies.h"
#include "JsonSchema.h"
#include "jackpot/JackpotSharedTypes.h"

class JackpotClientConfig
{
   public:
	// members
	std::string ClientId;
	EJackpotClientType ClientType = EJackpotClientType::Client;
	ECurrency Currency = ECurrency::EUR;
	double Denomination = 0.01;
	std::string ExternalTriggerType;
	std::string Location;

	// constructors
	JackpotClientConfig() = default;
	JackpotClientConfig(const std::string& clientId, ECurrency currency = ECurrency::EUR, double denomination = 0.01, const std::string& externalTriggerType = {},
	                    EJackpotClientType clientType = EJackpotClientType::Client, const std::string& location = {});

	// methods
	static const JsonSchema& JackpotClientConfigSchema();
	static JackpotClientConfig FromJSON(const json& val);
	json ToJSON() const;
	bool operator==(const JackpotClientConfig& other) const = default;
};
