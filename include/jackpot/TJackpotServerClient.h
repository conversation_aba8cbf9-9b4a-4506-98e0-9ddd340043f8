#pragma once

#include "Currencies.h"
#include "jackpot/JackpotSharedTypes.h"
#include "jackpot/client/JackpotClientConfig.h"
#include "web/yprotocol/YProtocol.h"

namespace jackpot
{

// This is the SERVER-SIDE representation of a client instance!
class TJackpotServerClient : public yprotocol::YProtocolClient
{
   public:
	std::string ID;
	EJackpotClientType ClientType = EJackpotClientType::Client;
	ECurrency Currency = ECurrency::EUR;
	double Denomination = 0;
	std::string ExternalTriggerType;
	std::string Location;

	TJackpotServerClient(yprotocol::YProtocol* server, std::shared_ptr<yprotocol::YRequestHandler> requestHandler, const std::string& id, EJackpotClientType clientType,
	                     ECurrency currency = ECurrency::EUR, double denomination = 0, const std::string& externalTriggerType = {}, const std::string& location = {},
	                     std::optional<security::JWToken> token = {});

	JackpotClientConfig GetJackpotClientConfig() const;
};

}    // namespace jackpot
