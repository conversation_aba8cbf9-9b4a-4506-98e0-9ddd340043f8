#pragma once

#include "jackpot/dto/ClientInfoDto.h"
#include "jackpot/dto/RequestDto.h"
#include "web/WebUtils.h"

class ListClientInfoReqDto : public RequestDtoTConf
{
   public:
	// members
	ClientInfoFilter Filter;

	// constructors
	ListClientInfoReqDto();
	ListClientInfoReqDto(const web::QueryString& query);
	ListClientInfoReqDto(const ClientInfoFilter& filter);

	// methods
	void OnConfigLoaded(const std::filesystem::path& loc) override;
	const static JsonSchema ListClientInfoReqSchema;
	web::QueryString ToQueryString() const;
	json ToJSON() const override;
	bool operator==(const ListClientInfoReqDto& other) const = default;
};
