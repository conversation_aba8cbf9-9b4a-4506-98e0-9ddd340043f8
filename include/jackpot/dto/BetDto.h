#pragma once

#include "jackpot/JackpotSharedTypes.h"
#include "jackpot/dto/BetProcessedDto.h"
#include "jackpot/dto/ClientBetsRespDto.h"
#include "jackpot/dto/InitRespDto.h"
#include "jackpot/dto/JackpotHistoryEntry.h"
#include "jackpot/dto/JackpotWonEventDto.h"
#include "web/WebUtils.h"

class BetFilter
{
   public:
	// members
	std::optional<std::string> ClientID;
	std::unordered_set<EJackpotBetStatus> Statuses;
	uint64_t FromTimeMs = 0;
	uint64_t ToTimeMs = 0;

	// constructors
	explicit BetFilter(const std::optional<std::string>& clientID = {}, const std::unordered_set<EJackpotBetStatus>& statuses = {}, uint64_t fromTimeMs = 0,
	                   uint64_t toTimeMs = 0);

	// methods
	web::QueryString ToQueryString() const;
	static BetFilter FromQueryString(const web::QueryString& query);
	bool operator==(const BetFilter& rhs) const = default;
};

class BetDto
{
   public:
	/**
	 * The id of the bet (uuid).
	 */
	std::string ID;
	/**
	 * The id of the client (uuid).
	 */
	std::string ClientID;
	/**
	 * The id of the jackpot level (uuid).
	 */
	std::string LevelID;
	/**
	 * The name of the player (display name).
	 */
	std::string PlayerName;
	/**
	 * The time of the bet (milliseconds since epoch).
	 */
	uint64_t BetTimeMs;
	/**
	 * The amount of the bet (full bet).
	 */
	double Amount = 0.0;
	/**
	 * Paid in amount (the part of the bet that is paid in the jackpot - goes to pot + backPot).
	 */
	double PaidInAmount = 0.0;
	/**
	 * The won amount
	 */
	double WonAmount = 0.0;
	/**
	 * The amount that was credited to the client (in currency units)
	 */
	double CreditedAmount = 0.0;
	/**
	 * The amount of credits that were credited to the client.
	 */
	uint64_t Credits = 0;
	/**
	 * The denomination of the bet.
	 */
	double Denomination = 0.0;
	/**
	 * The status of the bet.
	 */
	EJackpotBetStatus Status = EJackpotBetStatus::Lost;
	/**
	 * The bet reference is a unique identifier for the bet (unique inside a single request). It is used to properly match the bet ID from the response that has array of
	 * bets.
	 */
	std::string BetReference;
	/**
	 * The player reference is a unique identifier for the player (unique per casino or group).
	 */
	std::string PlayerReference;
	/**
	 * The session reference is a unique identifier for the session (unique per player and game).
	 */
	std::string SessionReference;
	/**
	 * The round reference is a unique identifier for the round.
	 */
	std::string RoundReference;
	/**
	 * The location of the client.
	 */
	std::string ClientLocation;
	/**
	 * The result of the bet is flagged (e.g. when something went wrong)
	 */
	bool IsFlagged = false;
	/**
	 * The external trigger type name (if the bet paidIn amount was triggered by an external trigger type). Empty string otherwise.
	 */
	std::string ExternalTriggerType;


	/**
	 * Converts this dto to json.
	 * @return
	 */
	[[nodiscard]] json ToJSON() const;

	/**
	 * Converts this dto to BetDataDto (used in ClientBetsRespDto).
	 * @return
	 */
	BetDataDto ToBetData() const;

	/**
	 * Converts this dto to WonBetDto (used in JackpotHistoryEntry).
	 * @return
	 */
	WonBetDto ToWonBet() const;

	/**
	 * Converts this dto to InitBetDto (used in InitRespDto)
	 * @return
	 */
	InitBetDto ToInitBet() const;

	/**
	 * Converts this dto to BetProcessedDto (used in BetsProcessedEventDto)
	 * @return
	 */
	BetProcessedDto ToBetProcessed() const;

	/**
	 * Converts this dto to JackpotWinnerDto (used in JackpotWonEventDto)
	 * @return
	 */
	JackpotWinnerDto ToJackpotWinner() const;

	/**
	 * Converts json to dto.
	 * @param val
	 * @return
	 */
	static BetDto FromJSON(const json& val);

	/**
	 * Checks if this dto matches the filter.
	 * @param filter The filter to match.
	 * @return True if the dto matches the filter, false otherwise.
	 */
	bool Match(const BetFilter& filter) const;

	bool operator==(const BetDto& rhs) const = default;

	BetDto(const std::string& betID, const std::string& clientID, const std::string& levelID, const std::string& playerName, uint64_t betTime, double amount,
	       double paidInAmount, double denomination, EJackpotBetStatus status, const std::string& betReference, const std::string& playerReference,
	       const std::string& sessionReference, const std::string& roundReference, const std::string& clientLocation, const std::string& externalTriggerType,
	       double wonAmount = 0.0, double creditedAmount = 0.0, uint64_t credits = 0, bool isFlagged = false);
};
