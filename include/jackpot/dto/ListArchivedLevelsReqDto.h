#pragma once

#include <unordered_set>

#include "jackpot/dto/RequestDto.h"

class ListArchivedLevelsReqDto : public RequestDtoTConf
{
   public:
	// members
	std::unordered_set<std::string> LevelIDs;

	// constructors
	ListArchivedLevelsReqDto();

	// methods
	void OnConfigLoaded(const std::filesystem::path& loc) override;
	const static JsonSchema ListArchivedLevelsReqSchema;
	json ToJSON() const override;
	bool operator==(const ListArchivedLevelsReqDto& rhs) const = default;
};
