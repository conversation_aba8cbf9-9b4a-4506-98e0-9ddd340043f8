#pragma once

#include "Json.h"

class SetJackpotLevelValueRespDto
{
   public:
	// members
	std::string LevelID;
	double PotChangedBy = 0;
	double BackPotChangedBy = 0;

	// constructors
	SetJackpotLevelValueRespDto(const std::string& levelID, double potChangedBy, double backpotChangedBy);

	// methods
	json ToJSON() const;
	static SetJackpotLevelValueRespDto FromJSON(const json& val);
	bool operator==(const SetJackpotLevelValueRespDto& other) const = default;
};
