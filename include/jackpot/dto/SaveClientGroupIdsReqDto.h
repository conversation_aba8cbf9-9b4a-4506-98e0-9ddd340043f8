#pragma once

#include <unordered_set>

#include "jackpot/dto/RequestDto.h"

class SaveClientGroupIdsReqDto : public RequestDtoTConf, public RequestDto
{
   public:
	// members
	std::string ClientID;
	std::unordered_set<std::string> ClientGroupIDs;

	// constructors
	SaveClientGroupIdsReqDto();
	SaveClientGroupIdsReqDto(const std::string& clientID, const std::unordered_set<std::string>& clientGroupIDs);

	// methods
	void OnConfigLoaded(const std::filesystem::path& loc) override;
	const static JsonSchema SaveClientGroupIdsReqSchema;
	static SaveClientGroupIdsReqDto FromJSON(const json& val);
	json ToJSON() const override;
};
