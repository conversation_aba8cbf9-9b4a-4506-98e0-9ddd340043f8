#pragma once

#include "jackpot/dto/JackpotLevelInfoBase.h"
#include "jackpot/dto/RequestDto.h"

class AddExternalTriggerTypeReqDto : public RequestDtoTConf
{
   public:
	// members
	std::string LevelID;
	std::unordered_map<std::string, ExternalTriggerTypeValues> ExternalTriggerTypeData;

	// constructors
	AddExternalTriggerTypeReqDto();

	// methods
	void OnConfigLoaded(const std::filesystem::path& loc) override;
	bool operator==(const AddExternalTriggerTypeReqDto& other) const = default;
	const static JsonSchema AddExternalTriggerTypeReqSchema;
	json ToJSON() const override;
};
