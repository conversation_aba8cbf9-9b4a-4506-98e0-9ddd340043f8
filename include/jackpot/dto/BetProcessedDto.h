#pragma once

#include "Json.h"
#include "jackpot/JackpotSharedTypes.h"

struct BetProcessedDto
{
	// members
	std::string BetID;
	std::string BetReference;
	std::string PlayerReference;
	std::string RoundReference;
	EJackpotBetStatus BetStatus = EJackpotBetStatus::Lost;
	double AmountWon = 0;
	uint64_t CreditsWon = 0;

	// methods
	bool operator==(const BetProcessedDto& other) const = default;
};

void to_json(json& sourceJson, const BetProcessedDto& dto);
void from_json(const json& sourceJson, BetProcessedDto& dto);
