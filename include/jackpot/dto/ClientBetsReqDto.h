#pragma once

#include "jackpot/dto/ClientBetDto.h"
#include "jackpot/dto/RequestDto.h"


class ClientBetsReqDto : public RequestDto
{
   public:
	// members
	std::vector<ClientBetDto> ClientBets;

	// constructors
	ClientBetsReqDto(std::vector<ClientBetDto> clientBets);
	explicit ClientBetsReqDto(const json& req);

	// methods
	const static JsonSchema ClientBetsReqSchema;
	json ToJSON() const override;
	bool operator==(const ClientBetsReqDto& other) const = default;
};
