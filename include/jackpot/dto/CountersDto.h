#pragma once

#include <cstdint>

#include "Json.h"

class CountersDto
{
   public:
	// members
	uint32_t WinCounter = 0;
	double BetAmountCounter = 0.0;
	double PaidInAmountCounter = 0.0;
	double WinAmountCounter = 0.0;
	double UserAddedCounter = 0.0;

	// constructors
	CountersDto() = default;
	CountersDto(uint32_t winCounter, double betAmountCounter, double paidInAmountCounter, double winAmountCounter, double userAddedCounter);

	// methods
	json ToJSON() const;
	static CountersDto FromJSON(const json& val);
	CountersDto operator+(const CountersDto& other) const;
	CountersDto& operator+=(const CountersDto& other);
	bool operator==(const CountersDto& rhs) const = default;
};

void to_json(json& sourceJson, const CountersDto& dto);
void from_json(const json& sourceJson, CountersDto& dto);
