#pragma once

#include <unordered_map>

#include "Currencies.h"
#include "Json.h"
#include "JsonSchema.h"
#include "Timing.h"
#include "jackpot/JackpotSharedTypes.h"

/**
 * Keys used for parameters in jackpot level configuration and requests
 */
namespace jackpotlevelkey
{
constexpr const char* ID = "id";
constexpr const char* TYPE = "type";
constexpr const char* POT_TYPE = "potType";
constexpr const char* CURRENCY = "currency";
constexpr const char* CLIENT_GROUP_ID = "clientGroupId";
constexpr const char* INCREMENT = "increment";
constexpr const char* EXTERNAL_TRIGGER_TYPE_DATA = "externalTriggerTypeData";
constexpr const char* MIN_POT = "minPot";
constexpr const char* MAX_POT = "maxPot";
constexpr const char* AVG_PAYOUT = "avgPayout";
constexpr const char* TIME_FROM = "from";
constexpr const char* TIME_TO = "to";
constexpr const char* TIMEZONE = "timezone";
constexpr const char* WEEK_DAYS = "weekDays";
constexpr const char* MIN_BET = "minBet";
constexpr const char* MAX_BET = "maxBet";
constexpr const char* MIN_WINNABLE_BET = "minWinnableBet";
constexpr const char* MAX_WINNABLE_BET = "maxWinnableBet";
constexpr const char* NAME = "name";
constexpr const char* AUTO_ACTION_AFTER_WIN = "autoActionAfterWin";
constexpr const char* AUTO_ACTION_AFTER_WIN_OFFSET = "autoActionAfterWinOffsetSec";
constexpr const char* MIN_TIME_BETWEEN_WINS = "minTimeBetweenWins";
constexpr const char* USE_FIXED_PAID_IN = "useFixedPaidIn";
constexpr const char* KEEP_POT_ABOVE_MIN_POT = "keepPotAboveMinPot";
constexpr const char* TEMPLATE_ID = "templateId";
constexpr const char* LEVEL_REFERENCE = "levelReference";
constexpr const char* DEMO = "demo";
constexpr const char* CUSTOM_DATA = "customData";

constexpr const char* LEVEL_ID = "levelId";
}    // namespace jackpotlevelkey

struct ExternalTriggerTypeValues
{
	/**
	 * The amount of money paid in for this external trigger type (in level currency units)
	 */
	double PaidIn = 0.0;
	/**
	 * The win probability for this external trigger type
	 */
	double WinProbability = 0.0;

	bool operator==(const ExternalTriggerTypeValues& cmp) const = default;
};

void to_json(json& sourceJson, const ExternalTriggerTypeValues& dto);
void from_json(const json& sourceJson, ExternalTriggerTypeValues& dto);

class JackpotLevelInfoBase
{
   public:
	std::string ID;    // uuid
	EJackpotLevelType Type = EJackpotLevelType::Mystery;
	EJackpotPotType PotType = EJackpotPotType::Progressive;
	ECurrency Currency = ECurrency::NONE;
	std::string ClientGroupID;

	/**
	 * The increment portion (no units - this is a raw number, eg. 0.01 for 1%!)
	 */
	double Increment = 0.;
	/**
	 * The map of external trigger types with their 'paid in' amounts (absolute amount that is taken from bet for this level in level currency units)
	 */
	std::unordered_map<std::string, ExternalTriggerTypeValues> ExternalTriggerTypeData;

	double MinPot = 0.;
	double MaxPot = 0.;

	/**
	 * The average expected payout for this level (in level currency units). Mandatory for External levels.
	 */
	double AvgPayout = 0.;
	uint16_t TimeFrom = 0;
	uint16_t TimeTo = 0;
	std::string Timezone;
	// 1111111 = all week days, 1 = Monday, 10 = Tuesday, 100 = Wednesday, 1000 = Thursday, 10000 = Friday, 100000 = Saturday, 1000000 = Sunday
	Bitflag<EWeekday, uint8_t> WeekDays = Bitflag<EWeekday, uint8_t>::All();
	double MinBet = 0.;
	double MaxBet = 0.;
	double MinWinnableBet = 0.;
	double MaxWinnableBet = 0.;
	std::string Name;
	EActionAfterWin AutoActionAfterWin = EActionAfterWin::Nothing;
	uint32_t AutoActionAfterWinOffsetSec = 0;
	/**
	 * The minimum time between two wins in seconds.
	 */
	uint32_t MinTimeBetweenWinsSec = 0;
	/**
	 * If true, the level will use fixed paid in amount (from ExternalTriggerTypeData) instead of increment.
	 * Valid only for External levels.
	 */
	bool bUseFixedPaidIn = false;
	/**
	 * If true, the level will reset pot to at least MinPot value after win, and could have negative backPot as a result.
	 * Valid only for External levels.
	 */
	bool bKeepPotAboveMinPot = false;
	/**
	 * The template ID of a template from which this level was created. Optional.
	 */
	std::optional<std::string> TemplateID;
	/**
	 * The level reference string, defined by a client. Optional.
	 */
	std::optional<std::string> LevelReference;
	/**
	 * If true, the level is a demo level. Optional.
	 */
	bool bDemo = false;
	/**
	 * Custom data for the level. Optional.
	 */
	json CustomData;

	json ToJSON() const;
	static JackpotLevelInfoBase FromJSON(const json& val);
	static const JsonSchema& JackpotLevelInfoBaseSchema();
	bool operator==(const JackpotLevelInfoBase& cmp) const = default;
};

void to_json(json& sourceJson, const JackpotLevelInfoBase& dto);
void from_json(const json& sourceJson, JackpotLevelInfoBase& dto);
