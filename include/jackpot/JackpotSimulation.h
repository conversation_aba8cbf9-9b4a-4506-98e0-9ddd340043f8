#pragma once

#include "ThreadSafeProperty.h"
#include "jackpot/JackpotService.h"
#include "jackpot/JackpotUtils.h"
#include "jackpot/TJackpotLevel.h"

DECLARE_LOG_CATEGORY(LogJackpotSimulation, Debug)

class JackpotSimulation : public LoggingComponent, public std::enable_shared_from_this<JackpotSimulation>
{
	std::string ID;
	JackpotSimulationInfoDto mInfo;
	uint64_t mStartTimeMs = 0;
	uint64_t mEndTimeMs = 0;
	ESimulationStatus mStatus = ESimulationStatus::Running;
	std::string mStatusMsg;
	std::list<JackpotSimulationLevelDto> mResults;

	std::shared_ptr<JackpotConfigDto> mJackpotConfig;
	std::shared_ptr<JackpotDao> mJackpotDao;
	std::shared_ptr<JackpotServerClientManager> mJackpotServerClientManager;
	std::unique_ptr<JackpotService> mJackpotService;

	std::unordered_map<std::string, JackpotClientConfig> mClients;
	std::unordered_map<std::string, JackpotLevelInfoBase> mLevels;

	std::atomic<bool> mStopRequested { false };
	boost::future<void> mFuture;
	std::atomic<uint16_t> mProgress { 0 };

	void Run(std::optional<boost::chrono::milliseconds> timeout);

	std::optional<JackpotSimulationWinDto> GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
	                                                               const std::vector<BetProcessedDto>& betsData) const;
	std::optional<JackpotSimulationWinDto> GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
	                                                               const std::vector<BetDataDto>& betsData) const;
	std::optional<JackpotSimulationWinDto> GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
	                                                               const std::vector<JackpotSimulationWonBet>& wonBets) const;
	std::unordered_set<std::string> GetBetsWon(const JackpotLevelInfoBase& levelInfo, const SimulationClientInfoDto& clientInfo,
	                                           const std::vector<BetDataDto>& betsData) const;

   public:
	JackpotSimulation(JackpotSimulationInfoDto info);
	~JackpotSimulation();

	boost::future<JackpotSimulationDto> StartAsync(std::optional<boost::chrono::milliseconds> timeout = std::nullopt);

	void Stop();
	bool IsRunning() const;

	JackpotSimulationDto Data() const;
};

class JackpotDaoMock : public JackpotDao
{
	ThreadSafeProperty<std::unordered_map<std::string, JackpotLevelInfoBase>, std::shared_mutex> mJackpotLevels;

   public:
	// disk sync must be true! Otherwise we get memory buildup because we are not clearing mPendingBatches queue
	JackpotDaoMock() : JackpotDao(std::string(), true) {}

	void SaveJackpotLevelConfig(const JackpotLevelInfoBase& jackpotLevel) override
	{
		ScopedLock lock(mJackpotLevels);
		mJackpotLevels->emplace(jackpotLevel.ID, jackpotLevel);
	}
	std::vector<JackpotLevelInfoBase> ListJackpotLevelConfigs() override
	{
		std::vector<JackpotLevelInfoBase> jackpotLevels;
		ScopedLock lock(mJackpotLevels);
		for (const auto& level : &mJackpotLevels | std::views::values) jackpotLevels.emplace_back(level);
		return jackpotLevels;
	}
	std::optional<JackpotLevelInfoBase> GetJackpotLevelConfig(const std::string& id) override
	{
		SharedScopedLock lock(mJackpotLevels);
		auto it = mJackpotLevels->find(id);
		return it == mJackpotLevels->end() ? std::nullopt : std::make_optional(it->second);
	}
	void SaveCountersSinceTS(const std::string& jackpotLevelId, uint64_t countersSinceEpochMs) override {}
	std::unordered_map<std::string, std::unordered_set<std::string>> GetClient2ClientGroupsMap() const override
	{
		return std::unordered_map<std::string, std::unordered_set<std::string>>();
	}
	std::list<JackpotHistoryEntry> ListJackpotHistory(const std::string& jackpotLevelId, size_t maxEntries = 35, bool topByAmount = false) const override { return {}; }
	std::map<std::string, JackpotHistoryEntry> ListPendingWinHistoryMap(const std::string& jackpotLevelId) override { return {}; }
	std::map<std::string, std::pair<double, double>> GetPendingPotDiffs(const std::string& jackpotLevelId) const override { return {}; }
	std::unordered_map<std::string, EnabledClientDto> ListEnabledClients(const std::string& jackpotLevelId) override { return {}; }
	std::unordered_map<std::string, BetDto> ListPendingBets(const std::string& jackpotLevelId) override { return {}; }
	CountersDto GetCounters(const std::string& jackpotLevelId) const override { return {}; }
	BetAggregationDto GetCurrentBetAggregation(const std::string& jackpotLevelId, AggPeriod aggPeriod) const override { return {}; }
	std::optional<JackpotLevelInfoBase> GetPendingJackpotLevelConfig(const std::string& levelId) const override { return {}; }
	bool GetPendingDisable(const std::string& levelId) const override { return false; }
	bool GetPendingArchive(const std::string& levelId) const override { return false; }

   private:
	void WriteBatch(rocksdb::WriteBatch& batch) const override { batch.Clear(); }
	std::string GetValue(const std::string& key) const override { return {}; }
	void PutValue(const std::string& key, const std::string& value) override {}
	void DeleteValue(const std::string& key) override {}
};
