/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#ifndef __QUBO_RFID_DRV_H__
#define __QUBO_RFID_DRV_H__

#include <cstdint>
#include <string>
#include <unordered_set>
#include <vector>

#define RET_WRONG_INPUT_PARAMS -1
#define RET_DEVICE_BUSY        -2
#define RET_SUCCESS            0

/* 1Wire */
#define E_KEY_DATA_LENGTH 7

namespace quborfid_drv
{
typedef void* ThInst;

/*---------------------------------------------------------------------------------------------------
FUNCTION: PollEvent()

DESC:

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 no event; <>0 event value
---------------------------------------------------------------------------------------------------*/
int PopEvent(void* hInst, unsigned long* UserData1, unsigned long* UserData2);

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetKeyCode()

DESC:	The function fills the buffer with the currently insert key code. If there is no key the function
            returns -2;

PARAMS: hInst - instance handle
    KeyNumber - KeyNumber if key code matches one of key in configuration; if 0 the key code does not
            match
    pBuffer - pointer to buffer where the keycode will be written - must be at least E_KEY_DATA_LENGTH bytes long

RESULT: 0 success; 0< no key; 0> failure
---------------------------------------------------------------------------------------------------*/
int GetKeyCode(void* hInst, short* KeyNumber, std::vector<uint8_t>& pBuffer);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()

DESC:	Funkcija ustvari instanco quborfid_drv in vrne handle - konstruktor

PARAMS: pSerialFileName - path to device file
                OnKeyInsertUserCode - SDL message code for admin card in
                OnKeyEjectUserCode - SDL message code for admin card out
                OnCommunicationErrorUserCode - SDL message code for signalling communication error

RESULT: InstanceHandle or NULL if failure
---------------------------------------------------------------------------------------------------*/
ThInst Init(const char* pSerialFileName, int OnKeyInsertUserCode, int OnKeyEjectUserCode, int OnCommunicationErrorUserCode, int OnCommunicationOKUserCode,
            std::unordered_set<std::string>* UsedPorts);

/*---------------------------------------------------------------------------------------------------
FUNCTION: Destroy()

DESC:	Funkcija izprazni resurse instance - destruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja
        TMessage - kazalec na strkturo s podatki iz sporočila

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
void Destroy(ThInst hInst);


}    // namespace quborfid_drv
#endif    //__KEYBOARD_2_DRV_H__
