/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#ifndef __KEYBOARD_ELBET_DRV_LOCAL_H__
#define __KEYBOARD_ELBET_DRV_LOCAL_H__


#include <SDL2/SDL.h>
#include <SDL2/SDL_thread.h>
#include <fcntl.h>
#include <string.h>
#include <strings.h>
#include <sys/types.h>
#include <termios.h>
#include <unistd.h>

#include <cstdlib>
#include <queue>

#include "keyboard_drv.h"

/*****************************************************/
/* DEFINE-i STANJ IN KOMAND  - KOMUNIKACIJA Z NAPRAVO*/
/*****************************************************/


namespace key_elbet_drv
{
/* tip za state avtomat funkcije */
typedef int (*TpSAFunction)(void* pMyData);

typedef struct
{
	unsigned char pData[36];
	unsigned short DataLength;
} TMyMessage;

typedef struct TMyData
{
	/* kazalci na nasledje in prejšnje stanje */
	TpSAFunction NextState;
	TpSAFunction PreviusState;

	unsigned char LedBuffer[32];

	TMyMessage* pMessage;

	/* kazalec na strukturo ki je lastna vsaki fazi - po zaključku posamezne faze mora biti na NULL */
	void* pStateData;

	int hSerial;
	char pSerialFileName[100];

	SDL_Thread* pSAThread;    // kazalec na nitko, v kateri se vrti SA
	bool QuitThread;    // ali moramo zaključiti z nitko
	SDL_mutex* pQuitThreadMutex;    // uporaba za izhod iz thread-a

	/* sporočilo, ki naj se pošlje. če je null, potem pošiljamo samo status request ničesar */
	std::queue<TMyMessage*> CommandQueue;
	SDL_mutex* pCommandQueueMutex;    // uporaba v funkcijah v CylinderIF_cmd.cpp - sicer nisem prepričan ali je sploh potrebno paziti na std::queue

	int SDLEventCode_OnKeyDown;
	int SDLEventCode_OnKeyUp;
	int SDLEventCode_OnCommunicationError;
} TMyData;



/* napovedi lokalnih funkcij */
int SetPreviousState(TMyData* pMyData);
void SetNextState(TMyData* pMyData, TpSAFunction pNextStateFunction);

int stateSendCommand(TMyData* pMyData);
int stateStatusRequest(TMyData* pMyData);


int MainInThread(ThInst hInst);
int SendMessage(TMyData* pMyData, TMyMessage* pMessage);
int ComDriverInit(TMyData* pMyData);
void ComDriverDestroy(TMyData* pMyData);
}    // namespace key_elbet_drv

#endif    //__KEYBOARD_DRV_LOCAL_H__
