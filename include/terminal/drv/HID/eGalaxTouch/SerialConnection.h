#ifndef _GALAX_TOUCH_SERIAL_CONNECTION_
#define _GALAX_TOUCH_SERIAL_CONNECTION_


#include <SDL2/SDL.h>
#include <strings.h>
#include <termios.h>
#include <unistd.h>

#define MAX_SENDING_TIME 1000
#define MAX_READING_TIME 1000

/*---------------------------------------------------------------------------------------------------
STRUKTURA: TgalaxMessage

DESC: 	definicija strukture, za izmenjavo vsebine sporočila

---------------------------------------------------------------------------------------------------*/

typedef struct TgalaxMessage
{
	unsigned char Command;
	unsigned char DataLength;
	unsigned char pData[250];
} TgalaxMessage;


/*---------------------------------------------------------------------------------------------------
FUNCTION: etscFlushIO()

DESC:	Funkcija izprazni vhodno izhodne bufferje

PARAMS: hInst - handle na instanco

RESULT: 0- success; <0 - failure
---------------------------------------------------------------------------------------------------*/
int gtscFlushIO(void* hInst);

/*---------------------------------------------------------------------------------------------------
FUNCTION: SendReceiveMessage()

DESC:	Funkcija pokliče SendMessage, počaka in pokliče ReciveMsg

PARAMS: handle,
                TMessage
                Retries - how many times should the function try to get the valid answer
                SendReciveDelay - delay betwen send and recive(ms)

RESULT: 0 - success; <0 - failure
---------------------------------------------------------------------------------------------------*/
int gtscSendReceiveMessage(void* hInst, TgalaxMessage* pMessage, int Retries, unsigned int SendReciveDelay);


/*---------------------------------------------------------------------------------------------------
FUNCTION: ctlksSendMessage()

DESC:	Funkcija zgradi sporocilo za prenos proti napravi in ga pošlje na serijski port

PARAMS: hInst - handle na instanco komunikacijskega driverja
                pMessage - kazalec na strukturo s podatki za sporočilo

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int gtscSendMessage(void* hInst, TgalaxMessage* pMessage);


/*---------------------------------------------------------------------------------------------------
FUNCTION: ctlksReceiveMessage()

DESC:	Funkcija prebere sporocilo iz naprave, izlušči vsebino in vrne vsebino sporocila
            Je tipa locking; v primeru napake pri prenosu zahteva ponovitev sporočila

PARAMS: hInst - handle na instanco komunikacijskega driverja
                TgalaxMessage - kazalec na strkturo s podatki iz sporočila; !!!NASTAVITI JE POTREBNO DATA_LENGTH
                na vrednost - dolžina podatkovnega dela v paketu ki ga pričakujemo!!!
                !!! na pData mora biti pripravljenega toliko prostora kolikor je DataLength !!!!!

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int gtscReciveMsg(void* hInst, TgalaxMessage* pMessage);

/*---------------------------------------------------------------------------------------------------
FUNCTION: etscReciveReportPacket()

DESC:	Funkcija prebere sporocilo iz Report Packet iz naprave

PARAMS:  hInst - handle na instanco komunikacijskega driverja
         TgalaxMessage - kazalec na strkturo TgalaxMessage

RESULT: on success BYTES_READ; res < 0 failure;
---------------------------------------------------------------------------------------------------*/
int gtscReciveReportPacket(void* hInst, TgalaxMessage* pMessage);


/*---------------------------------------------------------------------------------------------------
FUNCTION: ctlksInit()

DESC:	Funkcija ustvari instanco ctlks in vrne handle - konstruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja
                Device - pot do datoteke porta(/dev/ttyS0,...)

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int gtscInit(void** hInst, const char* pSerialDeviceFileName, struct termios TermiosConfig);

/*---------------------------------------------------------------------------------------------------
FUNCTION: ctlksDestroy()

DESC:	Funkcija izprazni resurse instance ctlks - destruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja
                TgalaxMessage - kazalec na strkturo s podatki iz sporočila

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
void gtscDestroy(void* hInst);


#endif
