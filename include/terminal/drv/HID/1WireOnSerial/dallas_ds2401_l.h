#ifndef __DALLAS_DS2401_DRIVER_LOCAL__
#define __DALLAS_DS2401_DRIVER_LOCAL__


#include <SDL2/SDL.h>
#include <SDL2/SDL_thread.h>
#include <fcntl.h>
#include <string.h>
#include <strings.h>
#include <sys/types.h>
#include <termios.h>
#include <unistd.h>

#include <cstdlib>

#include "1wire_on_serial.h"


namespace ds2401drv
{
typedef struct TMyData
{
	void* h1WireOnSerial;

	unsigned char Buffer[8];

	char SerialNumber[7];
	SDL_mutex* pSerialNumberMutex;

	short KeyNumber;    // if -1 key is not present
	SDL_mutex* pKeyNumberMutex;

	int OnKeyInsertUserCode;
	int OnKeyEjectUserCode;

	SDL_Thread* hSAThread;
	bool QuitThread;
	SDL_mutex* pQuitThreadMutex;

} TMyData;


}    // namespace ds2401drv
#endif /*__DALLAS_DS2401_DRIVER_LOCAL__*/
