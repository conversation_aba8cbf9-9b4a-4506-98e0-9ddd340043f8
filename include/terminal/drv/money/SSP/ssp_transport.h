/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON><PERSON>   <PERSON>
 *   <PERSON><PERSON><PERSON>@localhost.localdomain   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
#pragma once

namespace ssp_transport
{


typedef void* ThSspTrans;



/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()
DESC:	Funkcija ustvari instanco EasySerial in vrne handle - konstruktor
PARAMS: Device - pot do datoteke porta(/dev/ttyS0,...)
RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
ThSspTrans Init(char* Device, unsigned char SlaveId);


/*-------------------------------------------------------------------------------------------------
FUNCTION: DestroyMessage()
DESC:	Funkcija sprosti vse resurse v povezavi s sporočilom
PARAMS: hInst -- Handle na instanco komunikacijskega driverja
RESULT: void
---------------------------------------------------------------------------------------------------*/
void Destroy(ThSspTrans hInst);


/*---------------------------------------------------------------------------------------------------
FUNCTION: SendReceiveMessage()
DESC:	Funkcija pokliÄe SendMessage, poÄaka in pokliÄe ReciveMsg
PARAMS: handle,
                TMessage
                Retries - how many times should the function try to get the valid answer
                SendReciveDelay - delay betwen send and recive(ms)
RESULT: 0 - success; <0 - failure
---------------------------------------------------------------------------------------------------*/
int SendReceiveMessage(ThSspTrans hInst, unsigned char* pDataToSend, unsigned short SendLength, unsigned char* pDataRecived, unsigned short* RecivedLength);

/*------------------erial port programming linux----------------------------------------------------------------
Funkcija: SendMessage()
DESC: Funkcija zgradi sporočilo za prenos proti napravi in ga pošlje na serijski port
RESULT: hInst- To je nas komunikacijski handle
        pData- Pointer na podatke, ki jih želimo poslati
        DataLength- Dolzina podatkih ,ki jih pošiljamo
------------------------------------------------------------------------------------*/
int SendMessage(ThSspTrans hInst, unsigned char* pData, unsigned short DataLength);


/*----------------------------------------------------------------------------------
Funkcija: ReciveMsg()
DESC: Funkcija prebere sporocilo iz naprave, izlušči vsebino in vrne vsebino sporocila
RESULT: hInst- To je nas komunikacijski handle
        pDest- Pointer na buffer kamor bomo zapisali prejete podatke
        DataLength- Ob klicu gre za dolžino buferja(pDest), po klicu pa dolžino prejetih
                    podatkov
------------------------------------------------------------------------------------*/

int ReciveMsg(ThSspTrans hInst, unsigned char* pDest, unsigned short* DataLength);



/*----------------------------------------------------------------------------------
Funkcija: EncryptionMsg()
DESC: Funkcija zakriptira vrednosti
RESULT: hInst- to je enkripcijski handle
        pData- pointer na podatke, ki jih želimo zakriptirati
        DataLength- dolžina podatkov, ki jih želimo zakriptirati
------------------------------------------------------------------------------------*/
// int EncryptionMsg(ThSspTrans2 hInst, unsigned char *pData, unsigned short DataLength);

/*
FUNCTION: Init()
      DESC:	Funkcija ustvari instanco ThSspTrans2 in vrne handle - konstruktor
      PARAMS: Device - pot do datoteke porta(/dev/ttyS0,...)
      RESULT: 0 success; 0<> failure
*/

// ThSspTrans2 Init();

/*-------------------------------------------------------------------------------------------------
FUNCTION: DestroyMessage()
DESC:	Funkcija sprosti vse resurse v povezavi s enkripcijo
PARAMS: hInst --
RESULT: void
---------------------------------------------------------------------------------------------------*/
// void Destroy(ThSspTrans2 hInst);


}    // namespace ssp_transport
