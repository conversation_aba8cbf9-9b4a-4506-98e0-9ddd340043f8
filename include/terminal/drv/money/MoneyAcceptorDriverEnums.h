#pragma once
#include <cstdint>

#include "Enums.h"

BETTER_ENUM(ETicketPrintingStatus, uint8_t, NOT_PRINTING, PRINTING_TICKET, PRINT_SUCCESSFUL, PRINT_ERROR);
BETTER_ENUM(ETicketGraphicsUploadStatus, int, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_UPLOADING, <PERSON><PERSON><PERSON><PERSON><PERSON>S_UPLOAD_SUCCESSFUL, G<PERSON>PHICS_UPLOAD_TIMED_OUT, GRAPHICS_UPLOAD_NOT_POSSIBLE,
            GRAPHICS_DELETE_ERROR, GRAPHICS_WRITE_ERROR, GRAPHICS_UPLOAD_ERROR)

BETTER_ENUM(ETicketPrinterStatus, uint8_t, COMMUNICATION_ERROR, PRINTING, TICKET_LOW, UPLOADING_GRAPHICS, HEAD_UP, CHASIS_OPEN, PAPER_NOT_LOADED, PAPER_JAM, SY<PERSON>EM_ERROR,
            BUSY, TAKE_TICKET, INTERNAL_ERROR);

BETTER_ENUM(ECoinAcceptorStatus, uint8_t, COMMUNICATION_ERROR, HOPER_EMPTY);

BETTER_ENUM(EBillAcceptorStatus, uint8_t, COMMUNICATION_ERROR, STACKER_OPEN, STACKER_FULL, ACCEPTOR_JAM, STACKER_JAM, INHIBIT, ACCEPTING_STATE, ESCROWED_STATE,
            STACKING_STATE, RETURNING_STATE, OUT_OF_SERVICE, CALIBRATING, INTERNAL_ERROR);
#