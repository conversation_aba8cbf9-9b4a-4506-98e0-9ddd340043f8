#pragma once

#include <SDL2/SDL.h>
#include <strings.h>
#include <unistd.h>

#include <cstdlib>
#include <queue>
#include <string>

#include "Vector.h"
#include "drv/SerialCommunication.h"
#include "drv/money/MoneyAcceptorDriverEnums.h"

// status reading 1st byte
#define STATUS_PRINTER_BUSY         1 << 5
#define STATUS_PRINTER_SYS_ERROR    1 << 4
#define STATUS_PRINTER_HEAD_UP      1 << 3
#define STATUS_PRINTER_PAPER_LOADED 1 << 2
#define STATUS_PRINTER_HEAD_ERROR   1 << 1    // unused
#define STATUS_PRINTER_VOLT         1 << 0    // unused

// status reading 2nd byte
#define STATUS_PRINTER_TEMP             1 << 5    // unused
#define STATUS_PRINTER_LIBREF           1 << 4
#define STATUS_PRINTER_REGION_TRUNCATED 1 << 3
#define STATUS_PRINTER_LIBLOAD          1 << 2
#define STATUS_PRINTER_BUFFER_OVERFLOW  1 << 1
#define STATUS_PRINTER_BUFFER_JOB_MEM   1 << 0

// status reading 3rd byte
#define STATUS_PRINTER_UNSUPPORTED_COMMAND 1 << 5
#define STATUS_PRINTER_LIBSAVE             1 << 4    // unused
#define STATUS_PRINTER_PAPER_PRESENT       1 << 3
#define STATUS_PRINTER_FLASH               1 << 2    // unused
#define STATUS_PRINTER_OL_STAT             1 << 1    // unused
#define STATUS_PRINTER_PS                  1 << 0    // unused

// status reading 4th byte
//  5,4 byte RESERVED
#define STATUS_PRINTER_JOURNAL_MODE 1 << 3    // unused
#define STATUS_PRINTER_CUTTER_ERROR 1 << 2    // unused
#define STATUS_PRINTER_PAPER_JAM    1 << 1
#define STATUS_PRINTER_PAPER_LOW    1 << 0

// status reading 5th byte
#define STATUS_PRINTER_SF5_BARCODE      1 << 5
#define STATUS_PRINTER_TOP_FORM         1 << 4
#define STATUS_PRINTER_SF5_XOFF         1 << 3    // unused
#define STATUS_PRINTER_SF5_CHASIS_OPEN  1 << 2
#define STATUS_PRINTER_SF5_BARCODE_DONE 1 << 1
#define STATUS_PRINTER_SF5_POWER_RESET  1 << 0


namespace gen2drv
{
/* tip za state avtomat funkcije */
typedef int (*TpSAFunction)(void* pMyData);

BETTER_ENUM(ETicketPrinterInternalError, int8_t, INTERNAL_ERROR_POWER_RESET, INTERNAL_ERROR_UNSUPPORTED_COMMAND, INTERNAL_ERROR_OUT_OF_MEMORY,
            INTERNAL_ERROR_INTERNAL_BUFFER_OVERFLOW, INTERNAL_ERROR_OBJECT_NOT_DEFINED_IN_PRINTER_LIB, INTERNAL_ERROR_PRINTER_REGION_TRUNCATED,
            INTERNAL_ERROR_LIBRARY_LOAD_ERROR);


struct TMyData : public Lockable<>
{
	/* kazalci na nasledje in prejšnje stanje */
	TpSAFunction NextState;
	TpSAFunction PreviusState;

	/* kazalec na strukturo ki je lastna vsaki fazi - po zaključku posamezne faze mora biti na NULL */
	void* pStateData;

	std::string SerialPort;
	std::unique_ptr<ImaxaSerial> Serial;
	int currentBaudrate;

	std::vector<uint8_t> MessageData;

	/* komanda, ki naj se izvrši. če je null, potem ne izvajamo ničesar */
	TpSAFunction Command;
	std::array<std::string, 16> TicketData;    // 16 vrednosti za tiskanje ticket-a, uporabi ga statePrintTicket
	//   unsigned char TicketLayout[MAX_RENDERED_TICKET_LENGTH]; //skripta ki nareka obliko ticket-a, sparsa se in zamenjajo se vrednosti 0x25 0x25 0x01 z vrednostjo
	//   TicketData[0], 0x25 0x25 0x02 z vrednostjo TicketData[1],...
	std::string TicketLayout;
	char ControllerVersion[2];    // version of the controller - !!!use command mutex to read it!!!
	std::string PrinterStatus;
	std::string SoftwareVersion;

	std::recursive_mutex PrinterCommandMutex;

	Uint64 IsPrinterBusyTime = 0;
	// internal
	bool BarCode;
	bool BarCodeDataAccessed;
	bool TopOfForm;

	std::string LastSuccessfulyPrintedTicketCode;
	Bitflag<ETicketPrinterStatus> TicketPrinterStatusFlags;
	bool InitPowerResetEventOccured;
	bool InitPowerResetHandled;

	Bitflag<ETicketPrinterInternalError> InternalStatusFlags;

	int SDL_EventCode_OnResettingDevice;
	int SDL_EventCode_OnTicketStatusChange;
	int GraphicsObjectToUpload = 0;
	std::vector<uint8_t> BytesToUpload;
	iVector2D GraphicsSizeToUpload;

	std::map<int, iVector2D> GraphicsSizes;
	std::vector<std::string> PrePrintCommands;

	SDL_Thread* pSAThread;    // kazalec na nitko, v kateri se vrti SA
	bool QuitThread;    // ali moramo zaključiti z nitko
	SDL_mutex* pQuitThreadMutex;    // uporaba za izhod iz thread-a
};


/* napovedi lokalnih funkcij */
void SetNextState(TMyData* pMyData, TpSAFunction pNextStateFunction);

int stateCommunicationError(TMyData* pMyData);
int statePrinterError(TMyData* pMyData);
int statePrintTicket(TMyData* pMyData);
int stateResetDevice(TMyData* pMyData);
int stateOperationMode(TMyData* pMyData);
int stateUploadGraphics(TMyData* pMyData);

int _updatePrinterStatus(TMyData* pMyData);
int _clearPrinterErrorStatus(TMyData* pMyData);

int MainInThread(void* hInst);


}    // namespace gen2drv
