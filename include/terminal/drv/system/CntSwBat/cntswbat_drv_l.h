/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/

#ifndef __CNTSWBAT_DRIVER_LOCAL_H__
#define __CNTSWBAT_DRIVER_LOCAL_H__

#include <SDL2/SDL.h>
#include <SDL2/SDL_thread.h>
#include <string.h>
#include <unistd.h>

#include <cstdlib>
#include <queue>

#include "cntsw_serial.h"
#include "cntswbat_drv.h"


#define TIME_OUT                3000
#define MAX_SEND_RECIVE_RETRIES 5
#define SEND_RECIVE_DELAY_MS    30
#define RECIVE_SEND_DELAY_MS    50


/*****************************************************/
/* DEFINE-i STANJ IN KOMAND  - KOMUNIKACIJA Z NAPRAVO*/
/*****************************************************/

#define COUNTERS_COUNT 8
#define SW_COUNT       8
namespace cntswbatdrv
{
typedef struct
{
	unsigned char Cmd;
	int RespDataLength;
	int RespDataLengthIfError;
	char CommandName[20];
} TCommand;

const TCommand* GetCmdByName(const char* Name);



/* tip za state avtomat funkcije */
typedef int (*TpSAFunction)(void* pMyData);

struct TDeviceStatus
{
	SDL_mutex* pStructMutex;

	bool SwStatus[SW_COUNT];    // 1 - OPEN, 0-CLOSE

	bool CommunicationOK;

	time_t TimeOnDevice;
	time_t TimeOnDeviceFetchedTimestamp;

	char BATPresence[20];
	bool BATCharging;
	bool BATFault;
	bool BATVoltage;

	char PCBName[4];
	time_t FWDate;
	time_t IFDate;

	bool CountersSenseEnabled;
	int CountersPulseWidthMS;

	bool CountersEnabled;
	uint8_t CountersCommonWiresOk;
	uint8_t CounterWireErrors;
};


struct TMyData
{
	/* kazalci na nasledje in prejšnje stanje */
	TpSAFunction NextState;
	TpSAFunction PreviusState;

	/* kazalec na strukturo ki je lastna vsaki fazi - po zaključku posamezne faze mora biti na NULL */
	void* pStateData;

	char pSerialFileName[100];
	cntswserial::TCntSwSerial hSerial;
	cntswserial::TMessage* pMessage;
	unsigned char seq_id;

	TDeviceStatus* pDeviceStatus;

	/* sporočilo, ki naj se pošlje. če je null, potem pošiljamo samo status request ničesar */
	std::queue<cntswserial::TMessage*> CommandQueue;
	SDL_mutex* pCommandQueueMutex;    // uporaba v funkcijah v CylinderIF_cmd.cpp - sicer nisem prepričan ali je sploh potrebno paziti na std::queue

	SDL_Thread* pSAThread;    // kazalec na nitko, v kateri se vrti SA
	bool QuitThread;    // ali moramo zaključiti z nitko
	SDL_mutex* pQuitThreadMutex;    // uporaba za izhod iz thread-a

	int SDLEventCode_OnDoorEvent;
	int SDLEventCode_OnCountersBusy;
	int SDLEventCode_OnCountersDone;
	int OnEKeyInsertUserCode;
	int OnEKeyEjectUserCode;
	int SDLEventCode_OnCommunicationError;
	int SDLEventCode_OnCommunicationOK;
	int SDLEventCode_OnCountersError;
	int SDLEventCode_OnCountersOK;
};


/* napovedi lokalnih funkcij */
int SetPreviousState(TMyData* pMyData);
void SetNextState(TMyData* pMyData, TpSAFunction pNextStateFunction);

int stateCommunicationError(TMyData* pMyData);
int stateStatusRequest(TMyData* pMyData);
int stateDeviceError(TMyData* pMyData);


int MainInThread(void* hInst);

unsigned char GetNext_seq_id(TMyData* pMyData);

}    // namespace cntswbatdrv


#endif
