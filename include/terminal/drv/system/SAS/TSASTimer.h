#pragma once

#include "ThreadSafeProperty.h"

class TSASTimer : public Lockable<>
{
   public:
	bool bTimerSet;
	uint64_t nStartTime;
	uint64_t nTimerTime;
	uint64_t nRememberedTime;

	TSASTimer();

	TSASTimer& operator=(const TSASTimer& rhs);

	// On Linux SDL_GetTicks64(), does not work correctlly if time is set back or forward
	// if nPrevTicks > SDL_GetTicks64() set it to SDL_GetTicks64()
	void AdjustTime(uint64_t& nPrevTicks);
	/*  void AdjustTime(unsigned int &nPrevTicks)
	  {
	    Uint32 lTicks = 0;
	    Uint32 lPrevTicks = 0;
	#define BACKFLOW_CONSTANT 0xFAD9A3FF
	    lTicks = SDL_GetTicks64();
	    lPrevTicks = (long long)nPrevTicks;
	    // if ticks rolover 0xFFFFFFFF (due to system time change) or current time lower then previous set to current time
	//    if ((std::abs(lTicks - lPrevTicks) > (0xFFFFFFFF/2)) || (lTicks < lPrevTicks))
	    if ( (lPrevTicks<(0xFFFFFFFF-BACKFLOW_CONSTANT) && lPrevTicks+BACKFLOW_CONSTANT<lTicks) || (lTicks < lPrevTicks) )
	    {
	      nPrevTicks = lTicks;
	    }
	  };*/
	uint64_t Elapsed();
	bool IsSet();
	bool IsTimeout();
	void RememberTime();
	void Reset();
	void Start(uint64_t nTimer);
	void Restart();
	void Stop();
};

void SASTimerUnitTest();
