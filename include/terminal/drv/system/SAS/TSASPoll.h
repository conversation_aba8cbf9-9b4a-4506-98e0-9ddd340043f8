#pragma once

#include "TSASExceptions.h"
#include "cstdio"

// poll max length
#define SAS_MAX_PCT_LEN 256

// timing requierments in ms
#define SAS_LOOP_BREAK_NOTIFY_PERIOD 200
#define SAS_VERSION_ID               "602"

// Packets types
#define SAS_PCT_NACK 1
#define SAS_PCT_ACK  2

// extended meter size in bytes (6 BCD is 12 digits)
#define SAS_EXTENDED_METER_SIZE 6

enum
{
	FOR_ME = 0,
	FOR_ANY_CLIENT,
	FOR_OTHER_CLIENT,
	FOR_ADDRESS_0
};

struct LongPoolStruct
{
	char cType;
	int nPctLen;    // if -1, then the 3rd byte of packet
	char sDescription[128];
};

struct MeterNamesStruct
{
	unsigned char cCode;
	char sMeterName[128];
	char cLen;
};
struct MeterIDsStruct
{
	unsigned char cCode;
	unsigned short* pnMeterID;
	char cLen;
};
int sas_PopulateLongPollsDesc();

class TSASPoll
{
   public:
	TSASPoll() { Init(); };

	// comparison, copy
	int operator==(TSASPoll& pPct);
	// void          operator = ( TSASPoll& pPct );

	unsigned char m_Buf[SAS_MAX_PCT_LEN];
	int m_Len;
	TSASTimer m_tLastByteReceive;
	int m_nParityPos;

   public:
	int AppendChar(unsigned char c, int nCount = 1);
	int AppendShort(unsigned short s);
	int AppendStrAsBin(const TString& sStr, size_t nLen);
	int AppendCounter(unsigned short nMeterID, int nBCDLen, int gameN = 0);
	int AppendBCD(uint64_t nValue, int nBCDLen);
	int AppendBCD(const TString& nValue, int nBCDLen);
	int AppendStr(const std::string& str, size_t nLen);
	int AppendBuf(const unsigned char* pBuf, int nLen);
	int AppendInt(unsigned int n, int nLen);
	void Build(int nType, class TSASChannel* pSas);
	int BuildGeneralPollResponse(unsigned char bException, void* pSas);
	int BuildRealTimeEventResponse(const ExceptionEntry& entry, void* pSas);
	int BuildRLongPollResponse(TSASPoll* pInputPct, void* pSas);
	int BuildMLongPollResponse(TSASPoll* pInputPct, void* pSas);
	int BuildSLongPollResponse(TSASPoll* pInputPct, void* pSas, bool* bRealTimeEventReport = NULL);
	static unsigned short CcittCrc(unsigned char* s, int len, unsigned short crcval);
	void Clear();
	int GetByte(int n);
	int GetBuf(char* pBuf, int nStart, int nCount);
	int SetByte(int n, unsigned char cVal);
	int GetBCDAsInt(int nStart, int nLen);
	uint64_t GetBCDAsUInt64(int nStart, int nLen);
	int GetBCDAsStr(int nStart, int nLen, TString& sResult);
	int GetBinAsStr(int nStart, int nLen, TString& sResult);
	int GetInt(int nStart, int nLen, unsigned int* nResult);
	int GetStr(int nStart, int nLen, TString& sResult);
	char GetType(void* pSas);
	void Init();
	int IsGlobalBroadcast();
	int IsSync80();
	int IsComplete(void* pSas);
	int IsGeneralPoll(int nTarget, void* pSas);
	int IsLongPoll(int nTarget, void* pSas);
	int IsLenOK();
	int IsCrcOK();
	void LogPoll();
	void ToBCD(uint64_t nValue, int BCDLen, std::vector<uint8_t>& outBuf);
	char* GetAsStr();

   private:
	TSASModule* pSASModule;

	struct ActiveGame
	{
		unsigned int GameNumber;
		unsigned int GameCycleSequence;
		unsigned int GameStartSequence;
	};

	std::vector<ActiveGame> mActiveGames;
};
