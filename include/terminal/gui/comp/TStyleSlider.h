#pragma once

#include "TActionElement.h"
#include "TDBParameterControl.h"
#include "TStyleButton2.h"
#include "gui/layout.hpp"
#include "gui/widgets/container.hpp"

struct TSliderValueDescriptor
{
	LocalizedMessage ValueCaption;
	std::string StrValue;
	int64_t IntValue = -1;
};

struct TSliderArrowImages
{
	ImagePtr Normal;
	ImagePtr Pressed;
	ImagePtr Disabled;
	ImagePtr High;
};

enum class ESliderInputType
{
	SetByCode,
	Interactive,
	Direct
};

/**
    <AUTHOR> Presl <<EMAIL>>
 */
class TStyleSlider : public LayoutContainer, public MouseListener, public TActionElement, public TDBParamControl, public KeyListener
{
   protected:
	ImagePtr WarnImage;

   public:
	TStyleSlider(Container* pParent, const std::string& name, float x, float y, float w, float h, size_t numValues, const std::shared_ptr<TAppParam>& param = {},
	             bool is_string = false);
	TStyleSlider(Container* pParent, float x, float y, float w, float h, const std::shared_ptr<TAppParam>& pParam, bool IsString = false);

	ESliderType Type = ESliderType::SLIDER_STRING;

	std::function<void(ESliderInputType, int)> mpOnChangeCallback;

	virtual void drawLogic(Graphics* graphics, float deltaTime) override;

	virtual void mousePressed(MouseEvent& mouseEvent) override;
	virtual void mouseDragged(MouseEvent& mouseEvent) override;
	virtual void mouseReleased(MouseEvent& mouseEvent) override;

	void disableSound();

	void setSliderBackground(const ImagePtr& background);
	void setSliderButtonImages(const ImagePtr& normal, const ImagePtr& pressed, const ImagePtr& disabled, const ImagePtr& high = {});
	void setSliderArrowsImages(const std::array<TSliderArrowImages, 2>& images);

	void generateActionOnSlide(bool generate) { bGenerateActionOnSlide = generate; }
	void showValuesOnSlide(bool show);
	void showArrows(bool show);
	void HideControls();

	void SetFlipRightArrowImage(const bool flip);
	void SetSliderButtonBackgroundColors(const Color& pressed, const Color& disabled, const Color& highlighted);
	void SetWriteValueOutsideSlider(const bool writeOutside);
	void SetShowProgressBar(bool progressBarVisible);

	void draw(Graphics* graphics) override;

	size_t numValues() const;
	void AddValue(const LocalizedMessage& Caption, const std::string& SValue, int64_t IValue);
	void ClearValues();
	int SetValue(const ESliderInputType inputType, const std::string& SValue, int64_t IValue, ParameterChangeSource source = ParameterChangeSource::User);
	void setValue(const ESliderInputType inputType, int index, bool updatex = true, bool triggerAction = true,
	              ParameterChangeSource source = ParameterChangeSource::User);
	bool NextValue();
	bool PrevValue();

	int FindValue(const std::string& SValue) const;
	int FindValue(int64_t IValue) const;

	virtual bool isFocused() const override { return bPressed; }

	void setCaption(const LocalizedMessage& Caption);

	int getIndex() const { return mCurrentIndex; }
	TSliderValueDescriptor GetSelected() const;

	void OnParamChanged(TAppParam* param, const ParamPropertyBitflag& changeFlag) override;

	void setGeneratesActionOnPressed(bool generateAction);

	void setGeneratesActionOnReleased(bool generateAction);

	void setGeneratesActionOnButtons(bool generateAction);

	void setLoopAround(bool loopAround);

	bool generatesActionOnPressed() const;

	bool generatesActionOnButtons() const;

	bool generatesActionOnReleased() const;

	bool pressed() const;

	void SetTrackProgressBarColor(const Color& color);

	TStyleButton2* sliderButton() const;

	virtual bool IsLocked() const override { return (RestrictedState() == ELockedState::LOCKED || RestrictedState() == ELockedState::HIDDEN); };

	void setLabel(const LocalizedMessage& caption, const Color& labelColor = White, float FixedWidth = 0.f);

	Label* label() const;

	Label* valueLabel() const;

	std::array<TStyleButton2*, 2>& GetSliderArrows();

	PROPERTY(Brush, TrackBrush);

   private:
	int getIndexFromPx(float px_pos);
	float getPxFromIndex(int index);

	virtual bool DoAction_Implementation(const HardwareButtonEvent& ev) override;
	virtual void GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const override;

	Label* plValue = NULL;
	Container* pSliderContentContainer = NULL;
	Widget* pSliderProgressBarBG = NULL;
	Widget* pSliderProgressBar = NULL;
	TStyleButton2* pSliderButton = NULL;
	std::array<TStyleButton2*, 2> pSliderArrows;

	Color SliderButtonDisabledBackground;
	Color SliderButtonPressedBackground;
	Color SliderButtonHighlightedBackground;

	bool bPressed = false;
	bool bLoopAround = true;

	bool bGenerateActionOnSlide = false;

	bool bShowValuesOnSlide = true;
	bool bShowArrows = false;
	bool bParamChanged = false;
	bool bActionOnPress = false;
	bool bActionOnRelease = true;
	bool bActionOnButtons = false;

	bool bFlipArrows = true;
	bool bWriteValueOutsideSlider = false;
	bool bProgressBarVisible = false;

	int mCurrentIndex = 0;
	float mSlidingX = -1;

	StackLayout* pLayout;

	Label* pLabel = NULL;

	std::vector<TSliderValueDescriptor> Values;

	std::string mSoundEffectName;
};
