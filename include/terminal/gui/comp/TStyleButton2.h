#pragma once

#include "TActionElement.h"
#include "TDBParameterControl.h"
#include "TLangMessages.h"
#include "gui/widgets/button.hpp"

class TStyleButton2 : public Button, public TActionElement
{
   public:
	TStyleButton2(Container* pParent, const LocalizedMessage& Caption = {}, const ColorProperty& TextColor = White, const std::string& ClickSound = std::string());
	TStyleButton2(Container* pParent, const LocalizedMessage& Caption, float x, float y, float width, float height, const std::string& XMLFilename,
	              const std::string& ClickSound = std::string());

	virtual bool shouldDrawBackground(const Brush& background, const Outline& outline) const override;
	virtual void mousePressed(MouseEvent& mouseEvent) override;
	virtual void mouseReleased(MouseEvent& mouseEvent) override;
	virtual bool isEnabled() const override;

	void setHighImage(ImagePtr image);
	void setDownImage(ImagePtr image);
	void setNormalImage(ImagePtr image);
	void setDisabledImage(ImagePtr image);
	void setAllStatesToImage(ImagePtr image);

	void setIcon(ImagePtr icon);

	void setRestriction(int restriction);

	virtual void drawBackground(Graphics* graphics, const Outline& outline) override;
	virtual void drawLogic(Graphics* graphics, float deltaTime) override;
	virtual void draw(Graphics* graphics) override;

	virtual const BrushProperty& getBackground() const override;

	std::string ClickSound;

	bool DrawHighInseadOfNormal = false;
	Uint32 AutoDrawHightOnLongPressDelay = 0;    // if 0, this feature is disabled

	Uint32 AutoRepeatFirstInterval = 0;    // prvi delay za zacetek ponavljanja - if 0 AutoRepeat function is disabled
	Uint32 AutoRepeatInterval = 0;    // hitrost ponavaljanja, ko je firstIntervalDosezen - if 0 AutoRepeat function is disabled

	ImagePtr getNormalImage() const { return Normal; }
	ImagePtr getHighImage() const { return High; }
	ImagePtr getDownImage() const { return Down; }
	ImagePtr getDisabledImage() const { return Disabled; }

	std::optional<Color> OverrideTextDisabledColor;
	std::function<bool()> EnabledCheck;

   protected:
	virtual bool DoAction_Implementation(const HardwareButtonEvent& ev) override;
	virtual void GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const override;

	virtual bool isActive() const { return false; }

	virtual bool IsLocked() const override { return false; }

	virtual void generateAction() override;

	Uint64 FirstPressTimestamp = 0;
	Uint64 tAutoRepeatAnchor = 0;    // interval za samodejno ponavljanje tipke, ce drzimo tipko -
	Uint64 LastPressTimestamp = 0;
	// int text_w; /*lokalne v TStyleButton2::draw, ampak sem jih dal sem zaradi performanse*/
	// int text_x; /*lokalne v TStyleButton2::draw, ampak sem jih dal sem zaradi performanse*/
	// int text_y; /*lokalne v TStyleButton2::draw, ampak sem jih dal sem zaradi performanse*/


	bool bPressedFlag = false;    // da se vedno vsaj 1x nariše v stanju down

	ImagePtr WarnImage;

   private:
	ImagePtr High;
	ImagePtr Down;
	ImagePtr Normal;
	ImagePtr Disabled;
	ImagePtr BtnIcon;
	int mRestriction = 0;
	DelegateHandle RestrictionChangeHandle;

	ELockedState RestrictionLock = ELockedState::UNLOCKED;

	void updateRestrictionLock(int64_t restriction);
};
