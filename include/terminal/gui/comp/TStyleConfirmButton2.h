/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include "TStyleButton2.h"

/**
    <AUTHOR> <<EMAIL>>
*/
class TStyleConfirmButton2 : public TStyleButton2
{
   public:
	TStyleConfirmButton2(Container* parent, const LocalizedMessage& Caption, const ColorProperty& TextColor, std::string ClickSound);

	virtual void drawLogic(Graphics* graphics, float deltaTime) override;

	std::vector<LocalizedMessage> mCaptionList;

	bool isInConfirmation() const { return mExpiresAt; }

	void resetConfirmButton();

   private:
	Uint64 mExpiresAt = 0;
	std::vector<LocalizedMessage> mOrigCaptions;
	size_t mShownIdx = 0;

   protected:
	virtual void generateAction() override;
};
