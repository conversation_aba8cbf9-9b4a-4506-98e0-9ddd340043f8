#pragma once

#include "TActionElement.h"
#include "TScrollArea.h"

class DropdownItemContainer final : public TScrollArea, public TActionElement
{
   public:
	DropdownItemContainer(TActionElement* parent);

	virtual bool IsLocked() const override;

	virtual bool DoAction_Implementation(const HardwareButtonEvent& ev) override;

	virtual void GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const override;

	virtual void drawBackground(Graphics* graphics, const Outline& outline) override;

	void setConsideredItemsNumber(const size_t& itemsNumber);

	void setItemHeight(const float& itemHeight);

	void setTipConsideration(const bool TipConsideration);

	void setItemMargin(const float& itemMargin);

   private:
	TActionElement* pParent;

	float mItemHeight = 0;
	float mItemMargin = 0;
	bool bIsTipConsidered = false;
	size_t mConsideredItemsNumber = 0;
};