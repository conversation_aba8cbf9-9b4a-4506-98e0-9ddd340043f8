#pragma once

#include "PlatformGamePackage.h"
#include "gui/widgets/icon.hpp"
#include "gui/widgets/label.hpp"

class TLobbyMenuButton : public LayoutContainer
{
   public:
	struct LobbyMenuButtonData
	{
		LocalizedMessage Title;
		ImagePtr ButtonImageEnabled;
		ImagePtr ButtonImageDisabled;
	};

	TLobbyMenuButton(const LobbyMenuButtonData& data);
	void SetSelected(bool selected);
	bool GetSelected() const;

	Label* plTitle;

   private:
	ImagePtr Normal;
	ImagePtr Disabled;

	Icon* piCategoryItem;
	bool IsSelected;

	ImageBrush BackgroundWhenSelected;

	std::string selectedTypography;
	std::string defaultTypography;

	std::optional<ImageBrush> BackgroundWhenNotSelected;

	PROPERTY(Outline, SelectedOutline);

	virtual const OutlineProperty& getOutlineStyle() const override;
};
