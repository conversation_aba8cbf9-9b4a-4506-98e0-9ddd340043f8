#pragma once

#include <gui/widgets/button.hpp>

#include "StyleElements.h"
#include "TDBDataSet.h"
#include "TScrollArea.h"
#include "YDelegate.h"

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnRowSelected, int)


class THeaderButton : public Button
{
   private:
	LocalizedMessage mOrigCaption;
	LocalizedMessage CaptionSuffix;

   public:
	THeaderButton(const LocalizedMessage& CaptionLine1, const LocalizedMessage& CaptionLine2, std::string FieldName);

	std::string FieldName;

	void setCaptionSuffix(const LocalizedMessage& suffix);
};

struct TFieldColorRule
{
	std::string FieldName;
	std::string Value;
	Color rColor;
};

class TDBGridListBox : public TScrollArea
{
   private:
	int mSelected = -1;
	double mLastStatusChange = 0.;

	DelegateHandle DataSetHandle;

	class GridContent : public LayoutContainer, public MouseListener
	{
		class TDBGridListBox* ParentList = NULL;

		virtual void mousePressed(MouseEvent& ev) override;

	   public:
		GridContent(TDBGridListBox* parent);

		virtual void draw(Graphics* graphics) override;
	};

   protected:
	virtual void draw(Graphics* graphics) override;

   public:
	TDBGridListBox(TDBDataSet* pDataSet, class TDBGrid* pDBGrid);

	TDBDataSet* pDataSet;

	class TDBGrid* pDBGrid;

	void ReloadDataSet();

	void setRowHeight(float hRow);

	/*Height of one row*/
	float RowHeight = 20;
	float Padding = 4.f;
	size_t RowsPerPage = 200;
	bool bScrollToLastOnLoad = false;
	BaseFont* nFont;
	BaseFont* sFont;

	FOnRowSelected OnRowSelected;

	int getSelectedRow() const;
	void setSelectedRow(int selected);

	void pageDown();
	void pageUp();

	void SelectFirst() { setSelectedRow(0); };
	void SelectLast() { setSelectedRow(pDataSet->Count()); };
	void SelectPrevious();
	void SelectNext();
};

struct TCell
{
	float Width;
	int FieldIndex;
	std::string FieldName;
	Alignment2D Alignment;
	bool FormatAsCurrency;
	THeaderButton* widget;
};


class TDBGrid : public LayoutContainer, public TypographyWidget
{
   public:
	TDBGrid(TDBDataSet* pDataSet, float headerHeight, float rowHeight, Container* pParent, float x, float y, float w, float h);

	TDBDataSet* pDataSet;

	TDBGridListBox* DBListBox;

	Container* TitleBar;

	std::vector<TCell> HeaderCells;

	void AddTitleColumn(const LocalizedMessage& CaptionLine1, const std::string& FieldName, float width, const Alignment2D& Alignment = align::LEFT_CENTER,
	                    bool FormatAsCurrency = false)
	{
		AddTitleColumn(CaptionLine1, {}, FieldName, width, Alignment, FormatAsCurrency);
	}

	void AddTitleColumn(const LocalizedMessage& CaptionLine1, const LocalizedMessage& CaptionLine2, const std::string& FieldName, float width,
	                    const Alignment2D& Alignment = align::LEFT_CENTER, bool FormatAsCurrency = false);

	void SetTitleColumnCaption(size_t col, const LocalizedMessage& Caption);

	void setRowHeight(float height);

	void setTitleBarHeight(float height);

	size_t PageFieldOffset = 0;

	int ListBoxHeight;

	bool bAutoGet = false;

   private:
	DelegateHandle OnListChangedHandle;
};