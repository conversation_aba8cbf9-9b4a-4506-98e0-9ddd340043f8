/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include <TSetupBase.h>

#include "TAdminKeySystem.h"

/**
    <AUTHOR> <PERSON> <<EMAIL>>
*/
class TKeyProperties : public TSetupBase
{
   public:
	TKeyProperties(Container* pParent, float x, float y, float w, float h);

	virtual void Refresh() override;

	void setKeyRights(const std::set<EKeyRights>& KeyRights);

	void setMasterKey(const TAdminKeyDescriptor& key);

	void setEditedkey(const TAdminKeyDescriptor& key);
	const TAdminKeyDescriptor& getCurrentEditedKey() const;

   private:
	bool bHasClipboardRights;
	std::bitset<EKeyRights::_size()> clipboardKeyRights;

	std::map<EKeyRights, TToggleButton*> vsKeyRights;

	TAdminKeyDescriptor EditedKey;
	TAdminKeyDescriptor MasterKey;

	void refreshKeyInfo();

	TStyleButton2* btnClearAll;
	TStyleButton2* btnSetAll;
	TStyleButton2* btnCopy;
	TStyleButton2* btnPaste;
	std::vector<TStyleButton2*> btnPresets;
};
