/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>                                  *
 *   <EMAIL>                                              *
 ***************************************************************************/
#pragma once

#include "PackmanClient.h"
#include "TMenuPanelBase.h"
#include "TScreenshots.h"
#include "common/TGameDBSrv.h"
#include "comp/TScrollArea.h"
#include "comp/TStyleSlider2.h"

struct ScreenshotData
{
	SDL_Surface* screenshot = NULL;
	SDL_Surface* modifiedDate = NULL;
	SDL_Surface* modifiedTime = NULL;
	SDL_Surface* extraText = NULL;
	SDL_Surface* betText = NULL;
	SDL_Surface* winText = NULL;
	SDL_Surface* denomiText = NULL;
};

constexpr const int REEL_SIZE = 9;

/**
    <AUTHOR> Toškan <<EMAIL>>
*/
class TScreenshotViewer : public TMenuPanelBase, public MouseListener
{
   private:
	struct FTranslatedTextToUse
	{
		std::string Results;
		std::string Bet;
		std::string Win;
		std::string Credit;
	};
	void threadedLoad(uint64_t loaderID, int screenN, const SavedGameround& round, const FTranslatedTextToUse& translations);
	FTranslatedTextToUse CurrentTranslations;
	double mDenomination = 0.01;
	std::string mCurrency;
	uint64_t mActiveLoadID = 0;

   public:
	TScreenshotViewer(Container* pParent, float x, float y, float w, float h);

	~TScreenshotViewer();

	virtual void mouseClicked(MouseEvent& mouseEvent) override;
	virtual void mouseDragged(MouseEvent& mouseEvent) override;
	virtual void mouseReleased(MouseEvent& mouseEvent) override;

	void OnShow();
	void OnHide();

	virtual void draw(Graphics* graphics) override;
	virtual void drawLogic(Graphics* graphics, float deltaTime) override;

	void OnCameBackFromGamePlay();

	void SetInstance(const TGameDBSrv::Instance& inst);

	void setSelectedScreenshot(int screenshotN);

   protected:
	TScreenshots* pScreenshotModule;

	TPackmanClient* Packman = NULL;

	iRectangle mMenuScreenDimension;

	TStyleSlider2* pSlider;
	TStyleButton2* mpBtnBack;
	TStyleButton2* mpBtnRefresh;
	TStyleButton2* mpBtnCounters;
	TStyleButton2* mpBtnLogs;
	TStyleButton2* mpBtnReplay;
	TStyleButton2* mpBtnCloseReplay;
	Label* pSelectedTerminalLabel;
	Label* pNoteScreenshotValuesLabel;

	TScrollArea* pCounterList = NULL;
	TScrollArea* pLogList = NULL;
	ImagePtr UnfinishedGameImage;

	ThreadSafeProperty<FontPtr, std::mutex> timestampFont;
	ThreadSafeProperty<FontPtr, std::mutex> numExtraResultsFont;

	TGameDBSrv::Instance mSelectedStation;

	void saveTextureToReel(int screenN, const ScreenshotData& loadedData);

	class ReelItem
	{
		ImagePtr mScreenshot;
		ImagePtr mTimestamp;
		ImagePtr mDate;
		ImagePtr mExtraScreensText;
		ImagePtr mBet;
		ImagePtr mWin;
		ImagePtr mCreditValue;
		uint64_t mGameRoundID = 0;
		SavedGameround mGameRound;

	   public:
		bool bAttemptedLoad = false;

		ReelItem() {}
		ReelItem(uint64_t gameRoundID, const SavedGameround& rInfo) : mGameRoundID(gameRoundID), mGameRound(rInfo) {}

		void setScreenshot(const ImagePtr& screenshot);

		void setTimestamp(const ImagePtr& timestamp);

		void setDate(const ImagePtr& date);

		void setExtraText(const ImagePtr& text);

		void setBet(const ImagePtr& bet);

		void setWin(const ImagePtr& win);

		void setCreditValue(const ImagePtr& win);

		const ImagePtr& screenshot() const { return mScreenshot; }

		const ImagePtr& timestamp() const { return mTimestamp; }

		const ImagePtr& date() const { return mDate; }

		const ImagePtr& extraScreenshotsText() const { return mExtraScreensText; }

		const ImagePtr& bet() const { return mBet; }

		const ImagePtr& win() const { return mWin; }

		const ImagePtr& creditValue() const { return mCreditValue; }

		void clear();

		const SavedGameround& round() const { return mGameRound; }

		uint64_t gameRoundID() const { return mGameRoundID; }
	};

	void onScreenshotSelected(int screenN) const;

	int moveReel(int finalPosition);
	std::vector<ReelItem> pReel;
	int pFirstScreenN;    // The screenshot index saved at pReelFirstIndex
	float mTargetReelPos = 0.f;
	float mScrollSmoothing = 0.f;
	float mScrollSmoothingTarget = 0.f;

	float mOverrideSwipePosition = -1.f;    // If more than 0, this value is used to calculate reel positions
	float mOnStartSwipeReelPos = -1.f;    // The position the reel was in when swipe began

	Rectangle ScreenshotArea;

	void deleteReelTextures();
	float getReelPos() const;

	int mNumImages = -1;
	std::filesystem::path mScreenshotDirectory;

	ImagePtr pGameLoading;
	bool bCanReplay = false;

	ImagePtr pImageIcon;
	SDL_Rect pLoadingSize;

	int mSelectedGameN;

	bool pCanGoBack;

	ImagePtr pZoomedScreenshot;
	void zoomIn(ImagePtr screenshot);
	void zoomOut();

	int ReloadDisplay();

	void animate(float fromScreenN, int toScreenN);

	void ReplaySelected();

	bool canReplay() const;

	void OnLogsPressed();
	void OnCountersPressed();

	void CloseReplayGame();

	virtual bool DoAction_Implementation(const HardwareButtonEvent& ev) override;
	virtual void GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const override;

	virtual void onLanguageChanged(ELanguage lang) override;
	void updateTranslations();
};
