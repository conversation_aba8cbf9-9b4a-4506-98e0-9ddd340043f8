#pragma once

#include "Database.h"
#include "TLogBase.h"
#include "comp/StyleElements.h"
#include "comp/TDBGrid.h"

/**
    <AUTHOR> <<EMAIL>>
*/
class TEventLogPanel : public TLogBase
{
   public:
	TEventLogPanel(Container* pParent, float x, float y, float w, float h);
	virtual int OnRefresh(int FilterMode, bool disableCache) override;

   private:
	void TransformField(size_t colIdx, TDBField& field) const;
	int timeOffset = 0;
};
