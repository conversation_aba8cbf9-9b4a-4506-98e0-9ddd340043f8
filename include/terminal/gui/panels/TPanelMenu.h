#pragma once

#include "TMenuPanelBase.h"
#include "TPayoutPanel.h"
#include "TSetupPanel.h"
#include "TTotalsAllLog.h"
#include "comp/StyleElements.h"
#include "components/TChecksumDisplay.h"

/**
    <AUTHOR> <<EMAIL>>
*/
class TPanelMenu : public TMenuPanelBase
{
   public:
	TPanelMenu(Container* pParent, float x, float y, float w, float h);
	~TPanelMenu();

	void BeforeShow();
	void DelayedShow();
	void OnHide();

	TPanelBase* GetOpenPanel() const;

	virtual void drawLogic(Graphics* graphics, float deltaTime) override;
	virtual void drawBackground(Graphics* graphics, const Outline& outline) override;
	bool IsAttendantMenuOpened() const;

	void CancelDelayMenuShow();
	void UpdateAssetsData();

	TSetupPanel* pPnlSetup;

	TPanelBase* pPnlCredit;
	TPayoutPanel* pPnlPayout;
	TPanelBase* pPnlCounters;
	TPanelBase* pPnlPowerMenu;
	// TPanelBase* mpPnlLastGames;
	TPanelBase* pOtherStationsPanel;
	TPanelBase* pPnlHoperRetry;
	TPanelBase* pPnlHoperControl;

	TTotalsAllLog* pTotalsAllLog;

	bool mCloseButtonEnabled;

   private:
	void CopyUpdateFromUSBAndExecute();

	TStyleButton2* pBtnPayin;
	TStyleButton2* pBtnPayout;
	TStyleButton2* pBtnSetup;
	TStyleButton2* pBtnCounter;
	TStyleButton2* pBtnTotalCounter;
	TStyleButton2* pBtnPowerMenu;
	TStyleButton2* pBtnKeyboard;
	TStyleButton2* pBtnOtherStations;
	TStyleButton2* pBtnHoperControl;
	TStyleButton2* pBtnInternet;
	std::optional<std::string> mInternetError;

	//		TStyleButton2* pBtnOpen;
	TStyleButton2* pBtnClose;
	TStyleConfirmButton2* pBtnUpdateFromUSB;
	TStyleButton2* pBtnPrintLastTicket;

	// UNUSED  Label* pLblCaption;
	Label* pLblKeyCaption;
	Label* pLblCurrentDateTime;
	Label* pLblPeripheralWarning;
	Label* pLblFPS;
	Label* pLblNoKeyProgrammedWarning;
	long AllowPayinByAttendant;

	Label* pLblSerial;
	Label* pLblValidity;
	Label* pLblTemperature;
	Label* plJurisdiction;

	std::weak_ptr<TTimedTaskHandler> DelayedShowHandle;

	struct AssetData
	{
		std::string Name;
		std::string Manufacturer;
		std::string Version;
		std::string Checksum;
		bool CheckOK;

		AssetData(const std::string& name, const std::string& manufacturer, const std::string& version, const std::string& checksum, bool checkOk)
		{
			Name = name;
			Manufacturer = manufacturer;
			Version = version;
			Checksum = checksum;
			CheckOK = checkOk;
		}
	};
	Label* plAssetName;
	Label* plAssetManufacturer;
	Label* plAssetVersion;
	Label* plAssetChecksum;
	Label* plAssetCheck;
	std::vector<AssetData> mAssets;
	int mMD5Idx;
	void SetAssetCaptions();
	int GetFirstAssetCheckFailed();

	TStyleSlider* pMenuLanguage;

	uint64_t mLastRPCUpdateTimestamp;

	std::thread USBThread;
	std::binary_semaphore QuitUSBCheck = std::binary_semaphore(0);
	void CheckForUSB();

	virtual void onLanguageChanged(ELanguage lang) override;

	Rectangle PanelArea;

	virtual bool DoAction_Implementation(const HardwareButtonEvent& ev) override;
	virtual void GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const override;

	void ShowStationAssignDialogIfNotAssigned();
};
