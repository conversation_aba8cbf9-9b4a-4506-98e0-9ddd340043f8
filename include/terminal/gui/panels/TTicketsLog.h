/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include <TLogBase.h>

/**
    <AUTHOR> <<EMAIL>>
*/
class TTicketsLog : public TLogBase
{
   public:
	TTicketsLog(Container* pParent, float x, float y, float w, float h);
	virtual int OnRefresh(int FilterMode, bool disableCache) override;
};
