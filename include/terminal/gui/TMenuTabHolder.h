#pragma once

#include "PlatformGamePackage.h"
#include "comp/TLobbyMenuButton.h"
#include "gui/layout.hpp"

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnGameCategorySelected, ELobbyGameCategory);
DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnLobbyButtonPressed, ELobbyButtons);

class TMenuTabHolder : public LayoutContainer
{
   public:
	TMenuTabHolder();

	TLobbyMenuButton* addCategory(ELobbyGameCategory gameCategory);
	TLobbyMenuButton* addCategory(ELobbyButtons lobbyButton);

	void SetSelectedCategory(ELobbyGameCategory category);
	void DeselectAllLobbyButtons();
	void SetSelectedButton(ELobbyButtons category, bool triggerEvent = true);
	void SetCategoryVisible(ELobbyGameCategory category, bool visible);

	void SetLobbyVisible(bool visible);
	void SetNumberOfActiveGames(int numberOfActiveGames);

	TLobbyMenuButton* GetLobbyButton(ELobbyButtons category);

	FOnGameCategorySelected OnGameCategorySelected;
	FOnLobbyButtonPressed OnLobbyButtonPressed;

	PROPERTY(Brush, MenuButtonBackground);
	PROPERTY(Shadow, SelectedMenuButtonShadow);

   private:
	bool bLobbyVisible = true;
	uint8_t ActiveGameCounter = 0;

	std::map<ELobbyGameCategory, TLobbyMenuButton*> mGameCategories;
	std::map<ELobbyButtons, TLobbyMenuButton*> mLobbyButtons;
	ELobbyGameCategory mSelectedCategory = ELobbyGameCategory::AllGames;
	ELobbyButtons mSelectedButton = ELobbyButtons::AddGame;
	std::shared_ptr<Shader> SelectionCutoutShader;

	GridLayout* gridLayout = NULL;

	LayoutContainer* gameCategoryContainer = NULL;

	virtual void drawBackground(Graphics* graphics, const Outline& outline) override;
	virtual bool shouldDrawBackground(const Brush& background, const Outline& outline) const override;
};
