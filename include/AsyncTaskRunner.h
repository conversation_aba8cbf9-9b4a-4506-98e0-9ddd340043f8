#pragma once

#include <boost/asio/basic_waitable_timer.hpp>
#include <boost/asio/strand.hpp>
#include <boost/asio/thread_pool.hpp>
#include <functional>
#include <list>
#include <queue>
#include <unordered_map>
#include <unordered_set>

#include "ThreadSafeProperty.h"

enum class ETaskRunModeWhenRunnerNotActive : uint8_t
{
	ThrowException,
	Queued,
	ExecuteSynchronously
};

class AsyncTaskRunner
{
   public:
	struct TaskExecution
	{
		std::chrono::system_clock::time_point Start = {};
		std::chrono::system_clock::time_point End = {};
		std::optional<std::exception> Exception = {};
	};
	struct ThreadProfileInfo
	{
		std::chrono::system_clock::time_point Start = {};
		std::chrono::system_clock::time_point End = {};
		std::list<TaskExecution> Tasks = {};

		std::string PrintUsage(size_t charCount) const;
	};

	bool bEnableProfiling = false;
	std::chrono::system_clock::duration ProfilingMaxAge = std::chrono::minutes(5);

	static TaskExecution ExecuteTaskProfiled(const std::function<void()>& task);

   private:
	struct QueueWorker
	{
		boost::asio::strand<boost::asio::thread_pool::executor_type> Strand;
		size_t NumTasks = 0;

		QueueWorker(boost::asio::thread_pool& io) : Strand(io.executor()) {}
	};

	ThreadSafeProperty<std::unordered_map<std::string, QueueWorker>, std::mutex> RunningQueues;
	imaxa_condition_variable RunningQueuesCV;
	std::unique_ptr<boost::asio::thread_pool> ThreadPool;
	std::unordered_map<std::thread::id, ThreadProfileInfo> ProfilingData;
	std::list<std::thread> ThreadsInPool;
	std::queue<std::pair<std::function<void()>, std::string>> QueuedTasksWaitingForRunnerStart;

	std::function<void()> WrapTask(std::function<void()> task);

   public:
	ETaskRunModeWhenRunnerNotActive TaskRunModeWhenRunnerNotActive = ETaskRunModeWhenRunnerNotActive::ThrowException;

	AsyncTaskRunner();
	virtual ~AsyncTaskRunner();

	std::unordered_map<std::thread::id, ThreadProfileInfo> ShutdownTaskWorkers();

	void SpawnWorkers(size_t num, const std::string& workerName = {});

	std::unordered_set<std::thread::id> GetWorkers() const;

	std::unordered_map<std::thread::id, ThreadProfileInfo> GetProfilingData() const { return ProfilingData; }

	// this takes around 10 microseconds to queue the task
	void DoAsyncTask(std::function<void()> task, const std::string& queue_id = std::string());

	void FlushAsyncTaskQueue(const std::string& id);

	std::shared_ptr<boost::asio::basic_waitable_timer<std::chrono::system_clock>> CreateTimer(const std::chrono::system_clock::time_point& time,
	                                                                                          const std::function<void(const boost::system::error_code&)>& handler) const
	{
		if (!ThreadPool)
			throw std::runtime_error("No thread pool available to create timer");

		auto ret = std::make_shared<boost::asio::basic_waitable_timer<std::chrono::system_clock>>(*ThreadPool, time);
		ret->async_wait(handler);
		return ret;
	}

	template <std::forward_iterator It, typename Handler>
	void ParallelFor(It begin, It end, const Handler& f)
	{
		if (begin == end)
			return;

		ThreadSafeProperty<size_t, std::mutex> InProgress { static_cast<size_t>(std::distance(begin, end)) };
		imaxa_condition_variable CVar;
		for (auto it = begin; it != end; it++)
		{
			DoAsyncTask([&f, it, &InProgress, &CVar]() {
				f(*it);
				InProgress.Lock();
				(&InProgress)--;
				InProgress.Unlock();
				CVar.notify_one();
			});
		}

		ScopedLock lock(InProgress);
		CVar.wait(lock, [&InProgress]() -> bool { return &InProgress == 0; });
	}
};