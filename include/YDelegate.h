#pragma once

#include <oneapi/tbb/concurrent_hash_map.h>

#include <map>

#include "ThreadSafeProperty.h"

// TODO: Write tests for delegates

class [[nodiscard]] DelegateHandleInfo
{
   private:
	size_t DelegateID = 0;
	size_t mID = 0;

	friend class BaseDelegateInterface;

   public:
	DelegateHandleInfo() {}
	DelegateHandleInfo(size_t handleID, size_t delegateID)
	{
		mID = handleID;
		DelegateID = delegateID;
	}
	DelegateHandleInfo(const DelegateHandleInfo& copy) = delete;
	~DelegateHandleInfo() { Invalidate(); }

	void Invalidate();

	size_t ID() const { return mID; }

	bool Valid() const { return DelegateID && mID; }
};

class [[nodiscard]] DelegateHandle : public std::shared_ptr<DelegateHandleInfo>
{
   public:
	DelegateHandle() {}
	DelegateHandle(const std::shared_ptr<DelegateHandleInfo>& ptr) : shared_ptr(ptr) {}
};

class BaseDelegateInterface
{
   private:
	static std::atomic<size_t>& GetDelegateID();
	static tbb::concurrent_hash_map<size_t, BaseDelegateInterface*>& GetDelegateMap();

	size_t ID;

   protected:
	BaseDelegateInterface();
	BaseDelegateInterface(BaseDelegateInterface& other);
	virtual ~BaseDelegateInterface();

	BaseDelegateInterface& operator=(BaseDelegateInterface& other);

	virtual void unref(DelegateHandleInfo& handle);

	DelegateHandle ref(size_t handleId) const;

	friend class DelegateHandleInfo;
};

template <typename ResultType, typename... ArgTypes>
class BaseDelegate : public BaseDelegateInterface
{
   public:
	typedef std::function<ResultType(ArgTypes...)> handler_function;

   protected:
	struct HandlerInfo
	{
		handler_function Method;
		bool bHasHandle = false;
		std::weak_ptr<DelegateHandleInfo> Handle;
	};
	typedef std::map<size_t, HandlerInfo> MapType;
	typedef typename MapType::const_reference DelegatePair;

	size_t mMaxID = 0;
	ThreadSafeProperty<MapType, std::shared_mutex> mCallbacks;

	virtual void unref(DelegateHandleInfo& handle) override
	{
		if (handle.ID())
		{
			ScopedLock lock(mCallbacks);
			mCallbacks->erase(handle.ID());
		}
		BaseDelegateInterface::unref(handle);
	}

   public:
	BaseDelegate() = default;

	virtual ~BaseDelegate() override
	{
		ScopedLock lock(mCallbacks);
		for (DelegatePair pair : &mCallbacks)
		{
			if (pair.second.bHasHandle)
			{
				if (auto handle = pair.second.Handle.lock())
					BaseDelegateInterface::unref(*handle);
			}
		}
		mCallbacks->clear();
	}

	BaseDelegate(BaseDelegate&& callback) noexcept : BaseDelegateInterface(callback) { *this = std::move(callback); }
	BaseDelegate(const BaseDelegate& callback) = delete;

	BaseDelegate(const handler_function& handler) { *this = handler; }

	BaseDelegate& operator=(const BaseDelegate& callback) = delete;

	BaseDelegate& operator=(BaseDelegate&& callback) noexcept
	{
		BaseDelegateInterface::operator=(callback);
		std::swap(mCallbacks, callback.mCallbacks);
		std::swap(mMaxID, callback.mMaxID);
		return *this;
	}

	DelegateHandle bind(const handler_function& f)
	{
		ScopedLock lock(mCallbacks);
		mMaxID++;
		HandlerInfo& emplaced = (&mCallbacks)[mMaxID];
		DelegateHandle newHandle = ref(mMaxID);
		emplaced.bHasHandle = true;
		emplaced.Handle = newHandle;
		emplaced.Method = f;
		return newHandle;
	}

	void bindLocal(const handler_function& f)
	{
		ScopedLock lock(mCallbacks);
		mMaxID++;
		HandlerInfo& emplaced = (&mCallbacks)[mMaxID];
		emplaced.Method = f;
	}

	BaseDelegate& operator+=(const handler_function& f)
	{
		bindLocal(f);
		return *this;
	}

	BaseDelegate& operator-=(const DelegateHandle& handle)
	{
		if (handle)
		{
			ScopedLock lock(mCallbacks);
			mCallbacks->erase(handle->ID());
		}
		return *this;
	}

	typedef std::conditional_t<std::is_same_v<ResultType, void>, void, std::map<size_t, ResultType>> return_type;
	virtual return_type operator()(ArgTypes... args) const = 0;

	void clear()
	{
		ScopedLock lock(mCallbacks);
		mCallbacks->clear();
		mMaxID = 0;
	}

	inline bool isBound() const { return static_cast<bool>(num()); }

	// returns the number of handlers bound to this delegate
	size_t num() const
	{
		SharedScopedLock lock(mCallbacks);
		return mCallbacks->size();
	}
};

template <typename... ArgTypes>
class SimpleSyncDelegate : public BaseDelegate<void, ArgTypes...>
{
   public:
	virtual void operator()(ArgTypes... args) const override
	{
		auto calls = this->mCallbacks.getCopy();

		for (const auto& p : calls)
		{
			if (p.second.bHasHandle && p.second.Handle.expired())
				continue;

			p.second.Method(args...);
		}
	}
};

template <typename ResultType, typename... ArgTypes>
class ResultSyncDelegate : public BaseDelegate<ResultType, ArgTypes...>
{
   public:
	virtual std::map<size_t, ResultType> operator()(ArgTypes... args) const override
	{
		auto calls = this->mCallbacks.getCopy();

		std::map<size_t, ResultType> results;
		for (const auto& p : calls)
		{
			if (p.second.bHasHandle && p.second.Handle.expired())
				continue;

			ResultType res = p.second.Method(args...);
			results[p.first] = res;
		}

		return results;
	}
};

#define DECLARE_SIMPLE_DELEGATE(name, ...)                 typedef std::function<void(__VA_ARGS__)> name;
#define DECLARE_RETURN_DELEGATE(name, type, ...)           typedef std::function<type(__VA_ARGS__)> name;
#define DECLARE_SIMPLE_MULTICAST_DELEGATE(name, ...)       typedef SimpleSyncDelegate<__VA_ARGS__> name;
#define DECLARE_RETURN_MULTICAST_DELEGATE(name, type, ...) typedef ResultSyncDelegate<type, __VA_ARGS__> name;