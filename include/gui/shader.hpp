#pragma once

#include <bitset>
#include <filesystem>
#include <set>
#include <stack>

#include "../../sdl-gpu/src/externals/glew/GL/glew.h"
#include "Logger.h"
#include "SDL_gpu.h"
#include "gui/brush.hpp"
#include "gui/color.hpp"
#include "image.hpp"
#include "rectangle.hpp"

DECLARE_LOG_CATEGORY(LogShader, Normal);

struct ShaderBufferInfo
{
	GPU_ShaderBufferAccessFrequency AccessFrequency = GPU_ShaderBufferAccessFrequency::GPU_BUFFER_ACCESS_FREQUENCY_DRAW;
	GPU_ShaderBufferUsageType Usage = GPU_ShaderBufferUsageType::GPU_BUFFER_USAGE_DRAW;
	std::vector<uint8_t> InitialValue;
};

struct ShaderCompileSettings
{
	std::string VertexShader = {};
	std::string FragShader = {};
	size_t NumBrushes = 0;
	std::map<std::string, std::string> Defines = {};
	std::set<std::string> Samplers = {};
	std::set<std::string> AdditionalUniforms = {};
	std::map<std::string, ShaderBufferInfo> Buffers = {};
	bool bRequireLibrary = false;
};

struct SamplerSettings
{
	Rectangle SampleArea { 0, 0, 1, 1 };
};

template <typename T>
struct GL_PaddedType
{
	T Value;

	GL_PaddedType(T val) : Value(std::move(val)) {}

	GL_PaddedType<T>& operator=(T val)
	{
		Value = std::move(val);
		return *this;
	}

   private:
	char Padding[16 - sizeof(T) % 16] = {};
};

struct ShaderProgram
{
   public:
	typedef uint8_t TextureUnitIndex;

   private:
	class UsedBrushes : public std::vector<EBrushType>
	{
	   public:
		std::string Print() const;

		size_t Hash() const;
	};

	Uint32 VertShaderID = 0;
	struct FragmentShaderInstancedPerBrush
	{
		struct InstancedBufferInfo
		{
			uint32_t BufferID = 0;
			size_t Size = 0;
			int Location = -1;
			ShaderBufferInfo Info = {};

			operator bool() const { return BufferID && Size; }
		};

		Uint32 FragShaderID = 0;
		Uint32 ShaderProgramID = 0;
		GPU_ShaderBlock ShaderBlock = { -1, -1, -1, -1 };
		std::map<std::string, int> CachedUniforms;
		InstancedBufferInfo BrushesUniformBuffer = {};
		std::vector<uint32_t> BrushOffsets;
		std::vector<int> BrushTextureUniformLocations;
		int ShaderSizeUniformLocation = -1;
		std::map<std::string, InstancedBufferInfo> CachedBuffers;

		~FragmentShaderInstancedPerBrush();

		void CacheUsedUniforms(const std::set<std::string>& names, bool bWarnIfDoesNotExists = true, bool bIsUniformBuffer = false);

		void CacheBuffers(const std::map<std::string, ShaderBufferInfo>& buffers, bool bWarnIfDoesNotExists = true);

		int UniformLocation(const std::string& name, bool bWarnIfDoesNotExists = true, bool bIsUniformBuffer = false);

		InstancedBufferInfo* GetBuffer(const std::string& name, bool bWarnIfDoesNotExists = true);
		InstancedBufferInfo GetOrCreateBuffer(const std::string& name, const ShaderBufferInfo& info, bool bWarnIfDoesNotExists = true);

		bool SetActive(bool bActive);
	};

	std::unordered_map<size_t, FragmentShaderInstancedPerBrush> Instances;
	struct ActiveShader
	{
		FragmentShaderInstancedPerBrush* FragShader = NULL;
		std::set<uint32_t> BuffersUsed = {};
		std::unordered_map<GPU_Image*, size_t> Images = {};

		ActiveShader() {}
		ActiveShader(FragmentShaderInstancedPerBrush* fragShader, const std::vector<Brush>& brushes);

		static void InitializeBrushAt(uint8_t* data, const Brush& brush);
	};
	std::stack<ActiveShader> ActiveShaderStack;

	FragmentShaderInstancedPerBrush& LoadFragmentShader(const std::string& headerSrc, const std::string& shaderSrc, const UsedBrushes& brushes, bool bRequireLibrary);
	void LoadFragmentShaders(const std::string& headerSrc, const std::string& shaderSrc, size_t numBrushes, const UsedBrushes& brushes, bool bRequireLibrary);

	struct ImageSampler
	{
		TextureUnitIndex texUnit = 0;
		ImagePtr tex;
	};
	std::map<std::string, ImageSampler> SamplerImages;

   public:
	bool Activate(const std::function<void(ShaderProgram&)>& worker, const std::vector<Brush>& brushes);

	void CacheUsedUniforms(const std::set<std::string>& names, bool bWarnIfDoesNotExists = true, bool bIsUniformBuffer = false);
	void CacheBuffers(const std::map<std::string, ShaderBufferInfo>& buffers, bool bWarnIfDoesNotExists = true);

	void SetSampler(const std::string& samplerImageName, ImagePtr image, const SamplerSettings& settings = {}, bool bWarnIfDoesNotExists = true);

	size_t SetSamplerTemp(const std::string& samplerImageName, GPU_Image* image, const SamplerSettings& settings = {}, bool bWarnIfDoesNotExists = true);

	void ClearSamplerImage(const std::string& samplerImageName, bool bWarnIfDoesNotExists = true);

	void ClearSamplerImage(const std::string& samplerImageName, size_t imageUnit, bool bWarnIfDoesNotExists = true);

	void SetFloatArray(const std::string& varName, const float* values, size_t arrayNum, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, values, 1, arrayNum, bWarnIfDoesNotExists);
	}
	void SetFloat(const std::string& varName, float value, bool bWarnIfDoesNotExists = true) { SetFloatArray(varName, &value, 1, bWarnIfDoesNotExists); }

	void SetInt(const std::string& varName, int value, bool bWarnIfDoesNotExists = true);

	void SetUInt(const std::string& varName, Uint32 value, bool bWarnIfDoesNotExists = true);

	void SetBufferData(const std::string& bufferName, const void* data, size_t size, bool bWarnIfDoesNotExists = true);

	inline void SetColor(const std::string& varName, const LinearColor& col, bool bWarnIfDoesNotExists = true) { SetVector(varName, col, bWarnIfDoesNotExists); }
	void SetColorArray(const std::string& varName, const LinearColor* col, size_t count, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, reinterpret_cast<const float*>(col), LinearColor::Dimension, count);
	}

	void SetVector(const std::string& varName, const float* floats, size_t dimension, size_t arrayCount = 1, bool bWarnIfDoesNotExists = true);
	inline void SetVector(const std::string& varName, const std::vector<float>& vec, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, vec.data(), vec.size(), bWarnIfDoesNotExists);
	}

	// input is column-major if bTranspose is false (meaning the matrix gets built column-first from the |matrix| array)
	// input is row-major if bTranspose is true (meaning the matrix gets built row-first from the |matrix| array)
	void SetMatrix(const std::string& varName, const sVector2D& size, const float* matrix, bool bTranspose = false, bool bWarnIfDoesNotExists = true);

	template <typename T, uint8_t dim>
	void SetMatrix(const std::string& varName, const Matrix<T, dim>& matrix, bool bTranspose = false, bool bWarnIfDoesNotExists = true)
	{
		SetMatrix(varName, { dim, dim }, matrix.data(), bTranspose, bWarnIfDoesNotExists);
	}
	void SetMatrixArray(const std::string& varName, const sVector2D& size, const float* matrix, bool bTranspose = false, bool bWarnIfDoesNotExists = true);

	template <IsVectorOfType<float> T>
	inline void SetVectorArray(const std::string& varName, const T* v, size_t arrayNum, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, reinterpret_cast<const float*>(v), T::Dimension, arrayNum, bWarnIfDoesNotExists);
	}

	template <IsVectorOfType<float> T>
	inline void SetVector(const std::string& varName, const T& v, bool bWarnIfDoesNotExists = true)
	{
		SetVectorArray(varName, &v, 1, bWarnIfDoesNotExists);
	}

	inline void SetRectangle(const std::string& varName, const Rectangle& rect, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, rect.asVector(), bWarnIfDoesNotExists);
	}

	inline void SetColor(const std::string& varName, float r, float g, float b, float a = 1.f, bool bWarnIfDoesNotExists = true)
	{
		SetVector(varName, BaseVector4D<float>(r, g, b, a), bWarnIfDoesNotExists);
	}

	void SetDrawSize(const Vector2D& size);

	~ShaderProgram();

	static std::bitset<128> AvailableTextureUnits;
	static std::bitset<64> AvailableBufferBindingPoints;

	static std::shared_ptr<ShaderProgram> Compile(const ShaderCompileSettings& settings);
};

class Shader
{
	std::shared_ptr<ShaderProgram> mProgram;
	Uint32 mFrameNum = 0;

	std::list<std::function<void(ShaderProgram&)>> mUpdates;

   public:
	Shader() {}
	Shader(const std::filesystem::path& vertShader, const std::filesystem::path& fragShader);
	Shader(const std::filesystem::path& fragShader);
	Shader(std::shared_ptr<ShaderProgram> program);
	Shader(const Shader& copy) = delete;

	Shader& operator=(const Shader& rhs) = delete;

	std::shared_ptr<ShaderProgram> Program() const;

	void Unload();

	void Update();

	void AddUpdateCallback(const std::function<void(ShaderProgram&)>& update);

	bool Activate(const std::function<void(ShaderProgram&)>& worker, const std::vector<Brush>& brushes = {});

	static const std::string CommonVertexShader;
};
