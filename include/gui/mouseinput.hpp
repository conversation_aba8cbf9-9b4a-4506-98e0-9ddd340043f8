#pragma once

#include <cstdint>

#include "Vector.h"

/**
 * Mouse event types.
 */
enum class EMouseEventType
{
	MOVED = 0,
	PRESSED,
	RELEASED,
	WHEEL_MOVED,
	ENTERED,
	EXITED
};

/**
 * Mouse button types.
 */
enum class EMouseEventButton : int
{
	NONE = 0,
	LEFT,
	RIGHT,
	MIDDLE
};

/**
 * Internal class representing mouse input. Generally you won't have to
 * bother using this class as it will get translated into a MouseEvent.
 * The class should be seen as a bridge between the low layer backends
 * providing input and the higher lever parts of the Gui (such as widgets).
 *
 * <AUTHOR>
 * <AUTHOR>
 */
class MouseInput
{
   public:
	/**
	 * Constructor.
	 */
	MouseInput() {};

	/**
	 * Constructor.
	 *
	 * @param button the button pressed.
	 * @param type the type of input.
	 * @param x the mouse x coordinate.
	 * @param y the mouse y coordinate.
	 * @param timeStamp the mouse inputs time stamp.
	 */
	MouseInput(EMouseEventButton button, EMouseEventType type, const Vector2D& pos, int timeStamp);

	/**
	 * Sets the input type.
	 *
	 * @param type the type of input.
	 */
	void setType(EMouseEventType type);

	/**
	 * Gets the input type.
	 *
	 * @return the input type.
	 */
	EMouseEventType getType() const;

	/**
	 * Sets the button pressed.
	 *
	 * @param button the button pressed.
	 */
	void setButton(EMouseEventButton button);

	uint32_t buttonState() const;

	void setButtonState(uint32_t state);

	/**
	 * Gets the button pressed.
	 *
	 * @return the button pressed.
	 */
	EMouseEventButton getButton() const;

	/**
	 * Sets the timestamp for the input.
	 *
	 * @param timeStamp the timestamp of the input.
	 */
	void setTimeStamp(uint32_t timeStamp);

	/**
	 * Gets the time stamp of the input.
	 *
	 * @return the time stamp of the input.
	 */
	uint32_t getTimeStamp() const;

	/**
	 * Sets the x coordinate of the input.
	 *
	 * @param x the x coordinate of the input.
	 * @since 0.6.0
	 */
	void setPos(const Vector2D& pos);

	/**
	 * Gets the x coordinate of the input.
	 *
	 * @return the x coordinate of the input.
	 * @since 0.6.0
	 */
	const Vector2D& getPos() const;

	/**
	 * Sets the x coordinate of the input.
	 *
	 * @param x the x coordinate of the input.
	 * @since 0.6.0
	 */
	void setMove(const Vector2D& delta);

	/**
	 * Gets the y coordinate of the input.
	 * @since 0.6.0
	 */
	const Vector2D& getMove() const;

	bool isPressed(EMouseEventButton button) const;

   protected:
	EMouseEventType mType;
	EMouseEventButton mButton = EMouseEventButton::NONE;
	uint32_t mTimeStamp = 0;
	Vector2D mPos;
	Vector2D mMove;
	uint32_t mButtonState = 0;
};
