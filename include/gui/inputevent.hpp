#pragma once

#include <cstdint>

#include "gui/event.hpp"
#include "gui/modifiers.h"

enum class EInputConsumedState : uint8_t
{
	NotConsumed,
	PropagationStopped,
	Handled
};

enum class EInputEventType : uint8_t
{
	Mouse,
	Key,
	Text
};

/**
 * Base class for all input events.
 *
 * <AUTHOR> @since 0.6.0
 */
class InputEvent : public Event
{
   public:
	/**
	 * Constructor.
	 *
	 * @param source the source widget of the event.
	 * @param modifiers state of modifiers
	 */
	InputEvent(Widget* source, EInputEventType type, Modifiers modifiers);

	EInputEventType getInputType() const;

	const Modifiers& modifiers() const;

	/**
	 * Marks the event as consumed. How widgets should act on consumed
	 * events are up to the widgets themselves.
	 */
	void consume(EInputConsumedState s = EInputConsumedState::Handled);

	EInputConsumedState consumedState() const;

	/**
	 * Checks if the input event is consumed.
	 *
	 * @return true if the input event is consumed, false otherwise.
	 */
	bool isConsumed() const;

   protected:
	Modifiers mModifiers;
	EInputConsumedState Consumed = EInputConsumedState::NotConsumed;
	EInputEventType InputType = EInputEventType::Mouse;
};