#pragma once

#include "gui/image.hpp"
#include "gui/widget.hpp"

/**
 * Implements an icon capable of displaying an image.
 */
class Icon : public Widget
{
   public:
	/**
	 * Constructor.
	 *
	 * @param filename The filename of the image to display.
	 */
	Icon(const std::string& filename);

	/**
	 * Constructor.
	 *
	 * @param image The image to display.
	 */
	Icon(const ImagePtr& image);

	Icon();

	/**
	 * Descructor.
	 */
	virtual ~Icon() {}

	void setImage(const ImagePtr& image);

	ImagePtr getImage() const { return mImage; }

	void setPadding(const Rectangle& padding);
	inline void setPadding(const Vector2D& padding) { setPadding(Rectangle(padding, padding)); }

	// Inherited from Widget

	virtual void draw(Graphics* graphics) override;

	void recomputeSize();

	Alignment2D Alignment = align::CENTER_CENTER;
	EScaleMode ScaleMode = EScaleMode::NONE;
	bVector2D Autosize = bVector2D(true);
	bool bIntegerScaling = false;
	GPU_FlipEnum Flip = GPU_FLIP_NONE;
	Vector2D ScaleFactor = Vector2D(1);
	PixelSnap PixelSnapping;

   protected:
	/**
	 * The image to display.
	 */
	ImagePtr mImage;
	Rectangle mPadding;
};
