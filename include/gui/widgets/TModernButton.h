#pragma once

#include "gui/layout.hpp"
#include "gui/widgets/icon.hpp"
#include "gui/widgets/label.hpp"

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnPressed)
DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnSelectedChanged, bool)

enum class EModernButtonType
{
	Normal,
	Radio,
	Toggle
};

class TModernButton : public LayoutContainer
{
   private:
	bool bSelected = false;

	Rectangle mPadding;

   public:
	PROPERTY_DEFAULT(Brush, SelectedBrush, Color(245, 208, 0));

	TModernButton(Container* parent, const Vector2D& fixedSize = {});

	virtual void drawLogic(Graphics* gfx, float dt) override;

	virtual const BrushProperty& getBackground() const override;

	void SetIcon(const ImagePtr& image);

	void SetPadding(const Rectangle& btnPadding, float iconPadding = 0.f);

	void SetPadding(const Vector2D& xyPadding, float iconPadding = 0.f);

	void SetBorderScale(float scale);

	void SetSelected(bool bSelected);

	bool IsSelected() const;

	Label* Text;
	Icon* pIcon;
	std::string ClickSound;
	EModernButtonType Type = EModernButtonType::Normal;
	bool bRadioButtonAllowDeselectAll = false;


	std::string RadioButtonGroup;

	FOnPressed OnPressed;
	FOnSelectedChanged OnSelectedChanged;
	std::function<bool()> EnabledCondition;
};
