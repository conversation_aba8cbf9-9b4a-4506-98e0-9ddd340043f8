#pragma once

#include <list>
#include <optional>

#include "YDelegate.h"

class Widget;
class Gui;

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnWidgetFocused, Widget*, Widget*);

/**
 * Used to keep track of widget focus. You will probably not have
 * to use the FocusHandler directly to handle focus. Widget has
 * functions for handling focus which uses a FocusHandler. Use them
 * instead.
 *
 * @see Widget::isFocused
 * @see Widget::requestFocus
 * @see Widget::setFocusable
 * @see Widget::isFocusable
 * @see FocusListener
 */
class FocusHandler
{
   public:
	/**
	 * Constructor.
	 */
	FocusHandler();

	/**
	 * Destructor.
	 */
	virtual ~FocusHandler() {};

	/**
	 * Sets focus to a widget. A focus event will also be sent to the widget's
	 * focus listeners.
	 *
	 * @param widget the widget to focus.
	 */
	virtual void requestFocus(Widget* widget);

	/**
	 * Sets modal focus to a widget.
	 *
	 * @param widget the Widget to focus modal.
	 * @throws Exception when another widget already has modal focus.
	 */
	virtual void requestModalFocus(Widget* widget, bool bForce);

	/**
	 * Releases modal focus if the widget has modal focus.
	 * Otherwise nothing will be done.
	 *
	 * @param widget the Widget to release modal focus for.
	 */
	virtual void releaseModalFocus(Widget* widget);

	/**
	 * Sets modal mouse input focus to a widget. Modal mouse input focus means
	 * no other widget then the widget with modal mouse input focus will
	 * receive mouse input..
	 * The widget with modal mouse input focus will also receive mouse input no
	 * matter what the mouse input is or where the mouse input occurs.
	 *
	 * @param widget the widget to focus for modal mouse input focus.
	 * @throws Exception when another widget already has modal mouse input focus.
	 */
	virtual void requestModalMouseInputFocus(Widget* widget);

	/**
	 * Releases modal mouse input focus if the widget has modal mouse input
	 * focus. Otherwise nothing will be done.
	 *
	 * @param widget the widget to release modal mouse input focus for.
	 */
	virtual void releaseModalMouseInputFocus(Widget* widget);

	/**
	 * Gets the widget with focus.
	 *
	 * @return the Widget with focus. NULL will be returned if
	 *         no Widget has focus.
	 */
	virtual Widget* getFocused() const;

	/**
	 * Gets the widget with modal focus.
	 *
	 * @return the Widget with modal focus. NULL will be returned
	 *         if no Widget has modal focus.
	 */
	virtual Widget* getModalFocused() const;

	/**
	 * Gets the widget with modal mouse input focus.
	 *
	 * @return the widget with modal mouse input focus. NULL will be returned
	 *         if no widget has modal mouse input focus.
	 */
	virtual Widget* getModalMouseInputFocused() const;

	/**
	 * Focuses the next Widget. If no Widget has focus the first
	 * Widget gets focus. The order in which the Widgets are focused
	 * depends on the order you add them to the GUI.
	 */
	virtual void focusNext(class Container* rootWhenNothingFocused);

	/**
	 * Focuses the previous Widget. If no Widget has focus the first
	 * Widget gets focus. The order in which the widgets are focused
	 * depends on the order you add them to the GUI.
	 */
	virtual void focusPrevious(class Container* rootWhenNothingFocused);

	/**
	 * Checks if a Widget is focused.
	 *
	 * @param widget widget to check if it is focused.
	 * @return true if the widget is focused.
	 */
	virtual bool isFocused(const Widget* widget) const;

	/**
	 * Focuses nothing. A focus event will also be sent to the focused widget's
	 * focus listeners if a widget has focus.
	 */
	virtual void focusNone();

	/**
	 * Focuses the next Widget which allows tab in unless current focused
	 * Widget disallows tab out.
	 */
	virtual void tabNext(class Container* rootWhenNothingFocused);

	/**
	 * Focuses the previous Widget which allows tab in unless current focused
	 * Widget disallows tab out.
	 */
	virtual void tabPrevious(class Container* rootWhenNothingFocused);

	/**
	 * Gets the widget being dragged.
	 *
	 * @return the widget being dragged.
	 */
	virtual Widget* getDraggedWidget() const;

	bool isDragging(const Widget* w) const;

	virtual std::optional<size_t> getHoverDepth(Widget* w) const = 0;

	/**
	 * Sets the widget being dragged.
	 *
	 * @param draggedWidget the widget being dragged.
	 */
	virtual void setDraggedWidget(Widget* draggedWidget);

	/**
	 * Gets the last widget with modal focus.
	 *
	 * @return the last widget with modal focus.
	 */
	virtual Widget* getLastWidgetWithModalFocus();

	/**
	 * Sets the last widget with modal focus.
	 *
	 * @param lastWidgetWithModalFocus the last widget with modal focus.
	 */
	virtual void setLastWidgetWithModalFocus(Widget* lastWidgetWithModalFocus);

	/**
	 * Gets the last widget with modal mouse input focus.
	 *
	 * @return the last widget with modal mouse input focus.
	 */
	virtual Widget* getLastWidgetWithModalMouseInputFocus();

	/**
	 * Sets the last widget with modal mouse input focus.
	 *
	 * @param lastMouseWithModalMouseInputFocus  the last widget with modal mouse input focus.
	 */
	virtual void setLastWidgetWithModalMouseInputFocus(Widget* lastWidgetWithModalMouseInputFocus);

	/**
	 * Gets the last widget pressed.
	 *
	 * @return the last widget pressed.
	 */
	virtual Widget* getLastWidgetPressed();

	/**
	 * Sets the last widget pressed.
	 *
	 * @param lastWidgetPressed the last widget pressed.
	 */
	virtual void setLastWidgetPressed(Widget* lastWidgetPressed);

	void setDefaultFocusedWidget(Widget* w);

	FOnWidgetFocused OnFocused;

   protected:
	struct FocusedWidgetData
	{
		Widget* WidgetPtr = nullptr;
		DelegateHandle DeathHandle;
		DelegateHandle VisibilityChangedHandle;

		FocusedWidgetData() {}
		FocusedWidgetData(Widget* w, const std::function<void(Widget*)>& onDeathOrEffectivelyInvisible);
	};

	FocusedWidgetData mFocusedWidget;
	FocusedWidgetData mDefaultFocusedWidget;

	std::list<FocusedWidgetData> mModalFocusedWidgets;
	Widget* mModalMouseInputFocusedWidget;

	Widget* mDraggedWidget;
	Widget* mLastWidgetWithModalFocus;
	Widget* mLastWidgetWithModalMouseInputFocus;
	Widget* mLastWidgetPressed;

	void focus(Widget* widget);

   private:
	void onFocusedWidgetDestroyed(Widget* w);

	bool isTabFocusableWidget(Widget* w) const;

	void focusNextOrPrevInternal(Container* rootWhenNothingFocused, bool bNext);
	void tabNextOrPrevInternal(Container* rootWhenNothingFocused, bool bNext);
};
