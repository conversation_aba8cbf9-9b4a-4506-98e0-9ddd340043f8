#pragma once

#include <functional>
#include <list>

#include "YDelegate.h"
#include "gui/transition.hpp"

template <typename T>
concept Interpolatable = requires(T a, T b, float d) {
	// Check if the assignment operator is defined
	{ a = b } -> std::same_as<T&>;

	// Check if the sum operator with the same type is defined
	{ a + b } -> std::same_as<T>;

	// Check if the multiplication operator with a floating-point type is defined
	{ a* d } -> std::same_as<T>;
} || std::is_floating_point_v<T>;

class Widget;

template <Interpolatable T>
struct AnimationFrame
{
	T& Property;
	const T& LastValue;
	const T& TargetValue;
	const T& AnimatedValue;
};

template <Interpolatable T>
using AnimationTrackCustomSetter = std::function<void(const AnimationFrame<T>&)>;

struct AnimationPropertyTrack
{
   private:
	Widget* PropertyParent;
	ptrdiff_t Offset;
	size_t PropertySize;
	void* ValueCache;    // heap allocated storage of the starting value [index 0], the last value [index 1] and the target value [index 2]
	void (*Deleter)(void*);
	std::function<void(const AnimationPropertyTrack*, float)> AnimationHandler;

	enum ECachedValue : uint8_t
	{
		START_VALUE,
		LAST_VALUE,
		TARGET_VALUE
	};

	template <Interpolatable T>
	T* GetCachedValues() const
	{
		return static_cast<T*>(ValueCache);
	}

   public:
	template <std::derived_from<Widget> Parent, Interpolatable T>
	static ptrdiff_t GetOffsetOfProperty(Parent* parent, T Parent::*member)
	{
		return reinterpret_cast<uint8_t*>(&(parent->*member)) - reinterpret_cast<uint8_t*>(static_cast<Widget*>(parent));
	}

	AnimationPropertyTrack() = delete;
	AnimationPropertyTrack(const AnimationPropertyTrack& copy) = delete;
	AnimationPropertyTrack(AnimationPropertyTrack&& move) noexcept;

	template <std::derived_from<Widget> Parent, Interpolatable T>
	AnimationPropertyTrack(Parent* w, T Parent::*member, T targetValue, const AnimationTrackCustomSetter<T>& customSetter = {})
	{
		PropertyParent = w;
		Offset = AnimationPropertyTrack::GetOffsetOfProperty(w, member);
		PropertySize = sizeof(T);
		ValueCache = new T[3];
		Deleter = [](void* ptr) {
			delete[] static_cast<T*>(ptr);
		};
		GetCachedValues<T>()[START_VALUE] = w->*member;
		GetCachedValues<T>()[LAST_VALUE] = w->*member;
		GetCachedValues<T>()[TARGET_VALUE] = targetValue;

		AnimationHandler = [customSetter](const AnimationPropertyTrack* track, float t) {
			track->GetCachedValues<T>()[LAST_VALUE] = *track->GetProperty<T>();
			const T animatedValue = track->GetCachedValues<T>()[START_VALUE] + (track->GetCachedValues<T>()[TARGET_VALUE] - track->GetCachedValues<T>()[START_VALUE]) * t;
			const AnimationFrame<T> frame { *track->GetProperty<T>(), track->GetLastValue<T>(), track->GetTargetValue<T>(), animatedValue };
			if (customSetter)
				customSetter(frame);
			else
				frame.Property = animatedValue;
		};
	}

	template <Interpolatable T>
	AnimationPropertyTrack(Widget* w, T& member, T targetValue, const AnimationTrackCustomSetter<T>& customSetter = {})
	{
		PropertyParent = w;
		Offset = reinterpret_cast<const uint8_t*>(&member) - reinterpret_cast<const uint8_t*>(w);
		PropertySize = sizeof(T);
		ValueCache = new T[3];
		Deleter = [](void* ptr) {
			delete[] static_cast<T*>(ptr);
		};
		GetCachedValues<T>()[START_VALUE] = member;
		GetCachedValues<T>()[LAST_VALUE] = member;
		GetCachedValues<T>()[TARGET_VALUE] = targetValue;

		AnimationHandler = [customSetter](const AnimationPropertyTrack* track, float t) {
			track->GetCachedValues<T>()[LAST_VALUE] = *track->GetProperty<T>();
			const T animatedValue = track->GetCachedValues<T>()[START_VALUE] * (1.f - t) + track->GetCachedValues<T>()[TARGET_VALUE] * t;
			const AnimationFrame<T> frame { *track->GetProperty<T>(), track->GetLastValue<T>(), track->GetTargetValue<T>(), animatedValue };
			if (customSetter)
				customSetter(frame);
			else
				frame.Property = animatedValue;
		};
	}

	~AnimationPropertyTrack();

	ptrdiff_t GetPropertyOffset() const { return Offset; }

	Widget* GetParent() const { return PropertyParent; }

	template <Interpolatable T>
	const T& GetStartValue() const
	{
		return GetCachedValues<T>()[START_VALUE];
	}

	template <Interpolatable T>
	const T& GetLastValue() const
	{
		return GetCachedValues<T>()[LAST_VALUE];
	}

	template <Interpolatable T>
	const T& GetTargetValue() const
	{
		return GetCachedValues<T>()[TARGET_VALUE];
	}

	template <Interpolatable T>
	T* GetProperty() const
	{
		return reinterpret_cast<T*>(reinterpret_cast<uint8_t*>(PropertyParent) + Offset);
	}

	void Update(float a) const;
};

enum class EAnimationTrackAction : uint8_t
{
	Stop,
	FinishAndStop
};

struct TransitionTimeline : public Transition
{
   private:
	std::list<AnimationPropertyTrack> PropertyTracks;

	void Update(float elapsedSeconds) const;

	friend class AnimationController;

   public:
	TransitionTimeline(Transition transition) : Transition(std::move(transition)) {}

	void AddPropertyTrack(AnimationPropertyTrack track);

	template <std::derived_from<Widget> Parent, Interpolatable T>
	void AddPropertyTrack(Parent* w, T Parent::*member, T targetValue, const AnimationTrackCustomSetter<T>& customSetter = {})
	{
		AddPropertyTrack(AnimationPropertyTrack(w, member, targetValue, customSetter));
	}

	template <Interpolatable T>
	void AddPropertyTrack(Widget* w, T& member, T targetValue, const AnimationTrackCustomSetter<T>& customSetter = {})
	{
		AddPropertyTrack(AnimationPropertyTrack(w, member, targetValue, customSetter));
	}

	void ForEachPropertyTrack(const std::function<void(const AnimationPropertyTrack&)>& callback) const;

	size_t NumTracks() const;
};

class AnimationController
{
   public:
	struct ActiveTrackInfo
	{
		const AnimationPropertyTrack* Track = nullptr;
		Transition UsedTransition;
		double Elapsed = 0.0;
		double MaxAge = 0.0;
	};

   private:
	struct InProgressTimeline : public TransitionTimeline
	{
		double Elapsed = 0.0;
		double MaxAge = 0.0;

		InProgressTimeline(TransitionTimeline timeline);

		void Tick(double delta);
	};

	struct TimelineIteratorHash
	{
		size_t operator()(const std::list<InProgressTimeline>::iterator& it) const noexcept;
	};

	std::list<InProgressTimeline> Timelines;
	struct PropertyTrackInfo
	{
		std::list<InProgressTimeline>::iterator ParentTimeline;
		std::list<AnimationPropertyTrack>::iterator Track;

		void ExecAction(EAnimationTrackAction action);
	};
	struct WidgetAnimInfo
	{
		std::unordered_map<ptrdiff_t, PropertyTrackInfo> PropertyTracks;
		DelegateHandle OnWidgetDeath;
	};
	std::unordered_map<Widget*, WidgetAnimInfo> PerWidgetTimelines;

	void ExecActionOnPropertyTrack_Internal(Widget* widget, ptrdiff_t propertyOffset, EAnimationTrackAction action);
	ActiveTrackInfo GetPropertyTrack(Widget* widget, ptrdiff_t propertyOffset) const;

   public:
	void PlayTimeline(TransitionTimeline timeline);

	template <std::derived_from<Widget> Parent, Interpolatable T>
	ActiveTrackInfo GetPropertyTrack(Parent* widget, T Parent::*member) const
	{
		return GetPropertyTrack(widget, AnimationPropertyTrack::GetOffsetOfProperty(widget, member));
	}

	template <Interpolatable T>
	ActiveTrackInfo GetPropertyTrack(Widget* widget, T& member) const
	{
		return GetPropertyTrack(widget, reinterpret_cast<const uint8_t*>(&member) - reinterpret_cast<const uint8_t*>(widget));
	}

	template <std::derived_from<Widget> Parent, Interpolatable T>
	T GetAnimatedPropertyTargetValue(Parent* widget, T Parent::*member) const
	{
		auto track = GetPropertyTrack(widget, member);
		return track.Track ? track.Track->template GetTargetValue<T>() : widget->*member;
	}

	template <Interpolatable T>
	T GetAnimatedPropertyTargetValue(Widget* widget, T& member) const
	{
		auto track = GetPropertyTrack(widget, member);
		return track.Track ? track.Track->template GetTargetValue<T>() : member;
	}

	template <std::derived_from<Widget> Parent, Interpolatable T>
	void Animate(Parent* widget, T Parent::*member, T targetValue, const Transition& transition, const AnimationTrackCustomSetter<T>& customSetter = {})
	{
		TransitionTimeline timeline(transition);
		timeline.AddPropertyTrack(widget, member, targetValue, customSetter);
		PlayTimeline(std::move(timeline));
	}


	template <std::derived_from<Widget> Parent, Interpolatable T>
	void ExecActionOnPropertyTrack(Parent* widget, T Parent::*member, EAnimationTrackAction action)
	{
		ExecActionOnPropertyTrack_Internal(widget, AnimationPropertyTrack::GetOffsetOfProperty(widget, member), action);
	}

	template <Interpolatable T>
	void ExecActionOnPropertyTrack(Widget* widget, T& member, EAnimationTrackAction action)
	{
		ExecActionOnPropertyTrack_Internal(widget, reinterpret_cast<const uint8_t*>(&member) - reinterpret_cast<const uint8_t*>(widget), action);
	}

	void ExecActionOnWidgetTracks(Widget* forWidget, EAnimationTrackAction action) { ExecActionOnPropertyTrack_Internal(forWidget, 0, action); }

	void Tick(double delta);
};