#pragma once

#include <nlohmann/json.hpp>

using json = nlohmann::json;

namespace nlohmann
{
template <typename T>
struct adl_serializer<std::optional<T>>
{
	static void to_json(json& j, const std::optional<T>& opt)
	{
		if (opt)
			j = opt.value();
		else
			j = nullptr;
	}

	static void from_json(const json& j, std::optional<T>& opt)
	{
		if (j.is_null())
			opt = std::nullopt;
		else
			opt = j.get<T>();
	}
};
}    // namespace nlohmann
