#pragma once

#ifdef __cplusplus
extern "C" {
#endif

#if defined(_MSC_VER)
//  Microsoft
#define EXPORT __declspec(dllexport)
#define IMPORT __declspec(dllimport)
#elif defined(__GNUC__)
//  GCC
#define EXPORT __attribute__((visibility("default")))
#define IMPORT
#else
//  do nothing and hope for the best?
#define EXPORT
#define IMPORT
#pragma warning Unknown dynamic link import / export semantics.
#endif

extern EXPORT const char* GenCertPassword();
extern EXPORT const char* GenCertPasswordForHostname(const char* overrideHostname);

#ifdef __cplusplus
}
#endif