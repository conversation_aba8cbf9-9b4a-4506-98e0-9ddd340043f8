#pragma once

#include <vector>

#include "TGameButton.h"
#include "gui/layout.hpp"
#include "gui/widgets/TModernButton.h"
#include "gui/widgets/button.hpp"


BETTER_ENUM(EGameFilters, uint8_t, AllGames, Live, Automated, Virtual)

DECLARE_SIMPLE_MULTICAST_DELEGATE(FOnPagesCalculated, int, int)

class TLobbyIconPositioner : public LayoutContainer
{
   public:
	TLobbyIconPositioner(Container* pParent);

	void AddCategoryButton(const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf, const igp::FPlatformEnvironment& env);
	void RepositionLobbyIcons();
	void SetGameFilter(EGameFilters filter);
	void SetGameLobbyCategory(ELobbyGameCategory filter);

	void GetVisibleGameFilters(std::set<EGameRNGType>& rngType, std::set<ELobbyGameCategory>& gameType) const;

   public:
	FOnPagesCalculated OnPagesCalculated;

   private:
	TGameButton* findGameButton(const igp::PlatformGameConfiguration& conf) const;
	static size_t getIconPositions(const Vector2D& inParentSize, std::vector<TGameButton*> buttons, std::vector<Vector2D>& pos);
	std::vector<TGameButton*> GetFilteredVectorOfButtons();
	std::vector<TGameButton*> GetPredeterminedOrderOfButtons();

   private:
	std::vector<TGameButton*> mButtons;
	EGameFilters mGameFilters = EGameFilters::AllGames;
	ELobbyGameCategory mMenuCategory = ELobbyGameCategory::AllGames;
};



class TLobbyPages : public LayoutContainer
{
   public:
	TLobbyPages();

	void AddAndGetCategoryButton(const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf, const igp::FPlatformEnvironment& env) const;

	void RedrawContent();
	void SetSelectedCategory(ELobbyGameCategory gameCategory);
	void GoToPage(size_t pageIdx);
	const std::set<ELobbyGameCategory> GetAndSetVisibleFiltersAndCategories();
	void SetChangeGameMode(bool changeGameMode) const;
	void SetCompactView(bool) const;

   public:
	Button* mCloseButton = NULL;

   private:
	GridLayout* mLayout = NULL;
	LayoutContainer* mFilters = NULL;
	Container* mPageContent = NULL;
	LayoutContainer* mPageCounter = NULL;
	LayoutContainer* mChangeGameContainer = NULL;
	EGameFilters mGameFilters = EGameFilters::AllGames;
	TLobbyIconPositioner* mIconPositioner = NULL;

	StackLayout* filterStack = NULL;

	Label* lPageCounter = NULL;
	Label* lPageCounterMax = NULL;
	Icon* arrowLeft = NULL;
	Icon* arrowRight = NULL;

	size_t CurrentPageIdx = 0;
	size_t MaxPages = 0;

	std::map<EGameFilters, TModernButton*> mFilterButtons;

   private:
	void UpdateFilters(EGameFilters filter);
	void PositionChangeGameContainer() const;
};
