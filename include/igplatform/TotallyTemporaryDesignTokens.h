#pragma once

#include "gui/color.hpp"
#include "gui/graphics.hpp"
#include "gui/typography.hpp"

namespace tempDesignTokens
{
const Color GoldTextColor = Color::FromRGBInteger(0xFFD1A3);
// const Color GreyTextColor = Color::Gray(191);
const Color GreyTextColor = Color::FromRGBInteger(0xBFBFBF);
const Color UnselectedMenuCategory = Color::FromRGBInteger(0xF9FAFB);
const Color TopInfoBarBackgroundColor = Color::FromRGBInteger(0x16191A);
const Color TopInfoBarBackgroundExpandedColor = Color::FromRGBInteger(0x212628);

const Color GameViewTopBarColor = Color::FromRGBInteger(0x212628);
const Color GameViewTopBarCloseButtonColor = Color::FromRGBInteger(0x16191A);
const Color GameViewOutlineColor = Color::FromRGBInteger(0x414141);

const Color ActiveGameWidgetBaccaratBankerWinColor = Color::FromRGBInteger(0xFF3535);
const Color ActiveGameWidgetBaccaratPlayerWinColor = Color::FromRGBInteger(0x006BFF);
const Color ActiveGameWidgetBaccaratTieWinColor = Color::FromRGBInteger(0x00B376);

const Color ActiveGameWidgetDragonWinColor = Color::FromRGBInteger(0xFF3535);
const Color ActiveGameWidgetTigerWinColor = Color::FromRGBInteger(0xF1CB00);
const Color ActiveGameWidgetTieWinColor = Color::FromRGBInteger(0x00B376);

const Color ActiveGameWidgetRouletteZero = Color::FromRGBInteger(0x00B376);
const Color ActiveGameWidgetRouletteBlack = Color::FromRGBInteger(0xBABABA);
const Color ActiveGameWidgetRouletteRed = Color::FromRGBInteger(0xFF3535);

const Color ActiveGameWidget3HDragonTie = Color::FromRGBInteger(0x00B376);
const Color ActiveGameWidget3HDragonTigerWin = Color::FromRGBInteger(0x006BFF);

const Color GameMenuSettingsToggleButtonColor = Color::FromRGBInteger(0xFFD1A3);

const Color GameTag_RNGName = Color::FromRGBInteger(0x22973F);
const Color GameTag_RNGNameBorder = Color::FromRGBInteger(0x1D5E2E);
const Color GameTag_RNGType = Color::FromRGBInteger(0xC8170D);
const Color GameTag_RNGTypeBorder = Color::FromRGBInteger(0xA5170F);
const Color GameTag_GameName = Color::FromRGBInteger(0xE26E00);
const Color GameTag_GameNameBorder = Color::FromRGBInteger(0xBB4A02);
const Color GameIcon_UnavailableBorder = Color::FromRGBInteger(0xA5170F);

const Color LobbyControlsBackground = Color::FromRGBInteger(0x16191A);
const Color LobbyControlsSelectedFilter = Color::FromRGBInteger(0xFFD1A3);

const Color GameIcon3HeadedDragonGold = Color::FromRGBInteger(0x6E5442);
const Color GameIcon3HeadedDragonBlack = Color::FromRGBInteger(0x2C292B);
const Color GameIcon3HeadedDragonRed = Color::FromRGBInteger(0x561111);

const Color CardGameTieResultColor = Color::FromRGBInteger(0x00B376);
const Color CardGameNaturalResultColor = Color::FromRGBInteger(0xF1CB00);
const Color BaccaratLeftSideResultColor = Color::FromRGBInteger(0x006BFF);
const Color BaccaratRightSideResultColor = Color::FromRGBInteger(0xFF3535);
const Color TigerDragonLeftSideResultColor = Color::FromRGBInteger(0xFF3535);
const Color TigerDragonRightSideResultColor = Color::FromRGBInteger(0xF1CB00);
const Color ThreeHeadedDragonLeftSideResultColor = Color::FromRGBInteger(0xFF3535);
const Color ThreeHeadedDragonRightSideResultColor = Color::FromRGBInteger(0xF1CB00);
const Color MainRoadHistoryPlaceholderDot = Color::FromRGBInteger(0x937557);

const std::string buttonPress = "button_press.wav";

// green
const LinearGradient GameIconRouletteLastResultGreenBackground =
  LinearGradient(180.f, { { .color = Color::FromRGBInteger(0x13845E), .position = 0.f }, { .color = Color::FromRGBInteger(0x075C3F), .position = 1.f } });
const LinearGradient GameIconRouletteLastResultGreenOutlineGradient =
  LinearGradient(180.f, { { .color = Color::FromRGBInteger(0x0F815A), .position = 0.f }, { .color = Color::FromRGBInteger(0x09A06E), .position = 1.f } });
const Outline GameIconRouletteLastResultGreenOutline = Outline(GameIconRouletteLastResultGreenOutlineGradient, 2_px, 0_px, 0_px, {});

// red
const LinearGradient GameIconRouletteLastResultRedBackground =
  LinearGradient(180.f, { { .color = Color::FromRGBInteger(0xFF3535), .position = 0.f }, { .color = Color::FromRGBInteger(0xDB0404), .position = 1.f } });
const LinearGradient GameIconRouletteLastResultRedOutlineGradient =
  LinearGradient(180.f, { { .color = Color::FromRGBInteger(0xFF3535), .position = 0.f }, { .color = Color::FromRGBInteger(0xED1D1D), .position = 1.f } });
const Outline GameIconRouletteLastResultRedOutline = Outline(GameIconRouletteLastResultRedOutlineGradient, 2_px, 0_px, 0_px, {});

// black
const LinearGradient GameIconRouletteLastResultBlackBackground =
  LinearGradient(180.f, { { .color = Color::FromRGBInteger(0x414141), .position = 0.f }, { .color = Color::FromRGBInteger(0x171717), .position = 1.f } });
const LinearGradient GameIconRouletteLastResultBlackOutlineGradient = LinearGradient(180.f, { { .color = Color::FromRGBInteger(0x414141), .position = 0.f },
                                                                                              { .color = Color::FromRGBInteger(0x323232), .position = 55.55f },
                                                                                              { .color = Color::FromRGBInteger(0x2C2C2C), .position = 1.f } });
const Outline GameIconRouletteLastResultBlackOutline = Outline(GameIconRouletteLastResultBlackOutlineGradient, 2_px, 0_px, 0_px, {});

const Outline MainRoadElementOutline = { {}, 1.5_px, 1_rem, 0.75_px, {} };

// warnings
const Color NotificationErrorStringColor = Color::FromRGBInteger(0xD61D0D);
const Color NotificationWarningStringColor = Color::FromRGBInteger(0xC04A00);
const Color NotificationNotificationStringColor = Color::FromRGBInteger(0x475659);
const Color NotificationHintStringColor = Color::FromRGBInteger(0x086CCE);
const Color NotificationSuccessStringColor = Color::FromRGBInteger(0x008006);

const Color NotificationReasonErrorStringColor = Color::FromRGBInteger(0x4C051F);
const Color NotificationReasonWarningStringColor = Color::FromRGBInteger(0x431607);
const Color NotificationReasonNotificationStringColor = Color::FromRGBInteger(0x16191A);
const Color NotificationReasonHintStringColor = Color::FromRGBInteger(0x0A2E47);
const Color NotificationReasonSuccessStringColor = Color::FromRGBInteger(0x0E2510);

const Color NotificationBackgroundColorError = Color::FromRGBInteger(0xFFEBEE);
const Color NotificationBackgroundColorWarning = Color::FromRGBInteger(0xFFF3E0);
const Color NotificationBackgroundColorSuccess = Color::FromRGBInteger(0xE8F5E9);
const Color NotificationBackgroundColorHint = Color::FromRGBInteger(0xE3F2FD);
const Color NotificationBackgroundColorNotification = Color::FromRGBInteger(0xF4F6F7);

const Color NotificationOutlineColorError = Color::FromRGBInteger(0xD61D0D);
const Color NotificationOutlineColorWarning = Color::FromRGBInteger(0xAF5800);
const Color NotificationOutlineColorSuccess = Color::FromRGBInteger(0x008006);
const Color NotificationOutlineColorHint = Color::FromRGBInteger(0x167BDF);
const Color NotificationOutlineColorNotification = Color::FromRGBInteger(0x475659);


const Color LockOutlineColor = Color::FromRGBInteger(0x5D2AD2);
const Color LockTitleColor = Color::FromRGBInteger(0x5D2AD2);
const Color LockBackgroundColor = Color::FromRGBInteger(0xF7F3FF);
const Color LockReasonColor = Color::FromRGBInteger(0x281263);

const Color GameIconInfoBackgroundColor = Black.Fade(0.7);

const Color DropdownListOutlineColor = Color::FromRGBInteger(0x595859);
const Color DropdownGreyColor = Color::FromRGBInteger(0x212628);

constexpr int GameIconHistoryHeight = 82;
constexpr int GameIconInfoHeight = 70;
constexpr int GameIconLogoHeight = 98;

const Dimension RoundedCorner_1rem = 1_rem;
const Dimension RoundedCorner_0 = 0_px;
const Dimension RoundedCorner_3 = 3_px;
const Dimension RoundedCorner_4 = 4_px;
const Dimension RoundedCorner_8 = 8_px;
const Dimension RoundedCorner_12 = 12_px;


/* Used for:
 * Lobby Game icon title when game is featured
 * Lobby Category Title eg: "Roulette Games"
 */
const std::string TitleFont_24 = "global.usedFonts.TitleFont_24";

/* Used for:
 *  Normal (non-featured) game icon title
 */
const std::string TitleFont_20 = "global.usedFonts.TitleFont_20";
/* Used for:
 * Lobby menu Game icon title - left lobby game icon title
 * Active games widget title
 */
const std::string TitleFont_16 = "global.usedFonts.TitleFont_16";
/* Used for:
 * Top info bar currently played game name
 */
const std::string TitleFont_18 = "global.usedFonts.TitleFont_18";

const std::string Inter_400_45 = "global.usedFonts.MainFont_400_45";

/* Used for:
 * Used for most greyed out texts
 */
const std::string MainFont_400_18 = "global.usedFonts.MainFont_400_18";
const std::string MainFont_400_18_NoHeight = "global.usedFonts.MainFont_400_18_noH";

/* Used for:
 * Balance
 * Lobby Menu (left menu) add game button
 * Lobby Menu (left menu) play game button
 * Top info bar currently played game bet/limit info+
 */
const std::string MainFont_400_16 = "global.usedFonts.MainFont_400_16";
const std::string MainFont_400_16_NoHeight = "global.usedFonts.MainFont_400_16_noH";

/* Used for:
 * Menu lobby (left menu) back to lobby arrow text
 * Active game Widget RNG Name
 * Active game Widget bet info
 */
const std::string MainFont_400_14 = "global.usedFonts.MainFont_400_14";

/* Used for:
 * game menu settings
 */
const std::string MainFont_500_16 = "global.usedFonts.MainFont_500_16";
/* Used for:
 * Featured game (big game icon) bet limit / win limit (white text)
 * Active game widget baccarat most recent result text
 */
const std::string MainFont_600_18 = "global.usedFonts.MainFont_600_18";
/* Used for:
 * Active game widget current game status
 * Active game widget bet info
 * Menu at active game widget game counters
 */
const std::string MainFont_600_14 = "global.usedFonts.MainFont_600_14";
/* Used for:
 * Info bar money/balance/credits
 * Cashout
 * Play awaaay
 * Active games widget results that are not the most recent (eg: roulette 12)
 * Notifications texts
 */
const std::string MainFont_600_16 = "global.usedFonts.MainFont_600_16";
/* Used for:
 * Active games widget timer
 */
const std::string MainFont_600_20 = "global.usedFonts.MainFont_600_20";

/* Used for:
 * Notifications
 */
const std::string MainFont_400_20 = "global.usedFonts.MainFont_400_20";
/* Used for:
 * Top bar balance currency display
 */
const std::string MainFont_600_24 = "global.usedFonts.MainFont_600_24";

/* ----- GRADIENTS ----- */

/* Used for:
 * Outlines of game buttons and other buttons like play, add game, etc.
 */
const LinearGradient Golden_Outline_Gradient = LinearGradient(289.02, { { .color = Color::FromRGBInteger(0xD9B088), .position = 0.6814f },
                                                                        { .color = White, .position = 0.7658f },
                                                                        { .color = Color::FromRGBInteger(0xD9B088), .position = 0.9172f },
                                                                        { .color = Color::FromRGBInteger(0xD9B088), .position = 1.069f },
                                                                        { .color = Color::FromRGBInteger(0xD9B088).Fade(0.561151), .position = 1.1181f },
                                                                        { .color = Color::FromRGBInteger(0xD9B088).Fade(0.f), .position = 1.1415f } })
                                                 .expandBy(1.1415f)
                                                 .moveEndTo({});

/* Used for:
 * The menu category buttons, has a transparent beginning
 */
const LinearGradient Golden_Outline_Gradient_Transparent_LeftToRight =
  LinearGradient(272.08, { { .color = Color::FromRGBInteger(0xD9B088).Fade(0.f), .position = 0.2127f },
                           { .color = Color::FromRGBInteger(0xD9B088), .position = 0.5447f },
                           { .color = Color::FromRGBInteger(0xD9B088), .position = 0.6477f },
                           { .color = White, .position = 0.7643f },
                           { .color = Color::FromRGBInteger(0xD9B088), .position = 0.9733f },
                           { .color = Color::FromRGBInteger(0xD9B088), .position = 1.1831f },
                           { .color = Color::FromRGBInteger(0xD9B088).Fade(0.561151), .position = 1.2509f },
                           { .color = Color::FromRGBInteger(0xD9B088).Fade(0.f), .position = 1.2832f } })
    // .expandBy(1.2832f) because designers are crazy, we have to adjust this differently than it should be
    .expandBy(1.08362f)
    .moveEndTo({});

/* Used for:
 * Buttons
 */
const LinearGradient Buttons_Gradient = LinearGradient(
  Vector2D(0, 0), Vector2D(0, 1), { { .color = Color::FromRGBInteger(0x2C3335), .position = 0.f }, { .color = Color::FromRGBInteger(0x0B0D0D), .position = 1.f } });
/*Used for:
 * buttons on pressed state
 */
const LinearGradient Buttons_Gradient_Pressed = LinearGradient(
  Vector2D(0, 0), Vector2D(0, 1), { { .color = Color::FromRGBInteger(0x0B0D0D), .position = 0.f }, { .color = Color::FromRGBInteger(0x2C3335), .position = 1.f } });

/* Used for:
 * Active games widget bets open phase
 */
const LinearGradient Bets_Open_Gradient =
  LinearGradient(Vector2D(0, 0), Vector2D(1, 0),
                 { { .color = Color::FromRGBInteger(0x146C60), .position = 0.f }, { .color = Color::FromRGBInteger(0x146C60).Fade(0.f), .position = 0.6f } });
/* Used for:
 * Active games widget bets closed phase
 */
const LinearGradient Bets_Close_Gradient = LinearGradient(Vector2D(0, 0), Vector2D(1, 0),
                                                          { { .color = Color::FromRGBInteger(0x146C60).Fade(0.f), .position = 0.f },
                                                            { .color = Color::FromRGBInteger(0x146C60), .position = 0.3987f },
                                                            { .color = Color::FromRGBInteger(0x146C60), .position = 0.3988f },
                                                            { .color = Color::FromRGBInteger(0x146C60).Fade(0.5f), .position = 0.6031f },
                                                            { .color = Color::FromRGBInteger(0x146C60).Fade(0.f), .position = 1.f } });
/* Used for:
 * Active games widget waiting for result phase
 */
const LinearGradient Waiting_Result_Gradient = LinearGradient(Vector2D(0, 0), Vector2D(1, 0),
                                                              { { .color = Color::FromRGBInteger(0x146C60).Fade(0.f), .position = 0.f },
                                                                { .color = Color::FromRGBInteger(0x146C60), .position = 0.3988f },
                                                                { .color = Color::FromRGBInteger(0x146C60).Fade(0.75f), .position = 0.6031f },
                                                                { .color = Color::FromRGBInteger(0x146C60).Fade(0.5f), .position = 1.f } });
/* Used for:
 * Active games widget game has no phase
 */
const LinearGradient Unknown_Phase_Gradient =
  LinearGradient(Vector2D(0, 0), Vector2D(1, 0), { { .color = Color::Gray(60), .position = 0.f }, { .color = Color::Gray(60).Fade(0.f), .position = 1.f } });
/* Used for:
 * Active games widget repeat button
 */
const LinearGradient RepeatButton_Gradient = LinearGradient(
  Vector2D(0, 0), Vector2D(0, 1), { { .color = Color::FromRGBInteger(0x10564D), .position = 0.f }, { .color = Color::FromRGBInteger(0x041613), .position = 1.f } });



const Outline GameIconBorderOutline = Outline(Golden_Outline_Gradient, 2_px, RoundedCorner_12, 1_px, Shadow(Black.Fade(0.25f), {}, 8_px, 12_px, {}, false));
const Outline GameIconBorderOutlineSelected = Outline(Golden_Outline_Gradient, 4_px, RoundedCorner_12, 1_px, Shadow(Black.Fade(0.25f), {}, 8_px, 12_px, {}, false));


const LinearGradient ActiveGamesWidgetsHistory = LinearGradient(Vector2D(0, 0), Vector2D(1, 0),
                                                                { { .color = Color::FromRGBInteger(0x000000).Fade(0), .position = 0.f },
                                                                  { .color = Color::FromRGBInteger(0x000000).Fade(1.f), .position = 0.2f },
                                                                  { .color = Color::FromRGBInteger(0x000000).Fade(1.f), .position = 0.8f },
                                                                  { .color = Color::FromRGBInteger(0x000000).Fade(0.f), .position = 1.f } });

const Transition DefaultFadeTransition = { 0.35s };


const std::string GlobalFont_10 = "global.usedFonts.GlobalFont10";
const std::string GlobalFont_11 = "global.usedFonts.GlobalFont11";
const std::string GlobalFont_12 = "global.usedFonts.GlobalFont12";
const std::string GlobalFont_14 = "global.usedFonts.GlobalFont14";
const std::string GlobalFont_15 = "global.usedFonts.GlobalFont15";
const std::string GlobalFont_16 = "global.usedFonts.GlobalFont16";
const std::string GlobalFont_18 = "global.usedFonts.GlobalFont18";
const std::string GlobalFont_20 = "global.usedFonts.GlobalFont20";
const std::string GlobalFont_22 = "global.usedFonts.GlobalFont22";
const std::string GlobalFont_24 = "global.usedFonts.GlobalFont24";
const std::string GlobalFont_26 = "global.usedFonts.GlobalFont26";
const std::string GlobalFont_28 = "global.usedFonts.GlobalFont28";
const std::string GlobalFont_30 = "global.usedFonts.GlobalFont30";
const std::string GlobalFont_32 = "global.usedFonts.GlobalFont32";
const std::string GlobalFont_36 = "global.usedFonts.GlobalFont36";
const std::string GlobalFont_40 = "global.usedFonts.GlobalFont40";
const std::string GlobalFont_52 = "global.usedFonts.GlobalFont52";
const std::string GlobalFont_62 = "global.usedFonts.GlobalFont62";
const std::string GlobalFont_72 = "global.usedFonts.GlobalFont72";
const std::string GlobalFont_120 = "global.usedFonts.GlobalFont120";

}    // namespace tempDesignTokens
