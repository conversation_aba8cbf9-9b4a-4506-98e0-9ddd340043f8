/*
 * Copyright (c) 2014, <PERSON>. All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *     * Redistributions of source code must retain the above copyright
 *       notice, this list of conditions and the following disclaimer.
 *     * Redistributions in binary form must reproduce the above copyright
 *       notice, this list of conditions and the following disclaimer in the
 *       documentation and/or other materials provided with the distribution.
 *     * Neither the name of the WebSocket++ Project nor the
 *       names of its contributors may be used to endorse or promote products
 *       derived from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL PETER THORSON BE LIABLE FOR ANY
 * DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CO<PERSON><PERSON>QUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 * LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
 * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
 * (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 * SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#pragma once

#include <websocketpp/common/system_error.hpp>

#include "web/ImaxaEndpoint.h"

namespace web::websockets
{

class imaxa_server_interface
{
   public:
	virtual ~imaxa_server_interface() {}

	virtual void start_accept(lib::error_code& ec) = 0;
};

/// Server endpoint role based on the given config
/**
 *
 */
template <typename config>
class imaxa_server_endpoint : public imaxa_endpoint_impl<config>, public imaxa_server_interface
{
   public:
	/// Type of this endpoint
	typedef imaxa_server_endpoint<config> type;

	/// Type of the endpoint concurrency component
	typedef typename config::concurrency_type concurrency_type;
	/// Type of the endpoint transport component
	typedef typename config::transport_type transport_type;

	/// Type of the connections this server will create
	typedef imaxa_connection_impl<config> connection_type;
	/// Type of a shared pointer to the connections this server will create
	typedef std::shared_ptr<connection_type> connection_ptr;

	/// Type of the connection transport component
	typedef typename transport_type::transport_con_type transport_con_type;
	/// Type of a shared pointer to the connection transport component
	typedef typename transport_con_type::ptr transport_con_ptr;

	/// Type of the endpoint component of this server
	typedef imaxa_endpoint_impl<config> endpoint_type;

	/// The type and signature of the callback passed to the start_accept method
	typedef lib::function<void(lib::error_code const&, lib::error_code const&)> accept_loop_handler;

	friend class imaxa_connection_impl<config>;

	explicit imaxa_server_endpoint();

	/// Destructor
	~imaxa_server_endpoint();

#ifdef _WEBSOCKETPP_DEFAULT_DELETE_FUNCTIONS_
	// no copy constructor because endpoints are not copyable
	imaxa_server_endpoint(imaxa_server_endpoint<config>&) = delete;

	// no copy assignment operator because endpoints are not copyable
	imaxa_server_endpoint<config>& operator=(imaxa_server_endpoint<config> const&) = delete;
#endif    // _WEBSOCKETPP_DEFAULT_DELETE_FUNCTIONS_

#ifdef _WEBSOCKETPP_MOVE_SEMANTICS_
	/// Move constructor
	imaxa_server_endpoint(imaxa_server_endpoint<config>&& o);

#ifdef _WEBSOCKETPP_DEFAULT_DELETE_FUNCTIONS_
	// no move assignment operator because of const member variables
	imaxa_server_endpoint<config>& operator=(imaxa_server_endpoint<config>&&) = delete;
#endif    // _WEBSOCKETPP_DEFAULT_DELETE_FUNCTIONS_

#endif    // _WEBSOCKETPP_MOVE_SEMANTICS_

	/// Create and initialize a new connection
	/**
	 * The connection will be initialized and ready to begin. Call its start()
	 * method to begin the processing loop.
	 *
	 * Note: The connection must either be started or terminated using
	 * connection::terminate in order to avoid memory leaks.
	 *
	 * @since 0.9.0
	 *
	 * @param [out] ec A status code that indicates why the failure occurred
	 *        if the returned pointer is blank.
	 * @return A pointer to the new connection.
	 */
	connection_ptr get_connection(lib::error_code& ec);

	/// Starts the server's async connection acceptance loop (exception free)
	/**
	 * Initiates the server connection acceptance loop. Must be called after
	 * listen. This method will have no effect until the underlying io_context
	 * starts running. It may be called after the io_context is already running.
	 *
	 * Refer to documentation for the transport policy you are using for
	 * instructions on how to stop this acceptance loop.
	 *
	 * Error handling:
	 * start_accept will return an error via the `ec` parameter if there is a
	 * problem starting the accept loop. Once successfully started the loop will
	 * continue to renew itself after each connection. This method has no way of
	 * delivering that happen after the loop is started. Use
	 * `start_accept(accept_loop_handler)` instead to get full error information
	 * no matter when the async loop ends.
	 *
	 * @deprecated use `start_accept(accept_loop_handler) instead
	 *
	 * @param [out] ec A status code indicating an error, if any.
	 */
	virtual void start_accept(lib::error_code& ec) override;

	/// Starts the server's async connection acceptance loop (exception free)
	/**
	 * Initiates the server connection acceptance loop. Requires a transport
	 * policy that supports an asyncronous listen+accept loop. Must be called
	 * while the endpoint is listening (or start_accept will return immediately
	 * with an error that the server is not listening).
	 *
	 * Consult the documentation for the underlying transport for information
	 * about exactly when this code will start running, when in the transport
	 * event loop it makes sense to call it, and for instructions on how to
	 * stop this acceptance loop.
	 *
	 * Error handling:
	 * start_accept will attempt to start an asyncronous acceptance loop that
	 * accepts a connection and then re-issues a new accept command. If this loop
	 * ends or fails for any reason (including immediately) the `completion_handler`
	 * will be called with two status codes. The first is the library level status
	 * code the second is the underlying transport status code (if any).
	 *
	 * @since 0.9.0
	 *
	 * @param completion_handler A handler function to be called when the async
	 *        accept loop ends.
	 */
	void start_accept(accept_loop_handler completion_handler);

#ifndef _WEBSOCKETPP_NO_EXCEPTIONS_
	/// Starts the server's async connection acceptance loop (exception)
	/**
	 * Initiates the server connection acceptance loop. Requires a transport
	 * policy that supports an asyncronous listen+accept loop. Must be called
	 * while the endpoint is listening (or start_accept will return immediately
	 * with an error that the server is not listening).
	 *
	 * Consult the documentation for the underlying transport for information
	 * about exactly when this code will start running, when in the transport
	 * event loop it makes sense to call it, and for instructions on how to
	 * stop this acceptance loop.
	 *
	 * Error handling:
	 * start_accept will throw an exception if there is a problem starting the
	 * accept loop. Once successfully started the loop will continue to renew
	 * itself after each connection. This method has no way of delivering that
	 * happen after the loop is started. Use `start_accept(accept_loop_handler)`
	 * instead to get full error information no matter when the async loop ends.
	 *
	 * @deprecated use `start_accept(accept_loop_handler)` instead
	 *
	 * @exception websocketpp::exception If the accept loop fails to be set up.
	 */
	void start_accept();
#endif    // _WEBSOCKETPP_NO_EXCEPTIONS_

   private:
	/// Handler callback for start_accept (deprecated)
	void handle_accept_legacy(connection_ptr con, lib::error_code const& ec);

	/// Handler callback for start_accept
	void handle_accept(connection_ptr con, accept_loop_handler completion_handler, lib::error_code const& tec);
};

}    // namespace web::websockets
