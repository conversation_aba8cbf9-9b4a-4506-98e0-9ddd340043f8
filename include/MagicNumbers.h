/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include <cstdint>

#include "Enums.h"
#include "terminal/drv/money/MoneyAcceptorDriverEnums.h"

namespace magic
{
/** use this macro, to test MAGIC_CODES... */
/** magic_test - returns true, if the code matches magic_code, else false */
constexpr bool magic_test(long code, long magic_code)
{
	return (code & magic_code) == magic_code;
}

/** Common defines */
const int NOT_CONNECTED = 0;

/** se uporabljajo v event.user.data1 */
constexpr int DOOR_ID(uint8_t idx)
{
	return 0x08 + 8 * idx;
}
const int DOOR_DEPOSIT_BOX = 0x08;
const int DOOR_BIG = 0x10;
const int DOOR_SMALL = 0x18;
const int DOOR_LOGIC = 0x20;
const int BTN_CLEAR = 0x28;
const int BTN_REPEAT = 0x30;
const int BTN_UNDO = 0x38;
const int BTN_START = 0x40;
const int BTN_JUGADA = 0x48;
const int BTN_APUESTA = 0x50;
const int BTN_RIESGO = 0x58;
const int BTN_COBRAR = 0x60;
const int BTN_VOID = 0x68;

const uint8_t MAX_HARDWARE_BUTTONS = 10;

/** se uporablja v IGPLatform - zaradi kompatibilnosti z roulette smo dali iste ID-je*/
inline constexpr long HARDWARE_BTN_CODE(uint8_t idx)
{
	return 0x70 + 8 * idx;
}
inline constexpr uint8_t HARDWARE_BTN_ID_FROM_CODE(long code)
{
	return ((code & 0xFFFF) - 0x70) >> 3;
}
inline constexpr bool IS_HARDWARE_BUTTON(long code)
{
	code = (code & 0xFFFF);
	if (code < 0x70)
		return false;
	return ((code - 0x70) >> 3) < 10;
}
/*const int BTN_SERVICE = 0x70;
const int BTN_PAYOUT = 0x78;
const int BTN_INFO = 0x80;
const int BTN_GAME_SELECT = 0x88;
const int BTN_GAMBLE_RED = 0x90;
const int BTN_GAMBLE_BLACK = 0x98;
const int BTN_SPIN_TAKE = 0x100;
const int BTN_AUTO_SPIN = 0x108;
const int BTN_MAX_BET = 0x110;
const int BTN_TEN = 0x118;*/
const int PIR_SENSOR = 0x120;

const int HARDWARE_BUTTON_SDL_EVENT_TYPE = 246;

/* Button light blink presets for IGPLATFORMV6 */
// these will not work on MyCoins!!!
enum ELightState
{
	LIGHT_OFF_BLINK_OFF = 0,
	LIGHT_AUTO = 0,    // je isto kot link_off, ker se v TStyleButton uposteva LIGHT_OFF_BLINK_OFF tako da se posilja glede na IsEnabled()
	LIGHT_ON = 1,
	LIGHT_BLINK200MS_A = 2,
	LIGHT_BLINK200MS_B = 3,
	LIGHT_BLINK500MS_A = 4,
	LIGHT_BLINK500MS_B = 5,
	LIGHT_BLINK800MS_A = 6,
	LIGHT_BLINK800MS_B = 7,
	LIGHT_BLINK1200MS_A = 8,
	LIGHT_BLINK1200MS_B = 9,
	// higher values will blink async with value as delay in centiseconds
	LIGHT_BLINK_ASYNC_100ms = 10,
	LIGHT_BLINK_ASYNC_200ms = 20,
	LIGHT_BLINK_ASYNC_400ms = 40,
	LIGHT_BLINK_ASYNC_500ms = 50,
	LIGHT_BLINK_ASYNC_800ms = 80
};

inline uint32_t LIGHT_ACTION_ID(int BTN_ID, int LIGHT_ID)
{
	return (uint32_t)((BTN_ID & 0xFFFF) | ((LIGHT_ID & 0xFF) << 16));
}

/** SDL MESSAGE CODES are combined with device Id-s */
const int DEVICE_COMMUNICATION_OK = 0x00;
const int DEVICE_COMMUNICATION_ERROR = 0x01;

/** Touch Screens */
const int TOUCH_SCREEN = 0x010000;
/** events */
constexpr int TOUCH_CALIB_TOUCH_POINT(int i)
{
	return TOUCH_SCREEN | (2 << i);
}
const int TOUCH_CALIB_TOUCH_PROGRESS = TOUCH_SCREEN | 0x20;
const int TOUCH_CALIB_SUCCESS = TOUCH_SCREEN | 0x40;
const int TOUCH_CALIB_ERROR = TOUCH_SCREEN | 0x80;
const int TOUCH_OPERATION_MODE = TOUCH_SCREEN | 0x100;
const int TOUCH_INITIALIZATION_MODE = TOUCH_SCREEN | 0x200;
const int TOUCH_COMMAND_OK = TOUCH_SCREEN | 0x400;
const int TOUCH_COMMAND_ERROR = TOUCH_SCREEN | 0x800;

/** devices */
const int MICROTOUCH = (TOUCH_SCREEN | 0x1000);
const int ELOTOUCH = (TOUCH_SCREEN | 0x2000);
const int GALAXTOUCH = (TOUCH_SCREEN | 0x4000);
const int PSAUXTOUCH = (TOUCH_SCREEN | 0x8000);

/** RouletteKeyboards */
const int ROULETTE_KEYBOARD = 0x020000;
/** events */
// TODO: KEY_DOWN MORA ZACET Z 0x02,.., ker 0x01 je commerror !! - zamenjaj in poskusi če vse dela kot je treba
const int KEY_DOWN = (ROULETTE_KEYBOARD | 0x01);
const int KEY_UP = (ROULETTE_KEYBOARD | 0x02);
// #define LIGHT_ON KEY_UP
// #define LIGHT_OFF KEY_DOWN
const int DOOR_OPEN = KEY_DOWN;
const int DOOR_CLOSE = KEY_UP;



/** devices */
const int TKP1 = (ROULETTE_KEYBOARD | 0x1000);
const int KEYBOARD_3 = (ROULETTE_KEYBOARD | 0x2000);
const int AGT_KEYBOARD = (ROULETTE_KEYBOARD | 0x4000);
const int AIK_KEYBOARD = (ROULETTE_KEYBOARD | 0x8000);
const int ELBET_KEYBOARD = (ROULETTE_KEYBOARD | 0x800);
const int AIKV2_KEYBOARD = (ROULETTE_KEYBOARD | 0x400);


/** Bill acceptors */
const int BILL_ACCEPTOR = 0x040000;

/** events */
const int BILL_STATUS_CHANGE = (BILL_ACCEPTOR | 0x01);
const int BILL_ACCEPTED = (BILL_ACCEPTOR | 0x02);
const int BILL_STACKED = (BILL_ACCEPTOR | 0x04);
const int TIMEOUT_BILL_RETURNED = (BILL_ACCEPTOR | 0x80);
const int POWER_WITH_BILL_IN_ACCEPTOR = (BILL_ACCEPTOR | 0x100);
const int POWER_WITH_BILL_IN_STACKER = (BILL_ACCEPTOR | 0x200);
// const int STACKER_CLOSE = (BILL_ACCEPTOR | 0x400);
const int TICKET_INSTEAD_OF_BILL = (BILL_ACCEPTOR | 0x800);
const int BILL_ASSIGNMENT = (BILL_ACCEPTOR | 0x10000);

/** device ID-s */
const int JCM_ID0003 = (BILL_ACCEPTOR | 0x1000);
const int SSP_PROTOCOL = (BILL_ACCEPTOR | 0x2000);
// #define BILL_ON_KEYBOARD_3 (BILL_ACCEPTOR | 0x4000)
const int MEI_PROTOCOL = (BILL_ACCEPTOR | 0x4000);
const int ICT_PROTOCOL = (BILL_ACCEPTOR | 0x8000);
const int BSCP_PROTOCOL = (BILL_ACCEPTOR | 0x1000000);    // od 0x1000000 do 0x80000000 je range za tipe naprav
// sem že rezerviral, ker ga bo treba kmalu narest
const int CCTALK_BILL_OVER_MYCOINS1 = (BILL_ACCEPTOR | 0x2000000); /*poskusil bom narediti brez tega - ce je slo, brisi*/



/** Coin acceptors */
const int COIN_ACCEPTOR = 0x080000;
// const int COMMUNICATION_ERROR = (COIN_ACCEPTOR | (1 << (ECoinAcceptorErrors::COMMUNICATION_ERROR)));
const int COIN_STATUS_CHANGE = (COIN_ACCEPTOR | 0x01);
const int COIN_ACCEPTED = (COIN_ACCEPTOR | 0x02);
const int HOPER_DONE = (COIN_ACCEPTOR | 0x04);
// #define SWITCH_EVENT (COIN_ACCEPTOR|0x08) //je definiran pri IO_BOARD
// const int HOPER_EMPTY = (COIN_ACCEPTOR | 0x10);
const int HOPER_PAY_ACK = (COIN_ACCEPTOR | 0x20);
const int HOPER_COIN_OUT = (COIN_ACCEPTOR | 0x40);
const int EEPROM_DISCONNECT = (COIN_ACCEPTOR | 0x80);
const int EEPROM_DATA_READ_COMPLETE = (COIN_ACCEPTOR | 0x100);
const int POWER_RESET = (COIN_ACCEPTOR | 0x200);
const int EEPROM_DATA_READ_ERROR = (COIN_ACCEPTOR | 0x400);
const int CCTALK_DEVICE_STATUS = (COIN_ACCEPTOR | 0x800);

const int MYCOINS1 = (COIN_ACCEPTOR | 0x1000);
const int COINS_ON_KEYBOARD_3 = (COIN_ACCEPTOR | 0x2000);



/** User authentification devices */
const int USER_ID_DEVICE = 0x100000;
/** events */
const int ADMIN_KEY_IN = (USER_ID_DEVICE | 0x02);
const int ADMIN_KEY_OUT = (USER_ID_DEVICE | 0x04);

/** devices ID */
const int DS2401_OVER_RS232 = (USER_ID_DEVICE | 0x1000);
const int DS2401_ON_KEYBOARD = (USER_ID_DEVICE | 0x2000);
const int ACORN_RFID = (USER_ID_DEVICE | 0x4000);
const int BSTN_RFID = (USER_ID_DEVICE | 0x8000);

/** Online systems */
const int ONLINE_SYSTEM = 0x200000;
/** events */

/** devices ID */
// compat,ker je bila napaka v starejših verijah je bil ONLINE_SYSTEM 0x100000
const int SAS_6_0_2_COMPAT = (0x100000 | 0x1000);
const int SAS_6_0_2 = (ONLINE_SYSTEM | 0x1000);
const int SAS_6_0_3 = (ONLINE_SYSTEM | 0x2000);



/** Ticket Print devices */
const int TICKET_PRINTER = 0x400000;
/** events */

const int TICKET_STATUS_CHANGE = (TICKET_PRINTER | 0x01);
const int TICKET_OPERATING = (TICKET_PRINTER | 0x02);
const int TICKET_RESET = (TICKET_PRINTER | 0x04);
const int TICKET_PRINTER_ERROR = (TICKET_PRINTER | 0x80);
const int TICKET_PRINTING = (TICKET_PRINTER | 0x10);
const int TICKET_TICKET_LOW = (TICKET_PRINTER | 0x20);
const int TICKET_UPLOADING_GRAPHICS = (TICKET_PRINTER | 0x40);

/** devices ID */
const int ITHACA950 = (TICKET_PRINTER | 0x1000);
const int FL_GEN2 = (TICKET_PRINTER | 0x2000);
const int FL_GEN5 = (TICKET_PRINTER | 0x4000);

/** IO Boards */
const int IOBOARD = 0x800000;
/** events */
const int OPEN_START = (IOBOARD | 0x02);
const int CLOSE_STOP = (IOBOARD | 0x04);
const int DISCONNECT = (IOBOARD | 0x08);
const int CONNECT = (IOBOARD | 0x10);

const int MECHANICAL_COUNTERS_EVENT = (IOBOARD | 0x20);
const int SWITCH_EVENT = (IOBOARD | 0x40);

/** devices ID */
const int CNTSWBAT = (IOBOARD | 0x1000);
const int IGPLATFORMV6 = (IOBOARD | 0x2000);

};    // namespace magic