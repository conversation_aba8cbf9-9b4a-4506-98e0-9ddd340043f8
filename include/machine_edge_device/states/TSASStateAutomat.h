/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#ifndef TStateAutomatH
#define TStateAutomatH

#define STATE_HISTORY_COUNT 100

#include <list>

#include "TApplication.h"

class TSAState;

BETTER_ENUM(OnExecuteStatus, uint8_t, HANDLED, UNRECOGNIZED, IGNORED, WAITING, FAILED, ERROR)

/**
    <AUTHOR> <<EMAIL>>
*/
class TStateAutomat : public rtfwk_sdl2::TAppModule, public Lockable<>
{
   public:
	TStateAutomat();
	virtual ~TStateAutomat();

	typedef void* (*TpSAFunction)(TStateAutomat* pThis, void* pStateData);

	/// to lahko derived class implementira za spreminjanje intervala
	void SetPollTime(uint32_t PollTimeMs) {}

	/**ob klicu, se pokliče trenutno aktivni state v SA-ju. Funkcija senči TAppModule::Poll() */

	void LogicLoop();


	int SetNext(const std::shared_ptr<TSAState>& pNextState);
	int ForceNext(const std::shared_ptr<TSAState>& pNextState, bool afterCurrent);
	int Back();
	int Forward();
	int GoNext();
	std::list<std::shared_ptr<TSAState>>::iterator FindInsertPosition(std::list<std::shared_ptr<TSAState>>::iterator it, const std::shared_ptr<TSAState>& pNextState);

	std::shared_ptr<TSAState> GetPreviousState();
	std::shared_ptr<TSAState> GetCurrentState();
	std::shared_ptr<TSAState> GetNextState();

	const uint8_t STATE_HISTORY_SIZE = 3;

	void RemovePreviousStatesButKeep(size_t numToKeep);

	std::list<std::shared_ptr<TSAState>> StateQueue;    // State queue
	std::list<std::shared_ptr<TSAState>>::iterator CurrentState;    // Iterator to the current state

	void CleanStateQueue();
	// Function to remove all elements of a specific type from StateQueue
	template <typename T>
	void RemoveInstancesOfType()
	{
		ScopedLock lock(this);
		for (auto it = StateQueue.begin(); it != StateQueue.end();)
		{
			if (std::dynamic_pointer_cast<T>(*it))
			{
				it = StateQueue.erase(it);    // erase возвращает итератор на следующий элемент после удаленного
			}
			else
			{
				++it;
			}
		}
	}
	// Function to count all elements of a specific type from StateQueue, with an option to ignore a specific state
	template <typename T>
	uint32_t CountInstancesOfType(const std::shared_ptr<TSAState> stateToIgnore = nullptr) const
	{
		uint32_t count = 0;
		uint32_t index = 0;
		ScopedLock lock(this);
		for (auto it = StateQueue.begin(); it != StateQueue.end(); ++it, ++index)
		{
			if (stateToIgnore && stateToIgnore == *it)    // Skip the specified state if requested
			{
				continue;
			}

			// Uncomment the line below to print the current element's index and type
			// printf("Index: %u, Type: %s\n", index, typeid(**it).name());

			if (std::dynamic_pointer_cast<T>(*it))
			{
				count++;
			}
		}
		return count;
	}

   protected:
	/// eventi za derived class

	/// the should make sure it is thread safe!!!
	/// fukcije morajo zagotoviti, da so threade safe, ker TStateAutomat je...

	/// BeforePoll se poklice pred vsakim pollom SA-ja. če funkcija vrne <>0 potem se ne bo klical NextState() - PAZI MORAS JO NAPISATI THREAD SAFE
	int BeforePoll() { return 0; };
	/// Event ki se prozi po vsakem pollu - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	virtual void AfterPoll() { return; };
	/// Klice se, predno spremenimo NextState - ob klicu SetNext() - če funkcija vrne <>0 potem se NextState ne bo sprejet - PAZI MORAS JO NAPISATI THREAD SAFE
	int BeforeStateChange() { return 0; };
	/// Event se prozi po vsaki spremembi state-a - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	void AfterStateChange() { return; };
	/// prozi se ob klicu funkcije Back(),  če vrne <>0 se Back() ne upošteva - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	int OnBack() { return 0; };
	/// prozi se ob klicu funkcije Forward(), če vrne <>0 se Forward() ne upošteva - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	int OnForward() { return 0; };
	/// Klice se ob SetNext(), če vrne <>0 se SetNext ne upošteva - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	int BeforeSetNext() { return 0; };
	/// Event se prozi po uspešni spremembi state-a - PAZI MOGOCE JO MORAS JO NAPISATI THREAD SAFE
	void AfterSetNext() { return; };
};
#endif    //__T_GAME_SA_H__
