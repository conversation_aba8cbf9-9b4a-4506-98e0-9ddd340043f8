//---------------------------------------------------------------------------

#ifndef TSlotMachineSAS_ComSA_AFTH
#define TSlotMachineSAS_ComSA_AFTH

#include "TSAState.h"
BETTER_ENUM(ELockCode, uint8_t, REQUEST_LOCK = 0x00, CANCEL_LOCK = 0x80, INTERROGATE_STATUS = 0xFF);

// #define ADMIRAL_LEGACY    // using right now to make possible to diacover <PERSON> SLot
//---------------------------------------------------------------------------
class TSA_AFT_RegisterGamingMachine1 : public TSAState
{
   public:
	TSA_AFT_RegisterGamingMachine1(TStateAutomat* pSA) : TSAState(pSA, EStatePriority::AFT) {};
	OnExecuteStatus OnExecute() override;
	int OnEnter() override
	{
		SetStateID(1, "####%d:AFT_RegisterGamingMachine SEND", SLOT->Address);
		return 0;
	};
	OnEventStatus OnEvent(std::shared_ptr<TSASMessage> message) override;
};
//---------------------------------------------------------------------------
class TSA_AFT_QueryLockAndBalances1 : public TSAState
{
   public:
	TSA_AFT_QueryLockAndBalances1(TStateAutomat* pSA, const ELockCode lockCode, const ETransferCondition transferCondition, const uint16_t lockTimeout) :
	    TSAState(pSA, EStatePriority::DISCOVERY), mLockCode(lockCode), mLockTimeout(lockTimeout), mTransferCondition(transferCondition)
	{
		if (lockTimeout > 9999)
			mLockTimeout = 9999;
	};

	OnExecuteStatus OnExecute() override;
	int OnEnter() override
	{
		SLOT->SetName("TSA_QueryLockAndBalances1 Slot " + std::to_string(SLOT->Address));
		SetStateID(1, "####%d:QueryLockAndBalances SEND", SLOT->Address);
		return 0;
	};
	OnEventStatus OnEvent(std::shared_ptr<TSASMessage> message) override;

   protected:
	uint8_t mLockCode;
	uint16_t mLockTimeout;
	ETransferCondition mTransferCondition;
};
//---------------------------------------------------------------------------
class TSA_AFT_TransferFunds : public TSAState
{
   public:
	TSA_AFT_TransferFunds(TStateAutomat* pSA, const std::string& transactionId, const uint64_t cashableInCents, const uint64_t restrictedInCents,
	                      const uint64_t nonrestrictedInCents, const EAFTTransferType transferType, const EAFTTransferCode transferCode, const uint8_t transferFlags,
	                      const std::string& expiration) :
	    TSAState(pSA, EStatePriority::AFT), mCashableInCents(cashableInCents), mRestrictedInCents(restrictedInCents), mNonRestrictedInCents(nonrestrictedInCents),
	    mTransferType(transferType), mTransferCode(transferCode), mTransactionId(transactionId), mTransferFlags(transferFlags), mExpiration(expiration) {};

	OnExecuteStatus OnExecute() override;

	uint64_t mCashableInCents;
	uint64_t mRestrictedInCents;
	uint64_t mNonRestrictedInCents;
	EAFTTransferType mTransferType;
	EAFTTransferCode mTransferCode;
	std::string mTransactionId;
	uint8_t mTransferFlags;
	std::string mExpiration;

	int OnEnter() override
	{
		SetStateID(1, "####%d:AFT_TransferFunds", SLOT->Address);
		return 0;
	};
	OnEventStatus OnEvent(std::shared_ptr<TSASMessage> message) override;
};

class TSA_AFT_TransferFundsInterrogation : public TSAState
{
   public:
	TSA_AFT_TransferFundsInterrogation(TStateAutomat* pSA, const std::string& transactionId,
	                                   /*const EAFTTrensferType transferType,*/ const EAFTTransferCode TransferCode,
	                                   const int TransactionIndex = 0 /*last transaction*/) :
	    TSAState(pSA, EStatePriority::AFT), TransferCode(TransferCode), TransactionIndex(TransactionIndex), mTransactionId(transactionId)
	{
	}

	EAFTTransferCode TransferCode;    // 0xFE ali 0xFF  - ali potrjujemo ali pa nas samo zanima za kaj gre
	int TransactionIndex;
	std::string mTransactionId;
	OnExecuteStatus OnExecute() override;
	int OnEnter() override
	{
		SetStateID(1, "####%d:TSA_AFT_TransferFundsInterrogation", SLOT->Address);
		return 0;
	};
	OnEventStatus OnEvent(std::shared_ptr<TSASMessage> message) override;
};
//---------------------------------------------------------------------------
/*
class TSA_AFT_SetHostCashout : public TSAState
{
   public:
    TSA_AFT_SetHostCashout(TStateAutomat* pSA) : TSAState(pSA, EStatePriority::AFT) {};
    OnExecuteStatus OnExecute() override;
    int OnEnter() override
    {
        SetStateID(1, "####%d:TSA_AFT_SetHostCashout", SLOT->Address);
        return 0;
    };
    OnEventStatus OnEvent(std::shared_ptr<TSASMessage> message) override;
};*/
//---------------------------------------------------------------------------



#endif
