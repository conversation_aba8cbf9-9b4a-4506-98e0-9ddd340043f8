#pragma once

#include "TBaseSASCommunication.h"

class TNoSerialComm : public TBaseSASCommunication
{
   public:
	TNoSerialComm(const char* port, const int32_t baudrate) : TBaseSASCommunication(port, baudrate, ESASCommType::NO_SERIAL) {};

	bool Init() override;
	bool StartPoller(uint8_t sasAddress, std::shared_ptr<TSlotMachineSAS> slotMachine, uint32_t pollInterval) override;

   protected:
	void HandleSASMessage(const std::shared_ptr<TSASMessage>& pMsg);
	std::tuple<uint8_t*, uint16_t> GetLongPoll74(const std::shared_ptr<TSASMessage>& pMsg);
	std::tuple<uint8_t*, uint16_t> GetLongPoll54(const std::shared_ptr<TSASMessage>& pMsg);
	std::tuple<uint8_t*, uint16_t> GetLongPoll1f(const std::shared_ptr<TSASMessage>& pMsg);
};
