//
// Created by sviatoslavs on 05.11.24.
//

#pragma once



class THysterBool
{
   public:
	THysterBool();

	bool operator=(int x);
	bool operator=(bool x);
	bool operator++();
	bool operator--();
	bool operator+=(int x);
	bool operator-=(int x);

	bool Value() const;
	void setTrueTreshold(int treshold);
	void setFalseTreshold(int treshold);
	int GetCounter() const;

	int On();
	int Off();
	bool StatusChanged();

   private:
	int counter;
	int on;
	int off;
	bool value;
	bool statusChanged;
};
