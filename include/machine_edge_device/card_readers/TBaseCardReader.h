//
// Created by matej on 2/20/25.
//

#pragma once
#include <cstdint>
#include <functional>

class TBaseCardReader
{
   public:
	TBaseCardReader() = default;
	virtual ~TBaseCardReader() = default;

	virtual bool Init() = 0;
	virtual int32_t CheckCardStatus() = 0;
	virtual int32_t GetCardID(uint64_t& id) = 0;
	void SetEventCallBack(const std::function<void(uint8_t)>& callBack) { mEventCallback = callBack; };
	void SetAuthKey(const std::array<uint8_t, 6>& authKey) { mAuthKey = authKey; };
	bool IsCardDetected() const { return mCardDetected; };

   protected:
	std::function<void(uint8_t)> mEventCallback;
	std::array<uint8_t, 6> mAuthKey;
	bool mCardDetected;
};
