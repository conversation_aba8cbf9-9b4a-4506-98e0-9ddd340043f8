//
// Created by sviatoslavs on 11.12.24.
//

#pragma once
#include <algorithm>
#include <memory>
#include <mutex>
#include <vector>


class BasePayment
{
   public:
	BasePayment()
	{
		std::lock_guard<std::mutex> lock(mtx);
		mPayments.emplace_back(this);
	}

	virtual ~BasePayment()
	{
		std::lock_guard<std::mutex> lock(mtx);
		mPayments.erase(std::remove(mPayments.begin(), mPayments.end(), this), mPayments.end());
	}
	// global function to check if we have any transaction in progress
	static bool IsInPayment()
	{
		std::lock_guard<std::mutex> lock(mtx);
		for (auto payment : mPayments)
		{
			if (payment->IsInProgress())
			{
				return true;
			}
		}
		return false;
	}

   private:
	// pure virtual function to check if the payment is in progress for the derived class
	virtual bool IsInProgress() = 0;
	// static inline std::vector<std::weak_ptr<BasePayment>> mPayments;
	static inline std::vector<BasePayment*> mPayments;
	static inline std::mutex mtx;
};
