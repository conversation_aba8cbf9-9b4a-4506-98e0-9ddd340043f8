#pragma once

#include <rocksdb/db.h>

#include "TApplication.h"
#include "common/TBuildDefinitions.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "web/WebServer.h"
#include "web/yprotocol/YProtocolWebServer.h"

DECLARE_LOG_CATEGORY(LogDealerAssistBackend, Debug)

namespace dealer_assist::backend
{
class DealerAssistBackendClient;
};    // namespace dealer_assist::backend

class TDealerAssistBackendApp : public rtfwk_sdl2::TApplication, public YProtocolWebServer
{
   private:
	virtual web::websockets::session::validation::value accept_ws(const imaxa_connection_ptr& con, std::optional<security::JWToken> token) override;
	virtual void OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& e) override;
	virtual void OnClientDisconnected(yprotocol::YProtocolClient& client) override;
	virtual void OnPingTimer() override;
	virtual const LogCategory& GetLogCategory() const override { return LogDealerAssistBackend; }

	void ConnectToDatabase();

	void on_open(imaxa_connection_hdl_ref hdl, const std::weak_ptr<yprotocol::YProtocolClient>& clientPtr);

	void DatabaseForEach(const std::string& keyMatch, const std::function<void(const std::string&, const std::string&)>& work) const;
	std::pair<rocksdb::Status, std::optional<json>> DatabaseGetJSON(const std::string& key) const;
	rocksdb::Status DatabasePutJSON(const std::string& key, const json& val) const;
	rocksdb::Status DatabaseDelete(const std::string& key) const;

	// http requests
	void ListTableGroups(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void ManageTableGroups(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void CreateTableGroup(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void DeleteTableGroup(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);

	void ListWorkers(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void ManageWorkers(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void CreateWorker(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void DeleteWorker(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);

	void ListTables(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void ManageTables(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void CreateTable(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void DeleteTable(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);

	void ListRounds(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void ListFlaggedRounds(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);
	void ClearActiveRound(const imaxa_connection_ptr& con, const std::vector<std::string>& resourcePath, const web::QueryString& query);

	// Websocket message handlers
	std::shared_ptr<yprotocol::YDedicatedRequestHandler<yprotocol::YProtocolClient>> RequestHandler;
	json Authenticate(yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json OpenNewRound(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json FlagRound(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json SaveRoundState(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json GetTableHistory(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);

	void Broadcast(const yprotocol::EventTemplate& eventTemplate, const json& ctx);
	void Broadcast(const std::string& tableId, const yprotocol::EventTemplate& eventTemplate, const json& ctx);

	void GenerateDocumentation(const std::filesystem::path& outputFile);

	int StartServer();

	dealer_assist::backend::TableDto GetTable(const std::string& id) const;
	dealer_assist::backend::DealerAssistWorkerInfoDto GetWorker(const std::string& id) const;
	dealer_assist::backend::TableGroupDto GetTableGroup(const std::string& id) const;
	dealer_assist::backend::RoundInfoDto GetRound(const std::string& table, uint32_t round) const;
	dealer_assist::backend::RoundEventsDto GetRoundEvents(const std::string& table, uint32_t round) const;

	std::map<std::string, std::map<uint32_t, dealer_assist::backend::RoundInfoDto>> GetRounds(const std::string& table) const;
	std::map<std::string, std::map<uint32_t, dealer_assist::backend::RoundEventsDto>> GetEvents(const std::string& table) const;

	std::unique_ptr<rocksdb::DB> Database;
	struct TableClients
	{
		std::shared_ptr<dealer_assist::backend::DealerAssistBackendClient> DealerAssistTable;
		std::set<std::shared_ptr<dealer_assist::backend::DealerAssistBackendClient>> Supervisors;
	};
	ThreadSafeProperty<std::unordered_map<std::string, TableClients>, std::shared_mutex> Clients;

	std::shared_ptr<yprotocol::EventTemplate> TableCroupierChangedEvent;
	std::shared_ptr<yprotocol::EventTemplate> TableClearedEvent;

	virtual int LoadEnvironmentVariables() override;

   public:
	TDealerAssistBackendApp(const std::string& type);
	~TDealerAssistBackendApp();

	virtual int Init(const std::vector<std::string>& args) override;

	virtual void OnConfigLoaded(const std::filesystem::path& filepath) override;

	virtual void Startup() override;

	virtual void Destroy() override;

	std::string Version() const override { return version::FULL_VERSION_STRING; }
};
