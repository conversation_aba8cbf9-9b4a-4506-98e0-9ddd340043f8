//
// Created by <PERSON><PERSON><PERSON> on 22. 02. 24.
//

#pragma once
#include <unordered_set>

#include "TApplication.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/TDealerAssistGameLogic.h"
#include "web/yprotocol/YProtocolTypes.h"
#include "yserver/hosts/baccarat/TBaccaratGame.h"

DECLARE_LOG_CATEGORY(LogBaccaratDealerAssist, Normal)

using namespace yserver::gamehost::baccarat;

namespace dealer_assist
{
class TBaccaratDealerAssistGameLogic : public TDealerAssistGameLogic
{
	uint64_t mBetsOpenTime = 15000;
	uint64_t mPauseAtRoundFinishTime = 5000;
	uint64_t mNewRoundPreparationTime = 4000;
	uint32_t mDecisionTime = 5000;
	uint32_t mAnimationTime = 4000;
	uint64_t mAllowBetsCloseTime = 0;
	uint64_t mPauseBeforeRoundEndTime = 2000;
	uint64_t mPauseAtCardBurnEndTime = 0;

	bool bShouldCheckDuplicateCards = true;

	std::string mCutCardBarcode;
	std::unordered_set<std::string> mOpenBetTypes;

	void OnRoundBegin() override;
	void OnRoundBegin_AssumeWriteLock() override;
	void OnPauseBeforeRoundEnd() override;
	void OnRoundEnd() override;
	void OnRoundEnd_AssumeWriteLock() override;

	void AddCard_AssumeLocked(uint32_t card);

	// Tracking cards
	bool IsCardOnTable_AssumeLocked(const FScannedCard& card) const;
	void OnConfigLoaded(const std::filesystem::path& confLoc) override;

	json ShoeChanged_AssumeWriteLock() override;
	void ResetCurrentRound_AssumeWriteLock() override;

	// Dealing cards
	void DealRegularBaccarat_AssumedLockedWrite(const FScannedCard& data);
	void DealFaceDownCards_AssumedLockedWrite(const FScannedCard& data);
	void RevealFirstSide();
	void RevealRemainingSide();
	void RevealRemainingSide_AssumedLockedWrite();
	void RevealCard(EBaccaratDealingPhase cardPosition, EBaccaratSide side, uint8_t cardIndexInHand, bool lastRound = false);
	bool CanRevealRemainingCards_AssumeReadLock() override;
	void UpdateTableState_AssumeWriteLock() override;

	void HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data) override;

   public:
	TBaccaratDealerAssistGameLogic(std::string tableId, DealerGameInfoDto gameInfo);
	~TBaccaratDealerAssistGameLogic() override;

	void HandleScannedCard(const FScannedCard& data) override;
	void HandleCroupierLogin(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) override;
	void OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round) override;
	void OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) override;

	DealerAssistInitResponseDto GetInitPacket() const override;
};
};    // namespace dealer_assist
