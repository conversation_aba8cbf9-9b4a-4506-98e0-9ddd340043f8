#pragma once

#include "TApplication.h"
#include "TBaccaratDealerAssistGameLogic.h"
#include "TDealerAssistGameLogic.h"
#include "common/TBuildDefinitions.h"
#include "dealer-assist/DealerAssistTableBackendClient.h"
#include "dealer-assist/FixedQueue.h"
#include "dealer-assist/TDealerAssistBaseClient.h"
#include "dealer-assist/dto/ChatMessageDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/RoundsUntilCloseDto.h"
#include "web/WebServerAvahi.h"
#include "web/yprotocol/YClient.h"
#include "web/yprotocol/YProtocol.h"
#include "web/yprotocol/YProtocolWebServer.h"

DECLARE_LOG_CATEGORY(DealerAssistAppLog, Debug)

#define DEFAULT_DEALER_ASSIST_PORT 7100

using namespace dealer_assist;

BETTER_ENUM(EClientType, uint8_t, GameHost, DealerConsole)

class TDealerAssistApp : public rtfwk_sdl2::TApplication, public avahi::PublishedWebServer<YProtocolWebServer>
{
	struct DealerAssistState
	{
		std::shared_ptr<DealerAssistWorker> Croupier;
		std::shared_ptr<DealerAssistWorker> Supervisor;
		std::optional<EGameType> CurrentGameType;
		std::optional<DealerGameInfoDto> CurrentGameInfo;
		EDealerAssistPhase mDealingPhase = EDealerAssistPhase::RoundError;
		std::optional<uint8_t> mSubPhase;
		std::optional<EDealerAssistTableErrorCode> mErrorCode;
		std::shared_ptr<dealer_assist::SupervisorState> SupervisorState;
	};

	std::string mDealerAssistBackendAddress;
	bool bDealerAssistBackendSecure;
	bool bAllowUnsecureAdminActions = false;
	std::string mTableId;
	uint32_t mNumOfDecks = 8;
	GameflowSettings mGameflowSettings;
	std::string mStreamUrl;
	std::string mStreamId;
	EStreamType mStreamType = EStreamType::WebRTC;
	ETableMode mTableMode = ETableMode::LandBase;

	std::optional<backend::RoundInfoDto> mRoundInfo;
	std::map<std::string, std::string> mChecksums;
	std::map<std::string, web::NetworkInterface> mNetworkInterfaces;

	web::websockets::session::validation::value accept_ws(const imaxa_connection_ptr& con, std::optional<security::JWToken> token) override;
	void OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& e) override;
	void on_open(imaxa_connection_hdl_ref hdl, const std::weak_ptr<yprotocol::YProtocolClient>& clientPtr);
	void on_close(imaxa_connection_hdl_ref hdl, const std::weak_ptr<yprotocol::YProtocolClient>& clientPtr);

	void OnPingTimer() override;
	void GenerateChecksums();

	void OnClientDisconnected(yprotocol::YProtocolClient& client) override;
	std::shared_ptr<yprotocol::YProtocolClient> createClient(const std::string& clientType, const std::string& clientId, std::optional<security::JWToken> token,
	                                                         bool isLocalClient);
	DealerAssistInitResponseDto GetInitPacket(const EDealerAssistEventRecipient recipient = EDealerAssistEventRecipient::All) const;

	std::unique_ptr<class THoneywellScannerConnection> Scanner;
	std::shared_ptr<class TDealerAssistGameLogic> mDealerAssistLogic;
	std::map<uint32_t, DealerGameInfoDto> mGameConfigs;

	ThreadSafeProperty<DealerAssistState, std::shared_mutex> State;
	std::shared_ptr<DealerAssistTableBackendClient> mDealerAssistBackendClient;

	// Handling clients (live hosts)
	ThreadSafeProperty<FixedQueue<ChatMessageDto>, std::shared_mutex> mChatMessages { FixedQueue<ChatMessageDto>(50) };
	void RemoveClientData(const std::string& clientId);

	ThreadSafeProperty<std::array<std::map<std::string, std::shared_ptr<DealerAssistBaseClient>>, EClientType::_size()>, std::shared_mutex> mClients;

	// Handling players
	ThreadSafeProperty<std::map<std::string, uint64_t>, std::shared_mutex> mPlayers;
	void AddNumberOfPlayers(const std::string& clientId, uint64_t numOfPlayers);
	uint32_t GetNumberOfAllPlayers() const;
	void StateUpdate();

	version::Manifest GetAppManifest() const;
	version::Version GetAppVersion() const;

	// Handling serial port
	bool bUseWebsocketScanner = false;
	std::string mWebsocketScannerAddress;
	bool bWebsocketScannerSecure;
	std::shared_ptr<web::SingleWebsocketClient> mWebsocketScannerClient;
	void SerialListener();
	virtual void OnSerialPortMessage(const std::string& data);
	void ConnectWebsocketScanner();

	void ConnectToDealerAssistBackend();

	void HandleBadgeScanned(const std::string& badgeID);
	void AuthorizeWorker(const std::string& badgeID = std::string(), std::optional<backend::EDealerAssistAccessLevel> accessLevel = std::nullopt);

	void SupervisorLogin_AssumeWriteLock();
	void SupervisorLogout_AssumeWriteLock();

	void SetCurrentGame_AssumeWriteLock(const uint32_t gameId, bool bGameSwitched = false);

	bool IsLocalClient(const std::string& ip) const;
	bool CanPerformUnsecureAdminActions(const yprotocol::YProtocolClient& client) const;

	// Websocket message handlers
	std::shared_ptr<yprotocol::YDedicatedRequestHandler<yprotocol::YProtocolClient>> RequestHandler;
	json ReceivedBarcode(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json ManageNumberOfPlayers(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json DealerAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const;
	json SupervisorAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const;
	json FlagStateChanged(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const;
	json PostChatMessage(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json PostBetsStatistics(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const;
	json GetChatMessages(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const;
	json GetAvailableGames(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json ChangeGame(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json ChangedGameResult(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json CancelSupervisorAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);
	json GetTableHistory(const yprotocol::YProtocolClient& client, const yprotocol::Request& req);

	// Event handling
	void Broadcast(const yprotocol::EventTemplate& eventTemplate, const json& data, std::optional<EClientType> target = std::nullopt);

	std::shared_ptr<yprotocol::EventTemplate> mNotifyDealerConsoleEvent;
	std::shared_ptr<yprotocol::EventTemplate> mDealerLoginChangedEvent;
	std::shared_ptr<yprotocol::EventTemplate> mReportHistoryEvent;
	std::shared_ptr<yprotocol::EventTemplate> mChatMessageEvent;
	std::shared_ptr<yprotocol::EventTemplate> mUpdateStateEvent;
	std::shared_ptr<yprotocol::EventTemplate> mGamePhaseChangedEvent;
	std::shared_ptr<yprotocol::EventTemplate> mSupervisorStateChangedEvent;

	void ChangeGamePhaseAndNotify(EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase = std::nullopt);
	void ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase = std::nullopt);

   protected:
	int LoadEnvironmentVariables() override;
	int StartServer();
	void TryReconnect();
	void TryReconnectWebsocketScanner();

	std::thread mSerialThread;
	std::string mTargetPort;
	uint64_t mReconnectInterval;
	uint64_t mLastConnectAttempt = 0;
	bool bConnecting = false;

   public:
	TDealerAssistApp(const std::string& type);
	~TDealerAssistApp();

	int Init(const std::vector<std::string>& args) override;
	void OnConfigLoaded(const std::filesystem::path& fileloc) override;

	int RunOnce() override;
	void Startup() override;
	void Destroy() override;

	bool isScannerActive() const { return Scanner != nullptr; }
};
