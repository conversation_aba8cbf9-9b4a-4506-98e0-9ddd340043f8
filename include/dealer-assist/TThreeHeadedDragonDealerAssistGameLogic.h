//
// Created by <PERSON><PERSON><PERSON> on 25. 11. 24.
//

#pragma once
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/TDealerAssistGameLogic.h"
#include "web/yprotocol/YProtocolTypes.h"
#include "yserver/YServerTypes.h"
#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"

DECLARE_LOG_CATEGORY(LogThreeHeadedDragonDealerAssist, Normal)

using namespace yserver::gamehost::threeheadeddragon;

namespace dealer_assist
{
class TThreeHeadedDragonDealerAssistGameLogic : public TDealerAssistGameLogic
{
	uint64_t mBetsOpenTime = 15000;
	uint64_t mDecisionTime = 5000;
	uint64_t mPauseAtRoundFinishTime = 5000;
	uint64_t mNewRoundPreparationTime = 4000;
	uint64_t mPauseBeforeRoundEndTime = 2000;
	uint64_t mPauseAtCardBurnEndTime = 0;

	bool bShouldCheckDuplicateCards = true;

	std::string mCutCardBarcode;
	std::unordered_set<std::string> mOpenBetTypes;

	void OnRoundBegin() override;
	void OnRoundBegin_AssumeWriteLock() override;
	void OnPauseBeforeRoundEnd() override;
	void OnRoundEnd() override;
	void OnRoundEnd_AssumeWriteLock() override;

	void DealCards_AssumedLockedWrite(const FScannedCard& data);
	void RevealTigerCard();
	bool CanRevealRemainingCards_AssumeReadLock() override;

	void AddCard_AssumeWriteLock(uint32_t card);

	// Tracking cards
	bool IsCardOnTable_AssumeReadLock(const FScannedCard& card) const;
	void OnConfigLoaded(const std::filesystem::path& confLoc) override;
	json ShoeChanged_AssumeWriteLock() override;
	void ResetCurrentRound_AssumeWriteLock() override;
	void UpdateTableState_AssumeWriteLock() override;

	void HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data) override;

   public:
	TThreeHeadedDragonDealerAssistGameLogic(std::string tableId, const DealerGameInfoDto& gameInfo);
	~TThreeHeadedDragonDealerAssistGameLogic() override;

	void HandleScannedCard(const FScannedCard& data) override;
	void HandleCroupierLogin(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) override;
	void OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round) override;
	void OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) override;

	DealerAssistInitResponseDto GetInitPacket() const override;
};

}    // namespace dealer_assist
