//
// Created by <PERSON><PERSON><PERSON> on 18. 3. 24.
//
#pragma once

#include "dealer-assist/DealerAssistSharedTypes.h"

namespace dealer_assist
{
class DealerAssistCroupierChangeDto : public EventDto
{
   private:
	std::optional<DealerAssistWorker> mNewCroupierInfo;
	std::optional<DealerAssistWorker> mOldCroupierInfo;

   public:
	DealerAssistCroupierChangeDto() = default;

	json ToJSON() const override;
	static DealerAssistCroupierChangeDto FromJSON(const json& val);

	const std::optional<DealerAssistWorker>& GetNewCroupierInfo() const { return mNewCroupierInfo; }
	const std::optional<DealerAssistWorker>& GetOldCroupierInfo() const { return mOldCroupierInfo; }
};
};    // namespace dealer_assist
