//
// Created by <PERSON><PERSON><PERSON> on 20. 02. 24.
//

#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/ScannedCard.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "yserver/hosts/baccarat/BaccaratHostSharedTypes.h"

using namespace yserver::gamehost::baccarat;

namespace dealer_assist
{
class AddCardDto : public EventDto
{
   public:
	uint32_t mScannedCard;
	uint8_t mCardPosition;
	std::optional<uint8_t> mNextCardPosition;
	std::optional<uint8_t> mOutcome;

	json ToJSON() const override;

	static AddCardDto FromJSON(const json& val);

	FScannedCard GetScannedCard() const;
	uint32_t GetCardValue() const;
};

class AddBaccaratCardDto : public AddCardDto
{
   public:
	AddBaccaratCardDto() : AddCardDto() {};
	AddBaccaratCardDto(const AddCardDto& baseDto);
	uint32_t mBankerHandValue = 0;
	uint32_t mPlayerHandValue = 0;

	json ToJSON() const override;

	static AddBaccaratCardDto FromJSON(const json& val);
};

class AddDragonTigerCardDto : public AddCardDto
{
   public:
	AddDragonTigerCardDto() : AddCardDto() {};
	AddDragonTigerCardDto(const AddCardDto& baseDto);
	uint32_t mDragonHandValue = 0;
	uint32_t mTigerHandValue = 0;

	json ToJSON() const override;
};

class AddThreeHeadedDragonCardDto : public AddCardDto
{
   public:
	AddThreeHeadedDragonCardDto() : AddCardDto() {};
	AddThreeHeadedDragonCardDto(const AddCardDto& baseDto);
	uint32_t GoldenDragonHandValue = 0;
	uint32_t BlackDragonHandValue = 0;
	uint32_t RedDragonHandValue = 0;
	uint32_t TigerHandValue = 0;
	std::optional<std::vector<uint8_t>> Outcomes;

	json ToJSON() const override;
};

};    // namespace dealer_assist