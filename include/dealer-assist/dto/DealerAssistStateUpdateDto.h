//
// Created by <PERSON><PERSON><PERSON> on 12. 02. 24.
//

#pragma once

#include <utility>

#include "Json.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "yserver/hosts/baccarat/TBaccaratGameLogic.h"

using namespace yserver::gamehost::baccarat;

namespace dealer_assist
{
class DealerAssistStateUpdateDto : public EventDto
{
   private:
	EDealerAssistPhase mPhase = EDealerAssistPhase::WaitingForPlayers;
	uint64_t mRoundId = 0;
	std::optional<uint8_t> mWinningWager;
	std::optional<std::vector<uint8_t>> mWinningWagers;
	std::optional<EDealerAssistTableErrorCode> mErrorCode;

   public:
	DealerAssistStateUpdateDto() = default;
	DealerAssistStateUpdateDto(EDealerAssistPhase phase, uint64_t gameId);

	std::optional<uint32_t> subPhase;

	json ToJSON() const override;
	static DealerAssistStateUpdateDto FromJSON(const json& val);
	uint64_t GetRoundId() const;
	void SetWinningWager(uint8_t wager);
	void SetWinningWagers(std::vector<uint8_t> wagers);
	void SetErrorCode(const EDealerAssistTableErrorCode& errorCode);

	EDealerAssistPhase GetPhase() const;
	std::optional<uint8_t> GetWinningWager() const;
	std::optional<EDealerAssistTableErrorCode> GetErrorCode() const;
};
};    // namespace dealer_assist
