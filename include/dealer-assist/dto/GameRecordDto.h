//
// Created by <PERSON><PERSON><PERSON> on 8. 1. 25.
//

#pragma once

#include "dealer-assist/DealerAssistSharedTypes.h"

namespace dealer_assist
{

class GameRecordDto : public EventDto
{
   public:
	std::vector<uint8_t> Winners;
	uint32_t WinningHandValue = 0;
	uint64_t GameRoundId = 0;
	std::string GameType;
	json Cards;
	json ExtraData;
	std::unordered_map<std::string, uint32_t> HandValues;

	GameRecordDto() = default;

	json ToJSON() const override;
	static GameRecordDto FromJSON(const json& val);
};

struct BaccaratGameRecordExtraDataDto
{
	bool PlayerPair = false;
	bool BankerPair = false;
	bool Naturals = false;
};

void to_json(json& sourceJson, const BaccaratGameRecordExtraDataDto& dto);
void from_json(const json& sourceJson, BaccaratGameRecordExtraDataDto& dto);

};    // namespace dealer_assist
