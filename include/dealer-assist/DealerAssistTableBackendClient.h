//
// Created by <PERSON><PERSON><PERSON> on 4. 4. 24.
//

#pragma once

#include "dealer-assist//dto/GameRecordDto.h"
#include "web/yprotocol/YClient.h"

using namespace dealer_assist;

class DealerAssistTableBackendClient : public YClient
{
   public:
	DealerAssistTableBackendClient(const std::string& hostname, bool bSecure, const std::string& name = {}) : YClient(hostname, bSecure, name) {}

	boost::future<YResponse> FlagRound(uint32_t roundId, const std::string& reason);
	boost::future<std::vector<GameRecordDto>> GetTableHistory(const std::string& filterByGameType);
	boost::future<std::vector<GameRecordDto>> GetTableHistory(const std::string& filterByGameType);
	boost::future<YResponse> OpenNewRound(const std::string& gameType);
	boost::future<YResponse> SaveResult(const json& result);
};
