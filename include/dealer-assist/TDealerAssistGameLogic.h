//
// Created by <PERSON><PERSON><PERSON> on 22. 02. 24.
//

#pragma once

#include "TTaskMan.h"
#include "YDelegate.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/DealerAssistTableBackendClient.h"
#include "dealer-assist/ScannedCard.h"
#include "dealer-assist/TVirtualDealer.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "dealer-assist/dto/CardBurnDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/DealerAssistStateUpdateDto.h"
#include "dealer-assist/dto/FlagStateChangeDto.h"
#include "dealer-assist/dto/ResultChangeRequestDto.h"
#include "dealer-assist/dto/RoundsUntilCloseDto.h"
#include "web/yprotocol/YProtocolTypes.h"
#include "yserver/YServerTypes.h"
#include "yserver/hosts/cards/CardGameBetStatistics.h"

DECLARE_LOG_CATEGORY(LogDealerAssistGameLogic, Normal)

namespace dealer_assist
{
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnBroadcastEventDelegate, const yprotocol::EventTemplate&, const EventDto&)
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnBroadcastPhaseChangedDelegate, const yprotocol::EventTemplate&, const DealerAssistStateUpdateDto&)
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnUpdateStateDelegate)
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnReloginCroupierDelegate)
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnSwitchGameDelegate)
DECLARE_SIMPLE_MULTICAST_DELEGATE(OnTableCloseDelegate)

struct DealerAssistTableState
{
   public:
	bool bShouldBurnCards = false;
	uint32_t NumOfActivePlayers = 0;
	uint32_t NumOfAllPlayers = 0;
	uint8_t NumOfDecks = 8;
	CardBurnDto ShoeChangeState;
	std::unordered_set<FScannedCard> CardsOnTable;    // For counting cards (reset after shoe change)
	TableState Table;
	DealerAssistActionsState ActionsState;
	std::shared_ptr<dealer_assist::SupervisorState> SupervisorState;
	std::shared_ptr<DealerAssistWorker> Croupier;
	std::shared_ptr<yserver::gamehost::TBaseGameLogic> GameLogic;
	std::shared_ptr<TVirtualDealerLogic> VirtualDealerLogic;
	std::vector<GameRecordDto> GameRecords;
	std::optional<RoundsUntilCloseDto> RoundsUntilChange;
	GameflowSettings GameflowConfig;
	uint32_t RoundsPlayedCounter = 0;
	DealerAssistTableState() = default;
};

class DealerAssistException : public std::exception
{
   public:
	explicit DealerAssistException(const backend::EDealerAssistErrorCode code) : mCode(code), mMessage(std::string("DealerAssistError: ") + code._to_string()) {}

	const char* what() const noexcept override { return mMessage.c_str(); }

	backend::EDealerAssistErrorCode Code() const { return mCode; }

   private:
	backend::EDealerAssistErrorCode mCode;
	std::string mMessage;
};

class TDealerAssistGameLogic : public TConfiguration
{
   private:
	// Proceede with game after supervisor called
	void ProceedWithGame_AssumeWriteLock();
	void TrySaveRound(json roundState, backend::ERoundStatus status, std::shared_ptr<boost::promise<void>> promise);
	void TryOpenNewRound(std::shared_ptr<boost::promise<void>> promise);

   protected:
	ThreadSafeProperty<DealerAssistTableState, std::shared_mutex> State;

	// Events
	std::shared_ptr<yserver::EventTemplate> mAddCardEvent;
	std::shared_ptr<yserver::EventTemplate> mGameStateChangedEvent;
	std::shared_ptr<yserver::EventTemplate> mNotifyDealerConsoleEvent;
	std::shared_ptr<yserver::EventTemplate> mCardBurnEvent;
	std::shared_ptr<yserver::EventTemplate> mReportPlayersEvent;
	std::shared_ptr<yserver::EventTemplate> mCutCardEvent;
	std::shared_ptr<yserver::EventTemplate> mFlagStateChangeEvent;
	std::shared_ptr<yserver::EventTemplate> mToggleChatEvent;
	std::shared_ptr<yserver::EventTemplate> mReportBetStatisticsEvent;
	std::shared_ptr<yserver::EventTemplate> mGameResultChangedEvent;
	std::shared_ptr<yprotocol::EventTemplate> mSupervisorStateChangedEvent;
	std::shared_ptr<yserver::EventTemplate> mRoundsUntilCloseEvent;

	std::unique_ptr<CardGameBetsStatistic> mBetsStatistics;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mNewRoundPreparationTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mRetryTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mPauseBeforeRoundEndTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mPauseAtRoundEndTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mAnimationTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mDecisionTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mPauseAfterCardBurnTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mPauseBetweenCardsTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mBetTimer;
	std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler> mCardRevealTimer;

	void StartTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer);
	void StopTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer);
	void LogoutCroupier() const;

	const uint8_t faceDownCard = 0;
	bool bScannerConnected = false;

	void ReportRoundsUntilChange_AssumeWriteLock(ERoundsCountdownType type);
	void CheckRoundClosing_AssumeWriteLock();
	void CheckGameChangeRounds_AssumeWriteLock();
	void ReportFlagStateChange(EFlagStateType type, bool state) const;
	void FlagRound_AssumeWriteLock(const EFlagRoundReason& event);
	void HandleCardBurn_AssumeWriteLock(const FScannedCard& data);
	virtual boost::future<void> SaveGameResultBeforeLogout_AssumeReadLock();
	virtual boost::future<void> SaveRoundState_AssumeReadLock(backend::ERoundStatus status);
	void SaveIntermediateRoundState_AssumeReadLock();
	void SupervisorCalled_AssumeWriteLock();
	void VoidRound_AssumeWriteLock();
	virtual json ShoeChanged_AssumeWriteLock() = 0;
	void HandleCutCard_AssumeWriteLock();
	virtual void HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data) = 0;
	void OpenNewRound(uint32_t ID);
	json RedrawCards_AssumeWriteLock();
	virtual void ResetCurrentRound_AssumeWriteLock() = 0;

	void OpenNewRound_AssumeWriteLock();
	virtual void OnRoundBegin() = 0;
	virtual void OnRoundBegin_AssumeWriteLock() = 0;
	virtual void OnPauseBeforeRoundEnd() = 0;
	virtual void OnRoundEnd() = 0;
	virtual void OnRoundEnd_AssumeWriteLock() = 0;

	virtual bool CanRevealRemainingCards_AssumeReadLock() = 0;
	virtual void UpdateTableState_AssumeWriteLock() = 0;
	bool CanCloseTable() const;
	bool CanChangeGame() const;

	void DealWithVirtualDealer();
	void BurnCardsWithVirtualDealer();
	void DealWithVirtualDealer_AssumeWriteLock();
	void BurnCardsWithVirtualDealer_AssumeWriteLock();
	void ReportNumberOfPlayers_AssumeReadLock();

   public:
	const EGameType GameType;
	const std::string TableId;
	const bool bVirtualGame;
	std::shared_ptr<DealerAssistTableBackendClient> DealerAssistBackendClient;

	TDealerAssistGameLogic(EGameType gameType, std::string tableId, bool virtualGame = false);
	void OnConfigLoaded(const std::filesystem::path& filename) override;

	bool IsOpenVersion() const { return GameType == EGameType::OpenBaccarat || GameType == EGameType::OpenDragonTiger || GameType == EGameType::ThreeHeadedDragon; }

	// Pure virtual methods
	virtual void HandleScannedCard(const FScannedCard& data) = 0;
	virtual void HandleCroupierLogin(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) = 0;
	virtual void OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo) = 0;
	void HandleCroupierSwitch(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo);
	virtual void StopAllTimers();

	virtual void OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round = std::nullopt) = 0;
	virtual DealerAssistInitResponseDto GetInitPacket() const = 0;

	virtual DealerAssistTableState GetTableState() const;
	virtual SupervisorState GetSupervisorState() const;

	virtual void HandleSupervisorLogin(const DealerAssistWorker& supervisorInfo);
	virtual void HandleSupervisorLogout();
	virtual void HandleTableClear();
	virtual json HandleDealerAction(const EDealerAction& action);
	virtual json HandleSupervisorAction(const ESupervisorAction& action);
	virtual json HandleFlagStateChange(const FlagStateChangeDto& flagStateChanged);
	void ScannerConnectionStateChanged(bool isConnected);
	void OnNumberOfPlayersChanged(uint32_t numOfPlayers);

	void AddHostBetsStatistics(const std::string& hostId, const std::unordered_map<std::string, BetTypeHostStats>& bets);
	void ClearBetsStatisticsForHost(const std::string& hostId);

	void ChangeGamePhaseAndNotify(EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase = std::nullopt);
	void ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase = std::nullopt);

	boost::future<void> SaveGameResultBeforeLogout();
	void HandleCroupierRelogin(const DealerAssistWorker& croupierInfo);
	void SetDealerAssistClient(const std::shared_ptr<DealerAssistTableBackendClient>& client);
	bool CanSwitchGame();
	void ResetSupervisorAction();
	void SetChangedGameResult(const ResultChangeRequestDto& dto);
	void ResetSelectedGame();
	void SupervisorSelectedGame(const DealerGameInfoDto& gameInfo);
	bool CanLogoutCroupier();
	bool CanSwitchCroupier();
	void SetGameflowSettings(const GameflowSettings& settings);

	virtual void OnDealerAssistBackendDisconnected();

	// Delegates
	OnBroadcastEventDelegate OnBroadcastEvent;
	OnUpdateStateDelegate OnStateUpdate;
	OnBroadcastPhaseChangedDelegate OnBroadcastPhaseChangedEvent;
	OnReloginCroupierDelegate OnReloginCroupierEvent;
	OnSwitchGameDelegate OnSwitchGameEvent;
	OnTableCloseDelegate OnTableCloseEvent;
};
};    // namespace dealer_assist
