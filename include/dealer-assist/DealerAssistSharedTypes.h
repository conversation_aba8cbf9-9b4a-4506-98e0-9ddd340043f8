#pragma once

#include <string>

#include "Enums.h"
#include "Json.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "hosts/cards/CardGameSharedTypes.h"

namespace dealer_assist
{
BETTER_ENUM(EDealerAssistPhase, uint8_t, WaitingForCroupier, WaitingForPlayers, NewRoundPreparation, BetsOpen, BetsClosed, DealingCards, RoundEnd, ShoeChange, CardBurn,
            RoundVoid, RoundError, Decision, TableClosed);
BETTER_ENUM(EDealingSubPhase, uint8_t, FaceDown = 0, Dealing = 1, SideSelectionAnimation = 2, LeftSide = 3, RightSide = 4);
BETTER_ENUM(ESupervisorSubPhase, uint8_t, WaitingForSupervisor, GameSelection, ConfirmGameSelection, ConfirmAction, ActionSelection);
BETTER_ENUM(EDealerAssistTableErrorCode, uint8_t, BackendDisconnected, RoundOpeningFailed, RoundResultSavingFailed);
BETTER_ENUM(EDealerAssistTableNotificationCode, uint8_t, AuthenticationFailed, AuthenticationSuccess, CroupierChangeInvalidState, DuplicateCard, FlagFailed, InvalidCard,
            InvalidCroupierId, RoundOpeningFailed, RoundResultSavingFailed, ScannerDisconnected, ScannerConnected);
BETTER_ENUM(EDealerAction, uint8_t, ShoeChanged, RedrawCards, CallSupervisor, CompleteRound);
BETTER_ENUM(ESupervisorAction, uint8_t, ContinueGame, VoidGame, FreeHand, CloseTable, ChangeResult, ChangeGame, ChangeShoe);
BETTER_ENUM(EFlagRoundReason, uint8_t, InvalidCard, DuplicatedCards, CutCard, CroupierFlagged, RoundVoid);
BETTER_ENUM(EFlagStateType, uint8_t, ChatEnabled, RoundFlagged, TableClosingRequested, SupervisorCalled, ResultChangingRequested, GameChangingRequested,
            ChangeShoeRequested, FreeHandEnabled);
BETTER_ENUM(ERoundsCountdownType, uint8_t, TableClose, GameChange);
BETTER_ENUM(EDealerAssistEventRecipient, uint8_t, DealerConsole, Gamehost, All);
BETTER_ENUM(EStreamType, uint8_t, imaxaPlayer, NanoCosmos, WebRTC);
BETTER_ENUM(EGameType, uint8_t, Baccarat, DragonTiger, ThreeHeadedDragon, OpenBaccarat, OpenDragonTiger)
BETTER_ENUM(ETableMode, uint8_t, LandBase, Online)

extern const std::string DealerAssistServerServiceName;

extern const std::string ADD_CARD_EVENT_NAME;
extern const std::string DEALER_ASSIST_TABLE_NOTIFICATION_EVENT_NAME;
extern const std::string GAME_PHASE_CHANGED_EVENT_NAME;
extern const std::string GAME_STATE_CHANGED_EVENT_NAME;
extern const std::string CARD_BURN_EVENT_NAME;
extern const std::string CUT_CARD_DRAWN_EVENT_NAME;
extern const std::string PLAYER_COUNT_CHANGED_EVENT_NAME;
extern const std::string DEALER_LOGIN_CHANGED_EVENT_NAME;
extern const std::string DEALER_ACTION_EVENT_NAME;
extern const std::string FLAG_STATE_CHANGED_EVENT_NAME;
extern const std::string SET_FLAG_STATE_EVENT_NAME;
extern const std::string POST_CHAT_MESSAGE_EVENT_NAME;
extern const std::string TOGGLE_CHAT_EVENT_NAME;
extern const std::string GET_CHAT_MESSAGES;
extern const std::string POST_BET_STATISTICS;
extern const std::string REPORT_BET_STATISTICS_RESULT;
extern const std::string CHANGE_GAME_EVENT_NAME;
extern const std::string GET_GAMES_EVENT_NAME;
extern const std::string CHANGE_GAME_RESULT_EVENT_NAME;
extern const std::string CANCEL_SUPERVISOR_ACTION_EVENT_NAME;
extern const std::string SUPERVISOR_STATE_CHANGED_EVENT_NAME;
extern const std::string GAME_RESULT_CHANGED_EVENT_NAME;
extern const std::string SUPERVISOR_ACTION_EVENT_NAME;
extern const std::string ROUNDS_UNTIL_CHANGE_EVENT_NAME;
extern const std::string GET_TABLE_HISTORY_EVENT_NAME;

extern const std::string DEALER_ASSIST_CROUPIER_CLIENT_TYPE;
extern const std::string DEALER_ASSIST_GAMEHOST_CLIENT_TYPE;

class EventDto
{
   public:
	virtual ~EventDto() = default;
	EDealerAssistEventRecipient Recipient = EDealerAssistEventRecipient::All;
	virtual json ToJSON() const = 0;

	EventDto(EDealerAssistEventRecipient recipient = EDealerAssistEventRecipient::All) : Recipient(recipient) {}

	virtual bool operator==(const EventDto& other) const = default;
};

class EmptyEventDto : public EventDto
{
   public:
	json ToJSON() const override { return json(); }
};

struct DealerGameInfoDto
{
	EGameType GameType = EGameType::Baccarat;
	bool IsVirtual = false;
	uint32_t Id = 0;
	std::string DisplayName = "Unknown";
	yserver::gamehost::ECardRule CardRule = yserver::gamehost::ECardRule::Asian;
	json Config = {};

	bool operator==(const DealerGameInfoDto& other) const;
	bool operator!=(const DealerGameInfoDto& other) const;
	std::string ToString() const;

	static const JsonSchema& Schema();
	static const JsonSchema& GameConfigSchema();
};

struct GameflowSettings
{
	uint32_t RoundsBeforeClose = 0;
	uint32_t RoundsBeforeGameChange = 0;
	bool SkipDecisionIfNoBets = false;
};

void to_json(json& sourceJson, const DealerGameInfoDto& dto);
void from_json(const json& sourceJson, DealerGameInfoDto& dto);

struct DealerAssistActionsState
{
	bool bChatEnabled = false;
	bool bRoundFlagged = false;
	bool bTableCloseRequested = false;
	bool bSupervisorCalled = false;
	bool bResultChangeRequested = false;
	bool bGameChangeRequested = false;
	bool bChangeShoeRequested = false;
	bool bFreeHandEnabled = false;

	json ToJSON() const;
	static DealerAssistActionsState FromJSON(const json& val);
};

struct DealerAssistWorker
{
	std::string ID;
	std::string Name;
	std::string Location;
	backend::EDealerAssistAccessLevel AccessLevel = backend::EDealerAssistAccessLevel::None;

	json ToJSON() const;
	static DealerAssistWorker FromJSON(const json& val);
	bool IsSupervisor() const;
};

struct SupervisorState : public EventDto
{
	bool Active = false;
	bool bContinueUnfinishedGame = false;
	std::optional<ESupervisorSubPhase> SubPhase;
	std::optional<EGameType> SelectedGameType;
	std::optional<DealerGameInfoDto> SelectedGameInfo;
	std::optional<ESupervisorAction> RequestedAction;
	json ChangedGameResult;

	json ToJSON() const override;
	static SupervisorState FromJSON(const json& val);

	void Reset();
};

struct TableState
{
	std::optional<uint8_t> mDealingPhase;
	std::optional<uint8_t> mCardPosition;
	std::optional<uint8_t> mSubPhase;
	std::optional<uint8_t> mWinner;
	std::optional<uint8_t> mSelectedSide;
	std::optional<std::vector<uint8_t>> mWinners;

	json mHandValue;
	json mCards;
	std::map<uint8_t, uint32_t> mFaceDownCards;

	std::optional<EDealerAssistTableErrorCode> mErrorCode;
	EDealerAssistPhase mPhase = EDealerAssistPhase::RoundError;
	std::optional<uint64_t> mRoundID;
	std::optional<uint32_t> mRemainingBettingTimeInSeconds = 0;
	std::optional<uint64_t> mTimestamp = 0;

	json ToJSON() const;
	static TableState FromJSON(const json& val);

	void Clear();
};

struct UpdatedCard
{
	uint32_t position;
	uint32_t oldValue;
	uint32_t newValue;
};

}    // namespace dealer_assist
