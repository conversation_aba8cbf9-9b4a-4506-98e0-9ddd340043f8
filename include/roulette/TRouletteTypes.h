/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#pragma once

#include <array>
#include <cstdint>

#include "Enums.h"
#include "TConfiguration.h"
#include "Vector.h"
#include "yserver/TRouletteGamesExtraData.h"

namespace roulette
{
const size_t MAX_BETTING_CHIPS_COUNT = 6;
const int MAX_BET_CHIP_VALUE = 33333;

static const std::array<std::string, EBetStyle::_size() + 1> BetNames = { "Unknown (void)",    // BET_NONE
	                                                                      "Straight up",    // BET_PLAIN
	                                                                      "Split",    // BET_CAVAL
	                                                                      "Trio",    // BET_TRASV
	                                                                      "Corner",    // BET_CARRE
	                                                                      "Five numbers",    // BET_FIFTH
	                                                                      "Six numbers",    // BET_SIXTH
	                                                                      "Horizontal line",    // BET_HLINE
	                                                                      "Third",    // BET_THIRD
	                                                                      "Red / Black",    // BET_COLOR
	                                                                      "Odd / Even",    // BET_ODEVN
	                                                                      "Half",    // BET_PHALF
	                                                                      "Les Figures",    // BET_LES_FIGURES
	                                                                      "Tout Va",    // BET_TOUT_VA
	                                                                      "Le Finali 3",
	                                                                      "Le Finali 4",
	                                                                      "Le Finali 5",
	                                                                      "Le Finali Tout Va 3",
	                                                                      "Le Finali Tout Va 4",
	                                                                      "Le Finali Tout Va 5" };

class TRouletteStake : public TConfiguration
{
   public:
	struct FFieldLimit
	{
		uint64_t Min = 0;
		uint64_t Max = 0;
		uint64_t Multiple = 0;

		FFieldLimit& operator*=(uint64_t multiply);
		FFieldLimit operator*(uint64_t multiply) const;
	};

   private:
	// starts with BET_PLAIN as the first type and skips BET_NONE, so the index in this table is equal to betType-1
	std::array<FFieldLimit, EBetStyle::_size()> LimitsTable;

	virtual void OnConfigLoaded(const std::filesystem::path& filename) override;

   public:
	TRouletteStake();

	void SetMaxBet(EBetStyle fieldType, uint64_t maxBet);
	void SetMinBet(EBetStyle fieldType, uint64_t minBet);
	void SetFieldLimit(EBetStyle fieldType, const FFieldLimit& limit);

	FFieldLimit GetFieldLimit(EBetStyle fieldType) const;

	uint64_t PlayboardLimitMin;
	uint64_t PlayboardLimitMax;
	unsigned int BetsCountMax;
	uint64_t MaxTableWinLimit;
	std::map<EBetStyle, uint32_t> MultiplierOverrides;
	std::array<uint64_t, MAX_BETTING_CHIPS_COUNT> ChipValues;

	bool IsValid() const;

	static const JsonSchema& StakeSchema();
};

BETTER_ENUM(EPlayboardMode, uint8_t, Normal, LesFigures, LesFiguresPlus, LeFinali);

struct FBetPosition
{
   public:
	EBetStyle Type = EBetStyle::None;
	std::set<uint32_t> CoveredNumbers;
	std::string Name;

   public:
	FBetPosition(uint32_t Number);
	FBetPosition(const std::set<uint32_t>& CoveredNums, EBetStyle BetType = EBetStyle::None);
	FBetPosition() {}

	FBetPosition operator|(const FBetPosition& other) const noexcept;

	FBetPosition operator|(uint32_t number) const noexcept;

	// EBetStyle Type() const noexcept;
	//
	// const std::string& Name() const noexcept;

	void SetOverrideName(const std::string& name) noexcept;

	bool Contains(uint32_t number) const noexcept;

	const std::set<uint32_t>& Numbers() const noexcept;

	json ToJSON() const;

	static void FromJson(const json& json, FBetPosition& field);

	static bool IsValidFor(EBetStyle style, EPlayboardMode mode);

	void GenerateName();
};

struct WonField
{
	WonField(uint32_t posX, uint32_t posY, const FBetPosition& pos, uint64_t won = 0) : X(posX), Y(posY), Position(pos), Won(won) {}
	WonField() {};
	uint32_t X;
	uint32_t Y;
	FBetPosition Position;
	uint64_t Won;
	virtual ~WonField() = default;

	json ToJSON() const;
	static void FromJson(const json& json, WonField& field);
};

struct CalculateWinsResultDto
{
	uint64_t TotalWon = 0;
	std::list<WonField> WinningFields;
	virtual ~CalculateWinsResultDto() = default;

	json ToJSON() const;
	static void FromJson(const json& jsonIn, CalculateWinsResultDto& field);
};



};    // namespace roulette
