#pragma once

#include "TBuildDefinitions.h"
#include "TDBManager.h"


using namespace rtfwk_sdl2;

typedef struct
{    // carefully with this structure!!!
	std::string RouletteID;
	std::string ExpireDate;
	std::string ExpireCode;
	std::string ExpireOrderStatus;
} TExpireData;

typedef struct
{    // carefully with this structure!!!
	std::string ClientID;
	std::string ChallengeCode;
} TChallengeData;

class TExpireApp : public TApplication
{
   public:
	TExpireApp(const std::string& type);
	~TExpireApp() {};
	virtual int Init(const std::vector<std::string>& args) override;
	int InitRPCServer();

	virtual int RunOnce() override;

	virtual void OnWriteLogs(const std::list<TLogEntry>& logs) override;

	std::string Version() const override { return version::FULL_VERSION_STRING; }

	void StartExpireSH();

	void MainTask();

	std::vector<TExpireData> getExpireData();
	std::vector<TChallengeData> getChallengeCodes(const std::string& RouletteID);
	void errorExpireCode(const std::string& RouletteID);
	void saveExpireCode(const std::string& RouletteID, const std::string& ClientID, const std::string& ExpireCode, int ExpireIndex);

	int CalculateExpireCode(unsigned short Day, unsigned short Month, unsigned short Year, unsigned short ChallengeCode, unsigned char Flags,
	                        unsigned char* pResult /*must be long enough*/);

   private:
	TDBManager* pDBMngr;

	virtual int LoadEnvironmentVariables() override;

	std::weak_ptr<TTimedTaskHandler> mpMainTask;
};
