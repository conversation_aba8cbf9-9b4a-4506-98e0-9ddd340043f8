site_name: <PERSON><PERSON><PERSON> (master)
site_description: SOCI - The C++ Database Access Library
repo_url: https://github.com/SOCI/soci/
copyright: Copyright &copy; 2017 <a href="https://github.com/msobczak"><PERSON><PERSON><PERSON></a> and <a href="http://soci.sourceforge.net/people.html">SOCI Team</a>.

nav:
  - Home: index.md
  - Overview:
    - Getting Started: quickstart.md
    - Installation: installation.md
    - Library Structure: structure.md
    - License: license.md
    - FAQ: faq.md
  - User Guide:
    - Connections: connections.md
    - Queries: queries.md
    - Data Binding: binding.md
    - Data Indicators: indicators.md
    - Data Types: types.md
    - LOBs: lobs.md
    - Statements: statements.md
    - Transactions: transactions.md
    - Procedures: procedures.md
    - Errors: errors.md
    - Logging: logging.md
    - Interfaces: interfaces.md
  - Backends:
    - Features: backends/index.md
    - DB2: backends/db2.md
    - Firebird: backends/firebird.md
    - MySQL: backends/mysql.md
    - ODBC: backends/odbc.md
    - Oracle: backends/oracle.md
    - PostgreSQL: backends/postgresql.md
    - SQLite3: backends/sqlite3.md
  - Miscellaneous:
    - Beyond SQL: beyond.md
    - Multi-threading: multithreading.md
    - Boost: boost.md
    - Utilities: utilities.md
    - Vagrant: vagrant.md
  - API:
    - Client API: api/client.md
    - Backend API: api/backend.md
    - Ada:
        - languages/ada/index.md
        - languages/ada/concepts.md
        - languages/ada/idioms.md
        - languages/ada/reference.md

#theme: readthedocs
