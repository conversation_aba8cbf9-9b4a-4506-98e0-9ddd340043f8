
#include "XmlRpcClient.h"

#include "Cryptography.h"
#include "TApplication.h"
#include "XmlRpc.h"

namespace XmlRpc
{

// Static data
const std::string XmlRpcClient::REQUEST_BEGIN = "<?xml version=\"1.0\"?>\r\n"
                                                "<methodCall><methodName>";
const std::string XmlRpcClient::REQUEST_END_METHODNAME = "</methodName>\r\n";
const std::string XmlRpcClient::REQUEST_END = "</methodCall>\r\n";
const std::string XmlRpcClient::METHODRESPONSE_TAG = "<methodResponse>";
const std::string XmlRpcClient::FAULT_TAG = "<fault>";

XmlRpcClient::XmlRpcClient(const WebClientConfig& cfg, bool bEnableWebsockets) : SingleWebsocketClient(cfg), bUseWebsockets(bEnableWebsockets)
{
	LogComponentName = "xmlrpc-client";
	Log(Normal, "New client on host address %s", cfg.Host.c_str());

	MyID = crypto::GenRandID(4);

	if (bUseWebsockets)
	{
		PingInterval = 2500;
		Initialize();
	}
}

XmlRpcClient::~XmlRpcClient()
{
	if (PollTask)
		PollTask->Remove();

	Stop();
}

std::error_code XmlRpcClient::run()
{
	std::error_code ec;
	if (bUseWebsockets)
	{
		Start("xmlrpc client");
		ec = Connect();
		if (ec)
			Log(Warning, "Could not create websocket connection to %s: %s", HostAddress.c_str(), ec.message().c_str());
	}
	else
	{
		ec = singleStatusRequest();
		if (ec)
			return ec;

		if (!PollTask)
			PollTask = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda, rtfwk_sdl2::TASK_THREAD_SAFE>(
			  [this]() {
				  const std::error_code ec = singleStatusRequest();
				  if (!ec && OnEvent)
					  OnEvent(LastStatusResult);
			  },
			  100U, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "GetGameState");
	}

	if (!ec)
		Log(Important, "Successfully connected to %s!", HostAddress.c_str());

	return ec;
}

std::error_code XmlRpcClient::singleStatusRequest()
{
	const std::error_code ec = execute("GetGameState", XmlRpc::XmlRpcValue(), LastStatusResult, 3.0);
	bConnected = !ec;
	return ec;
}

// Execute the named procedure on the remote server.
// Params should be an array of the arguments for the method.
// Returns true if the request was sent and a result received (although the result
// might be a fault).
std::error_code XmlRpcClient::execute(const char* method, XmlRpcValue const& params, XmlRpcValue& result, double TimeoutSeconds /*-1.0 - endless*/)
{
	std::string body = REQUEST_BEGIN;
	body += method;
	body += REQUEST_END_METHODNAME;

	// If params is an array, each element is a separate parameter
	if (params.valid())
	{
		body += PARAMS.Begin;
		if (params.getType() == XmlRpcValue::TypeArray)
		{
			for (size_t idx = 0; idx < params.size(); idx++) body += PARAM.Begin + params[idx].toXml() + PARAM.End;
		}
		else
		{
			body += PARAM.Begin;
			body += params.toXml();
			body += PARAM.End;
		}

		body += PARAMS.End;
	}
	body += REQUEST_END;

	result.clear();
	web::http::request req;
	req.set_method(web::http::method(web::http::verb::post));
	req.append_header(web::http::field::content_type, "text/xml");
	req.append_header(web::http::field::user_agent, MyID);
	req.set_body(std::move(body));

	// VERY IMPORTANT FOR BACKWARD COMPATIBILITY
	// need to add a space before content length because the old XmlRpcServer is stupid
	req.replace_header(web::http::header(web::http::field::content_length), " " + req.get_header(web::http::header(web::http::field::content_length)));

	/*if (TimeoutSeconds > 0.)
	    HTTPConfig.set_timeout(std::chrono::milliseconds((int64_t)(TimeoutSeconds * 1e3)));*/

	const web::http::response response { Request(std::move(req)).get() };
	std::error_code ec = response.get_error_code();
	if (!ec)
	{
		if (!parseResponse(response.get_body(), result))
			ec = web::websockets::error::make_error_code(web::websockets::error::payload_violation);
	}

	if (ec)
		Log(Error, "Executing request on %s: %s", HostAddress.c_str(), ec.message().c_str());

	return ec;
}

// Convert the response xml into a result value
bool XmlRpcClient::parseResponse(const std::string& body, XmlRpcValue& result)
{
	// printf("parsing XmlRpc: %s\n", body.c_str());
	if (body.empty())
		return false;

	// Parse response xml into result
	size_t offset = 0;
	if (!XmlRpcUtil::findTag(METHODRESPONSE_TAG, body, offset))
	{
		Log(Error, "parseResponse: Invalid response - no methodResponse. Response: %s", body.c_str());
		return false;
	}

	// Expect either <params><param>... or <fault>...
	if ((XmlRpcUtil::nextTagIs(PARAMS.Begin, body, offset) && XmlRpcUtil::nextTagIs(PARAM.Begin, body, offset)) ||
	    (XmlRpcUtil::nextTagIs(FAULT_TAG, body, offset) && (_isFault = true)))
	{
		if (!result.fromXml(body, offset))
		{
			Log(Error, "parseResponse: Invalid response value, reading from %lu. Response: %s", offset, body.c_str());
			return false;
		}
	}
	else
	{
		Log(Error, "parseResponse: Invalid response - no param or fault tag. Response: %s", body.c_str());
		return false;
	}

	return result.valid();
}

void XmlRpcClient::on_message(imaxa_connection_hdl_ref hdl, const message_ptr& msg)
{
	if (!OnEvent)
		return;

	size_t offset = 0;
	LastStatusResult.clear();
	LastStatusResult.fromXml(msg->get_payload(), offset);
	OnEvent(LastStatusResult);
}

void XmlRpcClient::on_open(imaxa_connection_hdl_ref hdl)
{
	SingleWebsocketClient::on_open(hdl);
	bConnected = true;
}

void XmlRpcClient::on_close(imaxa_connection_hdl_ref hdl)
{
	SingleWebsocketClient::on_close(hdl);
	bConnected = false;
}

}    // namespace XmlRpc
