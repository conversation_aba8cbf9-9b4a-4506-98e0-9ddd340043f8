#pragma once

#include <string>

#include "web/ImaxaConnection.h"

namespace XmlRpc
{

// Representation of a parameter or result value
class XmlRpcValue;

// The XmlRpcServer processes client requests to call RPCs
class XmlRpcServer;

//! Abstract class representing a single RPC method
class XmlRpcServerMethod
{
   public:
	//! Constructor
	XmlRpcServerMethod(const std::string& name, XmlRpcServer* server = 0);
	//! Destructor
	virtual ~XmlRpcServerMethod() {}

	//! Returns the name of the method
	const std::string& name() const { return _name; }

	//! Execute the method. Subclasses must provide a definition for this method.
	virtual void execute(imaxa_connection_hdl_ref client, XmlRpcValue& params, XmlRpcValue& result) = 0;

	//! Returns a help string for the method.
	//! Subclasses should define this method if introspection is being used.
	virtual std::string help() const { return std::string(); }

   protected:
	std::string _name;
	XmlRpcServer* _server;
};
}    // namespace XmlRpc
