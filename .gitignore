# Compiled source #
###################
*.com
*.class
*.dll
*.exe
*.o
*.so
*.kdev4*
*.am
*.in
*.#*
*.a

# Logs #
######################
*.log

# Licenses #
######################
*.lic

# OS generated files #
######################
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
[Tt]humbs.db

# KEY and CERTIFICATE files #
*.pem
*.key
*.crt


# Build files #
######################
[Bb]uild/
[Bb]uild2_old/
Makefile

# Module folders #
######################

# Other folders #
######################
.kdev4/
billboard_720_2/
CMakeModules/
CMakeFiles/
.vscode/
.idea/
*.*~
docs/doxygen/

SQL_HISTORY_TDBPilot.txt
core

cmake-build*
docker_image_check

manifest.json
# Used by Jenkins to obtain version information
versionInfo.txt

# python stuff
__pycache__/
*.py[cod]

scripts/evolution/deploy/data/
*.i
