/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include "MyUtils.h"

#include <algorithm>
#include <boost/process.hpp>
#include <cctype>
#include <cmath>
#include <cstdio>
#include <ctime>
#include <map>
#include <regex>
#include <sstream>
#include <string>
#include <thread>

#include "Logger.h"
#include "TApplication.h"
#include "YUtils.h"

/* Tukaj so samo noinst funkcije */

namespace MyUtils
{

typedef std::pair<std::string, std::string> CharPair;

struct TSpecialChars
{
	std::unordered_map<std::string, std::string> LowerToUpper;
	std::unordered_map<std::string, std::string> UpperToLower;

	TSpecialChars(const std::initializer_list<CharPair>& pairs)
	{
		for (const auto& [lower, upper] : pairs)
		{
			LowerToUpper[lower] = upper;
			UpperToLower[upper] = lower;
		}
	}
};


static TSpecialChars SpecialChars = {
	CharPair("à", "À"), CharPair("á", "Á"), CharPair("â", "Â"), CharPair("ã", "Ã"), CharPair("ä", "Ä"), CharPair("å", "Å"), CharPair("æ", "Æ"), CharPair("ç", "Ç"),
	CharPair("è", "È"), CharPair("é", "É"), CharPair("ê", "Ê"), CharPair("ë", "Ë"), CharPair("ì", "Ì"), CharPair("í", "Í"), CharPair("î", "Î"), CharPair("ï", "Ï"),
	CharPair("ð", "Ð"), CharPair("ñ", "Ñ"), CharPair("ò", "Ò"), CharPair("ó", "Ó"), CharPair("ô", "Ô"), CharPair("õ", "Õ"), CharPair("ö", "Ö"), CharPair("ø", "Ø"),
	CharPair("ù", "Ù"), CharPair("ú", "Ú"), CharPair("û", "Û"), CharPair("ü", "Ü"), CharPair("ý", "Ý"), CharPair("þ", "Þ"), CharPair("ÿ", "Ÿ"), CharPair("ā", "Ā"),
	CharPair("ă", "Ă"), CharPair("ą", "Ą"), CharPair("ć", "Ć"), CharPair("ĉ", "Ĉ"), CharPair("ċ", "Ċ"), CharPair("č", "Č"), CharPair("ď", "Ď"), CharPair("đ", "Đ"),
	CharPair("ē", "Ē"), CharPair("ĕ", "Ĕ"), CharPair("ė", "Ė"), CharPair("ę", "Ę"), CharPair("ě", "Ě"), CharPair("ĝ", "Ĝ"), CharPair("ğ", "Ğ"), CharPair("ġ", "Ġ"),
	CharPair("ģ", "Ģ"), CharPair("ĥ", "Ĥ"), CharPair("ħ", "Ħ"), CharPair("ĩ", "Ĩ"), CharPair("ī", "Ī"), CharPair("ĭ", "Ĭ"), CharPair("į", "Į"), CharPair("ı", "İ"),
	CharPair("ĳ", "Ĳ"), CharPair("ĵ", "Ĵ"), CharPair("ķ", "Ķ"), CharPair("ĺ", "Ĺ"), CharPair("ļ", "Ļ"), CharPair("ľ", "Ľ"), CharPair("ŀ", "Ŀ"), CharPair("ł", "Ł"),
	CharPair("ń", "Ń"), CharPair("ņ", "Ņ"), CharPair("ň", "Ň"), CharPair("ŋ", "Ŋ"), CharPair("ő", "Ő"), CharPair("œ", "Œ"), CharPair("ŕ", "Ŕ"), CharPair("ŗ", "Ŗ"),
	CharPair("ř", "Ř"), CharPair("ś", "Ś"), CharPair("ŝ", "Ŝ"), CharPair("ş", "Ş"), CharPair("š", "Š"), CharPair("ţ", "Ţ"), CharPair("ť", "Ť"), CharPair("ŧ", "Ŧ"),
	CharPair("ũ", "Ũ"), CharPair("ū", "Ū"), CharPair("ŭ", "Ŭ"), CharPair("ů", "Ů"), CharPair("ű", "Ű"), CharPair("ų", "Ų"), CharPair("ō", "Ō"), CharPair("ŏ", "Ŏ"),
	CharPair("ŵ", "Ŵ"), CharPair("ŷ", "Ŷ"), CharPair("ź", "Ź"), CharPair("ż", "Ż"), CharPair("ƃ", "Ƃ"), CharPair("ƅ", "Ƅ"), CharPair("ƈ", "Ƈ"), CharPair("ƌ", "Ƌ"),
	CharPair("ƒ", "Ƒ"), CharPair("ƙ", "Ƙ"), CharPair("ơ", "Ơ"), CharPair("ƣ", "Ƣ"), CharPair("ƥ", "Ƥ"), CharPair("ƨ", "Ƨ"), CharPair("ƭ", "Ƭ"), CharPair("ư", "Ư"),
	CharPair("ƴ", "Ƴ"), CharPair("ƶ", "Ƶ"), CharPair("ƹ", "Ƹ"), CharPair("ǎ", "Ǎ"), CharPair("ǐ", "Ǐ"), CharPair("ǒ", "Ǒ"), CharPair("ǔ", "Ǔ"), CharPair("ǖ", "Ǖ"),
	CharPair("ǘ", "Ǘ"), CharPair("ǚ", "Ǚ"), CharPair("ǜ", "Ǜ"), CharPair("ƽ", "Ƽ"), CharPair("ǟ", "Ǟ"), CharPair("ǡ", "Ǡ"), CharPair("ǣ", "Ǣ"), CharPair("ǥ", "Ǥ"),
	CharPair("ǧ", "Ǧ"), CharPair("ǩ", "Ǩ"), CharPair("ǫ", "Ǫ"), CharPair("ǭ", "Ǭ"), CharPair("ǯ", "Ǯ"), CharPair("ǵ", "Ǵ"), CharPair("ǹ", "Ǹ"), CharPair("ž", "Ž"),
	CharPair("ǻ", "Ǻ"), CharPair("ǽ", "Ǽ"), CharPair("ǿ", "Ǿ"), CharPair("ȁ", "Ȁ"), CharPair("ȃ", "Ȃ"), CharPair("ȅ", "Ȅ"), CharPair("ȇ", "Ȇ"), CharPair("ȉ", "Ȉ"),
	CharPair("ȋ", "Ȋ"), CharPair("ȍ", "Ȍ"), CharPair("ȏ", "Ȏ"), CharPair("ȑ", "Ȑ"), CharPair("ȓ", "Ȓ"), CharPair("ȕ", "Ȕ"), CharPair("ȗ", "Ȗ"), CharPair("ș", "Ș"),
	CharPair("ț", "Ț"), CharPair("ȝ", "Ȝ"), CharPair("ȣ", "Ȣ"), CharPair("ȥ", "Ȥ"), CharPair("ȧ", "Ȧ"), CharPair("ȩ", "Ȩ"), CharPair("ȫ", "Ȫ"), CharPair("ȭ", "Ȭ"),
	CharPair("ȯ", "Ȯ"), CharPair("ȱ", "Ȱ"), CharPair("ȳ", "Ȳ"), CharPair("ɉ", "Ɉ"), CharPair("ɋ", "Ɋ"), CharPair("ɍ", "Ɍ"), CharPair("ȟ", "Ȟ"), CharPair("ȼ", "Ȼ"),
	CharPair("ɂ", "Ɂ"), CharPair("ɏ", "Ɏ"), CharPair("α", "Α"), CharPair("β", "Β"), CharPair("γ", "Γ"), CharPair("δ", "Δ"), CharPair("ε", "Ε"), CharPair("ζ", "Ζ"),
	CharPair("η", "Η"), CharPair("θ", "Θ"), CharPair("ι", "Ι"), CharPair("κ", "Κ"), CharPair("λ", "Λ"), CharPair("μ", "Μ"), CharPair("ν", "Ν"), CharPair("ξ", "Ξ"),
	CharPair("ο", "Ο"), CharPair("π", "Π"), CharPair("ρ", "Ρ"), CharPair("σ", "Σ"), CharPair("τ", "Τ"), CharPair("υ", "Υ"), CharPair("φ", "Φ"), CharPair("χ", "Χ"),
	CharPair("ψ", "Ψ"), CharPair("ω", "Ω"), CharPair("ϊ", "Ϊ"), CharPair("ϋ", "Ϋ"), CharPair("ϙ", "Ϙ"), CharPair("ϛ", "Ϛ"), CharPair("ϝ", "Ϝ"), CharPair("ϟ", "Ϟ"),
	CharPair("ϡ", "Ϡ"), CharPair("ϣ", "Ϣ"), CharPair("ϥ", "Ϥ"), CharPair("ϧ", "Ϧ"), CharPair("ϩ", "Ϩ"), CharPair("ϫ", "Ϫ"), CharPair("ϭ", "Ϭ"), CharPair("ϯ", "Ϯ"),
	CharPair("ϸ", "Ϸ"), CharPair("ϻ", "Ϻ")
};

char decimal_separator = ',';
char thousand_separator = '.';
std::string thousand_grouping = "\03";
std::string baseLocaleName = "C";

int currency_decimal_places = 2;

std::locale user_locale;

struct myseparators : std::numpunct<char>
{
	/*decimal separator*/
	char do_thousands_sep() const override { return thousand_separator; }
	char do_decimal_point() const override { return decimal_separator; }
	std::string do_grouping() const override { return thousand_grouping; }
};

std::string GetCurrentTimeString()
{
	time_t Time = time(NULL);
	char TimeString[50];
#ifdef OS_LINUX
	tm timestruct;
	localtime_r(&Time, &timestruct);
	asctime_r(&timestruct, TimeString);
	sprintf(TimeString, "%02d:%02d:%02d", timestruct.tm_hour, timestruct.tm_min, timestruct.tm_sec);
//	TimeString[strlen(TimeString)-1]='\0'; //brišemo '/n'
#else
	// Windows
	strcpy(TimeString, TimeToStr(Now()).c_str());
#endif
	return TimeString;
}

std::string GetCurrentTimeStampString()
{
	time_t Time = time(NULL);
	char TimeString[50];
#ifdef OS_LINUX
	tm timestruct;
	localtime_r(&Time, &timestruct);
	asctime_r(&timestruct, TimeString);
	sprintf(TimeString, "%02d:%02d:%02d %04d-%02d-%02d", timestruct.tm_hour, timestruct.tm_min, timestruct.tm_sec, timestruct.tm_year + 1900, timestruct.tm_mon + 1,
	        timestruct.tm_mday);
//	TimeString[strlen(TimeString)-1]='\0'; //brišemo '/n'
#else
	// Windows
	strcpy(TimeString, TimeToStr(Now()).c_str());
#endif
	return TimeString;
}

std::string GetCurrentDateString()
{
	time_t Time = time(NULL);
	char DateString[80];
	memset(DateString, 0, 80);
	tm timestruct;
	localtime_r(&Time, &timestruct);
	strftime(DateString, 79, "%F", &timestruct);
	return DateString;
}


bool IsTime(Uint64& LastTime, Uint64 Interval, bool UpdateLastTime)
{
	const Uint64 ticks = SDL_GetTicks64();
	if (ticks > LastTime + Interval)
	{
		if (UpdateLastTime)
			LastTime = ticks;
		return true;
	}
	else
		return false;
}

bool CompareTimes(Uint64 Time1, Uint64 Time2, Uint64 Diff)
{
	if (Diff > 0)
	{
		const Uint64 diff = std::abs((long)Time1 - (long)Time2);
		if (diff < Diff && Time1 >= Time2)
			return true;
	}
	else
	{
		if (Time1 >= Time2)
			return true;
	}

	return false;
}

void UpdateLocale(const std::string& baseLocale)
{
	baseLocaleName = baseLocale;
	user_locale = std::locale(std::locale(baseLocale), new myseparators());
}

std::string int2str(int64_t val)
{
	std::ostringstream sin;
	sin.imbue(MyUtils::user_locale);
	sin << val;
	return sin.str();
}

std::string currency2string(double value)
{
	return dtos(value, currency_decimal_places, true);
}

std::string dtos(double value, int precision, bool useLocale)
{
	std::ostringstream format_message;
	if (useLocale)
		format_message.imbue(MyUtils::user_locale);
	format_message.setf(std::ios::fixed, std::ios::floatfield);
	format_message.precision(precision);
	format_message << value;
	return format_message.str();
}

/* round number n to d decimal places */
double round(double n, unsigned d)
{
	return floor(n * pow(10., d) + .5) / pow(10., d);
}
// čisto na hitrco narejena funkcija - ne dela za negativna ...

/**
 * @brief Function to find the greatest common divisor (GCD) using the Euclidean algorithm
 * @param a numerator
 * @param b denominator
 * @returns greatest common divisor
 */
uint32_t greatestCommonDivisor(uint32_t a, uint32_t b)
{
	while (b != 0)
	{
		uint32_t temp = b;
		b = a % b;
		a = temp;
	}
	return a;
}

// Function to convert a decimal to a fraction
/**
 *
 * @brief Function to convert a decimal to a fraction
 * @param decimal decimal number to convert
 * @param numerator numerator of the fraction
 * @param denominator denominator of the fraction
 */
void decimalToFraction(double decimal, uint32_t& numerator, uint32_t& denominator)
{
	const uint32_t precision = 1000000;    // Precision to consider for the decimal conversion
	denominator = precision;
	numerator = static_cast<uint32_t>(std::round(decimal * precision));

	const uint32_t commonDivisor = greatestCommonDivisor(numerator, denominator);

	numerator /= commonDivisor;
	denominator /= commonDivisor;
}

constexpr int utf8_strlen(const char chr)
{
	unsigned char c = chr;
	if ((c & 0b10000000) == 0)
		return 1;
	else if ((c & 0xE0) == 0xC0)
		return 2;
	else if ((c & 0xF0) == 0xE0)
		return 3;
	else if ((c & 0xF8) == 0xF0)
		return 4;
	else if ((c & 0xFC) == 0xF8)
		return 5;
	else if ((c & 0xFE) == 0xFC)
		return 6;
	else
		return 0;    // invalid utf8
}

uint32_t GetUTF8CodePoint(const char* u, const char** end)
{
	int l = utf8_strlen(u[0]);
	if (end)
		*end = u + l;

	if (l < 1)
		return 0;
	unsigned char u0 = u[0];
	if (u0 <= 127)
		return u0;
	if (l < 2)
		return 0;
	unsigned char u1 = u[1];
	if (u0 >= 192 && u0 <= 223)
		return (u0 - 192) * 64 + (u1 - 128);
	if (u0 == 0xed && (u1 & 0xa0) == 0xa0)
		return 0;    // code points, 0xd800 to 0xdfff
	if (l < 3)
		return 0;
	unsigned char u2 = u[2];
	if (u0 >= 224 && u0 <= 239)
		return (u0 - 224) * 4096 + (u1 - 128) * 64 + (u2 - 128);
	if (l < 4)
		return 0;
	unsigned char u3 = u[3];
	if (u0 >= 240 && u0 <= 247)
		return (u0 - 240) * 262144 + (u1 - 128) * 4096 + (u2 - 128) * 64 + (u3 - 128);
	return 0;
}

std::string ToUppercase(const std::string& Input, bool OnlyFirst)
{
	const unsigned int s = OnlyFirst ? utf8_strlen(Input[0]) : Input.size();

	std::string newString;

	for (size_t i = 0; i < s;)
	{
		if (Input.at(i) >= 0)
			newString.push_back(std::toupper(Input.at(i++)));    // ce je navadna crka, jo samo dodamo
		else
		{
			std::ostringstream os;
			os << std::hex;
			const size_t end = i + utf8_strlen(Input[i]);
			for (; i < end; i++) { os << Input[i]; }

			std::string chr = os.str();
			auto find = SpecialChars.LowerToUpper.find(chr);
			if (find == SpecialChars.LowerToUpper.end())
				newString += chr;
			else
				newString += find->second;
		}
	}
	return newString;
}

std::string ToLowercase(const std::string& Input, bool SkipFirst)
{
	std::string newString;
	size_t i = 0;
	if (SkipFirst)
	{
		i = utf8_strlen(Input[0]);
		newString += Input.substr(0, i);
	}


	for (; i < Input.size();)
	{
		if (Input.at(i) >= 0)
			newString.push_back(std::tolower(Input.at(i++)));    // ce je navadna crka, jo samo dodamo
		else
		{
			std::ostringstream os;
			os << std::hex;
			const size_t end = i + utf8_strlen(Input[i]);
			for (; i < end; i++) { os << Input[i]; }

			std::string chr = os.str();
			auto find = SpecialChars.UpperToLower.find(chr);
			if (find == SpecialChars.UpperToLower.end())
				newString += chr;
			else
				newString += find->second;
		}
	}
	return newString;
}

bool ContainsURL(const std::string& str)
{
	const std::regex urlPattern(R"((http|https|ftp|ftps)://[^\s/$.?#].[^\s]*)");
	return std::regex_search(str, urlPattern);
}

void NumberToText(const char* only_number /*number in text form - max 60 digits*/, char* outputString)
{
	if (only_number == std::string("0"))
	{
		strcpy(outputString, "ZERO ");
		return;
	}

	char one_array[10][11] = { " ", "ONE ", "TWO ", "THREE ", "FOUR ", "FIVE ", "SIX ", "SEVEN ", "EIGHT ", "NINE " };
	char ten_array[10][11] = { "TEN ", "ELEVEN ", "TWELVE ", "THIRTEEN ", "FOURTEEN ", "FIFTEEN ", "SIXTEEN ", "SEVENTEEN ", "EIGHTEEN ", "NINETEEN " };
	char twenty_array[10][11] = { " ", " ", "TWENTY ", "THIRTY ", "FOURTY ", "FIFTY ", "SIXTY ", "SEVENTY ", "EIGHTY ", "NINETY " };
	char big_unit_array[22][20] = { "VIGINTILLION",      "NOVEMDECILLION", "OCTODECILLION", "SEPTENDECILLION", "SEXDECILLION", "QUINDECILLION",
		                            "QUATTUORDECILLION", "TREDECILLION",   "DUODECILLION",  "UNDECILLION",     "DECILLION",    "NONILLION",
		                            "OCTILLION",         "SEPTILLION",     "SEXTILLION",    "QUINTILLION",     "QUADRILLION",  "TRILLION ",
		                            "BILLION ",          "MILLION ",       "THOUNSAND ",    "HUNDRED " };
	const int MaxUnit = 21;
	char tempString[150][20];
	memset(tempString, 0, 150 * 20);
	// E.g. 1,234,567,890,123
	int i;    // for only_number
	int j;    // for tempString
	int rem;
	int sizeofNumber = strlen(only_number);
	int tflag = 0;
	for (i = 0, j = 0; i < sizeofNumber; i++)
	{
		rem = (sizeofNumber - i) % 3;
		if (!rem)
			tflag = 0;
		if (only_number[i] != '0')
		{
			if (rem == 1)    // in oneth position
			{
				strcpy(tempString[j++], one_array[only_number[i] - 48]);
				tflag = 1;
			}
			else if (rem == 2)
			{    // in tenth position
				if (only_number[i] == '1')
					strcpy(tempString[j++], ten_array[only_number[++i] - 48]);
				else
					strcpy(tempString[j++], twenty_array[only_number[i] - 48]);
				tflag = 1;
			}
			else if (rem == 0)
			{    // in hundredth position
				strcpy(tempString[j++], one_array[only_number[i] - 48]);
				strcpy(tempString[j++], big_unit_array[MaxUnit]);
				tflag = 1;
			}
		}
		if (tflag)
		{
			switch (sizeofNumber - i)
			{
					// select which big_unit_array to be placed behind the number
				case 64: strcpy(tempString[j++], big_unit_array[MaxUnit - 21]); break;
				case 61: strcpy(tempString[j++], big_unit_array[MaxUnit - 20]); break;
				case 58: strcpy(tempString[j++], big_unit_array[MaxUnit - 19]); break;
				case 55: strcpy(tempString[j++], big_unit_array[MaxUnit - 18]); break;
				case 52: strcpy(tempString[j++], big_unit_array[MaxUnit - 17]); break;
				case 49: strcpy(tempString[j++], big_unit_array[MaxUnit - 16]); break;
				case 46: strcpy(tempString[j++], big_unit_array[MaxUnit - 15]); break;
				case 43: strcpy(tempString[j++], big_unit_array[MaxUnit - 14]); break;
				case 40: strcpy(tempString[j++], big_unit_array[MaxUnit - 13]); break;
				case 37: strcpy(tempString[j++], big_unit_array[MaxUnit - 12]); break;
				case 34: strcpy(tempString[j++], big_unit_array[MaxUnit - 11]); break;
				case 31: strcpy(tempString[j++], big_unit_array[MaxUnit - 10]); break;
				case 28: strcpy(tempString[j++], big_unit_array[MaxUnit - 9]); break;
				case 25: strcpy(tempString[j++], big_unit_array[MaxUnit - 8]); break;
				case 22: strcpy(tempString[j++], big_unit_array[MaxUnit - 7]); break;
				case 19: strcpy(tempString[j++], big_unit_array[MaxUnit - 6]); break;
				case 16: strcpy(tempString[j++], big_unit_array[MaxUnit - 5]); break;
				case 13: strcpy(tempString[j++], big_unit_array[MaxUnit - 4]); break;
				case 10: strcpy(tempString[j++], big_unit_array[MaxUnit - 3]); break;
				case 7: strcpy(tempString[j++], big_unit_array[MaxUnit - 2]); break;
				case 4: strcpy(tempString[j++], big_unit_array[MaxUnit - 1]); break;
			}
		}
	}
	j--;
	// to minus an extra increment of j
	// paste text from tempString to outputString
	strcpy(outputString, tempString[0]);
	for (i = 1; i <= j; i++) strcat(outputString, tempString[i]);
}
/* End of NumberToText() */


/* funkcija pretvori Data, ki je estnajstiki zapis tevila v string obliki("23fc") v estnajstiki zapis tevila v dvojiki obliki(0x23 0xFC HexLength = 2)
dela na obstoje�m bufferju - Data se spremeni po izvedbi te funkcije!!!zadnji byti se pretvorijo v dvojiki zapis hex.
HexLength - je vhodno/izhodni parameter in pove maximalno dolino binarnega hex zapisa, ob izhodu pa koliko bytov je bilo zapisanih v hex */
unsigned char* ASCIIHexToBinHex(const char* pString, unsigned char* pResult, unsigned* HexLength)
{
	if (!pString || !pResult || !HexLength)
		return NULL;

	unsigned char tmp1, tmp2;

	const unsigned len = strlen(pString);
	long pass = (long)(len / 2) + (len % 2) - 1;
	if (pass > (long)((*HexLength) - 1))
		pass = (*HexLength) - 1;

	int i = ((pass + 1) * 2) - 1;
	*HexLength = 0;
	for (; i > 0; i -= 2)
	{
		tmp1 = pString[i];
		if (tmp1 > 96)    // gre za malo �ko
			tmp1 -= 32;    // to uppercase
		tmp1 = tmp1 - (tmp1 > 64 ? 55 : 48);

		tmp2 = pString[i - 1];
		if (tmp2 > 96)    // gre za malo �ko
			tmp2 -= 32;    // to uppercase
		tmp2 = tmp2 - (tmp2 > 64 ? 55 : 48);

		pResult[pass--] = tmp1 + (tmp2 << 4);
		*HexLength = (*HexLength) + 1;
	}

	return &pResult[pass + 1];
}

/*
   funkcija pretvori hex zapis v binarni obliki(npr. 0x23 0xfc DataLength=2) v zapis ASCII z
   enako predstavitvijo(vrne "23FC" DataLength=4+1)
*/
char* BinHexToASCIIHex(unsigned char* Data, char* pDestination, unsigned* DataLength)
{
	if (!Data || !pDestination || !DataLength)
		return NULL;

	// unsigned pass=0;
	int y = (*DataLength);
	*DataLength = 0;
	for (int i = 0; i < y; i++)
	{
		pDestination[*DataLength] = ((Data[i] & 0xf0) >> 4) + (((Data[i] & 0xf0) >> 4) > 9 ? 55 : 48);
		*DataLength = (*DataLength) + 1;
		pDestination[*DataLength] = (Data[i] & 0x0f) + ((Data[i] & 0x0f) > 9 ? 55 : 48);
		*DataLength = (*DataLength) + 1;
	}
	pDestination[*DataLength] = '\0';

	return pDestination;
}

void SystemAsync(const std::string& cmd, bool bAsChild)
{
	const auto start = std::chrono::system_clock::now();
	rtfwk_sdl2::pApp->LaunchNewWorker([cmd, bAsChild, start]() {
		TLOG(LogApp, Normal, "Executing system command after waiting in queue for %.1fms: %s",
		     std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now() - start).count() * 1e-3f, cmd.c_str())
		if (bAsChild)
			system(cmd.c_str());
		else
			system(("systemd-run " + cmd).c_str());
	});
}

void StringReplace(std::string& str, const std::string& from, const std::string& to)
{
	size_t start_pos = 0;
	while ((start_pos = str.find(from, start_pos)) != std::string::npos)
	{
		str.replace(start_pos, from.length(), to);
		start_pos += to.length();    // Handles case where 'to' is a substring of 'from'
	}
}

int SystemExec(const std::string& cmd, std::vector<std::string>& outLines)
{
	std::vector<std::string> args { "-c", cmd };
	boost::process::ipstream out;
	boost::process::child c(boost::process::search_path("sh"), args, boost::process::std_out > out, boost::process::std_err > boost::process::null);

	for (std::string line; c.running() && std::getline(out, line);) { outLines.push_back(line); }
	c.wait();

	return c.exit_code();
}

bool FolderExists(const std::filesystem::path& path)
{
	return std::filesystem::exists(path) && std::filesystem::is_directory(path);
}

bool FileExists(const std::filesystem::path& path, bool checkIfRegFile, bool checkIfLink)
{
	std::error_code ec;
	if (std::filesystem::exists(path, ec) && !ec)
	{
		bool ok = true;
		if (checkIfRegFile && checkIfLink)
		{
			ok &= std::filesystem::is_regular_file(path) || std::filesystem::is_symlink(path);
		}
		else if (checkIfRegFile)
			ok &= std::filesystem::is_regular_file(path);
		else if (checkIfLink)
			ok &= std::filesystem::is_symlink(path);
		return ok;
	}
	else
	{
		return false;
	}
}

std::string CreateLoggableTicketNumber(const std::string& ticketValidationNumber)
{
	std::string loggedTicketString = ticketValidationNumber;
	StringReplace(loggedTicketString, "-", "");
	const size_t endOfStarsIndex = std::max(4LU, loggedTicketString.size()) - 4;
	std::string stars(endOfStarsIndex, '*');
	stars += loggedTicketString.substr(endOfStarsIndex);
	auto blocks = yutils::Split(ticketValidationNumber, "-", false);
	std::string outString;
	size_t index = 0;
	for (size_t i = 0; i < blocks.size(); i++)
	{
		if (blocks[i].empty())
			continue;
		if (i > 0)
			outString += "-";
		outString += stars.substr(index, blocks[i].length());
		index += blocks[i].length();
	}
	return outString;
}

void UnescapeXml(std::string& xml)
{
	MyUtils::StringReplace(xml, "&quot;", "\"");
	MyUtils::StringReplace(xml, "&apos;", "'");
	MyUtils::StringReplace(xml, "&amp;", "&");
	MyUtils::StringReplace(xml, "&lt;", "<");
	MyUtils::StringReplace(xml, "&gt;", ">");
}

int PostSDLEvent(int UserCode, void* pUserData1, void* pUserData2)
{
	SDL_Event event;
	event.type = SDL_USEREVENT;
	event.user.code = UserCode;
	event.user.data1 = pUserData1;
	event.user.data2 = pUserData2;
	return SDL_PushEvent(&event);
}

std::vector<FRemovableMediaInformation> GetAllStorageUSBs()
{
	std::map<std::string, std::string> DisksByLabel;
	std::filesystem::directory_iterator labelIter("/dev/disk/by-label");
	for (const std::filesystem::directory_entry& entry : labelIter)
	{
		if (!entry.is_symlink())
			continue;

		std::string driveLabel(entry.path().filename().string());
		size_t nextEscapeSequence = std::string::npos;
		while ((nextEscapeSequence = driveLabel.find("\\x")) != std::string::npos)
		{
			const int character = std::stoi(driveLabel.substr(nextEscapeSequence + 2, 2), NULL, 16);
			driveLabel.replace(nextEscapeSequence, 4, 1, (char)character);
		}

		DisksByLabel.emplace(std::filesystem::read_symlink(entry.path()).filename().string(), driveLabel);
	}

	std::map<std::filesystem::path, std::filesystem::path> MountMap;
	std::ifstream mountMapFile("/etc/mtab");
	if (mountMapFile.is_open())
	{
		std::string line;
		while (!mountMapFile.eof() && std::getline(mountMapFile, line))
		{
			const auto columns = yutils::Split(line, " ", true);
			if (columns.size() < 2)
				continue;
			MountMap.emplace(columns[0], columns[1]);
		}
	}

	for (const std::filesystem::directory_entry& entry : labelIter)
	{
		if (!entry.is_symlink())
			continue;

		std::string driveLabel(entry.path().filename().string());
		size_t nextEscapeSequence = std::string::npos;
		while ((nextEscapeSequence = driveLabel.find('\\')) != std::string::npos)
		{
			const int character = std::stoi(driveLabel.substr(nextEscapeSequence + 1, 3), NULL, 16);
			driveLabel.replace(nextEscapeSequence, 4, 1, (char)character);
		}

		DisksByLabel.emplace(std::filesystem::read_symlink(entry.path()).filename().string(), driveLabel);
	}

	std::vector<FRemovableMediaInformation> ret;

	std::filesystem::directory_iterator iter("/sys/block");
	for (const std::filesystem::directory_entry& entry : iter)
	{
		if (!entry.is_directory())
			continue;

		bool bRemovable = false;
		{
			std::ifstream removableFile(entry.path() / "removable");
			if (removableFile.is_open())
				removableFile >> bRemovable;
		}

		if (!bRemovable)
			continue;

		FRemovableMediaInformation info;
		info.Vendor = "Unknown vendor";
		info.Model = "Unknown model";

		{
			std::ifstream readOnlyFlag(entry.path() / "ro");
			if (readOnlyFlag.is_open())
				readOnlyFlag >> info.bReadOnly;
		}

		{
			std::ifstream vendorFile(entry.path() / "device" / "vendor");
			if (vendorFile.is_open())
			{
				std::getline(vendorFile, info.Vendor);
				yutils::TrimInPlace(info.Vendor);
			}
		}
		{
			std::ifstream modelFile(entry.path() / "device" / "model");
			if (modelFile.is_open())
			{
				std::getline(modelFile, info.Model);
				yutils::TrimInPlace(info.Model);
			}
		}

		std::filesystem::directory_iterator iter2(entry.path() / "device" / "block");
		for (const std::filesystem::directory_entry& entry2 : iter2)
		{
			if (!entry2.is_directory())
				continue;
			{
				std::ifstream sizeFile(entry2.path() / "size");
				if (sizeFile.is_open())
				{
					size_t sizeInBlocks512;
					sizeFile >> sizeInBlocks512;    // reported in 512 byte chunks
					info.SizeMB += (sizeInBlocks512 * 512) / (1000 * 1000);
				}
			}
		}

		info.ConnectedTime = std::filesystem::last_write_time(entry.path());

		for (size_t idx = 1;; idx++)
		{
			const std::string partitionDevFileName = entry.path().filename().string() + std::to_string(idx);
			const std::filesystem::path pathToPartition(entry.path() / partitionDevFileName);
			if (!std::filesystem::exists(pathToPartition))
				break;

			FPartitionInfo partition;
			auto find = MountMap.find("/dev/" + partitionDevFileName);
			if (find != MountMap.end())
				partition.MountedPath = find->second;

			auto findLabel = DisksByLabel.find(partitionDevFileName);
			if (findLabel != DisksByLabel.end())
				partition.Label = findLabel->second;

			{
				std::ifstream readOnlyFlag(pathToPartition / "ro");
				if (readOnlyFlag.is_open())
					readOnlyFlag >> partition.bReadOnly;
			}

			{
				std::ifstream sizeFile(pathToPartition / "size");
				if (sizeFile.is_open())
				{
					size_t sizeInBlocks512;
					sizeFile >> sizeInBlocks512;    // reported in 512 byte chunks
					partition.SizeMB += (sizeInBlocks512 * 512) / (1000 * 1000);
				}
			}

			if (!partition.SizeMB)
				continue;

			info.Partitions.emplace_back(std::move(partition));
		}

		ret.emplace_back(std::move(info));
	}
	return ret;
}

}    // namespace MyUtils
