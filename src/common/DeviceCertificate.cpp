#include "common/DeviceCertificate.h"

#include <boost/asio/ip/host_name.hpp>
#include <fstream>

#include "Cryptography.h"
#include "common/DeviceFilesystemStatics.h"

web::FCertificates igp::LoadDeviceCertFromFolder(const std::filesystem::path& folder, const std::string& overrideHostname, const std::string& overridePassword)
{
	return igp::LoadCustomDeviceCert(folder / CertificateFilename, folder / PrivateKeyFilename, overrideHostname, overridePassword);
}

web::FCertificates igp::LoadCustomDeviceCert(const std::filesystem::path& cert, const std::filesystem::path& privateKey, const std::string& overrideHostname,
                                             const std::string& overridePassword)
{
	web::FCertificates certInfo;
	certInfo.CertificateChainFile = cert;
	certInfo.PrivateKeyFile = privateKey;
	if (!certInfo.PrivateKeyFile.empty())
	{
		if (overridePassword.empty())
		{
			std::ifstream uuidFile("/var/lib/dbus/machine-id");
			if (uuidFile.is_open())
			{
				std::string uuid;
				std::getline(uuidFile, uuid);
				uuidFile.close();
				certInfo.PrivateKeyPassword =
				  crypto::Hash((overrideHostname.empty() ? boost::asio::ip::host_name() : overrideHostname) + uuid + "imaxa", EHashAlgorithm::SHA256);
			}
		}
		else
		{
			certInfo.PrivateKeyPassword = overridePassword;
		}
	}
	return certInfo;
}
