#include "common/TTimetable.h"

#include <time.h>

#include <ctime>

TTimetable::TTimetable(const std::string& name, const std::string& from, const std::string& to)
{
	Name = name;

	auto now = std::chrono::system_clock::now();
	time_t now_timet = std::chrono::system_clock::to_time_t(now);
	tm tmCurrent;
	localtime_r(&now_timet, &tmCurrent);

	tm tmLocalFrom = tmCurrent;
	strptime(from.c_str(), "%H:%M", &tmLocalFrom);    // ure in minute preberemo iz timetable.json
	tmLocalFrom.tm_sec = 0;

	From = std::chrono::system_clock::from_time_t(mktime(&tmLocalFrom));

	tm tmLocalTo = tmCurrent;
	strptime(to.c_str(), "%H:%M", &tmLocalTo);    // ure in minute preberemo iz timetable.json
	tmLocalTo.tm_sec = 0;

	To = std::chrono::system_clock::from_time_t(mktime(&tmLocalTo));

	// popravimo from in to glede na trenuten cas
	if (From >= To)
	{
		if (now < To)
		{
			From -= std::chrono::days(1);    // from zamaknemo za en dan nazaj
		}
		else
		{
			To += std::chrono::days(1);    // to zamaknemo za en dan naprej
		}
	}

	time_t time_tUTCFrom = mktime(&tmLocalFrom);
	tm tmUTCFrom;
	gmtime_r(&time_tUTCFrom, &tmUTCFrom);

	time_t time_tUTCTo = mktime(&tmLocalTo);
	tm tmUTCTo;
	gmtime_r(&time_tUTCTo, &tmUTCTo);

	char buf[30];
	std::strftime(buf, sizeof(buf), "%R", &tmLocalFrom);
	LocalFromTime = buf;

	std::strftime(buf, sizeof(buf), "%R", &tmLocalTo);
	LocalToTime = buf;

	std::strftime(buf, sizeof(buf), "%R", &tmUTCFrom);
	UTCFromTime = buf;

	std::strftime(buf, sizeof(buf), "%R", &tmUTCTo);
	UTCToTime = buf;
}

bool TTimetable::IsCurrentTimeBetween(int time_shift /* = 0*/) const
{
	auto now = std::chrono::system_clock::now() + std::chrono::hours(time_shift);

	return (now >= From) && (now < To);
}

std::string TTimetable::GetLocalFormatedString() const
{
	return LocalFromTime + " - " + LocalToTime;
}

std::string TTimetable::GetUTCFormatedString() const
{
	return UTCFromTime + " UTC - " + UTCToTime + " UTC";
}
