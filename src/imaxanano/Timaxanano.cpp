#include "Timaxanano.h"

#include <bitset>
#include <execution>
#include <optional>

#include "Cryptography.h"
#include "Currencies.h"
#include "MyUtils.h"
#include "NanoPlayerActions.h"
#include "NanoSharedTypes.h"
#include "TConfiguration.h"
#include "Timing.h"
#include "YUtils.h"
#include "common/FileUtils.h"
#include "common/TDBManager.h"
#include "common/THash.h"
#include "rocksdb/iterator.h"
#include "terminal/TCounterNames.h"
#include "web/WebServer.h"
#include "web/WebUtils.h"

#define DEFAULT_NANO_PORT 10036

MAIN_METHOD(Timaxanano, "NANO")

using namespace credit;
using namespace yutils;

DEFINE_LOG_CATEGORY(LogNano, "nano")

const std::string SALT_KEY = "beep-boop-im-a-train";

const std::string PLAYER_ID = "PLAYER";
const std::string USER_HANDPAY_PARAM = "HANDPAY_USER";
const std::string SYS_HANDPAY_PARAM_PREFIX = "HANDPAY_SYS_";

// A client IP used to aggregate all the counters from players that were removed
const std::string DEAD_PLAYER_CLIENT_IP = "DEAD_PLAYERS";

const std::string GAMEROUND_FILE_EXTENSION = ".ground";
const std::string TOKEN_FILE_EXTENSION = ".token";

const std::string NANO_KEY_CLIENT_ID = "NANOAUTH";

constexpr size_t SESSION_BYTE_SIZE = 8;

constexpr size_t PLAYER_HISTORY_LIMIT = 200;

constexpr const char* KIOSK_RESET_CONFIG_NAME = "kiosk-reset";
const std::set<std::string> RESERVED_CONFIG_NAMES = { KIOSK_RESET_CONFIG_NAME };
const std::unordered_set<int> BasePeriodicCounters = { ATTENDANT_COUNTER1_IN, ATTENDANT_COUNTER2_IN, OWNER_COUNTER1_IN, OWNER_COUNTER2_IN };

std::unordered_set<int> GetAllPeriodicCounters()
{
	std::unordered_set<int> all;
	for (int counter : BasePeriodicCounters)
	{
		all.insert(counter);
		all.insert(counter + 1);
		all.insert(counter + 2);
		all.insert(counter + 3);
	}
	return all;
}

constexpr uint64_t pow10(size_t n)
{
	uint64_t retVal = 1;
	while (n--) retVal *= 10;
	return retVal;
}

// we have to do this and not a normal hash because ticket printer accepts up to 18 digits, and 2 of those are reserved
constexpr uint64_t MAX_TRANSFER_TOKEN_ID = pow10(16) - 1;

json CreditJSON(const credit::CreditArray& credits)
{
	return json(static_cast<const std::array<int64_t, CREDIT_ALL>&>(credits));
}

credit::CreditArray CreditFromJSON(const json& credits)
{
	if (credits.is_array() && credits.size() == credit::CREDIT_ALL)
	{
		try
		{
			CreditArray Result;
			for (int i = 0; i < credit::CREDIT_ALL; i++) Result[i] = credits[i].get<int64_t>();
			return Result;
		}
		catch (...)
		{
			return {};
		}
	}
	throw nano_parse_error("Credit array is not valid");
}

BETTER_ENUM(ENanoMessageType, uint8_t, Ping, List)

const JsonSchema NanoMessageSchema = JsonSchema({ { "time", JsonSchema(json::value_t::number_unsigned, "The timestamp this message was sent at") },
                                                  { "body", JsonSchema(json::value_t::null, "The body of this message") },
                                                  { "type", JsonSchema(json::value_t::string, "The type of this message").SetToEnumType<ENanoMessageType>() } });
class nano_message : public TConfiguration
{
   public:
	ENanoMessageType type = ENanoMessageType::Ping;
	uint64_t time;
	json body;

	nano_message() { Schema() = NanoMessageSchema; }

	virtual void OnConfigLoaded(const std::filesystem::path& loc) override
	{
		type = ENanoMessageType::_from_string_nocase(GetConfig("type").get<std::string>().c_str());
		time = GetConfig("time").get<uint64_t>();
		body = GetConfig("body");
		uint64_t now = ytime::GetSystemTimeMsec();
		if (time + 1000 < now)
			throw nano_parse_error("message is too old!");
		if (time > now + 1000)
			throw nano_parse_error("message is from the future, please use the server from 2064 instead!");
	}
};

web::websockets::session::validation::value Timaxanano::accept_ws(const imaxa_connection_ptr& con)
{
	std::string resource = con->get_resource();
	resource = resource.substr(0, resource.find_first_of('?'));
	auto fragments = Split(resource, "/", true);

	if (fragments.empty())
		return websocketpp::session::validation::value::reject;

	if (fragments[0] == "session" && fragments.size() == 2)
	{
		std::shared_ptr<Session> sessionPtr;
		{
			SharedScopedLock lock(mSessions);
			auto session = mSessions->find(fragments[1]);
			if (session == mSessions->end())
				return websocketpp::session::validation::value::reject;

			sessionPtr = session->second.lock();
		}

		if (!sessionPtr)
			return websocketpp::session::validation::value::reject;

		const web::QueryString query(con->get_uri()->get_query());
		const GameIdentifier game(query.contains("game") ? query.Get("game") : std::string());
		if (game.Game >= MAX_GAMES)
			return websocketpp::session::validation::value::reject;

		ScopedLock lock2(sessionPtr->Data);
		sessionPtr->Data.Game = game;

		if (auto con2 = sessionPtr->Data.Stream.lock())
		{
			std::error_code ec;
			con2->close(websocketpp::close::status::normal, "Session taken by someone else", ec);
			Log(Warning, "Closed player stream of %s because someone else requested this session!", sessionPtr->Player.lock()->Username.c_str());
		}

		sessionPtr->Data.Stream = con;

		con->set_open_handler(std::bind(&Timaxanano::on_open_session, this, std::placeholders::_1, sessionPtr));
		con->set_close_handler(std::bind(&Timaxanano::on_close_session, this, std::placeholders::_1, sessionPtr));
	}
	else if (fragments[0] == "player" && fragments.size() == 2)
	{
		std::shared_ptr<NanoPlayer> playerPtr;
		{
			SharedScopedLock lock(mPlayerData);
			auto player = mPlayerData->find(fragments[1]);
			if (player == mPlayerData->end())
				return websocketpp::session::validation::value::reject;
			playerPtr = player->second;
			playerPtr->Lock();
		}

		playerPtr->PlayerStreams.insert(con);
		{
			ScopedLock lock(mPlayerListeners);
			mPlayerListeners->emplace(con, playerPtr);
		}
		playerPtr->Unlock();
		con->set_message_handler(std::bind(&Timaxanano::handle_player_message, this, playerPtr.get(), std::placeholders::_1, std::placeholders::_2));
		con->set_open_handler(std::bind(&Timaxanano::on_open_player, this, std::placeholders::_1));
		con->set_close_handler(std::bind(&Timaxanano::on_close_player, this, std::placeholders::_1));
	}
	else if (fragments.size() == 1 && fragments[0] == "all")
	{
		{
			ScopedLock lock(mGeneralListeners);
			mGeneralListeners->emplace(con, NanoEventListener({ crypto::GenRandID(8) }));
		}
		con->set_open_handler(std::bind(&Timaxanano::on_open_general, this, std::placeholders::_1));
		con->set_close_handler(std::bind(&Timaxanano::on_close_general, this, std::placeholders::_1));
	}
	else
	{
		return websocketpp::session::validation::value::reject;
	}

	return websocketpp::session::validation::value::accept;
}

void Timaxanano::on_open_session(imaxa_connection_hdl_ref hdl, std::shared_ptr<Session> session)
{
	if (!session)
	{
		if (auto con = hdl.lock())
		{
			std::error_code ec;
			con->close(websocketpp::close::status::subprotocol_error, "Potato", ec);
		}
		return;
	}

	auto player = session->Player.lock();
	if (player)
	{
		player->Lock();
		player->SetOnline(true);
		player->Unlock();
	}
}

void Timaxanano::on_open_player(imaxa_connection_hdl_ref hdl)
{
	ScopedLock lock(mPlayerListeners);
	auto find_client = mPlayerListeners->find(hdl);
	if (find_client == mPlayerListeners->end())
	{
		if (auto con = hdl.lock())
		{
			std::error_code ec;
			con->close(websocketpp::close::status::subprotocol_error, "Cement", ec);
		}
		return;
	}
	auto player = find_client->second.lock();

	if (!player)
	{
		mPlayerListeners->erase(find_client);
		if (auto con = hdl.lock())
		{
			std::error_code ec;
			con->close(websocketpp::close::status::subprotocol_error, "Potato", ec);
		}
		return;
	}
}

void Timaxanano::on_open_general(imaxa_connection_hdl_ref hdl)
{
	std::error_code ec;
	SharedScopedLock lock(mGeneralListeners);
	auto find_client = mGeneralListeners->find(hdl);
	if (find_client == mGeneralListeners->end())
	{
		if (auto con = hdl.lock())
			con->close(websocketpp::close::status::subprotocol_error, "Cement", ec);
	}
	else
	{
		json welcome(json::value_t::object);
		welcome["event"] = "welcome";
		welcome["context"] = find_client->second.ID;
		if (auto con = hdl.lock())
			ec = con->send(welcome.dump(), websocketpp::frame::opcode::TEXT);
	}
}

void Timaxanano::on_close_session(imaxa_connection_hdl_ref hdl, std::shared_ptr<Session> session)
{
	if (!session)
		return;

	{
		ScopedLock lock(session->Data);
		if (!session->Data.Stream.expired() && session->Data.Stream.lock() == hdl.lock())
		{
			session->Data.Stream.reset();
			if (!session->Data.HasActiveGameround())    // if session has active game round when its closed, it could mean that we will have to restore the session later,
			                                            // and we need to know to which game we account the transaction to
				session->Data.Game = GameIdentifier();
		}
	}

	auto player = session->Player.lock();
	if (player)
	{
		ScopedLock lock(*player);
		bool bOnline = false;
		for (const auto& s : player->Sessions)
		{
			ScopedLock lock2(s.second->Data);
			if (s.second->Data.Stream.lock())
			{
				bOnline = true;
				break;
			}
		}
		player->SetOnline(bOnline);
	}
}

void Timaxanano::on_close_player(imaxa_connection_hdl_ref hdl)
{
	std::shared_ptr<NanoPlayer> player;
	{
		ScopedLock lock(mPlayerListeners);
		auto find = mPlayerListeners->find(hdl);
		if (find != mPlayerListeners->end())
		{
			player = find->second.lock();
			mPlayerListeners->erase(find);
		}
	}

	if (player)
	{
		ScopedLock lock(*player);
		player->PlayerStreams.erase(hdl);
	}
}

void Timaxanano::on_close_general(imaxa_connection_hdl_ref hdl)
{
	ScopedLock lock(mGeneralListeners);
	mGeneralListeners->erase(hdl);
}

void Timaxanano::on_message(imaxa_connection_hdl_ref hdl, const message_ptr& msg)
{
	{
		SharedScopedLock lock(mGeneralListeners);
		auto clientIt = mGeneralListeners->find(hdl);
		if (clientIt == mGeneralListeners->end())
			return;
	}

	// Read json.
	json messageObject;
	try
	{
		messageObject = json::parse(msg->get_payload());
	}
	catch (const std::exception& e)
	{
		send_error(hdl, 1, "json-parse", e.what());
		return;
	}

	nano_message nano_msg;
	try
	{
		nano_msg.LoadSubconfiguration(messageObject);
	}
	catch (const nano_parse_error& err)
	{
		send_error(hdl, 2, "msg-parse", err.what(), messageObject);
		return;
	}

	switch (nano_msg.type)
	{
		case ENanoMessageType::List: {
			json list(json::value_t::array);
			SharedScopedLock lock(mPlayerData);
			for (auto playerIt : &mPlayerData)
			{
				playerIt.second->Lock();
				list.push_back(playerIt.second->Descriptor());
				playerIt.second->Unlock();
			}
			lock.unlock();
			if (auto con = hdl.lock())
				con->send(list.dump());
			break;
		}
		default: send_error(hdl, 100, "msg-exec", "unimplemented"); return;
	}
}

template <typename T>
using base_type = typename std::remove_cv<typename std::remove_reference<T>::type>::type;

template <Derived<nano::http_requests::request> T>
void Timaxanano::handle_http(imaxa_connection_ptr con, const std::function<void(imaxa_connection_ptr, T&)>& handler)
{
	// Read json.
	json contextObject;
	if (!con->get_request_body().empty())
	{
		try
		{
			contextObject = json::parse(con->get_request_body());
		}
		catch (const std::exception& e)
		{
			send_with_status(con, web::http::status::bad_request, "Error parsing context: " + std::string(e.what()));
			return;
		}
	}

	std::unique_ptr<base_type<T>> reg;
	try
	{
		reg = std::make_unique<base_type<T>>();
		reg->LoadSubconfiguration(contextObject);
	}
	catch (const std::runtime_error& err)
	{
		send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
		return;
	}

	handler(con, *reg);
}

template <Derived<nano::http_requests::player_based_request> T>
void Timaxanano::handle_player_http(imaxa_connection_ptr con_, const std::function<void(imaxa_connection_ptr, const std::shared_ptr<NanoPlayer>&, T&)>& handler,
                                    bool bAllowForAllPlayers, bool bAllowForDeadPlayers)
{
	handle_http<T>(con_, [&](imaxa_connection_ptr con, T& req) {
		if (req.Username.empty())    // empty username means for all players!
		{
			if (!bAllowForAllPlayers)
			{
				send_with_status(con, web::http::status::method_not_allowed, "This endpoint does not support a query for ALL players");
				return;
			}
			handler(con, {}, req);
		}
		else if (req.Username == DEAD_PLAYER_CLIENT_IP)
		{
			if (!bAllowForDeadPlayers)
			{
				send_with_status(con, web::http::status::method_not_allowed, "This endpoint does not support a query for DEAD players");
				return;
			}
			handler(con, {}, req);
		}
		else
		{
			SharedScopedLock lock(mPlayerData);
			auto find_player = mPlayerData->find(req.Username);
			if (find_player == mPlayerData->end() || !find_player->second)
			{
				send_with_status(con, web::http::status::not_found, "No such player");
				return;
			}
			std::shared_ptr<NanoPlayer> player = find_player->second;
			ScopedLock lock2(*player);
			lock.unlock();
			handler(con, player, req);
		}
	});
}

void Timaxanano::handle_player_message(NanoPlayer* player, imaxa_connection_hdl_ref hdl, message_ptr msg)
{
	// nothing for now
}

void Timaxanano::intersect_counter_sum(CounterMap& sum, const CounterMap& counters, const std::unordered_set<int>& intersection)
{
	for (const auto& counterId : intersection)
	{
		auto it = counters.find(counterId);
		if (it != counters.end())
		{
			sum[counterId].Value += it->second.Value;
		}
	}
}

void Timaxanano::on_http(const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query)
{
	std::error_code ec;
	if (resource.empty())
	{
		con->set_status(web::http::status::bad_request, "No resource path given!", ec);
		return;
	}

	if (con->defer_http_response())    // non zero means fail
	{
		con->set_status(web::http::status::internal_server_error, "Could not defer HTTP request!", ec);
		return;
	}

	DoAsyncTask(std::bind(&Timaxanano::process_http_request, this, con, resource, query, ytime::GetTimeMsec()));
}

void Timaxanano::on_game_transaction(const std::shared_ptr<NanoTransaction>& trx, const credit::CreditArray& requested, NanoPlayer& player, Session& session,
                                     const imaxa_connection_ptr& con)
{
	if (!trx)
	{
		send_with_status(con, web::http::status::internal_server_error, "Error executing transaction!");
	}
	else
	{
		if (trx->Amount == requested)
		{
			player.OnTransaction(*trx, session.SID, {});

			json response(json::value_t::object);
			response["balance"] = CreditJSON(player.Balance);
			response["balanceChangeID"] = player.BalanceChangeID;
			response["transaction"] = trx->ToShortJSON();
			response["denomination"] = mDenomination;
			std::error_code ec;
			con->set_json_body(response, {}, ec);
			send_with_status(con, web::http::status::ok);
		}
		else
		{
			NanoCreditTransfer(ENanoTransactionType::Rollback, trx->To, trx->From, trx->Amount, session.Data.ActiveGame, trx->GameID,
			                   "Transacted value did not match requested value.");
			send_with_status(con, web::http::status::internal_server_error, "could not execute transaction!");
		}
	}

	session.Data.LastActive = ytime::GetSystemTimeMsec();
	player.SetActive();
	SaveSession(session);
}

void Timaxanano::process_http_request(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, uint64_t startTime)
{
	ScopedAction scopedAction(
	  [this, resource = con->get_resource(), startTime]() { Log(Debug, "Request to %s took %lums", resource.c_str(), ytime::GetTimeMsec() - startTime); });

	const std::string expires = con->get_request_header(web::http::header(web::http::field::expires));
	if (expires.empty())
	{
		send_with_status(con, web::http::status::unauthorized, "No expire time!");
		return;
	}

	uint64_t time;
	if (!yutils::strToInt2(expires, time))
	{
		send_with_status(con, web::http::status::unauthorized, "Expire time must be an unsigned integer!");
		return;
	}

	if (time < ytime::GetSystemTimeMsec())
	{
		send_with_status(con, web::http::status::unauthorized, "Request expired!");
		return;
	}

	const std::string publicKey = con->get_request_header("X-Nano-Key");
	auto it = AuthKeys.find(publicKey);
	if (it == AuthKeys.end())
	{
		send_with_status(con, web::http::status::unauthorized, "Invalid API key!");
		return;
	}

	const std::string signature = crypto::Hash(it->second.Secret + expires + con->get_request_body(), EHashAlgorithm::SHA256);

	if (con->get_request_header(web::http::header(web::http::field::authorization)) != signature)
	{
		Log(Debug, "Signature mismatch: '%s' hashed with Expires=%s is: %s", con->get_request_body().c_str(),
		    con->get_request_header(web::http::header(web::http::field::expires)).c_str(), signature.c_str());
		send_with_status(con, web::http::status::unauthorized, "Bad signature!");
		return;
	}

	std::error_code ec;
	if (fragments[0] == "action")
	{
		if (fragments.size() != 3)
		{
			send_with_status(con, web::http::status::bad_request, "Player actions need 3 resource fragments");
			return;
		}

		const auto action_val = ENanoPlayerAction::_from_string_nocase_nothrow(fragments[2].c_str());
		if (!action_val)
		{
			send_with_status(con, web::http::status::bad_request, "Bad action '" + fragments[2] + "'");
			return;
		}
		const ENanoPlayerAction action = action_val.value();

		std::shared_ptr<Session> session;
		{
			SharedScopedLock lock(mSessions);
			auto find_session = mSessions->find(fragments[1]);
			if (find_session == mSessions->end())
			{
				send_with_status(con, web::http::status::not_found, "Invalid session");
				return;
			}
			session = find_session->second.lock();
		}

		std::shared_ptr<NanoPlayer> player;
		if (session)
			player = session->Player.lock();

		if (!player)
		{
			send_with_status(con, web::http::status::gone, "Session expired");
			return;
		}

		// Read json.
		json contextObject;
		if (!con->get_request_body().empty())
		{
			try
			{
				contextObject = json::parse(con->get_request_body());
			}
			catch (const std::exception& e)
			{
				send_with_status(con, web::http::status::bad_request, "Error parsing context: " + std::string(e.what()));
				return;
			}
		}

		ScopedLock playerLock(*player);
		ScopedLock sessionLock(session->Data);

		switch (action)
		{
			case ENanoPlayerAction::Authenticate: {
				std::unique_ptr<nano::actions::auth> auth;
				try
				{
					auth = std::make_unique<nano::actions::auth>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				const std::string passHash = crypto::Hash(SALT_KEY + auth->Pass, EHashAlgorithm::SHA256);
				if (player->Claimed() && !auth->Pass.empty())
				{
					if (passHash != player->Instance().SCPFolder)
					{
						send_with_status(con, web::http::status::unauthorized, "bad password");
						break;
					}
				}
				else if (!player->Claimed() && auth->bClaim && !auth->Pass.empty())
				{
					player->SetPassword(passHash);
				}

				if (!auth->Agent.empty())
					session->Data.LastAgent = auth->Agent;
				if (!auth->IP.empty())
					session->Data.LastIP = auth->IP;
				session->Data.LastActive = ytime::GetSystemTimeMsec();
				const bool bAuthenticated = !player->Claimed() || !auth->Pass.empty() || !bAuthRequired;

				json playerData(json::value_t::object);
				playerData["player"] = player->Descriptor();
				playerData["demo"] = false;
				playerData["denomination"] = mDenomination;
				playerData["authenticated"] = bAuthenticated;
				playerData["gameround"] = session->Data.HasActiveGameround() ? session->Data.ActiveGame->ToJSON() : json();

				con->set_json_body(playerData, {}, ec);
				send_with_status(con, web::http::status::ok);

				player->SetActive(false);
				if (bAuthenticated)
					player->SetLastAccessFrom(auth->Agent, auth->IP);
				player->UpdateDBInstance();

				json authEvent(json::value_t::object);
				authEvent["player"] = player->Username;
				authEvent["session"] = session->SID;
				BroadcastEvent("player-authenticated", authEvent);
				break;
			}
			case ENanoPlayerAction::Logout: {
				player->EndSession(player->Sessions.find(session->SID));
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::GameStatus: {
				std::unique_ptr<nano::actions::game_status> status;
				try
				{
					status = std::make_unique<nano::actions::game_status>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				std::shared_ptr<Gameround> gRound;
				if (status->GameroundID)
				{
					{
						SharedScopedLock glock(mActiveGamerounds);
						if (!mActiveGamerounds->empty() && mActiveGamerounds->begin()->first >= status->GameroundID)
						{
							auto find = mActiveGamerounds->find(status->GameroundID);
							if (find != mActiveGamerounds->end())
								gRound = find->second;
						}
					}

					if (!gRound)
					{
						SharedScopedLock glock(mGameroundArchive);
						if (!mGameroundArchive->empty())
						{
							auto find = mGameroundArchive->find(status->GameroundID);
							if (find != mGameroundArchive->end())
								gRound = find->second;
						}
					}
				}
				else if (session->Data.HasActiveGameround())
				{
					gRound = session->Data.ActiveGame;
				}

				con->set_json_body(gRound ? gRound->ToJSON() : json(), {}, ec);
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::VoidGame: {
				if (!session->Data.HasActiveGameround())
				{
					send_with_status(con, web::http::status::not_acceptable, "There is no gameround started");
					break;
				}

				VoidGame(*player, *session);
				player->SetActive();

				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::History: {
				con->set_json_body(session->GetHistory(*player), {}, ec);
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::Counters: {
				std::unique_ptr<nano::actions::counters> getCounters;
				try
				{
					getCounters = std::make_unique<nano::actions::counters>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				DigitalCounterCache<CounterMap> counters;
				for (const auto& [game, ids] : getCounters->RequestedCounters) counters.GetGameCounters(game) = player->GetCounters(ids, game);

				con->set_json_body(getCounters->RequestedCounters.empty() ? player->AllCounters().ToJSON() : counters.ToJSON(), {}, ec);
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::UpdateCounters: {
				std::unique_ptr<nano::actions::update_counters> update;
				try
				{
					update = std::make_unique<nano::actions::update_counters>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				std::set<int> intersect;
				std::ranges::set_intersection(NanoCounters::RESERVED_COUNTER_IDS, update->CounterIDs, std::inserter(intersect, intersect.begin()));
				if (!intersect.empty())
				{
					send_with_status(con, web::http::status::method_not_allowed, "Cannot update reserved counter(s) " + yutils::PrintContainer(intersect));
					break;
				}

				DigitalCounterCache<CounterMap> newValues;
				if (update->bIncrementalChange)
					newValues = player->IncrementCounters(update->CounterIDs, update->Value, update->Game, update->Source);
				else
					newValues = player->ResetCounters(update->CounterIDs, update->Value, update->Game, update->Source);

				con->set_json_body(newValues.ToJSON(), {}, ec);
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::Bet: {
				std::unique_ptr<nano::actions::transaction_bet> trx;
				try
				{
					trx = std::make_unique<nano::actions::transaction_bet>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				if (!session->Data.HasActiveGameround())
				{
					send_with_status(con, web::http::status::not_acceptable, "Cannot execute bet transactions because no game is in progress!");
					break;
				}

				if (!trx->Amount)
				{
					// empty transfer
					send_with_status(con, web::http::status::no_content, "bet amount is empty!");
					break;
				}
				else if (!player->Balance.Covers(static_cast<int64_t>(trx->Amount)))
				{
					send_with_status(con, web::http::status::method_not_allowed, "Not enough credits for this bet!");
					break;
				}


				if (mMaxBet > 0.0)
				{
					const int64_t maxBetCredits = std::floor(mMaxBet / mDenomination);
					if (static_cast<int64_t>(trx->Amount) > maxBetCredits)
					{
						send_with_status(con, web::http::status::not_acceptable, "Bet exceeds max bet!");
						break;
					}
				}

				auto amount = player->Balance.MakeBet(trx->Amount);
				std::shared_ptr<NanoTransaction> result =
				  NanoCreditTransfer(ENanoTransactionType::Bet, player->Username, MachineName(), amount, session->Data.ActiveGame, session->Data.Game, trx->Tag);

				on_game_transaction(result, amount, *player, *session, con);
				break;
			}
			case ENanoPlayerAction::Credit: {
				std::unique_ptr<nano::actions::transaction_credit> trx;
				try
				{
					trx = std::make_unique<nano::actions::transaction_credit>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				if (trx->Amount.IsEmpty())
				{
					// empty transfer
					send_with_status(con, web::http::status::no_content, "transaction amount is empty!");
					break;
				}

				std::shared_ptr<NanoTransaction> result =
				  NanoCreditTransfer(trx->Type, MachineName(), player->Username, trx->Amount, session->Data.ActiveGame, session->Data.Game, trx->Tag);
				on_game_transaction(result, trx->Amount, *player, *session, con);
				break;
			}
			case ENanoPlayerAction::Balance: {
				con->set_json_body(CreditJSON(player->Balance), {}, ec);
				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::Rollback: {
				std::unique_ptr<nano::actions::rollback> rollback;
				try
				{
					rollback = std::make_unique<nano::actions::rollback>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				if (!rollback->TRID)
				{
					send_with_status(con, web::http::status::bad_request, "Bad transaction ID");
					break;
				}

				if (!rollback->GameroundID || session->Data.GameroundID() != rollback->GameroundID)
				{
					send_with_status(con, web::http::status::not_acceptable, "The game you are trying to rollback does not exist or is inactive");
					break;
				}

				std::shared_ptr<NanoTransaction> transaction = FindTransaction(rollback->TRID);
				if (!transaction)
				{
					send_with_status(con, web::http::status::not_found, "The transaction you are trying to rollback does not exist");
					break;
				}
				else if (transaction->bRolledBack)
				{
					send_with_status(con, web::http::status::not_acceptable, "This transaction was already rolled back!");
					break;
				}
				else if (transaction->Type == ENanoTransactionType::Deposit || transaction->Type == ENanoTransactionType::Withdraw ||
				         transaction->Type == ENanoTransactionType::Rollback)
				{
					send_with_status(con, web::http::status::not_acceptable, "This transaction type cannot be rolled back!");
					break;
				}

				std::shared_ptr<NanoTransaction> rollbackedTr =
				  NanoCreditTransfer(ENanoTransactionType::Rollback, transaction->To, transaction->From, transaction->Amount, session->Data.ActiveGame,
				                     session->Data.Game, std::to_string(transaction->TransactionID));

				if (!rollbackedTr)
				{
					send_with_status(con, web::http::status::internal_server_error, "Could not roll back the transaction!");
					break;
				}


				player->OnTransaction(*rollbackedTr, session->SID, {});

				SaveSession(*session);

				send_with_status(con, web::http::status::ok);
				break;
			}
			case ENanoPlayerAction::GameEnd: {
				std::unique_ptr<nano::actions::game_end> gameEnd;
				try
				{
					gameEnd = std::make_unique<nano::actions::game_end>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				if (!session->Data.HasActiveGameround())
				{
					send_with_status(con, web::http::status::not_acceptable, "There is no gameround started");
					break;
				}

				if (gameEnd->numPlayed)
				{    // moved Increment Counters to before EndGame call, as EndGame saves counters
					player->IncrementCounters({ CS_GAMES_PLAYED, gameEnd->bWonAnything ? CS_GAMES_WON : CS_GAMES_LOST }, gameEnd->numPlayed, session->Data.Game,
					                          player->Username);
				}

				ScopedLock gameroundLock(*session->Data.ActiveGame);

				session->Data.ActiveGame->Ended = ytime::GetSystemTimeMsec();

				const uint64_t endedGID = session->Data.ActiveGame->ID;
				EndGame(*player, *session->Data.ActiveGame, session->SID);

				SaveSession(*session, true);

				{
					ScopedLock lock(mActiveGamerounds);
					mActiveGamerounds->erase(endedGID);
				}
				{
					SharedScopedLock lock(mGameroundArchive);
					(&mGameroundArchive)[endedGID] = session->Data.ActiveGame;
				}

				gameroundLock.unlock();
				session->Data.ActiveGame.reset();

				session->Data.PlayedGameRounds.insert(session->Data.PlayedGameRounds.end(), endedGID);

				player->AddToHistory(endedGID, contextObject);
				send_with_status(con, web::http::status::ok);
				player->SaveHistory();
				player->SetActive();
				player->CheckKioskExpired();

				break;
			}
			case ENanoPlayerAction::GameStart: {
				if (player->Instance().Status == HANDPAY_STATUS)
				{
					send_with_status(con, web::http::status::not_acceptable, "Player locked in handpay!");
					break;
				}

				if (session->Data.HasActiveGameround())
				{
					send_with_status(con, web::http::status::method_not_allowed, "Game already in progress (" + std::to_string(session->Data.ActiveGame->ID) + ")!");
					break;
				}

				session->Data.ActiveGame = std::make_shared<Gameround>(mGameroundID++, player->Username, session->SID, ytime::GetSystemTimeMsec());

				mGameroundIDParam->SetValue(mGameroundID, true);
				SaveSession(*session);

				{
					ScopedLock lock(mActiveGamerounds);
					(&mActiveGamerounds)[session->Data.ActiveGame->ID] = session->Data.ActiveGame;
				}

				con->set_body(std::to_string(session->Data.ActiveGame->ID), {}, ec);
				send_with_status(con, web::http::status::ok);

				json gameStartEvent(json::value_t::object);
				gameStartEvent["gid"] = session->Data.ActiveGame->ID;
				gameStartEvent["started"] = session->Data.ActiveGame->Started;
				player->TriggerEvent(session->SID, "game-start", gameStartEvent, EPlayerEventPropagation::OwnerSession);
				BroadcastPlayerEvent(player->Username, "game-start", gameStartEvent);
				player->SetActive();
				break;
			}
			case ENanoPlayerAction::RequestPayout: {
				if (player->IsInGame())
				{
					send_with_status(con, web::http::status::not_acceptable, "Cannot payout during an active game!");
					break;
				}

				if (player->Instance().Status == HANDPAY_STATUS)
				{
					send_with_status(con, web::http::status::accepted, "Player is already in handpay!");
					break;
				}

				const CreditArray queuedPayout =
				  player->StartHandpay({ .Source = EHandpayTriggerSource::User, .Amount = static_cast<uint64_t>(player->Balance.Total()) });

				con->set_json_body(CreditJSON(queuedPayout), {}, ec);
				send_with_status(con, web::http::status::ok);
				player->SetActive();
				break;
			}
			case ENanoPlayerAction::SetParam: {
				std::unique_ptr<nano::actions::set_param> setParamReq;
				try
				{
					setParamReq = std::make_unique<nano::actions::set_param>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				if (RESERVED_CONFIG_NAMES.contains(setParamReq->Param))
				{
					send_with_status(con, web::http::status::conflict, "This is a reserved parameter name!");
				}
				else
				{
					player->SetConfig(setParamReq->Param, setParamReq->Value);
					send_with_status(con, web::http::status::ok);
				}
				break;
			}
			case ENanoPlayerAction::TransferToken: {
				if (player->Instance().Status == HANDPAY_STATUS)
				{
					send_with_status(con, web::http::status::not_acceptable, "Player is in handpay");
					break;
				}

				if (player->Balance.IsEmpty())
				{
					send_with_status(con, web::http::status::not_acceptable, "Transfer tokens cannot be created with a zero balance");
					break;
				}

				if (player->Type() == ENanoPlayerType::Temp)
				{
					send_with_status(con, web::http::status::not_acceptable, "Temporary players may not create transfer tokens");
					break;
				}

				std::shared_ptr<FTransferToken> Token = std::make_shared<FTransferToken>();
				do {
					Token->ID = crypto::GetRandomInRange<uint64_t>(1, MAX_TRANSFER_TOKEN_ID);
					ScopedLock lock(mTransferTokens);
					if (!mTransferTokens->emplace(Token->ID, Token).second)
						Token->ID = 0;
				} while (!Token->ID);    // this will pretty much always only run once (unless a UID collision occurs)

				Token->OwnerPlayerID = player->Username;
				Token->CreatedTimestamp = ytime::GetSystemTimeMsec();

				nano::http_requests::PlayerCreateInfo info;
				info.Type = ENanoPlayerType::Temp;
				info.PassHash = crypto::Hash(std::to_string(Token->ID), EHashAlgorithm::SHA256);
				info.Jurisdiction = player->Jurisdiction;

				std::shared_ptr<NanoPlayer> new_player = CreatePlayer(info);
				if (!new_player)
				{
					RedeemTransferToken(Token->ID);
					Log(Debug, "Error creating new player for transfer token!");
					send_with_status(con, web::http::status::internal_server_error, "Error creating new player for transfer token");
					break;
				}

				Token->UserPlayerID = new_player->Username;

				const json createdPlayer = new_player->Descriptor();
				Token->Session = crypto::GenRandID(SESSION_BYTE_SIZE);

				std::shared_ptr<Session> new_session = std::make_shared<Session>(Token->Session, new_player, false);
				if (CreateSession(*new_player, new_session))
				{
					std::shared_ptr<NanoTransaction> transfer = NanoCreditTransfer(ENanoTransactionType::Withdraw, player->Username, new_player->Username,
					                                                               player->Balance, {}, session->Data.Game, "Transfer to web token");

					if (transfer)
					{
						Token->StartingCredits = transfer->Amount;

						player->OnTransaction(*transfer, session->SID, {});
						new_player->OnTransaction(*transfer, new_session->SID, {});
					}
					else
					{
						new_player->EndAllSessions();
						Log(Warning, "Transfer error from player %s to player %s!", player->Username.c_str(), new_player->Username.c_str());
					}
				}
				else
				{
					new_session.reset();
					Log(Warning, "TransferToken: Error creating new player session for player %s!", new_player->Username.c_str());
				}

				// if session is empty, means something failed, so revert what we did!
				if (!new_session || Token->StartingCredits.IsZero())
				{
					mPlayerData.Lock();
					mPlayerData->erase(new_player->Username);
					mPlayerData.Unlock();
					RedeemTransferToken(Token->ID);
					send_with_status(con, web::http::status::internal_server_error,
					                 new_session ? "Error transfering balance to new player" : "Error creating new player session");
				}
				else
				{
					BroadcastPlayerEvent(new_player->Username, "created", createdPlayer);


					const json tokenObj = Token->ToJSON();
					BroadcastEvent("new-transfer-token", tokenObj, true);

					con->set_json_body(tokenObj, {}, ec);
					send_with_status(con, web::http::status::ok);

					DoAsyncTask(
					  [bSync = ShouldSyncDiskOperations(), tokenObj, filename = mNanoDataDirectory / "Tokens" / (std::to_string(Token->ID) + TOKEN_FILE_EXTENSION)]() {
						  if (bSync)
						  {
							  filesystem::SyncWrite(filename, tokenObj.dump());
						  }
						  else
						  {
							  std::ofstream outFile(filename);
							  outFile << tokenObj;
						  }
					  });
				}
				new_player->Unlock();

				break;
			}
			case ENanoPlayerAction::RedeemTransferToken: {
				std::unique_ptr<nano::actions::redeem_transfer_token> redeemTokenReq;
				try
				{
					redeemTokenReq = std::make_unique<nano::actions::redeem_transfer_token>(contextObject);
				}
				catch (const nano_parse_error& err)
				{
					send_with_status(con, web::http::status::bad_request, std::string("Context is invalid: ") + err.what());
					break;
				}

				std::shared_ptr<FTransferToken> Token = RedeemTransferToken(redeemTokenReq->token);    // from the global token collection
				if (!Token || (!bCrossPlayerTransferTokensAllowed && Token->OwnerPlayerID != player->Username))
				{
					send_with_status(con, web::http::status::not_found, "Transfer token not found!");
					break;
				}

				std::shared_ptr<NanoPlayer> playerToTransferFrom;
				{
					SharedScopedLock lock(mPlayerData);
					auto find = mPlayerData->find(Token->UserPlayerID);
					if (find != mPlayerData->end())
						playerToTransferFrom = find->second;
				}

				if (!playerToTransferFrom)
				{
					send_with_status(con, web::http::status::no_content,
					                 "Player that was paid to no longer exists (probably was deleted due to reaching 0 credit balance)");
					DoAsyncTask([filename = mNanoDataDirectory / "Tokens" / (std::to_string(Token->ID) + TOKEN_FILE_EXTENSION)]() { std::filesystem::remove(filename); });
					BroadcastEvent("transfer-token-redeemed", Token->ToJSON(), true);
					break;
				}

				{
					ScopedLock lock(*playerToTransferFrom);
					if (playerToTransferFrom->Balance.IsEmpty())
					{
						send_with_status(con, web::http::status::no_content, "Nothing to payin");
					}
					else
					{
						auto result = NanoCreditTransfer(ENanoTransactionType::Deposit, playerToTransferFrom->Username, player->Username, playerToTransferFrom->Balance,
						                                 {}, redeemTokenReq->game, "Web token deposit");

						if (!result)
						{
							send_with_status(con, web::http::status::internal_server_error, "Failed to deposit amount back to original player account");
							break;
						}

						player->OnTransaction(*result, session->SID, {});
						playerToTransferFrom->OnTransaction(*result, Token->Session, {});
						con->set_json_body(CreditJSON(result->Amount), {}, ec);
					}

					playerToTransferFrom->DeletePlayer(true);
				}

				{
					ScopedLock lock(mPlayerData);
					mPlayerData->erase(playerToTransferFrom->Username);
				}

				send_with_status(con, web::http::status::ok);

				BroadcastEvent("transfer-token-redeemed", Token->ToJSON(), true);
				DoAsyncTask([filename = mNanoDataDirectory / "Tokens" / (std::to_string(Token->ID) + TOKEN_FILE_EXTENSION)]() { std::filesystem::remove(filename); });

				break;
			}
			default: {
				send_with_status(con, web::http::status::bad_request, std::string("Unimplemented player action: ") + action._to_string());
				break;
			}
		}
		con->send_http_response(ec);
	}
	else if (fragments[0] == "session")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::create_session>(
		  con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::create_session& login) {
			  const std::string passHash = crypto::Hash(SALT_KEY + login.PassHash, EHashAlgorithm::SHA256);
			  if (!player->Claimed() || player->Instance().SCPFolder == passHash)
			  {
				  if (!player->Claimed() && !login.PassHash.empty())
				  {
					  player->SetPassword(passHash);
				  }

				  std::shared_ptr<Session> session;
				  if (login.bAllowUnfinished)
				  {
					  for (const auto& existing_session : player->Sessions)    // give an existing session that has an unfinished game
					  {
						  ScopedLock lock(existing_session.second->Data);
						  if (existing_session.second->Data.HasActiveGameround() && existing_session.second->Data.Stream.expired())
						  {
							  session = existing_session.second;
							  break;
						  }
					  }
				  }

				  if (!session)    // create new
				  {
					  session = std::make_shared<Session>(crypto::GenRandID(SESSION_BYTE_SIZE), player, login.bCanTimeout);

					  if (!CreateSession(*player, session))
					  {
						  send_with_status(con, web::http::status::internal_server_error, "SHA256 collision! Wow, go play the lottery immediately!!!!!");
						  return;
					  }

					  Log(Normal, "Created new session for %s", player->Username.c_str());

					  player->TriggerEvent(session->SID, "session-new", session->SID, EPlayerEventPropagation::OnlyPlayerListeners);
				  }

				  session->Data.LastActive = ytime::GetSystemTimeMsec();
				  session->Data.LastAgent = login.Agent;
				  session->Data.LastIP = login.IP;

				  player->SetActive(false);
				  player->SetLastAccessFrom(login.Agent, login.IP);
				  player->UpdateDBInstance();

				  std::error_code ec;
				  con->set_body(session->SID, {}, ec);
				  send_with_status(con, web::http::status::ok);
			  }
			  else
			  {
				  send_with_status(con, web::http::status::unauthorized, "Wrong username and password combination");
			  }
		  });
	}
	else if (fragments[0] == "info")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::player_based_request>(
		  con, [](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::player_based_request& info) {
			  std::error_code ec;
			  con->set_json_body(player->Descriptor(), {}, ec);
			  send_with_status(con, web::http::status::ok);
		  });
	}
	else if (fragments[0] == "auth-listener")
	{
		if (fragments.size() < 2)
		{
			send_with_status(con, web::http::status::bad_request, "Missing arguments");
			return;
		}
		else if (fragments.size() > 2)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}


		handle_http<nano::http_requests::auth_listener>(con, [this, id = fragments[1]](imaxa_connection_ptr con, nano::http_requests::auth_listener& auth) {
			imaxa_connection_hdl match;
			{
				SharedScopedLock lock(mGeneralListeners);
				for (const auto& listener : &mGeneralListeners)
				{
					if (listener.second.ID == id)
					{
						match = listener.first;
						break;
					}
				}
			}

			if (match.expired())
			{
				send_with_status(con, web::http::status::not_found, "Listener not online");
				return;
			}

			if (auth.GetConfig(JsonPath()).get<std::string>() == crypto::Hash(SALT_KEY + id, EHashAlgorithm::SHA256))
			{
				{
					ScopedLock lock(mGeneralListeners);
					auto find = mGeneralListeners->find(match);
					if (find != mGeneralListeners->end())
						find->second.bAdmin = true;
				}
				send_with_status(con, web::http::status::ok);
			}
			else
			{
				send_with_status(con, web::http::status::forbidden);
			}
		});
	}
	else if (fragments[0] == "register")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_http<nano::http_requests::create_player>(con, [this](imaxa_connection_ptr con, nano::http_requests::create_player& reg) {
			auto player = CreatePlayer(reg);
			if (!player)
			{
				Log(Debug, "Error creating player %s!", reg.Username.c_str());
				send_with_status(con, web::http::status::internal_server_error, "Error creating player");
				return;
			}

			const json createdPlayer = player->Descriptor();
			player->Unlock();

			std::error_code ec;
			con->set_json_body(createdPlayer, {}, ec);
			send_with_status(con, web::http::status::ok);

			BroadcastPlayerEvent(player->Username, "created", createdPlayer);
		});
	}
	else if (fragments[0] == "deposit" || fragments[0] == "withdraw")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<nano::http_requests::credit_transfer>(
		  con, [this, &fragments](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, nano::http_requests::credit_transfer& transfer) {
			  const bool bIsInHandpay = player->Instance().Status == HANDPAY_STATUS;
			  // if the player is in handpay, don't allow a payin or payout
			  if (bIsInHandpay)
			  {
				  send_with_status(con, web::http::status::not_acceptable,
				                   "Player is in handpay state - deposits and withdrawals are not allowed until handpay is resolved!");
				  return;
			  }

			  if (transfer.bIsMoneyCents)
			  {
				  for (int64_t& amount : transfer.Amount)
				  {
					  const int64_t denomCents = std::round(mDenomination * 100);
					  const int64_t credits = amount / denomCents;
					  const int64_t remainderCents = amount - credits * denomCents;
					  if (remainderCents)
						  Log(Warning, "Converting for %s from %.2f %s to %ld credits with a denomination of %.2f results in %.2f leftover money.", fragments[0].c_str(),
						      amount * 0.01, mCurrency.c_str(), credits, mDenomination, remainderCents * 0.01);
					  amount = credits;
				  }
			  }

			  const int64_t StartBalance = player->Balance.Total();

			  const web::QueryString query(con->get_uri()->get_query());

			  std::shared_ptr<NanoTransaction> result;
			  if (fragments[0] == "deposit")
			  {
				  // cannot pay in zero!
				  if (transfer.Amount.IsZero())
				  {
					  send_with_status(con, web::http::status::not_acceptable, "Can't pay in zero!");
					  return;
				  }

				  if (mMaxBalance && player->BalanceAfterHandpays().Total() + transfer.Amount.Total() > mMaxBalance)
				  {
					  send_with_status(con, web::http::status::not_acceptable, "Deposit would exceed max balance!");
					  return;
				  }

				  if (!transfer.Owner.empty() && transfer.Owner != MachineName() && !bAllowDepositFromSameOwnerOnMultipleAccounts)
				  {
					  auto players = mPlayerData.getCopy();
					  for (const auto& [username, otherPlayer] : players)
					  {
						  if (player == otherPlayer)
							  continue;

						  if (otherPlayer->GetDepositOwner() == transfer.Owner)
						  {
							  send_with_status(con, web::http::status::not_acceptable,
							                   "The deposit 'owner' is already using another account (" + otherPlayer->Username + ")");
							  return;
						  }
					  }
				  }

				  result = NanoCreditTransfer(ENanoTransactionType::Deposit, transfer.Source, player->Username, transfer.Amount, {}, {}, transfer.Description, false);
			  }
			  else    // payout
			  {
				  if (player->Balance.IsZero())
				  {
					  send_with_status(con, web::http::status::not_acceptable, "Nothing to pay out!");
					  return;
				  }

				  if (transfer.Amount.IsZero())
					  transfer.Amount = player->Balance;

				  if (!player->Balance.Covers(transfer.Amount))
				  {
					  send_with_status(con, web::http::status::not_acceptable, "Not enough credits!");
					  return;
				  }

				  result =
				    NanoCreditTransfer(ENanoTransactionType::Withdraw, player->Username, transfer.Source, transfer.Amount, {}, {}, transfer.Description, true, false);
			  }

			  if (!result)
			  {
				  Log(Warning, "%s error for player %s!", (fragments[0] == "deposit") ? "Deposit" : "Withdraw", player->Username.c_str());
				  send_with_status(con, web::http::status::internal_server_error, "Error processing " + fragments[0]);
				  return;
			  }

			  player->OnTransaction(*result, {}, transfer.Owner);

			  json response(json::value_t::object);
			  response["balance"] = CreditJSON(player->Balance);
			  response["transaction"] = result->ToShortJSON();
			  response["denomination"] = mDenomination;

			  std::error_code ec;
			  con->set_json_body(response, {}, ec);
			  send_with_status(con, web::http::status::ok);

			  const int64_t EndBalance = player->Balance.Total();

			  const int64_t Amount = transfer.Amount.Total();
			  if (fragments[0] == "deposit")
			  {
				  Log(Info, "Pay-in success for player %s: %ld + %.2f(money) -> %ld [denomination is %.2f]", player->Username.c_str(), StartBalance,
				      Amount * mDenomination, EndBalance, mDenomination);
				  if (EndBalance - StartBalance != Amount)
				  {
					  Log(Warning, "Paid value does not exactly match for player %s: %.2f exact payin requested vs %.2f actually paid", player->Username.c_str(),
					      Amount * mDenomination, (EndBalance - StartBalance) * mDenomination);
				  }
			  }
			  else
			  {
				  player->CheckKioskExpired();
				  Log(Info, "Pay-out success for player %s: %ld - %s -> %ld", player->Username.c_str(), StartBalance, Amount ? std::to_string(Amount).c_str() : "all",
				      EndBalance);
				  if (StartBalance - EndBalance != Amount)
				  {
					  Log(Warning, "Paid out value does not exactly match for player %s: %.2f exact payin requested vs %.2f actually paid", player->Username.c_str(),
					      Amount * mDenomination, (EndBalance - StartBalance) * mDenomination);
				  }
			  }
			  player->SetActive();
		  });
	}
	else if (fragments[0] == "handpay")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<nano::http_requests::initiate_handpay>(
		  con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, nano::http_requests::initiate_handpay& handpay) {
			  const int64_t availableBalance = player->BalanceAfterHandpays().MaxPayout().Total();
			  if (availableBalance <= 0)
			  {
				  send_with_status(con, web::http::status::not_acceptable, "No balance available to handpay!");
				  return;
			  }

			  const GameIdentifier game = web::QueryString(con->get_uri()->get_query()).Get("game");

			  if (handpay.bIsMoneyCents)
				  handpay.Amount = handpay.Amount * 1e-2 / mDenomination;

			  if (handpay.Amount)
				  handpay.Amount = std::min<uint64_t>(availableBalance, handpay.Amount);
			  player->StartHandpay({ .Source = EHandpayTriggerSource::User, .Amount = handpay.Amount, .Time = ytime::GetSystemTimeMsec(), .RequestedFromGame = game });

			  std::error_code ec;
			  con->set_json_body(player->PendingUserHandpay->second, {}, ec);
			  send_with_status(con, web::http::status::ok);
		  });
	}
	else if (fragments[0] == "query-handpay")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<nano::http_requests::query_handpay>(
		  con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, nano::http_requests::query_handpay& handpay) {
			  FPendingHandpay pend;
			  if (player->GetCurrentHandpay(pend))
			  {
				  std::error_code ec;
				  con->set_json_body(pend.ToJson(FindTransaction(pend.TriggeredByTRID)), {}, ec);
				  send_with_status(con, web::http::status::ok);
			  }
			  else
			  {
				  send_with_status(con, web::http::status::no_content, "No currently pending handpay");
			  }
		  });
	}
	else if (fragments[0] == "confirm-handpay")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<nano::http_requests::confirm_handpay>(con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player,
		                                                                     nano::http_requests::confirm_handpay& handpay) {
			if (handpay.ConfirmedBy.empty())
				handpay.ConfirmedBy = MachineName();

			FPendingHandpay pend;
			if (player->GetCurrentHandpay(pend))
			{
				pend.Amount = std::min<uint64_t>(pend.Amount, player->Balance.MaxPayout().Total());
				if (handpay.ConfirmedAmount && pend.Amount != handpay.ConfirmedAmount)
				{
					Log(Warning, "Handpay confirmed with different amount than expected for player %s (%lu confirmed vs %lu expected)!", player->Username.c_str(),
					    handpay.ConfirmedAmount, pend.Amount);
					send_with_status(con, web::http::status::conflict, "Confirmed amount does not match handpay amount");
					return;
				}

				std::shared_ptr<NanoTransaction> instigator = FindTransaction(pend.TriggeredByTRID);

				json response(json::value_t::object);
				if (pend.Amount)
				{
					const int64_t StartBalance = player->Balance.Total();

					auto result = NanoCreditTransfer(ENanoTransactionType::Withdraw, player->Username, handpay.ConfirmedBy, player->Balance.MakePayout(pend.Amount), {},
					                                 pend.RequestedFromGame, yutils::Format("Handpay triggered by %s confirmed", pend.Source._to_string()), true, false);

					if (!result)
					{
						Log(Warning, "Handpay confirmation error for player %s!", player->Username.c_str());
						send_with_status(con, web::http::status::internal_server_error, "Error processing handpay");
						return;
					}

					player->OnTransaction(*result, {}, handpay.ConfirmedBy);

					if (instigator)
					{
						const int64_t TotalPaid = result->Amount.Total();
						const ENanoTransactionType trxTypeForThisPlayer = instigator->TransactionTypeForUser(player->Username);
						const std::string source = (trxTypeForThisPlayer == ENanoTransactionType::Win) ? player->Username : result->From;
						switch (trxTypeForThisPlayer)
						{
							case ENanoTransactionType::Win: player->IncrementCounters({ CS_TOTAL_ATTENDANT_PAID_PAYTABLE_WIN }, TotalPaid, result->GameID, source); break;
							case ENanoTransactionType::Jackpot:
								player->IncrementCounters({ CS_TOTAL_ATTENDANT_PAID_PROGRESSIVE_WIN }, TotalPaid, result->GameID, source);
								break;
							case ENanoTransactionType::ExternalBonus:
								player->IncrementCounters({ CS_TOTAL_MACHINE_PAID_EXTERNAL_BONUS_WIN }, TotalPaid, result->GameID, source);
								break;
							case ENanoTransactionType::ExtBonusHandpay:
								player->IncrementCounters({ CS_TOTAL_ATTENDANT_PAID_EXTERNAL_BONUS_WIN }, TotalPaid, result->GameID, source);
								break;
							default: break;
						}
					}

					const int64_t EndBalance = player->Balance.Total();

					player->CheckKioskExpired();
					Log(Info, "Hand-pay success for player %s: %ld - %lu -> %ld", player->Username.c_str(), StartBalance, pend.Amount, EndBalance);
					if (StartBalance - EndBalance != static_cast<int64_t>(pend.Amount))
					{
						Log(Warning, "Hand-paid value does not exactly match for player %s: %.2f exact payin requested vs %.2f actually paid", player->Username.c_str(),
						    pend.Amount * mDenomination, (EndBalance - StartBalance) * mDenomination);
					}

					response["transaction"] = result->ToShortJSON();
				}

				response["balance"] = CreditJSON(player->Balance);
				response["denomination"] = mDenomination;
				response["handpay"] = pend.ToJson(instigator);

				player->ResolveHandpay(response["handpay"], handpay.ConfirmedBy);

				std::error_code ec;
				con->set_json_body(response, {}, ec);
				send_with_status(con, web::http::status::ok);
			}
			else
			{
				send_with_status(con, web::http::status::no_content, "No currently pending handpay");
			}
		});
	}
	else if (fragments[0] == "cancel-handpay")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::player_based_request>(
		  con, [](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::player_based_request& cancel) {
			  player->ClearUserHandpay(true);

			  send_with_status(con, web::http::status::ok);
			  player->SetActive();
		  });
	}
	else if (fragments[0] == "void")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::void_gameround>(
		  con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::void_gameround& gameround) {
			  bool bFound = false;
			  for (auto session : player->Sessions)
			  {
				  ScopedLock lock(session.second->Data);
				  if (gameround.GameroundID == session.second->Data.GameroundID())
				  {
					  VoidGame(*player, *session.second);
					  bFound = true;
					  break;
				  }
			  }

			  send_with_status(con, bFound ? web::http::status::ok : web::http::status::not_found);
		  });
	}
	else if (fragments[0] == "config")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_http<const nano::http_requests::request>(con, [this](imaxa_connection_ptr con, const nano::http_requests::request& cancel) {
			std::error_code ec;
			json conf(json::value_t::object);
			conf["domain"] = MachineName();
			conf["currency"] = mCurrency;
			conf["locale"] = mLocale;
			conf["jurisdiction"] = mJurisdiction;
			conf["denomination"] = mDenomination;
			conf["max-bet"] = mMaxBet;
			conf["player-initiated-payout"] = bPayoutEnabled;
			conf["decimal-char"] = mDecimalChar;
			conf["thousand-char"] = mThousandChar;
			conf["decimal-places"] = mDecimalPlaces;
			conf["align-left"] = mAlignLeft;
			conf["currency-symbol"] = mCurrencySymbol;
			conf["version"] = Version();
			con->set_json_body(conf, {}, ec);
			send_with_status(con, web::http::status::ok);
		});
	}
	else if (fragments[0] == "list-transfer-tokens")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		struct TransferTokenDesc
		{
			FTransferToken Token;
			CreditArray CurrentCredits;
		};

		std::map<uint64_t, TransferTokenDesc> tokens;
		{
			SharedScopedLock lock1(mPlayerData);
			SharedScopedLock lock2(mTransferTokens);
			for (const auto& tokenIt : &mTransferTokens)
			{
				TransferTokenDesc desc;
				desc.Token = *tokenIt.second;
				auto find = mPlayerData->find(tokenIt.second->UserPlayerID);
				if (find != mPlayerData->end())
					desc.CurrentCredits = find->second->Balance;
				tokens[tokenIt.first] = std::move(desc);
			}
		}

		json tokenCollection(json::value_t::object);
		for (const auto& t : tokens)
		{
			json& tokenOut = tokenCollection[std::to_string(t.first)];
			tokenOut = t.second.Token.ToJSON(true);
			tokenOut["current-credits"] = CreditJSON(t.second.CurrentCredits);
		}

		con->set_json_body(tokenCollection, {}, ec);
		send_with_status(con, web::http::status::ok);
	}
	else if (fragments[0] == "gamerounds")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		std::list<std::shared_ptr<Gameround>> gamerounds;
		{
			SharedScopedLock lock(mActiveGamerounds);
			for (const auto& gameroundIt : &mActiveGamerounds) gamerounds.push_back(gameroundIt.second);
		}

		if (query.Get("active-only", "0") != "1")
		{
			SharedScopedLock lock(mGameroundArchive);
			size_t N = 0;
			for (auto gr = mGameroundArchive->rbegin(); N < 100 && gr != mGameroundArchive->rend(); N++, gr++) gamerounds.push_back(gr->second);
		}

		json gameroundArray(json::value_t::array);
		for (const std::shared_ptr<Gameround>& g : gamerounds) gameroundArray.push_back(g->ToJSON());

		con->set_json_body(gameroundArray, {}, ec);
		send_with_status(con, web::http::status::ok);
	}
	else if (fragments[0] == "transaction")
	{
		if (fragments.size() < 2)
		{
			send_with_status(con, web::http::status::bad_request, "Missing TRID");
			return;
		}
		if (fragments.size() > 2)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		int64_t idx = 0;
		if (!yutils::strToInt2(fragments[1], idx) || idx < 0)
		{
			send_with_status(con, web::http::status::bad_request, "Bad transaction ID");
			return;
		}

		std::shared_ptr<NanoTransaction> trx = FindTransaction(idx);
		if (!trx)
		{
			send_with_status(con, web::http::status::not_found, "Transaction with given ID not found");
			return;
		}

		con->set_json_body(trx->ToJSON(), {}, ec);
		send_with_status(con, web::http::status::ok);
	}
	else if (fragments[0] == "transactions")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		std::list<std::shared_ptr<NanoTransaction>> transactions;
		{
			SharedScopedLock lock(mTransactions);
			for (auto tr = mTransactions->rbegin(); tr != mTransactions->rend() && transactions.size() < 500; tr++) transactions.push_back(tr->second);
		}

		json list(json::value_t::object);
		for (const std::shared_ptr<NanoTransaction>& tr : transactions) list[std::to_string(tr->TransactionID)] = tr->ToJSON();

		con->set_json_body(list, {}, ec);
		send_with_status(con, web::http::status::ok);
	}
	else if (fragments[0] == "list")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		const bool bFullList = query.Get("extended", "0") == "1";

		json list(json::value_t::array);
		{
			SharedScopedLock lock(mPlayerData);
			for (const auto& playerIt : &mPlayerData)
			{
				playerIt.second->Lock();
				list.push_back(playerIt.second->Descriptor(bFullList));
				playerIt.second->Unlock();
			}
		}

		con->set_json_body(list, {}, ec);
		send_with_status(con, web::http::status::ok);
	}
	else if (fragments[0] == "reset-counters")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::reset_counters>(
		  con,
		  [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::reset_counters& reset) {
			  const uint64_t now = ytime::GetSystemTimeMsec();
			  std::set<std::string> ResetPlayerNames;

			  const std::unordered_set<int> AllPeriodicCounters = GetAllPeriodicCounters();

			  if (player)    // just for one player
			  {
				  player->ResetCounters(AllPeriodicCounters, 0, {}, reset.Source);
				  player->LastCounterReset = now;
				  ResetPlayerNames.insert(player->Username);
			  }
			  else    // for all players
			  {
				  SharedScopedLock lock(mPlayerData);
				  for (const auto& p : &mPlayerData)
				  {
					  ScopedLock lock2(*p.second);
					  p.second->ResetCounters(AllPeriodicCounters, 0, {}, reset.Source);
					  p.second->LastCounterReset = now;
					  ResetPlayerNames.insert(p.second->Username);
				  }
			  }

			  pDBMngr->ExecuteAsync(
			    [ResetPlayerNames, now](TGameDBSrv* db, soci::session& sql) {
				    for (const std::string& player : ResetPlayerNames)
					    db->_SetParam(player, "NANO", "LAST_COUNTER_RESET", std::to_string(now), "Last counter reset time",
					                  "The last time the attendant counters were reset", ELanguage::English, true);
			    },
			    true, true, { ETransactionFlags::WaitTransaction });

			  send_with_status(con, web::http::status::ok);
		  },
		  true);
	}
	else if (fragments[0] == "ram-clear")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_http<const nano::http_requests::ram_clear>(con, [this](imaxa_connection_ptr con, const nano::http_requests::ram_clear& req) {
			nano::ERamClearMode mode = nano::ERamClearMode::_from_string_nocase(req.GetConfig("mode").get<std::string>().c_str());
			bool bWriteRamClearHistory = pDBMngr->ExecuteSync<bool>(
			  [&](TGameDBSrv* db, soci::session& sql) -> bool {
				  int64_t in = 0, out = 0, bet = 0, win = 0;
				  SharedScopedLock lock(mPlayerData);
				  for (const auto& player : &mPlayerData | std::views::values)
				  {
					  ScopedLock lockP(*player);
					  in += player->AllCounters().GetCounter({}, LIFETIME_COUNTER_IN).Value;
					  out += player->AllCounters().GetCounter({}, LIFETIME_COUNTER_OUT).Value;
					  bet += player->AllCounters().GetCounter({}, LIFETIME_COUNTER_BET).Value;
					  win += player->AllCounters().GetCounter({}, LIFETIME_COUNTER_WIN).Value;
				  }
				  lock.unlock();

				  sql << "UPDATE RAM_CLEAR_HISTORY SET RAM_CLEAR_TIMESTAMP=CURRENT_TIMESTAMP, \"IN\"=?, OUT=?, BET=?, WIN=? WHERE RAM_CLEAR_TIMESTAMP is NULL",
				    soci::use(in), soci::use(out), soci::use(bet), soci::use(win);
				  mIsInRamClear = true;
				  return true;
			  },
			  false);

			if (!bWriteRamClearHistory)
				Log(Warning, "Could not write to RAM_CLEAR_HISTORY database table. Maybe the table is missing?!");

			pDBMngr->ExecuteSync(
			  [this, mode](TGameDBSrv* db, soci::session& sql) {
				  sql << "DELETE FROM DIGITAL_COUNTERS WHERE COUNTER_INDEX >= 0";
				  sql << "DELETE FROM DIGITAL_COUNTERS_V7 WHERE COUNTER_INDEX >= 0";

				  if (mode >= nano::ERamClearMode::Full)
				  {
					  sql
					    << "delete from CONFIGURATION where (ATTR_NAME like 'MEMORY_PACK_SN' or ATTR_NAME like 'SOFTWARE_VERSION' or ATTR_NAME like 'AFT_CURRENT%' or ATTR_NAME like 'AFT_AUTO_CASHOUT_STATE' or ATTR_NAME like 'AFT_REGISTRATION\\_%' or ATTR_NAME like 'AFT_TRANSFER\\_%' or ATTR_NAME like 'PREV_RESTRICTED_EXPIRATION' or ATTR_NAME like 'PREV_POOL_ID' or ATTR_NAME like 'AFT_HISTORY\\_%') and CLIENT_IP not like 'ROULETTE' ESCAPE '\\'";

					  sql << "DELETE FROM LOG";
					  sql << "DELETE FROM CREDIT_TRANSFERS";
					  if (db->HasCreditTransfersV7())
						  sql << "DELETE FROM CREDIT_TRANSFERS_V7";

					  sql << "DELETE FROM GAME_PHASE_DATA";
					  sql << "DELETE FROM MONETARY";

					  sql << "SET GENERATOR GEN_LOG TO 10";
					  sql << "SET GENERATOR GEN_CREDIT_TRANSFERS TO 10";
					  sql << "SET GENERATOR GEN_CREDIT_TRANSFERS_V7 TO 10";
					  sql << "SET GENERATOR GEN_MONETARY TO 10";

					  sql << "SET GENERATOR GEN_GAME_ID_WHEEL_1 TO 10";
					  sql << "SET GENERATOR GEN_GAME_ID_LADY_1 TO 10000010";
					  sql << "SET GENERATOR GEN_GAME_ID_LADY_2 TO 20000010";
					  sql << "SET GENERATOR GEN_GAME_ID_LADY_3 TO 30000010";
					  sql << "SET GENERATOR GEN_GAME_ID_LADY_4 TO 40000010";
					  sql << "SET GENERATOR GEN_GAME_ID_LADY_5 TO 50000010";
					  sql << "SET GENERATOR GEN_GAME_ID_WHEEL_2 TO 60000010";
					  sql << "SET GENERATOR GEN_GAME_ID_WHEEL_3 TO 70000010";
					  sql << "SET GENERATOR GEN_GAME_ID_WHEEL_4 TO 80000010";
					  sql << "SET GENERATOR GEN_GAME_ID_WHEEL_5 TO 90000010";
					  sql << "SET GENERATOR GEN_GAME_ID TO 100000010";

					  if (db->mDBType == TGameDBSrv::EDBType::firebird)
					  {
						  // first delete all 'DEMO' except GLOBAL, because we will update (or insert) only GLOBAL
						  sql << "DELETE FROM CONFIGURATION where ATTR_NAME like 'DEMO' and CLIENT_IP not like 'GLOBAL'";
						  sql
						    << "UPDATE OR INSERT INTO CONFIGURATION (CLIENT_IP, USER_MODIFIED, ATTR_NAME, ATTR_NUMERIC_VALUE, ATTR_STRING_VALUE, DESCRIPTION_ID, ATTR_CREATED)"
						       "VALUES ('GLOBAL', 'RAM_CLEAR', 'DEMO', 0, '0', NULL, CURRENT_TIMESTAMP)";

						  // first delete all 'RAM_CLEAR_DATE' except GLOBAL, because we will update (or insert) only GLOBAL
						  sql << "DELETE FROM CONFIGURATION where ATTR_NAME like 'RAM_CLEAR_DATE' and CLIENT_IP not like 'GLOBAL'";
						  sql
						    << "UPDATE OR INSERT INTO CONFIGURATION (CLIENT_IP, USER_MODIFIED, ATTR_NAME, ATTR_NUMERIC_VALUE, ATTR_STRING_VALUE, DESCRIPTION_ID, ATTR_CREATED)"
						       "VALUES ('GLOBAL', 'RAM_CLEAR', 'RAM_CLEAR_DATE', 0, '0', NULL, CURRENT_TIMESTAMP)";
						  sql
						    << "UPDATE OR INSERT INTO DESCRIPTIONS2(ATTR_NAME, LANG_ID, DESCRIPTION, CAPTION) VALUES ('RAM_CLEAR_DATA', 11, 'Timestamp of last invoked RAM-clear.', 'Ram Clear Date')";
					  }
					  else if (db->mDBType == TGameDBSrv::EDBType::postgresql)
					  {
						  // first delete all 'DEMO', because we will insert only GLOBAL
						  sql << "DELETE FROM CONFIGURATION where ATTR_NAME like 'DEMO'";
						  sql << "INSERT INTO CONFIGURATION (CLIENT_IP, USER_MODIFIED, ATTR_NAME, ATTR_NUMERIC_VALUE, ATTR_STRING_VALUE, DESCRIPTION_ID, ATTR_CREATED)"
						         "VALUES ('GLOBAL', 'RAM_CLEAR', 'DEMO', 0, '0', NULL, CURRENT_TIMESTAMP) ON CONFLICT DO NOTHING";

						  // first delete all 'RAM_CLEAR_DATE', because we will insert only GLOBAL
						  sql << "DELETE FROM CONFIGURATION where ATTR_NAME = 'RAM_CLEAR_DATE'";
						  sql << "INSERT INTO CONFIGURATION (CLIENT_IP, USER_MODIFIED, ATTR_NAME, ATTR_NUMERIC_VALUE, ATTR_STRING_VALUE, DESCRIPTION_ID, ATTR_CREATED)"
						         "VALUES ('GLOBAL', 'RAM_CLEAR', 'RAM_CLEAR_DATE', 0, '0', NULL, CURRENT_TIMESTAMP)"
						         "ON CONFLICT DO NOTHING";

						  // first delete all 'RAM_CLEAR_DATE', because we will insert new description
						  sql << "DELETE FROM DESCRIPTIONS2 where ATTR_NAME = 'RAM_CLEAR_DATA'";
						  sql
						    << "INSERT INTO DESCRIPTIONS2(ATTR_NAME, LANG_ID, DESCRIPTION, CAPTION) VALUES ('RAM_CLEAR_DATA', 11, 'Timestamp of last invoked RAM-clear.', 'Ram Clear Date') ON CONFLICT DO NOTHING";
					  }
					  else
						  throw std::runtime_error("Invalid database type specified");

					  sql << "DELETE FROM JACKPOT_CLIENTS";

					  if (mode >= nano::ERamClearMode::FactoryReset)
					  {
						  sql << "DELETE FROM CONFIGURATION";
						  sql << "DELETE FROM COMMAND_QUEUE";
						  sql << "DELETE FROM INSTANCES";
						  sql << "DELETE FROM LANGUAGES";
					  }
					  else
					  {
						  std::string username;
						  soci::statement deletePlayerConf =
						    (sql.prepare << "DELETE FROM CONFIGURATION WHERE CLIENT_IP=? AND ATTR_NAME like 'USR\\_%' ESCAPE '\\'", soci::use(username));
						  soci::statement deletePlayerCommands = (sql.prepare << "DELETE FROM COMMAND_QUEUE WHERE CMD_FOR=?", soci::use(username));
						  soci::statement deletePlayer =
						    (sql.prepare << "DELETE FROM INSTANCES WHERE INSTANCE_ID=? AND INSTANCE_TYPE=?", soci::use(username), soci::use(PLAYER_ID));

						  ScopedLock lock(mPlayerData);
						  for (const auto& p : &mPlayerData)
						  {
							  username = p.first;
							  deletePlayerConf.execute(true);
							  deletePlayerCommands.execute(true);
							  deletePlayer.execute(true);

							  ScopedLock pLock(*p.second);
							  p.second->DeletePlayer(false);
						  }
						  mPlayerData->clear();
					  }

					  uint64_t maxID;
					  sql << "SELECT COALESCE(MAX(CONFIG_ID), 1) FROM CONFIGURATION", soci::into(maxID);
					  sql << yutils::Format("SET GENERATOR GEN_CONFIGURATION TO %lu", maxID).c_str();

					  sql << "SELECT COALESCE(MAX(CMD_ID), 1) FROM COMMAND_QUEUE", soci::into(maxID);
					  sql << yutils::Format("SET GENERATOR GEN_COMMAND_QUEUE_ID TO %lu", maxID).c_str();

					  sql << "SELECT COALESCE(MAX(LANG_ID), 1) FROM LANGUAGES", soci::into(maxID);
					  sql << yutils::Format("SET GENERATOR GEN_LANGUAGES TO %lu", maxID).c_str();
				  }
				  else
				  {
					  SharedScopedLock lock(mPlayerData);
					  for (const auto& p : &mPlayerData)
					  {
						  ScopedLock lockP(*p.second);
						  p.second->LastCounterReset = ytime::GetSystemTimeMsec();
						  p.second->ClearCachedData();
					  }
				  }

				  db->_SetParam(ClientID(), "NANO", "GAMEROUND_ID", "1", "Gameround ID", "The last gameround ID", ELanguage::English, false);
			  },
			  0, false, true, { ETransactionFlags::WaitTransaction });

			std::error_code ec;
			{
				ScopedLock lock(mPlayerListeners);
				for (const auto& key : &mPlayerListeners | std::views::keys) { Endpoint->close(key, websocketpp::close::status::going_away, "Ram clear", ec); }
				mPlayerListeners->clear();
			}

			{
				ScopedLock lock(mGeneralListeners);
				for (const auto& key : &mGeneralListeners | std::views::keys) { Endpoint->close(key, websocketpp::close::status::going_away, "Ram clear", ec); }
				mGeneralListeners->clear();
			}

			std::filesystem::remove_all(mNanoDataDirectory);
			std::filesystem::remove_all(mGameroundsDirectory / "Archive");

			if (TransactionDatabase)
			{
				TransactionDatabase->Close();
				delete TransactionDatabase;
				TransactionDatabase = NULL;
			}
			const std::filesystem::path dbpath = mNanoDataDirectory / "Transactions";
			std::filesystem::remove_all(dbpath);

			rocksdb::Options dbOptions;
			dbOptions.create_if_missing = true;
			rocksdb::DB::Open(dbOptions, dbpath, &TransactionDatabase);

			send_with_status(con, web::http::status::ok);
		});
	}
	else if (fragments[0] == "is-ram-cleared")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::player_based_request>(
		  con,
		  [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::player_based_request& ramclear) {
			  json responseObj(json::value_t::object);
			  responseObj["cleared"] = mIsInRamClear;
			  std::error_code ec;
			  con->set_json_body(responseObj, {}, ec);
			  send_with_status(con, web::http::status::ok);
		  },
		  true, true);
	}
	else if (fragments[0] == "counters")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::get_counters>(
		  con,
		  [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::get_counters& counters) {
			  json responseObj(json::value_t::object);
			  DigitalCounterCache<CounterMap> results;
			  if (player)
			  {
				  for (const auto& [game, counterSet] : counters.RequestedCounters)
				  {
					  auto& gameCounters = results.GetGameCounters(game);
					  gameCounters = player->GetCounters(counterSet, game);
				  }
			  }
			  else if (counters.Username == DEAD_PLAYER_CLIENT_IP)    // for dead players
			  {
				  SharedScopedLock lock(mDeadPlayerCounters);

				  for (const auto& [game, gameCounters] : counters.RequestedCounters)
				  {
					  const std::vector<GameIdentifier> gamesInCategory = mDeadPlayerCounters->GetGamesInCategory(game);

					  for (const GameIdentifier& g : gamesInCategory)
					  {
						  auto& perGameCounters = mDeadPlayerCounters->GetGameCounters(g);
						  auto& outCounters = results.GetGameCounters(g);
						  if (gameCounters.empty())
						  {
							  outCounters = perGameCounters;
						  }
						  else
						  {
							  for (const int& counter : gameCounters)
							  {
								  CounterInfo info;
								  auto find = perGameCounters.find(counter);
								  if (find != perGameCounters.end())
									  info = find->second;
								  outCounters[counter] = info;
							  }
						  }
					  }
				  }

				  responseObj = results.ToJSON();
			  }
			  else    // for all players
			  {
				  for (const auto& [game, gameCounters] : counters.RequestedCounters)
				  {
					  const bool bAllCounters = gameCounters.empty();
					  {
						  SharedScopedLock lock(mDeadPlayerCounters);
						  const std::vector<GameIdentifier> gamesInCategory = mDeadPlayerCounters->GetGamesInCategory(game);
						  for (const GameIdentifier& g : gamesInCategory)
						  {
							  const auto& deadPlayerCounters = mDeadPlayerCounters->GetGameCounters(g);
							  CounterMap& outCounters = results.GetGameCounters(g);
							  if (bAllCounters)
							  {
								  for (const auto& [counterId, info] : deadPlayerCounters) outCounters[counterId].Value += info.Value;
							  }
							  else
							  {
								  Timaxanano::intersect_counter_sum(outCounters, deadPlayerCounters, gameCounters);
							  }
						  }
					  }

					  SharedScopedLock lock(mPlayerData);
					  auto playerList = &mPlayerData;
					  lock.unlock();

					  for (const auto& player : playerList | std::views::values)
					  {
						  ScopedLock playerLock(*player);
						  const DigitalCounterCache<CounterMap> playerCounters = player->GetCountersCategory(gameCounters, game);
						  playerCounters.ForEachCounter([&results, bAllCounters, playerCounters](const GameIdentifier& g, const int counterId, const CounterInfo& info) {
							  if (!bAllCounters && playerCounters.GetGameCounters(g).empty())
								  return;
							  results.GetGameCounters(g)[counterId].Value += info.Value;
						  });
					  }
				  }
			  }
			  responseObj = results.ToJSON();

			  std::error_code ec;
			  con->set_json_body(responseObj, {}, ec);
			  send_with_status(con, web::http::status::ok);
		  },
		  true, true);
	}
	else if (fragments[0] == "delete-player")
	{
		if (fragments.size() > 1)
		{
			send_with_status(con, web::http::status::bad_request, "Junk resource path");
			return;
		}

		handle_player_http<const nano::http_requests::delete_player>(
		  con, [this](imaxa_connection_ptr con, const std::shared_ptr<NanoPlayer>& player, const nano::http_requests::delete_player& del) {
			  if (!player->Balance.IsEmpty())
			  {
				  send_with_status(con, web::http::status::not_acceptable, "Cannot delete a player that has a non-zero balance!");
				  return;
			  }

			  player->DeletePlayer(true);
			  mPlayerData.Lock();
			  mPlayerData->erase(player->Username);
			  mPlayerData.Unlock();
			  send_with_status(con, web::http::status::ok);
		  });
	}
	else
	{
		send_with_status(con, web::http::status::bad_request, "Unknown resource fragment '" + fragments[0] + "'");
	}
}

void Timaxanano::send_error(imaxa_connection_hdl_ref hdl, int errCode, const std::string& errorName, const std::string& errorDesc, const json& errData) {}

bool Timaxanano::CreateSession(NanoPlayer& player, const std::shared_ptr<Session>& session)
{
	ScopedLock lock(mSessions);
	const bool bAdded = mSessions->emplace(session->SID, session).second;
	lock.unlock();
	if (bAdded)
	{
		ScopedLock playerLock(player);
		player.Sessions.emplace(session->SID, session);
	}
	return bAdded;
}

const JsonSchema ImaxananoConf = JsonSchema(
  { { "db", JsonSchema({ { "address", JsonSchema(json::value_t::string, "Database address", "127.0.0.1/3050") },
                         { "name", JsonSchema(json::value_t::string, "Database file name", "PC_DATA.GDB") },
                         { "username", JsonSchema(json::value_t::string, "The username hash to use for authenticating to the database") },
                         { "password", JsonSchema(json::value_t::string, "The password hash to use for authenticating to the database") },
                         { "type", JsonSchema(json::value_t::string, "The type of database connection to use (Firebird, PostgreSQL)", "Firebird") } }) },
    { "demo.credits", JsonSchema(json::value_t::number_unsigned, "Amount of credits to give demo players", 0U) },
    { "disk-sync", JsonSchema(json::value_t::boolean, "Should disk operations be synchronized (flushed)", false) },
    { "workers", JsonSchema(json::value_t::number_unsigned, "Number of worker threads to spawn", 8U).AddConstraint(limits::IValueLimit::MakeRange(2U, {}, true, false)) },
    { "player",
      JsonSchema(
        { { "authRequired", JsonSchema(json::value_t::boolean, "Are users required to authenticate when connecting to their session", false) },
          { "payoutEnabled", JsonSchema(json::value_t::boolean, "Are payout requests from sessions allowed?", true) },
          { "deleteTimeout", JsonSchema(json::value_t::number_unsigned, "How long to wait before removing dead players (minutes)?", 60U) },
          { "checkDeleteInterval", JsonSchema(json::value_t::number_unsigned, "How often to check for invalid players and purge them (seconds)?", 60U) },
          { "sessionTimeout", JsonSchema(json::value_t::number_unsigned, "How long to wait before timing out an inactive session (minutes, 0 means never)", 20U) },
          { "manage-kiosk-password",
            JsonSchema(json::value_t::boolean, "True if kiosk player password should be managed (reset after reaching 0 or required after payin at 0)", false) },
          { "generic-transfer-tokens",
            JsonSchema(json::value_t::boolean, "If true, transfer tokens are allowed to be redeemed by any player, not just the one who created the token.", true) } }) },
    { "storage-dir",
      JsonSchema(json::value_t::string, "The directory to use (or create if nonexistent) as a writable root directory for Nano data", "/var/IGP/nano-data") },
    { "archive-lifetime", JsonSchema(json::value_t::number_unsigned, "How old do archived gamerounds have to be before deleting them (days)?", 14U) } });

std::string ALLOW_UNLIMITED_CREDITS_SWITCH = "allow-unlimited-credits";

Timaxanano::Timaxanano(const std::string& type) : TApplication(type)
{
	mPort = DEFAULT_NANO_PORT;
	bPrintCommands = false;

	Schema() += ImaxananoConf;

	SetLogicInterval(2);

	SetLogCategory(LogNano);

	mConnectionAcceptHandler = std::bind(&Timaxanano::accept_ws, this, std::placeholders::_1);

	LaunchArgs.RegisterSwitch(ALLOW_UNLIMITED_CREDITS_SWITCH);
}

Timaxanano::~Timaxanano()
{
	if (TransactionDatabase)
	{
		TransactionDatabase->Close();
		delete TransactionDatabase;
	}
}

/* main init */
int Timaxanano::Init(const std::vector<std::string>& args)
{
	if (!args.empty())
		SetParam("Config", args[0]);

	TApplication::Init(args);

	if (LaunchArgs.HasSwitch(ALLOW_UNLIMITED_CREDITS_SWITCH))
		bAllowUnlimitedCredits = true;
	Log(Info, "Allow unlimited credits: %s", bAllowUnlimitedCredits ? "TRUE" : "FALSE");

	std::string conf = GetParam("Config")->Value();
	try
	{
		LoadPatchedConfiguration(conf, GetParam("UserConfig", { ParameterDomain::CACHE })->Value());
	}
	catch (const ConfigError& err)
	{
		Log(Critical, "CONFIG FILE %s COULD NOT LOAD: %s", conf.c_str(), err.what());
		return -1;
	}

	/* Load DBInterface module */
	if ((pDBMngr = new TDBManager())->InitOnly("DBManager1", 99))
		return -1;

	if ((pDBMngrTransactions = new TDBManager())->InitOnly("DBManager2", 100))
		return -1;

	pDBMngrTransactions->DatabaseAddress = pDBMngr->DatabaseAddress = mMainDatabaseInfo.Address;
	pDBMngrTransactions->DatabaseName = pDBMngr->DatabaseName = mMainDatabaseInfo.DatabaseName;
	pDBMngrTransactions->Username = pDBMngr->Username = HashKey(mMainDatabaseInfo.User);
	pDBMngrTransactions->Password = pDBMngr->Password = HashKey(mMainDatabaseInfo.Pass);

	if (pDBMngr->Load(NULL))
		return -1;

	pDBMngr->OnUnload += [this]() {
		pDBMngr = NULL;
	};

	if (pDBMngrTransactions->Load(NULL))
		return -1;

	pDBMngrTransactions->OnUnload += [this]() {
		pDBMngrTransactions = NULL;
	};

	pDBMngr->ExecuteSync(
	  [](TGameDBSrv* db, soci::session& sql) {
		  const std::string newCommandEvent = DBEvent_NewCmd + "_";
		  db->RegisterEvent(newCommandEvent + pApp->GetGlobalDomain());
		  db->RegisterEvent(newCommandEvent + PLAYER_ID);
		  db->RecreateEventListener();
	  },
	  0, false, false);

	Log(Info, "Loading parameters from DB...");
	const std::set<std::string> cachedParams = pApp->GetCachedParams(PARAM_MASK_NO_CACHE);
	pDBMngr->ExecuteSync([cachedParams](TGameDBSrv* db, soci::session& sql) {
		std::string procName;
		std::string group(pApp->GetGroupDomain());
		soci::rowset allParams = (sql.prepare << R"(SELECT cfg.CLIENT_IP, cfg.ATTR_NAME, cfg.ATTR_STRING_VALUE, cfg.ATTR_NUMERIC_VALUE, dsc.CAPTION, dsc.DESCRIPTION from
									CONFIGURATION cfg LEFT JOIN
									(SELECT ATTR_NAME, CAPTION, DESCRIPTION from DESCRIPTIONS2 WHERE LANG_ID=11) dsc
									ON cfg.ATTR_NAME=dsc.ATTR_NAME
									WHERE (cfg.CLIENT_IP='GLOBAL' OR cfg.CLIENT_IP=? OR cfg.CLIENT_IP=?)
									)",
		                          soci::use(group), soci::use(pApp->ClientID()));

		std::string ParamName, Value;
		size_t count = 0;
		for (const soci::row& paramRow : allParams)
		{
			const std::string domain = paramRow.get<std::string>(0);
			ParamName = paramRow.get<std::string>(1);

			// this would mean we already read this parameter from DB, so don't do it again
			if (cachedParams.contains(domain + "|" + ParamName))
				continue;

			Value.clear();
			if (paramRow.get_indicator(2) == soci::i_ok)
			{
				Value = paramRow.get<std::string>(2);
			}
			else if (paramRow.get_indicator(3) == soci::i_ok)
			{
				Value = std::to_string(paramRow.get<big_int>(3));
			}

			std::shared_ptr<TAppParam> newParam = std::make_shared<TAppParam>(ParamName, domain);
			newParam->Parameter::SetValue(Value);
			newParam->Parameter::SetRule(pApp->GetLimits().Find(ParamName));

			if (paramRow.get_indicator(4) == soci::i_ok)
				newParam->SetCaption(paramRow.get<std::string>(4));
			if (paramRow.get_indicator(5) == soci::i_ok)
				newParam->SetDescription(paramRow.get<std::string>(5));

			pApp->AddParamToCache(newParam);

			count++;
		}

		TLOG(LogApp, Info, "Loaded %lu parameters from DB.", count);
	});

	pApp->AddTimedTask<TtthLambda>(
	  [this]() {
		  std::map<std::string, size_t> events;
		  pDBMngr->TriggerEvents(events);
		  if (!events.empty())
			  pDBMngr->ExecuteAsync(
			    [this, events](TGameDBSrv* db, soci::session& sql) {
				    for (const std::pair<const std::string, size_t>& p : events) OnDBEvent(db, p.first, p.second);
			    },
			    false, true, { ETransactionFlags::WaitTransaction });
	  },
	  1000, TTimedTaskHandler::ENDLESS, "Trigger DB events task");

	int initRes = 0;
	do {
		if (initRes)
		{
			SaveParams();
			SDL_Delay(1000);
		}
		initRes = StartServer();
	} while ((initRes != 0) && !shuttingDown());

	if (initRes != 0)
	{
		Log(Critical, "IMAXA NANO SERVER COULD NOT START");
		return -1;
	}

	Log(Important, "======= IMAXA NANO SERVER %s IS ONLINE =======", version::FULL_VERSION_STRING);

	// Do this to tell the application main thread all GUI loading is complete
	setStatus(EApplicationState::Startup);

	return 0;
}

void Timaxanano::OnConfigLoaded(const std::filesystem::path& filename)
{
	WebServer::OnConfigLoaded(filename);

	mNumWorkerThreads = GetConfig("workers").get<uint32_t>();

	mMainDatabaseInfo.Address = GetConfig("db.address").get<std::string>();
	mMainDatabaseInfo.DatabaseName = GetConfig("db.name").get<std::string>();
	mMainDatabaseInfo.User = GetConfig("db.username").get<std::string>();
	mMainDatabaseInfo.Pass = GetConfig("db.password").get<std::string>();
	mMainDatabaseInfo.Type = GetConfig("db.type").get<std::string>();
	SetParam("DBType", mMainDatabaseInfo.Type);

	// če naslov ne vsebuje informacije o portu, jo dodamo - zato, da nam lepo dela primerjava med JACKPOT_DB_SRV in DB_SRV
	if (std::string::npos == mMainDatabaseInfo.Address.rfind("/"))
		mMainDatabaseInfo.Address += "/3050";

	mDemoCredits = GetConfig("demo.credits").get<uint64_t>();
	Log(Info, "Demo players will be given %lu credits.", mDemoCredits);

	bAuthRequired = GetConfig("player.authRequired").get<bool>();
	Log(Info, "Authentication required: %s", bAuthRequired ? "TRUE" : "FALSE");

	bPayoutEnabled = GetConfig("player.payoutEnabled").get<bool>();
	Log(Info, "Payout enabled: %s", bPayoutEnabled ? "TRUE" : "FALSE");

	mDeadPlayerTimeout = GetConfig("player.deleteTimeout").get<uint64_t>() * 60 * 1000;    // one hour, in milliseconds
	Log(Info, "Dead player removal set to %lu minutes.", mDeadPlayerTimeout / 60000);

	mCheckDeadPlayerInterval = GetConfig("player.checkDeleteInterval").get<uint64_t>() * 1000;    // one minute, in milliseconds
	Log(Info, "Dead player removal check will be done every %lu seconds.", mCheckDeadPlayerInterval / 1000);

	mSessionTimeout = GetConfig("player.sessionTimeout").get<uint64_t>() * 60 * 1000;    // 20 minutes, in milliseconds
	Log(Info, "Session timeout set to %lu minutes.", mSessionTimeout / 60000);

	mNanoDataDirectory = GetConfig("storage-dir").get<std::string>();
	Log(Info, "Storage directory for player data: %s", mNanoDataDirectory.c_str());

	mDaysUntilArchiveDeletion = GetConfig("archive-lifetime").get<uint32_t>();
	Log(Info, "Will delete gameround archive entries older than %u days.", mDaysUntilArchiveDeletion);

	bManageKioskPassword = GetConfig("player.manage-kiosk-password").get<bool>();
	Log(Info, "Kiosk player password management: %s", bManageKioskPassword ? "ON" : "OFF");

	bDiskSync = GetConfig("disk-sync").get<bool>();

	if (!std::filesystem::exists(mNanoDataDirectory))
	{
		Log(Warning, "Storage directory doesn't exist, will create necessary folders to reach %s", mNanoDataDirectory.c_str());
		if (std::filesystem::create_directories(mNanoDataDirectory))
		{
			Log(Info, "Successfully created storage directory", mNanoDataDirectory.c_str());
		}
		else
		{
			throw ConfigError("Could not create storage directories required to reach " + mNanoDataDirectory.string());
		}
	}

	bCrossPlayerTransferTokensAllowed = GetConfig("player.generic-transfer-tokens").get<bool>();

	mGameroundsDirectory = mNanoDataDirectory / "Gamerounds";
	mPlayersDirectory = mNanoDataDirectory / "Players";

	std::filesystem::create_directories(mGameroundsDirectory / "Archive");
	std::filesystem::create_directories(mNanoDataDirectory / "Tokens");
	std::filesystem::create_directories(mPlayersDirectory);

	const std::filesystem::path DB_Folder = mNanoDataDirectory / "Transactions";

	rocksdb::Options dbOptions;
	dbOptions.create_if_missing = true;

	rocksdb::Status status = rocksdb::DB::Open(dbOptions, DB_Folder, &TransactionDatabase);

	if (!status.ok())
		throw ConfigError("Unable to open or create transaction database at " + DB_Folder.string());

	Log(Info, "Successfully opened database at %s.", DB_Folder.c_str());
}

void Timaxanano::OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& e)
{
	web::WebServer::OnInitialize(e);

	// Register our message handler
	e->set_message_handler(std::bind(&Timaxanano::on_message, this, std::placeholders::_1, std::placeholders::_2));

	e->set_user_agent("imaxanano " + std::string(version::FULL_VERSION_STRING));
}

const int64_t UTC_OFFSET_SEC = ytime::GetUTCOffsetSeconds();
int Timaxanano::StartServer()
{
	Initialize();

	Log(Info, "Loading money accounting parameters from DB...");
	pDBMngr->ExecuteSync(
	  [this](TGameDBSrv* db, soci::session& sql) {
		  int64_t MCoeff = db->_GetOrSetParam(GetGlobalDomain(), "BILL_CURRENCY_TO_CREDIT", "1", "Conversion To Credit",
		                                      "Value in money for BILL_CREDIT_TO_CURRENCY credits (stevec v denom)")
		                     .AsInteger();
		  int64_t CCoeff =
		    db->_GetOrSetParam(GetGlobalDomain(), "BILL_CREDIT_TO_CURRENCY", "1", "Conversion To Money", "Credits for BILL_CURRENCY_TO_CREDIT money (imenovalec v denom)")
		      .AsInteger();
		  mDenomination = double(MCoeff) / CCoeff;
		  mCurrency = db->_GetOrSetParam(GetGlobalDomain(), "BILL_CURRENCY", "NONE", "Currency ISO code", "The currency used by this machine").AsString();
		  mCurrencySymbol = yutils::Trim(CurrencyInfo::GetCurrencySymbolFromCode(mCurrency));

		  mJurisdiction = db->_GetParamForIP(GetGlobalDomain(), "JURISDICTION", 11, { ParameterDomain::GLOBAL }).Value();

		  mCounterScale =
		    db->_GetOrSetParam(GetGlobalDomain(), "COUNTERS_SCALE", "100", "Counters Scale", "The coefficient in percents, which is multiplied to the lifetime counters.")
		      .AsInteger();

		  mLocale = db->_GetOrSetParam(GetGlobalDomain(), "LOCALE", "en_US", "The default locale", "The default locale on this machine").Value();
		  mMachineName = db->GetMachineID();

		  mDecimalChar =
		    db->_GetOrSetParam(GetGlobalDomain(), "SYSTEM_DECIMAL_SEPARATOR", ",", "Decimal Separator", "Separator for decimal places - max one character!!!").Value();
		  mThousandChar =
		    db->_GetOrSetParam(GetGlobalDomain(), "SYSTEM_THOUSAND_SEPARATOR", ".", "Thousand Separator", "Separator for thousand groups - max one character!!!").Value();
		  mDecimalPlaces =
		    db->_GetOrSetParam(GetGlobalDomain(), "SYSTEM_CURRENCY_DECIMAL_PLACES", "2", "Currency Decimal Places", "Decimal places used for showing currency in program")
		      .AsInteger();
		  mAlignLeft = db->_GetOrSetParam(GetGlobalDomain(), "CURRENCY_ON_LEFT", "0", "Display Currency on left",
		                                  "If true, will display the currency symbol on the left side of the amount, otherwise on the right side.")
		                 .AsBoolean();

		  mIsInPresentationMode = db->_GetOrSetParam(GetGlobalDomain(), "DEMO", "0", "Demo mode", "Is demo mode enabled").AsInteger() == 2;
	  },
	  0, false, true, { ETransactionFlags::WaitTransaction });

	bool bRamClearHistoryTableOK = pDBMngr->ExecuteSync<bool>(
	  [this](TGameDBSrv* db, soci::session& sql) -> bool {
		  int64_t count = 0;
		  sql << "select coalesce(sum(COUNTER_VALUE), 0) from DIGITAL_COUNTERS where COUNTER_INDEX>=51 and COUNTER_INDEX<=54", soci::into(count);
		  mIsInRamClear = (sql.got_data() && count == 0);
		  if (!mIsInRamClear)    // if not in RAM_CLEAR
		  {
			  sql << "select count(COUNTING_FROM_TIMESTAMP) from RAM_CLEAR_HISTORY", soci::into(count);
			  if (sql.got_data() && count == 0)    // if table RAM_CLEAR_HISTORY empty
			  {
				  std::tm countingFrom;
				  soci::indicator countingIndicator;
				  sql << "select COUNTING_FROM from DIGITAL_COUNTERS where COUNTER_INDEX=51", soci::into(countingFrom, countingIndicator);
				  if (!(sql.got_data() && countingIndicator == soci::indicator::i_ok))    // if didn't get COUNTING_FROM from payin counter
				  {
					  time_t timeNow = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
					  localtime_r(&timeNow, &countingFrom);
				  }
				  sql << "insert into RAM_CLEAR_HISTORY(COUNTING_FROM_TIMESTAMP, DENOMINATION, CURRENCY, COUNTER_SCALE) values(?,?,?,?)", soci::use(countingFrom),
				    soci::use(mDenomination), soci::use(mCurrency), soci::use(mCounterScale);
			  }
		  }
		  return true;
	  },
	  false);

	if (!bRamClearHistoryTableOK)
		Log(Warning, "Database table RAM_CLEAR_HISTORY is missing!");

	auto param = GetOrSetParam("MAX_BET_CENTS", 0, "Maximum bet per game", "The maximum bet allowed per game in cents", PARAM_READ_GLOBAL_WRITE_GLOBAL);
	mMaxBet = param->AsInteger() * 1e-2;
	param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			mMaxBet = param->AsInteger() * 1e-2;
			json ctx(json::value_t::object);
			ctx["name"] = "max-bet";
			ctx["value"] = mMaxBet;
			BroadcastEvent("config-changed", ctx);

			Log(Important, "Max Bet param changed to %.2f!", mMaxBet);
		}
	};

	param =
	  GetOrSetParam("MAX_WIN_LIMIT", 0, "Win Limit", "Highest possible win value. When exceeded, win amounts will request handpay.", PARAM_READ_GLOBAL_WRITE_GLOBAL);
	mMaxWin = param->AsInteger();
	param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			mMaxWin = param->AsInteger();
			Log(Important, "Max Win param changed to %d!", mMaxWin);
		}
	};

	const int64_t DEFAULT_MAX_BALANCE = 999999999UL;
	param = GetOrSetParam("MAX_CREDITS", DEFAULT_MAX_BALANCE, "Credits Limit",
	                      "Highest possible credit value. When exceeded, win amounts will request handpay and payins that would exceed it are not allowed.",
	                      PARAM_READ_GLOBAL_WRITE_GLOBAL);
	mMaxBalance = param->AsInteger();
	param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			mMaxBalance = param->AsInteger();
			Log(Important, "Max Balance param changed to %d!", mMaxBalance);
		}
	};
	if (bAllowUnlimitedCredits && mMaxBalance == 0)
		param->SetValue(0, true);
	else if (mMaxBalance == 0 || mMaxBalance > DEFAULT_MAX_BALANCE)
		param->SetValue(DEFAULT_MAX_BALANCE, true);

	param = GetOrSetParam(
	  "DEPOSIT_ON_MULTIPLE_TERMINALS", true, "Deposit on multiple terminals",
	  "If true, depositing on multiple terminals with the same transaction 'owner' is allowed. If false, only one player can have a deposit made to it by a given owner.",
	  PARAM_READ_GLOBAL_WRITE_GLOBAL);
	bAllowDepositFromSameOwnerOnMultipleAccounts = param->AsBoolean();
	param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
			bAllowDepositFromSameOwnerOnMultipleAccounts = param->AsInteger();
	};


	Log(Important, "Connection established with IMAXA db '%s' at %s. Currency: %s  Jurisdiction: %s   Denomination: %.2f  Machine name: %s",
	    mMainDatabaseInfo.DatabaseName.c_str(), mMainDatabaseInfo.Address.c_str(), mCurrency.c_str(), mJurisdiction.c_str(), mDenomination, MachineName().c_str());

	Log(Info, "Loading authentication data...");
	AuthKeys.emplace(std::string(), FNanoAuthKey({ .Secret = SALT_KEY }));
	pDBMngr->ExecuteSync([this](TGameDBSrv* db, soci::session& sql) {
		soci::rowset nanoKeyRowset =
		  (sql.prepare << "SELECT ATTR_NAME, ATTR_STRING_VALUE, ATTR_NUMERIC_VALUE FROM CONFIGURATION WHERE CLIENT_IP=?", soci::use(NANO_KEY_CLIENT_ID));

		for (const soci::row& row : nanoKeyRowset)
		{
			FNanoAuthKey key;
			key.Secret = row.get<std::string>(1);
			if (row.get_indicator(2) == soci::i_ok)
				key.Permissions = row.get<big_int>(2);

			AuthKeys.emplace(row.get<std::string>(0), std::move(key));
		}
	});

	Log(Info, "Loading dead player counters...");
	ScopedLock deadPlayerLock(mDeadPlayerCounters);
	pDBMngr->ExecuteSync([this](TGameDBSrv* db, soci::session& sql) {
		std::string SQL;
		if (db->mDBType == TGameDBSrv::EDBType::firebird)
		{
			if (db->HasDigitalCountersV7())
			{
				SQL =
				  "SELECT COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTER_VALUE, DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', COUNTING_FROM) FROM DIGITAL_COUNTERS_V7 WHERE CLIENT_ID=? AND MOD(COUNTER_INDEX, 1000) < 900";
			}
			else
			{
				SQL =
				  "SELECT COUNTER_INDEX, COUNTER_VALUE, DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', COUNTING_FROM) FROM DIGITAL_COUNTERS WHERE CLIENT_IP=? AND MOD(COUNTER_INDEX, 1000) < 900";
			}
		}
		else if (db->mDBType == TGameDBSrv::EDBType::postgresql)
		{
			SQL =
			  "SELECT COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTER_VALUE, EXTRACT(EPOCH FROM (COUNTING_FROM - '1970-01-01 00:00')) * 1000 FROM DIGITAL_COUNTERS_V7 WHERE CLIENT_ID=? AND MOD(COUNTER_INDEX, 1000) < 900";
		}
		else
			throw std::runtime_error("Invalid database type specified");

		if (db->HasDigitalCountersV7())
		{
			soci::rowset deadCounters = (sql.prepare << SQL, soci::use(DEAD_PLAYER_CLIENT_IP));
			for (const soci::row& row : deadCounters)
			{
				GameIdentifier gameId;

				int counterId = row.get<int>(0);
				gameId.Game = row.get<int>(1);
				gameId.Configuration = row.get<std::string>(2);

				CounterInfo info;
				info.Value = row.get<big_int>(3);
				info.CountingFrom_Timestamp = row.get_indicator(4) == soci::i_null ? ytime::GetSystemTimeMsec() : (row.get_casted<big_int>(4) - UTC_OFFSET_SEC * 1000);

				mDeadPlayerCounters->GetGameCounters(gameId)[counterId] = info;
			}
		}
		else
		{
			soci::rowset deadCounters = (sql.prepare << SQL, soci::use(DEAD_PLAYER_CLIENT_IP));
			for (const soci::row& row : deadCounters)
			{
				GameIdentifier gameId;
				int counterId = row.get<int>(0);
				counterId = NanoCounters::unmapCounterId_Legacy(counterId, &gameId);

				CounterInfo info;
				info.Value = row.get<big_int>(1);
				info.CountingFrom_Timestamp = row.get_indicator(2) == soci::i_null ? ytime::GetSystemTimeMsec() : (row.get_casted<big_int>(2) - UTC_OFFSET_SEC * 1000);

				mDeadPlayerCounters->GetGameCounters(gameId)[counterId] = info;
			}
		}
	});

	Log(Info, "Loading players into memory...");

	ScopedLock playerLock(mPlayerData);
	pDBMngr->ExecuteSync(
	  [&](TGameDBSrv* db, soci::session& sql) {
		  std::string SQL;
		  if (db->mDBType == TGameDBSrv::EDBType::firebird)
		  {
			  SQL =
			    "SELECT INSTANCES.*, DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', INSTANCE_LAST_UPDATE_TIMESTAMP) as LAST_UPDATE_EPOCH from INSTANCES where INSTANCE_TYPE=?";
		  }
		  else if (db->mDBType == TGameDBSrv::EDBType::postgresql)
		  {
			  SQL =
			    "SELECT INSTANCES.*, EXTRACT(EPOCH FROM (INSTANCE_LAST_UPDATE_TIMESTAMP - '1970-01-01 00:00')) * 1000 as LAST_UPDATE_EPOCH from INSTANCES where INSTANCE_TYPE=?";
		  }
		  else
			  throw std::runtime_error("Invalid database type specified");

		  soci::rowset players = (sql.prepare << SQL, soci::use(PLAYER_ID));
		  std::map<std::string, std::vector<TGameDBSrv::Instance>> Instances;
		  for (const soci::row& playerData : players)
		  {
			  TGameDBSrv::Instance Inst;
			  try
			  {
				  Inst.ID = playerData.get<std::string>("INSTANCE_ID");
				  Inst.Type = PLAYER_ID;
				  Inst.Number = playerData.get<int>("INSTANCE_TYPE_NUMBER");
				  Inst.Status = playerData.get<std::string>("INSTANCE_STATUS", Inst.Status);
				  Inst.MAC = playerData.get<std::string>("INSTANCE_MAC_ADDRESS");
				  Inst.PhysicalIP = playerData.get<std::string>("INSTANCE_PHYSICAL_IP", Inst.PhysicalIP);
				  Inst.Port = playerData.get<int>("INSTANCE_LISTEN_PORT");
				  Inst.SSHPort = playerData.get<int>("INSTANCE_SSH_PORT", Inst.SSHPort);
				  Inst.SCPFolder = playerData.get<std::string>("INSTANCE_SCP_FOLDER", Inst.SCPFolder);
				  Inst.VideoURL = playerData.get<std::string>("INSTANCE_VIDEO_URL", Inst.VideoURL);
				  Inst.AudioURL = playerData.get<std::string>("INSTANCE_AUDIO_URL", Inst.AudioURL);
				  std::string featureList = playerData.get<std::string>("INSTANCE_SUPPORTED_FEATURES", std::string());
				  Inst.Features = yutils::Split(featureList, ";", true);
				  Inst.LastUpdateTimestamp = playerData.get_indicator("LAST_UPDATE_EPOCH") == soci::i_null ? 0 : playerData.get_casted<big_int>("LAST_UPDATE_EPOCH");
				  Inst.bValid = true;
				  Instances[Inst.ID].push_back(Inst);
			  }
			  catch (...)
			  {
				  Log(Warning, "Error loading instance: %s", Inst.Print().c_str());
			  }
		  }

		  for (auto& pair : Instances)
		  {
			  TGameDBSrv::Instance Inst;
			  if (pair.second.size() > 1)
			  {
				  auto find = std::find_if(pair.second.begin(), pair.second.end(),
				                           [](const TGameDBSrv::Instance& inst) -> bool { return inst.MAC == HashKey(inst.MAC.c_str(), inst.MAC.size(), true); });
				  if (find == pair.second.end())
					  find = pair.second.begin();
				  bool bIsHandpay = false;

				  std::string mac;
				  int port;
				  soci::statement deleteDuplicates =
				    (sql.prepare << "DELETE FROM INSTANCES WHERE INSTANCE_MAC_ADDRESS=? AND INSTANCE_LISTEN_PORT=?", soci::use(mac), soci::use(port));
				  for (auto it = pair.second.begin(); it != pair.second.end(); it++)
				  {
					  if (it == find)
						  continue;
					  if (it->Status == HANDPAY_STATUS)
						  bIsHandpay = true;

					  mac = it->MAC;
					  port = it->Port;
					  deleteDuplicates.execute(true);
				  }
				  Inst = *find;
				  if (bIsHandpay)
					  Inst.Status = HANDPAY_STATUS;
			  }
			  else
			  {
				  Inst = pair.second.front();
			  }

			  // Migration from old system
			  const std::string hash = HashKey(Inst.ID.c_str(), Inst.ID.size(), true);
			  if (Inst.MAC != hash)
			  {
				  Inst.MAC = hash;
				  sql << "SELECT ATTR_STRING_VALUE FROM CONFIGURATION WHERE ATTR_NAME='NICKNAME' AND CLIENT_IP=?", soci::use(Inst.ID), soci::into(Inst.PhysicalIP);

				  if (sql.got_data())
				  {
					  Inst.PhysicalIP = Inst.PhysicalIP.substr(0, std::min(15UL, Inst.PhysicalIP.length()));
					  sql << "DELETE FROM CONFIGURATION WHERE ATTR_NAME='NICKNAME' AND CLIENT_IP=?", soci::use(Inst.ID);
				  }

				  std::string pass;
				  if (!Inst.SCPFolder.empty())
					  pass = crypto::Hash(SALT_KEY + crypto::Hash(Inst.SCPFolder, EHashAlgorithm::SHA256), EHashAlgorithm::SHA256);

				  sql << "UPDATE INSTANCES SET INSTANCE_MAC_ADDRESS=?, INSTANCE_PHYSICAL_IP=?, INSTANCE_SCP_FOLDER=? WHERE INSTANCE_ID=? AND INSTANCE_TYPE=?",
				    soci::use(Inst.MAC), soci::use(Inst.PhysicalIP), soci::use(pass), soci::use(Inst.ID), soci::use(PLAYER_ID);
				  Log(Info, "Migrated player %s to imaxanano system.", Inst.ID.c_str());

				  sql.get_backend()->commit_retain();

				  if (!db->HasCreditTransfersV7())
				  {
					  soci::rowset transfers =
					    (sql.prepare
					       << "SELECT CT_ID, CLIENT_IP, FROM_IP, TO_IP FROM CREDIT_TRANSFERS WHERE (FROM_IP=? OR TO_IP=?) AND (CLIENT_IP='MONEY_IN' OR CLIENT_IP='MONEY_OUT')",
					     soci::use(Inst.ID), soci::use(Inst.ID));

					  size_t migratedTransactions = 0;
					  for (const soci::row& row : transfers)
					  {
						  int64_t ID = row.get<big_int>(0);
						  std::string transType = row.get<std::string>(1);
						  std::string from = row.get<std::string>(2);
						  std::string to = row.get<std::string>(3);
						  try
						  {
							  int64_t MonetaryTransactionID;
							  const std::string monetaryIDStr =
							    (transType == "MONEY_IN") ? from : to;    // SQL from above guarantees this is either MONEY_IN or MONEY_OUT
							  const bool ok = yutils::strToInt2(monetaryIDStr, MonetaryTransactionID);
							  if (!ok)
								  throw std::runtime_error("Can't convert '" + monetaryIDStr + "' to integer");

							  sql << "DELETE FROM MONETARY WHERE MON_ID=?", soci::use(MonetaryTransactionID);

							  if (transType == "MONEY_IN")
							  {
								  sql << "UPDATE CREDIT_TRANSFERS SET FROM_IP='WEB' WHERE CT_ID=?", soci::use(ID);
							  }
							  else
							  {
								  sql << "UPDATE CREDIT_TRANSFERS SET TO_IP='WEB' WHERE CT_ID=?", soci::use(ID);
							  }

							  sql.get_backend()->commit_retain();
							  migratedTransactions++;
						  }
						  catch (const std::exception& e)
						  {
							  Log(Warning, "Failed to migrate transaction %ld: %s", ID, e.what());
							  sql.get_backend()->rollback_retain();
						  }
					  }
					  if (migratedTransactions)
						  Log(Info, "Successfully migrated %lu transactions of %s.", migratedTransactions, Inst.ID.c_str());
				  }
			  }

			  const std::string nanoPlayerHash = crypto::Hash(Inst.ID, EHashAlgorithm::MD5);
			  if (std::filesystem::exists(mPlayersDirectory / nanoPlayerHash))    // old location of player data was here
			  {
				  std::error_code ec;
				  std::filesystem::rename(mPlayersDirectory / nanoPlayerHash, mPlayersDirectory / Inst.ID, ec);
				  if (ec)
				  {
					  ec.clear();
					  std::filesystem::remove_all(mPlayersDirectory / nanoPlayerHash, ec);
				  }
			  }

			  auto player = std::make_shared<NanoPlayer>(this, Inst, mPlayersDirectory / Inst.ID);
			  player->CounterScale = mCounterScale;
			  mPlayerData->emplace(Inst.ID, player);
			  player->Load();

			  db->RegisterEvent(DBEvent_NewCmd + "_" + Inst.ID);
		  }    // end of for each instance

		  db->RecreateEventListener();
	  },
	  0, false, true, { ETransactionFlags::WaitTransaction });

	std::list<std::filesystem::path> toRemove;
	for (const auto& element : std::filesystem::directory_iterator(mPlayersDirectory))
	{
		if (element.is_directory() && !mPlayerData->contains(element.path().filename()))
			toRemove.push_back(element.path());
	}

	for (const std::filesystem::path& remove : toRemove) std::filesystem::remove_all(remove);

	std::shared_ptr<TAppParam> gameroundParam = pApp->GetExactParam(MachineName(), "GAMEROUND_ID", DomainBitflag::All(), false);
	if (gameroundParam)
	{
		int gameroundID = gameroundParam->AsInteger();
		pApp->DeleteParam(gameroundParam);
		pDBMngr->ExecuteSync([&](TGameDBSrv* db, soci::session& sql) { db->_DeleteParam(MachineName(), "GAMEROUND_ID"); });

		pApp->SetParam("GAMEROUND_ID", gameroundID, ParameterDomain::MINE, "Gameround ID", "The last gameround ID", true);
		Log(Info, "Successfully deleted old GAMEROUND_ID param with machine name ID and created new one with value %lu!", gameroundID);
	}
	mGameroundIDParam = pApp->GetOrSetParam("GAMEROUND_ID", 1, "Gameround ID", "The last gameround ID", PARAM_READ_MINE_WRITE_MINE);

	const uint64_t nextValidGid = mGameroundIDParam->AsInteger();
	mGameroundID = nextValidGid;
	Log(Info, "Got last gameround ID %ld. Will continue from here.", nextValidGid);

	ParallelFor(mPlayerData->begin(), mPlayerData->end(),
	            [nextValidGid](const std::pair<std::string, std::shared_ptr<NanoPlayer>>& p) { p.second->LoadHistory(nextValidGid); });

	if (mPlayerData->size())
		Log(Info, "Successfully loaded %lu players!", mPlayerData->size());
	else
		Log(Warning, "There are no players in the database!");

	playerLock.unlock();

	ScopedLock transactionLock(mTransactions);
	mTransactionID = 1;
	{
		rocksdb::ReadOptions opts;
		LogScope Scope;
		std::unique_ptr<rocksdb::Iterator> iter(TransactionDatabase->NewIterator(opts));
		uint64_t tid;
		for (iter->SeekToFirst(); iter->Valid(); iter->Next())
		{
			if (!yutils::strToInt2(iter->key().ToString(), tid))
			{
				Scope.Log(*this, Warning, "Could parse transaction ID '%s'", iter->value().ToString().c_str());
				continue;
			}

			mTransactionID = std::max(mTransactionID, tid + 1);
			json trJson;
			try
			{
				trJson = json::parse(iter->value().ToString());
			}
			catch (const std::exception& e)
			{
				Scope.Log(*this, Warning, "Could not parse transaction JSON id %lu: %s", tid, e.what());
				continue;
			}

			std::shared_ptr<NanoTransaction> tr;
			try
			{
				tr = NanoTransaction::FromJSON(trJson);
			}
			catch (const std::exception& e)
			{
				Scope.Log(*this, Warning, "Transaction JSON is invalid for id %lu: %s", tid, e.what());
				continue;
			}

			if (tr->GameroundID >= nextValidGid)    // if not valid, delete this transaction!
			{
				Scope.Log(*this, Warning, "Deleting transaction because it has an invalid game round ID: %lu", tr->GameroundID);
				TransactionDatabase->Delete(rocksdb::WriteOptions(), iter->key());
				continue;
			}

			auto placed = mTransactions->try_emplace(tid, tr);
			if (!placed.second)
				Scope.Log(*this, Critical, "Transaction id %lu already exists! This is VERY VERY WRONG AND SHOULD BE IMPOSSIBLE!!!", tid);
		}
	}
	Log(Info, "Loaded %lu transactions from database.", mTransactions->size());

	mPlayerData.Lock();

	auto gameround_dir = std::filesystem::directory_iterator(mGameroundsDirectory);
	for (const auto& element : gameround_dir)
	{
		if (element.is_regular_file() && element.path().extension() == GAMEROUND_FILE_EXTENSION)
		{
			const std::string roundStr = element.path().stem();
			int64_t GID;
			if (yutils::strToInt2(roundStr, GID))
			{
				std::ifstream infile(element.path());
				if (!infile.is_open())
				{
					Log(Warning, "Can't open active session file %s!", element.path().c_str());
					continue;
				}

				json SessionJSON;
				try
				{
					infile >> SessionJSON;
				}
				catch (const std::exception& e)
				{
					Log(Warning, "Session file %s could not be parsed to JSON: %s", element.path().c_str(), e.what());
					continue;
				}

				std::shared_ptr<Session> sessionPtr;
				try
				{
					sessionPtr = Session::FromJSON(SessionJSON, &mTransactions);
				}
				catch (...)
				{
				}
				if (!sessionPtr)
				{
					Log(Warning, "Session file %s could not be parsed from JSON!", element.path().c_str());
					continue;
				}

				if (!sessionPtr->Data.HasActiveGameround())
				{
					Log(Info, "Session file %s has no gameround in progress, archiving it!", element.path().c_str());
					SaveSession(*sessionPtr, true);
					continue;
				}

				auto playerIt = mPlayerData->find(sessionPtr->Data.ActiveGame->Player);
				if (playerIt != mPlayerData->end())
					sessionPtr->Player = playerIt->second;

				auto playerPtr = sessionPtr->Player.lock();
				if (!playerPtr)
				{
					Log(Info, "Session file %s has a non existing player(%s) attributed to the gameround in progress - archiving it!", element.path().c_str(),
					    sessionPtr->Data.ActiveGame->Player.c_str());
					SaveSession(*sessionPtr, true);
					continue;
				}

				if (CreateSession(*playerPtr, sessionPtr))
				{
					Log(Normal, "Re-created session of player %s", playerPtr->Username.c_str());

					{
						ScopedLock lock(mActiveGamerounds);
						(&mActiveGamerounds)[sessionPtr->Data.ActiveGame->ID] = sessionPtr->Data.ActiveGame;
					}

					playerPtr->TriggerEvent(sessionPtr->SID, "session-new", sessionPtr->SID, EPlayerEventPropagation::OnlyPlayerListeners);
					playerPtr->SetActive(true);
				}
			}
		}
	}

	auto token_dir = std::filesystem::directory_iterator(mNanoDataDirectory / "Tokens");
	std::set<std::filesystem::path> TokensToRemove;
	mTransferTokens.Lock();
	for (const auto& element : token_dir)
	{
		if (element.is_regular_file() && element.path().extension() == TOKEN_FILE_EXTENSION)
		{
			const std::string roundStr = element.path().stem();
			int64_t ID;
			if (!yutils::strToInt2(roundStr, ID))
			{
				Log(Warning, "Weird token file %s does not have a valid token ID in its name", element.path().c_str());
				continue;
			}

			std::ifstream infile(element.path());
			if (!infile.is_open())
			{
				Log(Warning, "Can't open token file %s!", element.path().c_str());
				continue;
			}

			json TokenJSON;
			try
			{
				infile >> TokenJSON;
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Token file %s could not be parsed to JSON: %s", element.path().c_str(), e.what());
				continue;
			}

			std::shared_ptr<FTransferToken> Token = std::make_shared<FTransferToken>(FTransferToken::FromJSON(TokenJSON));

			auto playerIt = mPlayerData->find(Token->UserPlayerID);
			if (playerIt == mPlayerData->end())
			{
				Log(Warning, "Token %lu target player %s does not exist anymore, deleting it!", Token->ID, Token->UserPlayerID.c_str());
				TokensToRemove.insert(element.path());
				continue;
			}

			if (Token->OwnerPlayerID != Token->UserPlayerID)
			{
				if (!playerIt->second->Sessions.contains(Token->Session))
				{
					std::shared_ptr<Session> new_session = std::make_shared<Session>(Token->Session, playerIt->second, false);
					if (!CreateSession(*playerIt->second, new_session))
						Log(Error, "Error re-creating session for transfer token %lu (player %s) - UID collision", Token->ID, Token->UserPlayerID.c_str());
				}

				if (!bCrossPlayerTransferTokensAllowed && !mPlayerData->contains(Token->OwnerPlayerID))
				{
					Log(Warning, "Token %lu owner player %s does not exist anymore, deleting it!", Token->ID, Token->OwnerPlayerID.c_str());
					TokensToRemove.insert(element.path());
					continue;
				}
			}

			if (!Token->CreatedTimestamp)
				Token->CreatedTimestamp = ytime::GetSystemTimeMsec();

			mTransferTokens->emplace(Token->ID, Token);
			Log(Info, "Loaded transfer token %lu for player %s->%s", Token->ID, Token->OwnerPlayerID.c_str(), Token->UserPlayerID.c_str());
		}
	}
	mTransferTokens.Unlock();
	for (const std::filesystem::path& token : TokensToRemove) std::filesystem::remove(token);

	mPlayerData.Unlock();

	{
		SharedScopedLock lock(mActiveGamerounds);
		if (mActiveGamerounds->size())
		{
			Log(Important, "Loaded %lu in progress gamerounds", mActiveGamerounds->size());
		}
	}

	const std::filesystem::path archive_dir = mGameroundsDirectory / "Archive";
	gameround_dir = std::filesystem::directory_iterator(archive_dir);

	toRemove.clear();
	ScopedLock archiveLock(mGameroundArchive);
	uint32_t deleted = 0;
	for (auto element : gameround_dir)
	{
		if (!element.is_regular_file())
			continue;

		if (element.path().extension() != GAMEROUND_FILE_EXTENSION)
		{
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		if (std::chrono::duration_cast<std::chrono::days>(std::chrono::file_clock::now() - element.last_write_time()).count() > (int)mDaysUntilArchiveDeletion)
		{
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		const std::string roundStr = element.path().stem();
		int64_t GID = yutils::strToInt(roundStr, 0);
		if (!GID)
		{
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		std::ifstream infile(element.path());
		if (!infile.is_open())
		{
			Log(Warning, "Can't open active archive gameround file %s!", element.path().c_str());
			continue;
		}

		json GameroundJSON;
		try
		{
			infile >> GameroundJSON;
		}
		catch (const std::exception& e)
		{
			Log(Warning, "Archived gameround file %s could not be parsed to JSON: %s", element.path().c_str(), e.what());
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		std::shared_ptr<Gameround> gameroundPtr;
		try
		{
			gameroundPtr = Gameround::FromJSON(GameroundJSON, &mTransactions);
		}
		catch (...)
		{
		}

		if (!gameroundPtr)
		{
			Log(Warning, "Archived gameround file %s could not be parsed from JSON - will delete it!", element.path().c_str());
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		if (gameroundPtr->Player.empty())
		{
			Log(Info, "Archived gameround file %s has no player attributed to the gameround in progress!", element.path().c_str());
			toRemove.push_back(element.path());
			deleted++;
			continue;
		}

		auto placed = mGameroundArchive->try_emplace(gameroundPtr->ID, gameroundPtr);
		if (!placed.second)
			Log(Warning, "Archived gameround ID %lu could not be loaded - another game with this ID exists!", gameroundPtr->ID);
	}

	for (const std::filesystem::path& remove : toRemove) std::filesystem::remove(remove);

	Log(Info, "Loaded %lu archived gamerounds (and %u were deleted because they were older than %u days)", mGameroundArchive->size(), deleted, mDaysUntilArchiveDeletion);
	archiveLock.unlock();
	transactionLock.unlock();

	mAutoCountersResetHourParam =
	  pApp->GetOrSetParam("AUTO_COUNTERS_RESET_HOUR", -1, "Auto counter reset hour", "Hour of the day for auto counters reset", PARAM_READ_GLOBAL_WRITE_GLOBAL);
	mAutoCountersResetHourParam->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			SetCounterAutoReset(param->AsInteger());
		}
	};
	SetCounterAutoReset(mAutoCountersResetHourParam->AsInteger());

	// Start the server accept loop, can fail if port bind fails
	try
	{
		Start("nanothread");
		Log(Critical, "Server started on port %d.", mPort);
	}
	catch (const websocketpp::exception& e)
	{
		Log(Error, "Server could not start on port %d: %s", mPort, e.what());
		return 1;
	}

	AddTimedTask<TAsyncTimedTaskHandler>(std::bind(&Timaxanano::CheckDeadPlayers, this), (uint32_t)mCheckDeadPlayerInterval, TTimedTaskHandler::ENDLESS,
	                                     "Dead player removal");

	return 0;
}

int Timaxanano::LoadEnvironmentVariables()
{
	int res = TApplication::LoadEnvironmentVariables();
	if (res)
		return res;

	if (!GetParamFromCache("Config"))
		LoadParamFromEnvironmentOptional("CONFIGURATION", "Config", "imaxanano.conf");

	SetClientID("NANO_0");

	LoadParamFromEnvironmentOptional("CONFIGURATION_USER", "UserConfig", {});
	return 0;
}

uint16_t Timaxanano::GetServicePort() const
{
	return Port();
}

void Timaxanano::SetCounterAutoReset(int hour)
{
	if (auto task = pDailyCounterResetTask.lock())
		task->Remove();
	pDailyCounterResetTask.reset();

	if (hour < 0 || hour > 23)
		return;

	std::tm lastTrigger;
	time_t timeNow = std::chrono::system_clock::to_time_t(std::chrono::system_clock::now());
	localtime_r(&timeNow, &lastTrigger);

	if (hour >= lastTrigger.tm_hour)
	{
		timeNow -= 24 * 60 * 60;
		localtime_r(&timeNow, &lastTrigger);
	}
	lastTrigger.tm_hour = hour;

	auto task = pApp->AddTimedTask<TtthLambda, ETimedTaskType::TASK_THREAD_SAFE>(std::bind(&Timaxanano::AutoCounterReset, this), 24 * 60 * 60 * 1000 /*1day*/,
	                                                                             TTimedTaskHandler::ENDLESS, "auto counter reset");
	task->Enable(std::chrono::system_clock::from_time_t(std::mktime(&lastTrigger)));
	pDailyCounterResetTask = task;
}

void Timaxanano::AutoCounterReset()
{
	const uint64_t now = ytime::GetSystemTimeMsec();
	const std::unordered_set<int> AllPeriodicCounters = GetAllPeriodicCounters();
	std::set<std::string> ResetPlayerNames;

	{
		SharedScopedLock lock(mPlayerData);
		for (const auto& player : &mPlayerData)
		{
			ScopedLock lock2(*player.second);
			player.second->ResetCounters(AllPeriodicCounters, 0, {}, "AutoReset");
			player.second->LastCounterReset = now;
			ResetPlayerNames.insert(player.second->Username);
		}
	}

	pDBMngr->ExecuteAsync(
	  [ResetPlayerNames, now](TGameDBSrv* db, soci::session& sql) {
		  for (const std::string& player : ResetPlayerNames)
			  db->_SetParam(player, "NANO", "LAST_COUNTER_RESET", std::to_string(now), "Last counter reset time", "The last time the attendant counters were reset",
			                ELanguage::English, true);
	  },
	  true, true, { ETransactionFlags::WaitTransaction });
}

CreditArray Timaxanano::EndGame(NanoPlayer& player, Gameround& activeRound, const std::string& inSession)
{
	json gameEndEvent(json::value_t::object);
	gameEndEvent["gid"] = activeRound.ID;
	CreditArray totalChange;
	CreditArray totalBet, totalWin;
	for (const std::pair<const uint64_t, std::shared_ptr<NanoTransaction>>& tr : activeRound.Transactions)
	{
		if (!tr.second)
			continue;

		totalChange += tr.second->TransferedAmountInContextOf(player.Username);
		if (tr.second->Type == ENanoTransactionType::Bet)
			totalBet += tr.second->Amount;
		else if (tr.second->Type == ENanoTransactionType::Win || tr.second->Type >= ENanoTransactionType::Jackpot)
			totalWin += tr.second->Amount;
	}
	gameEndEvent["total-change"] = CreditJSON(totalChange);
	gameEndEvent["total-bet"] = CreditJSON(totalBet);
	gameEndEvent["total-win"] = CreditJSON(totalWin);
	gameEndEvent["ended"] = activeRound.Ended;

	player.TriggerEvent(inSession, "game-end", gameEndEvent, EPlayerEventPropagation::OwnerSession);
	BroadcastPlayerEvent(player.Username, "game-end", gameEndEvent);

	return totalBet;
}

std::shared_ptr<NanoTransaction> Timaxanano::FindTransaction(uint64_t TRID) const
{
	if (!TRID)
		return {};

	SharedScopedLock lock(mTransactions);
	auto find = mTransactions->find(TRID);
	if (find == mTransactions->end())
		return {};

	return find->second;
}

NanoPlayer::NanoPlayer(Timaxanano* owner, const TGameDBSrv::Instance& instance, const std::filesystem::path& playerSaveDir) :
    Nano(owner), mInstance(instance), Username(instance.ID), Nickname(instance.PhysicalIP), PlayerHistoryDir(playerSaveDir)
{
	if (instance.HasFeature("KIOSK"))
	{
		mType = ENanoPlayerType::Kiosk;
	}
	else if (!Nickname.empty())
	{
		mType = ENanoPlayerType::Persistent;
	}
	else
	{
		mType = ENanoPlayerType::Temp;
	}
	LastCounterReset = 0;

	Jurisdiction = owner->mJurisdiction;

	CounterScale = owner->mCounterScale;
	DecimalChar = owner->mDecimalChar;
	ThousandChar = owner->mThousandChar;
	DecimalPlaces = owner->mDecimalPlaces;
	AlignLeft = owner->mAlignLeft;
	CurrencySymbol = owner->mCurrencySymbol;
}

const TGameDBSrv::Instance& NanoPlayer::Instance() const
{
	return mInstance;
}

void NanoPlayer::SetPassword(const std::string& pass)
{
	if (mInstance.SCPFolder.empty() != pass.empty())
	{
		TriggerEvent({}, "claimed", !pass.empty());
	}
	mInstance.SCPFolder = pass;
	UpdateDBInstance();
}

void NanoPlayer::SetOnline(bool bOnline)
{
	if (bOnline == bIsOnline)
		return;
	bIsOnline = bOnline;
	if (mInstance.Status != HANDPAY_STATUS)
		SetStatus(bIsOnline ? "ONLINE" : "OFFLINE");
}

void NanoPlayer::Load()
{
	Nano->pDBMngr->ExecuteSync(
	  [&](TGameDBSrv* db, soci::session& sql) {
		  soci::rowset playerSettings =
		    (sql.prepare << "SELECT ATTR_NAME, ATTR_STRING_VALUE FROM CONFIGURATION WHERE CLIENT_IP=? AND ATTR_NAME like 'USR_%'", soci::use(Username));

		  for (const soci::row& row : playerSettings)
		  {
			  const std::string name = MyUtils::ToLowercase(row.get<std::string>(0).substr(4));    // cut away the "USR_" part
			  const std::string value = row.get<std::string>(1, std::string());

			  if (name == "locale")
				  Locale = value;
			  else if (name == "currency")
				  Currency = value;
			  else
			  {
				  try
				  {
					  Config[name] = json::parse(value);
				  }
				  catch (const std::exception& e)
				  {
					  Config[name] = value;
				  }
			  }
		  }

		  if (Currency.empty())
			  Currency = Nano->mCurrency;

		  if (Locale.empty())
			  Locale = Nano->mLocale;

		  std::string SQL;
		  if (db->mDBType == TGameDBSrv::EDBType::firebird)
		  {
			  if (db->HasDigitalCountersV7())
			  {
				  SQL =
				    "SELECT COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTER_VALUE, DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', COUNTING_FROM) FROM DIGITAL_COUNTERS_V7 WHERE CLIENT_ID=? AND MOD(COUNTER_INDEX, 1000) < 900";
			  }
			  else
			  {
				  SQL =
				    "SELECT COUNTER_INDEX, COUNTER_VALUE, DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', COUNTING_FROM) FROM DIGITAL_COUNTERS WHERE CLIENT_IP=? AND MOD(COUNTER_INDEX, 1000) < 900";
			  }
		  }
		  else if (db->mDBType == TGameDBSrv::EDBType::postgresql)
		  {
			  SQL =
			    "SELECT COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTER_VALUE, EXTRACT(EPOCH FROM (COUNTING_FROM - '1970-01-01 00:00')) * 1000 FROM DIGITAL_COUNTERS_V7 WHERE CLIENT_ID=? AND MOD(COUNTER_INDEX, 1000) < 900";
		  }
		  else
			  throw std::runtime_error("Invalid database type specified");

		  if (db->HasDigitalCountersV7())
		  {
			  soci::rowset playerCounters = (sql.prepare << SQL, soci::use(Username));
			  for (const soci::row& row : playerCounters)
			  {
				  const int counterId = row.get<int>(0);

				  GameIdentifier game;
				  game.Game = row.get<int>(1);
				  game.Configuration = row.get<std::string>(2);

				  CounterInfo info;
				  info.Value = row.get<big_int>(3);
				  info.CountingFrom_Timestamp = row.get_indicator(4) == soci::i_null ? ytime::GetSystemTimeMsec() : (row.get_casted<big_int>(4) - UTC_OFFSET_SEC * 1000);

				  Counters.GetGameCounters(game)[counterId] = info;
			  }
		  }
		  else
		  {
			  soci::rowset playerCounters = (sql.prepare << SQL, soci::use(Username));
			  for (const soci::row& row : playerCounters)
			  {
				  GameIdentifier game;
				  int counterId = row.get<int>(0);
				  counterId = NanoCounters::unmapCounterId_Legacy(counterId, &game);

				  CounterInfo info;
				  info.Value = row.get<big_int>(1);
				  info.CountingFrom_Timestamp = row.get_indicator(2) == soci::i_null ? ytime::GetSystemTimeMsec() : (row.get_casted<big_int>(2) - UTC_OFFSET_SEC * 1000);

				  Counters.GetGameCounters(game)[counterId] = info;
			  }
		  }

		  LastCounterReset = db->_GetOrSetParam(Username, "LAST_COUNTER_RESET", std::to_string(ytime::GetSystemTimeMsec()), "Last counter reset time",
		                                        "The last time the attendant counters were reset", true, { ParameterDomain::MINE })
		                       .AsInteger();

		  Parameter userHandpay = db->_GetParamForIP(Username, USER_HANDPAY_PARAM, 11, { ParameterDomain::MINE });
		  if (userHandpay.IsValid())
			  PendingUserHandpay = std::make_pair(GameIdentifier(), static_cast<uint64_t>(userHandpay.AsInteger()));

		  soci::rowset systemHandpays = (sql.prepare << "SELECT ATTR_NAME, ATTR_STRING_VALUE FROM CONFIGURATION WHERE CLIENT_IP=? AND ATTR_NAME like '" +
		                                                  SYS_HANDPAY_PARAM_PREFIX + "%' ORDER BY ATTR_NAME ASC",
		                                 soci::use(Username));

		  for (const soci::row& row : systemHandpays)
		  {
			  const std::string name = row.get<std::string>(0).substr(SYS_HANDPAY_PARAM_PREFIX.size());    // cut away SYS_HANDPAY_PARAM_PREFIX
			  const std::string value = row.get<std::string>(1, std::string());
			  PendingHandpays.push_back(FPendingHandpay::FromString(value));
		  }

		  if (PendingUserHandpay || !PendingHandpays.empty())
			  SetStatus(HANDPAY_STATUS);
		  else
			  SetStatus("OFFLINE");
	  },
	  0, false, true, { ETransactionFlags::WaitTransaction });

	// Do not keep balance counters in cache!
	for (int credit_type = 0; credit_type < CREDIT_ALL; credit_type++)
	{
		Balance[credit_type] = Counters.GetCounter({}, NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]).Value;
		Counters.RemoveCounter(NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]);
	}

	if (Type() == ENanoPlayerType::Kiosk)
	{
		try
		{
			DisplayableGameroundID = GetConfig(KIOSK_RESET_CONFIG_NAME, 0L).get<uint64_t>();
		}
		catch (const std::exception& e)
		{
		}
	}

	auto param = Nano->pDBMngr->GetSpecificParam(Username, "DEPOSIT_OWNER", { ParameterDomain::OTHER });
	if (!param.IsNull())
		DepositOwner = param.AsString();
}

void NanoPlayer::CheckKioskExpired()
{
	if (Type() == ENanoPlayerType::Kiosk && Balance.IsZero())
	{
		// if player is in active game, don't reset history
		for (const auto& session : Sessions | std::views::values)
		{
			ScopedLock lock(session->Data);
			if (session->Data.ActiveGame)
				return;
		}

		for (const auto& session : Sessions | std::views::values)
		{
			ScopedLock lock(session->Data);
			session->Data.PlayedGameRounds.clear();
		}

		if (Nano->bManageKioskPassword)
			SetPassword({});

		DepositOwner.clear();
		DisplayableGameroundID = Nano->mGameroundID.load();
		SetConfig(KIOSK_RESET_CONFIG_NAME, DisplayableGameroundID);

		TriggerEvent({}, "kiosk-reset", DisplayableGameroundID);
	}
}

void NanoPlayer::AddToHistory(uint64_t gid, const json& game)
{
	ScopedLock lock(GameroundHistory);
	AddToHistory_AssumesLocked(gid, game);
}

void NanoPlayer::AddToHistory_AssumesLocked(uint64_t gid, const json& game)
{
	if (GameroundHistory->size() >= PLAYER_HISTORY_LIMIT)
	{
		auto erased = GameroundHistory->extract(GameroundHistory->begin());
		if (UnsavedGameroundID && erased.key() == UnsavedGameroundID)
			UnsavedGameroundID = GameroundHistory->begin()->first;
	}

	GameroundHistory->insert_or_assign(GameroundHistory->end(), gid, game);
	if (!UnsavedGameroundID)
		UnsavedGameroundID = gid;
}

void Timaxanano::send_with_status(const imaxa_connection_ptr& con, web::http::status::value code, const std::string& msg)
{
	std::error_code ec;
	if (msg.empty())
		con->set_status(code, ec);
	else
		con->set_status(code, msg, ec);
	ec.clear();
	con->send_http_response(ec);
}

void Timaxanano::OnDBEvent(TGameDBSrv* db, const std::string& event, size_t num)
{
	if ((event.length() > 7) && (event.substr(0, DBEvent_NewCmd.length()) == DBEvent_NewCmd))
	{
		std::list<DBCommand> commands;
		db->GetCommands(ClientID(), mLastCmdID, commands, "%");
		if (commands.empty())
			return;

		const int64_t latestCommandID = commands.back().ID;
		if (latestCommandID > mLastCmdID)
		{
			mLastCmdID = latestCommandID;
		}
		else
		{
			rtfwk_sdl2::pApp->WriteLog(LT_WARNING, NO_GAME_ID, "Next command ID returned is not newer than the previous one! (%ld vs %ld)", mLastCmdID, latestCommandID);
		}

		for (auto it = commands.begin(); it != commands.end();)
		{
			if (bPrintCommands)
				rtfwk_sdl2::pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "New command in DB: %s", it->print().c_str());

			try
			{
				if (OnDBCommand_DBThread(db, *it, {}))
				{
					it = commands.erase(it);    // handled!
					continue;
				}
			}
			catch (...)
			{
				rtfwk_sdl2::pApp->WriteLog(LT_WARNING, NO_GAME_ID, "Something went wrong executing command %s!", it->print().c_str());
			}

			it++;
		}

		if (!commands.empty())
		{
			rtfwk_sdl2::pApp->Defer(
			  [this, commands]() {
				  for (const DBCommand& command : commands)
				  {
					  try
					  {
						  OnDBCommand(command, {});
					  }
					  catch (...)
					  {
						  rtfwk_sdl2::pApp->WriteLog(LT_WARNING, NO_GAME_ID, "Something went wrong executing command %s (deferred)!", command.print().c_str());
					  }
				  }
			  },
			  "NanoDBCommand");
		}
	}
	else
	{
		Log(Warning, "Unknown event received from DB (%s)!", event.c_str());
	}
}

bool Timaxanano::OnDBCommand(const DBCommand& command, const std::string& callerID)
{
	if (TDBInterface::OnDBCommand(command, callerID))
		return true;

	if (command.Command == "REFRESH_PARAMETER")
	{
		auto param = GetExactParamFromCache(command.To, command.GetString1Param(), { ParameterDomain::MINE });
		if (param)
			param->Update();
		return true;
	}
	else if (command.Command == "NEW_PARAMETER")
	{
		GetExactParam(command.To, command.GetString1Param());
		return true;
	}

	return false;
}

void Timaxanano::Broadcast(const json& msg, bool bOnlyForAdmins)
{
	json event = msg;
	event["time"] = ytime::GetSystemTimeMsec();

	const std::string str = event.dump();
	SharedScopedLock lock(mGeneralListeners);
	for (const auto& client : &mGeneralListeners)
		if (!bOnlyForAdmins || client.second.bAdmin)
			if (auto con = client.first.lock())
				con->send(str, websocketpp::frame::opcode::TEXT);
}

NanoTransaction::NanoTransaction(uint64_t id, ENanoTransactionType type, const std::set<uint64_t>& ctIDs, const std::string& from, const std::string& to,
                                 const credit::CreditArray& amount, uint64_t gameroundID, const GameIdentifier& game, uint64_t timestamp, const std::string& desc)
{
	CTIDs = ctIDs;
	Amount = amount;
	TransactionID = id;

	From = from;
	To = to;
	Type = type;
	GameroundID = gameroundID;
	GameID = game;
	Timestamp = timestamp;
	Description = desc;
}

credit::CreditArray NanoTransaction::TransferedAmountInContextOf(const std::string& client) const
{
	if (To == From)
		return CreditArray();

	if (To == client)
		return Amount;

	if (From == client)
		return -Amount;

	return CreditArray();
}

ENanoTransactionType NanoTransaction::TransactionTypeForUser(const std::string& client) const
{
	switch (Type)
	{
		case ENanoTransactionType::Deposit: return client == From ? ENanoTransactionType::Withdraw : ENanoTransactionType::Deposit;
		case ENanoTransactionType::Withdraw: return client == To ? ENanoTransactionType::Deposit : ENanoTransactionType::Withdraw;
		default: return Type;
	}
}

json NanoTransaction::ToJSON() const
{
	json trObj(json::value_t::object);
	trObj["trid"] = TransactionID;
	trObj["amount"] = CreditJSON(Amount);
	trObj["type"] = Type._to_string();
	trObj["from"] = From;
	trObj["to"] = To;
	trObj["gid"] = GameroundID;
	trObj["game_id"] = std::string(GameID);
	json ctids(json::value_t::array);
	for (const uint64_t& ctid : CTIDs) ctids.push_back(ctid);
	trObj["ctIDs"] = std::move(ctids);
	trObj["desc"] = Description;
	trObj["time"] = Timestamp;
	return trObj;
}

json NanoTransaction::ToShortJSON() const
{
	json transactionObj(json::value_t::object);
	transactionObj["type"] = Type._to_string();
	transactionObj["from"] = From;
	transactionObj["to"] = To;
	transactionObj["amount"] = CreditJSON(Amount);
	transactionObj["trid"] = TransactionID;
	transactionObj["gid"] = GameroundID;
	transactionObj["game_id"] = std::string(GameID);
	transactionObj["time"] = Timestamp;
	return transactionObj;
}

GameIdentifier GetGameFromTransaction(const json& val)
{
	const json* gameVal = FindMember(val, "game_id");
	if (gameVal)
	{
		if (gameVal->is_number_unsigned())
			return { gameVal->get<GameID>() };

		if (gameVal->is_string())
			return gameVal->get<std::string>();
	}

	return {};
}

std::shared_ptr<NanoTransaction> NanoTransaction::FromJSON(const json& val)
{
	std::optional<ENanoTransactionType> type;
	if (auto opt = ENanoTransactionType::_from_string_nothrow(val["type"].get<std::string>().c_str()))
	{
		type = *opt;
	}
	else if (val["type"].get<std::string>() == "Bonus")
	{
		type = ENanoTransactionType::Jackpot;
	}
	else
	{
		throw std::runtime_error("Unknown transaction type: " + val["type"].get<std::string>());
	}

	std::set<uint64_t> ctids;
	for (const json& v : val["ctIDs"]) { ctids.insert(v.get<uint64_t>()); }
	std::shared_ptr<NanoTransaction> ret = std::make_shared<NanoTransaction>(val["trid"].get<uint64_t>(), *type, ctids, val["from"].get<std::string>(),
	                                                                         val["to"].get<std::string>(), CreditFromJSON(val["amount"]), val["gid"].get<uint64_t>(),
	                                                                         GetGameFromTransaction(val), val["time"].get<uint64_t>(), val["desc"].get<std::string>());
	return ret;
}

bool NanoTransaction::operator<(const NanoTransaction& rhs) const
{
	return TransactionID < rhs.TransactionID;
}

Gameround::Gameround(uint64_t gid, const std::string& player, const std::string& session, uint64_t started) : ID(gid), Player(player), Session(session), Started(started)
{
}

json Gameround::ToJSON() const
{
	ScopedLock lock(this);
	json gameroundObj(json::value_t::object);
	gameroundObj["gid"] = ID;
	json transactionArray(json::value_t::array);

	gameroundObj["player"] = Player;
	gameroundObj["session"] = Session;
	CreditArray change;
	for (const std::pair<const uint64_t, std::shared_ptr<NanoTransaction>>& tr : Transactions)
	{
		transactionArray.push_back(tr.first);

		if (!tr.second)
			continue;

		change += tr.second->TransferedAmountInContextOf(Player);
	}
	gameroundObj["total-change"] = CreditJSON(change);

	gameroundObj["transactions"] = transactionArray;
	gameroundObj["started"] = Started;
	gameroundObj["ended"] = Ended;
	gameroundObj["void"] = bVoid;
	return gameroundObj;
}

std::shared_ptr<Gameround> Gameround::FromJSON(const json& val, const std::map<uint64_t, std::shared_ptr<NanoTransaction>>& transactionRegistry)
{
	std::shared_ptr<Gameround> ret =
	  std::make_shared<Gameround>(val["gid"].get<uint64_t>(), val["player"].get<std::string>(), val["session"].get<std::string>(), val["started"].get<uint64_t>());
	ret->Ended = val["ended"].get<uint64_t>();
	ret->bVoid = val["void"].get<bool>();
	for (const json& tr : val["transactions"])
	{
		const uint64_t id = tr.get<uint64_t>();
		if (!id)
			continue;
		auto found = transactionRegistry.find(id);
		ret->Transactions[id] = (found == transactionRegistry.end()) ? NULL : found->second;
	}
	return ret;
}

void Timaxanano::BroadcastEvent(const std::string& event, const json& ctx, bool bAdmin)
{
	json evObj(json::value_t::object);
	evObj["event"] = event;
	evObj["context"] = ctx;
	Broadcast(evObj, bAdmin);
}

void Timaxanano::BroadcastPlayerEvent(const std::string& username, const std::string& event, const json& ctx, bool bAdmin)
{
	json playerEventObj(json::value_t::object);
	playerEventObj["player"] = username;
	playerEventObj["event"] = event;
	playerEventObj["context"] = ctx;
	Broadcast(playerEventObj, bAdmin);
}

std::shared_ptr<NanoTransaction> Timaxanano::NanoCreditTransfer(ENanoTransactionType type, const std::string& from, const std::string& to, const CreditArray& amount,
                                                                const std::shared_ptr<Gameround>& Gameround, const GameIdentifier& game, const std::string& description,
                                                                bool bDeductFromSourceBalance, bool bIncrementTargetBalance)
{
	if (from == to || !amount.IsStrictlyPositive(false))
		return NULL;

	std::shared_ptr<NanoTransaction> toRollback;
	ScopedLock<std::shared_mutex> rollbackLock;
	CreditArray useAmount = amount;
	if (type == ENanoTransactionType::Rollback)
	{
		useAmount.Clear();    // gets filled later from data that's in the database
		if (uint64_t id = yutils::strToInt(description, 0L))
		{
			toRollback = FindTransaction(id);
			if (!toRollback)
				return {};

			rollbackLock = ScopedLock(toRollback->bRolledBack);
			if (&toRollback->bRolledBack)
				return {};
		}
		else
		{
			return NULL;
		}
	}

	const uint64_t timestamp = ytime::GetSystemTimeMsec();

	// const auto preStartTask = std::chrono::steady_clock::now();
	// std::chrono::time_point<std::chrono::steady_clock> startTask, endTask;

	std::shared_ptr<NanoTransaction> result = pDBMngrTransactions->ExecuteSync<std::shared_ptr<NanoTransaction>>([&](TGameDBSrv* db, soci::session& sql) {
		const uint64_t gid = Gameround ? Gameround->ID : 0;

		// endTask = startTask = std::chrono::steady_clock::now();
		//  ===== ROLLBACK STUFF =====
		if (toRollback)
		{
			for (const uint64_t& id : toRollback->CTIDs)
			{
				const std::string rollbackID = "RB_" + std::to_string(id);
				big_int ctid = 0;
				if (db->HasCreditTransfersV7())
					sql << "SELECT CT_ID FROM CREDIT_TRANSFERS_V7 WHERE CLIENT_ID=?", soci::use(rollbackID), soci::into(ctid);
				else
					sql << "SELECT CT_ID FROM CREDIT_TRANSFERS WHERE CLIENT_IP=?", soci::use(rollbackID), soci::into(ctid);

				if (ctid)    // if a rollbacked transaction already exists, go to the next one
					continue;

				big_int rb_amount;
				int credit_type_counter_id;
				if (db->HasCreditTransfersV7())
				{
					sql << "SELECT CREDIT_VALUE, CREDIT_TYPE FROM CREDIT_TRANSFERS_V7 WHERE CT_ID=? AND GAMEROUND_ID=? AND FROM_CLIENT_ID=? AND TO_CLIENT_ID=?",
					  soci::use(id), soci::use(gid), soci::use(to), soci::use(from), soci::into(rb_amount), soci::into(credit_type_counter_id);
				}
				else
				{
					sql << "SELECT CREDIT_VALUE, CREDIT_TYPE FROM CREDIT_TRANSFERS WHERE CT_ID=? AND GAME_ID=? AND FROM_IP=? AND TO_IP=?", soci::use(id), soci::use(gid),
					  soci::use(to), soci::use(from), soci::into(rb_amount), soci::into(credit_type_counter_id);
				}

				if (!sql.got_data())    // can't find the transaction to roll back!
					continue;

				useAmount[NanoCounters::CreditTypeFromCounterID(credit_type_counter_id)] += rb_amount;
			}
		}
		// ===== END OF ROLLBACK STUFF =====


		GameIdentifier total;    // default: Game = 0 and Configuration is empty
		std::set<uint64_t> CTIDs;
		for (int credit_type = 0; credit_type < CreditType::CREDIT_ALL; credit_type++)
		{
			if (!useAmount[credit_type])    // skip if no amount for this credit type
				continue;

			if (bDeductFromSourceBalance && from != MachineName())
			{
				int res = RET_GENERAL_ERROR;
				if (db->HasDigitalCountersV7())
				{
					sql << "select RESULT from DEC_DIGITAL_CNT_V7(?,?,?,?,?,0)", soci::use(from), soci::use<unsigned short>(total.Game), soci::use(total.Configuration),
					  soci::use(NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]), soci::use(useAmount[credit_type]), soci::into(res);
				}
				else
				{
					sql << "select RESULT from DEC_DIGITAL_CNT(?, ?, ?, 0)", soci::use(from), soci::use(NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]),
					  soci::use(useAmount[credit_type]), soci::into(res);
				}

				if (!sql.got_data())
					throw RollbackTransaction("could not decrement counter");

				if (res != 0)
					throw RollbackTransaction("not enough credits");
			}

			big_int CTID = 0;
			if (db->HasDigitalCountersV7())
			{
				sql << "SELECT GEN_ID( GEN_CREDIT_TRANSFERS_V7, 1 ) FROM RDB$DATABASE", soci::into(CTID);
				if (!sql.got_data())
					throw RollbackTransaction("error fetching generator id of a credit transfer");
			}
			else
			{
				sql << "SELECT GEN_ID( GEN_CREDIT_TRANSFERS, 1 ) FROM RDB$DATABASE", soci::into(CTID);
				if (!sql.got_data())
					throw RollbackTransaction("error fetching generator id of a credit transfer");
			}

			const std::string transferType(type._to_string());

			if (db->HasCreditTransfersV7())
			{
				sql
				  << "insert into CREDIT_TRANSFERS_V7(CT_ID, GAMEROUND_ID, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, CLIENT_ID, FROM_CLIENT_ID, TO_CLIENT_ID, CREDIT_VALUE, CREDIT_TYPE, TIME_STAMP) values(?, ?, ?, ?, ?, ?, ?, ?, ?, GETEXACTTIMESTAMPUTC())",
				  soci::use(CTID), soci::use(gid), soci::use<unsigned short>(game.Game), soci::use(game.Configuration), soci::use(transferType), soci::use(from),
				  soci::use(to), soci::use(useAmount[credit_type]), soci::use(NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]);
			}
			else
			{
				const int isCashable(credit_type == CreditType::CREDIT_CASHABLE ? 1 : 0);
				sql
				  << "insert into CREDIT_TRANSFERS(CT_ID, GAME_ID, CLIENT_IP, FROM_IP, TO_IP, CREDIT_VALUE, IS_CACHABLE, CREDIT_TYPE, TIME_STAMP) values(?, ?, ?, ?, ?, ?, ?, ?, GETEXACTTIMESTAMPUTC())",
				  soci::use(CTID), soci::use(gid), soci::use(transferType), soci::use(from), soci::use(to), soci::use(useAmount[credit_type]), soci::use(isCashable),
				  soci::use(NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type]);
			}

			if (bIncrementTargetBalance && to != MachineName())
			{
				db->IncCounter(to, total, NanoCounters::CREDIT_TYPE_COUNTER_IDS[credit_type], useAmount[credit_type]);
			}

			CTIDs.insert(CTID);
		}

		auto trans = std::make_shared<NanoTransaction>(mTransactionID, type, CTIDs, from, to, useAmount, gid, game, timestamp, description);

		mTransactionID++;

		// endTask = std::chrono::steady_clock::now();

		return trans;
	});

	// const auto end = std::chrono::steady_clock::now();

	/*Log(Debug, "Transaction times: %.02fms | %.02fms | %.02fms", std::chrono::duration_cast<std::chrono::microseconds>(startTask - preStartTask).count() * 1e-3f,
	    std::chrono::duration_cast<std::chrono::microseconds>(endTask - startTask).count() * 1e-3f,
	    std::chrono::duration_cast<std::chrono::microseconds>(end - endTask).count() * 1e-3f);*/

	if (type == ENanoTransactionType::Rollback)
	{
		if (result)
			&toRollback->bRolledBack = true;
		rollbackLock.unlock_checked();
	}
	else if (type == ENanoTransactionType::Deposit && mIsInRamClear)
	{
		bool bRamClearHistoryInsert = pDBMngr->ExecuteSync<bool>(
		  [&](TGameDBSrv* db, soci::session& sql) -> bool {
			  sql << "insert into RAM_CLEAR_HISTORY(COUNTING_FROM_TIMESTAMP, DENOMINATION, CURRENCY, COUNTER_SCALE) values(CURRENT_TIMESTAMP,?,?,?)",
			    soci::use(mDenomination), soci::use(mCurrency), soci::use(mCounterScale);

			  db->_SetParam("GLOBAL", "NANO", "RAM_CLEAR_DATE", std::to_string(time(NULL)), {}, {}, ELanguage::English, true);

			  mIsInRamClear = false;
			  return true;
		  },
		  false);

		if (!bRamClearHistoryInsert)
			Log(Warning, "Could not write into RAM_CLEAR_HISTORY database table on first deposit!");
	}

	// add this transaction to the cache
	if (result)
	{
		DoAsyncTask(
		  [this, result]() {
			  rocksdb::WriteOptions opt;
			  opt.sync = ShouldSyncDiskOperations();
			  TransactionDatabase->Put(opt, std::to_string(result->TransactionID), result->ToJSON().dump());
		  },
		  "transactions");

		{
			ScopedLock lock(mTransactions);
			auto placed = mTransactions->try_emplace(result->TransactionID, result);
			if (!placed.second)
				Log(Critical, "Could not save transaction ID %lu to cache because it seems one already exists with this ID!", result->TransactionID);
		}

		if (Gameround)
		{
			ScopedLock lock(*Gameround);
			Gameround->Transactions[result->TransactionID] = result;
		}
	}

	return result;
}

void Timaxanano::VoidGame(NanoPlayer& player, Session& session)
{
	if (!session.Data.HasActiveGameround())
		return;

	ScopedLock lock(*session.Data.ActiveGame);

	const std::map<uint64_t, std::shared_ptr<NanoTransaction>> toRollback = session.Data.ActiveGame->Transactions;
	for (const std::pair<const uint64_t, std::shared_ptr<NanoTransaction>>& tr : toRollback)
	{
		if (!tr.second)
		{
			Log(Critical, "Recovery: transaction '%lu' could not be rolled back because it couldn't be found - it was likely not written to the database!", tr.first);
			continue;
		}

		if (tr.second->Type == ENanoTransactionType::Deposit || tr.second->Type == ENanoTransactionType::Withdraw || tr.second->Type == ENanoTransactionType::Rollback)
			continue;

		std::shared_ptr<NanoTransaction> rollbackedTr = NanoCreditTransfer(ENanoTransactionType::Rollback, tr.second->To, tr.second->From, tr.second->Amount,
		                                                                   session.Data.ActiveGame, tr.second->GameID, std::to_string(tr.second->TransactionID));

		if (rollbackedTr)
		{
			if (rollbackedTr->Amount != tr.second->Amount)
			{
				Log(Warning, "Recovery: credit check failed for transaction %lu (%ld of %ld recovered)!", tr.second->TransactionID, rollbackedTr->Amount.Total(),
				    tr.second->Amount.Total());
				continue;
			}

			player.OnTransaction(*rollbackedTr, session.SID, {});
		}
	}

	const uint64_t endedGID = session.Data.ActiveGame->ID;
	EndGame(player, *session.Data.ActiveGame, session.SID);

	SaveSession(session, true);

	{
		ScopedLock lock2(mActiveGamerounds);
		mActiveGamerounds->erase(endedGID);
	}

	{
		ScopedLock lock2(mGameroundArchive);
		(&mGameroundArchive)[endedGID] = session.Data.ActiveGame;
	}

	lock.unlock();

	session.Data.ActiveGame.reset();
	session.Data.PlayedGameRounds.insert(session.Data.PlayedGameRounds.end(), endedGID);
	player.AddToHistory(endedGID, json());
	player.SaveHistory();
	player.SetActive();
}

std::shared_ptr<NanoPlayer> Timaxanano::CreatePlayer(const nano::http_requests::PlayerCreateInfo& info)
{
	int freeNumber = 1;
	std::bitset<0x8000> bAvailableNumbers;
	bAvailableNumbers.set();
	bAvailableNumbers.reset(0);

	{
		ScopedLock lock(mPlayerData);
		for (const auto& player : &mPlayerData)
		{
			if (player.second)
				bAvailableNumbers.reset(player.second->Instance().Number);
			else
				bAvailableNumbers.reset(std::stoi(player.first));
		}
		freeNumber = (int)bAvailableNumbers._Find_first();
		mPlayerData->emplace(std::to_string(freeNumber), std::shared_ptr<NanoPlayer>());
	}


	std::string features = "YPLAYER;";
	std::string nickname = info.Nickname;
	if (info.Type == ENanoPlayerType::Kiosk)
		features += "KIOSK;";
	else if (info.Type == ENanoPlayerType::Temp)
		nickname.clear();

	const std::string passHash = info.PassHash.empty() ? std::string() : crypto::Hash(SALT_KEY + info.PassHash, EHashAlgorithm::SHA256);

	TGameDBSrv::Instance inst;
	const bool bSuccess = pDBMngr->ExecuteSync<bool>(
	  [&](TGameDBSrv* db, soci::session& sql) -> bool {
		  if (info.Username.empty())
		  {
			  const std::string username = PLAYER_ID + "_" + std::to_string(freeNumber);
			  db->ClearInstanceData(username, false);
			  if (db->RegisterInstance(PLAYER_ID, HashKey(username.c_str(), username.length(), true), nickname.substr(0, 15), freeNumber, 22, passHash, info.Data1,
			                           info.Data2, features, freeNumber, inst))
				  return false;
		  }
		  else
		  {
			  if (info.Username == MachineName())
				  return false;    // not allowed to have same username as machine name!

			  const std::string mac = HashKey(info.Username.c_str(), info.Username.length(), true);

			  db->GetInstance(info.Username, mac, inst);
			  if (inst.bValid)
				  return false;    // username already taken
			  db->ClearInstanceData(info.Username, true);

			  inst.ID = info.Username;
			  inst.LastUpdateTimestamp = ytime::GetSystemTimeMsec();
			  inst.MAC = mac;
			  inst.Number = freeNumber;
			  inst.PhysicalIP = nickname.substr(0, 15);
			  inst.Port = freeNumber;
			  inst.SCPFolder = passHash;
			  inst.SSHPort = 22;
			  inst.Status = "OFFLINE";
			  inst.Type = PLAYER_ID;
			  inst.VideoURL = info.Data1;
			  inst.AudioURL = info.Data2;
			  inst.Features = { "YPLAYER" };
			  if (info.Type == ENanoPlayerType::Kiosk)
				  inst.Features.emplace_back("KIOSK");

			  sql
			    << "INSERT INTO INSTANCES (INSTANCE_TYPE, INSTANCE_TYPE_NUMBER, INSTANCE_STATUS, INSTANCE_MAC_ADDRESS, INSTANCE_LISTEN_PORT, INSTANCE_PHYSICAL_IP, INSTANCE_SSH_PORT, "
			       "INSTANCE_SCP_FOLDER, INSTANCE_VIDEO_URL, INSTANCE_AUDIO_URL, INSTANCE_ID, INSTANCE_SUPPORTED_FEATURES, INSTANCE_LAST_UPDATE_TIMESTAMP)"
			       " VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, GETEXACTTIMESTAMPUTC())",
			    soci::use(PLAYER_ID), soci::use(inst.Number), soci::use(inst.Status), soci::use(inst.MAC), soci::use(inst.Port), soci::use(inst.PhysicalIP),
			    soci::use(inst.SSHPort), soci::use(inst.SCPFolder), soci::use(inst.VideoURL), soci::use(inst.AudioURL), soci::use(inst.ID), soci::use(features);

			  if (!info.Jurisdiction.empty())
				  db->_SetParam(inst.ID, inst.ID, "JURISDICTION", info.Jurisdiction, "Player jurisdiction", "The jurisdiction this players accounting is done in",
				                ELanguage::English, true);
		  }

		  // if we get to here, we succeeded in adding a player!
		  db->RegisterEvent(DBEvent_NewCmd + "_" + inst.ID);
		  db->RecreateEventListener();

		  return true;
	  },
	  false, 0, false, true, { ETransactionFlags::WaitTransaction });

	if (!bSuccess)
	{
		ScopedLock lock(mPlayerData);
		mPlayerData->erase(std::to_string(freeNumber));
		return NULL;
	}

	Log(Important, "Created new player PLAYER_%d!", freeNumber);

	std::shared_ptr<NanoPlayer> player = std::make_shared<NanoPlayer>(this, inst, mPlayersDirectory / crypto::Hash(inst.ID, EHashAlgorithm::MD5));
	player->Lock();
	player->Jurisdiction = info.Jurisdiction.empty() ? mJurisdiction : info.Jurisdiction;
	player->Locale = mLocale;
	player->Currency = mCurrency;
	player->CounterScale = mCounterScale;

	{
		ScopedLock lock(mPlayerData);
		mPlayerData->erase(std::to_string(freeNumber));
		mPlayerData->emplace(player->Username, player);
	}

	// delete possible old directories
	if (std::filesystem::exists(player->PlayerHistoryDir))
	{
		std::filesystem::remove_all(player->PlayerHistoryDir);
		Log(Info, "Removed player %s directory!", player->Username.c_str());
	}

	if (!std::filesystem::create_directories(player->PlayerHistoryDir))
	{
		Log(Warning, "Failed to create save directory for player %s (%s)!", player->Username.c_str(), player->PlayerHistoryDir.c_str());
	}

	player->Load();

	return player;
}

Session::Session(const std::string& sid, const std::shared_ptr<NanoPlayer>& playerPtr, bool bTimeoutable) : SID(sid), bCanTimeout(bTimeoutable), Player(playerPtr) {}

void Session::TriggerEvent(const std::string& eventName, const json& context) const
{
	ScopedLock lock(Data);

	auto con = Data.Stream.lock();
	if (!con)
		return;

	auto playerPtr = Player.lock();
	if (!playerPtr)
		return;

	json obj(json::value_t::object);
	obj["event"] = eventName;
	obj["context"] = context;

	const std::string payload = obj.dump();
	std::error_code ec = con->send(payload, websocketpp::frame::opcode::TEXT);
	if (ec)
		TLOG(LogNano, Warning, "Error sending session event %s: %s", eventName.c_str(), ec.message().c_str())
}

json Session::ToJSON() const
{
	json gameroundStateJson(json::value_t::object);
	gameroundStateJson["player"] = Player.expired() ? json() : Player.lock()->Username;
	gameroundStateJson["demo"] = false;
	gameroundStateJson["timeout"] = bCanTimeout;
	gameroundStateJson["session"] = SID;
	gameroundStateJson["gameround"] = Data.HasActiveGameround() ? Data.ActiveGame->ToJSON() : json();
	gameroundStateJson["last-ip"] = Data.LastIP;
	gameroundStateJson["last-agent"] = Data.LastAgent;
	gameroundStateJson["last-active"] = Data.LastActive;
	gameroundStateJson["gameid"] = std::string(Data.Game);
	return gameroundStateJson;
}

json Session::GetHistory(NanoPlayer& player) const
{
	json list(json::value_t::array);

	if (Data.PlayedGameRounds.empty())
		return list;

	SharedScopedLock lock(player.GameroundHistory);
	for (auto it = player.GameroundHistory->find(*Data.PlayedGameRounds.begin()); it != player.GameroundHistory->end(); it++) list.push_back(it->second);

	return list;
}

std::shared_ptr<Session> Session::FromJSON(const json& val, const std::map<uint64_t, std::shared_ptr<NanoTransaction>>& transactionRegistry)
{
	std::shared_ptr<Session> ret =
	  std::make_shared<Session>(val["session"].get<std::string>(), std::shared_ptr<NanoPlayer>(), !val.contains("timeout") || val["timeout"].get<bool>());
	if (val["gameround"].is_object())
		ret->Data.ActiveGame = Gameround::FromJSON(val["gameround"], transactionRegistry);
	ret->Data.LastIP = val["last-ip"].get<std::string>();
	ret->Data.LastActive = val["last-active"].get<uint64_t>();
	ret->Data.LastAgent = val["last-agent"].get<std::string>();
	return ret;
}

json FTransferToken::ToJSON(bool bWithoutSession) const
{
	json obj(json::value_t::object);
	obj["player"] = UserPlayerID;
	obj["owner"] = OwnerPlayerID;
	if (!bWithoutSession)
	{
		obj["token"] = ID;
		obj["session"] = Session;
	}
	obj["created"] = CreatedTimestamp;
	obj["starting-credits"] = CreditJSON(StartingCredits);
	return obj;
}

FTransferToken FTransferToken::FromJSON(const json& val)
{
	FTransferToken Token;
	Token.ID = val["token"].get<uint64_t>();
	Token.UserPlayerID = val["player"].get<std::string>();
	Token.OwnerPlayerID = val["owner"].get<std::string>();
	Token.Session = val["session"].get<std::string>();
	if (val.contains("created"))    // older Nanos did not have this
		Token.CreatedTimestamp = val["created"].get<uint64_t>();
	if (val.contains("starting-credits"))
		Token.StartingCredits = CreditFromJSON(val["starting-credits"]);
	return Token;
}

std::string FPendingHandpay::ToString() const
{
	return yutils::Format("%lu;%lu;%s;%lu;%s", Time, TriggeredByTRID, Source._to_string(), Amount, std::string(RequestedFromGame));
}

FPendingHandpay FPendingHandpay::FromString(const std::string& str)
{
	FPendingHandpay ret;
	auto parts = yutils::Split(str, ";", true);
	if (parts.size() < 4)
		return ret;

	ret.Time = yutils::strToInt(parts[0], 0);
	ret.TriggeredByTRID = yutils::strToInt(parts[1], 0);
	ret.Source = EHandpayTriggerSource::_from_string(parts[2].c_str());
	ret.Amount = yutils::strToInt(parts[3], 0);
	if (parts.size() > 4)
		ret.RequestedFromGame = parts[4];
	return ret;
}

json FPendingHandpay::ToJson(const std::shared_ptr<NanoTransaction>& instigator) const
{
	json PendingHandpay(json::value_t::object);
	PendingHandpay["time"] = Time;
	PendingHandpay["src"] = Source;
	PendingHandpay["amount"] = Amount;
	if (instigator)
		PendingHandpay["transaction"] = instigator->ToShortJSON();

	return PendingHandpay;
}

bool SessionData::HasActiveGameround() const
{
	return ActiveGame && ActiveGame->ID;
}

uint64_t SessionData::GameroundID() const
{
	return ActiveGame ? ActiveGame->ID : 0;
}

void NanoPlayer::LoadHistory(int64_t lastvalid_gid)
{
	ScopedLock lock(GameroundHistory);
	GameroundHistory->clear();
	UnsavedGameroundID = 0;

	std::list<std::filesystem::path> toRemove;

	const std::filesystem::path GameroundDir = PlayerHistoryDir / "gamerounds";
	if (!std::filesystem::exists(GameroundDir))
	{
		if (std::filesystem::create_directories(GameroundDir))
		{
			TLOG(LogNano, Important, "Player %s directory did not exist and was newly created (%s)!", Username.c_str(), GameroundDir.c_str())
		}
		else
		{
			TLOG(LogNano, Warning, "Player %s directory does not exist and an attempt to create it failed (%s)!", Username.c_str(), GameroundDir.c_str())
			return;
		}
	}

	for (const std::filesystem::directory_entry& entry : std::filesystem::directory_iterator(GameroundDir))
	{
		if (entry.path().extension() != GAMEROUND_FILE_EXTENSION)
		{
			toRemove.push_back(entry.path());
			continue;
		}

		std::ifstream infile(entry.path());
		if (!infile.is_open())
		{
			TLOG(LogNano, Warning, "Can't open player %s gameround file %s!", entry.path().c_str())
			continue;
		}

		const int64_t gid = yutils::strToInt(entry.path().stem(), 0);
		if (gid <= 0 || gid >= lastvalid_gid)
		{
			toRemove.push_back(entry.path());
			continue;
		}

		try
		{
			json Gameround;
			infile >> Gameround;
			GameroundHistory->emplace(gid, std::move(Gameround));
		}
		catch (const std::exception& e)
		{
			toRemove.push_back(entry.path());
			TLOG(LogNano, Warning, "Error parsing player %s history file: %s", Username.c_str(), e.what())
		}
	}

	while (GameroundHistory->size() > PLAYER_HISTORY_LIMIT)
	{
		toRemove.push_back(GameroundDir / (std::to_string(GameroundHistory->begin()->first) + GAMEROUND_FILE_EXTENSION));
		GameroundHistory->erase(GameroundHistory->begin());
	}

	TLOG(LogNano, Debug, "Loaded %lu games from player %s history.", GameroundHistory->size(), Username.c_str())
	lock.unlock();

	for (const std::filesystem::path& file : toRemove)
	{
		std::error_code ec;
		std::filesystem::remove(file, ec);
	}
}

void NanoPlayer::SaveHistory()
{
	std::map<uint64_t, json> historyToSave;
	{
		ScopedLock lock(GameroundHistory);
		if (!UnsavedGameroundID)
			return;
		auto it = GameroundHistory->find(UnsavedGameroundID);
		UnsavedGameroundID = 0;
		if (it == GameroundHistory->end())
			return;
		historyToSave.insert(it, GameroundHistory->end());
	}

	Nano->DoAsyncTask(
	  [bSync = Nano->ShouldSyncDiskOperations(), historyToSave, user = Username, dir = PlayerHistoryDir / "gamerounds"]() {
		  if (!std::filesystem::exists(dir))
		  {
			  TLOG(LogNano, Warning, "Player %s directory does not exist, creating it!", user.c_str())
			  if (!std::filesystem::create_directories(dir))
			  {
				  TLOG(LogNano, Error, "Player %s directory (%s) could not be created!", user.c_str(), dir.c_str())
				  return;
			  }
		  }

		  for (const auto& p : historyToSave)
		  {
			  const std::string filename = dir / (std::to_string(p.first) + ".json");
			  if (bSync)
			  {
				  filesystem::SyncWrite(filename, p.second.dump());
			  }
			  else
			  {
				  std::ofstream outfile(filename);
				  outfile << p.second;
			  }
		  }

		  TLOG(LogNano, Debug, "Saved %lu player %s gamerounds", historyToSave.size(), user.c_str())
	  },
	  "history-" + Username);
}

ENanoPlayerType NanoPlayer::Type() const
{
	return mType;
}

bool NanoPlayer::IsInGame() const
{
	for (auto session : Sessions)
	{
		ScopedLock lock(session.second->Data);
		if (session.second->Data.HasActiveGameround())
			return true;
	}
	return false;
}

bool NanoPlayer::Claimed() const
{
	return !mInstance.SCPFolder.empty();
}

json NanoPlayer::Descriptor(bool bExtended)
{
	ScopedLock lock(this);
	json playerJSON(json::value_t::object);
	playerJSON["type"] = mType._to_string();
	playerJSON["status"] = Instance().Status;
	playerJSON["username"] = Username;
	playerJSON["nickname"] = Nickname;
	playerJSON["jurisdiction"] = Jurisdiction;
	playerJSON["locale"] = Locale;
	playerJSON["displayableGID"] = DisplayableGameroundID;
	playerJSON["currency"] = Currency;
	playerJSON["claimed"] = Claimed();
	playerJSON["balance"] = CreditJSON(Balance);
	playerJSON["last-agent"] = mInstance.VideoURL;
	playerJSON["last-ip"] = mInstance.AudioURL;
	playerJSON["decimal-char"] = DecimalChar;
	playerJSON["thousand-char"] = ThousandChar;
	playerJSON["decimal-places"] = DecimalPlaces;
	playerJSON["align-left"] = AlignLeft;
	playerJSON["currency-symbol"] = CurrencySymbol;

	json configJSON(json::value_t::object);
	for (const auto& [key, value] : Config)
	{
		if (!RESERVED_CONFIG_NAMES.contains(key))
			configJSON[key] = value;
	}
	playerJSON["config"] = std::move(configJSON);
	if (bExtended)
	{
		CounterMap counters = GetCounters({ ATTENDANT_COUNTER2_BET, ATTENDANT_COUNTER2_WIN, ATTENDANT_COUNTER2_IN, ATTENDANT_COUNTER2_OUT, LIFETIME_COUNTER_BET,
		                                    LIFETIME_COUNTER_WIN, LIFETIME_COUNTER_IN, LIFETIME_COUNTER_OUT },
		                                  {});

		json counterObj(json::value_t::object);
		counterObj["bet"] = counters[ATTENDANT_COUNTER2_BET].Value;
		counterObj["win"] = counters[ATTENDANT_COUNTER2_WIN].Value;
		counterObj["in"] = counters[ATTENDANT_COUNTER2_IN].Value;
		counterObj["out"] = counters[ATTENDANT_COUNTER2_OUT].Value;
		playerJSON["counters"] = std::move(counterObj);

		json lifetimeCounterObj(json::value_t::object);
		lifetimeCounterObj["bet"] = counters[LIFETIME_COUNTER_BET].Value;
		lifetimeCounterObj["win"] = counters[LIFETIME_COUNTER_WIN].Value;
		lifetimeCounterObj["in"] = counters[LIFETIME_COUNTER_IN].Value;
		lifetimeCounterObj["out"] = counters[LIFETIME_COUNTER_OUT].Value;
		playerJSON["lifetime-counters"] = std::move(lifetimeCounterObj);

		playerJSON["last-reset"] = LastCounterReset;
	}
	return playerJSON;
}

void NanoPlayer::SetActive(bool bUpdateInDB)
{
	mInstance.LastUpdateTimestamp = ytime::GetSystemTimeMsec();
	if (bUpdateInDB)
		Nano->pDBMngr->ExecuteAsync([inst = mInstance](TGameDBSrv* db, soci::session& sql) { db->UpdateInstanceActive(inst.ID, inst.MAC); }, true, true,
		                            { ETransactionFlags::WaitTransaction });
}

void NanoPlayer::UpdateDBInstance() const
{
	Nano->pDBMngr->ExecuteAsync([inst = mInstance](TGameDBSrv* db, soci::session& sql) { db->UpdateInstance(inst); });
}

void NanoPlayer::SetLastAccessFrom(const std::string& userAgent, const std::string& ip)
{
	if (!userAgent.empty())
		mInstance.VideoURL = userAgent.substr(0, 512);
	if (!ip.empty())
		mInstance.AudioURL = ip.substr(0, 512);
}

void NanoPlayer::ClearUserHandpay(bool bCancelled)
{
	if (!PendingUserHandpay)
		return;

	PendingUserHandpay.reset();
	Nano->pDBMngr->ExecuteSync([&](TGameDBSrv* db, soci::session& sql) { db->_DeleteParam(Username, USER_HANDPAY_PARAM); });


	if (PendingHandpays.empty())
	{
		if (bCancelled)
		{
			TriggerEvent({}, "handpay-cancelled");
			Nano->BroadcastPlayerEvent(Username, "handpay-cancelled", {});
		}
		SetStatus(bIsOnline ? "ONLINE" : "OFFLINE");
	}
}

CreditArray NanoPlayer::StartHandpay(const FPendingHandpay& handpay)
{
	CreditArray amount = BalanceAfterHandpays();

	int64_t amountTotal = 0;
	if (handpay.Source == EHandpayTriggerSource::User)
	{
		if (PendingUserHandpay)
		{
			const uint64_t newAmount = handpay.Amount && PendingUserHandpay->second ? std::max<uint64_t>(handpay.Amount, PendingUserHandpay->second) : 0UL;
			if (newAmount == PendingUserHandpay->second)
				return newAmount ? amount.MakePayout(newAmount) : amount.MaxPayout();
			PendingUserHandpay->second = newAmount;
		}
		else
			PendingUserHandpay = { handpay.RequestedFromGame, handpay.Amount };

		amountTotal = PendingUserHandpay->second;

		Nano->pDBMngr->SetSpecificParam(Username, Username, USER_HANDPAY_PARAM, std::to_string(amountTotal), {}, {}, false);
	}
	else
	{
		amountTotal = handpay.Amount;
		PendingHandpays.push_back(handpay);
		SavePendingHandpays();
	}

	if (amountTotal)
		amount = amount.MakePayout(PendingUserHandpay->second);
	else
		amount = amount.MaxPayout();

	OnHandpayChanged();

	return amount;
}

void NanoPlayer::OnHandpayChanged()
{
	SetActive();

	FPendingHandpay pending;
	if (GetCurrentHandpay(pending))
	{
		SetStatus(HANDPAY_STATUS);

		const json handpayJson(pending.ToJson(Nano->FindTransaction(pending.TriggeredByTRID)));
		TriggerEvent({}, "handpay-pending", handpayJson);
		Nano->BroadcastPlayerEvent(Username, "handpay-pending", handpayJson);
	}
	else
	{
		SetStatus(bIsOnline ? "ONLINE" : "OFFLINE");
	}
}

void NanoPlayer::SavePendingHandpays()
{
	Nano->pDBMngr->ExecuteSync(
	  [&](TGameDBSrv* db, soci::session& sql) {
		  sql << "DELETE FROM CONFIGURATION WHERE CLIENT_IP=? AND ATTR_NAME LIKE '" + SYS_HANDPAY_PARAM_PREFIX + "%'", soci::use(Username);
		  int i = 0;
		  for (const FPendingHandpay& handpay : PendingHandpays)
			  db->_SetParam(Username, Username, SYS_HANDPAY_PARAM_PREFIX + std::to_string(i++), handpay.ToString(), {}, {}, ELanguage::English, false);
	  },
	  0, false, true, { ETransactionFlags::WaitTransaction });
}

bool NanoPlayer::GetCurrentHandpay(FPendingHandpay& outHandpay) const
{
	if (PendingHandpays.empty())
	{
		if (!PendingUserHandpay)
			return false;

		outHandpay.Source = EHandpayTriggerSource::User;
		outHandpay.RequestedFromGame = PendingUserHandpay->first;
		outHandpay.Amount = PendingUserHandpay->second;
	}
	else
	{
		outHandpay = PendingHandpays.front();
	}

	if (!outHandpay.Amount)
		outHandpay.Amount = Balance.MaxPayout().Total();

	return true;
}

void NanoPlayer::ResolveHandpay(const json& handpayObj, const std::string& paidBy)
{
	if (PendingHandpays.empty())
	{
		ClearUserHandpay(false);
	}
	else
	{
		PendingHandpays.pop_front();
		SavePendingHandpays();
	}

	json handpayConfirmedEventCtx(json::value_t::object);
	handpayConfirmedEventCtx["handpay"] = handpayObj;
	handpayConfirmedEventCtx["confirmed-by"] = paidBy;
	TriggerEvent({}, "handpay-confirmed", handpayConfirmedEventCtx);
	Nano->BroadcastPlayerEvent(Username, "handpay-confirmed", handpayConfirmedEventCtx);

	OnHandpayChanged();
}

CreditArray NanoPlayer::BalanceAfterHandpays() const
{
	CreditArray bal(Balance);
	for (const FPendingHandpay& handpay : PendingHandpays)
	{
		if (handpay.Amount)
			bal -= bal.MakePayout(handpay.Amount);
		else
			bal -= bal.MaxPayout();
	}
	return bal;
}

void NanoPlayer::EndAllSessions()
{
	for (auto sessionIt = Sessions.begin(); sessionIt != Sessions.end(); sessionIt = EndSession(sessionIt)) {}
}

json NanoPlayer::GetConfig(const std::string& paramName, const json& ifNotExists) const
{
	auto paramIt = Config.find(paramName.substr(0, 36));
	return (paramIt == Config.end()) ? ifNotExists : paramIt->second;
}

void NanoPlayer::SetConfig(const std::string& paramName, const json& value)
{
	const std::string confName = paramName.substr(0, 36);
	std::string paramValue;
	if (paramName == "locale")
	{
		Locale = value.get<std::string>();
		paramValue = Locale;
	}
	else if (paramName == "currency")
	{
		Currency = value.get<std::string>();
		paramValue = Currency;
	}
	else
	{
		Config[paramName] = value;
		paramValue = value.dump().substr(0, 146);
	}
	Nano->pDBMngr->SetSpecificParam(Username, Username, "USR_" + MyUtils::ToUppercase(confName), paramValue, {}, {}, true);

	if (!RESERVED_CONFIG_NAMES.contains(paramName))
		TriggerEvent({}, "user-setting-changed", { { "name", paramName }, { "value", value } });
}

const std::string& NanoPlayer::GetDepositOwner() const
{
	ScopedLock lock(this);
	return DepositOwner;
}

std::map<std::string, std::shared_ptr<Session>>::iterator NanoPlayer::EndSession(std::map<std::string, std::shared_ptr<Session>>::iterator session)
{
	if (session == Sessions.end())
		return session;
	TriggerEvent(session->first, "session-end", session->first, EPlayerEventPropagation::OwnerSession);
	Nano->mSessions.Lock();
	Nano->mSessions->erase(session->first);
	Nano->mSessions.Unlock();

	{
		ScopedLock lock(session->second->Data);
		if (auto con = session->second->Data.Stream.lock())
		{
			std::error_code ec;
			con->close(websocketpp::close::status::normal, "Session ended.", ec);
			Nano->Log(Warning, "Closed player stream of %s because the session has ended!", Username.c_str());
		}
	}

	return Sessions.erase(session);
}

void NanoPlayer::SetStatus(const std::string& status)
{
	if (mInstance.Status != status)
	{
		mInstance.Status = status;
		Nano->pDBMngr->ExecuteAsync([inst = mInstance](TGameDBSrv* db, soci::session& sql) { db->UpdateInstanceStatus2(inst.ID, inst.MAC, inst.Status); });
		TriggerEvent({}, "status", status);
		Nano->BroadcastPlayerEvent(Username, "status", status);
	}
}

void NanoPlayer::TriggerEvent(const std::string& session, const std::string& eventName, const json& context, EPlayerEventPropagation EventPropagation) const
{
	json obj(json::value_t::object);
	obj["event"] = eventName;
	obj["context"] = context;
	if (!session.empty())
		obj["session"] = session;

	const std::string payload = obj.dump();
	if (!PlayerStreams.empty())
	{
		std::error_code ec;
		for (const auto& stream : PlayerStreams)
		{
			Nano->Endpoint->send(stream, payload, websocketpp::frame::opcode::TEXT, ec);
			if (ec)
			{
				TLOG(LogNano, Warning, "Error sending player event to a stream!");
			}
			ec.clear();
		}
	}

	if ((EventPropagation == EPlayerEventPropagation::OwnerSession || EventPropagation == EPlayerEventPropagation::AllExceptOwnerSession) && session.empty())
		EventPropagation = EPlayerEventPropagation::AllSessions;

	if (EventPropagation != EPlayerEventPropagation::OnlyPlayerListeners)
	{
		for (const auto& session_pair : Sessions)
		{
			if (EventPropagation == EPlayerEventPropagation::OwnerSession && session_pair.first != session)
				continue;

			if (EventPropagation == EPlayerEventPropagation::AllExceptOwnerSession && session_pair.first == session)
				continue;

			ScopedLock lock(session_pair.second->Data);
			if (auto connection = session_pair.second->Data.Stream.lock())
			{
				std::error_code ec = connection->send(payload, websocketpp::frame::opcode::TEXT);
				if (ec)
				{
					TLOG(LogNano, Warning, "Error sending player event to a session: %s", ec.message().c_str())
				}
			}
		}
	}

	// Nano->BroadcastPlayerEvent(Username, "event", obj);
}

CounterMap NanoPlayer::GetCounters(const std::unordered_set<int>& counterIDs, const GameIdentifier& game)
{
	CounterMap results;
	std::unordered_set<int> toQueryInDB;

	ScopedLock lock(this);
	auto& perGameCounters = Counters.GetGameCounters(game);
	if (counterIDs.empty())
	{
		results = perGameCounters;
	}
	else
	{
		for (const int& id : counterIDs)
		{
			if (NanoCounters::IsCreditCounter(id))
			{
				if (!game.valid())
					results[id].Value = Balance[NanoCounters::CreditTypeFromCounterID(id)];
				continue;
			}

			auto emplaced = perGameCounters.try_emplace(id, CounterInfo());

			// if we just added it (means it didn't exist before), then load it from DB
			if (emplaced.second)
				toQueryInDB.insert(id);
			else
				results[id] = emplaced.first->second;
		}
	}

	if (!toQueryInDB.empty())
	{
		Nano->pDBMngr->ExecuteSync(
		  [&](TGameDBSrv* db, soci::session& sql) {
			  CounterInfo info;
			  for (auto counter : toQueryInDB)
			  {
				  db->GetCounter(Username, game, counter, info.Value, &info.CountingFrom_Timestamp);
				  results[counter] = info;
			  }
		  },
		  0, false, true, { ETransactionFlags::ReadOnly });
	}

	return results;
}

DigitalCounterCache<CounterMap> NanoPlayer::GetCountersCategory(const std::unordered_set<int>& counterIDs, const GameIdentifier& game)
{
	DigitalCounterCache<CounterMap> results;
	std::map<GameIdentifier, std::unordered_set<int>> toQueryInDB;

	ScopedLock lock(this);

	const std::vector<GameIdentifier> gamesInCategory = Counters.GetGamesInCategory(game);

	for (const GameIdentifier& g : gamesInCategory)
	{
		auto& perGameCounters = Counters.GetGameCounters(g);
		auto& outCounters = results.GetGameCounters(g);

		if (counterIDs.empty())
		{
			outCounters = perGameCounters;
		}
		else
		{
			for (const int& id : counterIDs)
			{
				if (NanoCounters::IsCreditCounter(id))
				{
					if (!g.valid())
						outCounters[id].Value = Balance[NanoCounters::CreditTypeFromCounterID(id)];
					continue;
				}

				auto emplaced = perGameCounters.try_emplace(id, CounterInfo());

				// if we just added it (means it didn't exist before), then load it from DB
				if (emplaced.second)
					toQueryInDB[g].insert(id);
				else
					outCounters[id] = emplaced.first->second;
			}
		}
	}

	if (!toQueryInDB.empty())
	{
		Nano->pDBMngr->ExecuteSync(
		  [&](TGameDBSrv* db, soci::session& sql) {
			  for (const auto& [g, counters] : toQueryInDB)
			  {
				  auto& outCounters = results.GetGameCounters(g);
				  for (int counter : counters)
				  {
					  CounterInfo& info = outCounters[counter];
					  db->GetCounter(Username, g, counter, info.Value, &info.CountingFrom_Timestamp);
				  }
			  }
		  },
		  0, false, true, { ETransactionFlags::ReadOnly });
	}

	return results;
}

DigitalCounterCache<CounterMap> NanoPlayer::GetCounterTotals(const std::unordered_set<int>& counterIDs, const GameIdentifier& game)
{
	const std::vector<GameIdentifier> games = NanoCounters::GetAllCounterSetsFor(game);

	DigitalCounterCache<CounterMap> ret;
	for (const GameIdentifier& g : games) ret.GetGameCounters(g) = GetCounters(counterIDs, g);
	return ret;
}

void NanoPlayer::ClearCachedData()
{
	Counters = {};
	ScopedLock lock(GameroundHistory);
	GameroundHistory->clear();
}

const std::unordered_map<int, int> HiddenCounterMap = { { LIFETIME_COUNTER_IN, INVISIBLE_IN },
	                                                    { LIFETIME_COUNTER_OUT, INVISIBLE_OUT },
	                                                    { LIFETIME_COUNTER_BET, INVISIBLE_BET },
	                                                    { LIFETIME_COUNTER_WIN, INVISIBLE_WIN },
	                                                    { CS_GAMES_PLAYED, INVISIBLE_GAMES_PLAYED },
	                                                    { CS_ATTENDANT_HANDPAY, INVISIBLE_ATTENDANT_HANDPAY },
	                                                    { CS_BILLS_TOTAL_CREDIT, INVISIBLE_BILLS_IN },
	                                                    { CS_ADMIN_KEY_IN_CREDIT, INVISIBLE_ELECTRONIC_IN },
	                                                    { CS_ADMIN_KEY_OUT_CASHABLE, INVISIBLE_ELECTRONIC_OUT },
	                                                    { CS_ADMIN_KEY_OUT_RESTRICTED, INVISIBLE_ELECTRONIC_OUT },
	                                                    { CS_ADMIN_KEY_OUT_NONRESTRICTED, INVISIBLE_ELECTRONIC_OUT },
	                                                    { CS_COIN_IN, INVISIBLE_COINS_IN },
	                                                    { CS_COIN_OUT, INVISIBLE_COINS_OUT },
	                                                    { CS_TICKET_IN_CREDIT, INVISIBLE_TICKETS_IN },
	                                                    { CS_TICKET_OUT_CREDIT, INVISIBLE_TICKETS_OUT },
	                                                    { CS_TOTAL_MACHINE_PAID_EXTERNAL_BONUS_WIN, INVISIBLE_BONUS_WIN },
	                                                    { CS_TOTAL_ATTENDANT_PAID_EXTERNAL_BONUS_WIN, INVISIBLE_BONUS_WIN },
	                                                    { CS_TOTAL_MACHINE_PAID_PROGRESSIVE_WIN, INVISIBLE_PROGRESSIVE_WIN },
	                                                    { CS_TOTAL_ATTENDANT_PAID_PROGRESSIVE_WIN, INVISIBLE_PROGRESSIVE_WIN } };

const std::unordered_set<int> GameOnlyCounterSet = { CS_POWER_UP_COUNTER };

const std::unordered_set<int> ScalingCounterSet = { LIFETIME_COUNTER_IN,
	                                                LIFETIME_COUNTER_OUT,
	                                                LIFETIME_COUNTER_BET,
	                                                LIFETIME_COUNTER_WIN,
	                                                LIFETIME_COUNTER_RESTRICTED_BET,
	                                                LIFETIME_COUNTER_NON_RESTRICTED_BET,
	                                                CS_BILLS_IN_STACKER_CREDIT,
	                                                CS_BILLS_TOTAL_CREDIT,
	                                                CS_BILLS_TOTAL_OUT_CREDIT,
	                                                CS_COIN_IN,
	                                                CS_COIN_OUT,
	                                                CS_IN_HOUSE_TOTAL_IN,
	                                                CS_IN_HOUSE_TOTAL_OUT,
	                                                CS_IN_HOUSE_CASHABLE_IN_CENTS,
	                                                CS_IN_HOUSE_CASHABLE_OUT_CENTS,
	                                                CS_IN_HOUSE_RESTRICTED_IN_CENTS,
	                                                CS_IN_HOUSE_RESTRICTED_OUT_CENTS,
	                                                CS_IN_HOUSE_NONRESTRICTED_IN_CENTS,
	                                                CS_IN_HOUSE_NONRESTRICTED_OUT_CENTS,
	                                                CS_CASHABLE_TICKET_IN_CREDIT,
	                                                CS_CASHABLE_TICKET_OUT_CREDIT,
	                                                CS_RESTRICTED_TICKET_IN_CREDIT,
	                                                CS_RESTRICTED_TICKET_OUT_CREDIT,
	                                                CS_NONRESTRICTED_TICKET_IN_CREDIT,
	                                                CS_ATTENDANT_HANDPAY,
	                                                CS_ADMIN_KEY_IN_CREDIT,
	                                                CS_TOTAL_MACHINE_PAID_EXTERNAL_BONUS_WIN,
	                                                CS_TOTAL_ATTENDANT_PAID_EXTERNAL_BONUS_WIN,
	                                                CS_TOTAL_MACHINE_PAID_PROGRESSIVE_WIN,
	                                                CS_TOTAL_ATTENDANT_PAID_PROGRESSIVE_WIN,
	                                                CS_TOTAL_MACHINE_PAID_PAYTABLE_WIN,
	                                                CS_TOTAL_ATTENDANT_PAID_PAYTABLE_WIN,
	                                                CS_TICKET_IN_CREDIT,
	                                                CS_TICKET_OUT_CREDIT,
	                                                CS_METER_IMAXA_JACKPOT_WIN_CREDITS,
	                                                CS_GAMBLE_TOTAL_BET,
	                                                CS_GAMBLE_TOTAL_WIN,
	                                                CS_RETURN_CHANGE_TOTAL_CREDITS,
	                                                CS_PAYOUT_ROUNDED_TOTAL_CREDITS };

DigitalCounterCache<CounterMap> NanoPlayer::IncrementCounters(const std::unordered_set<int>& counterIDs, int64_t amount, const GameIdentifier& game,
                                                              const std::string& source, const std::string& sourceSession)
{
	if (!amount || counterIDs.empty())
		return {};

	time_t TimeSeconds = time(NULL);
	tm Time;
	localtime_r(&TimeSeconds, &Time);

	const std::vector<GameIdentifier> games = NanoCounters::GetAllCounterSetsFor(game);

	std::map<GameIdentifier, std::unordered_map<int, int64_t>> toIncrement;
	for (const GameIdentifier& g : games)
	{
		CounterMap& perGameCounters = Counters.GetGameCounters(g);
		for (const int& id : counterIDs)
		{
			int64_t scaledAmount = amount;
			if (CounterScale < 100 && ScalingCounterSet.contains(id))
			{
				int remainderCounterID = NanoCounters::getRemainderCounterId(id);    // get shifted id
				CounterInfo& remainderCounter = perGameCounters[remainderCounterID];

				// calculate scaled amount and remainder
				int tmpAmount = (amount * CounterScale) + remainderCounter.Value;
				scaledAmount = static_cast<int>(tmpAmount / 100.0);
				int remainder = tmpAmount - (scaledAmount * 100);

				TLOG(LogNano, Info,
				     "Saving counter %d to DB with scaled amount of %ld (original amount: %ld +  previous remainder: %ld), because COUNTER_SCALE is set to %d%!", id,
				     scaledAmount, amount, remainderCounter.Value, CounterScale)
				if (remainder > 0)
					TLOG(LogNano, Info, "Saving remainder counter %d to DB with amount of %ld, which will be added to next change!", remainderCounterID, remainder)

				// update in database
				Nano->pDBMngr->ExecuteAsync([username = Username, remainderCounterID, remainder, g](TGameDBSrv* db, soci::session& sql) {
					db->ResetCounter(username, g, remainderCounterID, remainder, true);
				});

				// update cached value
				remainderCounter.Value = remainder;
			}

			auto& toIncrementPerGame = toIncrement[g];
			toIncrementPerGame.insert({ id, scaledAmount });
			toIncrementPerGame.insert({ NanoCounters::getYearCounterId(id, (Time.tm_year + 1900)), scaledAmount });
			toIncrementPerGame.insert({ NanoCounters::getMonthCounterId(id, (Time.tm_mon + 1)), scaledAmount });

			if (!Nano->IsInPresentationMode())
			{
				auto find = HiddenCounterMap.find(id);
				if (find != HiddenCounterMap.end())
					toIncrementPerGame.insert({ find->second, amount });
			}
		}
	}

	DigitalCounterCache<CounterMap> values;
	for (const auto& [g, toIncrementPerGame] : toIncrement)
	{
		CounterMap& perGameCnt = Counters.GetGameCounters(g);
		CounterMap& perGameCntOut = values.GetGameCounters(g);
		for (auto const& [counterId, byVal] : toIncrementPerGame)
		{
			auto emplaced = perGameCnt.try_emplace(counterId, CounterInfo());
			CounterInfo& ctInfo = emplaced.first->second;
			if (emplaced.second)
				ctInfo.CountingFrom_Timestamp = ytime::GetSystemTimeMsec();

			ctInfo.Value += byVal;
			if (counterId > 0)
				perGameCntOut[counterId] = ctInfo;
		}
	}

	Nano->pDBMngr->ExecuteAsync([user = Username, toIncrement, incrementCausedByGame = game.valid(), values](TGameDBSrv* db, soci::session& sql) {
		int counterID;
		int64_t amount;
		if (db->HasCreditTransfersV7())
		{
			int gamePackage;
			std::string gameConfig;
			soci::statement st =
			  (sql.prepare
			     << R"(UPDATE OR INSERT INTO DIGITAL_COUNTERS_V7(COUNTER_VALUE, CLIENT_ID, COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTING_FROM) VALUES(?,?,?,?,?,current_timestamp))",
			   soci::use(amount), soci::use(user), soci::use(counterID), soci::use(gamePackage), soci::use(gameConfig));

			values.ForEachCounterSet([&](const GameIdentifier& gameId, const CounterMap& counters) {
				gamePackage = gameId.Game;
				gameConfig = gameId.Configuration;
				for (const auto& [id, byValue] : counters)
				{
					if (incrementCausedByGame && !gamePackage && GameOnlyCounterSet.contains(id))
						return;

					counterID = id;
					amount = byValue.Value;
					st.execute(true);
				}
			});
		}
		else
		{
			soci::statement st =
			  (sql.prepare
			     << R"(MERGE INTO DIGITAL_COUNTERS CNT USING (SELECT CAST(? AS T_MACHINE_IP) AS CLIENT, CAST(? AS T_TINTEGER) AS CNT_ID, CAST(? AS bigint) AS CHANGE FROM RDB$DATABASE) S
ON (CNT.CLIENT_IP = S.CLIENT AND CNT.COUNTER_INDEX = S.CNT_ID)
WHEN MATCHED THEN
  UPDATE SET CNT.COUNTER_VALUE = CNT.COUNTER_VALUE + S.CHANGE
WHEN NOT MATCHED THEN
  INSERT (CLIENT_ID, COUNTER_INDEX, COUNTER_VALUE, COUNTING_FROM) VALUES (S.CLIENT, S.CNT_ID, S.CHANGE, current_timestamp);
)",
			   soci::use(user), soci::use(counterID), soci::use(amount));
			for (const auto& [g, toIncrementPerGame] : toIncrement)
			{
				for (auto const& [id, byValue] : toIncrementPerGame)
				{
					if (incrementCausedByGame && !g.valid() && GameOnlyCounterSet.contains(id))
						continue;
					counterID = getGameCounterId_Legacy(id, g);
					amount = byValue;
					st.execute(true);
				}
			}
		}
	});

	json changeEvent(json::value_t::object);
	changeEvent["source"] = source;
	changeEvent["change"] = amount;
	changeEvent["game"] = std::string(game);
	changeEvent["counters"] = values.ToJSON();
	TriggerEvent(sourceSession, "counter-change", changeEvent, EPlayerEventPropagation::AllExceptOwnerSession);

	return values;
}

DigitalCounterCache<CounterMap> NanoPlayer::ResetCounters(const std::unordered_set<int>& counterIDs, int64_t value, const GameIdentifier& game, const std::string& source,
                                                          const std::string& sourceSession)
{
	time_t TimeSeconds = time(NULL);
	tm Time;
	localtime_r(&TimeSeconds, &Time);

	const CounterInfo toClearWith({ 0, ytime::GetSystemTimeMsec() });

	DigitalCounterCache<CounterMap> toClear;
	std::unordered_set<int> idsToClear;
	for (const int& CounterId : counterIDs)
	{
		if (!CounterId || CounterId >= (int)NanoCounters::VALID_COUNTERS_COUNT)
			continue;

		idsToClear.insert(CounterId);
		if (CounterId > 0)
		{
			idsToClear.insert(NanoCounters::getYearCounterId(CounterId, (Time.tm_year + 1900)));
			idsToClear.insert(NanoCounters::getMonthCounterId(CounterId, (Time.tm_mon + 1)));
		}
	}

	if (idsToClear.empty())
		return {};

	const std::vector<GameIdentifier> gamesToReset = Counters.GetGamesInCategory(game);

	const uint64_t timeNow = ytime::GetSystemTimeMsec();
	Nano->pDBMngr->ExecuteSync([&](TGameDBSrv* db, soci::session& sql) {
		for (const GameIdentifier& g : gamesToReset)
		{
			auto& perGameCounters = Counters.GetGameCounters(g);
			auto& toClearCounters = toClear.GetGameCounters(g);
			for (const int& counterId : idsToClear)
			{
				auto find = perGameCounters.find(counterId);
				if (find != perGameCounters.end())
				{
					find->second.Value = value;
					find->second.CountingFrom_Timestamp = timeNow;
					toClearCounters[counterId] = find->second;
				}
				else if (value == 0)    // if we want to reset a nonexisting counter, don't do DB queries
				{
					continue;
				}

				db->ResetCounter(Username, g, counterId, value);
			}
		}
	});

	if (!toClear.empty())
	{
		json changeEvent(json::value_t::object);
		changeEvent["source"] = source;
		changeEvent["game"] = std::string(game);
		changeEvent["value"] = value;
		changeEvent["counters"] = toClear.ToJSON();
		TriggerEvent(sourceSession, "counter-reset", changeEvent, EPlayerEventPropagation::AllExceptOwnerSession);
	}

	return toClear;
}

const DigitalCounterCache<CounterMap>& NanoPlayer::AllCounters() const
{
	return Counters;
}

void NanoPlayer::OnTransaction(const NanoTransaction& trans, const std::string& inSession, const std::string& instigator)
{
	json transactionObj = trans.ToShortJSON();
	if (!instigator.empty())
		transactionObj["instigator"] = instigator;
	TriggerEvent(inSession, "transaction", transactionObj, EPlayerEventPropagation::OwnerSession);
	Nano->BroadcastPlayerEvent(Username, "transaction", transactionObj);

	const CreditArray change = trans.TransferedAmountInContextOf(Username);
	const ENanoTransactionType typeForThisPlayer = trans.TransactionTypeForUser(Username);

	if (typeForThisPlayer == ENanoTransactionType::Deposit && Balance.IsZero() && change.Total() > 0)
	{
		if (mType == ENanoPlayerType::Kiosk && Nano->bManageKioskPassword)
		{
			TriggerEvent(inSession, "request-auth");
		}

		if (!instigator.empty())
		{
			DepositOwner = instigator;
			Nano->pDBMngr->SetSpecificParam(Username, Username, "DEPOSIT_OWNER", DepositOwner.substr(0, 146),
			                                "The 'owner' of the last Deposit transaction to this account", {}, false);
		}
	}

	switch (typeForThisPlayer)
	{
		case ENanoTransactionType::Deposit:
			IncrementCounters({ LIFETIME_COUNTER_IN, ATTENDANT_COUNTER2_IN, OWNER_COUNTER2_IN }, trans.Amount.Total(), trans.GameID, trans.From);
			break;
		case ENanoTransactionType::Withdraw:
			IncrementCounters({ LIFETIME_COUNTER_OUT, ATTENDANT_COUNTER2_OUT, OWNER_COUNTER2_OUT }, trans.Amount.Total(), trans.GameID, trans.To);
			break;
		case ENanoTransactionType::Bet:
			IncrementCounters({ LIFETIME_COUNTER_BET, ATTENDANT_COUNTER2_BET, OWNER_COUNTER2_BET }, trans.Amount.Total(), trans.GameID, Username);
			IncrementCounters({ LIFETIME_COUNTER_RESTRICTED_BET }, trans.Amount[CREDIT_RESTRICTED], trans.GameID, Username);
			IncrementCounters({ LIFETIME_COUNTER_NON_RESTRICTED_BET }, trans.Amount[CREDIT_NON_RESTRICTED], trans.GameID, Username);
			break;
		case ENanoTransactionType::Win:
		case ENanoTransactionType::Jackpot:
		case ENanoTransactionType::ExternalBonus:
		case ENanoTransactionType::ExtBonusHandpay: {
			const int64_t PreWinBalance = BalanceAfterHandpays().Total();
			const int64_t TotalWon = trans.Amount.Total();
			const std::string source = (typeForThisPlayer == ENanoTransactionType::Win) ? Username : trans.From;

			IncrementCounters({ LIFETIME_COUNTER_WIN, ATTENDANT_COUNTER2_WIN, OWNER_COUNTER2_WIN }, TotalWon, trans.GameID, source);

			int64_t IncreaseMachinePaidCounters = 0;
			FPendingHandpay Handpay;
			Handpay.TriggeredByTRID = trans.TransactionID;
			if (typeForThisPlayer == ENanoTransactionType::Jackpot)
			{
				IncrementCounters({ CS_METER_IMAXA_JACKPOT_WIN_CREDITS }, TotalWon, trans.GameID, Username);
				IncrementCounters({ CS_METER_IMAXA_JACKPOT_WIN_COUNT }, 1, trans.GameID, Username);
			}
			if (typeForThisPlayer == ENanoTransactionType::ExtBonusHandpay)
			{
				Handpay.Source = EHandpayTriggerSource::Jackpot;
				Handpay.Amount = TotalWon;
			}
			else if (Nano->mMaxWin && TotalWon >= Nano->mMaxWin)
			{
				IncrementCounters({ LIFETIME_BIG_WIN }, TotalWon, trans.GameID, source);
				Handpay.Source = EHandpayTriggerSource::ExceededMaxWin;
				Handpay.Amount = TotalWon;
				TLOG(LogNano, Important, "%s: Won amount(%ld) exceeds MaxWinLimit(%lu) -> going into handpay for the won amount", Username.c_str(), TotalWon,
				     Nano->mMaxWin)
			}
			else if (Nano->mMaxBalance && PreWinBalance + TotalWon > Nano->mMaxBalance)
			{
				Handpay.Source = EHandpayTriggerSource::ExceededMaxBalance;
				Handpay.Amount = PreWinBalance + TotalWon - Nano->mMaxBalance;
				IncreaseMachinePaidCounters = Nano->mMaxBalance - PreWinBalance;
				TLOG(LogNano, Important,
				     "%s: Won amount(%ld) pushed balance over credit limit(%ld + %ld = %lu, limit is %lu) -> going into handpay for the amount over limit (%lu)",
				     Username.c_str(), TotalWon, PreWinBalance, TotalWon, PreWinBalance + TotalWon, Nano->mMaxBalance, Handpay.Amount)
			}
			else
			{
				IncreaseMachinePaidCounters = TotalWon;
			}

			if (IncreaseMachinePaidCounters)
			{
				if (typeForThisPlayer == ENanoTransactionType::Win)
					IncrementCounters({ CS_TOTAL_MACHINE_PAID_PAYTABLE_WIN }, IncreaseMachinePaidCounters, trans.GameID, source);
				else if (typeForThisPlayer == ENanoTransactionType::Jackpot)
					IncrementCounters({ CS_TOTAL_MACHINE_PAID_PROGRESSIVE_WIN }, IncreaseMachinePaidCounters, trans.GameID, source);
				else
					IncrementCounters({ CS_TOTAL_MACHINE_PAID_EXTERNAL_BONUS_WIN }, IncreaseMachinePaidCounters, trans.GameID, source);
			}

			if (Handpay.Amount)
			{
				Handpay.Time = ytime::GetSystemTimeMsec();
				StartHandpay(Handpay);
			}
		}
		break;
		default: break;
	}

	Balance += change;
	OnBalanceChanged(typeForThisPlayer, inSession);
}

void NanoPlayer::OnBalanceChanged(ENanoTransactionType changeSource, const std::string& session)
{
	BalanceChangeID++;

	json ctx(json::value_t::object);
	ctx["balance"] = CreditJSON(Balance);
	ctx["source"] = changeSource._to_string();
	ctx["changeID"] = BalanceChangeID;
	TriggerEvent(session, "balance-update", ctx);
	Nano->BroadcastPlayerEvent(Username, "balance-update", ctx);
}

std::shared_ptr<FTransferToken> Timaxanano::RedeemTransferToken(uint64_t id)
{
	ScopedLock lock(mTransferTokens);
	auto find = mTransferTokens->extract(id);
	return find.empty() ? nullptr : find.mapped();
}

void Timaxanano::CheckDeadPlayers()
{
	std::atomic<size_t> deletedSessions = 0, deletedUnique = 0;

	std::atomic<int> RemovedPlayers = 0;

	const uint64_t now = ytime::GetSystemTimeMsec();

	auto players = mPlayerData.getCopy();
	std::for_each(std::execution::par_unseq, players.begin(), players.end(), [&](const std::pair<std::string, std::shared_ptr<NanoPlayer>>& playerIt) {
		auto& player = playerIt.second;
		ScopedLock lock(*player);
		size_t numDeletedSessions = 0;
		for (auto session_it = player->Sessions.begin(); session_it != player->Sessions.end();)
		{
			std::shared_ptr<Session> session_ptr = session_it->second;
			ScopedLock sesionDataLock(session_ptr->Data);

			/* if (session_ptr->Data.HasActiveGameround() && !session_ptr->Data.ActiveGame->Ended &&
			    (session_ptr->Data.ActiveGame->Started + 30 * 60 * 1000 < now))
			{
			    VoidGame(*session_ptr);
			} */

			// if there is no active game, the session is disconnected, the player is not a kiosk player, and the session should time out, delete
			// it!
			if (mSessionTimeout && session_ptr->bCanTimeout && !session_ptr->Data.HasActiveGameround() && !session_ptr->Data.Stream.lock() &&
			    player->Type() != ENanoPlayerType::Kiosk && session_ptr->Data.LastActive + mSessionTimeout < now)
			{
				session_it = player->EndSession(session_it);
				numDeletedSessions++;
			}
			else
			{
				session_it++;
			}
		}
		if (numDeletedSessions)
		{
			deletedUnique++;
			deletedSessions += numDeletedSessions;
		}
		if (player->Balance.IsZero() && player->Type() == ENanoPlayerType::Temp && mDeadPlayerTimeout &&
		    (player->Instance().LastUpdateTimestamp + mDeadPlayerTimeout < now))
		{
			player->DeletePlayer(true);
			mPlayerData.Lock();
			mPlayerData->erase(playerIt.first);
			mPlayerData.Unlock();
			RemovedPlayers++;
		}
	});

	if (deletedSessions.load() || RemovedPlayers)
	{
		Log(Info, "%lu player sessions (belonging to %lu unique players) were deleted due to inactivity. Of these unique players, %d were completely removed.",
		    deletedSessions.load(), deletedUnique.load(), RemovedPlayers.load());
	}
}

void NanoPlayer::DeletePlayer(bool bDeleteDB)
{
	if (mSaveHistoryThread.joinable())
		mSaveHistoryThread.join();

	TriggerEvent({}, "removed");
	Nano->BroadcastPlayerEvent(Username, "removed", json());
	EndAllSessions();

	if (bDeleteDB)
	{
		Nano->pDBMngr->ExecuteAsync([user = Username, counters = Counters](TGameDBSrv* db, soci::session& sql) {
			int counterID;
			int64_t value;
			if (db->HasDigitalCountersV7())
			{
				GameIdentifier game;
				soci::statement st =
				  (sql.prepare
				     << R"(MERGE INTO DIGITAL_COUNTERS_V7 CNT USING (SELECT CAST(? AS varchar(128)) AS CLIENT, CAST(? AS T_TINTEGER) AS CNT_ID, CAST(? AS T_TINTEGER) AS CNT_GAME_PACKAGE, CAST(? AS varchar(255)) AS CNT_GAME_CONFIG, CAST(? AS bigint) AS CHANGE FROM RDB$DATABASE) S
ON (CNT.CLIENT_ID = S.CLIENT AND CNT.COUNTER_INDEX = S.CNT_ID AND CNT.GAME_PACKAGE_ID = S.CNT_GAME_PACKAGE AND CNT.GAME_CONFIGURATION_NAME = S.CNT_GAME_CONFIG)
WHEN MATCHED THEN
  UPDATE SET CNT.COUNTER_VALUE = CNT.COUNTER_VALUE + S.CHANGE
WHEN NOT MATCHED THEN
  INSERT (CLIENT_ID, COUNTER_INDEX, GAME_PACKAGE_ID, GAME_CONFIGURATION_NAME, COUNTER_VALUE, COUNTING_FROM) VALUES (S.CLIENT, S.CNT_ID, S.CNT_GAME_PACKAGE, S.CNT_GAME_CONFIG, S.CHANGE, current_timestamp);
)",
				   soci::use(DEAD_PLAYER_CLIENT_IP), soci::use(counterID), soci::use(game.Game), soci::use(game.Configuration), soci::use(value));

				counters.ForEachCounter([&](const GameIdentifier& g, int id, const CounterInfo& info) {
					counterID = id;
					value = info.Value;
					game = g;
					st.execute(true);
				});
			}
			else
			{
				soci::statement st =
				  (sql.prepare
				     << R"(MERGE INTO DIGITAL_COUNTERS CNT USING (SELECT CAST(? AS T_MACHINE_IP) AS CLIENT, CAST(? AS T_TINTEGER) AS CNT_ID, CAST(? AS bigint) AS CHANGE FROM RDB$DATABASE) S
ON (CNT.CLIENT_IP = S.CLIENT AND CNT.COUNTER_INDEX = S.CNT_ID)
WHEN MATCHED THEN
  UPDATE SET CNT.COUNTER_VALUE = CNT.COUNTER_VALUE + S.CHANGE
WHEN NOT MATCHED THEN
  INSERT (CLIENT_IP, COUNTER_INDEX, COUNTER_VALUE, COUNTING_FROM) VALUES (S.CLIENT, S.CNT_ID, S.CHANGE, current_timestamp);
)",
				   soci::use(DEAD_PLAYER_CLIENT_IP), soci::use(counterID), soci::use(value));

				counters.ForEachCounter([&](const GameIdentifier& g, int id, const CounterInfo& info) {
					if (!g.Configuration.empty())
						return;
					counterID = getGameCounterId_Legacy(id, g);
					value = info.Value;
					st.execute(true);
				});
			}

			db->ClearInstanceData(user, user.starts_with("PLAYER_"));
			db->RemoveAllInstances(user, PLAYER_ID);

			db->UnregisterEvent(DBEvent_NewCmd + "_" + user);
			db->RecreateEventListener();
		});

		// transfer all counters to dead player sums (in-memory cache, persistent part is done above)
		ScopedLock lock(Nano->mDeadPlayerCounters);
		Counters.ForEachCounter([&](const GameIdentifier& g, int id, const CounterInfo& info) {
			auto emplaced = Nano->mDeadPlayerCounters->GetGameCounters(g).try_emplace(id, info);
			if (!emplaced.second)
				emplaced.first->second.Value += info.Value;
		});
	}

	std::set<uint64_t> TokensToRemove;
	{
		ScopedLock lock(Nano->mTransferTokens);
		for (auto allTokensIt = Nano->mTransferTokens->begin(); allTokensIt != Nano->mTransferTokens->end();)
		{
			if (allTokensIt->second->UserPlayerID == Username || allTokensIt->second->OwnerPlayerID == Username)
			{
				TokensToRemove.insert(allTokensIt->first);
				allTokensIt = Nano->mTransferTokens->erase(allTokensIt);
			}
			else
			{
				allTokensIt++;
			}
		}
	}

	if (!TokensToRemove.empty())
	{
		Nano->DoAsyncTask([tokenDir = Nano->mNanoDataDirectory / "Tokens", tokens = TokensToRemove]() {
			for (const uint64_t& token : tokens) std::filesystem::remove(tokenDir / (std::to_string(token) + TOKEN_FILE_EXTENSION));
		});
	}

	if (std::filesystem::exists(PlayerHistoryDir))
	{
		std::filesystem::remove_all(PlayerHistoryDir);
		TLOG(LogNano, Info, "Removed player %s directory!", Username.c_str())
	}
}

void Timaxanano::SaveSession(Session& session, bool bComplete)
{
	if (!session.Data.HasActiveGameround())
		return;

	const uint64_t gid = session.Data.ActiveGame->ID;
	const json toWrite = bComplete ? session.Data.ActiveGame->ToJSON() : session.ToJSON();

	DoAsyncTask(
	  [bSync = ShouldSyncDiskOperations(), bComplete, location = mGameroundsDirectory, gid, toWrite]() {
		  const std::string gameroundFileName = std::to_string(gid) + GAMEROUND_FILE_EXTENSION;
		  const std::filesystem::path activeGameroundFilePath = location / gameroundFileName;
		  std::string filename;

		  if (bComplete)
		  {
			  const std::filesystem::path archivedGameround = location / "Archive" / gameroundFileName;
			  if (std::filesystem::exists(activeGameroundFilePath))
			  {
				  std::error_code ec;
				  std::filesystem::remove(activeGameroundFilePath, ec);
				  if (ec)
					  TLOG(LogNano, Critical, "Gameround %lu could not be removed (%s) - this is a big problem and will mess with server logic!", gid,
					       ec.message().c_str())
			  }

			  filename = archivedGameround;
		  }
		  else
		  {
			  filename = activeGameroundFilePath;
		  }

		  if (bSync)
		  {
			  filesystem::SyncWrite(filename, toWrite.dump());
		  }
		  else
		  {
			  std::ofstream gameroundFile(filename);
			  gameroundFile << toWrite;
		  }
	  },
	  session.SID);
}
