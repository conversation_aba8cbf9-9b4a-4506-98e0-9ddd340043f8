#include "currency-api/ExchangeRateServerApp.h"

using namespace rtfwk_sdl2;

MAIN_METHOD(TExchangeRateServerApp, "EXCHANGE_RATE_SERVER")

DEFINE_LOG_CATEGORY(LogExchangeServer, "exchange-server")

TExchangeRateServerApp::TExchangeRateServerApp(const std::string& type) : TApplication(type)
{
	Schema().AddMember("default-currency", JsonSchema(json::value_t::string, "The base currency to use for conversion pairs", ECurrency(ECurrency::EUR)._to_string()));
	Schema().AddMember("currency-api", API->GetSchema());
	Schema().AddMember("currency-api.access-key", JsonSchema(json::value_t::string, "The API key to use"));

	LaunchArgs.RegisterSwitch("debug");

	AddRoute("/latest", web::http::verb::get, [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		std::error_code ec;
		if (query.Get("access_key") != CURRENCY_ACCESS_KEY)
		{
			con->set_status(web::http::status::unauthorized, ec);
			return;
		}

		const FCurrencyRates rates = API->GetAllRates(DefaultCurrency).RecalculateForBase(query.Get("base", DefaultCurrency));

		json response(json::value_t::object);
		response["success"] = !rates.Rates.empty();
		json ratesMap(json::value_t::object);
		for (const auto& [currency, rate] : rates.Rates) ratesMap[currency] = rate;
		response["rates"] = std::move(ratesMap);
		response["base"] = rates.BaseCurrency;
		response["timestamp"] = rates.LastUpdate;

		con->set_json_body(response, {}, ec);
		con->set_status(web::http::status::ok, ec);
	});
}

/* main init */
int TExchangeRateServerApp::Init(const std::vector<std::string>& args)
{
	if (!args.empty())
		SetParam("Config", args[0]);

	int ret = TApplication::Init(args);
	if (ret)
		return ret;

	if (LaunchArgs.HasSwitch("debug"))
		LogExchangeServer.DefaultFilter = LogVerbosityFilter::MoreSevere_Inclusive(Debug);

	return 0;
}

int TExchangeRateServerApp::LoadEnvironmentVariables()
{
	int res = TApplication::LoadEnvironmentVariables();
	if (res)
		return res;

	if (!GetParamFromCache("Config"))
		LoadParamFromEnvironmentOptional("CONFIGURATION", "Config", "exchangeRateServer.conf");
	LoadParamFromEnvironmentOptional("CONFIGURATION_USER", "UserConfig", {});

	const std::string conf = GetParam("Config")->Value();
	try
	{
		Log(Important, "Loading Exchange Rate Server configuration...");
		LoadPatchedConfiguration(conf, GetParam("UserConfig", { ParameterDomain::CACHE })->Value());
	}
	catch (const SchemaError& err)
	{
		Log(Critical, "Exchange Rate Server config does not conform to schema: %s", err.what());
		return -1;
	}
	catch (const ConfigError& err)
	{
		Log(Critical, "CONFIG FILE %s COULD NOT LOAD: %s", conf.c_str(), err.what());
		return -1;
	}

	return 0;
}

void TExchangeRateServerApp::Startup()
{
	TApplication::Startup();

	API->Update(DefaultCurrency);

	mCurrencyUpdateThread = std::thread([this]() {
		do {
			API->CheckForUpdates();
		} while (!mShouldQuit.try_acquire_for(std::chrono::minutes(1)));
	});

	Log(Info, "Starting server...");
	while (StartServer()) { std::this_thread::sleep_for(std::chrono::seconds(5)); }
}

int TExchangeRateServerApp::StartServer()
{
	if (!Initialize())
	{
		Log(Error, "Server initialization failed!");
		return -1;
	}

	// Start the server accept loop, can fail if port bind fails
	try
	{
		Start("exchange-srv");
		Log(Critical, "Server started on port %d.", Port());
	}
	catch (const websocketpp::exception& e)
	{
		Log(Error, "Server could not start on port %d: %s", Port(), e.what());
		return -1;
	}

	return 0;
}

void TExchangeRateServerApp::Destroy()
{
	mShouldQuit.release();
	if (mCurrencyUpdateThread.joinable())
		mCurrencyUpdateThread.join();

	Stop();

	TApplication::Destroy();
}

void TExchangeRateServerApp::OnConfigLoaded(const std::filesystem::path& file)
{
	WebServer::OnConfigLoaded(file);

	DefaultCurrency = GetConfig("default-currency").get<std::string>();

	API->LoadSubconfiguration(GetConfig("currency-api"));
	API->APIKey = GetConfig("currency-api.access-key").get<std::string>();
}
