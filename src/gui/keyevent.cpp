

/*
 * For comments regarding functions please see the header file.
 */

#include "gui/keyevent.hpp"

#include <algorithm>
#include <locale>

KeyEvent::KeyEvent(Widget* source, Modifiers modifiers, EKeyEventType type, const Key& key) : InputEvent(source, EInputEventType::Key, modifiers), mType(type), mKey(key)
{
}

KeyEvent::~KeyEvent() {}

EKeyEventType KeyEvent::getType() const
{
	return mType;
}

const Key& KeyEvent::getKey() const
{
	return mKey;
}

std::pair<SDL_Keycode, uint32_t> KeyEvent::resolveKeyAnd<PERSON>haracter() const
{
	return resolveKeyAndCharacter(mKey, modifiers());
}

std::pair<SDL_Keycode, uint32_t> KeyEvent::resolveKeyAnd<PERSON>haracter(const Key& key, const Modifiers& modifiers)
{
	switch (key.code())
	{
		case SDLK_KP_DIVIDE: return resolve<PERSON>ey<PERSON><PERSON><PERSON><PERSON><PERSON>(SDLK_SLASH, {}); break;
		case SDLK_KP_PLUS: return resolve<PERSON>ey<PERSON>nd<PERSON><PERSON>cter(SDLK_PLUS, {}); break;
		case SDLK_KP_MINUS: return resolveKeyAndCharacter(SDLK_MINUS, {}); break;
		case SDLK_KP_MULTIPLY: return resolveKeyAndCharacter(SDLK_ASTERISK, {}); break;
		case SDLK_KP_ENTER: return resolveKeyAndCharacter(SDLK_RETURN, {}); break;
		default: break;
	}

	SDL_Keycode keycode = key.code();
	uint32_t charcode = 0;
	if (key.isLetter())
	{
		const int diff = key.code() - SDLK_a;
		const bool shouldBeUppercase = modifiers.Shift ^ modifiers.CapsLock;
		charcode = (shouldBeUppercase ? 'A' : 'a') + diff;
	}
	else if (key.isCharacter())
	{
		charcode = keycode;
	}
	else if (key.code() >= SDLK_0 && key.code() <= SDLK_9)
	{
		if (modifiers.Shift)
		{
			switch (key.code())
			{
				case SDLK_1: charcode = '!'; break;
				case SDLK_2: charcode = '@'; break;
				case SDLK_3: charcode = '#'; break;
				case SDLK_4: charcode = '$'; break;
				case SDLK_5: charcode = '%'; break;
				case SDLK_6: charcode = '^'; break;
				case SDLK_7: charcode = '&'; break;
				case SDLK_8: charcode = '*'; break;
				case SDLK_9: charcode = '('; break;
				case SDLK_0: charcode = ')'; break;
				default: break;
			}
		}
		else
		{
			charcode = key.code();
		}
	}
	else if (key.isNumPad())
	{
		if (modifiers.NumLock)
		{
			if (key.code() >= SDLK_KP_1 && key.code() <= SDLK_KP_0)
			{
				charcode = (key.code() - SDLK_KP_1 + 1) % 10 + '0';
			}
			else if (key.code() == SDLK_KP_PERIOD)
			{
				charcode = std::use_facet<std::numpunct<char>>(std::locale()).decimal_point();
			}
		}
		else
		{
			switch (key.code())
			{
				case SDLK_KP_PERIOD: keycode = SDLK_DELETE; break;
				case SDLK_KP_0: keycode = SDLK_INSERT; break;
				case SDLK_KP_1: keycode = SDLK_END; break;
				case SDLK_KP_2: keycode = SDLK_DOWN; break;
				case SDLK_KP_3: keycode = SDLK_PAGEDOWN; break;
				case SDLK_KP_4: keycode = SDLK_LEFT; break;
				case SDLK_KP_6: keycode = SDLK_RIGHT; break;
				case SDLK_KP_7: keycode = SDLK_HOME; break;
				case SDLK_KP_8: keycode = SDLK_UP; break;
				case SDLK_KP_9: keycode = SDLK_PAGEUP; break;
				default: break;
			}
		}
	}

	return { keycode, charcode };
}
