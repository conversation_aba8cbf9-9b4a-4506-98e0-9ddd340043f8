/*
 * For comments regarding functions please see the header file.
 */

#include "gui/gui.hpp"

#include <MyUtils.h>

#include "gui/focushandler.hpp"
#include "gui/graphics.hpp"
#include "gui/guiexception.hpp"
#include "gui/input.hpp"
#include "gui/keyinput.hpp"
#include "gui/keylistener.hpp"
#include "gui/mouseinput.hpp"
#include "gui/mouselistener.hpp"
#include "gui/textinput.hpp"
#include "gui/widget.hpp"
#include "gui/widgets/window.hpp"

constexpr uint32_t MULTI_CLICK_INTERVAL = 300;    // the interval in milliseconds in which clicks are anderstood to be multi-cliekc (eg. double click)
Gui* Gui::pGui = nullptr;

Gui::Gui()
{
	pGui = this;
}

Gui::~Gui()
{
	if (mTopWindow)
	{
		mTopWindow->destroy();
		mTopWindow = NULL;
	}
}

Graphics* Gui::getGraphics() const
{
	return mGraphics.get();
}

void Gui::setInput(Input* input)
{
	mInput = input;
}

Input* Gui::getInput() const
{
	return mInput;
}

size_t Gui::handleInputs()
{
	size_t numInputEvents = 0;
	if (mInput)
	{
		numInputEvents += handleMouseInput();
		numInputEvents += handleKeyInput();
		numInputEvents += handleTextInput();
	}
	return numInputEvents;
}

void Gui::drawLogic(float deltaTime)
{
	handleModalFocus();
	handleModalMouseInputFocus();

	if (mTopWindow)
		mGraphics->execDrawLogic(mTopWindow, deltaTime);
}

void Gui::setTabbingEnabled(bool tabbing)
{
	bTabbing = tabbing;
}

bool Gui::isTabbingEnabled() const
{
	return bTabbing;
}

void Gui::tick(double dt)
{
	if (Graphics* gfx = getGraphics())
		gfx->incrementTime(dt);
	Animation.Tick(dt);
}

void Gui::addGlobalKeyListener(KeyListener* keyListener, bool bToFront)
{
	if (bToFront)
		mGlobalKeyListeners.push_front(keyListener);
	else
		mGlobalKeyListeners.push_back(keyListener);
}

void Gui::removeGlobalKeyListener(KeyListener* keyListener)
{
	mGlobalKeyListeners.remove(keyListener);
}

void Gui::addGlobalMouseListener(MouseListener* mouseListener, bool bToFront)
{
	if (bToFront)
		mGlobalMouseListeners.push_front(mouseListener);
	else
		mGlobalMouseListeners.push_back(mouseListener);
}

void Gui::removeGlobalMouseListener(MouseListener* mouseListener)
{
	mGlobalMouseListeners.remove(mouseListener);
}

size_t Gui::handleMouseInput()
{
	size_t numHandled = 0;
	while (!mInput->isMouseQueueEmpty())
	{
		numHandled++;
		MouseInput mouseInput(mInput->dequeueMouseInput());

		if (mouseInput.getType() == EMouseEventType::WHEEL_MOVED)
		{
			mouseInput.setPos(mLastMousePos);
		}
		else
		{
			// Save the current mouse state. It will be needed if modal focus
			// changes or modal mouse input focus changes.
			mLastMousePos = mouseInput.getPos();
		}

		if (EMouseEventType::RELEASED == mouseInput.getType() || (EMouseEventType::MOVED == mouseInput.getType() && mouseInput.buttonState()) ||
		    EMouseEventType::PRESSED == mouseInput.getType())
		{
			mLastPressedMousePos = mLastMousePos;
		}

		Widget* sourceWidget = getDraggedWidget();
		Widget* widgetUnderMouse = getMouseEventSource(mLastMousePos);

		if (widgetUnderMouse != sourceWidget)
			mClickCount = 0;

		if (!sourceWidget)
			sourceWidget = widgetUnderMouse;

		if (processMouseEvent(sourceWidget, mouseInput))
		{
			MouseEvent event(sourceWidget, mModifiers, mouseInput);
			event.mDown = mDownInfo;
			event.mClickCount = mClickCount;
			distributeMouseEventToGlobalMouseListeners(event);
			if (sourceWidget)
			{
				const Vector2D offset = sourceWidget->getAbsoluteDimension().Pos;
				event.mPos -= offset;
				event.mDown.DownPos -= offset;
			}
			distributeMouseEvent(event);
		}

		if (mouseInput.getType() == EMouseEventType::RELEASED && mouseInput.getButton() == EMouseEventButton::LEFT)
		{
			setDraggedWidget(NULL);
		}
	}
	return numHandled;
}

size_t Gui::handleKeyInput()
{
	size_t numHandled = 0;
	while (!mInput->isKeyQueueEmpty())
	{
		numHandled++;
		const KeyInput keyInput = mInput->dequeueKeyInput();

		// Save modifiers state
		mModifiers = keyInput.modifiers();

		KeyEvent keyEventToGlobalKeyListeners(NULL, mModifiers, keyInput.getType(), keyInput.getKey());

		distributeKeyEventToGlobalKeyListeners(keyEventToGlobalKeyListeners);

		// If a global key listener consumes the event it will not be
		// sent further to the source of the event.
		if (keyEventToGlobalKeyListeners.isConsumed())
			continue;

		// Send key inputs to the focused widgets
		Widget* src = getKeyEventSource();
		if (!src)
			continue;

		if (getModalFocused() && !src->hasModalFocus())
			continue;

		KeyEvent keyEvent(src, mModifiers, keyInput.getType(), keyInput.getKey());

		distributeKeyEvent(keyEvent);

		// If the key event hasn't been handled and tabbing is enabled check for tab press and change focus.
		if (keyEvent.consumedState() != EInputConsumedState::Handled && bTabbing && keyInput.getKey().code() == SDLK_TAB && keyInput.getType() == EKeyEventType::PRESSED)
		{
			if (mModifiers.Shift)
				tabPrevious(getMainWindow());
			else
				tabNext(getMainWindow());
		}
	}
	return numHandled;
}

size_t Gui::handleTextInput()
{
	size_t numHandled = 0;
	while (!mInput->isTextQueueEmpty())
	{
		numHandled++;
		const TextInput txtInput = mInput->dequeueTextInput();

		// Send text inputs to the focused widgets
		Widget* src = getKeyEventSource();
		if (!src)
			continue;

		if (getModalFocused() && !src->hasModalFocus())
			continue;

		TextEvent txtEvent(src, mModifiers, txtInput.getText());

		distributeKeyEvent(txtEvent);
	}
	return numHandled;
}

void Gui::handleMousePressed(Widget* source, const MouseInput& in)
{
	if ((mLastMousePressTimeStamp + MULTI_CLICK_INTERVAL) < in.getTimeStamp() || mLastMousePressButton != in.getButton() ||
	    getLastWidgetPressed() != source)    // if anything about the button press is different, or if enough time elapsed, reset the click counter
		mClickCount = 0;

	mLastMousePressTimeStamp = in.getTimeStamp();

	setLastWidgetPressed(source);
	mClickCount++;

	Widget* focusW = source;
	if (getModalFocused() && !focusW->hasModalFocus())
	{
		if (focusW->getFocusMode() != EFocusMode::Ignore)
			focusNone();
	}
	else
	{
		while (focusW && !focusW->isFocused() && (!getModalFocused() || focusW->hasModalFocus()))
		{
			focusW->requestFocus();
			if (focusW->isFocused())
				break;
			focusW = focusW->getParent();
		}
	}

	if (in.getButton() == EMouseEventButton::LEFT)
		setDraggedWidget(source);

	mLastMousePressButton = in.getButton();
}

Widget* Gui::getWidgetAt(const Vector2D& pos, const WidgetEventPropagationFunction& f) const
{
	if (!mTopWindow)
		return NULL;
	return mTopWindow->getWidgetAt(pos, f);
}

EventPropagateMode mouseEventSelector(const Widget* w)
{
	if (!w)
		return EventPropagateMode::ONLY_TO_CHILDREN;

	bool bReceive;
	switch (w->getMouseEventsCondition())
	{
		case Widget::EventPropagateCondition::ALWAYS: bReceive = true; break;
		case Widget::EventPropagateCondition::ENABLED: bReceive = w->isEnabled(); break;
		case Widget::EventPropagateCondition::VISIBLE: bReceive = w->getEffectiveVisiblity(false); break;
		case Widget::EventPropagateCondition::ENABLED_AND_VISIBLE: bReceive = w->isEnabled() && w->getEffectiveVisiblity(false); break;
		case Widget::EventPropagateCondition::NEVER: bReceive = false; break;
		default: bReceive = false;
	}
	return bReceive ? w->mouseInput() : EventPropagateMode::NONE;
}

EventPropagateMode keyEventSelector(const Widget* w)
{
	bool bReceive;
	switch (w->getKeyEventsCondition())
	{
		case Widget::EventPropagateCondition::ALWAYS: bReceive = true; break;
		case Widget::EventPropagateCondition::ENABLED: bReceive = w->isEnabled(); break;
		case Widget::EventPropagateCondition::VISIBLE: bReceive = w->isVisible(); break;
		case Widget::EventPropagateCondition::ENABLED_AND_VISIBLE: bReceive = w->isEnabled() && w->isVisible(); break;
		case Widget::EventPropagateCondition::NEVER: bReceive = false; break;
		default: bReceive = false;
	}
	return bReceive ? w->keyInput() : EventPropagateMode::NONE;
}

Widget* Gui::getMouseEventSource(const Vector2D& pos) const
{
	const Graphics::DrawnWidgetArea element = getGraphics()->queryDrawStack(&mouseEventSelector, pos);
	if (getModalMouseInputFocused() && (!element.Owner || !element.Owner->hasModalMouseInputFocus()))
		return getModalMouseInputFocused();

	return element.Owner;
}

Widget* Gui::getKeyEventSource()
{
	Widget* widget = getFocused();
	if (!widget)
		widget = getModalFocused();

	return widget;
}

void Gui::reCheckMouseWidgets(Widget* mouseOverWidget)
{
	std::map<Widget*, MouseOverInfo> PrevMouseWidgets;
	mWidgetsWithMouse.swap(PrevMouseWidgets);

	const Uint64 time = SDL_GetTicks64();
	if (mouseOverWidget)
	{
		const bool bIsSourceWidgetDragged = getDraggedWidget() == mouseOverWidget;
		Rectangle activeArea(mouseOverWidget->getAbsoluteDimension());
		size_t depth = 0;
		for (Widget* w = mouseOverWidget; w && (!getModalMouseInputFocused() || w->hasModalMouseInputFocus()); w = w->getParent(), depth++)
		{
			activeArea.Size = w->getSize();

			const bool bContainsMouse = activeArea.isPointInRect(mLastMousePos) || bIsSourceWidgetDragged;
			const bool bWidgetHadMouse = PrevMouseWidgets.erase(w);
			if (bContainsMouse != bWidgetHadMouse)
			{
				MouseEvent ev(bContainsMouse ? w : mouseOverWidget, mModifiers);
				ev.mDown = mDownInfo;
				ev.mType = bContainsMouse ? EMouseEventType::ENTERED : EMouseEventType::EXITED;
				ev.mTimestamp = time;
				ev.mPos = mLastMousePos;
				distributeMouseEventToGlobalMouseListeners(ev);
				ev.mPos -= activeArea.Pos;
				ev.mDown.DownPos -= activeArea.Pos;
				distributeMouseEvent(ev, true, true);
			}

			if (bContainsMouse)
				mWidgetsWithMouse.try_emplace(w, w->OnDeath.bind([this](Widget* w) { mWidgetsWithMouse.erase(w); }), depth);

			activeArea += -w->getDimension().Pos;
		}
	}

	// leftover widgets that weren't erased no longer have mouse, so call the exited event
	for (const auto& [widget, deathDelegate] : PrevMouseWidgets)
	{
		MouseEvent ev(widget, mModifiers);
		const Vector2D offset = widget->getAbsoluteDimension().Pos;
		ev.mPos = mLastMousePos;
		ev.mDown = mDownInfo;
		ev.mType = EMouseEventType::EXITED;
		ev.mTimestamp = time;
		distributeMouseEventToGlobalMouseListeners(ev);
		ev.mPos -= offset;
		ev.mDown.DownPos -= offset;
		distributeMouseEvent(ev, true, true);
	}
}

void Gui::updateSpeed(const MouseInput& in)
{
	// smoothing over 150ms
	constexpr float SmoothingDuration = 150;
	while (!BacklogTimestamp.empty() && (in.getTimeStamp() - BacklogTimestamp.front() > SmoothingDuration)) BacklogTimestamp.pop();

	const float interp = BacklogTimestamp.empty() ? 1.f : std::pow((in.getTimeStamp() - BacklogTimestamp.back()) / SmoothingDuration, 0.5f);
	const float timeDiff = 1e-3f * std::max(1L, (long)(in.getTimeStamp() - PrevSwipeTimestamp));
	SmoothSpeed = SmoothSpeed * (1.f - interp) + interp * in.getMove() / timeDiff;
	BacklogTimestamp.push(in.getTimeStamp());
}

GuiWindow* Gui::getMainWindow() const
{
	return mTopWindow;
}

void Gui::setDrawTarget(GPU_Target* target)
{
	mGraphics = std::make_unique<Graphics>();
	mGraphics->setTarget(target);
}

const Vector2D& Gui::getLastMousePosition(bool bPressed) const
{
	return bPressed ? mLastPressedMousePos : mLastMousePos;
}

bool Gui::processMouseEvent(Widget* source, MouseInput& in)
{
	if (!source)
	{
		// Check if the mouse leaves the application window.

		if (PrevSwipeTimestamp)
		{
			if (in.getTimeStamp() > PrevSwipeTimestamp)
				updateSpeed(in);

			mDownInfo.update(in.getMove(), SmoothSpeed, in.getTimeStamp() - PrevSwipeTimestamp);
			PrevSwipeTimestamp = 0;
		}

		reCheckMouseWidgets(NULL);

		MouseEvent event(NULL, mModifiers);
		event.mType = EMouseEventType::EXITED;
		event.mButtonState = in.buttonState();
		event.mDown = mDownInfo;
		distributeMouseEventToGlobalMouseListeners(event);

		return false;
	}

	if (in.getType() == EMouseEventType::PRESSED)
	{
		if (in.getButton() == EMouseEventButton::LEFT)
		{
			mDownInfo = FMouseDownInfo(in.getPos(), in.getTimeStamp());
			PrevSwipeTimestamp = in.getTimeStamp();
			SmoothSpeed = Vector2D();
		}

		handleMousePressed(source, in);
	}
	else if (in.getType() == EMouseEventType::MOVED)
	{
		if (in.isPressed(EMouseEventButton::LEFT))
		{
			updateSpeed(in);

			mDownInfo.update(in.getMove(), SmoothSpeed, in.getTimeStamp() - PrevSwipeTimestamp);
			PrevSwipeTimestamp = in.getTimeStamp();
		}

		reCheckMouseWidgets(source);
	}
	else if (in.getType() == EMouseEventType::RELEASED)
	{
		if (in.getButton() == EMouseEventButton::LEFT && PrevSwipeTimestamp)
		{
			if (in.getTimeStamp() > PrevSwipeTimestamp)
				updateSpeed(in);

			mDownInfo.update(in.getMove(), SmoothSpeed, in.getTimeStamp() - PrevSwipeTimestamp);
			PrevSwipeTimestamp = 0;
		}

		if (in.getButton() != mLastMousePressButton || getLastWidgetPressed() != source || (mDownInfo.DownPos - in.getPos()).length() > MAX_CLICK_DISTANCE)
		{
			mLastMousePressButton = EMouseEventButton::NONE;
			mClickCount = 0;
		}
		setLastWidgetPressed(NULL);
	}

	return true;
}

constexpr bool WantsEvents(EventPropagateMode mode)
{
	return mode == EventPropagateMode::NORMAL || mode == EventPropagateMode::NO_CHILDREN;
}

void Gui::distributeMouseEvent(MouseEvent& event, bool force, bool toSourceOnly)
{
	if (getModalFocused() && !event.getSource()->hasModalFocus() && !force)
	{
		return;
	}

	if (getModalMouseInputFocused() && !event.getSource()->hasModalMouseInputFocus() && !force)
	{
		return;
	}

	for (Widget* w = event.getSource(); w && !event.isConsumed(); w = w->getParent())
	{
		const EventPropagateMode thisMode = mouseEventSelector(w);
		if (WantsEvents(thisMode))
		{
			if (event.getType() == EMouseEventType::RELEASED && event.clickCount())
				w->OnClicked(w, event.pos());

			// Send the event to all mouse listeners of the widget.
			for (MouseListener* listener : w->_getMouseListeners())
			{
				if (listener)
					sendMouseEventToListener(*listener, event);

				if (event.isConsumed())
					break;
			}
		}

		if (w->consumesAllMouseEvents())
			event.consume(EInputConsumedState::PropagationStopped);

		if (toSourceOnly || event.isConsumed())
			break;

		// If a non modal focused widget has been reach
		// and we have modal focus cancel the distribution.
		if (getModalFocused() && !w->hasModalFocus())
		{
			break;
		}

		// If a non modal mouse input focused widget has been reach
		// and we have modal mouse input focus cancel the distribution.
		if (getModalMouseInputFocused() && !w->hasModalMouseInputFocus())
		{
			break;
		}

		const Vector2D shift = w->getDimension().Pos;
		event.mPos += shift;
		event.mDown.DownPos += shift;
	}
}

Widget* Gui::distributeKeyEvent(InputEvent& keyEvent)
{
	Widget* sourceWidget = keyEvent.getSource();
	if (!sourceWidget)
		return NULL;

	Widget* w = sourceWidget;
	for (; w && !keyEvent.isConsumed(); w = w->getParent())
	{
		// If a non modal focused widget has been reached and we have modal focus cancel the distribution.
		if (getModalFocused() && !w->hasModalFocus())
			return NULL;

		const EventPropagateMode thisMode = keyEventSelector(w);
		if (WantsEvents(thisMode))
		{
			// Send the event to all key listeners of the source widget.
			for (KeyListener* listener : w->_getKeyListeners())
			{
				if (!listener)
					continue;

				if (keyEvent.getInputType() == EInputEventType::Key)
				{
					sendKeyEventToListener(*listener, dynamic_cast<KeyEvent&>(keyEvent));
				}
				else if (keyEvent.getInputType() == EInputEventType::Text)
				{
					listener->onTextEvent(dynamic_cast<TextEvent&>(keyEvent));
				}
				else
				{
					throw GuiException("Invalid key event type.");
				}

				if (keyEvent.isConsumed())
					break;
			}
		}

		if (w->consumesAllKeyEvents())
		{
			keyEvent.consume(EInputConsumedState::PropagationStopped);
			break;
		}
	}

	return w;
}

void Gui::distributeKeyEventToGlobalKeyListeners(KeyEvent& keyEvent)
{
	for (KeyListener* listener : mGlobalKeyListeners)
	{
		if (listener)
			sendKeyEventToListener(*listener, keyEvent);

		if (keyEvent.isConsumed())
		{
			break;
		}
	}
}

void Gui::distributeMouseEventToGlobalMouseListeners(MouseEvent& mouseEvent)
{
	for (MouseListener* listener : mGlobalMouseListeners)
	{
		if (listener)
			sendMouseEventToListener(*listener, mouseEvent);

		if (mouseEvent.isConsumed())
			break;
	}
}

void Gui::handleModalMouseInputFocus()
{
	if (getLastWidgetWithModalMouseInputFocus() != getModalMouseInputFocused())
	{
		if (getLastWidgetWithModalMouseInputFocus())    // Check if modal mouse input focus has been released.
		{
			handleModalFocusReleased();
			setLastWidgetWithModalMouseInputFocus(NULL);
		}
		else    // Check if modal mouse input focus has been gained by a widget.
		{
			handleModalFocusGained();
			setLastWidgetWithModalMouseInputFocus(getModalMouseInputFocused());
		}
	}
}

void Gui::handleModalFocus()
{
	if (getLastWidgetWithModalFocus() != getModalFocused())
	{
		if (getLastWidgetWithModalFocus())    // Check if modal focus has been released.
		{
			handleModalFocusReleased();
			setLastWidgetWithModalFocus(NULL);
		}
		else    // Check if modal focus has been gained by a widget.
		{
			handleModalFocusGained();
			setLastWidgetWithModalFocus(getModalFocused());
		}
	}
}

void Gui::handleModalFocusGained()
{
	reCheckMouseWidgets(getMouseEventSource(mLastMousePos));

	setLastWidgetWithModalMouseInputFocus(getModalMouseInputFocused());
}

void Gui::handleModalFocusReleased()
{
	reCheckMouseWidgets(getMouseEventSource(mLastMousePos));
}

void Gui::sendMouseEventToListener(MouseListener& listener, MouseEvent& mouseEvent) const
{
	switch (mouseEvent.getType())
	{
		case EMouseEventType::ENTERED: listener.mouseEntered(mouseEvent); break;
		case EMouseEventType::EXITED: listener.mouseExited(mouseEvent); break;
		case EMouseEventType::MOVED:
			listener.mouseMoved(mouseEvent);
			if (getDraggedWidget())
				listener.mouseDragged(mouseEvent);
			break;
		case EMouseEventType::PRESSED: listener.mousePressed(mouseEvent); break;
		case EMouseEventType::RELEASED:
			listener.mouseReleased(mouseEvent);
			if (mouseEvent.clickCount())
				listener.mouseClicked(mouseEvent);
			break;
		case EMouseEventType::WHEEL_MOVED: listener.mouseWheelMoved(mouseEvent); break;
		default: throw GuiException("Unknown mouse event type.");
	}

	listener.onMouseEvent(mouseEvent);
}

void Gui::sendKeyEventToListener(KeyListener& listener, KeyEvent& keyEvent) const
{
	switch (keyEvent.getType())
	{
		case EKeyEventType::PRESSED: listener.keyPressed(keyEvent); break;
		case EKeyEventType::RELEASED: listener.keyReleased(keyEvent); break;
		default: throw GuiException("Unknown key event type.");
	}

	listener.onKeyEvent(keyEvent);
}

void Gui::setMainWindow(GuiWindow* window)
{
	mTopWindow = window;
}

std::optional<size_t> Gui::getHoverDepth(Widget* w) const
{
	auto find = mWidgetsWithMouse.find(w);
	if (find != mWidgetsWithMouse.end())
		return find->second.Depth;

	return {};
}
