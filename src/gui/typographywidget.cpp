#include "gui/typographywidget.hpp"

TypographyWidget::TypographyWidget()
{
	TextColor.OnChanged += [this](LinearColor oldVal, LinearColor newVal) {
		redrawRequired();
	};
	mTypography.OnChanged += [this](Typography oldVal, Typography newVal) {
		mCurrentTypography.Font = FontLibrary.Get(newVal);
		mCurrentTypography.LetterSpacing = newVal.letterSpacing;
		mCurrentTypography.LineHeight = newVal.lineHeight;
		redrawRequired();
	};
}

const LoadedTypography& TypographyWidget::getLoadedTypography() const
{
	return mCurrentTypography;
}

const ColorProperty& TypographyWidget::getTextColor() const
{
	return TextColor;
}
