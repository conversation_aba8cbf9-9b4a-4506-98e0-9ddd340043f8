#pragma once

#include <string>

#include "gui/brush.hpp"

namespace shaders
{

const std::string MaxGradientStopsDefine = "MAX_GRADIENT_STOPS";

const std::string SmoothCircle =
  R"(
uniform float feather;

in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
	float circleMask = distance(texCoord, vec2(0.5)) * 2.0;
	circleMask = smoothstep(1.0, 1.0 - feather, circleMask);
    fragColor = BRUSH_0(texCoord);
	fragColor.a *= circleMask;
}
)";

const std::string RoundedRect =
  R"(
uniform float uFeather;
uniform vec4 uRadius;

in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
	float rectMask = roundedRectMask((texCoord - vec2(0.5)) * uSize, uSize - vec2(2.0 * uFeather), uRadius, uFeather);

    vec4 innerColor = BRUSH_0(texCoord);
    innerColor.a *= rectMask;

    fragColor = innerColor;
}
)";

const std::string RoundedRectWithBorder =
  R"(
uniform float uFeather;
uniform vec4 uRadius;
uniform vec4 uBorderWidth;

in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
    vec2 rectMask = roundedRectWithBorderMask(texCoord * uSize, uSize * 0.5, uSize, uRadius, uFeather, uBorderWidth);
	float outerRectMask = rectMask.x;
    float innerRectMask = rectMask.y;

    vec4 innerColor = BRUSH_0(texCoord);
    vec4 outerColor = BRUSH_1(texCoord);
    outerColor.a *= (1.0 - innerRectMask);

    fragColor = colorBlend(outerColor, innerColor);
    fragColor.a *= outerRectMask;
}
)";

const std::string ShaderLibrary =
  R"(
uniform vec2 uSize;
const float Pi = 3.14159265359;

vec4 colorBlend(vec4 a, vec4 b)
{
    // performs a straight alpha "over" operation (A over B)
    vec4 outColor;
    outColor.a = a.a + b.a * (1.0 - a.a);
    outColor.rgb = (a.rgb * a.a + b.rgb * b.a * (1.0 - a.a)) / outColor.a;
	return outColor;
}

vec4 colorBlend_Premultiplied(vec4 a, vec4 b)
{
    return a + b * (1.0 - a.a);
}

vec4 gradient(float fragmentPosAlongGradient, vec4 colors[MAX_GRADIENT_STOPS], float stops[MAX_GRADIENT_STOPS], int numStops, float rescaleFactor)
{
  float stopPositions[MAX_GRADIENT_STOPS + 1];
  int lerpEndIndex = 0;
  for (int stopIdx = 0; stopIdx < numStops; stopIdx++)
  {
    stopPositions[stopIdx] = stops[stopIdx] * rescaleFactor;
    lerpEndIndex += int(step(stopPositions[stopIdx], fragmentPosAlongGradient));
  }
  stopPositions[numStops] = stopPositions[numStops - 1];

  int lerpStartIndex = max(lerpEndIndex - 1, 0);
  float distanceBetweenStops = stopPositions[lerpEndIndex] - stopPositions[lerpStartIndex];
  float lerp_alpha = (fragmentPosAlongGradient - stopPositions[lerpStartIndex]) / distanceBetweenStops;
  return mix(mix(colors[lerpStartIndex], colors[lerpEndIndex], clamp(lerp_alpha, 0.0, 1.0)), colors[lerpStartIndex], step(1.0, 1.0 - distanceBetweenStops));
}

vec4 gradient2D(vec2 uv, vec2 from, vec2 to, vec4 colors[MAX_GRADIENT_STOPS], float stops[MAX_GRADIENT_STOPS], int numStops)
{
  vec2 gradVector = to - from;
  float fragmentPos = dot(gradVector, uv - from);
  return gradient(fragmentPos, colors, stops, numStops, dot(gradVector, gradVector));
}

vec4 gradientRadial(vec2 uv, vec2 origin, float from, float to, vec4 colors[MAX_GRADIENT_STOPS], float stops[MAX_GRADIENT_STOPS], int numStops)
{
  vec2 radialOut = uv - origin;
  float angle = atan(radialOut.x, radialOut.y) + Pi;
  angle = clamp((angle - from) / (to - from), 0.0, 1.0);
  return gradient(angle, colors, stops, numStops, 1.0);
}

// atan with stable behavior in the entire function domain
float atan2(in float y, in float x)
{
    return mix(atan(y,x), Pi/2.0 - atan(x,y), step(y, x));
}

// returns the quadrant idx, starting with 0 for the top left and going clockwise
int getQuadrantIdx(vec2 pos)
{
    float domain = fract((atan2(pos.y, pos.x) + Pi) / (2.0 * Pi));
    return int(domain * 4.0);
}

float distanceToRoundedBox(vec2 distFromCenter, vec2 rectSize, float cornerRadius)
{
    return length(max(abs(distFromCenter) - rectSize * 0.5 + cornerRadius, 0.0)) - cornerRadius;
}

float roundedRectMask(vec2 distFromCenter, vec2 rectSize, vec4 cornerRadius, float feather)
{
    int cornerIdx = getQuadrantIdx(distFromCenter);
    float edgeDistance = distanceToRoundedBox(distFromCenter, rectSize, cornerRadius[cornerIdx]);

    float smoothedMask = 1.0 - smoothstep(0.0, feather, edgeDistance);

    return smoothedMask;
}

vec2 roundedRectWithBorderMask(vec2 uv, vec2 center, vec2 rectSize, vec4 cornerRadius, float feather, vec4 border)
{
  float mask = roundedRectMask(uv - center, rectSize - vec2(2.0 * feather), cornerRadius, feather);

  vec4 innerCornerRadius = vec4(cornerRadius.x - max(border.x, border.y), cornerRadius.y - max(border.y, border.z), cornerRadius.z - max(border.z, border.w), cornerRadius.w - max(border.w, border.x));
  innerCornerRadius = max(innerCornerRadius, vec4(0.0));

  vec2 innerRectCenter = center + (border.xy - border.zw) * 0.5;
  vec2 innerRectSize = rectSize - (border.xy + border.zw) - vec2(4.0 * feather);
  float innerRectMask = roundedRectMask(uv - innerRectCenter, innerRectSize, innerCornerRadius, feather);

  vec2 ret;
  ret.x = mask;
  ret.y = innerRectMask;
  return ret;
}

vec2 uvClampMirror(vec2 uv, vec4 clampUV)
{
  uv.x = mix(uv.x, 2.0 * clampUV.z - uv.x, step(clampUV.z, uv.x));
  uv.x = mix(2.0 * clampUV.x - uv.x, uv.x, step(clampUV.x, uv.x));
  uv.y = mix(uv.y, 2.0 * clampUV.w - uv.y, step(clampUV.w, uv.y));
  uv.y = mix(2.0 * clampUV.y - uv.y, uv.y, step(clampUV.y, uv.y));
  return uv;
}

vec2 uvClamp(vec2 uv, vec4 clampUV)
{
  return clamp(uv, clampUV.xy, clampUV.zw);
}

)";

const std::array<std::string, EBrushType::_size()> BrushShaders = { {

  // Empty (uses Color brush type with transparent color)
  {},

  // Color (uses vec4[color] for brush type)
  R"(
struct Brush_Color {
  vec4 Color;
};
)",

  // LinearGradient
  R"(
struct Brush_LinearGradient {
  vec4 Colors[MAX_GRADIENT_STOPS];
  float Stops[MAX_GRADIENT_STOPS];
  vec2 From;
  vec2 To;
  int NumColors;
};
vec4 paintFragmentWithBrush(vec2 uv, Brush_LinearGradient brush)
{
  return gradient2D(uv, brush.From, brush.To, brush.Colors, brush.Stops, brush.NumColors);
}
)",

  // RadialGradient
  R"(
struct Brush_RadialGradient {
  vec4 Colors[MAX_GRADIENT_STOPS];
  float Stops[MAX_GRADIENT_STOPS];
  vec2 Origin;
  float From;
  float To;
  int NumColors;
};
vec4 paintFragmentWithBrush(vec2 uv, Brush_RadialGradient brush)
{
  return gradientRadial(uv, brush.Origin, brush.From, brush.To, brush.Colors, brush.Stops, brush.NumColors);
}
)",

  // Image
  R"(
struct Brush_Image {
  vec4 SourceRect;
  mat4 ColorMatrix;
  vec2 UVTiling;
};
vec4 paintImage(vec2 uv, Brush_Image brush, sampler2D image, vec2 canvasSize)
{
	vec2 isNotTiling = step(brush.UVTiling, vec2(0.0));
	vec2 imageSamplePos = uv * mix(canvasSize / mix(brush.UVTiling, brush.SourceRect.zw, isNotTiling), vec2(1.0), isNotTiling);
	return brush.ColorMatrix * texture(image, brush.SourceRect.xy + fract(imageSamplePos) * brush.SourceRect.zw);
}
)"

} };

const std::string FillWithBrush =
  R"(
in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
	fragColor = BRUSH_0(texCoord);
}
)";

const std::string MSDF =
  R"(
uniform sampler2D glyphs;
uniform vec4 fgColor;
uniform vec4 atlasBounds;
uniform float distanceFieldScale;

in vec2 texCoord;

out vec4 fragColor;

float median(float r, float g, float b) {
    return max(min(r, g), min(max(r, g), b));
}

void main(void)
{
    vec4 thisGlyph = texture(glyphs, (atlasBounds.xy + texCoord * atlasBounds.zw));

    float sd = median(thisGlyph.r, thisGlyph.g, thisGlyph.b);
    float screenPxDistance = distanceFieldScale*(sd - 0.5);
    float opacity = clamp(screenPxDistance + 0.5, 0.0, 1.0);

    fragColor = mix(fragColor, fgColor, opacity);
}
)";

const std::string BoxShadow = R"(
uniform vec4 uCornerRadius;
uniform vec4 uColor;
uniform float uBlurRadius;

// A standard gaussian function, used for weighting samples
const float Sqrt2Pi = sqrt(2.0 * Pi);
float gaussian(float x, float sigma) {
    return exp(-(x * x) / (2.0 * sigma * sigma)) / (Sqrt2Pi * sigma);
}

// This approximates the error function, needed for the gaussian integral
vec2 erf(vec2 x) {
    vec2 s = sign(x), a = abs(x);
    x = 1.0 + (0.278393 + (0.230389 + 0.078108 * (a * a)) * a) * a;
    x *= x;
    return s - s / (x * x);
}

// Return the blurred mask along the x dimension
float roundedBoxShadowX(float x, float y, float sigma, float corner, vec2 halfSize) {
    float delta = min(halfSize.y - corner - abs(y), 0.0);
    float curved = halfSize.x - corner + sqrt(max(0.0, corner * corner - delta * delta));
    vec2 integral = 0.5 + 0.5 * erf((x + vec2(-curved, curved)) * (sqrt(0.5) / sigma));
    return integral.y - integral.x;
}

// Return the mask for the shadow of a box from lower to upper
float roundedBoxShadow(vec2 lower, vec2 upper, vec2 point, float sigma, float corner) {
    // Center everything to make the math easier
    vec2 center = (lower + upper) * 0.5;
    vec2 halfSize = (upper - lower) * 0.5;
    point -= center;

    // The signal is only non-zero in a limited range, so don't waste samples
    float low = point.y - halfSize.y;
    float high = point.y + halfSize.y;
    float start = clamp(-3.0 * sigma, low, high);
    float end = clamp(3.0 * sigma, low, high);

    // Accumulate samples (we can get away with surprisingly few samples)
    float stepSize = (end - start) / 4.0;
    float y = start + stepSize * 0.5;
    float value = 0.0;
    for (int i = 0; i < 4; i++) {
      value += roundedBoxShadowX(point.x, point.y - y, sigma, corner, halfSize) * gaussian(y, sigma) * stepSize;
      y += stepSize;
    }

    return value;
}

in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
    float sigma = uBlurRadius / 2.0;
    vec2 boxOffset = vec2(3.0*sigma); // must match inflation of box when this shader is called!

    int cornerIdx = getQuadrantIdx(texCoord - vec2(0.5));
    float mask = roundedBoxShadow(boxOffset, uSize - boxOffset, texCoord * uSize, sigma, uCornerRadius[cornerIdx]);

    fragColor = uColor;
    fragColor.a *= mask;
}

)";

const std::string ColorSwizzle = R"(
uniform mat4 swizzleMatrix;
uniform sampler2D tex;

in vec2 texCoord;
in vec4 color;

out vec4 fragColor;

void main(void)
{
    vec4 originalColor = texture(tex, texCoord) * color;
	fragColor = vec4(swizzleMatrix * originalColor);
}
)";

const std::string GaussianBlur = R"(
uniform int uSupport;
uniform vec2 uGaussCoefficients;
uniform vec2 uBlurDirection;

in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
    vec4 uvBounds = vec4(vec2(0.5) / uSize, uSize - vec2(0.5) / uSize);
    vec4 original_color = SAMPLE_uTexture(uvClamp(texCoord, uvBounds));

    vec3 gauss_coefficient = vec3(uGaussCoefficients, uGaussCoefficients.y * uGaussCoefficients.y);

    vec4 avg_color = original_color * gauss_coefficient.x;
    for (int i = 1; i <= uSupport; i += 2) {
        gauss_coefficient.xy *= gauss_coefficient.yz;

        float gauss_coefficient_subtotal = gauss_coefficient.x;
        gauss_coefficient.xy *= gauss_coefficient.yz;
        gauss_coefficient_subtotal += gauss_coefficient.x;
        float gauss_ratio = gauss_coefficient.x / gauss_coefficient_subtotal;

        vec2 offset = uBlurDirection * (float(i) + gauss_ratio);

        vec2 st0 = uvClamp(texCoord - offset, uvBounds);
        vec2 st1 = uvClamp(texCoord + offset, uvBounds);
        avg_color += (SAMPLE_uTexture(st0) + SAMPLE_uTexture(st1)) * gauss_coefficient_subtotal;
    }

    // premultiplied alpha reverse operation
    //if (avg_color.a > 0.0)
	//    avg_color.rgb /= avg_color.a;

  	fragColor = avg_color;
  	// fragColor = vec4(mix(vec3(1.0), vec3(1.0, 0.0, 0.0), avg_color.a), 1.0);
  	// fragColor = vec4(1.0,0.0,0.0,avg_color.r);
}
)";

const std::string Grayscale = R"(
in vec2 texCoord;

out vec4 fragColor;

void main(void)
{
	vec4 color = SAMPLE_uTexture(texCoord);
	float gray = dot(color.rgb, vec3(0.299, 0.587, 0.114));
	gray = pow(gray, 0.7);
	fragColor = vec4(vec3(gray), color.a);
})";

};    // namespace shaders