/*
 * For comments regarding functions please see the header file.
 */

#include "gui/image.hpp"

#include <MyUtils.h>

#include "TGuiApplication.h"
#include "gui/guiexception.hpp"

ImagePtr Image::GetImage(const std::string& Name, GPU_BlendPresetEnum blend_mode, bool bEnableAlphaBlending)
{
	return pGuiApp->GUI()->AssetLoader->Get(Name, blend_mode, bEnableAlphaBlending);
}

Image::Image(GPU_Image* image, bool autoFree, const std::filesystem::path& location) : Location(location)
{
	bAutoFree = autoFree;
	mTexture = image;
}

Image::~Image()
{
	if (bAutoFree)
		free();
}

GPU_Image* Image::getTexture() const
{
	return mTexture;
}

void Image::setTexture(GPU_Image* texture)
{
	if (texture == mTexture)
		return;
	if (bAutoFree)
		free();
	mTexture = texture;
}

int Image::getWidth() const
{
	if (!mTexture)
		throw GuiException("Trying to get the width of a non loaded image.");

	return mTexture->w;
}

int Image::getHeight() const
{
	if (!mTexture)
		throw GuiException("Trying to get the height of a non loaded image.");

	return mTexture->h;
}

iVector2D Image::getSize() const
{
	if (!mTexture)
		throw GuiException("Trying to get the size of a non loaded image.");

	return iVector2D(mTexture->w, mTexture->h);
}

void Image::free()
{
	if (mTexture)
	{
		THREAD_CHECK(rtfwk_sdl2::THREAD_DRAW);
		GPU_FreeImage(mTexture);
		mTexture = nullptr;
	}
}

ImagePtr Image::copy() const
{
	THREAD_CHECK(rtfwk_sdl2::THREAD_DRAW);
	if (mTexture)
	{
		return std::make_shared<Image>(GPU_CopyImage(mTexture), true);
	}

	return NULL;
}

void Image::setAlphaBlend(bool bBlend)
{
	GPU_SetBlending(mTexture, bBlend);
}

bool Image::getAlphaBlend() const
{
	return GPU_GetBlending(mTexture);
}

bool Image::isValid() const
{
	return mTexture && mTexture->w > 0 && mTexture->h > 0;
}