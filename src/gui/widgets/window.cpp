
/*
 * For comments regarding functions please see the header file.
 */

#include "gui/widgets/window.hpp"

#include "gui/focushandler.hpp"
#include "gui/graphics.hpp"

GuiWindow::GuiWindow(float headerHeight, const Rectangle& padding)
{
	Background = Color::Gray(32);
	OutlineStyle = Outline(Color::Gray(48), 5_px, 5_px);

	setConsumeAllMouseEvents(true);
	addMouseListener(this);

	mTitleLabel = new Label();
	mTitleLabel->setId("_windowTitle");
	mTitleLabel->setResizeMode(Label::ResizeMode::NONE);
	mTitleLabel->setHeight(headerHeight);
	mTitleLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 24_px });
	add(mTitleLabel);

	mTitleLabel->addMouseListener(this);
	OutlineStyle.AddModifier([this](Outline& outline) { outline.BorderWidth[1] = Dimension(mTitleLabel->getHeight(), EDimensionUnit::px); });

	BoxLayout* windowLayout = setLayout<BoxLayout>();
	windowLayout->DefaultBehavior.Alignment = ChildAlignment2D(EChildAlignment::Stretch);
	windowLayout->PerWidgetBehaviors[mTitleLabel].Alignment = ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Min);

	setTitleBarHeight(headerHeight);
	setPadding(padding);
}

Label* GuiWindow::getTitle() const
{
	return mTitleLabel;
}

void GuiWindow::mouseDragged(MouseEvent& mouseEvent)
{
	if (isMovable() && Gui::Get()->getDraggedWidget())
	{
		setFloating(true);
		setPosition(getPosition() + mouseEvent.relMovement());
	}

	mouseEvent.consume();
}

void GuiWindow::setMovable(bool movable)
{
	bMovable = movable;
}

bool GuiWindow::isMovable() const
{
	return bMovable;
}

void GuiWindow::setTitleBarHeight(float height)
{
	mTitleLabel->setHeight(height);
	dynamic_cast<BoxLayout*>(layout())->PerWidgetBehaviors[mTitleLabel].Padding = Rectangle(0, -height, 0, 0);
}

void GuiWindow::setPadding(const Rectangle& padding)
{
	layout<BoxLayout>()->DefaultBehavior.Padding = padding;
}

BoxLayout* GuiWindow::add(Widget* w, const ChildAlignment2D& alignment, bool bContainerHasOwnershipOfThis)
{
	add(w, bContainerHasOwnershipOfThis);
	auto boxLayout = dynamic_cast<BoxLayout*>(layout());
	if (alignment != boxLayout->DefaultBehavior.Alignment)
		boxLayout->PerWidgetBehaviors[w].Alignment = alignment;

	return boxLayout;
}

BoxLayout* GuiWindow::add(Widget* w, const ChildAlignment2D& alignment, const Rectangle& padding, bool bContainerHasOwnershipOfThis)
{
	auto boxLayout = add(w, alignment, bContainerHasOwnershipOfThis);
	const Rectangle targetPadding(getOutlineStyle().Value().BorderWidth.asRectangle().asVector() + padding.asVector());
	if (targetPadding != boxLayout->DefaultBehavior.Padding)
		boxLayout->PerWidgetBehaviors[w].Padding = targetPadding;

	return boxLayout;
}

bool GuiWindow::shouldDrawBackground(const Brush& background, const Outline& outline) const
{
	return true;
}
