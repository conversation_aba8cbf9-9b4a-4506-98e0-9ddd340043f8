#include "dealer-assist/DealerAssistSharedTypes.h"

#include <format>

#include "JsonSchema.h"

using namespace dealer_assist;

const std::string dealer_assist::DealerAssistServerServiceName = "tronius-dealer-assist-server";

const std::string dealer_assist::ADD_CARD_EVENT_NAME = "card-dealt";
const std::string dealer_assist::DEALER_ASSIST_TABLE_NOTIFICATION_EVENT_NAME = "notification";
const std::string dealer_assist::GAME_PHASE_CHANGED_EVENT_NAME = "game-phase-change";
const std::string dealer_assist::GAME_STATE_CHANGED_EVENT_NAME = "game-state-change";
const std::string dealer_assist::CARD_BURN_EVENT_NAME = "card-burn";
const std::string dealer_assist::PLAYER_COUNT_CHANGED_EVENT_NAME = "player-count-change";
const std::string dealer_assist::DEALER_LOGIN_CHANGED_EVENT_NAME = "login-change";
const std::string dealer_assist::DEALER_ACTION_EVENT_NAME = "dealer-action";
const std::string dealer_assist::SUPERVISOR_ACTION_EVENT_NAME = "supervisor-action";
const std::string dealer_assist::CUT_CARD_DRAWN_EVENT_NAME = "cut-card-drawn";
const std::string dealer_assist::FLAG_STATE_CHANGED_EVENT_NAME = "flag-state-change";
const std::string dealer_assist::SET_FLAG_STATE_EVENT_NAME = "set-flag-state";
const std::string dealer_assist::POST_CHAT_MESSAGE_EVENT_NAME = "chat-message";
const std::string dealer_assist::TOGGLE_CHAT_EVENT_NAME = "chat-toggle";
const std::string dealer_assist::GET_CHAT_MESSAGES = "get-chat-messages";
const std::string dealer_assist::POST_BET_STATISTICS = "post-bet-statistics";
const std::string dealer_assist::REPORT_BET_STATISTICS_RESULT = "report-bet-statistics-result";
const std::string dealer_assist::CHANGE_GAME_EVENT_NAME = "change-game";
const std::string dealer_assist::GET_GAMES_EVENT_NAME = "get-available-games";
const std::string dealer_assist::CHANGE_GAME_RESULT_EVENT_NAME = "change-game-result";
const std::string dealer_assist::CANCEL_SUPERVISOR_ACTION_EVENT_NAME = "cancel-supervisor-action";
const std::string dealer_assist::SUPERVISOR_STATE_CHANGED_EVENT_NAME = "supervisor-state-change";
const std::string dealer_assist::GAME_RESULT_CHANGED_EVENT_NAME = "game-result-change";
const std::string dealer_assist::ROUNDS_UNTIL_CHANGE_EVENT_NAME = "rounds-until-change";
const std::string dealer_assist::GET_TABLE_HISTORY_EVENT_NAME = "get-table-history";

const std::string dealer_assist::DEALER_ASSIST_CROUPIER_CLIENT_TYPE = "croupier";
const std::string dealer_assist::DEALER_ASSIST_GAMEHOST_CLIENT_TYPE = "host";

DealerAssistActionsState DealerAssistActionsState::FromJSON(const json& val)
{
	DealerAssistActionsState state;
	if (const auto chat = FindMember(val, "chatEnabled"); chat && !chat->is_null())
		state.bChatEnabled = chat->get<bool>();

	if (const auto flagged = FindMember(val, "roundFlagged"); flagged && !flagged->is_null())
		state.bRoundFlagged = flagged->get<bool>();

	if (const auto supervisorCalled = FindMember(val, "supervisorCalled"); supervisorCalled && !supervisorCalled->is_null())
		state.bSupervisorCalled = supervisorCalled->get<bool>();

	if (const auto resultChange = FindMember(val, "resultChangingRequested"); resultChange && !resultChange->is_null())
		state.bResultChangeRequested = resultChange->get<bool>();

	if (const auto gameChange = FindMember(val, "gameChangingRequested"); gameChange && !gameChange->is_null())
		state.bGameChangeRequested = gameChange->get<bool>();

	if (const auto tableClose = FindMember(val, "tableClosingRequested"); tableClose && !tableClose->is_null())
		state.bTableCloseRequested = tableClose->get<bool>();

	if (const auto changeShoe = FindMember(val, "changeShoeRequested"); changeShoe && !changeShoe->is_null())
		state.bChangeShoeRequested = changeShoe->get<bool>();

	if (const auto freeHand = FindMember(val, "freeHandEnabled"); freeHand && !freeHand->is_null())
		state.bFreeHandEnabled = freeHand->get<bool>();

	return state;
}

json DealerAssistActionsState::ToJSON() const
{
	json root(json::value_t::object);
	root["chatEnabled"] = bChatEnabled;
	root["roundFlagged"] = bRoundFlagged;
	root["supervisorCalled"] = bSupervisorCalled;
	root["resultChangingRequested"] = bResultChangeRequested;
	root["gameChangingRequested"] = bGameChangeRequested;
	root["tableClosingRequested"] = bTableCloseRequested;
	root["changeShoeRequested"] = bChangeShoeRequested;
	root["freeHandEnabled"] = bFreeHandEnabled;

	return root;
}

DealerAssistWorker DealerAssistWorker::FromJSON(const json& val)
{
	DealerAssistWorker state;
	val["id"].get_to(state.ID);
	val["name"].get_to(state.Name);

	const json* accessLevel = FindMember(val, "accessLevel");
	if (accessLevel && !accessLevel->is_null())
	{
		std::string accessLevelStr;
		accessLevel->get_to(accessLevelStr);
		state.AccessLevel = backend::EDealerAssistAccessLevel::_from_string(accessLevelStr.c_str());
	}

	const json* location = FindMember(val, "location");
	if (location && !location->is_null())
		location->get_to(state.Location);

	return state;
}

json DealerAssistWorker::ToJSON() const
{
	json root(json::value_t::object);
	root["id"] = ID;
	root["name"] = Name;
	root["location"] = Location;
	root["accessLevel"] = AccessLevel._to_string();
	return root;
}

bool DealerAssistWorker::IsSupervisor() const
{
	return AccessLevel == backend::EDealerAssistAccessLevel::Supervisor;
}

void dealer_assist::to_json(json& sourceJson, const DealerGameInfoDto& dto)
{
	sourceJson["id"] = dto.Id;
	sourceJson["gameType"] = dto.GameType._to_string();
	sourceJson["displayName"] = dto.DisplayName;
	sourceJson["isVirtual"] = dto.IsVirtual;
	sourceJson["cardRule"] = dto.CardRule._to_string();
	sourceJson["config"] = dto.Config;
}

void dealer_assist::from_json(const json& sourceJson, DealerGameInfoDto& dto)
{
	dto = {};
	if (const json* id = FindMember(sourceJson, "id"))
		id->get_to(dto.Id);

	if (const json* gameType = FindMember(sourceJson, "gameType"))
		gameType->get_to(dto.GameType);

	if (const json* displayName = FindMember(sourceJson, "displayName"))
		displayName->get_to(dto.DisplayName);

	if (const json* isVirtual = FindMember(sourceJson, "isVirtual"))
		isVirtual->get_to(dto.IsVirtual);

	if (const json* config = FindMember(sourceJson, "config"))
		dto.Config = *config;

	if (const json* cardRule = FindMember(sourceJson, "cardRule"))
		cardRule->get_to(dto.CardRule);
}

bool DealerGameInfoDto::operator==(const DealerGameInfoDto& other) const
{
	return GameType == other.GameType && IsVirtual == other.IsVirtual && Id == other.Id;
}

bool DealerGameInfoDto::operator!=(const DealerGameInfoDto& other) const
{
	return !(*this == other);
}

std::string DealerGameInfoDto::ToString() const
{
	return std::format("{}({})", GameType._to_string(), IsVirtual ? "Virtual" : "Live");
}

// SupervisorState
json SupervisorState::ToJSON() const
{
	json root(json::value_t::object);
	if (SelectedGameType)
		root["selectedGameType"] = SelectedGameType.value()._to_string();
	if (SubPhase)
		root["subPhase"] = SubPhase.value()._to_string();
	if (RequestedAction)
		root["selectedAction"] = RequestedAction.value()._to_string();
	if (!ChangedGameResult.empty())
		root["changedGameResult"] = ChangedGameResult;
	if (SelectedGameInfo)
		root["selectedGame"] = SelectedGameInfo;

	return root;
}

SupervisorState SupervisorState::FromJSON(const json& val)
{
	SupervisorState state;
	const json* subPhase = FindMember(val, "subPhase");
	if (subPhase && !subPhase->is_null())
		state.SubPhase = ESupervisorSubPhase::_from_string(subPhase->get<std::string>().c_str());

	const json* requestedAction = FindMember(val, "selectedAction");
	if (requestedAction && !requestedAction->is_null())
		state.RequestedAction = ESupervisorAction::_from_string(requestedAction->get<std::string>().c_str());

	const json* changedGameResult = FindMember(val, "changedGameResult");
	if (changedGameResult && !changedGameResult->is_null())
		state.ChangedGameResult = *changedGameResult;

	const json* selectedGameType = FindMember(val, "selectedGameType");
	if (selectedGameType && !selectedGameType->is_null())
		state.SelectedGameType = EGameType::_from_string(selectedGameType->get<std::string>().c_str());

	return state;
}

void SupervisorState::Reset()
{
	SubPhase.reset();
	RequestedAction.reset();
	ChangedGameResult.clear();
	Active = false;
}


TableState TableState::FromJSON(const json& val)
{
	TableState state;
	if (const json* tableState = FindMember(val, "tableState"))
	{
		if (!tableState->is_null())
		{
			const json* nextCardPosition = FindMember(*tableState, "nextCardPosition");
			if (nextCardPosition && !nextCardPosition->is_null())
				state.mDealingPhase = nextCardPosition->get<uint8_t>();

			const json* outcome = FindMember(*tableState, "outcome");
			if (outcome && !outcome->is_null())
				state.mWinner = outcome->get<uint8_t>();

			if (tableState->contains("cards"))
			{
				state.mCards = val["tableState"]["cards"];
			}

			if (tableState->contains("faceDownCards"))
			{
				state.mFaceDownCards = val["tableState"]["faceDownCards"].get<std::map<uint8_t, uint32_t>>();
			}
		}
	}

	if (const json* roundState = FindMember(val, "roundState"))
	{
		if (!roundState->is_null())
		{
			const json* phase = FindMember(*roundState, "phase");
			if (phase && !phase->is_null())
			{
				state.mPhase = EDealerAssistPhase::_from_string(phase->get<std::string>().c_str());
			}

			const json* remainingTime = FindMember(*roundState, "remainingBettingTimeInSeconds");
			if (remainingTime && !remainingTime->is_null())
				state.mRemainingBettingTimeInSeconds = remainingTime->get<uint32_t>();

			const json* roundId = FindMember(*roundState, "roundId");
			if (roundId && !roundId->is_null())
				state.mRoundID = roundId->get<uint32_t>();

			const json* timestamp = FindMember(*roundState, "timestamp");
			if (timestamp && !timestamp->is_null())
				state.mTimestamp = timestamp->get<uint32_t>();

			const json* errorCode = FindMember(*roundState, "errorCode");
			if (errorCode && !errorCode->is_null())
				state.mErrorCode = EDealerAssistTableErrorCode::_from_string(errorCode->get<std::string>().c_str());
		}
	}

	return state;
}

json TableState::ToJSON() const
{
	json table(json::value_t::object);
	if (mDealingPhase)
		table["nextCardPosition"] = mDealingPhase.value();
	if (mWinner && mPhase == EDealerAssistPhase::RoundEnd)
		table["outcome"] = mWinner.value();
	if (mWinners && mPhase == EDealerAssistPhase::RoundEnd)
		table["outcomes"] = mWinners.value();
	if (!mCards.empty())
	{
		table["handValue"] = mHandValue;
		table["cards"] = mCards;
	}

	if (mCardPosition)
		table["cardPosition"] = mCardPosition.value();

	if (!mFaceDownCards.empty())
	{
		table["faceDownCards"] = mFaceDownCards;
	}

	json round(json::value_t::object);
	round["phase"] = mPhase._to_string();

	if (mSubPhase)
		round["subPhase"] = mSubPhase.value();

	if (mRoundID)
		round["roundId"] = *mRoundID;

	if (mErrorCode)
		round["errorCode"] = mErrorCode.value()._to_string();

	if (mPhase == EDealerAssistPhase::BetsOpen)
	{
		if (mRemainingBettingTimeInSeconds)
			round["remainingBettingTimeInSeconds"] = mRemainingBettingTimeInSeconds.value();

		if (mTimestamp)
			round["timestamp"] = mTimestamp.value();
	}

	json root(json::value_t::object);
	root["tableState"] = std::move(table);
	root["roundState"] = std::move(round);
	return root;
}

void TableState::Clear()
{
	mCardPosition.reset();
	mDealingPhase = 0;
	mWinner.reset();
	mHandValue.clear();
	mCards.clear();
	mFaceDownCards.clear();
	mRoundID.reset();
	mRemainingBettingTimeInSeconds.reset();
	mTimestamp.reset();
}

const JsonSchema& DealerGameInfoDto::GameConfigSchema()
{
	static const JsonSchema DealerGameConfigSchema = JsonSchema(
	  { { "early-bets-close-timer", JsonSchema(json::value_t::number_unsigned, "Allow bets close if card is drawn inside given last ms", 2000)
	                                  .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 5000U)) },
	    { "check-duplicate-cards", JsonSchema(json::value_t::boolean, "True to check if card is already on table", true) },
	    { "chat-enabled", JsonSchema(json::value_t::boolean, "True to enable chat", true) },
	    { "bets-open-timer", JsonSchema(json::value_t::number_unsigned, "Duration of bets open phase (milliseconds)", 30000) },
	    { "decision-open-timer", JsonSchema(json::value_t::number_unsigned, "Duration of bets open phase (milliseconds)", 5000) },
	    { "animation-timer", JsonSchema(json::value_t::number_unsigned, "Duration of bets open phase (milliseconds)", 5000) },
	    { "pause-at-round-finish-timer", JsonSchema(json::value_t::number_unsigned, "Duration of pause between result and new round preparation (milliseconds)", 5000) },
	    { "new-round-preparation-timer", JsonSchema(json::value_t::number_unsigned, "Duration of pause before new round begins (milliseconds)", 4000) },
	    { "pause-at-card-burn-end-timer",
	      JsonSchema(json::value_t::number_unsigned, "Duration of pause after last card is burned and new round preparation begin (milliseconds)", 0) },
	    { "cut-card-barcode", JsonSchema(json::value_t::string, "The barcode of the cut card", "CUT") } });
	__attribute__((unused)) volatile int dummy {};
	return DealerGameConfigSchema;
}

const JsonSchema& DealerGameInfoDto::Schema()
{
	static const JsonSchema DealerGameSchema = JsonSchema(
	  { { "config", GameConfigSchema() },
	    { "displayName", JsonSchema(json::value_t::string, "The name of the game") },
	    { "id", JsonSchema(json::value_t::number_unsigned, "Game id") },
	    { "isVirtual", JsonSchema(json::value_t::boolean, "Has virtual dealer", false) },
	    { "cardRule", JsonSchema(json::value_t::string, "Card rule to use for the game", yserver::gamehost::ECardRule(yserver::gamehost::ECardRule::Asian)._to_string())
	                    .SetToEnumType<yserver::gamehost::ECardRule>() },
	    { "gameType", JsonSchema(json::value_t::string, "The type of the game", EGameType(EGameType::Baccarat)._to_string()).SetToEnumType<EGameType>() } },
	  "Information about a game on DA table.");
	__attribute__((unused)) volatile int dummy {};
	return DealerGameSchema;
}
