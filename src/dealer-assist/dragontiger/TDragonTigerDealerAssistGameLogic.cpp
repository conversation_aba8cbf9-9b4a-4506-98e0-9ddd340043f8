//
// Created by <PERSON><PERSON><PERSON> on 28. 8. 24.
//

#include "TDragonTigerDealerAssistGameLogic.h"

#include "Cryptography.h"
#include "TApplication.h"
#include "dealer-assist/TDealerAssistGameLogic.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "dealer-assist/dto/AddCardDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/NotifyDealerConsoleDto.h"

DEFINE_LOG_CATEGORY(LogDragonTigerDealerAssist, "dragon-tiger-dealer-assist-logic")

using namespace yserver::gamehost;
using namespace dealer_assist::backend;
using namespace dealer_assist;

TDragonTigerDealerAssistGameLogic::TDragonTigerDealerAssistGameLogic(std::string tableId, const DealerGameInfoDto& gameInfo) :
    TDealerAssistGameLogic(gameInfo.GameType, std::move(tableId), gameInfo.IsVirtual)
{
	State->GameLogic = std::make_unique<DragonTigerGameLogic>(gameInfo.CardRule);
	State->Table.mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;

	mOpenBetTypes = { EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string(), EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string() };

	TLOG(LogDragonTigerDealerAssist, Info, "Card rule for dragon tiger: %s", gameInfo.CardRule._to_string());
}

TDragonTigerDealerAssistGameLogic::~TDragonTigerDealerAssistGameLogic() {}

void TDragonTigerDealerAssistGameLogic::OnConfigLoaded(const std::filesystem::path& filename)
{
	TDealerAssistGameLogic::OnConfigLoaded(filename);

	mBetsOpenTime = GetConfig("bets-open-timer").get<uint32_t>();
	mDecisionTime = GetConfigOptional("decision-open-timer", 4000).get<uint32_t>();
	mAnimationTime = GetConfigOptional("animation-timer", 4000).get<uint32_t>();
	mPauseAtRoundFinishTime = GetConfig("pause-at-round-finish-timer").get<uint32_t>();
	mNewRoundPreparationTime = GetConfig("new-round-preparation-timer").get<uint32_t>();
	mPauseBeforeRoundEndTime = GetConfigOptional("pause-before-round-end-timer", 3500).get<uint32_t>();
	mCutCardBarcode = GetConfig("cut-card-barcode").get<std::string>();
	bShouldCheckDuplicateCards = GetConfig("check-duplicate-cards").get<bool>();
	mPauseAtCardBurnEndTime = GetConfig("pause-at-card-burn-end-timer").get<uint64_t>();

	State->ActionsState.bChatEnabled = GetConfig("chat-enabled").get<bool>();

	State->VirtualDealerLogic = std::make_shared<TVirtualDealerLogic>(8, 69);

	mBetTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mBetTimer);

		  TLOG(LogDragonTigerDealerAssist, Info, "Bets are closed. Dealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);

		  // Report dealing cards
		  if (GameType == EGameType::OpenDragonTiger)
			  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
		  else
			  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);

		  if (bVirtualGame)
			  DealWithVirtualDealer();
	  },
	  mBetsOpenTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Open bets timer");

	mDecisionTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mDecisionTimer);

		  TLOG(LogDragonTigerDealerAssist, Info, "Decision bets are closed. Revealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);

		  // Report dealing cards
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
		  RevealRemainingSide();
	  },
	  mDecisionTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Decision bets timer");

	mAnimationTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mAnimationTimer);

		  TLOG(LogDragonTigerDealerAssist, Info, "Animation over");

		  RevealFirstSide();
	  },
	  mAnimationTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Animation timer");

	mNewRoundPreparationTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mNewRoundPreparationTimer);
		  TLOG(LogDragonTigerDealerAssist, Info, "Waiting for new round to begin is over.");
		  OnRoundBegin();
	  },
	  mPauseAtRoundFinishTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause when one round is closed and dealer wait for new round to begin.");

	mPauseAfterCardBurnTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAfterCardBurnTimer);
		  ScopedLock lock(State);
		  OpenNewRound_AssumeWriteLock();
	  },
	  mPauseAtCardBurnEndTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause after last card burn and before new round begins.");

	mPauseAtRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAtRoundEndTimer);
		  TLOG(LogDragonTigerDealerAssist, Info, "Waiting at round end / void is over.");
		  ScopedLock lock(State);

		  if (CanCloseTable())
		  {
			  lock.unlock();
			  TLOG(LogDragonTigerDealerAssist, Info, "Closing table.");
			  LogoutCroupier();
		  }
		  else if (State->ActionsState.bGameChangeRequested)
		  {
			  if (!CanChangeGame())
			  {
				  TLOG(LogDragonTigerDealerAssist, Info, "Game change requested but waiting for countdown (%d/%d rounds). Opening new round.", State->RoundsPlayedCounter,
				       State->GameflowConfig.RoundsBeforeGameChange);
				  OpenNewRound_AssumeWriteLock();
			  }
			  else if (!State->SupervisorState->SelectedGameType.has_value())
			  {
				  TLOG(LogDragonTigerDealerAssist, Info, "Could not change game, no game selected. Opening new round.");
				  OpenNewRound_AssumeWriteLock();
			  }
			  else
			  {
				  TLOG(LogDragonTigerDealerAssist, Info, "Switching game.");

				  lock.unlock();

				  OnSwitchGameEvent();
			  }
		  }
		  else if (State->bShouldBurnCards)
		  {
			  ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

			  if (bVirtualGame)
			  {
				  ShoeChanged_AssumeWriteLock();
			  }
		  }
		  else
		  {
			  TLOG(LogDragonTigerDealerAssist, Info, "Opening new round");
			  OpenNewRound_AssumeWriteLock();
		  }
	  },
	  mPauseAtRoundFinishTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause at round end timer");

	mCardRevealTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mCardRevealTimer);
		  TLOG(LogDragonTigerDealerAssist, Info, "Revealing card");
		  RevealRemainingSide();
	  },
	  1500, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Reveal card timer");

	// Stop autostart of timers
	StopAllTimers();
}

// When backend is connected we request table history (from last cut card event)
void TDragonTigerDealerAssistGameLogic::OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round)
{
	ScopedLock lock(State);
	State->Table.mErrorCode.reset();
	bool updateState = false;

	if (round.has_value() && round->Status == backend::ERoundStatus::Open)
	{
		if (!round->State.is_null())
		{
			State->Table = TableState::FromJSON(round->State);
			State->GameLogic->SetCurrentStateOfCards(State->Table.mCards);
			TLOG(LogDragonTigerDealerAssist, Info, "Game was not finished on backend, restoring state.");
			updateState = true;
		}

		if (round->ID != State->Table.mRoundID)
		{
			TLOG(LogDragonTigerDealerAssist, Info, "Saved round ID in state %llu, received round ID %u are different", State->Table.mRoundID.value_or(0), round->ID);
			State->Table.mRoundID = round->ID;

			updateState = true;
		}
	}

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::WaitingForCroupier);
	lock.unlock();

	if (updateState)
		OnStateUpdate();

	DealerAssistBackendClient->GetTableHistory(GameType._to_string()).then(boost::launch::sync, [this](boost::future<std::vector<GameRecordDto>> futureHistory) {
		ScopedLock lock(State);
		State->GameRecords = futureHistory.get();
		TLOG(LogDragonTigerDealerAssist, Info, "Received history with %i records", State->GameRecords.size());

		if (State->GameRecords.size() > 0)
			OnStateUpdate();
	});
}

void TDragonTigerDealerAssistGameLogic::OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogDragonTigerDealerAssist, Info, "Game switched.");

	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;

	StopAllTimers();

	State->bShouldBurnCards = true;
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

	if (bVirtualGame)
		ShoeChanged_AssumeWriteLock();
}

DealerAssistInitResponseDto TDragonTigerDealerAssistGameLogic::GetInitPacket() const
{
	SharedScopedLock lockTableState(State);

	TableState tableState = State->Table;

	const auto phase = State->Table.mPhase;
	auto hello = DealerAssistInitResponseDto();

	if (phase == EDealerAssistPhase::DealingCards || phase == EDealerAssistPhase::RoundEnd)
	{
		json result = json::object();
		result["tigerHandValue"] = State->GameLogic->GetHandValue(EDragonTigerSide::Tiger);
		result["dragonHandValue"] = State->GameLogic->GetHandValue(EDragonTigerSide::Dragon);
		tableState.mDealingPhase = EBaccaratDealingPhase::_from_index(State->GameLogic->GetDealingPhase());
		tableState.mHandValue = result;
		tableState.mCards = State->GameLogic->GetCurrentStateOfCards();
		tableState.mRoundID = State->Table.mRoundID;
	}

	if (phase == EDealerAssistPhase::BetsOpen)
	{
		auto now = ytime::GetTimeMsec();
		auto remainingTimeInRound = mBetsOpenTime - (now - tableState.mTimestamp.value_or(0));
		auto remainingTimeInSeconds = remainingTimeInRound / 1000;
		tableState.mRemainingBettingTimeInSeconds = remainingTimeInSeconds;
	}

	if (phase == EDealerAssistPhase::CardBurn || State->ShoeChangeState.CutCardDrawn)
		hello.mShoeChangeState = State->ShoeChangeState;

	if (State->SupervisorState)
	{
		hello.mSupervisorState = *State->SupervisorState;
	}

	if (State->RoundsUntilChange)
		hello.mRoundsUntilChange = *State->RoundsUntilChange;

	hello.mBetTimeInSec = mBetsOpenTime / 1000;
	hello.mDecisionTimeInSec = mDecisionTime / 1000;
	hello.mAnimationTimeInSec = mAnimationTime / 1000;

	hello.mPlayerCount = State->NumOfAllPlayers;
	hello.mActionsState = State->ActionsState;

	if (State->Croupier)
		hello.mCroupierInfo = *State->Croupier;

	hello.mHistoryRecords = State->GameRecords;
	hello.mTableState = tableState;
	hello.mGameType = GameType._to_string();
	return hello;
}

void TDragonTigerDealerAssistGameLogic::HandleScannedCard(const FScannedCard& data)
{
	ScopedLock lockTableState(State);
	if (data.Suite() == ECardSuite::None || data.Face() == ECardFace::None)
	{
		if (data.Barcode() == mCutCardBarcode)
			HandleCutCard_AssumeWriteLock();
		return;
	}

	if (State->SupervisorState->Active && State->ActionsState.bSupervisorCalled)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Ignoring card scan, supervisor called");
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::CardBurn)
	{
		HandleCardBurn_AssumeWriteLock(data);
		return;
	}
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Ignoring card scan, not in dealing or card burn phase");
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::DealingCards && State->Table.mSubPhase.has_value() && State->Table.mSubPhase != EDealingSubPhase::FaceDown &&
	    State->Table.mSubPhase != EDealingSubPhase::Dealing)
	{
		TLOG(LogDragonTigerDealerAssist, Warning, "Ignoring card scan, is in dealing phase but not in dealing subphase");
		return;
	}

	if (IsCardOnTable_AssumeReadLock(data) && bShouldCheckDuplicateCards)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Ignoring card scan, card already on table");
		json context = json::object();
		context["reason"] = "Card already on table";
		auto dto = NotifyDealerConsoleDto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::DuplicateCard), context);
		dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
		OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);

		return;
	}

	if (bVirtualGame)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Ignoring card scan, virtual dealer is dealing cards");
		return;
	}

	if (State->GameLogic->IsGameFinished())
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Ignoring card scan, game already finished");
		return;
	}

	if (GameType == EGameType::OpenDragonTiger)
		DealFaceDownCards_AssumedLockedWrite(data);
	else
		DealRegularCard_AssumedLockedWrite(data);
}

void TDragonTigerDealerAssistGameLogic::HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data)
{
	if (GameType == EGameType::OpenDragonTiger)
		DealFaceDownCards_AssumedLockedWrite(data);
	else
		DealRegularCard_AssumedLockedWrite(data);
}

void TDragonTigerDealerAssistGameLogic::HandleCroupierLogin(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogDragonTigerDealerAssist, Info, "Croupier login detected.");
	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;

	StopAllTimers();

	if (State->Table.mRoundID.has_value())    // Round already has value, we are in the middle of the round
		if (GameType == EGameType::OpenDragonTiger)
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
		else
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
	else
	{
		State->bShouldBurnCards = true;
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

		if (bVirtualGame)
			ShoeChanged_AssumeWriteLock();
	}

	OnStateUpdate();
}

void TDragonTigerDealerAssistGameLogic::OnRoundBegin()
{
	ScopedLock lockTableState(State);
	OnRoundBegin_AssumeWriteLock();
}

void TDragonTigerDealerAssistGameLogic::OnRoundBegin_AssumeWriteLock()
{
	TLOG(LogDragonTigerDealerAssist, Info, "New round begins - bets are open!");
	State->Table.mTimestamp = ytime::GetTimeMsec();
	State->Table.mWinner.reset();

	ReportNumberOfPlayers_AssumeReadLock();
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::BetsOpen);

	StartTimer(mBetTimer);
}

void TDragonTigerDealerAssistGameLogic::OnPauseBeforeRoundEnd()
{
	TLOG(LogDragonTigerDealerAssist, Info, "Pausing before round end.");

	mPauseBeforeRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>([this]() { OnRoundEnd(); }, mPauseBeforeRoundEndTime,
	                                                                                   rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Pause before round end timer");
}

void TDragonTigerDealerAssistGameLogic::OnRoundEnd()
{
	ScopedLock lock(State);
	OnRoundEnd_AssumeWriteLock();
}

void TDragonTigerDealerAssistGameLogic::OnRoundEnd_AssumeWriteLock()
{
	const auto winner = State->GameLogic->Evaluate();
	TLOG(LogDragonTigerDealerAssist, Info, "Winner is: %s", EDragonTigerWinner::_from_integral(winner[0])._to_string());

	if (bVirtualGame)
		State->VirtualDealerLogic->RoundFinished();

	CheckRoundClosing_AssumeWriteLock();
	CheckGameChangeRounds_AssumeWriteLock();

	SaveRoundState_AssumeReadLock(backend::ERoundStatus::Closed).then([this](boost::future<void> future) {
		try
		{
			future.get();
			ScopedLock lockTableState(State);
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundEnd);
			ResetCurrentRound_AssumeWriteLock();

			if (State->ActionsState.bChangeShoeRequested)
				State->bShouldBurnCards = true;

			// Pause before going in NewRoundPreparation
			TLOG(LogDragonTigerDealerAssist, Info, "Pausing before new round begins.");
			StartTimer(mPauseAtRoundEndTimer);
		}
		catch (const DealerAssistException& ex)
		{
			TLOG(LogDragonTigerDealerAssist, Warning, "DealerAssistException: %s", ex.what());

			if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
				OnTableCloseEvent();
		}
		catch (const std::exception& e)
		{
			TLOG(LogDragonTigerDealerAssist, Warning, "Error on saving result: %s", e.what());
		}
	});

	ReportNumberOfPlayers_AssumeReadLock();
}

void TDragonTigerDealerAssistGameLogic::AddCard_AssumeWriteLock(const uint32_t card)
{
	switch (State->GameLogic->GetDealingPhase())
	{
		case EDragonTigerDealingPhase::DragonCard: State->GameLogic->AddCard(card, EDragonTigerSide::Dragon); break;
		case EDragonTigerDealingPhase::TigerCard: State->GameLogic->AddCard(card, EDragonTigerSide::Tiger); break;
		default: break;
	}
}

bool TDragonTigerDealerAssistGameLogic::IsCardOnTable_AssumeReadLock(const FScannedCard& card) const
{
	return State->CardsOnTable.contains(card);
}

json TDragonTigerDealerAssistGameLogic::ShoeChanged_AssumeWriteLock()
{
	TLOG(LogDragonTigerDealerAssist, Info, "Shoe changed detected.");
	if (State->Table.mPhase == EDealerAssistPhase::ShoeChange)
	{
		State->CardsOnTable.clear();
		State->GameRecords.clear();

		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::CardBurn);

		if (bVirtualGame)
		{
			State->VirtualDealerLogic->ShuffleCards();
			BurnCardsWithVirtualDealer_AssumeWriteLock();
		}

		return {};
	}

	throw std::runtime_error("Shoe change detected in unexpected phase");
}

void TDragonTigerDealerAssistGameLogic::ResetCurrentRound_AssumeWriteLock()
{
	mBetsStatistics->ClearAllBetAmounts();

	// Clear state and timers
	State->SupervisorState->Reset();
	State->Table.Clear();
	State->GameLogic->ClearGame();

	StopAllTimers();
}

void TDragonTigerDealerAssistGameLogic::DealRegularCard_AssumedLockedWrite(const FScannedCard& data)
{
	// Get dealing phase before we add card
	auto dealingPhase = State->GameLogic->GetDealingPhase();
	const auto cardValue = data.ToInt() % 1000;

	AddCard_AssumeWriteLock(cardValue);

	State->Table.mCardPosition = dealingPhase;
	State->Table.mDealingPhase = EBaccaratDealingPhase::_from_index(State->GameLogic->GetDealingPhase());
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	SaveIntermediateRoundState_AssumeReadLock();

	State->CardsOnTable.insert(data);

	auto dto = AddDragonTigerCardDto();
	dto.mScannedCard = cardValue;
	dto.mCardPosition = dealingPhase;
	dto.mTigerHandValue = State->GameLogic->GetHandValue(EDragonTigerSide::Tiger);
	dto.mDragonHandValue = State->GameLogic->GetHandValue(EDragonTigerSide::Dragon);

	if (State->GameLogic->GetDealingPhase() == EDragonTigerDealingPhase::Finished)
	{
		auto winners = State->GameLogic->Evaluate();
		State->Table.mWinner = winners.front();
		dto.mOutcome = *State->Table.mWinner;
	}
	else
	{
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();
	}

	OnBroadcastEvent(*mAddCardEvent, dto);

	if (State->GameLogic->GetDealingPhase() == EDragonTigerDealingPhase::Finished)
	{
		OnPauseBeforeRoundEnd();
	}
	else if (bVirtualGame)
	{
		DealWithVirtualDealer_AssumeWriteLock();
	}
}

void TDragonTigerDealerAssistGameLogic::DealFaceDownCards_AssumedLockedWrite(const FScannedCard& data)
{    // Get dealing phase before we add card
	auto dealingPhase = State->GameLogic->GetDealingPhase();
	const auto cardValue = data.ToInt() % 1000;

	State->Table.mFaceDownCards[dealingPhase] = cardValue;
	AddCard_AssumeWriteLock(faceDownCard);

	State->Table.mCardPosition = dealingPhase;
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	SaveIntermediateRoundState_AssumeReadLock();

	// State->CardsOnTable.insert(data);

	auto dto = AddDragonTigerCardDto();
	dto.mScannedCard = faceDownCard;
	dto.mCardPosition = dealingPhase;

	if (State->GameLogic->GetDealingPhase() != EDragonTigerDealingPhase::Finished)
	{
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();
	}

	OnBroadcastEvent(*mAddCardEvent, dto);

	if (State->GameLogic->GetDealingPhase() == EDragonTigerDealingPhase::Finished)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Start animation - choosing side!");
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::SideSelectionAnimation);
		StartTimer(mAnimationTimer);
	}
	else if (bVirtualGame)
	{
		DealWithVirtualDealer_AssumeWriteLock();
	}
}
void TDragonTigerDealerAssistGameLogic::RevealFirstSide()
{
	ScopedLock lock(State);

	const auto index = crypto::GetRandomInRange(0, 1);
	EDragonTigerSide side = EDragonTigerSide::_from_index(index);

	TLOG(LogDragonTigerDealerAssist, Info, "Revealing first side: %s", side._to_string());

	auto subPhase = side == EDragonTigerSide::Dragon ? EDealingSubPhase::LeftSide : EDealingSubPhase::RightSide;
	State->Table.mSelectedSide = side;

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, subPhase);

	const EDragonTigerDealingPhase cardPosition = side == EDragonTigerSide::Dragon ? EDragonTigerDealingPhase::DragonCard : EDragonTigerDealingPhase::TigerCard;

	const auto actualCard = State->Table.mFaceDownCards[cardPosition];
	State->GameLogic->AddOrReplaceCard(actualCard, side, 0);

	TLOG(LogDragonTigerDealerAssist, Info, "Revealing first card: %i", actualCard);

	auto dto = AddDragonTigerCardDto();
	dto.mScannedCard = actualCard;

	if (side == EDragonTigerSide::Dragon)
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Dragon hand: %i", State->GameLogic->GetHandValue(side));
		dto.mDragonHandValue = State->GameLogic->GetHandValue(side);
	}
	else
	{
		TLOG(LogDragonTigerDealerAssist, Info, "Tiger hand: %i", State->GameLogic->GetHandValue(side));
		dto.mTigerHandValue = State->GameLogic->GetHandValue(side);
	}

	State->Table.mFaceDownCards.erase(cardPosition);

	dto.mCardPosition = cardPosition._to_integral();

	OnBroadcastEvent(*mAddCardEvent, dto);

	if (State->GameflowConfig.SkipDecisionIfNoBets && !mBetsStatistics->HasBetsOnFields(mOpenBetTypes))
	{
		TLOG(LogDragonTigerDealerAssist, Info, "No bet on open fields, skip Decision phase.");
		TLOG(LogDragonTigerDealerAssist, Info, "Deal remaining side!");
		StartTimer(mCardRevealTimer);
	}
	else
	{
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::Decision);
		StartTimer(mDecisionTimer);
	}
}

void TDragonTigerDealerAssistGameLogic::RevealRemainingSide()
{
	ScopedLock lock(State);

	EDragonTigerSide otherside = State->Table.mSelectedSide == EDragonTigerSide::Dragon ? EDragonTigerSide::Tiger : EDragonTigerSide::Dragon;
	const EDragonTigerDealingPhase cardPosition = otherside == EDragonTigerSide::Dragon ? EDragonTigerDealingPhase::DragonCard : EDragonTigerDealingPhase::TigerCard;

	TLOG(LogDragonTigerDealerAssist, Info, "Revealing remaining side: %s", otherside._to_string());

	const auto actualCard = State->Table.mFaceDownCards[cardPosition];
	State->GameLogic->AddOrReplaceCard(actualCard, otherside, 0);
	State->Table.mFaceDownCards.erase(cardPosition);

	auto dto = AddDragonTigerCardDto();
	dto.mScannedCard = actualCard;

	TLOG(LogDragonTigerDealerAssist, Info, "Dragon hand: %i", State->GameLogic->GetHandValue(otherside));
	TLOG(LogDragonTigerDealerAssist, Info, "Tiger hand: %i", State->GameLogic->GetHandValue(otherside));

	dto.mDragonHandValue = State->GameLogic->GetHandValue(EDragonTigerSide::Dragon);
	dto.mTigerHandValue = State->GameLogic->GetHandValue(EDragonTigerSide::Tiger);

	dto.mCardPosition = cardPosition._to_integral();

	if (State->GameLogic->GetDealingPhase() == EDragonTigerDealingPhase::Finished)
	{
		auto winners = State->GameLogic->Evaluate();
		State->Table.mWinner = winners.front();
		dto.mOutcome = State->Table.mWinner;
	}

	TLOG(LogDragonTigerDealerAssist, Info, "Add last card: %s", JsonSchema::PrintValueInline(dto.ToJSON()).c_str());

	OnBroadcastEvent(*mAddCardEvent, dto);

	OnPauseBeforeRoundEnd();
}

bool TDragonTigerDealerAssistGameLogic::CanRevealRemainingCards_AssumeReadLock()
{
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
		return false;

	if (State->Table.mSubPhase != EDealingSubPhase::LeftSide && State->Table.mSubPhase != EDealingSubPhase::RightSide)
		return false;

	return State->GameLogic->HasFaceDownCard(EDragonTigerSide::Dragon) != State->GameLogic->HasFaceDownCard(EDragonTigerSide::Tiger);
};

void TDragonTigerDealerAssistGameLogic::UpdateTableState_AssumeWriteLock()
{
	json result = json::object();
	result["tigerHandValue"] = State->GameLogic->GetHandValue(EDragonTigerSide::Tiger);
	result["dragonHandValue"] = State->GameLogic->GetHandValue(EDragonTigerSide::Dragon);

	State->Table.mHandValue = std::move(result);
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	if (State->GameLogic->IsGameFinished())
	{
		State->GameLogic->Evaluate();
		State->Table.mWinner = State->GameLogic->GetWinner();
	}
	else
	{
		State->Table.mWinner.reset();
		State->Table.mWinners.reset();
	}
}
