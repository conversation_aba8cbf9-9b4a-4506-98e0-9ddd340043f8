//
// Created by <PERSON><PERSON><PERSON> on 22. 02. 24.
//

#include "dealer-assist/TBaccaratDealerAssistGameLogic.h"

#include "TApplication.h"
#include "dealer-assist/TDealerAssistGameLogic.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "dealer-assist/dto/AddCardDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/DealerAssistStateUpdateDto.h"
#include "dealer-assist/dto/NotifyDealerConsoleDto.h"

DEFINE_LOG_CATEGORY(LogBaccaratDealerAssist, "baccarat-dealer-assist-logic")

using namespace yserver::gamehost::baccarat;
using namespace yserver::gamehost;
using namespace dealer_assist::backend;
using namespace dealer_assist;

TBaccaratDealerAssistGameLogic::TBaccaratDealerAssistGameLogic(std::string tableId, const DealerGameInfoDto& gameInfo) :
    TDealerAssistGameLogic(gameInfo.GameType, std::move(tableId), gameInfo.IsVirtual)
{
	State->GameLogic = std::make_unique<BaccaratGameLogic>();
	State->Table.mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;

	mOpenBetTypes = { EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(), EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string() };
}

TBaccaratDealerAssistGameLogic::~TBaccaratDealerAssistGameLogic() {}

void TBaccaratDealerAssistGameLogic::OnConfigLoaded(const std::filesystem::path& filename)
{
	TDealerAssistGameLogic::OnConfigLoaded(filename);

	mBetsOpenTime = GetConfig("bets-open-timer").get<uint32_t>();
	mPauseAtRoundFinishTime = GetConfig("pause-at-round-finish-timer").get<uint32_t>();
	mNewRoundPreparationTime = GetConfig("new-round-preparation-timer").get<uint32_t>();
	mPauseBeforeRoundEndTime = GetConfigOptional("pause-before-round-end-timer", 3500).get<uint32_t>();
	mDecisionTime = GetConfig("decision-open-timer").get<uint32_t>();
	mAnimationTime = GetConfig("animation-timer").get<uint32_t>();
	mCutCardBarcode = GetConfig("cut-card-barcode").get<std::string>();
	bShouldCheckDuplicateCards = GetConfig("check-duplicate-cards").get<bool>();
	mAllowBetsCloseTime = GetConfig("early-bets-close-timer").get<uint64_t>();
	mPauseAtCardBurnEndTime = GetConfig("pause-at-card-burn-end-timer").get<uint64_t>();

	State->ActionsState.bChatEnabled = GetConfig("chat-enabled").get<bool>();

	State->VirtualDealerLogic = std::make_shared<TVirtualDealerLogic>(8, 69);

	mBetTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mBetTimer);

		  TLOG(LogBaccaratDealerAssist, Info, "Bets are closed. Dealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);

		  // Report dealing cards
		  if (GameType == EGameType::OpenBaccarat)
			  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
		  else
			  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);

		  if (bVirtualGame)
			  DealWithVirtualDealer();
	  },
	  mBetsOpenTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Open bets timer");

	mNewRoundPreparationTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mNewRoundPreparationTimer);
		  TLOG(LogBaccaratDealerAssist, Info, "Waiting for new round to begin is over.");
		  OnRoundBegin();
	  },
	  mNewRoundPreparationTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause when one round is closed and dealer wait for new round to begin.");

	mAnimationTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mAnimationTimer);

		  TLOG(LogBaccaratDealerAssist, Info, "Animation over");

		  RevealFirstSide();
	  },
	  mAnimationTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Animation timer");

	mPauseAfterCardBurnTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAfterCardBurnTimer);
		  ScopedLock lock(State);
		  OpenNewRound_AssumeWriteLock();
	  },
	  mPauseAtCardBurnEndTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause after last card burn and before new round begins.");

	mDecisionTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mDecisionTimer);

		  TLOG(LogBaccaratDealerAssist, Info, "Decision bets are closed. Revealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);

		  // Report dealing cards
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
		  TLOG(LogBaccaratDealerAssist, Info, "Phase changed to DealingCards - Dealing");
		  RevealRemainingSide();
	  },
	  mDecisionTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Decision bets timer");

	mPauseAtRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAtRoundEndTimer);
		  TLOG(LogBaccaratDealerAssist, Info, "Waiting at round end / void is over.");
		  ScopedLock lock(State);

		  if (CanCloseTable())
		  {
			  lock.unlock();
			  TLOG(LogBaccaratDealerAssist, Info, "Closing table.");
			  LogoutCroupier();
		  }
		  else if (State->ActionsState.bGameChangeRequested)
		  {
			  if (!CanChangeGame())
			  {
				  TLOG(LogBaccaratDealerAssist, Info, "Game change requested but waiting for countdown (%d/%d rounds). Opening new round.", State->RoundsPlayedCounter,
				       State->GameflowConfig.RoundsBeforeGameChange);
				  OpenNewRound_AssumeWriteLock();
			  }
			  else if (!State->SupervisorState->SelectedGameType.has_value())
			  {
				  TLOG(LogBaccaratDealerAssist, Info, "Could not change game, no game selected. Opening new round.");
				  OpenNewRound_AssumeWriteLock();
			  }
			  else
			  {
				  TLOG(LogBaccaratDealerAssist, Info, "Switching game.");
				  lock.unlock();

				  rtfwk_sdl2::pApp->Defer([this]() { OnSwitchGameEvent(); }, "Switch game event");
			  }
		  }
		  else if (State->bShouldBurnCards)
		  {
			  ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

			  // In case of virtual dealer we can skip shoe change conformation and move to card burn.
			  if (bVirtualGame)
			  {
				  ShoeChanged_AssumeWriteLock();
			  }
		  }
		  else
		  {
			  TLOG(LogBaccaratDealerAssist, Info, "Opening new round");
			  OpenNewRound_AssumeWriteLock();
		  }
	  },
	  mPauseAtRoundFinishTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause at round end timer");

	mCardRevealTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mCardRevealTimer);
		  TLOG(LogBaccaratDealerAssist, Info, "Revealing card");
		  RevealRemainingSide();
	  },
	  1500, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Reveal card timer");

	// Stop autostart of timers
	StopAllTimers();
}

// When backend is connected we request table history (from last cut card event)
void TBaccaratDealerAssistGameLogic::OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round)
{
	ScopedLock lock(State);
	State->Table.mErrorCode.reset();
	bool updateState = false;

	if (round.has_value() && round->Status == backend::ERoundStatus::Open)
	{
		if (!round->State.is_null())
		{
			State->Table = TableState::FromJSON(round->State);
			State->GameLogic->SetCurrentStateOfCards(State->Table.mCards);

			TLOG(LogBaccaratDealerAssist, Info, "Game was not finished on backend, restoring state.");
			updateState = true;
		}

		if (round->ID != State->Table.mRoundID)
		{
			State->Table.mRoundID = round->ID;
			TLOG(LogBaccaratDealerAssist, Info, "Game was not finished on backend, restoring game ID. Round ID: %i", State->Table.mRoundID);
			updateState = true;
		}
	}

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::WaitingForCroupier);
	lock.unlock();

	if (updateState)
		OnStateUpdate();

	DealerAssistBackendClient->GetTableHistory(GameType._to_string()).then(boost::launch::sync, [this](boost::future<std::vector<GameRecordDto>> futureHistory) {
		ScopedLock lock(State);
		State->GameRecords = futureHistory.get();
		TLOG(LogBaccaratDealerAssist, Info, "Received history with %i records", State->GameRecords.size());

		if (State->GameRecords.size() > 0)
			OnStateUpdate();
	});
}

void TBaccaratDealerAssistGameLogic::OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogBaccaratDealerAssist, Info, "Game switched.");

	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;

	StopAllTimers();

	State->bShouldBurnCards = true;
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

	if (bVirtualGame)
		ShoeChanged_AssumeWriteLock();
}

DealerAssistInitResponseDto TBaccaratDealerAssistGameLogic::GetInitPacket() const
{
	SharedScopedLock lockTableState(State);

	TableState tableState = State->Table;

	auto phase = State->Table.mPhase;

	auto hello = DealerAssistInitResponseDto();

	if (phase == EDealerAssistPhase::DealingCards || phase == EDealerAssistPhase::RoundEnd)
	{
		json result = json(json::value_t::object);
		result["bankerHandValue"] = State->GameLogic->GetHandValue(EBaccaratSide::Banker);
		result["playerHandValue"] = State->GameLogic->GetHandValue(EBaccaratSide::Player);

		tableState.mHandValue = std::move(result);
		tableState.mDealingPhase = State->GameLogic->GetDealingPhase();
		tableState.mCards = State->GameLogic->GetCurrentStateOfCards();
		tableState.mRoundID = State->Table.mRoundID;
	}

	if (phase == EDealerAssistPhase::BetsOpen)
	{
		auto now = ytime::GetSystemTimeMsec();
		auto remainingTimeInRound = mBetsOpenTime - (now - tableState.mTimestamp.value_or(0));
		auto remainingTimeInSeconds = remainingTimeInRound / 1000;
		tableState.mRemainingBettingTimeInSeconds = remainingTimeInSeconds;
	}

	if (phase == EDealerAssistPhase::CardBurn || State->ShoeChangeState.CutCardDrawn)
	{
		hello.mShoeChangeState = State->ShoeChangeState;
	}

	if (State->SupervisorState)
	{
		hello.mSupervisorState = *State->SupervisorState;
	}

	if (State->RoundsUntilChange)
		hello.mRoundsUntilChange = *State->RoundsUntilChange;

	hello.mBetTimeInSec = mBetsOpenTime / 1000;
	hello.mDecisionTimeInSec = mDecisionTime / 1000;
	hello.mAnimationTimeInSec = mAnimationTime / 1000;

	hello.mPlayerCount = State->NumOfAllPlayers;
	hello.mActionsState = State->ActionsState;

	if (State->Croupier)
	{
		hello.mCroupierInfo = *State->Croupier;
	}

	hello.mHistoryRecords = State->GameRecords;
	hello.mGameType = GameType._to_string();
	hello.mTableState = tableState;
	return hello;
}

void TBaccaratDealerAssistGameLogic::HandleScannedCard(const FScannedCard& data)
{
	ScopedLock lockTableState(State);
	if (data.Suite() == ECardSuite::None || data.Face() == ECardFace::None)
	{
		if (data.Barcode() == mCutCardBarcode)
			HandleCutCard_AssumeWriteLock();
		return;
	}

	if (State->SupervisorState->Active && State->ActionsState.bSupervisorCalled)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Ignoring card scan, supervisor called");
		// TODO: Return notification to FE
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::CardBurn)
	{
		HandleCardBurn_AssumeWriteLock(data);
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::BetsOpen)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Bets are open, check if we should close them. If card is dealt in the last  ms, close bets.");
		const uint64_t now = ytime::GetSystemTimeMsec();
		const uint64_t remainingTimeInRound = mBetsOpenTime - (now - State->Table.mTimestamp.value_or(0));

		TLOG(LogBaccaratDealerAssist, Info, "Remaining time in round: %i", remainingTimeInRound);

		if (remainingTimeInRound < mAllowBetsCloseTime)
		{
			TLOG(LogBaccaratDealerAssist, Info, "Close bets and add card");
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::BetsClosed);

			if (GameType == EGameType::OpenBaccarat)
				ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
			else
				ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);

			DealRegularBaccarat_AssumedLockedWrite(data);
			StopTimer(mBetTimer);

			return;
		}
	}

	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Ignoring card scan, not in dealing or card burn phase");
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::DealingCards && State->Table.mSubPhase.has_value() && State->Table.mSubPhase != EDealingSubPhase::FaceDown &&
	    State->Table.mSubPhase != EDealingSubPhase::Dealing)
	{
		TLOG(LogBaccaratDealerAssist, Warning, "Ignoring card scan, is in dealing phase but not in dealing subphase");
		return;
	}

	if (IsCardOnTable_AssumeLocked(data) && bShouldCheckDuplicateCards)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Ignoring card scan, card already on table");
		json context = json(json::value_t::object);
		context["reason"] = "Card already on table";
		auto dto = NotifyDealerConsoleDto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::DuplicateCard), context);
		dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
		OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);

		return;
	}

	if (bVirtualGame)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Ignoring card scan, virtual dealer is dealing cards");
		return;
	}

	if (State->GameLogic->IsGameFinished())
	{
		TLOG(LogBaccaratDealerAssist, Info, "Ignoring card scan, game already finished");
		return;
	}

	if (GameType == EGameType::OpenBaccarat && State->GameLogic->GetDealingPhase() <= EBaccaratDealingPhase(EBaccaratDealingPhase::BankerSecondCard)._to_integral())
	{
		DealFaceDownCards_AssumedLockedWrite(data);
	}
	else
	{
		DealRegularBaccarat_AssumedLockedWrite(data);
	}
}

void TBaccaratDealerAssistGameLogic::HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data)
{
	if (GameType == EGameType::OpenBaccarat && State->GameLogic->GetDealingPhase() <= EBaccaratDealingPhase(EBaccaratDealingPhase::BankerSecondCard)._to_integral())
	{
		DealFaceDownCards_AssumedLockedWrite(data);
	}
	else
	{
		DealRegularBaccarat_AssumedLockedWrite(data);
	}
}

void TBaccaratDealerAssistGameLogic::HandleCroupierLogin(const uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogBaccaratDealerAssist, Info, "Croupier login detected.");
	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;

	StopAllTimers();

	if (State->Table.mRoundID.has_value())    // Round already has value, we are in the middle of the round
		if (GameType == EGameType::OpenBaccarat)
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
		else
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
	else
	{
		State->bShouldBurnCards = true;
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

		if (bVirtualGame)
			ShoeChanged_AssumeWriteLock();
	}

	OnStateUpdate();
}

void TBaccaratDealerAssistGameLogic::OnRoundBegin()
{
	ScopedLock lockTableState(State);
	OnRoundBegin_AssumeWriteLock();
}

void TBaccaratDealerAssistGameLogic::OnRoundBegin_AssumeWriteLock()
{
	TLOG(LogBaccaratDealerAssist, Info, "New round begins - bets are open!");
	State->Table.mTimestamp = ytime::GetSystemTimeMsec();
	State->Table.mWinner.reset();

	ReportNumberOfPlayers_AssumeReadLock();
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::BetsOpen);

	StartTimer(mBetTimer);
}

void TBaccaratDealerAssistGameLogic::OnPauseBeforeRoundEnd()
{
	TLOG(LogBaccaratDealerAssist, Info, "Pausing before round end.");

	mPauseBeforeRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>([this]() { OnRoundEnd(); }, mPauseBeforeRoundEndTime,
	                                                                                   rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Pause before round end timer");
}

void TBaccaratDealerAssistGameLogic::OnRoundEnd()
{
	ScopedLock lockTableState(State);
	OnRoundEnd_AssumeWriteLock();
}

void TBaccaratDealerAssistGameLogic::OnRoundEnd_AssumeWriteLock()
{
	const auto winner = State->GameLogic->Evaluate();
	TLOG(LogBaccaratDealerAssist, Info, "Winner is: %s", EBaccaratWinner::_from_integral(winner[0])._to_string());

	if (bVirtualGame)
		State->VirtualDealerLogic->RoundFinished();

	CheckRoundClosing_AssumeWriteLock();
	CheckGameChangeRounds_AssumeWriteLock();

	SaveRoundState_AssumeReadLock(backend::ERoundStatus::Closed).then([this](boost::future<void> future) {
		try
		{
			future.get();
			ScopedLock lockTableState(State);
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundEnd);
			ResetCurrentRound_AssumeWriteLock();

			if (State->ActionsState.bChangeShoeRequested)
				State->bShouldBurnCards = true;

			// Pause before going in NewRoundPreparation
			TLOG(LogBaccaratDealerAssist, Info, "Pausing before new round begins.");
			StartTimer(mPauseAtRoundEndTimer);
		}
		catch (const DealerAssistException& ex)
		{
			TLOG(LogBaccaratDealerAssist, Warning, "DealerAssistException: %s", ex.what());

			if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
				OnTableCloseEvent();
		}
		catch (const std::exception& e)
		{
			TLOG(LogBaccaratDealerAssist, Warning, "Error on saving result: %s", e.what());
		}
	});

	ReportNumberOfPlayers_AssumeReadLock();
}

void TBaccaratDealerAssistGameLogic::AddCard_AssumeLocked(const uint32_t card)
{
	switch (State->GameLogic->GetDealingPhase())
	{
		case EBaccaratDealingPhase::PlayerFirstCard:
		case EBaccaratDealingPhase::PlayerSecondCard:
		case EBaccaratDealingPhase::PlayerThirdCard: State->GameLogic->AddCard(card, EBaccaratSide::Player); break;
		case EBaccaratDealingPhase::BankerFirstCard:
		case EBaccaratDealingPhase::BankerSecondCard:
		case EBaccaratDealingPhase::BankerThirdCard: State->GameLogic->AddCard(card, EBaccaratSide::Banker); break;
		default: break;
	}
}

bool TBaccaratDealerAssistGameLogic::IsCardOnTable_AssumeLocked(const FScannedCard& card) const
{
	return State->CardsOnTable.contains(card);
}

json TBaccaratDealerAssistGameLogic::ShoeChanged_AssumeWriteLock()
{
	TLOG(LogBaccaratDealerAssist, Info, "Shoe changed detected.");
	if (State->Table.mPhase == EDealerAssistPhase::ShoeChange)
	{
		State->CardsOnTable.clear();
		State->GameRecords.clear();

		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::CardBurn);

		if (bVirtualGame)
		{
			State->VirtualDealerLogic->ShuffleCards();
			BurnCardsWithVirtualDealer_AssumeWriteLock();
		}

		return {};
	}

	throw std::runtime_error("Shoe change detected in unexpected phase");
}

void TBaccaratDealerAssistGameLogic::ResetCurrentRound_AssumeWriteLock()
{
	mBetsStatistics->ClearAllBetAmounts();

	// Clear state and timers
	State->Table.Clear();
	State->GameLogic->ClearGame();

	StopAllTimers();
}

void TBaccaratDealerAssistGameLogic::DealRegularBaccarat_AssumedLockedWrite(const FScannedCard& data)
{
	// Get dealing phase before we add card
	const auto dealingPhase = State->GameLogic->GetDealingPhase();
	const auto cardValue = data.ToInt() % 1000;

	AddCard_AssumeLocked(cardValue);

	State->Table.mCardPosition = dealingPhase;
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	SaveIntermediateRoundState_AssumeReadLock();

	State->CardsOnTable.insert(data);

	bool bLastCard = State->GameLogic->GetDealingPhase() == EBaccaratDealingPhase::Finished;

	auto dto = AddBaccaratCardDto();
	dto.mScannedCard = cardValue;
	dto.mCardPosition = dealingPhase;
	dto.mBankerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Banker);
	dto.mPlayerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Player);

	if (bLastCard)
	{
		auto winners = State->GameLogic->Evaluate();
		State->Table.mWinner = winners.front();
		dto.mOutcome = *State->Table.mWinner;
	}
	else
	{
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();
	}

	OnBroadcastEvent(*mAddCardEvent, dto);

	if (State->GameLogic->GetDealingPhase() == EBaccaratDealingPhase::Finished)
	{
		OnPauseBeforeRoundEnd();
	}
	else if (bVirtualGame)
	{
		DealWithVirtualDealer_AssumeWriteLock();
	}
}

void TBaccaratDealerAssistGameLogic::DealFaceDownCards_AssumedLockedWrite(const FScannedCard& data)
{
	// Get dealing phase before we add card
	auto dealingPhase = State->GameLogic->GetDealingPhase();
	const auto cardValue = data.ToInt() % 1000;

	State->Table.mFaceDownCards[dealingPhase] = cardValue;
	AddCard_AssumeLocked(faceDownCard);

	State->Table.mCardPosition = dealingPhase;
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	SaveIntermediateRoundState_AssumeReadLock();

	State->CardsOnTable.insert(data);

	auto dto = AddBaccaratCardDto();
	dto.mScannedCard = faceDownCard;
	dto.mCardPosition = dealingPhase;

	if (dealingPhase != EBaccaratDealingPhase::BankerSecondCard)
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();


	OnBroadcastEvent(*mAddCardEvent, dto);

	if (dealingPhase == EBaccaratDealingPhase::BankerSecondCard)
	{
		TLOG(LogBaccaratDealerAssist, Info, "Start choosing side!");
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::SideSelectionAnimation);
		StartTimer(mAnimationTimer);
	}
	else if (bVirtualGame)
	{
		DealWithVirtualDealer_AssumeWriteLock();
	}
}

void TBaccaratDealerAssistGameLogic::RevealFirstSide()
{
	ScopedLock lock(State);

	EBaccaratSide side = EBaccaratSide::_from_index(crypto::GetRandomInRange(0, 1));
	TLOG(LogBaccaratDealerAssist, Info, "Revealing first side: %s", side._to_string());

	auto subPhase = side == EBaccaratSide::Player ? EDealingSubPhase::LeftSide : EDealingSubPhase::RightSide;

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, subPhase);
	State->Table.mSelectedSide = side;

	const EBaccaratDealingPhase firstCardPosition = (side == EBaccaratSide::Player) ? EBaccaratDealingPhase::PlayerFirstCard : EBaccaratDealingPhase::BankerFirstCard;
	const EBaccaratDealingPhase secondCardPosition = (side == EBaccaratSide::Player) ? EBaccaratDealingPhase::PlayerSecondCard : EBaccaratDealingPhase::BankerSecondCard;

	RevealCard(firstCardPosition, side, 0);
	RevealCard(secondCardPosition, side, 1);

	SaveIntermediateRoundState_AssumeReadLock();

	TLOG(LogBaccaratDealerAssist, Info, "Hand value: %i", State->GameLogic->GetHandValue(side));

	if (State->GameflowConfig.SkipDecisionIfNoBets && !mBetsStatistics->HasBetsOnFields(mOpenBetTypes))
	{
		TLOG(LogBaccaratDealerAssist, Info, "No bet on open fields, skip Decision phase.");
		TLOG(LogBaccaratDealerAssist, Info, "Deal remaining side!");
		StartTimer(mCardRevealTimer);
	}
	else
	{
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::Decision);
		StartTimer(mDecisionTimer);
	}
}

void TBaccaratDealerAssistGameLogic::RevealRemainingSide()
{
	ScopedLock lock(State);

	RevealRemainingSide_AssumedLockedWrite();
}

void TBaccaratDealerAssistGameLogic::RevealRemainingSide_AssumedLockedWrite()
{
	EBaccaratSide otherside = (State->Table.mSelectedSide == EBaccaratSide::Player) ? EBaccaratSide::Banker : EBaccaratSide::Player;
	const EBaccaratDealingPhase firstCardPosition =
	  (otherside == EBaccaratSide::Player) ? EBaccaratDealingPhase::PlayerFirstCard : EBaccaratDealingPhase::BankerFirstCard;
	const EBaccaratDealingPhase secondCardPosition =
	  (otherside == EBaccaratSide::Player) ? EBaccaratDealingPhase::PlayerSecondCard : EBaccaratDealingPhase::BankerSecondCard;

	TLOG(LogBaccaratDealerAssist, Info, "Revealing remaining side: %s", otherside._to_string());

	RevealCard(firstCardPosition, otherside, 0, false);
	RevealCard(secondCardPosition, otherside, 1, true);

	if (State->GameLogic->GetDealingPhase() == EBaccaratDealingPhase::Finished)
	{
		OnPauseBeforeRoundEnd();
	}
	else
	{
		TLOG(LogBaccaratDealerAssist, Info, "Deal additional cards!");

		if (bVirtualGame)
			DealWithVirtualDealer_AssumeWriteLock();
	}

	TLOG(LogBaccaratDealerAssist, Info, "Hand value: %i", State->GameLogic->GetHandValue(otherside));
}

void TBaccaratDealerAssistGameLogic::RevealCard(const EBaccaratDealingPhase cardPosition, const EBaccaratSide side, const uint8_t cardIndexInHand, const bool lastRound)
{
	const auto card = State->Table.mFaceDownCards[cardPosition];
	State->GameLogic->AddOrReplaceCard(card, side, cardIndexInHand);
	State->Table.mFaceDownCards.erase(cardPosition);

	TLOG(LogBaccaratDealerAssist, Info, "Revealing card: %i", card);

	AddBaccaratCardDto dto;
	dto.mScannedCard = card;
	dto.mCardPosition = cardPosition._to_integral();

	if (lastRound)
	{
		dto.mPlayerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Player);
		dto.mBankerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Banker);
	}
	else if (side == EBaccaratSide::Player)
		dto.mPlayerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Player);
	else
		dto.mBankerHandValue = State->GameLogic->GetHandValue(EBaccaratSide::Banker);

	if (State->GameLogic->GetDealingPhase() == EBaccaratDealingPhase::Finished && lastRound && cardIndexInHand == 1)
	{
		auto winners = State->GameLogic->Evaluate();
		State->Table.mWinner = winners.front();
		dto.mOutcome = State->Table.mWinner;
	}
	else if (lastRound)
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();

	TLOG(LogBaccaratDealerAssist, Info, "Dto value: %s", JsonSchema::PrintValueInline(dto.ToJSON()).c_str());
	OnBroadcastEvent(*mAddCardEvent, dto);
}

void TBaccaratDealerAssistGameLogic::UpdateTableState_AssumeWriteLock()
{
	json result = json(json::value_t::object);
	result["bankerHandValue"] = State->GameLogic->GetHandValue(EBaccaratSide::Banker);
	result["playerHandValue"] = State->GameLogic->GetHandValue(EBaccaratSide::Player);

	State->Table.mHandValue = std::move(result);
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	if (State->GameLogic->IsGameFinished())
	{
		State->Table.mWinner = State->GameLogic->Evaluate().front();
	}
	else
	{
		State->Table.mWinner.reset();
		State->Table.mWinners.reset();
	}
}

bool TBaccaratDealerAssistGameLogic::CanRevealRemainingCards_AssumeReadLock()
{
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
		return false;

	if (State->Table.mSubPhase != EDealingSubPhase::LeftSide && State->Table.mSubPhase != EDealingSubPhase::RightSide)
		return false;

	return State->GameLogic->HasFaceDownCard(EBaccaratSide::Player) != State->GameLogic->HasFaceDownCard(EBaccaratSide::Banker);
}
