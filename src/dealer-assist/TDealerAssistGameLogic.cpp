//
// Created by <PERSON><PERSON><PERSON> on 7. 12. 24.
//

#include "TDealerAssistGameLogic.h"

#include "TApplication.h"
#include "dealer-assist/dto/NotifyDealerConsoleDto.h"
#include "dealer-assist/dto/ReportNumOfActivePlayersDto.h"
#include "dealer-assist/dto/ResultChangeResponseDto.h"
#include "dealer-assist/dto/RoundsUntilCloseDto.h"
#include "dealer-assist/dto/ToggleChatDto.h"
#include "dto/ResultChangeRequestDto.h"

using namespace dealer_assist;
using namespace dealer_assist::backend;

DEFINE_LOG_CATEGORY(LogDealerAssistGameLogic, "dealer-assist-game-logic")

TDealerAssistGameLogic::TDealerAssistGameLogic(EGameType gameType, std::string tableId, const bool virtualGame) :
    GameType(gameType), TableId(std::move(tableId)), bVirtualGame(virtualGame)
{
	Schem<PERSON>() += DealerGameInfoDto::GameConfigSchema();

	mAddCardEvent = std::make_shared<yserver::EventTemplate>(ADD_CARD_EVENT_NAME, "When a card is scanned");
	mNotifyDealerConsoleEvent =
	  std::make_shared<yserver::EventTemplate>(DEALER_ASSIST_TABLE_NOTIFICATION_EVENT_NAME, "When dealer assist console needs to be notified about info or warning.");
	mGameStateChangedEvent = std::make_shared<yserver::EventTemplate>(GAME_PHASE_CHANGED_EVENT_NAME, "Game state is changed (phase, round number, etc.)");
	mCardBurnEvent = std::make_shared<yserver::EventTemplate>(CARD_BURN_EVENT_NAME, "Card burning process");
	mReportPlayersEvent = std::make_shared<yserver::EventTemplate>(PLAYER_COUNT_CHANGED_EVENT_NAME, "Report number of active players");
	mCutCardEvent = std::make_shared<yserver::EventTemplate>(CUT_CARD_DRAWN_EVENT_NAME, "Cut card detected");
	mFlagStateChangeEvent = std::make_shared<yserver::EventTemplate>(FLAG_STATE_CHANGED_EVENT_NAME, "Flag state changed");
	mToggleChatEvent = std::make_shared<yserver::EventTemplate>(TOGGLE_CHAT_EVENT_NAME, "Toggle chat");
	mReportBetStatisticsEvent = std::make_shared<yserver::EventTemplate>(REPORT_BET_STATISTICS_RESULT, "Report result of bet statistics");
	mGameResultChangedEvent = std::make_shared<yserver::EventTemplate>(GAME_RESULT_CHANGED_EVENT_NAME, "Game result changed");
	mSupervisorStateChangedEvent = std::make_shared<yserver::EventTemplate>(SUPERVISOR_STATE_CHANGED_EVENT_NAME, "Supervisor state changed");
	mRoundsUntilCloseEvent = std::make_shared<yserver::EventTemplate>(ROUNDS_UNTIL_CHANGE_EVENT_NAME, "Report rounds remaining until table close");

	State->SupervisorState = std::make_shared<SupervisorState>();
}

void TDealerAssistGameLogic::OnConfigLoaded(const std::filesystem::path& filename)
{
	mBetsStatistics = std::make_unique<CardGameBetsStatistic>(GetConfig("bets-statistics"));
	mBetsStatistics->OnUpdate += [this](const BetStatisticsDto& betStatistics) {
		OnBroadcastEvent(*mReportBetStatisticsEvent, betStatistics);
	};
}

void TDealerAssistGameLogic::OnDealerAssistBackendDisconnected()
{
	ScopedLock lock(State);
	ResetCurrentRound_AssumeWriteLock();
	State->Croupier.reset();

	State->Table.mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundError);
}

DealerAssistTableState TDealerAssistGameLogic::GetTableState() const
{
	return State.getCopy();
}

// -- Handling croupier --

bool TDealerAssistGameLogic::CanLogoutCroupier()
{
	SharedScopedLock lockTableState(State);
	auto phase = State->Table.mPhase;
	if (phase == EDealerAssistPhase::WaitingForCroupier || phase == EDealerAssistPhase::WaitingForPlayers || phase == EDealerAssistPhase::RoundEnd ||
	    phase == EDealerAssistPhase::NewRoundPreparation)
	{
		return true;
	}

	TLOG(LogDealerAssistGameLogic, Info, "Cannot logout croupier, game in progress.");
	return false;
}

bool TDealerAssistGameLogic::CanSwitchCroupier()
{
	SharedScopedLock lockTableState(State);
	auto phase = State->Table.mPhase;
	if (phase == EDealerAssistPhase::WaitingForCroupier || phase == EDealerAssistPhase::WaitingForPlayers || phase == EDealerAssistPhase::RoundEnd ||
	    phase == EDealerAssistPhase::NewRoundPreparation || phase == EDealerAssistPhase::BetsOpen)
	{
		return true;
	}

	TLOG(LogDealerAssistGameLogic, Info, "Cannot switch croupier.");
	return false;
}

void TDealerAssistGameLogic::HandleCroupierSwitch(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;
}

// --  Bet statistics --

void TDealerAssistGameLogic::AddHostBetsStatistics(const std::string& hostId, const std::unordered_map<std::string, BetTypeHostStats>& bets)
{
	mBetsStatistics->SetBetAmounts(hostId, bets);
}

void TDealerAssistGameLogic::ClearBetsStatisticsForHost(const std::string& hostId)
{
	mBetsStatistics->ClearBetAmounts(hostId);
}

// --  Handle open / close rounds --

void TDealerAssistGameLogic::OpenNewRound(uint32_t ID)
{
	ScopedLock lockTableState(State);
	State->Table.mRoundID = ID;
	State->Table.mErrorCode.reset();

	// New round is open, reset flags related to previous round
	State->ActionsState.bRoundFlagged = false;

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::NewRoundPreparation);

	StartTimer(mNewRoundPreparationTimer);
}

void TDealerAssistGameLogic::StartTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer)
{
	if (const auto task = timer.lock())
		task->Enable();
}
void TDealerAssistGameLogic::StopTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer)
{
	if (const auto task = timer.lock())
		task->Disable();
}

void TDealerAssistGameLogic::StopAllTimers()
{
	StopTimer(mAnimationTimer);
	StopTimer(mBetTimer);
	StopTimer(mDecisionTimer);
	StopTimer(mNewRoundPreparationTimer);
	StopTimer(mPauseAtRoundEndTimer);
	StopTimer(mRetryTimer);
	StopTimer(mPauseBeforeRoundEndTimer);
	StopTimer(mPauseAfterCardBurnTimer);
	StopTimer(mPauseBetweenCardsTimer);
	StopTimer(mCardRevealTimer);
}

void TDealerAssistGameLogic::ChangeGamePhaseAndNotify(const EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase)
{
	ScopedLock lockTableState(State);
	ChangeGamePhaseAndNotify_AssumeWriteLock(newPhase);
}

void TDealerAssistGameLogic::ChangeGamePhaseAndNotify_AssumeWriteLock(const EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase)
{
	TLOG(LogDealerAssistGameLogic, Info, "Game phase changed from %s to %s", State->Table.mPhase._to_string(), newPhase._to_string());

	State->Table.mPhase = newPhase;
	State->Table.mSubPhase = subPhase;

	DealerAssistStateUpdateDto stateUpdate = DealerAssistStateUpdateDto(State->Table.mPhase, State->Table.mRoundID.value_or(0));

	if (subPhase)
	{
		TLOG(LogDealerAssistGameLogic, Info, "Sub phase changed to %i", subPhase);
		stateUpdate.subPhase = *subPhase;
	}

	if (State->Table.mPhase == EDealerAssistPhase::RoundEnd || State->Table.mPhase == EDealerAssistPhase::NewRoundPreparation)
	{
		if (State->Table.mWinners)
			stateUpdate.SetWinningWagers(State->Table.mWinners.value());

		if (State->Table.mWinner)
			stateUpdate.SetWinningWager(State->Table.mWinner.value());
	}

	if (State->Table.mErrorCode)
		stateUpdate.SetErrorCode(*State->Table.mErrorCode);

	OnBroadcastPhaseChangedEvent(*mGameStateChangedEvent, stateUpdate);
}

void TDealerAssistGameLogic::SupervisorCalled_AssumeWriteLock()
{
	TLOG(LogDealerAssistGameLogic, Info, "Supervisor called");

	StopAllTimers();

	State->SupervisorState->Active = true;
	State->ActionsState.bSupervisorCalled = true;
	State->SupervisorState->SubPhase = ESupervisorSubPhase::WaitingForSupervisor;
	ReportFlagStateChange(EFlagStateType::SupervisorCalled, true);
	OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
}

json RedrawCards_AssumeLocked()
{
	return {};
}

void TDealerAssistGameLogic::FlagRound_AssumeWriteLock(const EFlagRoundReason& event)
{
	auto future = DealerAssistBackendClient->FlagRound(State->Table.mRoundID.value_or(0), event._to_string());

	if (const YResponse response = future.get(); response.Status == EMessageStatus::ResponseOk)
	{
		TLOG(LogDealerAssistGameLogic, Info, "Round flagged successfully.")
		if (event == EFlagRoundReason::CroupierFlagged)
		{
			State->ActionsState.bRoundFlagged = true;

			auto dto = FlagStateChangeDto(EFlagStateType(EFlagStateType::RoundFlagged), true);
			dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
			OnBroadcastEvent(*mFlagStateChangeEvent, dto);
		}
	}
	else
		TLOG(LogDealerAssistGameLogic, Error, "Round flagging failed with error: %s", response.ErrorMessage().c_str());
}

void TDealerAssistGameLogic::LogoutCroupier() const
{
	json auth(json::value_t::object);
	auth["accessLevel"] = EDealerAssistAccessLevel(EDealerAssistAccessLevel::None)._to_string();
	auth["id"] = std::string();
	auth["login"] = true;

	DealerAssistBackendClient->Request(EDealerAssistRequest(EDealerAssistRequest::Authenticate)._to_string(), auth)
	  .then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		  YResponse response = fut.get();
		  if (response.Status == EMessageStatus::ResponseOk)
		  {
			  TLOG(LogDealerAssistGameLogic, Info, "Requested table closing successfully triggered.");
			  FlagStateChangeDto flagStateChanged(EFlagStateType(EFlagStateType::TableClosingRequested), true);
			  OnBroadcastEvent(*mFlagStateChangeEvent, flagStateChanged);
		  }
		  else
		  {
			  TLOG(LogDealerAssistGameLogic, Error, "Dealer authentication failed with error: %s", response.ErrorMessage().c_str());
			  NotifyDealerConsoleDto dto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::AuthenticationFailed), response.ErrorMessage().c_str());
			  dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
			  OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);
		  }
	  });
}

boost::future<void> TDealerAssistGameLogic::SaveGameResultBeforeLogout()
{
	SharedScopedLock lockTableState(State);
	return SaveGameResultBeforeLogout_AssumeReadLock();
}

boost::future<void> TDealerAssistGameLogic::SaveGameResultBeforeLogout_AssumeReadLock()
{
	// If game is in progress, save result as void
	if (State->Table.mPhase == EDealerAssistPhase::NewRoundPreparation || State->Table.mPhase == EDealerAssistPhase::BetsOpen ||
	    State->Table.mPhase == EDealerAssistPhase::BetsClosed || State->Table.mPhase == EDealerAssistPhase::DealingCards)
	{
		auto promise = std::make_shared<boost::promise<void>>();
		boost::future<void> resultFuture = promise->get_future();

		auto status = backend::ERoundStatus::Cancelled;

		if (State->Table.mRoundID.has_value())
			status = backend::ERoundStatus::Void;

		SaveRoundState_AssumeReadLock(status).then([this, promise](boost::future<void> future) {
			try
			{
				future.get();
				ScopedLock lock(State);
				FlagRound_AssumeWriteLock(EFlagRoundReason(EFlagRoundReason::RoundVoid));
				ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundVoid);
				ResetCurrentRound_AssumeWriteLock();
				promise->set_value();
			}
			catch (const std::exception& e)
			{
				promise->set_exception(std::current_exception());
			}
		});

		return resultFuture;
	}

	boost::promise<void> promise;
	promise.set_value();
	return promise.get_future();
}

boost::future<void> TDealerAssistGameLogic::SaveRoundState_AssumeReadLock(const ERoundStatus status)
{
	json roundState(json::value_t::object);

	if (State->Table.mRoundID)
		roundState["round"] = *State->Table.mRoundID;

	roundState["status"] = status._to_string();
	roundState["gameType"] = GameType._to_string();

	if (status == ERoundStatus::Closed)
	{
		auto gameRecord = State->GameLogic->GetGameRecord();
		if (State->Table.mRoundID)
			gameRecord.GameRoundId = *State->Table.mRoundID;

		roundState["state"] = gameRecord.ToJSON();
	}
	else
		roundState["state"] = State->Table.ToJSON();

	TLOG(LogDealerAssistGameLogic, Info, "Saving result for round %i", State->Table.mRoundID);

	auto promise = std::make_shared<boost::promise<void>>();
	auto future = promise->get_future();

	TrySaveRound(roundState, status, promise);

	return future;
}

void TDealerAssistGameLogic::TrySaveRound(json roundState, ERoundStatus status, std::shared_ptr<boost::promise<void>> promise)
{
	DealerAssistBackendClient->SaveResult(roundState).then(boost::launch::sync, [this, roundState, status, promise](boost::future<YResponse> respFut) {
		try
		{
			auto response = respFut.get();

			if (response.Status == EMessageStatus::ResponseOk)
			{
				// Saving successful, we can reset the error code is exist.
				ScopedLock lock(State);
				State->Table.mErrorCode.reset();

				TLOG(LogDealerAssistGameLogic, Info, "Response on saving round: %s", JsonSchema::PrintValueInline(response.Message.Body()).c_str());
				if (status == ERoundStatus::Closed)
				{
					auto rec = State->GameLogic->GetGameRecord();
					rec.GameRoundId = State->Table.mRoundID.value_or(0);
					State->GameRecords.push_back(std::move(rec));
				}

				promise->set_value();
				return;
			}

			auto errObj = response.Message.Body();
			if (const auto ctx = FindMember(errObj, "context"))
			{
				if (const auto code = FindMember(*ctx, "code"); code && code->is_string())
				{
					const auto s = code->get<std::string>();

					if (s == EDealerAssistErrorCode(EDealerAssistErrorCode::RoundIsClosed)._to_string())
					{
						TLOG(LogDealerAssistGameLogic, Info, "Round already closed, continue with flow.");
						promise->set_value();
						return;
					}
					if (s == EDealerAssistErrorCode(EDealerAssistErrorCode::NotLoggedIn)._to_string())
					{
						promise->set_exception(boost::copy_exception(DealerAssistException(EDealerAssistErrorCode::NotLoggedIn)));
						return;
					}
				}
			}

			{
				ScopedLock lock(State);
				State->Table.mErrorCode = EDealerAssistTableErrorCode::RoundResultSavingFailed;
				ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundError);
				TLOG(LogDealerAssistGameLogic, Error, "Error on saving round: %s. Retrying in 2s.", response.ErrorMessage().c_str());
			}

			// Schedule next retry with the *same* promise
			mRetryTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
			  [this, status, roundState, promise]() {
				  ScopedLock lock(State);
				  TrySaveRound(roundState, status, promise);
			  },
			  2000, rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Retry saving round state");
		}
		catch (...)
		{
			promise->set_exception(boost::current_exception());
		}
	});
}

void TDealerAssistGameLogic::OpenNewRound_AssumeWriteLock()
{
	State->ShoeChangeState = CardBurnDto();
	ReportNumberOfPlayers_AssumeReadLock();

	DealerAssistBackendClient->OpenNewRound(GameType._to_string()).then(boost::launch::sync, [this](boost::future<YResponse> future) {
		YResponse response = future.get();
		if (response.Status == EMessageStatus::ResponseOk)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Response on opening new round: %s", JsonSchema::PrintValueInline(response.Message.Body()).c_str())
			auto roundInfo = RoundInfoDto::FromJSON(response.Message.Body());

			OpenNewRound(roundInfo.ID);
		}
		else
		{
			TLOG(LogDealerAssistGameLogic, Error, "Error on opening new round: %s", response.Message.Body().dump().c_str());
			auto errorObj = response.Message.Body();

			const json* errContext = FindMember(errorObj, "context");
			auto errorCode = FindMember(*errContext, "code");

			if (errorCode && errorCode->is_string())
			{
				if (errorCode->get<std::string>() == EDealerAssistErrorCode(EDealerAssistErrorCode::NotLoggedIn)._to_string())
				{
					TLOG(LogDealerAssistGameLogic, Info, "No worker logged in. Moving to waiting for croupier phase.");
					// TODO: Close table
					return;
				}
			}

			ScopedLock lock(State);
			State->Table.mErrorCode = EDealerAssistTableErrorCode::RoundOpeningFailed;
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundError);

			mRetryTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
			  [this]() {
				  ScopedLock lock(State);
				  OpenNewRound_AssumeWriteLock();
			  },
			  2000, rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Repeat request for opening new round after 2 seconds.");
		}
	});
}

void TDealerAssistGameLogic::HandleSupervisorLogin(const DealerAssistWorker& supervisorInfo)
{
	ScopedLock lock(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(supervisorInfo);

	if (State->ActionsState.bSupervisorCalled && State->SupervisorState->SubPhase == ESupervisorSubPhase::WaitingForSupervisor)
	{
		State->SupervisorState->SubPhase = ESupervisorSubPhase::ActionSelection;
		OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
	}
	else
	{
		TLOG(LogDealerAssistGameLogic, Info, "Supervisor login not allowed at this phase of game.");
		const auto dto = NotifyDealerConsoleDto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::CroupierChangeInvalidState),
		                                        "Supervisor login not allowed at this phase of game.");
		OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);
	}
}

void TDealerAssistGameLogic::HandleSupervisorLogout()
{
	ScopedLock lock(State);

	TLOG(LogDealerAssistGameLogic, Info, "Supervisor logout handling.");
	State->Croupier.reset();

	// If we are in state where we wait for conformation
	if (State->ActionsState.bSupervisorCalled && State->SupervisorState->SubPhase == ESupervisorSubPhase::ConfirmAction)
	{
		// If there was requested action
		if (!State->SupervisorState->RequestedAction.has_value())
		{
			TLOG(LogDealerAssistGameLogic, Info, "No previous dealing phase found, moving to waiting for croupier phase.");
		}
		else if (State->SupervisorState->RequestedAction == ESupervisorAction::CloseTable)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Confirmed table closing");
			State->ActionsState.bTableCloseRequested = true;
			ReportFlagStateChange(EFlagStateType::TableClosingRequested, true);

			if (State->Table.mPhase == EDealerAssistPhase::WaitingForCroupier)
			{
				State->SupervisorState->RequestedAction.reset();
				State->SupervisorState->Reset();
				lock.unlock();
				OnTableCloseEvent();
				return;
			}
		}
		else if (State->SupervisorState->RequestedAction == ESupervisorAction::VoidGame)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Confirmed Void Game - change phase - waiting for croupier");
		}
		else if (State->SupervisorState->RequestedAction == ESupervisorAction::FreeHand)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Confirmed Free Hand - notify host");
			State->ActionsState.bFreeHandEnabled = true;
			ReportFlagStateChange(EFlagStateType::FreeHandEnabled, true);
		}
		else
		{
			TLOG(LogDealerAssistGameLogic, Info, "Unhandled supervisor action %s", State->SupervisorState->RequestedAction.value()._to_string());
		}
	}
	else
	{
		TLOG(LogDealerAssistGameLogic, Info, "Not in correct supervisor state - returning to previous state");
	}

	State->ActionsState.bSupervisorCalled = false;
	ReportFlagStateChange(EFlagStateType::SupervisorCalled, false);

	if (State->Table.mPhase != EDealerAssistPhase::WaitingForCroupier)
		OnReloginCroupierEvent();
	else
	{
		State->SupervisorState->Reset();
		OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
	}
}

json TDealerAssistGameLogic::HandleDealerAction(const EDealerAction& action)
{
	ScopedLock lock(State);
	switch (action)
	{
		case EDealerAction::CompleteRound: TLOG(LogDealerAssistGameLogic, Warning, "Implement complete round"); return {};

		case EDealerAction::ShoeChanged: return ShoeChanged_AssumeWriteLock();

		case EDealerAction::RedrawCards: return RedrawCards_AssumeWriteLock();

		case EDealerAction::CallSupervisor: SupervisorCalled_AssumeWriteLock(); return {};

		default: TLOG(LogDealerAssistGameLogic, Info, "Ignoring dealer action %s", action._to_string()); return {};
	}
}

json TDealerAssistGameLogic::HandleSupervisorAction(const ESupervisorAction& action)
{
	ScopedLock lock(State);

	if (!State->ActionsState.bSupervisorCalled)
	{
		TLOG(LogDealerAssistGameLogic, Info, "Change shoe requested but supervisor not called");
		throw std::runtime_error("Change shoe requested but supervisor not called");
	}

	switch (action)
	{
		case ESupervisorAction::VoidGame:
			TLOG(LogDealerAssistGameLogic, Info, "Void requested with: %s and %s", State->Table.mPhase._to_string(),
			     ESupervisorSubPhase::_from_integral(State->Table.mSubPhase.value_or(0))._to_string());

			State->SupervisorState->RequestedAction = ESupervisorAction::VoidGame;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;

			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		case ESupervisorAction::ChangeShoe:
			TLOG(LogDealerAssistGameLogic, Info, "Change shoe requested");
			State->ActionsState.bChangeShoeRequested = !State->ActionsState.bChangeShoeRequested;
			ReportFlagStateChange(EFlagStateType::ChangeShoeRequested, State->ActionsState.bChangeShoeRequested);
			return {};

		case ESupervisorAction::ChangeGame:
			TLOG(LogDealerAssistGameLogic, Info, "Change game requested");
			State->SupervisorState->RequestedAction = ESupervisorAction::ChangeGame;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::GameSelection;
			State->ActionsState.bGameChangeRequested = true;
			State->ActionsState.bTableCloseRequested = false;

			State->RoundsUntilChange =
			  RoundsUntilCloseDto(State->GameflowConfig.RoundsBeforeGameChange, State->GameflowConfig.RoundsBeforeGameChange, ERoundsCountdownType::GameChange);
			State->RoundsPlayedCounter = 0;
			ReportRoundsUntilChange_AssumeWriteLock(ERoundsCountdownType::GameChange);

			ReportFlagStateChange(EFlagStateType::GameChangingRequested, true);
			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		case ESupervisorAction::ChangeResult:
			TLOG(LogDealerAssistGameLogic, Info, "Change result requested");
			State->SupervisorState->RequestedAction = ESupervisorAction::ChangeResult;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;
			State->ActionsState.bResultChangeRequested = true;
			ReportFlagStateChange(EFlagStateType::ResultChangingRequested, true);
			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		case ESupervisorAction::CloseTable:
			TLOG(LogDealerAssistGameLogic, Info, "Close table requested");
			State->SupervisorState->RequestedAction = ESupervisorAction::CloseTable;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;
			State->ActionsState.bTableCloseRequested = true;
			State->ActionsState.bGameChangeRequested = false;

			State->RoundsPlayedCounter = 0;
			State->RoundsUntilChange =
			  RoundsUntilCloseDto(State->GameflowConfig.RoundsBeforeClose, State->GameflowConfig.RoundsBeforeClose, ERoundsCountdownType::TableClose);
			ReportRoundsUntilChange_AssumeWriteLock(ERoundsCountdownType::TableClose);

			ReportFlagStateChange(EFlagStateType::TableClosingRequested, true);
			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		case ESupervisorAction::ContinueGame:
			TLOG(LogDealerAssistGameLogic, Info, "Continue game requested");
			State->SupervisorState->RequestedAction = ESupervisorAction::ContinueGame;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;
			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		case ESupervisorAction::FreeHand:
			TLOG(LogDealerAssistGameLogic, Info, "Free hand requested");
			State->SupervisorState->RequestedAction = ESupervisorAction::FreeHand;
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;
			OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
			return {};

		default: TLOG(LogDealerAssistGameLogic, Info, "Ignoring dealer action %s", action._to_string()); return {};
	}
}

json TDealerAssistGameLogic::HandleFlagStateChange(const FlagStateChangeDto& flagStateChanged)
{
	ScopedLock lock(State);

	switch (flagStateChanged.GetFlag())
	{
		case EFlagStateType::ChatEnabled: {
			State->ActionsState.bChatEnabled = flagStateChanged.GetState();
			auto dto = ToggleChatDto(State->ActionsState.bChatEnabled);
			dto.Recipient = EDealerAssistEventRecipient::Gamehost;

			OnBroadcastEvent(*mToggleChatEvent, dto);
			ReportFlagStateChange(flagStateChanged.GetFlag(), flagStateChanged.GetState());
			break;
		}

		case EFlagStateType::RoundFlagged: FlagRound_AssumeWriteLock(EFlagRoundReason(EFlagRoundReason::CroupierFlagged)); break;

		default: TLOG(LogDealerAssistGameLogic, Info, "Ignoring flag state change %s", flagStateChanged.GetFlag()._to_string()); break;
	}
	return {};
}

void TDealerAssistGameLogic::HandleCroupierRelogin(const DealerAssistWorker& croupierInfo)
{
	TLOG(LogDealerAssistGameLogic, Info, "Croupier re-login detected.");
	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);

	if (State->SupervisorState->RequestedAction == ESupervisorAction::VoidGame)
		VoidRound_AssumeWriteLock();
	else if (State->SupervisorState->RequestedAction == ESupervisorAction::ChangeResult)
	{
		if (!State->SupervisorState->ChangedGameResult.empty())
		{
			const auto dto = ResultChangeRequestDto::FromJSON(State->SupervisorState->ChangedGameResult);
			State->GameLogic->ChangeGameResult(dto.GetUpdatedCards());
			UpdateTableState_AssumeWriteLock();

			const ResultChangeResponseDto gameResultChangedDto = ResultChangeResponseDto(State->Table);
			OnBroadcastEvent(*mGameResultChangedEvent, gameResultChangedDto);
			OnStateUpdate();
		}

		State->ActionsState.bResultChangeRequested = false;
		ReportFlagStateChange(EFlagStateType::ResultChangingRequested, false);

		ProceedWithGame_AssumeWriteLock();
	}
	else
	{
		ProceedWithGame_AssumeWriteLock();
	}

	State->SupervisorState->Reset();
	OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
}

void TDealerAssistGameLogic::SetDealerAssistClient(const std::shared_ptr<DealerAssistTableBackendClient>& client)
{
	DealerAssistBackendClient = client;

	if (DealerAssistBackendClient->Connected())
	{
		ScopedLock lock(State);
		State->Table.mErrorCode.reset();
	}
}


void TDealerAssistGameLogic::HandleTableClear()
{
	ScopedLock lockTableState(State);
	TLOG(LogDealerAssistGameLogic, Info, "Remote table clear detected.");
	VoidRound_AssumeWriteLock();
}

void TDealerAssistGameLogic::ProceedWithGame_AssumeWriteLock()
{
	TLOG(LogDealerAssistGameLogic, Info, "Proceed with game.");

	if (State->Table.mPhase == EDealerAssistPhase::BetsOpen)
	{
		OnRoundBegin_AssumeWriteLock();
	}
	else if (State->Table.mPhase == EDealerAssistPhase::NewRoundPreparation)
	{
		StartTimer(mNewRoundPreparationTimer);
	}
	else if (State->Table.mPhase == EDealerAssistPhase::DealingCards)
	{
		if (State->GameLogic->IsGameFinished())
			OnPauseBeforeRoundEnd();
		else if (State->Table.mSubPhase == EDealingSubPhase::SideSelectionAnimation)
		{
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::SideSelectionAnimation);
			StartTimer(mAnimationTimer);
		}
		if (CanRevealRemainingCards_AssumeReadLock())
		{
			StartTimer(mCardRevealTimer);
		}
		else if (bVirtualGame)
		{
			DealWithVirtualDealer_AssumeWriteLock();
		}
	}
	else if (State->Table.mPhase == EDealerAssistPhase::BetsClosed)
	{
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards);
	}
	else if (State->Table.mPhase == EDealerAssistPhase::RoundEnd)
	{
		ResetCurrentRound_AssumeWriteLock();
		StartTimer(mPauseAtRoundEndTimer);
	}
	else if (State->Table.mPhase == EDealerAssistPhase::Decision)
	{
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::Decision);
		StartTimer(mDecisionTimer);
	}
}

void TDealerAssistGameLogic::ReportFlagStateChange(const EFlagStateType type, const bool state) const
{
	const FlagStateChangeDto dto = FlagStateChangeDto(type, state);
	OnBroadcastEvent(*mFlagStateChangeEvent, dto);
}

json TDealerAssistGameLogic::RedrawCards_AssumeWriteLock()
{
	TLOG(LogDealerAssistGameLogic, Info, "Redraw cards detected.");
	if (State->Table.mPhase == EDealerAssistPhase::DealingCards)
	{
		State->GameLogic->ClearCards();
		State->Table.mCardPosition = State->GameLogic->GetDealingPhase();
		State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
		State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

		OnStateUpdate();
		SaveRoundState_AssumeReadLock(backend::ERoundStatus::Open).then([this](boost::future<void> future) {
			try
			{
				future.get();
				TLOG(LogDealerAssistGameLogic, Info, "Successfully saved round state during card dealing.");
			}
			catch (const DealerAssistException& ex)
			{
				TLOG(LogDealerAssistGameLogic, Warning, "DealerAssistException: %s", ex.what());

				if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
					OnTableCloseEvent();
			}
			catch (const std::exception& e)
			{
				TLOG(LogDealerAssistGameLogic, Warning, "Error on saving round state during card dealing: %s", e.what());
			}
		});
		return {};
	}

	throw std::runtime_error("Redraw cards detected in unexpected phase");
}

void TDealerAssistGameLogic::SaveIntermediateRoundState_AssumeReadLock()
{
	SaveRoundState_AssumeReadLock(backend::ERoundStatus::Open).then([this](boost::future<void> future) {
		try
		{
			future.get();
			TLOG(LogDealerAssistGameLogic, Info, "Successfully saved round state during card dealing.");
		}
		catch (const DealerAssistException& ex)
		{
			TLOG(LogDealerAssistGameLogic, Warning, "DealerAssistException: %s", ex.what());

			if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
				OnTableCloseEvent();
		}
		catch (const std::exception& e)
		{
			TLOG(LogDealerAssistGameLogic, Warning, "Error on saving round state during card dealing: %s", e.what());
		}
	});
}

bool TDealerAssistGameLogic::CanSwitchGame()
{
	SharedScopedLock lockTableState(State);
	if (State->SupervisorState->Active && State->SupervisorState->SubPhase == ESupervisorSubPhase::GameSelection)
		return true;
	if (State->ActionsState.bGameChangeRequested && State->SupervisorState->SelectedGameType)
		return true;

	TLOG(LogDealerAssistGameLogic, Info, "Cannot switch game.");
	return false;
}

void TDealerAssistGameLogic::ResetSupervisorAction()
{
	ScopedLock lock(State);

	if (State->SupervisorState->SubPhase == ESupervisorSubPhase::WaitingForSupervisor)
	{
		State->ActionsState.bSupervisorCalled = false;
		ReportFlagStateChange(EFlagStateType::SupervisorCalled, false);
		State->SupervisorState->SubPhase.reset();
		State->SupervisorState->Reset();
		ProceedWithGame_AssumeWriteLock();
	}
	else
	{
		if (State->SupervisorState->RequestedAction == ESupervisorAction::CloseTable)
		{
			State->ActionsState.bTableCloseRequested = false;
			ReportFlagStateChange(EFlagStateType::TableClosingRequested, false);
		}
		else if (State->SupervisorState->RequestedAction == ESupervisorAction::ChangeGame)
		{
			State->ActionsState.bGameChangeRequested = false;
			State->SupervisorState->bContinueUnfinishedGame = false;
			State->SupervisorState->SelectedGameType.reset();
			State->SupervisorState->SelectedGameInfo.reset();
			ReportFlagStateChange(EFlagStateType::GameChangingRequested, false);
		}
		else if (State->SupervisorState->RequestedAction == ESupervisorAction::ChangeResult)
		{
			State->ActionsState.bResultChangeRequested = false;
			State->SupervisorState->ChangedGameResult.clear();
			ReportFlagStateChange(EFlagStateType::ResultChangingRequested, false);
		}

		State->RoundsUntilChange.reset();
		State->SupervisorState->RequestedAction.reset();
		State->SupervisorState->SubPhase = ESupervisorSubPhase::ActionSelection;
	}

	OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
}

SupervisorState TDealerAssistGameLogic::GetSupervisorState() const
{
	return *State.getCopy().SupervisorState;
}

void TDealerAssistGameLogic::SetChangedGameResult(const ResultChangeRequestDto& dto)
{
	ScopedLock lock(State);
	State->SupervisorState->ChangedGameResult = dto.ToJSON();
	State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmAction;
	OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
}

void TDealerAssistGameLogic::SupervisorSelectedGame(const DealerGameInfoDto& gameInfo)
{
	ScopedLock lock(State);
	State->SupervisorState->SelectedGameType = gameInfo.GameType;
	State->SupervisorState->SelectedGameInfo = gameInfo;
	State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmGameSelection;
	OnBroadcastEvent(*mSupervisorStateChangedEvent, *State->SupervisorState);
}

void TDealerAssistGameLogic::HandleCardBurn_AssumeWriteLock(const FScannedCard& data)
{
	// flag is still on we didn't burn any card yet. First card will tell us how many cards to burn.
	if (State->bShouldBurnCards)
	{
		State->ShoeChangeState.BurnedCards = 0;
		State->ShoeChangeState.Card = data.ToInt() % 1000;
		State->ShoeChangeState.NumOfCardsToBurn = data.GetNumOfCardsToBurn();
		State->bShouldBurnCards = false;
		State->ActionsState.bChangeShoeRequested = false;
		ReportFlagStateChange(EFlagStateType::ChangeShoeRequested, false);
		TLOG(LogDealerAssistGameLogic, Info, "First card scanned, burning %i cards.", State->ShoeChangeState.NumOfCardsToBurn);
		OnBroadcastEvent(*mCardBurnEvent, State->ShoeChangeState);

		if (bVirtualGame)
			BurnCardsWithVirtualDealer_AssumeWriteLock();
	}
	else
	{
		if (auto timer = mPauseAfterCardBurnTimer.lock(); timer->IsEnabled())
		{
			TLOG(LogDealerAssistGameLogic, Info, "Waiting for pause after card burn to finish. Invalid card scan");
			return;
		}

		auto burnedCards = State->ShoeChangeState.BurnedCards;
		auto numOfCardsToBurn = State->ShoeChangeState.NumOfCardsToBurn;
		burnedCards++;
		State->ShoeChangeState.BurnedCards = burnedCards;

		OnBroadcastEvent(*mCardBurnEvent, State->ShoeChangeState);
		if (burnedCards < numOfCardsToBurn)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Card %s burned.", data.Barcode().c_str());
			TLOG(LogDealerAssistGameLogic, Info, "Burned %i cards, %i to go.", burnedCards, numOfCardsToBurn - burnedCards);

			if (bVirtualGame)
				BurnCardsWithVirtualDealer_AssumeWriteLock();
		}
		else
		{
			TLOG(LogDealerAssistGameLogic, Info, "All cards burned, waiting for new round to begin.");
			StartTimer(mPauseAfterCardBurnTimer);
		}
	}
}

void TDealerAssistGameLogic::VoidRound_AssumeWriteLock()
{
	TLOG(LogDealerAssistGameLogic, Critical, "!ROUND VOIDED!");
	if (State->Table.mPhase == EDealerAssistPhase::NewRoundPreparation || State->Table.mPhase == EDealerAssistPhase::BetsOpen ||
	    State->Table.mPhase == EDealerAssistPhase::BetsClosed || State->Table.mPhase == EDealerAssistPhase::DealingCards)
	{
		SaveRoundState_AssumeReadLock(backend::ERoundStatus::Void).then([this](boost::future<void> future) {
			try
			{
				future.get();
				ScopedLock lock(State);
				FlagRound_AssumeWriteLock(EFlagRoundReason(EFlagRoundReason::RoundVoid));
				ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundVoid);

				ResetCurrentRound_AssumeWriteLock();

				if (State->ActionsState.bChangeShoeRequested)
					State->bShouldBurnCards = true;

				StartTimer(mPauseAtRoundEndTimer);
			}
			catch (const DealerAssistException& ex)
			{
				TLOG(LogDealerAssistGameLogic, Warning, "DealerAssistException: %s", ex.what());

				if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
					OnTableCloseEvent();
			}
			catch (const std::exception& e)
			{
				TLOG(LogDealerAssistGameLogic, Warning, "Error on saving result: %s", e.what());
			}
		});
	}
	else
	{
		TLOG(LogDealerAssistGameLogic, Warning, "Round already closed! We are in phase %s", State->Table.mPhase._to_string());
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundVoid);
		ResetCurrentRound_AssumeWriteLock();

		if (State->ActionsState.bChangeShoeRequested)
			State->bShouldBurnCards = true;

		StartTimer(mPauseAtRoundEndTimer);
	}
}

void TDealerAssistGameLogic::HandleCutCard_AssumeWriteLock()
{
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
	{
		TLOG(LogDealerAssistGameLogic, Info, "Ignoring cut card scan, not in dealing phase");
		return;
	}

	TLOG(LogDealerAssistGameLogic, Info, "Cut card detected.");
	State->bShouldBurnCards = true;
	State->ShoeChangeState.CutCardDrawn = true;
	OnBroadcastEvent(*mCutCardEvent, EmptyEventDto());
	FlagRound_AssumeWriteLock(EFlagRoundReason(EFlagRoundReason::CutCard));
}

void TDealerAssistGameLogic::ScannerConnectionStateChanged(bool isConnected)
{
	if (bScannerConnected == isConnected)
		return;

	bScannerConnected = isConnected;

	auto dto = NotifyDealerConsoleDto(
	  EDealerAssistTableNotificationCode(isConnected ? EDealerAssistTableNotificationCode::ScannerConnected : EDealerAssistTableNotificationCode::ScannerDisconnected));
	dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
	OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);
}

// --  HANDLE NUMBER OF PLAYERS --

void TDealerAssistGameLogic::OnNumberOfPlayersChanged(const uint32_t numOfPlayers)
{
	ScopedLock lockTableState(State);
	State->NumOfAllPlayers = numOfPlayers;
}

void TDealerAssistGameLogic::ReportNumberOfPlayers_AssumeReadLock()
{
	const ReportNumOfActivePlayersDto numOfPlayersDto = ReportNumOfActivePlayersDto(State->NumOfActivePlayers, State->NumOfAllPlayers);
	OnBroadcastEvent(*mReportPlayersEvent, numOfPlayersDto);
	TLOG(LogDealerAssistGameLogic, Info, "Number of %i active players reported.", State->NumOfAllPlayers);
}

void TDealerAssistGameLogic::ReportRoundsUntilChange_AssumeWriteLock(const ERoundsCountdownType type)
{
	uint32_t remainingRounds = 0;
	uint32_t maxRounds = 0;

	if (type == ERoundsCountdownType::GameChange && State->ActionsState.bGameChangeRequested)
	{
		maxRounds = State->GameflowConfig.RoundsBeforeGameChange;
		remainingRounds = std::max<uint32_t>(0, maxRounds - State->RoundsPlayedCounter);

		TLOG(LogDealerAssistGameLogic, Info, "Reported %d rounds remaining until game change (max: %d).", remainingRounds, maxRounds);
	}
	else if (type == ERoundsCountdownType::TableClose && State->ActionsState.bTableCloseRequested)
	{
		maxRounds = State->GameflowConfig.RoundsBeforeClose;
		remainingRounds = std::max<uint32_t>(0, maxRounds - State->RoundsPlayedCounter);

		TLOG(LogDealerAssistGameLogic, Info, "Reported %d rounds remaining until table close (max: %d).", remainingRounds, maxRounds);
	}

	RoundsUntilCloseDto dto(remainingRounds, maxRounds, type);
	State->RoundsUntilChange = dto;
	OnBroadcastEvent(*mRoundsUntilCloseEvent, dto);
}

void TDealerAssistGameLogic::CheckRoundClosing_AssumeWriteLock()
{
	if (State->ActionsState.bTableCloseRequested)
	{
		State->RoundsPlayedCounter++;

		ReportRoundsUntilChange_AssumeWriteLock(ERoundsCountdownType::TableClose);

		// Check if we've reached the maximum rounds limit
		if (State->RoundsPlayedCounter >= State->GameflowConfig.RoundsBeforeClose)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Maximum rounds limit reached (%d). Closing table.", State->GameflowConfig.RoundsBeforeClose);
		}
		else
		{
			TLOG(LogDealerAssistGameLogic, Info, "Rounds played until close: %d / %d", State->RoundsPlayedCounter, State->GameflowConfig.RoundsBeforeClose);
		}
	}
}

void TDealerAssistGameLogic::CheckGameChangeRounds_AssumeWriteLock()
{
	if (State->ActionsState.bGameChangeRequested)
	{
		State->RoundsPlayedCounter++;

		// Report updated rounds until game change
		ReportRoundsUntilChange_AssumeWriteLock(ERoundsCountdownType::GameChange);

		// Check if we've reached the maximum rounds limit for game change
		if (State->RoundsPlayedCounter >= State->GameflowConfig.RoundsBeforeGameChange)
		{
			TLOG(LogDealerAssistGameLogic, Info, "Maximum rounds limit reached (%d). Proceeding with game change.", State->GameflowConfig.RoundsBeforeGameChange);
		}
		else
		{
			TLOG(LogDealerAssistGameLogic, Info, "Rounds played until game change: %d / %d", State->RoundsPlayedCounter, State->GameflowConfig.RoundsBeforeGameChange);
		}
	}
}

// --  VIRTUAL DEALER --

void TDealerAssistGameLogic::DealWithVirtualDealer()
{
	ScopedLock lock(State);
	DealWithVirtualDealer_AssumeWriteLock();
}

void TDealerAssistGameLogic::BurnCardsWithVirtualDealer()
{
	ScopedLock lock(State);
	BurnCardsWithVirtualDealer_AssumeWriteLock();
}

void TDealerAssistGameLogic::DealWithVirtualDealer_AssumeWriteLock()
{
	if (!State->GameLogic->IsGameFinished())
	{
		mPauseBetweenCardsTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
		  [this]() {
			  ScopedLock lock(State);

			  const FScannedCard card = State->VirtualDealerLogic->DrawCardWithBarcode();
			  if (State->VirtualDealerLogic->NumberOfCardsLeft() == 140)
			  {
				  HandleCutCard_AssumeWriteLock();
			  }
			  HandleVirtualDealerCard_AssumeWriteLock(card);
		  },
		  2000, rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Virtual dealer deal card");
	}
}

void TDealerAssistGameLogic::BurnCardsWithVirtualDealer_AssumeWriteLock()
{
	if (State->Table.mPhase == EDealerAssistPhase::CardBurn)
	{
		const FScannedCard card = State->VirtualDealerLogic->DrawCardWithBarcode();
		mPauseBetweenCardsTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>([this, card]() { HandleScannedCard(card); }, 2000,
		                                                                                 rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Virtual dealer burn card");
	}
}

void TDealerAssistGameLogic::SetGameflowSettings(const GameflowSettingsDto& settings)
{
	ScopedLock lock(State);
	State->GameflowConfig = settings;
}

bool TDealerAssistGameLogic::CanCloseTable() const
{
	return State->ActionsState.bTableCloseRequested && State->RoundsPlayedCounter >= State->GameflowConfig.RoundsBeforeClose;
}

bool TDealerAssistGameLogic::CanChangeGame() const
{
	return State->ActionsState.bGameChangeRequested && State->RoundsPlayedCounter >= State->GameflowConfig.RoundsBeforeGameChange;
}
