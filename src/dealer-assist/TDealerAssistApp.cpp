#include "TDealerAssistApp.h"

#include "DealerAssistSharedTypes.h"
#include "TDealerAssistBaseClient.h"
#include "TDealerAssistCroupierClient.h"
#include "TDealerAssistHostClient.h"
#include "TThreeHeadedDragonDealerAssistGameLogic.h"
#include "Timing.h"
#include "YUtils.h"
#include "common/TBuildDefinitions.h"
#include "dealer-assist/TBaccaratDealerAssistGameLogic.h"
#include "dealer-assist/TDragonTigerDealerAssistGameLogic.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "dealer-assist/dto/BetStatisticsDto.h"
#include "dealer-assist/dto/DealerAssistCroupierChangeDto.h"
#include "dealer-assist/dto/GetAvailableGamesDto.h"
#include "dealer-assist/dto/NotifyDealerConsoleDto.h"
#include "dealer-assist/dto/ReportNumOfActivePlayersDto.h"
#include "dealer-assist/dto/ResultChangeRequestDto.h"
#include "dealer-assist/dto/SelectDealerGameRequestDto.h"
#include "dto/DealerAssistStateUpdateDto.h"
#include "honeywell_scanner.h"

using namespace rtfwk_sdl2;
using namespace dealer_assist::backend;
using namespace dealer_assist;

MAIN_METHOD(TDealerAssistApp, "DEALER ASSIST")

DEFINE_LOG_CATEGORY(DealerAssistAppLog, "dealer-assist")

const JsonSchema DealerGameSchema = JsonSchema(

  { { "config", DealerGameConfigSchema },
    { "display-name", JsonSchema(json::value_t::string, "The name of the game") },
    { "id", JsonSchema(json::value_t::number_unsigned, "Game id") },
    { "is-virtual", JsonSchema(json::value_t::boolean, "Has virtual dealer", false) },
    { "game-type", JsonSchema(json::value_t::string, "The type of the game", EGameType(EGameType::Baccarat)._to_string()).SetToEnumType<EGameType>() } });

const JsonSchema DealerAssistSchema = JsonSchema(
  { { "dealer-assist-backend", JsonSchema({ { "address", JsonSchema(json::value_t::string, "Address of the dealer assist backend") },
                                            { "secure", JsonSchema(json::value_t::boolean, "True to use TLS when connecting") } }) },
    { "barcode-scanner",
      JsonSchema({ { "reconnect-interval", JsonSchema(json::value_t::number_unsigned, "Amount of time (ms) before attempting to reconnect to the port", 60000U) },
                   { "serial-port", JsonSchema(json::value_t::string, "The unix serial port name for the barcode reader") },
                   { "use-websocket", JsonSchema(json::value_t::boolean, "Use websocket as primary connection", true) },
                   { "websocket-address", JsonSchema(json::value_t::string, "Websocket address") },
                   { "websocket-secure", JsonSchema(json::value_t::boolean, "Is websocket secured", true) } }) },
    { "table-mode", JsonSchema(json::value_t::string, "Mode of table", ETableMode(ETableMode::LandBase)._to_string()).SetToEnumType<ETableMode>() },
    { "num-of-decks", JsonSchema(json::value_t::number_integer, "Number of decks used", 8).Flag(yserver::CriticalSettingFlag) },
    { "gameflow-config", JsonSchema({ { "skip-decision-if-no-bets", JsonSchema(json::value_t::boolean, "Skip decision if no bets on open fields", false) },
                                      { "rounds-before-close", JsonSchema(json::value_t::number_unsigned, "Number of rounds before table is closed.", 3) },
                                      { "rounds-before-game-change", JsonSchema(json::value_t::number_unsigned, "Number of rounds before game is changed", 3) } }) },
    { "unsecure-admin-actions", JsonSchema(json::value_t::boolean, "Allow unsecure admin actions", false).Flag(yserver::CriticalSettingFlag) },
    { "games", JsonSchema(json::value_t::array, "Games used by the dealer assist").SetChildSchema(DealerGameSchema) },
    { "video-stream",
      JsonSchema(
        { { "video-url", JsonSchema(json::value_t::string, "URL of the video stream", "http://************:8889/baccaratFront/") },
          { "video-id", JsonSchema(json::value_t::string, "The video stream ID if required (some video-type options need this)", std::string()) },
          { "video-type",
            JsonSchema(json::value_t::string, "Type of the video stream", EStreamType(EStreamType::imaxaPlayer)._to_string()).SetToEnumType<EStreamType>() } }) } });

TDealerAssistApp::TDealerAssistApp(const std::string& type) : TApplication(type)
{
	Schema() += DealerAssistSchema;

	mPort = DEFAULT_DEALER_ASSIST_PORT;

	AvahiService.Config.service_name = dealer_assist::DealerAssistServerServiceName;
	AvahiService.Config.add_hostname_to_service_name = true;

	mNotifyDealerConsoleEvent =
	  std::make_shared<yserver::EventTemplate>(DEALER_ASSIST_TABLE_NOTIFICATION_EVENT_NAME, "When dealer assist console needs to be notified about info or warning.");

	mDealerLoginChangedEvent = std::make_shared<yserver::EventTemplate>(DEALER_LOGIN_CHANGED_EVENT_NAME, "When dealer changes");
	mChatMessageEvent = std::make_shared<yserver::EventTemplate>(POST_CHAT_MESSAGE_EVENT_NAME, "Post chat message");
	mUpdateStateEvent = std::make_shared<yserver::EventTemplate>(GAME_STATE_CHANGED_EVENT_NAME, "Update state");
	mGamePhaseChangedEvent = std::make_shared<yserver::EventTemplate>(GAME_PHASE_CHANGED_EVENT_NAME, "Game state is changed (phase, round number, etc.)");
	mSupervisorStateChangedEvent = std::make_shared<yserver::EventTemplate>(SUPERVISOR_STATE_CHANGED_EVENT_NAME, "Supervisor state changed");

	RequestHandler = std::make_shared<yprotocol::YDedicatedRequestHandler<yprotocol::YProtocolClient>>();
	RequestHandler->RegisterMethod(ADD_CARD_EVENT_NAME, "Add cards", std::bind(&TDealerAssistApp::ReceivedBarcode, this, std::placeholders::_1, std::placeholders::_2));

	RequestHandler->RegisterMethod(PLAYER_COUNT_CHANGED_EVENT_NAME, "Manage number of players",
	                               std::bind(&TDealerAssistApp::ManageNumberOfPlayers, this, std::placeholders::_1, std::placeholders::_2));

	RequestHandler->RegisterMethod(DEALER_ACTION_EVENT_NAME, "Dealer Action",
	                               std::bind(&TDealerAssistApp::DealerAction, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(SUPERVISOR_ACTION_EVENT_NAME, "Supervisor Action",
	                               std::bind(&TDealerAssistApp::SupervisorAction, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(SET_FLAG_STATE_EVENT_NAME, "Flag state changed",
	                               std::bind(&TDealerAssistApp::FlagStateChanged, this, std::placeholders::_1, std::placeholders::_2));

	RequestHandler->RegisterMethod(POST_CHAT_MESSAGE_EVENT_NAME, "Post chat message",
	                               std::bind(&TDealerAssistApp::PostChatMessage, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(POST_BET_STATISTICS, "Post bets statistics",
	                               std::bind(&TDealerAssistApp::PostBetsStatistics, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(GET_CHAT_MESSAGES, "Get chat messages",
	                               std::bind(&TDealerAssistApp::GetChatMessages, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(GET_GAMES_EVENT_NAME, "Get available games",
	                               std::bind(&TDealerAssistApp::GetAvailableGames, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(CHANGE_GAME_EVENT_NAME, "Change game", std::bind(&TDealerAssistApp::ChangeGame, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(CHANGE_GAME_RESULT_EVENT_NAME, "Change current round result",
	                               std::bind(&TDealerAssistApp::ChangedGameResult, this, std::placeholders::_1, std::placeholders::_2));
	RequestHandler->RegisterMethod(CANCEL_SUPERVISOR_ACTION_EVENT_NAME, "Cancel supervisor action",
	                               std::bind(&TDealerAssistApp::CancelSupervisorAction, this, std::placeholders::_1, std::placeholders::_2));

	RequestHandler->RegisterMethod(
	  EDealerAssistRequest(EDealerAssistRequest::GetTableHistory)._to_string(), "Get the round history for this table",
	  std::bind(&TDealerAssistBackendApp::GetTableHistory, this, std::placeholders::_1, std::placeholders::_2),
	  JsonSchema(
		{ { "max", JsonSchema(json::value_t::number_unsigned, "The maximum number of rounds to return (last X)", 200U) },
		  { "gameType", JsonSchema(json::value_t::string, "If given, only return games matching this type", std::string()) },
		  { "fromEvent", JsonSchema(json::value_t::string, "If given, returns round history starting at the last occurrence of the given event reported by FlagRound",
									std::string()) } }));

	SetPrintTraffic(Debug);
	SetLogCategory(DealerAssistAppLog);

	LaunchArgs.RegisterSwitch("table", ESwitchParamMode::Required);
}

TDealerAssistApp::~TDealerAssistApp()
{
	Log(Info, "Shutting down server!");

	if (mDealerAssistBackendClient)
		mDealerAssistBackendClient->Stop();

	Log(Info, "Dealer Assist server is down!");
}

/* main init */
int TDealerAssistApp::Init(const std::vector<std::string>& args)
{
	if (!args.empty())
		SetParam("Config", args[0]);

	TApplication::Init(args);

	mTableId = LaunchArgs.GetSwitchValue("table");

	const std::string conf = GetParam("Config")->Value();
	Log(Important, "Loading game from file %s", conf.c_str());
	try
	{
		LoadPatchedConfiguration(conf, GetParam("UserConfig", { ParameterDomain::CACHE })->Value());
	}
	catch (const ConfigError& err)
	{
		Log(Critical, "CONFIG FILE %s COULD NOT LOAD: %s", conf.c_str(), err.what());
		return -1;
	}

	// Do this to tell the application main thread all GUI loading is complete
	setStatus(EApplicationState::Startup);

	return 0;
}

void TDealerAssistApp::OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& e)
{
	WebServer::OnInitialize(e);

	// Register our message handler
	e->set_user_agent("Dealer Assist Server " + std::string(version::FULL_VERSION_STRING));

	State->SupervisorState = std::make_shared<SupervisorState>();
	State->mDealingPhase = EDealerAssistPhase::RoundError;
	State->mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;
	State->mSubPhase.reset();
}

void TDealerAssistApp::OnConfigLoaded(const std::filesystem::path& fileloc)
{
	YProtocolWebServer::OnConfigLoaded(fileloc);

	GenerateChecksums();

	mReconnectInterval = GetConfig("barcode-scanner.reconnect-interval").get<uint64_t>();
	mTargetPort = GetConfig("barcode-scanner.serial-port").get<std::string>();
	bUseWebsocketScanner = GetConfig("barcode-scanner.use-websocket").get<bool>();
	mWebsocketScannerAddress = GetConfig("barcode-scanner.websocket-address").get<std::string>();
	bWebsocketScannerSecure = GetConfigOptional("barcode-scanner.websocket-secure", false).get<bool>();
	mTableMode = ETableMode::_from_string(GetConfig("table-mode").get<std::string>().c_str());
	bAllowUnsecureAdminActions = GetConfig("unsecure-admin-actions").get<bool>();

	auto gamesJson = GetConfig("games");

	Log(Info, "Loading game configurations: %s ", JsonSchema::PrintValueInline(gamesJson).c_str());

	if (gamesJson.is_array())
	{
		for (const auto& game : gamesJson)
		{
			try
			{
				Log(Info, "Loading game: %s ", JsonSchema::PrintValueInline(game).c_str());
				auto gameId = game["id"].get<uint32_t>();
				auto gameType = game["game-type"].get<std::string>();
				auto gameName = game["display-name"].get<std::string>();
				auto gameIsVirtual = game["is-virtual"].get<bool>();
				auto gameConfig = game["config"];

				auto dto = DealerGameInfoDto();
				dto.Id = gameId;
				dto.GameType = EGameType::_from_string(gameType.c_str());
				dto.DisplayName = gameName;
				dto.IsVirtual = gameIsVirtual;
				dto.Config = gameConfig;

				mGameConfigs[gameId] = dto;

				Log(Info, "Loading game configuration for %u", gameId);
			}
			catch (const std::exception& e)
			{
				Log(Error, "Error while processing game configuration: %s", e.what());
			}
		}
	}

	mGameflowSettings.RoundsBeforeClose = GetConfig("gameflow-config.rounds-before-close").get<uint32_t>();
	mGameflowSettings.RoundsBeforeGameChange = GetConfig("gameflow-config.rounds-before-game-change").get<uint32_t>();
	mGameflowSettings.SkipDecisionIfNoBets = GetConfig("gameflow-config.skip-decision-if-no-bets").get<bool>();

	mNumOfDecks = GetConfig("num-of-decks").get<uint32_t>();
	mStreamUrl = GetConfig("video-stream.video-url").get<std::string>();
	mStreamId = GetConfig("video-stream.video-id").get<std::string>();
	mStreamType = EStreamType::_from_string(GetConfig("video-stream.video-type").get<std::string>().c_str());

	if (bUseWebsocketScanner)
	{
		ConnectWebsocketScanner();
	}

	mDealerAssistBackendAddress = GetConfig("dealer-assist-backend.address").get<std::string>();
	bDealerAssistBackendSecure = GetConfig("dealer-assist-backend.secure").get<bool>();
}

void TDealerAssistApp::GenerateChecksums()
{
	mChecksums[GetBinaryPath()] = crypto::File_Checksum(EHashAlgorithm::SHA1, GetBinaryPath());

	auto criticalConfig =
	  GetSchema().GenerateConfig(RootConfigNode(), { .bUseComments = false, .bSkipUnknownMembers = true, .RequiredFlags = { yserver::CriticalSettingFlag } });

	Log(Info, "Critical config: %s", JsonSchema::PrintValueInline(criticalConfig).c_str());
	auto homeDir = GetParam("OldGamesDir")->AsString();
	std::filesystem::path configDir(homeDir);
	std::filesystem::path criticalFileLocation = configDir / "dealerAssist.crit";
	const std::string toHash = JsonSchema::PrintValueInline(criticalConfig);
	Log(Info, "Critical config location: %s", criticalFileLocation.c_str());

	std::filesystem::path directory = criticalFileLocation.parent_path();
	if (!exists(directory))
	{
		if (!create_directories(directory))
		{
			Log(Warning, "Could not create directory %s", directory.c_str());
			return;
		}
	}

	std::ofstream outputCriticalFile(criticalFileLocation);
	outputCriticalFile << toHash;
	outputCriticalFile.close();

	mChecksums[criticalFileLocation] = crypto::Hash(toHash, EHashAlgorithm::SHA1);
}

int TDealerAssistApp::RunOnce()
{
	const int res = TApplication::RunOnce();

	if (!bConnecting && !Scanner && ytime::GetSystemTimeMsec() > mLastConnectAttempt + mReconnectInterval && !bUseWebsocketScanner)
	{
		bConnecting = true;
		DoAsyncTask(std::bind(&TDealerAssistApp::TryReconnect, this));
	}
	else if (!bConnecting && !mWebsocketScannerClient->IsConnected() && ytime::GetSystemTimeMsec() > mLastConnectAttempt + mReconnectInterval && bUseWebsocketScanner)
	{
		bConnecting = true;
		DoAsyncTask(std::bind(&TDealerAssistApp::TryReconnectWebsocketScanner, this));
	}

	return res;
}

void TDealerAssistApp::Startup()
{
	TApplication::Startup();

	mNetworkInterfaces = web::list_network_adapters();

	int initRes = 0;
	do {
		if (initRes)
			std::this_thread::sleep_for(std::chrono::seconds(5));
		initRes = StartServer();
	} while ((initRes != 0) && !shuttingDown());

	if (initRes != 0)
	{
		Log(Critical, "%s SERVER COULD NOT START", ClientType.c_str());
		return;
	}

	Log(Important, "======= %s SERVER v%s IS ONLINE =======", ClientType.c_str(), version::FULL_VERSION_STRING);
}

void TDealerAssistApp::Destroy()
{
	if (mDealerAssistBackendClient)
		mDealerAssistBackendClient->Stop();

	CloseAllConnections();

	Stop();

	TApplication::Destroy();
}

int TDealerAssistApp::StartServer()
{
	if (!Initialize())
		return 1;

	// Start the server accept loop, can fail if port bind fails
	try
	{
		Start("dealerassistws");
		Log(Critical, "Server started on port %d.", mPort);
	}
	catch (const std::exception& e)
	{
		Log(Error, "Server could not start on port %d: %s", mPort, e.what());
		return 2;
	}

	ConnectToDealerAssistBackend();

	return 0;
}

void TDealerAssistApp::ConnectWebsocketScanner()
{
	mWebsocketScannerClient = web::SingleWebsocketClient::New(mWebsocketScannerAddress, bWebsocketScannerSecure);

	mWebsocketScannerClient->OnConnectionStateChanged += [this](web::websockets::session::state::value s, const std::error_code& ec) {
		if (s == web::websockets::session::state::closed)
		{
			std::string suffix;
			if (ec)
				suffix = ": " + ec.message();

			Log(Info, "Scanner client connection lost %s", suffix.c_str());

			if (bUseWebsocketScanner && mDealerAssistLogic)
				mDealerAssistLogic->ScannerConnectionStateChanged(false);
		}
		else if (s == web::websockets::session::state::open)
		{
			Log(Info, "Scanner client connected");
			if (bUseWebsocketScanner && mDealerAssistLogic)
				mDealerAssistLogic->ScannerConnectionStateChanged(true);
		}
	};
	mWebsocketScannerClient->OnMessage = std::bind(&TDealerAssistApp::OnSerialPortMessage, this, std::placeholders::_1);

	mWebsocketScannerClient->Initialize();

	mWebsocketScannerClient->Start("scanner-" + mTableId);
}

void TDealerAssistApp::ConnectToDealerAssistBackend()
{
	mDealerAssistBackendClient = std::make_shared<DealerAssistTableBackendClient>(mDealerAssistBackendAddress, bDealerAssistBackendSecure, "Dealer Assist Backend");

	if (mDealerAssistLogic)
		mDealerAssistLogic->DealerAssistBackendClient = mDealerAssistBackendClient;

	mDealerAssistBackendClient->Client->PingInterval = 2000;    // ping the backend connection every 2 seconds

	mDealerAssistBackendClient->OnInitialized += [this](const json& data) {
		Log(Info, "DealerAssistBackend connection initialized!");
		std::optional<RoundInfoDto> roundInfo;
		if (data.is_object())
		{
			try
			{
				roundInfo = RoundInfoDto::FromJSON(data, false, true);
				Log(Info, "Round info: %s", JsonSchema::PrintValueInline(roundInfo->ToJSON()).c_str());
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Failed to parse round info from backend: %s", e.what());
			}
		}
		mRoundInfo = roundInfo;

		ScopedLock lock(State);
		State->mErrorCode.reset();
		ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase::TableClosed);
	};

	mDealerAssistBackendClient->OnEvent += [this](const std::string& eventName, const json& data) {
		ScopedLock lock(State);
		Log(Info, "DealerAssistDatabase received event: %s with data: %s", eventName.c_str(), JsonSchema::PrintValueInline(data).c_str());

		if (eventName == TABLE_CROUPIER_CHANGED_EVENT_NAME)
		{
			auto dto = DealerAssistCroupierChangeDto::FromJSON(data);
			if (!dto.GetNewCroupierInfo() || dto.GetNewCroupierInfo()->ID.empty())
			{
				if (dto.GetOldCroupierInfo()->IsSupervisor())
				{
					Log(Info, "Supervisor has logged out.");

					if (mDealerAssistLogic)
					{
						State->Supervisor.reset();
						lock.unlock();

						mDealerAssistLogic->HandleSupervisorLogout();
					}
					else
						SupervisorLogout_AssumeWriteLock();
				}
				else
				{
					Log(Info, "Croupier has logged out.");
					State->Croupier.reset();

					if (mDealerAssistLogic)
					{
						mDealerAssistLogic->StopAllTimers();
						mDealerAssistLogic.reset();
					}

					ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase::TableClosed);
				}
			}
			else if (!dto.GetOldCroupierInfo() || dto.GetOldCroupierInfo()->ID.empty())
			{
				Log(Info, "Worker has logged in.");
				auto workerInfo = DealerAssistWorker::FromJSON(data["new"]);

				if (workerInfo.IsSupervisor())
				{
					State->Supervisor = std::make_shared<DealerAssistWorker>(workerInfo);
					if (mDealerAssistLogic)
						mDealerAssistLogic->HandleSupervisorLogin(*State->Supervisor);
					else
						SupervisorLogin_AssumeWriteLock();
				}
				else
				{
					State->Croupier = std::make_shared<DealerAssistWorker>(workerInfo);
					if (mDealerAssistLogic)
					{
						// TODO: Check from parent state
						if (State->mDealingPhase == EDealerAssistPhase::WaitingForCroupier)
							mDealerAssistLogic->HandleCroupierLogin(GetNumberOfAllPlayers(), *State->Croupier);
						else
							mDealerAssistLogic->HandleCroupierRelogin(*State->Croupier);
					}
				}
			}
			else
			{
				Log(Info, "Croupier switch.");
				auto workerInfo = DealerAssistWorker::FromJSON(data["new"]);

				if (workerInfo.IsSupervisor())
				{
					State->Supervisor = std::make_shared<DealerAssistWorker>(workerInfo);

					if (mDealerAssistLogic)
					{
						mDealerAssistLogic->HandleCroupierSwitch(GetNumberOfAllPlayers(), *State->Supervisor);
						mDealerAssistLogic->HandleSupervisorLogin(*State->Supervisor);
					}
				}
				else
				{
					State->Croupier = std::make_shared<DealerAssistWorker>(workerInfo);

					if (mDealerAssistLogic)
						mDealerAssistLogic->HandleCroupierSwitch(GetNumberOfAllPlayers(), *State->Supervisor);
				}
			}

			Broadcast(*mDealerLoginChangedEvent, dto.ToJSON());
		}
		if (eventName == TABLE_CLEARED_EVENT_NAME)
		{
			if (mDealerAssistLogic)
				mDealerAssistLogic->HandleTableClear();
		}
	};

	mDealerAssistBackendClient->OnConnected += [this]() {
		Log(Info, "DealerAssistDatabase connected!");
		ScopedLock lock(State);
		State->mErrorCode.reset();
		ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase::TableClosed);
	};

	mDealerAssistBackendClient->OnDisconnected += [this](bool bReconnect) {
		Log(Info, "DealerAssistDatabase disconnected!");

		ScopedLock lock(State);
		State->Croupier.reset();
		State->Supervisor.reset();
		mDealerAssistLogic.reset();
		State->mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;
		ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase::RoundError);
	};


	const std::string wsResource = std::format("/{}", mTableId);

	mDealerAssistBackendClient->SetLogCategory(DealerAssistAppLog);
	mDealerAssistBackendClient->StartConnectLoop(wsResource);
}

std::shared_ptr<yprotocol::YProtocolClient> TDealerAssistApp::createClient(const std::string& clientType, const std::string& clientId,
                                                                           std::optional<security::JWToken> token, bool isLocalClient)
{
	auto clientExists = [this](const std::string& clientId, const EClientType& clientType) {
		SharedScopedLock lock(mClients);
		auto clientMap = mClients->at(clientType._to_index());
		return clientMap.find(clientId) != clientMap.end();
	};

	auto addClientToMap = [this](EClientType clientType, const std::string& clientId, std::shared_ptr<DealerAssistBaseClient> client) {
		ScopedLock lock(mClients);
		mClients->at(clientType._to_index()).emplace(clientId, client);
	};

	std::shared_ptr<DealerAssistBaseClient> client;
	EClientType clientTypeEnum = EClientType::GameHost;

	if (clientType == DEALER_ASSIST_GAMEHOST_CLIENT_TYPE)
	{
		client = std::make_shared<DealerAssistHostClient>(this, RequestHandler, clientId, mTableId, isLocalClient, token);
		clientTypeEnum = EClientType::GameHost;
	}
	else if (clientType == DEALER_ASSIST_CROUPIER_CLIENT_TYPE)
	{
		client = std::make_shared<DealerAssistCroupierClient>(this, RequestHandler, clientId, mTableId, isLocalClient, token);
		clientTypeEnum = EClientType::DealerConsole;
	}
	else
		return nullptr;

	if (clientExists(clientId, clientTypeEnum))
	{
		Log(Error, "Client with ID %s already exists", clientId.c_str());
		return nullptr;
	}

	addClientToMap(clientTypeEnum, clientId, client);
	return client;
}

web::websockets::session::validation::value TDealerAssistApp::accept_ws(const imaxa_connection_ptr& con, std::optional<security::JWToken> token)
{
	std::string resource = con->get_resource();
	resource = resource.substr(0, resource.find_first_of('?'));
	const auto fragments = yutils::Split(resource, "/", true);

	// require fragment size exactly 1 (clientId)
	if (fragments.size() != 2)
		return websocketpp::session::validation::reject;

	const std::string& clientType = fragments[0];
	const std::string& clientId = fragments[1];

	auto isLocalClient = IsLocalClient(con->get_remote_ip());

	auto client = createClient(clientType, clientId, token, isLocalClient);

	if (!client)
		return websocketpp::session::validation::reject;

	con->set_open_handler(std::bind(&TDealerAssistApp::on_open, this, std::placeholders::_1, client));
	con->set_close_handler(std::bind(&TDealerAssistApp::on_close, this, std::placeholders::_1, client));
	con->set_fail_handler([weakClient = std::weak_ptr<yprotocol::YProtocolClient>(client), this](imaxa_connection_hdl_ref hdl) {
		if (auto client = weakClient.lock())
		{
			RemoveClientData(client->UniqueIdentifier());

			if (mDealerAssistLogic)
				mDealerAssistLogic->ClearBetsStatisticsForHost(client->UniqueIdentifier());
		}
	});

	return websocketpp::session::validation::accept;
}

void TDealerAssistApp::on_open(imaxa_connection_hdl_ref hdl, const std::weak_ptr<yprotocol::YProtocolClient>& clientPtr)
{
	auto con = hdl.lock();

	auto client = clientPtr.lock();
	if (!client)
		return;

	Log(Info, "New client (%s) joined from %s!", client->UniqueIdentifier().c_str(), con->get_remote_ip().c_str());

	client->Reconnect(hdl);

	EDealerAssistEventRecipient recipient = EDealerAssistEventRecipient::All;

	if (std::dynamic_pointer_cast<DealerAssistHostClient>(client) != nullptr)
	{
		recipient = EDealerAssistEventRecipient::Gamehost;
	}

	client->SendNewServerMessage(yprotocol::EServerMessageType::Init, GetInitPacket(recipient).ToJSON());
}

void TDealerAssistApp::on_close(imaxa_connection_hdl_ref hdl, const std::weak_ptr<yprotocol::YProtocolClient>& clientPtr)
{
	auto con = hdl.lock();

	Log(Info, "Client from %s has left.", con->get_remote_ip().c_str());

	auto client = clientPtr.lock();
	if (client)
	{
		RemoveClientData(client->UniqueIdentifier());

		if (mDealerAssistLogic)
			mDealerAssistLogic->ClearBetsStatisticsForHost(client->UniqueIdentifier());
	}
}

bool TDealerAssistApp::IsLocalClient(const std::string& ip) const
{
	try
	{
		const boost::asio::ip::address_v4 clientAddr = boost::asio::ip::make_address_v4(ip);

		for (const auto& [interfaceName, networkInterface] : mNetworkInterfaces)
		{
			for (const auto& addressInfo : networkInterface.addresses_ip4)
			{
				if (addressInfo.address.is_unspecified())
				{
					continue;
				}

				// Check if client IP is in this network
				if (addressInfo.network.hosts().find(clientAddr) != addressInfo.network.hosts().end())
				{
					return true;
				}
			}
		}

		TLOG(LogApp, Warning, "IP %s is not a local client", ip.c_str());
		return false;
	}
	catch (const std::exception& e)
	{
		TLOG(LogApp, Error, "Error checking local client %s: %s", ip.c_str(), e.what());
		return false;
	}
}

bool TDealerAssistApp::CanPerformUnsecureAdminActions(const yprotocol::YProtocolClient& client) const
{
	auto* baseClient = dynamic_cast<const DealerAssistBaseClient*>(&client);
	return bAllowUnsecureAdminActions || (baseClient && baseClient->bIsLocal);
}

DealerAssistInitResponseDto TDealerAssistApp::GetInitPacket(const EDealerAssistEventRecipient recipient) const
{
	auto hello = DealerAssistInitResponseDto();

	// When game is selected use state of game otherwise use table state
	if (mDealerAssistLogic)
	{
		hello = mDealerAssistLogic->GetInitPacket();
	}
	else
	{
		hello.mTableState.mPhase = State->mDealingPhase;
		hello.mTableState.mSubPhase = State->mSubPhase;

		if (State->Croupier)
			hello.mCroupierInfo = *State->Croupier;
		else if (State->Supervisor)
			hello.mCroupierInfo = *State->Supervisor;

		if (State->SupervisorState)
		{
			hello.mSupervisorState = *State->SupervisorState;
		}
	}

	if (State->CurrentGameType)
	{
		hello.mGameType = State->CurrentGameType.value()._to_string();
	}

	if (State->CurrentGameInfo)
	{
		hello.mGameInfo = State->CurrentGameInfo.value();
	}

	hello.mChecksums = mChecksums;
	hello.mNumOfDecks = mNumOfDecks;
	hello.Recipient = recipient;
	hello.mTableId = mTableId;
	hello.mStreamUrl = mStreamUrl;
	hello.mStreamId = mStreamId;
	hello.mStreamType = mStreamType;
	hello.mAppVersion = GetAppVersion();
	hello.mTableMode = mTableMode;
	hello.mSupportedSupervisorActions = { ESupervisorAction(ESupervisorAction::VoidGame),     ESupervisorAction(ESupervisorAction::ChangeGame),
		                                  ESupervisorAction(ESupervisorAction::ChangeResult), ESupervisorAction(ESupervisorAction::CloseTable),
		                                  ESupervisorAction(ESupervisorAction::ContinueGame), ESupervisorAction(ESupervisorAction::ChangeShoe),
		                                  ESupervisorAction(ESupervisorAction::FreeHand) };


	Log(Info, "Init packet: %s", JsonSchema::PrintValueInline(hello.ToJSON()).c_str());
	return hello;
}

void TDealerAssistApp::OnPingTimer()
{
	PingClients();
}

void TDealerAssistApp::OnClientDisconnected(yprotocol::YProtocolClient& client)
{
	Log(Info, "Client %s disconnected. Erase client data.", client.UniqueIdentifier().c_str());
	RemoveClientData(client.UniqueIdentifier());
	yprotocol::YProtocol::OnClientDisconnected(client);
}

void TDealerAssistApp::Broadcast(const yprotocol::EventTemplate& eventTemplate, const json& data, std::optional<EClientType> target)
{
	if (!target)
	{
		for (auto type : EClientType::_values()) Broadcast(eventTemplate, data, type);
		return;
	}

	auto const event = yprotocol::Event(eventTemplate, data);
	const json eventJson = event.AsJsonObject();

	SharedScopedLock lock(mClients);
	auto clients = &mClients;

	for (const auto& [clientId, client] : clients[target->_to_index()]) client->SendNewServerMessage(yprotocol::EServerMessageType::Event, eventJson);
}

int TDealerAssistApp::LoadEnvironmentVariables()
{
	const int res = TApplication::LoadEnvironmentVariables();
	if (res)
		return res;

	if (!GetParamFromCache("Config"))
		LoadParamFromEnvironmentOptional("CONFIGURATION", "Config", "/config/live-table/live-table.conf");

	LoadParamFromEnvironmentOptional("CONFIGURATION_USER", "UserConfig", {});
	return 0;
}

void TDealerAssistApp::TryReconnect()
{
	Log(Info, "Attempting reconnection with target port %s", mTargetPort.c_str());
	try
	{
		Scanner = std::make_unique<THoneywellScannerConnection>(mTargetPort);
	}
	catch (const std::exception& e)
	{
		Log(Error, "Error opening port %s: %s", mTargetPort.c_str(), e.what());
	}

	Defer(
	  [this]() {
		  bConnecting = false;
		  mLastConnectAttempt = ytime::GetSystemTimeMsec();
		  if (Scanner)
		  {
			  Log(Important, "Listening for scans on port %s", Scanner->Port.c_str());
			  mSerialThread = std::thread(&TDealerAssistApp::SerialListener, this);
		  }
		  else
		  {
			  Log(Error, "Could not connect to a serial port. Will try again in ~%d seconds.", (int)std::roundf(mReconnectInterval * 1e-3f));
		  }
	  },
	  __FUNCTION__);
}

void TDealerAssistApp::TryReconnectWebsocketScanner()
{
	Log(Info, "Attempting reconnection to scanner client at %s", mWebsocketScannerClient->HostAddress.c_str());

	std::error_code ec = mWebsocketScannerClient->Connect();
	if (ec)
		Log(Error, "Error while connecting to scanner client: %s. Will try again in ~%d seconds.", ec.message().c_str(), (int)std::roundf(mReconnectInterval * 1e-3f));
	else
		Log(Important, "Success! Connected to scanner client at %s", mWebsocketScannerClient->HostAddress.c_str());

	Defer(
	  [this]() {
		  bConnecting = false;
		  mLastConnectAttempt = ytime::GetSystemTimeMsec();
	  },
	  __FUNCTION__);
}

void TDealerAssistApp::SerialListener()
{
	while (!pApp->shuttingDown())
	{
		std::string barcode;
		const int ret = Scanner->ReadNextCode(barcode);
		if (ret != 0)
			continue;

		OnSerialPortMessage(barcode);
	}

	Scanner.reset();
	Defer(
	  [this]() {
		  Log(Important, "No longer listening for scans on port %s", mTargetPort.c_str());
		  mSerialThread.join();
	  },
	  __FUNCTION__);
}

// Websocket message handlers
json TDealerAssistApp::ReceivedBarcode(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	if (!CanPerformUnsecureAdminActions(client))
	{
		Log(Error, "Non local client cannot perform this action");
		return { "Non local client cannot perform this action" };
	}

	auto code = req.GetParam().get<std::string>();
	Log(Info, "Scanned code %s", code.c_str());
	OnSerialPortMessage(code);
	return {};
}

json TDealerAssistApp::ChangeGame(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	ScopedLock lock(State);
	uint32_t gameId = 9999;
	try
	{
		Log(Info, "Requested changing game with data: %s", JsonSchema::PrintValueInline(req.GetParam()).c_str());
		const auto dto = SelectDealerGameRequestDto::FromJSON(req.GetParam());

		if (const auto game = dto.GetSelectedGame(); game.has_value())
			gameId = game.value();
		else if (const auto unfinishedGame = dto.GetUnfinishedGame(); unfinishedGame.has_value())
		{
			State->SupervisorState->bContinueUnfinishedGame = true;
			gameId = unfinishedGame.value();
		}

		Log(Info, "Requested changing to game with id %u", gameId);

		if (!mGameConfigs.contains(gameId))
			throw std::runtime_error(std::format("Game with id {} not found", gameId));

		if (mDealerAssistLogic && !mDealerAssistLogic->CanSwitchGame())
			throw std::runtime_error(std::format("Cannot switch game right now - table is likely not closed."));

		auto gameConfig = mGameConfigs[gameId].Config;
		auto gameTypeEnum = mGameConfigs[gameId].GameType;

		Log(Info, "Waiting for supervisor conformation");

		if (State->Supervisor)
		{
			State->SupervisorState->SelectedGameType = gameTypeEnum;
			State->SupervisorState->SelectedGameInfo = mGameConfigs[gameId];
			State->SupervisorState->SubPhase = ESupervisorSubPhase::ConfirmGameSelection;

			if (mDealerAssistLogic)
				mDealerAssistLogic->SupervisorSelectedGame(mGameConfigs[gameId]);
			else
				Broadcast(*mSupervisorStateChangedEvent, State->SupervisorState->ToJSON());
		}
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, std::format("Could not change game to {}: {}", mGameConfigs[gameId].DisplayName, e.what()));
	}

	json root(json::value_t::object);
	root["type"] = mGameConfigs[gameId].GameType._to_string();
	root["virtual"] = mGameConfigs[gameId].IsVirtual;

	return root;
}

json TDealerAssistApp::ChangedGameResult(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	SharedScopedLock lock(State);

	if (!mDealerAssistLogic)
		throw yprotocol::RequestError(req, std::format("Could not change result, missing selected game"));

	if (State->mDealingPhase != EDealerAssistPhase::DealingCards)
		throw yprotocol::RequestError(req, std::format("Could not change result, wrong phase {}", State->mDealingPhase._to_string()));

	auto supervisorState = mDealerAssistLogic->GetSupervisorState();
	if (!supervisorState.Active || supervisorState.RequestedAction != ESupervisorAction::ChangeResult)
		throw yprotocol::RequestError(req, std::format("Could not change result, supervisor is not present"));


	Log(Info, "Requested change result with new data: %s", JsonSchema::PrintValueInline(req.GetParam()).c_str());
	const auto reqJson = req.GetParam();

	const auto dto = ResultChangeRequestDto::FromJSON(reqJson);

	if (dto.GetUpdatedCards().empty())
		throw yprotocol::RequestError(req, std::format("Wrong request format"));

	mDealerAssistLogic->SetChangedGameResult(dto);

	return {};
}

json TDealerAssistApp::CancelSupervisorAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	Log(Info, "Requested cancel supervisor action");

	if (mDealerAssistLogic)
	{
		mDealerAssistLogic->ResetSupervisorAction();
	}
	else if (State->SupervisorState)
	{
		ScopedLock lock(State);
		State->SupervisorState->SelectedGameType.reset();
		State->SupervisorState->SelectedGameInfo.reset();
		State->SupervisorState->RequestedAction.reset();
		State->SupervisorState->SubPhase = ESupervisorSubPhase::GameSelection;
		State->SupervisorState->bContinueUnfinishedGame = false;
		Broadcast(*mSupervisorStateChangedEvent, State->SupervisorState->ToJSON());
	}

	return {};
}

void TDealerAssistApp::SetCurrentGame_AssumeWriteLock(const uint32_t gameId, bool bGameSwitched)
{
	if (!mGameConfigs.contains(gameId))
		throw std::runtime_error(std::format("Game with id {} not found", gameId));

	if (mDealerAssistLogic && !mDealerAssistLogic->CanSwitchGame())
		throw std::runtime_error(std::format("Cannot switch game right now - table is likely not closed."));

	const auto gameConfig = mGameConfigs[gameId].Config;
	auto gameTypeEnum = mGameConfigs[gameId].GameType;
	State->CurrentGameType = gameTypeEnum;
	State->CurrentGameInfo = mGameConfigs[gameId];

	Log(Info, "Loading game configuration for type %s and config: %s", gameTypeEnum._to_string(), JsonSchema::PrintValueInline(gameConfig).c_str());

	mDealerAssistLogic.reset();
	if (gameTypeEnum == EGameType::Baccarat || gameTypeEnum == EGameType::OpenBaccarat)
		mDealerAssistLogic = std::make_unique<TBaccaratDealerAssistGameLogic>(mTableId, gameTypeEnum == EGameType::OpenBaccarat, mGameConfigs[gameId].IsVirtual);
	else if (gameTypeEnum == EGameType::DragonTiger || gameTypeEnum == EGameType::OpenDragonTiger)
		mDealerAssistLogic = std::make_unique<TDragonTigerDealerAssistGameLogic>(mTableId, gameTypeEnum == EGameType::OpenDragonTiger, mGameConfigs[gameId].IsVirtual);
	else if (gameTypeEnum == EGameType::ThreeHeadedDragon)
		mDealerAssistLogic = std::make_unique<TThreeHeadedDragonDealerAssistGameLogic>(mTableId, mGameConfigs[gameId].IsVirtual);
	else
		throw std::runtime_error("Unknown game type.");

	State->SupervisorState->SelectedGameType.reset();
	State->SupervisorState->SelectedGameInfo.reset();
	State->SupervisorState->bContinueUnfinishedGame = false;

	mDealerAssistLogic->LoadSubconfiguration(gameConfig);
	mDealerAssistLogic->SetDealerAssistClient(mDealerAssistBackendClient);
	mDealerAssistLogic->SetGameflowSettings(mGameflowSettings);

	mDealerAssistLogic->OnBroadcastEvent += [this](const yserver::EventTemplate& eventTemplate, const EventDto& event) {
		auto recipient = event.Recipient;
		auto data = event.ToJSON();
		pApp->Defer(
		  [this, eventTemplate, data, recipient]() {
			  if (recipient == EDealerAssistEventRecipient::DealerConsole)
				  Broadcast(eventTemplate, data, EClientType::DealerConsole);
			  else if (recipient == EDealerAssistEventRecipient::Gamehost)
				  Broadcast(eventTemplate, data, EClientType::GameHost);
			  else
				  Broadcast(eventTemplate, data);
		  },
		  "mDealerAssistLogic->OnBroadcastEvent");
	};

	mDealerAssistLogic->OnStateUpdate += [this]() {
		pApp->Defer([this]() { StateUpdate(); }, "StateUpdate");
	};

	mDealerAssistLogic->OnSwitchGameEvent += [this]() {
		pApp->Defer(
		  [this]() {
			  ScopedLock lock(State);
			  if (State->SupervisorState->SelectedGameInfo)
				  SetCurrentGame_AssumeWriteLock(State->SupervisorState->SelectedGameInfo->Id, true);
			  else
				  Log(Error, "No game suggested for supervisor to confirm");
		  },
		  "SwitchGameEvent");
	};

	mDealerAssistLogic->OnReloginCroupierEvent += [this]() {
		pApp->Defer(
		  [this]() {
			  SharedScopedLock lock(State);

			  Log(Info, "Relogin croupier");
			  if (State->Croupier && !State->Croupier->ID.empty())
			  {
				  AuthorizeWorker(State->Croupier->ID, EDealerAssistAccessLevel::Croupier);
			  }
		  },
		  "ReloginCroupierEvent");
	};

	mDealerAssistLogic->OnBroadcastPhaseChangedEvent += [this](const yserver::EventTemplate& eventTemplate, const DealerAssistStateUpdateDto& event) {
		pApp->Defer(
		  [this, event]() {
			  State->mDealingPhase = event.GetPhase();
			  State->mSubPhase = event.subPhase;
			  State->mErrorCode = event.GetErrorCode();

			  Log(Info, "Game phase changed to %s", event.GetPhase()._to_string());

			  Broadcast(*mGamePhaseChangedEvent, event.ToJSON());
		  },
		  "ChangeGamePhaseAndNotify");
	};

	mDealerAssistLogic->OnTableCloseEvent += [this]() {
		if (mDealerAssistLogic)
			mDealerAssistLogic.reset();

		ScopedLock lock(State);
		State->CurrentGameType.reset();
		State->CurrentGameInfo.reset();
		ChangeGamePhaseAndNotify_AssumeLocked(EDealerAssistPhase::TableClosed);
	};

	if (!bGameSwitched && mDealerAssistBackendClient && mDealerAssistBackendClient->Connected())
		mDealerAssistLogic->OnDealerAssistBackendConnected(mRoundInfo);

	if (bGameSwitched && State->Croupier)
	{
		mDealerAssistLogic->OnGameSwitched(GetNumberOfAllPlayers(), *State->Croupier);
	}

	StateUpdate();
}

json TDealerAssistApp::ManageNumberOfPlayers(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	auto data = req.GetParam();
	auto obj = ReportNumOfActivePlayersDto::FromJSON(data);

	AddNumberOfPlayers(client.UniqueIdentifier(), obj.GetNumberOfAllPlayers());

	return {};
}

json TDealerAssistApp::PostChatMessage(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	ScopedLock lock(mChatMessages);
	auto data = req.GetParam();
	auto dto = ChatMessageDto::FromJSON(data);

	mChatMessages->add(dto);
	lock.unlock();

	Broadcast(*mChatMessageEvent, dto.ToJSON());

	return {};
}

json TDealerAssistApp::PostBetsStatistics(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const
{
	try
	{
		std::unordered_map<std::string, BetTypeHostStats> statsMap;
		const BetStatisticsDto bets = BetStatisticsDto::FromJSON(req.GetParam());
		for (const auto& betTypeInfo : bets.BetStatistics) { statsMap[betTypeInfo.BetType] = { betTypeInfo.Amount, betTypeInfo.NumOfPlayers }; }

		if (!mDealerAssistLogic)
			throw yprotocol::RequestError(req, std::format("Missing selected game"));

		mDealerAssistLogic->AddHostBetsStatistics(client.UniqueIdentifier(), statsMap);
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, std::format("Could not parse bet statistics: {}", e.what()));
	}

	return {};
}

json TDealerAssistApp::GetChatMessages(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const
{
	auto messages = mChatMessages.getCopy().get();
	json chatMessages(json::value_t::array);
	for (const auto& message : messages) { chatMessages.push_back(message.ToJSON()); }

	json responseMessages(json::value_t::object);
	responseMessages["messages"] = std::move(chatMessages);

	return responseMessages;
}

json TDealerAssistApp::GetAvailableGames(const yprotocol::YProtocolClient& client, const yprotocol::Request& req)
{
	std::vector<DealerGameInfoDto> availableGames;
	for (const auto& [id, gameConfig] : mGameConfigs) { availableGames.push_back(gameConfig); }

	auto dto = GetAvailableGamesDto();
	dto.AvailableGames = availableGames;

	if (mRoundInfo && mRoundInfo->Status == ERoundStatus::Open)
	{
		auto unfinishedRoundType = EGameType::_from_string(mRoundInfo->GameType.c_str());

		// Find matching game in availableGames
		const auto it = std::find_if(dto.AvailableGames.begin(), dto.AvailableGames.end(),
		                             [&unfinishedRoundType](const DealerGameInfoDto& game) { return game.GameType == unfinishedRoundType; });

		if (it != dto.AvailableGames.end())
		{
			// Assign the found game info to UnfinishedGame
			dto.UnfinishedGame = *it;    // This copies the matching DealerGameInfoDto
		}
	}

	return dto.ToJSON();
}

json TDealerAssistApp::DealerAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const
{
	if (!CanPerformUnsecureAdminActions(client))
	{
		Log(Error, "Non local client cannot perform this action");
		return { "Non local client cannot perform this action" };
	}

	auto data = req.GetParam("action").get<std::string>();
	auto action = EDealerAction::_from_string_nocase_nothrow(data.c_str());

	if (!action)
		throw yprotocol::RequestError(req, "Invalid action type");

	Log(Info, "Requested action %s", action.value()._to_string());

	try
	{
		if (!mDealerAssistLogic)
			throw yprotocol::RequestError(req, std::format("Missing selected game"));

		return mDealerAssistLogic->HandleDealerAction(*action);
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, e.what());
	}
}

json TDealerAssistApp::SupervisorAction(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const
{
	auto data = req.GetParam("action").get<std::string>();
	auto action = ESupervisorAction::_from_string_nocase_nothrow(data.c_str());

	if (!action)
		throw yprotocol::RequestError(req, "Invalid action type");

	Log(Info, "Requested supervisor action %s", action.value()._to_string());

	try
	{
		if (!mDealerAssistLogic)
			throw yprotocol::RequestError(req, std::format("Missing selected game"));

		return mDealerAssistLogic->HandleSupervisorAction(*action);
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, e.what());
	}
}

json TDealerAssistApp::FlagStateChanged(const yprotocol::YProtocolClient& client, const yprotocol::Request& req) const
{
	if (!CanPerformUnsecureAdminActions(client))
	{
		Log(Error, "Non local client cannot perform this action");
		return { "Non local client cannot perform this action" };
	}

	auto data = req.GetParam();
	auto obj = FlagStateChangeDto::FromJSON(data);

	try
	{
		if (!mDealerAssistLogic)
			throw yprotocol::RequestError(req, std::format("Missing selected game"));

		return mDealerAssistLogic->HandleFlagStateChange(obj);
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, e.what());
	}
}

void TDealerAssistApp::OnSerialPortMessage(const std::string& data)
{
	std::optional<FScannedCard> cardCode;
	std::string croupierBadge;

	auto trimmed = yutils::TrimReturnCharacters(data);

	switch (trimmed.length())
	{
		case 9: {
			if (trimmed[0] == '#')
			{
				int idx = 1;
				for (; idx < 9; idx++)
				{
					const char toUpper = std::toupper(trimmed[idx]);
					if (!((toUpper >= '0' && toUpper <= '9') || (toUpper >= 'A' && toUpper <= 'F')))
						break;
				}

				if (idx == 9)
				{
					croupierBadge = trimmed.substr(1, 8);    // remove the '#' character
				}
			}
			break;
		}
		default:
			if (trimmed.length() >= 3)
			{
				cardCode = trimmed;
			}
			else
			{
				Log(Error, "Unknown barcode: %s", trimmed.c_str());
			}
	};

	if (cardCode)
	{
		Log(Info, "Read %s", cardCode->to_string().c_str());

		if (!mDealerAssistLogic)
		{
			Log(Warning, "No game selected, ignoring scanned card");
			return;
		}

		mDealerAssistLogic->HandleScannedCard(*cardCode);
	}

	if (!croupierBadge.empty())
	{
		Log(Info, "Scanned croupier badge: %s", croupierBadge.c_str());
		HandleBadgeScanned(croupierBadge);
	}
}

void TDealerAssistApp::HandleBadgeScanned(const std::string& badgeID)
{
	bool showWarning = false;

	SharedScopedLock lock(State);

	// Handle logout
	if (State->Croupier && State->Croupier->ID == badgeID)
	{
		if (mDealerAssistLogic && mDealerAssistLogic->CanLogoutCroupier())
		{
			Log(Info, "Logout croupier");

			mDealerAssistLogic->SaveGameResultBeforeLogout().then([this](boost::future<void> future) {
				try
				{
					future.get();
					AuthorizeWorker();
				}
				catch (const std::exception& e)
				{
					Log(Warning, "Error while saving game result before logout: %s", e.what());
				}
			});
		}
		else
			showWarning = true;
	}
	else if (State->Supervisor && State->Supervisor->ID == badgeID)
	{
		Log(Info, "Logout supervisor");

		if (State->SupervisorState->SubPhase == ESupervisorSubPhase::ConfirmGameSelection && mRoundInfo.has_value() && mRoundInfo->Status == ERoundStatus::Open &&
		    !State->SupervisorState->bContinueUnfinishedGame)
		{
			TLOG(LogDragonTigerDealerAssist, Info, "Selected game was different than unfinished game. We need to void unfinished game first and then logout supervisor")

			json roundState = json(json::value_t::object);
			roundState["status"] = ERoundStatus(ERoundStatus::Void)._to_string();
			roundState["gameType"] = mRoundInfo->GameType;
			roundState["round"] = mRoundInfo->ID;
			roundState["state"] = mRoundInfo->State;

			mDealerAssistBackendClient->SaveResult(roundState).then(boost::launch::sync, [this](boost::future<YResponse> future) {
				const YResponse response = future.get();
				if (response.Status == EMessageStatus::ResponseOk)
				{
					TLOG(LogDragonTigerDealerAssist, Info, "Response on saving round: %s", JsonSchema::PrintValueInline(response.Message.Body()).c_str())
					mRoundInfo.reset();
					AuthorizeWorker();
				}
				else
				{
					auto errorObj = response.Message.Body();

					const json* errContext = FindMember(errorObj, "context");
					auto errorCode = FindMember(*errContext, "code");

					if (errorCode && !errorCode->is_null())
					{
						TLOG(LogDragonTigerDealerAssist, Info, "Round saving with error: %s", errorCode->get<std::string>().c_str());

						AuthorizeWorker();
					}
				}
			});
		}
		else
		{
			AuthorizeWorker();
		}
	}
	else if (State->Croupier && State->Croupier->ID != badgeID)
	{
		if (mDealerAssistLogic)
		{
			Log(Info, "Switch croupier with ID: %s", badgeID.c_str());
			AuthorizeWorker(badgeID);
		}
		else
			showWarning = true;
	}
	else if (State->Supervisor && State->Supervisor->ID != badgeID)
	{
		if (mDealerAssistLogic)
		{
			Log(Info, "Switch croupier with ID: %s", badgeID.c_str());
			AuthorizeWorker(badgeID);
		}
		else
			showWarning = true;
	}
	else
	{
		Log(Info, "Login croupier with ID: %s", badgeID.c_str());
		AuthorizeWorker(badgeID);
	}

	if (showWarning)
	{
		Log(Info, "Croupier logout or switch not allowed at this phase of game.");
		auto dto = NotifyDealerConsoleDto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::CroupierChangeInvalidState),
		                                  "Croupier logout or switch not allowed at this phase of game.");
		Broadcast(*mNotifyDealerConsoleEvent, dto.ToJSON(), EClientType::DealerConsole);
	}
}

void TDealerAssistApp::AuthorizeWorker(const std::string& badgeID, std::optional<backend::EDealerAssistAccessLevel> accessLevel)
{
	SharedScopedLock lock(State);

	json auth(json::value_t::object);

	bool bActiveSupervisor = false;
	if (mDealerAssistLogic)
		bActiveSupervisor = mDealerAssistLogic->GetSupervisorState().Active;

	if (!accessLevel)
	{
		accessLevel = State->mDealingPhase == EDealerAssistPhase::TableClosed || bActiveSupervisor ? EDealerAssistAccessLevel(EDealerAssistAccessLevel::Supervisor) :
		                                                                                             EDealerAssistAccessLevel(EDealerAssistAccessLevel::Croupier);

		accessLevel = badgeID.empty() ? EDealerAssistAccessLevel(EDealerAssistAccessLevel::None) : accessLevel;
	}

	auth["accessLevel"] = accessLevel.value()._to_string();
	auth["id"] = badgeID;
	auth["login"] = true;

	Log(Info, "Authorizing croupier with ID: %s and access level %s", badgeID.c_str(), accessLevel.value()._to_string());

	mDealerAssistBackendClient->Request(EDealerAssistRequest(EDealerAssistRequest::Authenticate)._to_string(), auth)
	  .then(boost::launch::sync, [this, badgeID](boost::future<YResponse> fut) {
		  YResponse response = fut.get();
		  if (response.Status == EMessageStatus::ResponseOk)
		  {
			  Log(Normal, "Received response on croupier login/logout");
			  if (!badgeID.empty())
			  {
				  NotifyDealerConsoleDto dto { EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::AuthenticationSuccess) };
				  Broadcast(*mNotifyDealerConsoleEvent, dto.ToJSON(), EClientType::DealerConsole);
			  }
		  }
		  else
		  {
			  Log(Error, "Dealer login/logout failed with error: %s", response.ErrorMessage().c_str());
			  NotifyDealerConsoleDto dto { EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::AuthenticationFailed),
				                           response.ErrorMessage().c_str() };
			  Broadcast(*mNotifyDealerConsoleEvent, dto.ToJSON(), EClientType::DealerConsole);
		  }
	  });
}

// Called when is no game active game
void TDealerAssistApp::SupervisorLogin_AssumeWriteLock()
{
	if (!State->Supervisor)
		Log(Warning, "Supervisor not logged in");

	if (State->mDealingPhase == EDealerAssistPhase::TableClosed)
	{
		State->SupervisorState->Active = true;
		State->SupervisorState->SubPhase = ESupervisorSubPhase::GameSelection;

		Broadcast(*mSupervisorStateChangedEvent, State->SupervisorState->ToJSON());
	}
	else
		Log(Error, "Supervisor login not allowed at this phase of game.");
}

// Called when is no game active game
void TDealerAssistApp::SupervisorLogout_AssumeWriteLock()
{
	if (State->SupervisorState->SubPhase == ESupervisorSubPhase::ConfirmGameSelection)
	{
		if (State->SupervisorState->SelectedGameInfo)
			SetCurrentGame_AssumeWriteLock(State->SupervisorState->SelectedGameInfo->Id);
		else
			Log(Error, "No game suggested for supervisor to confirm");
	}
	else
	{
		State->SupervisorState->SelectedGameType.reset();
		State->SupervisorState->SelectedGameInfo.reset();
	}

	State->Supervisor.reset();
	State->SupervisorState->Reset();

	Broadcast(*mSupervisorStateChangedEvent, State->SupervisorState->ToJSON());
}

// Handling players
void TDealerAssistApp::AddNumberOfPlayers(const std::string& clientId, uint64_t numOfPlayers)
{
	Log(Info, "Adding %d players for client %s", numOfPlayers, clientId.c_str());
	ScopedLock lock(mPlayers);
	mPlayers->insert_or_assign(clientId, numOfPlayers);
	lock.unlock();

	auto numOfAllPlayers = GetNumberOfAllPlayers();

	if (mDealerAssistLogic)
		mDealerAssistLogic->OnNumberOfPlayersChanged(numOfAllPlayers);
}

uint32_t TDealerAssistApp::GetNumberOfAllPlayers() const
{
	uint32_t sum = 0;
	SharedScopedLock<> lock(mPlayers);
	for (const auto& [_, num] : &mPlayers) sum += num;
	Log(Info, "Number of all players: %d", sum);
	return sum;
}

void TDealerAssistApp::RemoveClientData(const std::string& clientId)
{
	ScopedLock lockPlayers(mPlayers);
	mPlayers->erase(clientId);
	lockPlayers.unlock();

	ScopedLock lockClients(mClients);

	// Iterate over each map in the array
	for (auto& clientMap : &mClients)
	{
		// If the client with the given ID is found in the map, remove it
		auto it = clientMap.find(clientId);
		if (it != clientMap.end())
		{
			clientMap.erase(it);
			break;
		}
	}

	Log(Info, "Client with ID %s has been removed", clientId.c_str());
	Log(Info, "Number of host clients: %d", mClients->at(EClientType(EClientType::GameHost)._to_index()).size());
	Log(Info, "Number of dealer console clients: %d", mClients->at(EClientType(EClientType::DealerConsole)._to_index()).size());
	lockClients.unlock();

	if (mDealerAssistLogic)
		mDealerAssistLogic->OnNumberOfPlayersChanged(GetNumberOfAllPlayers());
}

version::Version TDealerAssistApp::GetAppVersion() const
{
	try
	{
		auto manifest = GetAppManifest();
		return manifest.version;
	}
	catch (const std::exception& e)
	{
		return version::Version::FromString(VERSION_STRING);
	}
}

version::Manifest TDealerAssistApp::GetAppManifest() const
{
	std::filesystem::path manifestPath = "manifest.json";
	std::ifstream manifestFile(manifestPath);
	if (!manifestFile.is_open())
		throw std::runtime_error("Could not find manifest file at " + manifestPath.string());

	json manifestJson;
	try
	{
		manifestFile >> manifestJson;
	}
	catch (const std::exception& e)
	{
		throw std::runtime_error("Could not parse manifest.json: " + std::string(e.what()));
	}

	try
	{
		return version::Manifest::FromJSON(manifestJson);
	}
	catch (const std::exception& e)
	{
		throw std::runtime_error("Could not deserialize manifest.json: " + std::string(e.what()));
	}
}

void TDealerAssistApp::StateUpdate()
{
	json response(json::value_t::object);
	response["state"] = GetInitPacket(EDealerAssistEventRecipient::All).ToJSON();
	Log(Info, "Sending state update for all dealer console clients");
	Broadcast(*mUpdateStateEvent, response);
}

void TDealerAssistApp::ChangeGamePhaseAndNotify(const EDealerAssistPhase newPhase, const std::optional<uint32_t> subPhase)
{
	ScopedLock lockTableState(State);
	ChangeGamePhaseAndNotify_AssumeLocked(newPhase, subPhase);
}

void TDealerAssistApp::ChangeGamePhaseAndNotify_AssumeLocked(const EDealerAssistPhase newPhase, std::optional<uint32_t> subPhase)
{
	Log(Info, "Game phase changed from %s to %s", State->mDealingPhase._to_string(), newPhase._to_string());
	State->mDealingPhase = newPhase;
	State->mSubPhase = subPhase;

	if (State->mDealingPhase == EDealerAssistPhase::RoundEnd)
		mRoundInfo.reset();

	DealerAssistStateUpdateDto stateUpdate = DealerAssistStateUpdateDto(State->mDealingPhase, 0);

	if (State->mSubPhase)
	{
		Log(Info, "Game sub-phase changed from %i to %i", State->mSubPhase, *subPhase);
		stateUpdate.subPhase = *State->mSubPhase;
	}

	if (State->mErrorCode)
		stateUpdate.SetErrorCode(*State->mErrorCode);

	Broadcast(*mGamePhaseChangedEvent, stateUpdate.ToJSON());
}
