set(DEALER_ASSIST_BACKEND_SOURCES
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/backend/TDealerAssistBackendApp.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/backend/DealerAssistBackendClient.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/backend/DealerAssistBackendSharedTypes.cpp
)

set(DEALER_ASSIST_SOURCES
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/honeywell_scanner.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistBaseClient.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/TDealerAssistCroupierClient.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/TDealerAssistHostClient.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/TDealerAssistApp.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/TDealerAssistGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistInitResponseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistStateUpdateDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ReportNumOfActivePlayersDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/NotifyDealerConsoleDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistCroupierChangeDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/CardBurnDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/AddCardDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ToggleChatDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/RoundsUntilCloseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ChatMessageDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/FlagStateChangeDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/BetStatisticsDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/SelectDealerGameRequestDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GetAvailableGamesDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ResultChangeRequestDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ResultChangeResponseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistTableBackendClient.cpp
        ${PROJECT_SOURCE_DIR}/src/common/CreditArray.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/CardGameBetStatistics.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratStake.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/ScannedCard.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/TVirtualDealer.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GameRecordDto.cpp
)

set(DEALER_ASSIST_SOURCES_BACCARAT
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/TCardBetSecurityManager.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TBetsSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/baccarat/TBaccaratDealerAssistGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/backend/DealerAssistBackendSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/CardGameSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dragontiger/TDragonTigerDealerAssistGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/threeheadeddragon/TThreeHeadedDragonDealerAssistGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TDealerGamesExtraData.cpp
)

add_executable(abbiati-wheel-server ${PROJECT_SOURCE_DIR}/src/dealer-assist/TAbbiatiWheelApp.cpp ${PROJECT_SOURCE_DIR}/src/dealer-assist/abbiati_transport.cpp)

add_executable(dealer-assist ${DEALER_ASSIST_SOURCES} ${DEALER_ASSIST_SOURCES_BACCARAT})

add_executable(dealer-assist-backend ${DEALER_ASSIST_BACKEND_SOURCES})

target_include_directories(abbiati-wheel-server PRIVATE ${PROJECT_SOURCE_DIR}/include/dealer-assist)
target_include_directories(dealer-assist PRIVATE ${PROJECT_SOURCE_DIR}/include/dealer-assist ${PROJECT_SOURCE_DIR}/include/yserver)
target_include_directories(dealer-assist-backend PRIVATE ${PROJECT_SOURCE_DIR}/include/dealer-assist)

target_link_libraries(abbiati-wheel-server rtfwk-nogfx yprotocol-server)
target_link_libraries(dealer-assist rtfwk-nogfx yprotocol-server loki-log yprotocol-client service-publisher)
target_link_libraries(dealer-assist-backend loki-log yprotocol-server ${ROCKS_DB_LIB})

add_version_define(dealer-assist)
