//
// Created by <PERSON><PERSON><PERSON> on 25. 11. 24.
//

#include "TThreeHeadedDragonDealerAssistGameLogic.h"

#include "Cryptography.h"
#include "TApplication.h"
#include "dealer-assist/TDealerAssistGameLogic.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"
#include "dealer-assist/dto/AddCardDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/NotifyDealerConsoleDto.h"

DEFINE_LOG_CATEGORY(LogThreeHeadedDragonDealerAssist, "three-headed-dragon-dealer-assist-logic")

using namespace dealer_assist::backend;
using namespace dealer_assist;
using namespace yserver::gamehost;

TThreeHeadedDragonDealerAssistGameLogic::TThreeHeadedDragonDealerAssistGameLogic(std::string tableId, const bool bVirtualGame) :
    TDealerAssistGameLog<PERSON>(EGameType::ThreeHeadedDragon, std::move(tableId), bVirtualGame)
{
	State->GameLogic = std::make_unique<ThreeHeadedDragonGameLogic>();
	State->Table.mErrorCode = EDealerAssistTableErrorCode::BackendDisconnected;


	mOpenBetTypes = { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::RedDragon)._to_string(),
		              EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::BlackDragon)._to_string(),
		              EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::GoldenDragon)._to_string() };
}

TThreeHeadedDragonDealerAssistGameLogic::~TThreeHeadedDragonDealerAssistGameLogic() {}

void TThreeHeadedDragonDealerAssistGameLogic::OnConfigLoaded(const std::filesystem::path& filename)
{
	TDealerAssistGameLogic::OnConfigLoaded(filename);

	mBetsOpenTime = GetConfig("bets-open-timer").get<uint32_t>();
	mPauseAtRoundFinishTime = GetConfig("pause-at-round-finish-timer").get<uint32_t>();
	mNewRoundPreparationTime = GetConfig("new-round-preparation-timer").get<uint32_t>();
	mDecisionTime = GetConfigOptional("decision-open-timer", 4000).get<uint32_t>();
	mPauseBeforeRoundEndTime = GetConfigOptional("pause-before-round-end-timer", 3500).get<uint32_t>();
	mCutCardBarcode = GetConfig("cut-card-barcode").get<std::string>();
	bShouldCheckDuplicateCards = GetConfig("check-duplicate-cards").get<bool>();
	mPauseAtCardBurnEndTime = GetConfig("pause-at-card-burn-end-timer").get<uint64_t>();

	State->ActionsState.bChatEnabled = GetConfig("chat-enabled").get<bool>();

	State->VirtualDealerLogic = std::make_shared<TVirtualDealerLogic>(8, 69);

	mBetTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mBetTimer);

		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Bets are closed. Dealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);
		  StartTimer(mBetClosedTimer);
	  },
	  mBetsOpenTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Open bets timer");


	mBetClosedTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mBetClosedTimer);

		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Dealing cards begin");

		  // Report dealing cards
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);

		  if (bVirtualGame)
			  DealWithVirtualDealer();
	  },
	  mPauseAtBetClosedTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause at bet closed timer");

	mDecisionTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mDecisionTimer);

		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Decision bets are closed. Revealing cards begin");

		  // Report closed bets
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::BetsClosed);

		  // Report dealing cards
		  ChangeGamePhaseAndNotify(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
		  RevealTigerCard();
	  },
	  mDecisionTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Decision bets timer");

	mNewRoundPreparationTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mNewRoundPreparationTimer);
		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Waiting for new round to begin is over.");
		  OnRoundBegin();
	  },
	  mPauseAtRoundFinishTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause when one round is closed and dealer wait for new round to begin.");

	mPauseAfterCardBurnTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAfterCardBurnTimer);
		  ScopedLock lock(State);
		  OpenNewRound_AssumeWriteLock();
	  },
	  mPauseAtCardBurnEndTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause after last card burn and before new round begins.");

	mPauseAtRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mPauseAtRoundEndTimer);
		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Waiting at round end / void is over.");
		  ScopedLock lock(State);

		  if (CanCloseTable())
		  {
			  lock.unlock();
			  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Closing table.");
			  LogoutCroupier();
		  }
		  else if (State->ActionsState.bGameChangeRequested)
		  {
			  if (!CanChangeGame())
			  {
				  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Game change requested but waiting for countdown (%d/%d rounds). Opening new round.",
				       State->RoundsPlayedCounter, State->GameflowConfig.RoundsBeforeGameChange);
				  OpenNewRound_AssumeWriteLock();
			  }
			  else if (!State->SupervisorState->SelectedGameType.has_value())
			  {
				  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Could not change game, no game selected. Opening new round.");
				  OpenNewRound_AssumeWriteLock();
			  }
			  else
			  {
				  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Switching game.");

				  lock.unlock();
				  rtfwk_sdl2::pApp->Defer([this]() { OnSwitchGameEvent(); }, "Switch game event");
			  }
		  }
		  else if (State->bShouldBurnCards)
		  {
			  ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

			  if (bVirtualGame)
			  {
				  ShoeChanged_AssumeWriteLock();
			  }
		  }
		  else
		  {
			  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Opening new round");
			  OpenNewRound_AssumeWriteLock();
		  }
	  },
	  mPauseAtRoundFinishTime, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Pause at round end timer");

	mCardRevealTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  StopTimer(mCardRevealTimer);
		  TLOG(LogThreeHeadedDragonDealerAssist, Info, "Revealing card");
		  RevealTigerCard();
	  },
	  1500, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Reveal card timer");

	// Stop autostart of timers
	StopAllTimers();
}

// When backend is connected we request table history (from last cut card event)
void TThreeHeadedDragonDealerAssistGameLogic::OnDealerAssistBackendConnected(std::optional<backend::RoundInfoDto> round)
{
	ScopedLock lock(State);
	State->Table.mErrorCode.reset();
	bool updateState = false;

	if (round.has_value() && round->Status == backend::ERoundStatus::Open)
	{
		if (!round->State.is_null())
		{
			State->Table = TableState::FromJSON(round->State);
			State->GameLogic->SetCurrentStateOfCards(State->Table.mCards);

			TLOG(LogThreeHeadedDragonDealerAssist, Info, "Game was not finished on backend, restoring state.");
			updateState = true;
		}

		if (round->ID != State->Table.mRoundID)
		{
			TLOG(LogThreeHeadedDragonDealerAssist, Info, "Saved round ID in state %llu, received round ID %u are different", State->Table.mRoundID.value_or(0),
			     round->ID);
			State->Table.mRoundID = round->ID;

			updateState = true;
		}
	}

	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::WaitingForCroupier);
	lock.unlock();

	if (updateState)
		OnStateUpdate();

	DealerAssistBackendClient->GetTableHistory(GameType._to_string()).then(boost::launch::sync, [this](boost::future<std::vector<GameRecordDto>> futureHistory) {
		ScopedLock lock(State);
		State->GameRecords = futureHistory.get();
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Received history with %lu records", State->GameRecords.size());

		if (State->GameRecords.size() > 0)
			OnStateUpdate();
	});
}

void TThreeHeadedDragonDealerAssistGameLogic::OnGameSwitched(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Game switched.");

	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;

	StopAllTimers();

	State->bShouldBurnCards = true;
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

	if (bVirtualGame)
		ShoeChanged_AssumeWriteLock();
}

DealerAssistInitResponseDto TThreeHeadedDragonDealerAssistGameLogic::GetInitPacket() const
{
	SharedScopedLock lockTableState(State);

	TableState tableState = State->Table;

	auto phase = State->Table.mPhase;

	auto hello = DealerAssistInitResponseDto();

	if (phase == EDealerAssistPhase::DealingCards || phase == EDealerAssistPhase::RoundEnd)
	{
		json result = json::object();
		result["tigerHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::Tiger);
		result["goldenDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::GoldenDragon);
		result["redDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::RedDragon);
		result["blackDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::BlackDragon);

		tableState.mDealingPhase = State->GameLogic->GetDealingPhase();
		tableState.mHandValue = result;
		tableState.mCards = State->GameLogic->GetCurrentStateOfCards();
		tableState.mRoundID = State->Table.mRoundID;
	}

	if (phase == EDealerAssistPhase::BetsOpen)
	{
		auto now = ytime::GetTimeMsec();
		auto remainingTimeInRound = mBetsOpenTime - (now - tableState.mTimestamp.value_or(0));
		auto remainingTimeInSeconds = remainingTimeInRound / 1000;
		tableState.mRemainingBettingTimeInSeconds = remainingTimeInSeconds;
	}

	if (phase == EDealerAssistPhase::CardBurn || State->ShoeChangeState.CutCardDrawn)
	{
		hello.mShoeChangeState = State->ShoeChangeState;
	}

	hello.mBetTimeInSec = mBetsOpenTime / 1000;
	hello.mDecisionTimeInSec = mDecisionTime / 1000;

	hello.mPlayerCount = State->NumOfAllPlayers;
	hello.mActionsState = State->ActionsState;

	if (State->Croupier)
	{
		hello.mCroupierInfo = *State->Croupier;
	}

	if (State->SupervisorState)
	{
		hello.mSupervisorState = *State->SupervisorState;
	}

	if (State->RoundsUntilChange)
		hello.mRoundsUntilChange = *State->RoundsUntilChange;

	hello.mHistoryRecords = State->GameRecords;
	hello.mGameType = EGameType(EGameType::ThreeHeadedDragon)._to_string();
	hello.mTableState = tableState;
	return hello;
}


void TThreeHeadedDragonDealerAssistGameLogic::HandleScannedCard(const FScannedCard& data)
{
	ScopedLock lockTableState(State);
	if (data.Suite() == ECardSuite::None || data.Face() == ECardFace::None)
	{
		if (data.Barcode() == mCutCardBarcode)
			HandleCutCard_AssumeWriteLock();
		return;
	}

	if (State->SupervisorState->Active && State->ActionsState.bSupervisorCalled)
	{
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Ignoring card scan, supervisor called");
		return;
	}

	if (State->Table.mPhase == EDealerAssistPhase::CardBurn)
	{
		HandleCardBurn_AssumeWriteLock(data);
		return;
	}
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
	{
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Ignoring card scan, not in dealing or card burn phase");
		return;
	}

	if (IsCardOnTable_AssumeReadLock(data) && bShouldCheckDuplicateCards)
	{
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Ignoring card scan, card already on table");
		json context = json::object();
		context["reason"] = "Card already on table";
		auto dto = NotifyDealerConsoleDto(EDealerAssistTableNotificationCode(EDealerAssistTableNotificationCode::DuplicateCard), context);
		dto.Recipient = EDealerAssistEventRecipient::DealerConsole;
		OnBroadcastEvent(*mNotifyDealerConsoleEvent, dto);

		return;
	}

	if (bVirtualGame)
	{
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Ignoring card scan, virtual dealer is dealing cards");
		return;
	}

	if (State->GameLogic->IsGameFinished())
	{
		TLOG(LogThreeHeadedDragonDealerAssist, Info, "Ignoring card scan, game already finished");
		return;
	}

	DealCards_AssumedLockedWrite(data);
}

void TThreeHeadedDragonDealerAssistGameLogic::DealCards_AssumedLockedWrite(const FScannedCard& data)
{
	// Get dealing phase before we add card
	auto dealingPhase = State->GameLogic->GetDealingPhase();
	const auto cardValue = data.ToInt() % 1000;

	if (dealingPhase == EThreeHeadedDragonDealingPhase::TigerCard)
	{
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::FaceDown);
		State->Table.mFaceDownCards[dealingPhase] = cardValue;
		AddCard_AssumeWriteLock(faceDownCard);
	}
	else
		AddCard_AssumeWriteLock(cardValue);

	State->Table.mCardPosition = dealingPhase;
	State->Table.mDealingPhase = EThreeHeadedDragonDealingPhase::_from_index(dealingPhase);
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	SaveIntermediateRoundState_AssumeReadLock();

	// State->CardsOnTable.insert(data);

	auto dto = AddThreeHeadedDragonCardDto();
	dto.mScannedCard = dealingPhase == EThreeHeadedDragonDealingPhase::TigerCard ? faceDownCard : cardValue;
	dto.mCardPosition = dealingPhase;

	if (State->GameLogic->GetDealingPhase() != EThreeHeadedDragonDealingPhase::Finished)
	{
		dto.mNextCardPosition = State->GameLogic->GetDealingPhase();
	}

	switch (State->GameLogic->GetDealingPhase())
	{
		case EThreeHeadedDragonDealingPhase::TigerCard: dto.TigerHandValue = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::Tiger); break;
		case EThreeHeadedDragonDealingPhase::BlackDragonCard: dto.BlackDragonHandValue = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::BlackDragon); break;
		case EThreeHeadedDragonDealingPhase::RedDragonCard: dto.GoldenDragonHandValue = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::RedDragon); break;
		case EThreeHeadedDragonDealingPhase::GoldenDragonCard: dto.RedDragonHandValue = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::GoldenDragon); break;
		default: break;
	}

	OnBroadcastEvent(*mAddCardEvent, dto);

	if (State->GameLogic->GetDealingPhase() == EThreeHeadedDragonDealingPhase::Finished)
	{
		if (State->GameflowConfig.SkipDecisionIfNoBets && !mBetsStatistics->HasBetsOnFields(mOpenBetTypes))
		{
			TLOG(LogThreeHeadedDragonDealerAssist, Info, "No bet on dragon fields, skip Decision phase.");
			TLOG(LogThreeHeadedDragonDealerAssist, Info, "Deal tiger side!");
			StartTimer(mCardRevealTimer);
		}
		else
		{
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::Decision);
			TLOG(LogThreeHeadedDragonDealerAssist, Info, "Start decision phase!");
			StartTimer(mDecisionTimer);
		}
	}
	else if (bVirtualGame)
	{
		DealWithVirtualDealer_AssumeWriteLock();
	}
}

void TThreeHeadedDragonDealerAssistGameLogic::HandleVirtualDealerCard_AssumeWriteLock(const FScannedCard& data)
{
	DealCards_AssumedLockedWrite(data);
}

void TThreeHeadedDragonDealerAssistGameLogic::HandleCroupierLogin(uint32_t numOfPlayers, const DealerAssistWorker& croupierInfo)
{
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Croupier login detected.");
	ScopedLock lockTableState(State);
	State->Croupier = std::make_unique<DealerAssistWorker>(croupierInfo);
	State->NumOfAllPlayers = numOfPlayers;
	StopTimer(mBetTimer);
	StopTimer(mNewRoundPreparationTimer);
	StopTimer(mPauseAtRoundEndTimer);


	if (State->Table.mRoundID.has_value())    // Round already has value, we are in the middle of the round
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::DealingCards, EDealingSubPhase::Dealing);
	else
	{
		State->bShouldBurnCards = true;
		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::ShoeChange);

		if (bVirtualGame)
			ShoeChanged_AssumeWriteLock();
	}

	OnStateUpdate();
}

void TThreeHeadedDragonDealerAssistGameLogic::OnRoundBegin()
{
	ScopedLock lockTableState(State);
	OnRoundBegin_AssumeWriteLock();
}

void TThreeHeadedDragonDealerAssistGameLogic::OnRoundBegin_AssumeWriteLock()
{
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "New round begins - bets are open!");
	State->Table.mTimestamp = ytime::GetTimeMsec();
	State->Table.mWinner.reset();

	ReportNumberOfPlayers_AssumeReadLock();
	ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::BetsOpen);
	StartTimer(mBetTimer);
}

void TThreeHeadedDragonDealerAssistGameLogic::OnPauseBeforeRoundEnd()
{
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Pausing before round end.");

	mPauseBeforeRoundEndTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>([this]() { OnRoundEnd(); }, mPauseBeforeRoundEndTime,
	                                                                                   rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE, "Pause before round end timer");
}

void TThreeHeadedDragonDealerAssistGameLogic::OnRoundEnd()
{
	ScopedLock lock(State);
	OnRoundEnd_AssumeWriteLock();
}

void TThreeHeadedDragonDealerAssistGameLogic::OnRoundEnd_AssumeWriteLock()
{
	auto winner = State->GameLogic->Evaluate();
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Winner is: %s", winner.size() > 0 ? EThreeHeadedDragonWinner::_from_integral(winner[0])._to_string() : " tiger");

	if (bVirtualGame)
		State->VirtualDealerLogic->RoundFinished();

	CheckRoundClosing_AssumeWriteLock();
	CheckGameChangeRounds_AssumeWriteLock();

	SaveRoundState_AssumeReadLock(backend::ERoundStatus::Closed).then([this](boost::future<void> future) {
		try
		{
			future.get();
			ScopedLock lockTableState(State);
			ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::RoundEnd);
			ResetCurrentRound_AssumeWriteLock();

			if (State->ActionsState.bChangeShoeRequested)
				State->bShouldBurnCards = true;

			// Pause before going in NewRoundPreparation
			TLOG(LogThreeHeadedDragonDealerAssist, Info, "Pausing before new round begins.");
			StartTimer(mPauseAtRoundEndTimer);
		}
		catch (const DealerAssistException& ex)
		{
			TLOG(LogThreeHeadedDragonDealerAssist, Warning, "DealerAssistException: %s", ex.what());

			if (ex.Code() == EDealerAssistErrorCode::NotLoggedIn)
				OnTableCloseEvent();
		}
		catch (const std::exception& e)
		{
			TLOG(LogThreeHeadedDragonDealerAssist, Warning, "Error on saving result: %s", e.what());
		}
	});

	ReportNumberOfPlayers_AssumeReadLock();
}

void TThreeHeadedDragonDealerAssistGameLogic::AddCard_AssumeWriteLock(uint32_t card)
{
	switch (State->GameLogic->GetDealingPhase())
	{
		case EThreeHeadedDragonDealingPhase::GoldenDragonCard: State->GameLogic->AddCard(card, EThreeHeadedDragonSide::GoldenDragon); break;
		case EThreeHeadedDragonDealingPhase::BlackDragonCard: State->GameLogic->AddCard(card, EThreeHeadedDragonSide::BlackDragon); break;
		case EThreeHeadedDragonDealingPhase::RedDragonCard: State->GameLogic->AddCard(card, EThreeHeadedDragonSide::RedDragon); break;
		case EThreeHeadedDragonDealingPhase::TigerCard: State->GameLogic->AddCard(card, EThreeHeadedDragonSide::Tiger); break;
		default:
			// Handle unexpected phase
			// throw std::runtime_error("Unexpected dealing phase");
			break;
	}
}

bool TThreeHeadedDragonDealerAssistGameLogic::IsCardOnTable_AssumeReadLock(const FScannedCard& card) const
{
	return State->CardsOnTable.contains(card);
}

json TThreeHeadedDragonDealerAssistGameLogic::ShoeChanged_AssumeWriteLock()
{
	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Shoe changed detected.");
	if (State->Table.mPhase == EDealerAssistPhase::ShoeChange)
	{
		State->CardsOnTable.clear();
		State->GameRecords.clear();

		ChangeGamePhaseAndNotify_AssumeWriteLock(EDealerAssistPhase::CardBurn);

		if (bVirtualGame)
		{
			State->VirtualDealerLogic->ShuffleCards();
			BurnCardsWithVirtualDealer_AssumeWriteLock();
		}

		return {};
	}

	throw std::runtime_error("Shoe change detected in unexpected phase");
}

void TThreeHeadedDragonDealerAssistGameLogic::ResetCurrentRound_AssumeWriteLock()
{
	mBetsStatistics->ClearAllBetAmounts();

	// Clear state and timers
	State->Table.Clear();
	State->GameLogic->ClearGame();

	StopTimer(mBetTimer);
	StopTimer(mNewRoundPreparationTimer);
	StopTimer(mPauseAtRoundEndTimer);
	StopTimer(mRetryTimer);
}

void TThreeHeadedDragonDealerAssistGameLogic::RevealTigerCard()
{
	ScopedLock lock(State);

	TLOG(LogThreeHeadedDragonDealerAssist, Info, "Revealing tiger side");

	const auto actualCard = State->Table.mFaceDownCards[EThreeHeadedDragonDealingPhase::TigerCard];
	State->GameLogic->AddOrReplaceCard(actualCard, EThreeHeadedDragonSide::Tiger, 0);

	auto dto = AddThreeHeadedDragonCardDto();
	dto.mScannedCard = actualCard;
	dto.mCardPosition = EThreeHeadedDragonDealingPhase(EThreeHeadedDragonDealingPhase::TigerCard)._to_integral();
	dto.TigerHandValue = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::Tiger);

	State->Table.mFaceDownCards.erase(EThreeHeadedDragonDealingPhase::TigerCard);

	if (State->GameLogic->GetDealingPhase() == EThreeHeadedDragonDealingPhase::Finished)
	{
		std::vector<uint8_t> winners = State->GameLogic->Evaluate();
		std::vector<uint8_t> winnersInt;
		EThreeHeadedDragonWinner winner = EThreeHeadedDragonWinner::Tie;

		for (const auto& winner : winners) { winnersInt.push_back(winner); }

		if (std::all_of(winners.begin(), winners.end(), [](const uint8_t outcome) { return outcome == EThreeHeadedDragonWinner::TigerWin; }))
			winner = EThreeHeadedDragonWinner::TigerWin;

		if (std::any_of(winners.begin(), winners.end(), [](const uint8_t outcome) { return outcome == EThreeHeadedDragonWinner::DragonWin; }))
			winner = EThreeHeadedDragonWinner::DragonWin;

		State->Table.mWinners = winnersInt;
		State->Table.mWinner = winner._to_integral();
		dto.Outcomes = winnersInt;
		dto.mOutcome = winner._to_integral();
	}

	OnBroadcastEvent(*mAddCardEvent, dto);

	OnPauseBeforeRoundEnd();
}

bool TThreeHeadedDragonDealerAssistGameLogic::CanRevealRemainingCards_AssumeReadLock()
{
	if (State->Table.mPhase != EDealerAssistPhase::DealingCards)
		return false;

	return State->GameLogic->HasFaceDownCard(EThreeHeadedDragonSide::Tiger) && !State->GameLogic->HasFaceDownCard(EThreeHeadedDragonSide::RedDragon);
}

void TThreeHeadedDragonDealerAssistGameLogic::UpdateTableState_AssumeWriteLock()
{
	json result = json::object();
	result["tigerHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::Tiger);
	result["goldenDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::GoldenDragon);
	result["redDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::RedDragon);
	result["blackDragonHandValue"] = State->GameLogic->GetHandValue(EThreeHeadedDragonSide::BlackDragon);

	State->Table.mHandValue = std::move(result);
	State->Table.mDealingPhase = State->GameLogic->GetDealingPhase();
	State->Table.mCards = State->GameLogic->GetCurrentStateOfCards();

	if (State->GameLogic->IsGameFinished())
	{
		State->GameLogic->Evaluate();
		State->Table.mWinner = State->GameLogic->GetWinner();
		State->Table.mWinners = State->GameLogic->GetWinners();
	}
	else
	{
		State->Table.mWinner.reset();
		State->Table.mWinners.reset();
	}
}
