//
// Created by <PERSON><PERSON><PERSON> on 4. 4. 24.
//

#include "dealer-assist/DealerAssistTableBackendClient.h"

#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/backend/DealerAssistBackendSharedTypes.h"

using namespace dealer_assist;
using namespace dealer_assist::backend;

boost::future<YResponse> DealerAssistTableBackendClient::FlagRound(uint32_t roundId, const std::string& reason)
{
	json flag(json::value_t::object);
	flag["round"] = roundId;
	flag["event"] = reason;
	flag["context"] = json(json::value_t::object);

	return Request(EDealerAssistRequest(EDealerAssistRequest::FlagRound)._to_string(), flag);
}

boost::future<YResponse> DealerAssistTableBackendClient::OpenNewRound(const std::string& gameType)
{
	json gameTypeJson(json::value_t::object);
	gameTypeJ<PERSON>["gameType"] = gameType;
	return Request(EDealerAssistRequest(EDealerAssistRequest::NewRound)._to_string(), gameTypeJson);
}

boost::future<std::vector<GameRecordDto>> DealerAssistTableBackendClient::GetTableHistory(const std::string& filterByGameType)
{
	auto type = EDealerAssistRequest(EDealerAssistRequest::GetTableHistory)._to_string();
	auto request = json(json::value_t::object);
	request["fromEvent"] = EFlagRoundReason(EFlagRoundReason::CutCard)._to_string();
	request["gameType"] = filterByGameType;

	return Request(type, request).then(boost::launch::sync, [this](boost::future<YResponse> fut) -> std::vector<GameRecordDto> {
		YResponse response = fut.get();
		std::vector<GameRecordDto> history;
		if (response.Status == EMessageStatus::ResponseOk && response.Message.Body().is_array())
		{
			for (const auto& item : response.Message.Body())
			{
				const json* state = FindMember(item, "state");
				const json* status = FindMember(item, "status");

				if (state && !state->is_null() && state->contains("gameNumber") && status && !status->is_null() &&
				    item["status"].get<std::string>() == backend::ERoundStatus(backend::ERoundStatus::Closed)._to_string())
				{
					auto historyItem = GameRecordDto::FromJSON(*state);
					history.push_back(historyItem);
				}
			}
		}
		else
			Log(Error, "Error on sending to backend: %s", response.ErrorMessage().c_str());
		return history;
	});
}

boost::future<std::vector<GameRecordDto>> DealerAssistTableBackendClient::GetTableHistory(const std::string& filterByGameType)
{
	auto type = EDealerAssistRequest(EDealerAssistRequest::GetTableHistory)._to_string();
	auto request = json(json::value_t::object);
	request["fromEvent"] = EFlagRoundReason(EFlagRoundReason::CutCard)._to_string();
	request["gameType"] = filterByGameType;

	return Request(type, request).then(boost::launch::sync, [this](boost::future<YResponse> fut) -> std::vector<GameRecordDto> {
		YResponse response = fut.get();
		std::vector<GameRecordDto> history;
		if (response.Status == EMessageStatus::ResponseOk && response.Message.Body().is_array())
		{
			for (const auto& item : response.Message.Body())
			{
				const json* state = FindMember(item, "state");
				const json* status = FindMember(item, "status");

				if (state && !state->is_null() && state->contains("gameNumber") && status && !status->is_null() &&
					item["status"].get<std::string>() == backend::ERoundStatus(backend::ERoundStatus::Closed)._to_string())
				{
					auto historyItem = GameRecordDto::FromJSON(*state);
					history.push_back(historyItem);
				}
			}
		}
		else
			Log(Error, "Error on sending to backend: %s", response.ErrorMessage().c_str());
		return history;
	});
}

boost::future<YResponse> DealerAssistTableBackendClient::SaveResult(const json& result)
{
	auto type = EDealerAssistRequest(EDealerAssistRequest::SaveRoundState)._to_string();
	return Request(type, result);
}
