//
// Created by <PERSON><PERSON><PERSON> on 11. 03. 24.
//

#include "dealer-assist/dto/NotifyDealerConsoleDto.h"

#include <utility>

using namespace dealer_assist;

NotifyDealerConsoleDto::NotifyDealerConsoleDto(const EDealerAssistTableNotificationCode code, json context) : mCode(code), mContext(std::move(context)) {}

EVerbosity NotifyDealerConsoleDto::getVerbosity() const
{
	switch (mCode)
	{
		case EDealerAssistTableNotificationCode::AuthenticationFailed:
		case EDealerAssistTableNotificationCode::ScannerDisconnected: return EVerbosity::Warning;

		default: return EVerbosity::Info;
	}
}


json NotifyDealerConsoleDto::ToJSON() const
{
	json root(json::value_t::object);
	root["code"] = mCode._to_string();
	root["severity"] = getVerbosity()._to_string();
	root["context"] = mContext;

	return root;
}

NotifyDealerConsoleDto NotifyDealerConsoleDto::FromJSON(const json& val)
{
	NotifyDealerConsoleDto dto;
	dto.mCode = EDealerAssistTableNotificationCode::_from_string(val["code"].get<std::string>().c_str());
	dto.mContext = val["context"];

	return dto;
}