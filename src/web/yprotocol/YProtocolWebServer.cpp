#include "web/yprotocol/YProtocolWebServer.h"

#include "web/error/UnauthorizedError.h"

const JsonSchema YProtocolServerSchema = JsonSchema(
  { { "keycloak",
      JsonSchema(security::KeycloakSettings::Schema())
        .AddMember(
          "jwks-retry-interval",
          JsonSchema(
            json::value_t::number_unsigned,
            "Number of seconds to wait before retrying to fetch JW<PERSON> from Keycloak server if it fails. If 0, will only request JW<PERSON> once on server start, and throw an exception if it fails - so server will not be started.",
            5U))
        .AddMember(
          "allow-start-without-jwks",
          JsonSchema(
            json::value_t::boolean,
            "If true, and jwks-retry-interval is not 0, then the server will be started even if the jwks request fails. In this case, any token verification will fail due to verifier_not_set, until jwks are successfully fetched from Keycloak.",
            true))
        .SetRequired(false)
        .AddMember("iat-leeway", JsonSchema(json::value_t::number_unsigned, "The issued at leeway in seconds (default: 0)", 0)) } });

YProtocolWebServer::YProtocolWebServer(web::features::value feat) : web::WebServer(feat)
{
	Schema() += YProtocolServerSchema;

	mConnectionAcceptHandler = [this](const imaxa_connection_ptr& con) {
		std::error_code ec;
		std::optional<security::JWToken> token = security::TokenFromAuthHeader(con->get_request_header(web::http::header(web::http::field::authorization)), ec);
		if (ec)
			Log(Warning, "Error parsing JWT: %s", ec.message().c_str());

		ec = VerifyToken(token);
		if (ec && ec.category() == security::error::token_verification_error_category() &&
		    ec.value() == static_cast<int>(security::error::security_error::token_given_but_verification_disabled))
		{
			token.reset();
			ec.clear();
		}

		if (ec)
		{
			Log(Warning, "Failed to verify JWT token: %s", ec.message().c_str());
			return web::websockets::session::validation::reject;
		}

		return accept_ws(con, std::move(token));
	};
}

void YProtocolWebServer::OnConfigLoaded(const std::filesystem::path& filename)
{
	WebServer::OnConfigLoaded(filename);

	if (ConfigExists("keycloak", true))
	{
		const std::string addressStr = GetConfig("keycloak.address").get<std::string>();
		mKeycloakSettings = security::KeycloakSettings::FromJSON(GetConfig("keycloak"));
		if (!mKeycloakSettings.Address.get_valid())
			Log(Warning, "Bad Keycloak address: %s", addressStr.c_str());
		if (!mKeycloakSettings.Address.is_absolute())
			Log(Warning, "Keycloak address should be absolute! (it is %s)", addressStr.c_str());
		if (mKeycloakSettings.Address.get_type() != websocketpp::uri::http)
			Log(Warning, "Keycloak address should be HTTP! (it is %s)", addressStr.c_str());
		mTokenVerificationSettings.bEnableVerification = true;
		mTokenVerificationSettings.IssuedAtLeewaySec = GetConfig("keycloak.iat-leeway").get<uint32_t>();

		KeycloakJWKSRetryInterval = GetConfig("keycloak.jwks-retry-interval").get<uint32_t>();
		bAllowStartWithoutJWKS = GetConfig("keycloak.allow-start-without-jwks").get<bool>();
	}
}

void YProtocolWebServer::SetKeycloakCertAuthorities(const std::vector<std::filesystem::path>& authorities)
{
	mKeycloakSettings.CertificateAuthorities = authorities;
}

void YProtocolWebServer::PreRun()
{
	WebServer::PreRun();

	if (KeycloakJWKSRetryInterval)
	{
		TLOG(LogYProtocol, Info, "Will retry JWT verification context creation every %u seconds until it succeeds...", KeycloakJWKSRetryInterval);
		if (bAllowStartWithoutJWKS)
		{
			TryCreateVerificationContext({});
		}
		else
		{
			BlockUntilSuccess(std::bind(&YProtocolWebServer::TryCreateVerificationContext, this, std::error_code()), std::chrono::seconds(KeycloakJWKSRetryInterval));
		}
	}
	else
	{
		if (mTokenVerificationSettings.bEnableVerification && !CreateJWTVerificationContext())
		{
			throw std::runtime_error("Failed to get Keycloak JWKS and jwks-retry-interval is 0, so server will not be started.");
		}
	}
}

void YProtocolWebServer::PreStop()
{
	if (JWKSRetryTimer)
	{
		JWKSRetryTimer->cancel();
		JWKSRetryTimer.reset();
	}

	WebServer::PreStop();
}

void YProtocolWebServer::AddRouteJson(const std::string& endpoint, web::http::verb v, const JsonAuthRequestHandler& handler)
{
	WebServer::AddRouteJson(
	  endpoint, v,
	  [this, handler](const imaxa_connection_ptr& con, const json& requestBody, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		  try
		  {
			  std::error_code ec;
			  const std::optional<security::JWToken> token =
			    security::TokenFromAuthHeader(con->get_request_header(web::http::header(web::http::field::authorization)), ec);
			  if (!ec)
				  ec = VerifyToken(token);

			  if (ec)
			  {
				  if (ec.category() != security::error::token_verification_error_category() ||
				      ec.value() != static_cast<int>(security::error::security_error::token_given_but_verification_disabled))
					  throw UnauthorizedError(ec.message());
			  }

			  handler(con, requestBody, resource, query, token);
		  }
		  catch (const HTTPError& err)
		  {
			  const auto status = err.getErrorCode();
			  const std::string reason = obsolete_reason(boost::beast::http::int_to_status(status));
			  Log(Warning, "HTTP %d(%s) for request to %s: %s", status, reason.c_str(), con->get_resource().c_str(), err.what());
			  std::error_code ec;
			  con->set_status(err.getErrorCode(), err.what(), ec);
		  }
		  catch (const std::exception& err)
		  {
			  Log(Error, "Internal server error for request to %s: %s", con->get_resource().c_str(), err.what());
			  std::error_code ec;
			  con->set_status(web::http::status::internal_server_error, err.what(), ec);
		  }
	  });
}

bool YProtocolWebServer::TryCreateVerificationContext(const std::error_code& ec)
{
	if (ec)
		return false;

	if (CreateJWTVerificationContext())
		return true;

	if (KeycloakJWKSRetryInterval && bAllowStartWithoutJWKS)
		JWKSRetryTimer = CreateTimer(std::chrono::system_clock::now() + std::chrono::seconds(KeycloakJWKSRetryInterval),
		                             std::bind(&YProtocolWebServer::TryCreateVerificationContext, this, std::placeholders::_1));

	return false;
}
