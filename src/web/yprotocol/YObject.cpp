#include "web/yprotocol/YObject.h"

#include "Cryptography.h"

using namespace yprotocol;

void UniquelyIdentifiableObject::SetUID(const std::string& uid)
{
	const std::string hashed = UIDHashType.has_value() && uid.length() ? crypto::Hash(uid, UIDHashType.value()) : uid;
	if (mUID == hashed)
		return;
	mUID = hashed;
	OnUIDChanged(hashed);
}

const std::string& UniquelyIdentifiableObject::UniqueIdentifier() const noexcept
{
	return mUID;
}

bool UniquelyIdentifiableObject::HasUniqueID() const
{
	return !mUID.empty();
}