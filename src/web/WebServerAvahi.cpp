#include "web/WebServerAvahi.h"

#include "web/WebUtils.h"

void avahi::_detail::PublishServerService(web::WebServer& server, Publisher& publisher)
{
	std::string interface;
	if (!server.Host().is_unspecified())
		interface = web::get_interface_from_ip(server.Host());

	if (server.Features & web::features::HTTP)
		publisher.Config.channels.push_back({ web::features::HTTP, server.Port(), interface, true, server.Host().is_v6() });
	if (server.Features & web::features::WebSockets)
		publisher.Config.channels.push_back({ web::features::WebSockets, server.Port(), interface, true, server.Host().is_v6() });
	publisher.StartPublish();
}