#include "web/KeycloakClientSettings.h"

using namespace security;

const JsonSchema KeycloakTokenResponseSchema =
  JsonSchema({ { "access_token", JsonSchema(json::value_t::string, "The JWT") },
               { "expires_in", JsonSchema(json::value_t::number_unsigned, "Number of seconds before the token is invalid") },
               { "refresh_token", JsonSchema(json::value_t::string, "The refresh token") },
               { "refresh_expires_in", JsonSchema(json::value_t::number_unsigned, "Number of seconds before the refresh token becomes invalid") },
               { "token_type", JsonSchema(json::value_t::string, "The type of this JWT").SetPossibleVals({ KEYCLOAK_TOKEN_TYPE }) } });
KeycloakToken KeycloakToken::FromJSON(const json& val)
{
	KeycloakTokenResponseSchema.TestThrow(val);
	KeycloakToken ret;
	ret.Token = val["access_token"].get<std::string>();
	ret.RefreshToken = val["refresh_token"].get<std::string>();
	ret.Expire = std::chrono::system_clock::now() + std::chrono::seconds(val["expires_in"].get<uint32_t>());
	ret.RefreshExpire = std::chrono::system_clock::now() + std::chrono::seconds(val["refresh_expires_in"].get<uint32_t>());
	return ret;
}

boost::future<web::http::response> KeycloakClientAuthenticationMode::RequestToken(const KeycloakClientSettings& settings) const
{
	auto client = web::WebClient::New(web::websockets::uri(settings.Address));
	ConfigureClient(*client);
	client->CertAuthorities = settings.CertificateAuthorities;
	web::QueryString params { GetParameters() };
	params["client_id"] = settings.ClientID;
	if (!settings.ClientSecret.empty())
		params["client_secret"] = settings.ClientSecret;
	if (!settings.Scope.empty())
		params["scope"] = settings.Scope;
	return client->Request(web::http::verb::post, std::format("/realms/{}/protocol/openid-connect/token", settings.Realm), params.ToString(),
	                       "application/x-www-form-urlencoded");
}

web::QueryString KeycloakRefreshTokenAuthentication::GetParameters() const
{
	return { { "grant_type", "refresh_token" }, { "refresh_token", RefreshToken } };
}

web::QueryString KeycloakUsernamePasswordAuthentication::GetParameters() const
{
	return { { "username", Username }, { "password", Password }, { "grant_type", "password" } };
}

KeycloakCertificateAuthentication::KeycloakCertificateAuthentication(const web::FCertificates& certs) : web::FCertificates(certs) {}

void KeycloakCertificateAuthentication::ConfigureClient(web::WebClient& client) const
{
	client.Certificates = static_cast<const FCertificates&>(*this);
}

web::QueryString KeycloakCertificateAuthentication::GetParameters() const
{
	return { { "grant_type", "password" } };
}

boost::future<KeycloakToken> KeycloakClientSettings::RequestBearerToken(const std::shared_ptr<KeycloakClientAuthenticationMode>& authModeOverride) const noexcept
{
	const std::shared_ptr<KeycloakClientAuthenticationMode>& authModeToUse = authModeOverride ? authModeOverride : AuthMode;
	if (!authModeToUse)
	{
		boost::promise<KeycloakToken> promise;
		promise.set_value({});
		return promise.get_future();
	}

	if (!Address.get_valid() || !Address.is_absolute() || Address.get_type() != websocketpp::uri::http)
	{
		boost::promise<KeycloakToken> promise;
		promise.set_exception(std::runtime_error("Invalid Keycloak address"));
		return promise.get_future();
	}

	return authModeToUse->RequestToken(*this).then(boost::launch::sync, [addr = Address](boost::future<web::http::response> fut) -> KeycloakToken {
		const web::http::response response(fut.get());
		if (response.get_error_code())
			BOOST_THROW_EXCEPTION(std::runtime_error(std::format("Failed to get token from Keycloak at {}: {}", addr.str(), response.get_error_code().message())));

		if (response.get_status_code() != web::http::status::ok)
			BOOST_THROW_EXCEPTION(std::runtime_error(std::format("Keycloak at {} rejected token request: {}", addr.str().c_str(), response.get_status_msg())));

		json responseJson;
		try
		{
			responseJson = json::parse(response.get_body());
		}
		catch (const std::exception&)
		{
			BOOST_THROW_EXCEPTION(std::runtime_error("Keycloak response to token request is not a valid JSON"));
		}

		try
		{
			return KeycloakToken::FromJSON(responseJson);
		}
		catch (const SchemaError& err)
		{
			BOOST_THROW_EXCEPTION(std::runtime_error(std::format("Keycloak token response is invalid: {}", err.what())));
		}
	});
}

boost::future<KeycloakToken> KeycloakClientSettings::RefreshToken(const KeycloakToken& currentToken, bool forceRefresh) const noexcept
{
	if (currentToken.RefreshToken.empty())
	{
		boost::promise<KeycloakToken> promise;
		promise.set_exception(std::runtime_error("Refresh token is not valid!"));
		return promise.get_future();
	}

	auto now = std::chrono::system_clock::now();
	if (!forceRefresh && now + TokenRefreshMargin < currentToken.Expire)
		return {};

	if (now > currentToken.RefreshExpire)
	{
		boost::promise<KeycloakToken> promise;
		promise.set_exception(std::runtime_error("Refresh token is expired!"));
		return promise.get_future();
	}

	return RequestBearerToken(std::make_shared<KeycloakRefreshTokenAuthentication>(currentToken.RefreshToken));
}

boost::future<KeycloakToken> KeycloakClientSettings::RequestBearerTokenAndLog(const std::shared_ptr<KeycloakClientAuthenticationMode>& authModeOverride,
                                                                              const LogCategory* logCategory) const noexcept
{
	auto tokenRequest = RequestBearerToken(authModeOverride);
	if (logCategory && tokenRequest.valid())
		return tokenRequest.then(boost::launch::sync, [logCategory, hasAuthMode = static_cast<bool>(AuthMode)](boost::future<KeycloakToken> fut) -> KeycloakToken {
			try
			{
				KeycloakToken ret = fut.get();
				if (!ret.Token.empty())
					Logger::Log(*logCategory, Info, "Access token successfully retrieved!");
				return ret;
			}
			catch (const std::exception& e)
			{
				Logger::Log(*logCategory, Error, "Failure getting access token: %s", e.what());
				BOOST_THROW_EXCEPTION(e);
			}
		});

	return tokenRequest;
}

boost::future<KeycloakToken> KeycloakClientSettings::ObtainOrRefreshToken(const KeycloakToken& currentToken, bool forceRefresh,
                                                                          const LogCategory* logCategory) const noexcept
{
	if (!currentToken.RefreshToken.empty())
	{
		auto refreshToken = RefreshToken(currentToken, forceRefresh);
		if (refreshToken.valid())
		{
			return refreshToken.then(boost::launch::sync, [logCategory, clientSettings = *this](boost::future<KeycloakToken> fut) -> KeycloakToken {
				std::string error;
				try
				{
					KeycloakToken ret = fut.get();
					Logger::Log(*logCategory, Info, "Access token successfully refreshed!");
					return ret;
				}
				catch (const std::exception& e)
				{
					if (!clientSettings.AuthMode)
					{
						Logger::Log(*logCategory, Error, "Failure refreshing access token - %s", e.what());
						BOOST_THROW_EXCEPTION(e);
					}
					error = e.what();
				}
				catch (...)
				{
					error = "Unknown exception";
				}

				Logger::Log(*logCategory, Warning, "Failure refreshing access token - %s. Will attempt a new login instead.", error.c_str());
				return clientSettings.RequestBearerTokenAndLog({}, logCategory).get();
			});
		}
		else
		{
			// if the current token is still valid, return it
			boost::promise<KeycloakToken> promise;
			promise.set_value(currentToken);
			return promise.get_future();
		}
	}

	return RequestBearerTokenAndLog({}, logCategory);
}
