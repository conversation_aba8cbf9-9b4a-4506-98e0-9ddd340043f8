#include "packman/PackmanAPI.h"

const std::string igp::UpdateStartedEventName = "update-started";
const std::string igp::UpdateProgressEventName = "update-progress";
const std::string igp::UpdateLogEventName = "update-log";
const std::string igp::UpdateCompletedEventName = "update-completed";
const std::string igp::PendingUpdateChangedEventName = "update-pending";
const std::string igp::TerminalShuttingDownEventName = "shutting-down";
const std::string igp::InternetStatusChangedEventName = "internet-status-changed";
const std::string igp::ServerHubStatusChangedEventName = "server-hub-status-changed";
const std::string igp::GameListUpdatedEventName = "game-list-updated";

const std::string igp::GameChecksumChangedEventName = "game-checksum-changed";
const std::string igp::IntegrityCheckCompletedEventName = "integrity-check-completed";

const std::string igp::PackmanStageChangedEventName = "stage-changed";

const std::string igp::PackmanHostStatusChangedEventName = "host-status-changed";

const std::string igp::SetTerminalBusyStateRequestName = "set-busy-state";
const std::string igp::ConfigureGamesRequestName = "configure-games";
const std::string igp::ConfigureHostsRequestName = "configure-hosts";
const std::string igp::UpdateCoreRequestName = "update-core";
const std::string igp::RestartMachineRequestName = "restart";
const std::string igp::ShutdownMachineRequestName = "shutdown";
const std::string igp::RestartTerminalRequestName = "restart-terminal";
const std::string igp::ForceUpdateRequestName = "force-update";
const std::string igp::UpdateStationStatusRequestName = "station-status";
const std::string igp::RAMClearRequestName = "ram-clear";

using namespace igp;

json FPendingUpdate::ToJSON() const
{
	json updatePendingCtx(json::value_t::object);
	updatePendingCtx["num-packages"] = NumGamePackages;
	updatePendingCtx["num-game-configs"] = NumGameConfigurations;
	updatePendingCtx["core-update"] = bCoreUpdateRequired;
	json terminals(json::value_t::array);
	for (const auto& [terminal, reason] : BusyTerminals)
	{
		json blockedByTerminal(json::value_t::object);
		blockedByTerminal["terminal"] = terminal;
		blockedByTerminal["state"] = reason._to_string();
		terminals.push_back(std::move(blockedByTerminal));
	}
	updatePendingCtx["busy-terminals"] = std::move(terminals);
	return updatePendingCtx;
}

FPendingUpdate FPendingUpdate::FromJSON(const json& val)
{
	FPendingUpdate ret;
	ret.NumGamePackages = val["num-packages"].get<uint32_t>();
	ret.NumGameConfigurations = val["num-game-configs"].get<uint32_t>();
	ret.bCoreUpdateRequired = val["core-update"].get<bool>();
	for (const json& terminal : val["busy-terminals"])
	{
		const ETerminalBusyState state = ETerminalBusyState::_from_string_nothrow(terminal["state"].get<std::string>().c_str(), ETerminalBusyState::None);
		if (state != ETerminalBusyState::None)
			ret.BusyTerminals.insert_or_assign(terminal["terminal"].get<std::string>(), state);
	}
	return ret;
}

json FGameConfigurationEntry::ToJSON() const
{
	json gameInfo(json::value_t::object);
	gameInfo["host"] = HostID;
	gameInfo["maxBet"] = MaxBetLimit;
	gameInfo["maxWin"] = MaxWinLimit;
	gameInfo["liveGame"] = LiveGameInfo ? LiveGameInfo->ToJSON({ .bForSerialization = true }) : json();
	gameInfo["status"] = Status;
	gameInfo["detail"] = StatusDetails;
	gameInfo["preset"] = bPreset;
	gameInfo["enabled"] = bEnabled;
	gameInfo["configurable"] = bConfigurable;
	gameInfo["display"] = DisplayType._to_string();
	gameInfo["config"] = Config.ToJSON();
	gameInfo["version"] = Version;
	return gameInfo;
}

FGameConfigurationEntry FGameConfigurationEntry::FromJSON(const json& val)
{
	FGameConfigurationEntry ret;
	val["host"].get_to(ret.HostID);
	val["maxBet"].get_to(ret.MaxBetLimit);
	val["maxWin"].get_to(ret.MaxWinLimit);
	const json& liveGameInfoJSON = val["liveGame"];
	if (!liveGameInfoJSON.is_null())
	{
		ret.LiveGameInfo = yserver::GameInformation();
		ret.LiveGameInfo->FromJSON(liveGameInfoJSON);
	}
	val["status"].get_to(ret.Status);
	val["detail"].get_to(ret.StatusDetails);
	val["preset"].get_to(ret.bPreset);
	val["enabled"].get_to(ret.bEnabled);
	val["configurable"].get_to(ret.bConfigurable);
	val["display"].get_to(ret.DisplayType);
	val["version"].get_to(ret.Version);
	ret.Config.FromJSON(val["config"]);
	return ret;
}

json FGameHostInformation::ToJSON() const
{
	json ret(json::value_t::object);
	ret["identifier"] = Identifier;
	ret["type"] = Type;
	ret["id"] = ID;
	ret["name"] = Name;
	ret["status"] = Status;
	ret["statusDesc"] = StatusDesc;
	json runtimeErrMap(json::value_t::object);
	for (const auto& [errId, errInfo] : RuntimeErrors) runtimeErrMap[errId] = errInfo.ToJSON({});
	ret["runtimeErrors"] = std::move(runtimeErrMap);
	json versionMap(json::value_t::object);
	for (const auto& [component, version] : Versions) versionMap[component] = version;
	ret["versions"] = std::move(versionMap);
	ret["error"] = Error;
	ret["root-location"] = RootLocation;
	json actionMap(json::value_t::object);
	for (const auto& [actionName, action] : Actions) actionMap[actionName] = action.ToJSON();
	ret["actions"] = std::move(actionMap);
	return ret;
}

void FGameHostInformation::LoadFromJSON(const json& val)
{
	Identifier = val["identifier"].get<std::string>();
	Type = yserver::gamehost::HostType::_from_string_nothrow(val["type"].get<std::string>().c_str(), yserver::gamehost::HostType::Null);
	ID = val["id"].get<uint32_t>();
	Name = val["name"].get<std::string>();
	Status = yserver::EModuleStatus::_from_string_nothrow(val["status"].get<std::string>().c_str(), yserver::EModuleStatus::Uninitialized);
	StatusDesc = val["statusDesc"].get<std::string>();
	RuntimeErrors.clear();
	const json& errors = val["runtimeErrors"];
	for (auto it = errors.begin(); it != errors.end(); it++) RuntimeErrors[it.key()] = yserver::ModuleRuntimeError::FromJSON(*it);
	Versions.clear();
	const json& versions = val["versions"];
	for (auto it = versions.begin(); it != versions.end(); it++) Versions[it.key()] = it->get<std::string>();
	Error = val["error"].get<std::string>();
	RootLocation = val["root-location"].get<std::string>();
	Actions.clear();
	const json& actions = val["actions"];
	for (auto it = actions.begin(); it != actions.end(); it++) Actions[it.key()].LoadFromJSON(*it);
}

json FExtendedGameHostInformation::ToJSON() const
{
	json ret(FGameHostInformation::ToJSON());
	ret["default"] = bDefault;
	ret["config"] = Config;
	return ret;
}

void FExtendedGameHostInformation::LoadFromJSON(const json& val)
{
	FGameHostInformation::LoadFromJSON(val);
	bDefault = val["default"].get<bool>();
	Config = val["config"];
}

json FGamePackageEntry::ToJSON() const
{
	json val(json::value_t::object);
	val["info"] = Info.ToJSON();
	val["status"] = Status;
	val["statusDetail"] = StatusDetails;
	val["checksum"] = Checksum;
	val["checksum-ok"] = bChecksumOK;
	json gameConfList(json::value_t::object);
	for (const auto& conf : Configurations) gameConfList[conf.first] = conf.second.ToJSON();
	val["configurations"] = std::move(gameConfList);
	return val;
}

FGamePackageEntry FGamePackageEntry::FromJSON(const json& val)
{
	FGamePackageEntry pack;
	pack.Info.LoadFromJSON(val["info"]);
	val["status"].get_to(pack.Status);
	val["statusDetail"].get_to(pack.StatusDetails);
	val["checksum"].get_to(pack.Checksum);
	val["checksum-ok"].get_to(pack.bChecksumOK);
	const json& confs = val["configurations"];
	for (auto it = confs.begin(); it != confs.end(); it++) pack.Configurations.emplace(it.key(), FGameConfigurationEntry::FromJSON(*it));
	return pack;
}

json FPackmanState::ToJSON() const
{
	json val(json::value_t::object);
	val["revision"] = Revision;
	json hosts(json::value_t::array);
	for (const auto& host : Hosts) hosts.push_back(host.ToJSON());
	val["hosts"] = std::move(hosts);
	json games(json::value_t::object);
	for (const auto& game : Games) games[std::to_string(game.first)] = game.second.ToJSON();
	val["games"] = std::move(games);
	return val;
}

FPackmanState FPackmanState::FromJSON(const json& val)
{
	FPackmanState ret;
	if (const json* revisionJson = FindMember(val, "revision"))
		revisionJson->get_to(ret.Revision);
	if (const json* hostsJson = FindMember(val, "hosts"))
		for (const json& hostInfo : *hostsJson) ret.Hosts.emplace_back().LoadFromJSON(hostInfo);
	if (const json* gamesJson = FindMember(val, "games"))
	{
		for (auto it = gamesJson->begin(); it != gamesJson->end(); it++)
		{
			const int64_t gid = yutils::strToInt(it.key(), 0);
			if (gid > 0)
				ret.Games.emplace(gid, FGamePackageEntry::FromJSON(*it));
		}
	}
	return ret;
}

json FUpdateStatus::ToJSON() const
{
	json val(json::value_t::object);
	val["completed"] = NumCompleted;
	val["total"] = NumTotal;
	val["current"] = CurrentlyUpdating.ToJSON();
	val["status"] = Status;
	return val;
}

FUpdateStatus FUpdateStatus::FromJSON(const json& val)
{
	FUpdateStatus ret;
	val["completed"].get_to(ret.NumCompleted);
	val["total"].get_to(ret.NumTotal);
	ret.CurrentlyUpdating = FPackageUpdateStatus::FromJSON(val["current"]);
	val["status"].get_to(ret.Status);
	return ret;
}

json FPackageActionInfo::ToJSON() const
{
	json val(json::value_t::object);
	val["action"] = Action;
	val["status"] = Status;
	val["success"] = bSuccess;
	return val;
}

FPackageActionInfo FPackageActionInfo::FromJSON(const json& val)
{
	FPackageActionInfo ret;
	val["action"].get_to(ret.Action);
	val["status"].get_to(ret.Status);
	val["success"].get_to(ret.bSuccess);
	return ret;
}

json FUpdateCompletedData::ToJSON() const
{
	json val(json::value_t::object);
	val["state"] = State.ToJSON();
	val["requiresRestart"] = bRequiresRestart;
	json actionsDone(json::value_t::object);
	for (const auto& [package, action] : ActionsDone) actionsDone[package] = action.ToJSON();
	val["actionsDone"] = std::move(actionsDone);
	return val;
}

FUpdateCompletedData FUpdateCompletedData::FromJSON(const json& val)
{
	FUpdateCompletedData ret;
	ret.State = FPackmanState::FromJSON(val["state"]);
	ret.bRequiresRestart = val["requiresRestart"].get<bool>();
	const json& actionsDone = val["actionsDone"];
	for (auto it = actionsDone.begin(); it != actionsDone.end(); ++it) ret.ActionsDone.emplace(it.key(), FPackageActionInfo::FromJSON(*it));
	return ret;
}

ServiceInformationPackman ServiceInformationPackman::FromJSON(const json& val)
{
	ServiceInformationPackman ret;
	ret.name = val["name"].get<std::string>();
	ret.domain = val["domain"].get<std::string>();
	ret.type = val["type"].get<int>();
	ret.host_name = val["host_name"].get<std::string>();
	ret.network_interface = val["network_interface"].get<std::string>();
	ret.address = boost::asio::ip::address::from_string(val["address"].get<std::string>());
	ret.port = val["port"].get<uint32_t>();
	ret.is_local = val["is_local"].get<bool>();
	ret.is_wide_area = val["is_wide_area"].get<bool>();
	return ret;
}

json ServiceInformationPackman::ToJSON() const
{
	json ret(json::value_t::object);
	ret["name"] = name;
	ret["domain"] = domain;
	ret["type"] = type;
	ret["host_name"] = host_name;
	ret["network_interface"] = network_interface;
	ret["address"] = address.to_string();
	ret["port"] = port;
	ret["is_local"] = is_local;
	ret["is_wide_area"] = is_wide_area;
	return ret;
}

void igp::to_json(json& val, const FNodeStatus& status)
{
	val["connected"] = status.Connected;
	val["node"] = status.NodeInfo;
	val["subnodes"] = status.SubNodes;
}

void igp::from_json(const json& val, FNodeStatus& status)
{
	val["connected"].get_to(status.Connected);
	val["node"].get_to(status.NodeInfo);
	val["subnodes"].get_to(status.SubNodes);
}
