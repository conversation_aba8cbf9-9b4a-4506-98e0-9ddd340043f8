#include "evolution/dto/TransactionDto.h"

const JsonSchema& TransactionDto::TransactionSchema()
{
	static auto schema = JsonSchema({ { "id", JsonSchema(json::value_t::string, "Transaction ID") },
	                                  { "refId", JsonSchema(json::value_t::string, "Reference ID") },
	                                  { "amount", JsonSchema(json::value_t::number_float, "Transaction amount") } });
	__attribute__((unused)) volatile int dummy {};
	return schema;
}


TransactionDto::TransactionDto(const std::string& id, const std::string& refId, const double amount) : ID(id), RefID(refId), Amount(amount) {}

json TransactionDto::ToJSON() const
{
	json val(json::value_t::object);
	val["id"] = ID;
	val["refId"] = RefID;
	val["amount"] = Amount;
	return val;
}

TransactionDto TransactionDto::FromJSON(const json& val)
{
	return TransactionDto(val["id"].get<std::string>(), val["refId"].get<std::string>(), val["amount"].get<double>());
}
