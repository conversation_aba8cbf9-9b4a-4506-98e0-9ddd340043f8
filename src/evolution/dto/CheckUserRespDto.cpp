#include "evolution/dto/CheckUserRespDto.h"

#include "JsonSchema.h"

CheckUserRespDto::CheckUserRespDto(const EStatus& status, const std::optional<std::string>& uuid, const std::optional<std::string>& sid) :
    EvolutionRespDto(status, sid), Uuid(uuid)
{
}

CheckUserRespDto::CheckUserRespDto(const EvolutionRespDto& evolutionResp) : EvolutionRespDto(evolutionResp.Status, evolutionResp.Sid) {}

json CheckUserRespDto::ToJSON() const
{
	json val = EvolutionRespDto::ToJSON();
	if (Uuid.has_value())
		val["uuid"] = Uuid.value();
	return val;
}

CheckUserRespDto CheckUserRespDto::FromJSON(const json& val)
{
	CheckUserRespDto dto = EvolutionRespDto::FromJSON(val);
	if (const json* uuidJson = FindMember(val, "uuid"); uuidJson && !uuidJson->is_null())
		dto.Uuid = uuidJson->get<std::string>();

	return dto;
}
