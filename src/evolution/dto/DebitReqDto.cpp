#include "evolution/dto/DebitReqDto.h"

const JsonSchema DebitReqDto::DebitReqSchema = JsonSchema(
  { { "currency", JsonSchema(json::value_t::string, "Currency code (ISO 4217 3 letter code) of player’s session currency") },
    { "game",
      JsonSchema(
        { { "id", JsonSchema(json::value_t::string, "Unique game round id.") },
          { "type", JsonSchema(json::value_t::string, "Game type") },
          { "details",
            JsonSchema({ { "table", JsonSchema({ { "id", JsonSchema(json::value_t::string, "Table identifier") },
                                                 { "vid", JsonSchema(json::value_t::string, "Virtual table identifier (can be null in case there is no virtual table Id)")
                                                            .SetRequired(false) } }) } }) } }) },
    { "transaction", TransactionDto::TransactionSchema() } });

DebitReqDto::DebitReqDto()
{
	Schema() += DebitReqSchema;
}

DebitReqDto::DebitReqDto(const std::string& sid, const std::string& userId, const ECurrency currency, const GameDto& game, const TransactionDto& transaction,
                         const std::string& uuid) : EvolutionReqDto(sid, userId, uuid), Currency(currency), Game(game), Transaction(transaction)
{
	Schema() += DebitReqSchema;
}

void DebitReqDto::OnConfigLoaded(const std::filesystem::path& loc)
{
	EvolutionReqDto::OnConfigLoaded(loc);
	Currency = ECurrency::_from_string(GetConfig("currency").get<std::string>().c_str());
	Game = GameDto::FromJSON(GetConfig("game"));
	Transaction = TransactionDto::FromJSON(GetConfig("transaction"));
}

DebitReqDto DebitReqDto::FromJSON(const json& val)
{
	DebitReqDto dto;
	dto.LoadSubconfiguration(val);
	return dto;
}

json DebitReqDto::ToJSON() const
{
	json val = EvolutionReqDto::ToJSON();
	val["currency"] = Currency._to_string();
	val["game"] = Game.ToJSON();
	val["transaction"] = Transaction.ToJSON();
	return val;
}
