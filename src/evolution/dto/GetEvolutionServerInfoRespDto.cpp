#include "evolution/dto/GetEvolutionServerInfoRespDto.h"

#include "YUtils.h"

const char* GetEvolutionServerInfoRespDto::TimeFormat = "%Y-%m-%dT%H:%M:%S%z";

GetEvolutionServerInfoRespDto::GetEvolutionServerInfoRespDto(time_t startTimeS, const std::string& version, const int64_t timezoneOffset) :
    StartTimeS(startTimeS), Version(version), TimezoneOffset(timezoneOffset)
{
}

json GetEvolutionServerInfoRespDto::ToJSON() const
{
	json val;
	val["startTime"] = yutils::FormatTime(TimeFormat, StartTimeS);
	val["version"] = Version;
	val["timezoneOffset"] = TimezoneOffset;
	return val;
}

GetEvolutionServerInfoRespDto GetEvolutionServerInfoRespDto::FromJSON(const json& val)
{
	return GetEvolutionServerInfoRespDto(yutils::ParseTime(TimeFormat, val["startTime"].get<std::string>()), val["version"].get<std::string>(),
	                                     val["timezoneOffset"].get<int64_t>());
}
