#include "evolution/EvolutionServerApp.h"

#include "Cryptography.h"
#include "Timing.h"
#include "YUtils.h"
#include "common/TBuildDefinitions.h"
#include "evolution/EvolutionClient.h"
#include "evolution/dto/GetEvolutionServerInfoRespDto.h"
#include "evolution/dto/UserAuthenticationReqDto.h"
#include "evolution/dto/UserAuthenticationRespDto.h"
#include "imaxanano/TNanoTypes.h"
#include "web/error/UnauthorizedError.h"
#include "web/yprotocol/YClient.h"


MAIN_METHOD(EvolutionServerApp, "EVOLUTION_SERVER")
DEFINE_LOG_CATEGORY(LogEvolutionServer, "evolution")

EvolutionServerApp::EvolutionServerApp(const std::string& type) : TApplication(type)
{
	// evolution server schema for config file
	Schema() += EvolutionConfigDto::EvolutionConfigSchema;

	// register http handlers
	AddRoute("/api/balance", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<BalanceReqDto>(con, resource, query, true, [this](const BalanceReqDto& req) -> json { return OnBalanceRequest(req); });
	         });
	AddRoute("/api/debit", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<DebitReqDto>(con, resource, query, true, [this](const DebitReqDto& req) -> json { return OnDebitRequest(req); });
	         });
	AddRoute("/api/credit", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<CreditReqDto>(con, resource, query, true, [this](const CreditReqDto& req) -> json { return OnCreditRequest(req); });
	         });
	AddRoute("/api/cancel", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<CancelReqDto>(con, resource, query, true, [this](const CancelReqDto& req) -> json { return OnCancelRequest(req); });
	         });
	AddRoute("/api/promo_payout", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<PromoPayoutReqDto>(con, resource, query, true, [this](const PromoPayoutReqDto& req) -> json { return OnPromoPayoutRequest(req); });
	         });
	AddRoute("/api/check", web::http::verb::post,
	         [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		         HandleHttpRequest<CheckUserReqDto>(con, resource, query, true, [this](const CheckUserReqDto& req) -> json { return OnCheckUserRequest(req); });
	         });
	AddRoute("/api/sid", web::http::verb::post, [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		HandleHttpRequest<CheckUserReqDto>(con, resource, query, true, [this](const CheckUserReqDto& req) -> json { return OnSidRequest(req); });
	});
	AddRoute("/", web::http::verb::get, [this](const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query) -> void {
		HandleHttpRequest<TConfiguration>(con, resource, query, false, [this](const TConfiguration&) -> json { return OnGetServerInfo(); });
	});

	// register WS handlers
	RequestHandler = std::make_shared<yprotocol::YDedicatedRequestHandler<EvolutionServerClient>>();
}

EvolutionServerApp::~EvolutionServerApp()
{
	Log(Info, "Shutting down server!");

	// close all clients
	CloseAllConnections();

	// stop web server
	Stop();

	// stop application
	TApplication::Destroy();

	Log(Info, "Server is closed.");
}

template <Derived<TConfiguration> T>
void EvolutionServerApp::HandleHttpRequest(const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query,
                                           const bool authRequired, const HttpHandler<T>& handler)
{
	const uint64_t startTS = ytime::GetSystemTimeMsec();
	const auto& reqMethod = con->get_request_method();
	const auto& reqName = con->get_resource();
	json bodyJson;
	std::string sentBody;

	try
	{
		if (authRequired)
		{
			ValidateAuthToken(GetAuthToken(query));
		}
		bodyJson = yutils::strToJson(con->get_request_body(), true);
		Log(Info, std::format("Received HTTP {} request {}{}", reqMethod, reqName, bodyJson.is_null() ? std::string() : ": " + yutils::jsonToStr(bodyJson)).c_str());
		auto response = handler(parseAndValidateHttpReq<T>(bodyJson));
		FormatDoubles(response, 2);
		sentBody = send_with_status(con, web::http::status::ok, {}, response);
	}
	catch (const SchemaError& e)
	{
		Log(Warning, "Schema error: %s", e.what());
		// if schema error, then we already have bodyJson and try just to sid from it
		std::string sid;
		if (const auto* sidJson = FindMember(bodyJson, "sid"))
			sid = sidJson->get<std::string>();
		const auto resp = EvolutionRespDto(EStatus::INVALID_PARAMETER, sid);
		sentBody = send_with_status(con, web::http::status::ok, {}, resp.ToJSON());
	}
	catch (const HTTPError& e)
	{
		Log(Warning, "Context error: %s", e.what());
		std::string sid;
		if (const auto* sidJson = FindMember(bodyJson, "sid"))
			sid = sidJson->get<std::string>();
		const auto resp = EvolutionRespDto(EStatus::UNKNOWN_ERROR, sid);
		sentBody = send_with_status(con, web::http::status::ok, {}, resp.ToJSON());
	}
	catch (const std::exception& e)
	{
		Log(Error, e.what());
		send_with_status(con, web::http::status::internal_server_error, e.what());
	}

	Log(Info, "Sending HTTP %s response %s (took %lums): %s", reqMethod.c_str(), reqName.c_str(), ytime::GetSystemTimeMsec() - startTS, sentBody.c_str());
}

int EvolutionServerApp::Init(const std::vector<std::string>& args)
{
	if (!args.empty())
		SetParam("Config", args[0]);

	if (const int res = TApplication::Init(args))
		return res;

	setStatus(EApplicationState::Startup);

	return 0;
}

int EvolutionServerApp::LoadEnvironmentVariables()
{
	const int res = TApplication::LoadEnvironmentVariables();
	if (res)
		return res;

	if (!GetParamFromCache("Config"))
		LoadParamFromEnvironmentOptional("CONFIGURATION", "Config", "evolution-server.conf");

	LoadParamFromEnvironmentOptional("CONFIGURATION_USER", "UserConfig", {});

	std::string conf = GetParam("Config")->Value();
	try
	{
		Log(Important, "Loading Evolution server configuration...");
		LoadPatchedConfiguration(conf, GetParam("UserConfig", { ParameterDomain::CACHE })->Value());
	}
	catch (const SchemaError& err)
	{
		Log(Critical, "Evolution server config does not conform to schema: %s", err.what());
		return -1;
	}
	catch (const ConfigError& err)
	{
		Log(Critical, "CONFIG FILE %s COULD NOT LOAD: %s", conf.c_str(), err.what());
		return -1;
	}

	return 0;
}

void EvolutionServerApp::OnConfigLoaded(const std::filesystem::path& loc)
{
	YProtocolWebServer::OnConfigLoaded(loc);

	// load server config
	const auto configJson = GetConfig({});
	mEvolutionConfig = std::make_shared<EvolutionConfigDto>(EvolutionConfigDto::FromJSON(configJson));
	Log(Debug, "Evolution config loaded: %s", JsonSchema::PrintValueInline(configJson).c_str());

	// starting dao
	std::filesystem::path EvolutionDbFolder = mEvolutionConfig->EvolutionDbDir;
	if (EvolutionDbFolder.is_relative())
		EvolutionDbFolder = loc.parent_path() / EvolutionDbFolder;
	if (!exists(EvolutionDbFolder))
		create_directories(EvolutionDbFolder);
	mEvolutionDao = std::make_shared<EvolutionDao>(EvolutionDbFolder, mEvolutionConfig->bDiskSync);

	// starting service
	mEvolutionService = std::make_unique<EvolutionService>(mEvolutionConfig);

	// evolution client
	mEvolutionWebClient = web::WebClient::New(mEvolutionConfig->EvolutionEndpoint.Hostname, true);
	mEvolutionWebClient->LogComponentName = "EvolutionClient";
	mEvolutionWebClient->SetLogCategory(LogEvolutionServer);

	// test data
	if (mEvolutionConfig->TestData)
	{
		mYServerAdminAPIClient = std::make_unique<YServerAdminAPIClient>(
		  std::make_shared<YClient>(mEvolutionConfig->TestData->YServerHost, mEvolutionConfig->TestData->YServerSecure, "Y client"));
		mYServerAdminAPIClient->SetAPIKeys(mEvolutionConfig->TestData->ApiKey, mEvolutionConfig->TestData->ApiSecret);

		// nano client
		if (!mEvolutionConfig->TestData->DemoUser)
		{
			if (mEvolutionConfig->TestData->NanoProviderDomain.empty())
				throw ConfigError("Nano provider domain  should be set for non-demo mode!");
			mNanoClient = GetNanoClient(mEvolutionConfig->TestData->NanoProviderDomain);
		}
	}
}

void EvolutionServerApp::Startup()
{
	// start server
	TApplication::Startup();

	int initRes = 0;
	do {
		initRes = StartServer();
	} while (initRes != 0 && !shuttingDown());

	if (initRes != 0)
	{
		Log(Critical, "%s SERVER COULD NOT START", ClientType.c_str());
		return;
	}

	Log(Important, "======= %s SERVER v%s IS ONLINE =======", ClientType.c_str(), version::FULL_VERSION_STRING);
}

int EvolutionServerApp::StartServer()
{
	if (!Initialize())
		return 1;

	// Start the server accept loop, can fail if port bind fails
	try
	{
		Start("evolution-server");
		Log(Info, "Server started on host %s and port %d.", mHostAddress.to_string().c_str(), mPort);
	}
	catch (const websocketpp::exception& e)
	{
		Log(Error, "Server could not start on port %d: %s", mPort, e.what());
		return 2;
	}

	return 0;
}

web::websockets::session::validation::value EvolutionServerApp::accept_ws(const imaxa_connection_ptr& con, std::optional<security::JWToken> token)
{
	// parse query string to get server address and secure flag and connect to yserver
	const web::QueryString query(con->get_uri()->get_query());
	const std::string yAddress = query.Get("server");
	if (yAddress.empty())
	{
		Log(Warning, "New client connect: No server address in query string. Rejecting connection.");
		return websocketpp::session::validation::reject;
	}
	const bool ySecure = query.Get("secure") == "true";

	// create server client
	const auto client = CreateClient(yAddress, ySecure, std::move(token));
	if (!client)
		return websocketpp::session::validation::reject;

	con->set_open_handler([this, client](imaxa_connection_hdl_ref hdl) -> void { on_open(hdl, client); });
	con->set_fail_handler([this, client](imaxa_connection_hdl_ref hdl) -> void { on_open_failed(hdl, client); });

	return websocketpp::session::validation::accept;
}

void EvolutionServerApp::OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& endpoint)
{
	WebServer::OnInitialize(endpoint);
	endpoint->set_user_agent("Evolution Server " + std::string(version::FULL_VERSION_STRING));
}

void EvolutionServerApp::on_open(imaxa_connection_hdl_ref hdl, const std::weak_ptr<EvolutionServerClient>& clientPtr)
{
	const auto client = clientPtr.lock();
	if (!client)
		return;

	client->Reconnect(hdl);

	const auto conn = hdl.lock();
	if (!conn)
		return;

	const UserAuthenticationReqDto userAuthReq =
	  UserAuthenticationReqDto(crypto::GenRandUUIDv4(),
	                           UserAuthPlayerDto(client->GetPlayerID(), true, client->mYClient->Nickname, client->mYClient->Locale, client->mYClient->Currency,
	                                             UserAuthSessionDto(client->GetSessionID(), conn->get_remote_ip())),
	                           UserAuthConfigDto(UserAuthBrandDto(std::nullopt, "1"), std::nullopt, std::nullopt));

	const json body = userAuthReq.ToJSON();
	web::http::request req;
	req.set_json_body(body);
	req.set_method(web::http::method(web::http::verb::post));
	req.set_uri(std::format("/ua/v1/{}/{}", mEvolutionConfig->EvolutionEndpoint.CasinoKey, mEvolutionConfig->EvolutionEndpoint.ApiToken));
	Log(Info, "Sending user authentication request %s %s: %s", req.get_method().c_str(), req.get_uri().c_str(), req.get_body().c_str());
	mEvolutionWebClient->Request(std::move(req)).then(boost::launch::sync, [this, client](boost::future<web::http::response> fut) {
		const web::http::response resp = fut.get();

		json init;

		if (resp.get_error_code())
			init["error"] = std::format("User authentication request failed: {}", resp.get_error_code().message());
		else
		{
			Log(Debug, "Received user auth response: %s", resp.get_body().c_str());
			const auto respDto = UserAuthenticationRespDto::FromJSON(yutils::strToJson(resp.get_body()));
			if (!respDto.IsOK())
				init["error"] = std::format("User authentication received resp with errors: {}", resp.get_body());
			else
			{
				Log(Info, "User authentication successful: %s", resp.get_body().c_str());
				init["url"] = std::format("https://{}{}", mEvolutionConfig->EvolutionEndpoint.Hostname, respDto.Entry.value());
				init["urlEmbedded"] = std::format("https://{}{}", mEvolutionConfig->EvolutionEndpoint.Hostname, respDto.EntryEmbedded.value());
			}
		}

		client->SendNewServerMessage(yprotocol::EServerMessageType::Init, init);

		Log(Info, "New client %s connected", client->GetPlayerID().c_str());

		if (const json* error = FindMember(init, "error"))
		{
			Log(Warning, "Client %s could not authenticate: %s", client->GetPlayerID().c_str(), error->get<std::string>().c_str());
			client->Disconnect(websocketpp::close::status::normal, "Client could not authenticate");
		}
	});
}

void EvolutionServerApp::on_open_failed(imaxa_connection_hdl_ref hdl, const std::weak_ptr<EvolutionServerClient>& clientPtr)
{
	auto client = clientPtr.lock();
	if (!client)
		return;

	remove_client(*client);
}

void EvolutionServerApp::remove_client(const EvolutionServerClient& client)
{
	mEvolutionService->RemoveClient(client);
}

void EvolutionServerApp::OnClientDisconnected(yprotocol::YProtocolClient& client)
{
	remove_client(dynamic_cast<EvolutionServerClient&>(client));
	YProtocol::OnClientDisconnected(client);
}

void EvolutionServerApp::OnPingTimer()
{
	PingClients();
}

template <typename T>
using base_type = typename std::remove_cv<typename std::remove_reference<T>::type>::type;

/**
 * @brief Parse and validate HTTP request body
 *
 * @tparam T The class to which we are parsing the request body
 * @param bodyJson The request body
 * @return Instance of T
 */
template <Derived<TConfiguration> T>
T EvolutionServerApp::parseAndValidateHttpReq(const json& bodyJson)
{
	// Validate json against schema and load it into the request object of type T
	std::unique_ptr<base_type<T>> request = std::make_unique<base_type<T>>();
	request->LoadSubconfiguration(bodyJson);

	return *request;
}

/**
 * Send HTTP response to client
 *
 * @param connection Connection pointer
 * @param statusCode Response HTTP status code
 * @param statusMsg Response HTTP status message
 * @param jsonBody Response body (json)
 * @return The sent body as string (useful for logging)
 */
std::string EvolutionServerApp::send_with_status(const imaxa_connection_ptr& connection, const web::http::status::value statusCode, const std::string& statusMsg,
                                                 const json& jsonBody)
{
	std::error_code ec;
	connection->set_status(statusCode, statusMsg, ec);
	ec.clear();
	std::string body;
	if (!jsonBody.is_null())
	{
		body = yutils::jsonToStr(jsonBody);
		// Set body options if body size is greater than 1KB
		web::http::body_options bodyOptions = {};
		if (body.size() > 1024)
		{
			bodyOptions.input_encoding = web::http::content_encoding::from_string(connection->get_response_header(websocketpp::http::Header_ContentEncoding));
			bodyOptions.output_encoding = { web::http::content_encoding::zstd, web::http::content_encoding::brotli, web::http::content_encoding::deflate,
				                            web::http::content_encoding::gzip };
		}
		connection->set_body(body, bodyOptions, ec);
		connection->append_header(web::http::field::content_type, "application/json", ec);
	}
	connection->send_http_response(ec);
	return body;
}

std::string EvolutionServerApp::GetAuthToken(const web::QueryString& query)
{
	if (const auto autoTokenStr = query.Get("authToken"); !autoTokenStr.empty())
		return autoTokenStr;

	throw SchemaError(ESchemaErrorCode::VALUE_NOT_VALID, "Missing authToken in query string");
}

void EvolutionServerApp::ValidateAuthToken(const std::string& authToken) const
{
	if (!mEvolutionConfig->AuthTokens.contains(authToken))
		throw UnauthorizedError("Invalid authToken");
}

/** HANDLERS */

json EvolutionServerApp::OnGetServerInfo() const
{
	const auto resp = GetEvolutionServerInfoRespDto(StartedSystemTimeMs / 1000, version::FULL_VERSION_STRING, ytime::GetUTCOffsetSeconds() / 60);
	return resp.ToJSON();
}

json EvolutionServerApp::OnBalanceRequest(const BalanceReqDto& req) const
{
	const auto resp = mEvolutionService->Balance(req);
	return resp.ToJSON();
}

json EvolutionServerApp::OnDebitRequest(const DebitReqDto& req) const
{
	const auto resp = mEvolutionService->Debit(req);
	return resp.ToJSON();
}

json EvolutionServerApp::OnCreditRequest(const CreditReqDto& req)
{
	std::shared_ptr<EvolutionServerClient> tempClient = ReconnectClientIfDisconnected(req.UserId, req.Sid);

	const auto resp = mEvolutionService->Credit(req);

	if (tempClient)
		mEvolutionService->RemoveClient(*tempClient);

	return resp.ToJSON();
}

json EvolutionServerApp::OnCancelRequest(const CancelReqDto& req)
{
	std::shared_ptr<EvolutionServerClient> tempClient = ReconnectClientIfDisconnected(req.UserId, req.Sid);

	const auto resp = mEvolutionService->Cancel(req);

	if (tempClient)
		mEvolutionService->RemoveClient(*tempClient);

	return resp.ToJSON();
}

json EvolutionServerApp::OnPromoPayoutRequest(const PromoPayoutReqDto& req)
{
	std::shared_ptr<EvolutionServerClient> tempClient = ReconnectClientIfDisconnected(req.UserId, req.Sid);

	const auto resp = mEvolutionService->PromoPayout(req);

	if (tempClient)
		mEvolutionService->RemoveClient(*tempClient);

	return resp.ToJSON();
}

json EvolutionServerApp::OnCheckUserRequest(const CheckUserReqDto& req) const
{
	const auto resp = mEvolutionService->CheckUser(req);
	return resp.ToJSON();
}

json EvolutionServerApp::OnSidRequest(const CheckUserReqDto& req)
{
	if (!mEvolutionConfig->TestData)
		throw std::runtime_error("Testing is not enabled");

	if (!mEvolutionConfig->TestData->WhitelistedTestUsers.contains(req.UserId))
		return CheckUserRespDto(EStatus::INVALID_PARAMETER, req.Uuid, req.Sid).ToJSON();

	const auto testUserIt = mEvolutionConfig->TestData->WhitelistedTestUsers.find(req.UserId);
	if (testUserIt == mEvolutionConfig->TestData->WhitelistedTestUsers.end())
		return CheckUserRespDto(EStatus::INVALID_PARAMETER, req.Uuid, req.Sid).ToJSON();

	const auto yAddress = CreateClientEndpoint(mEvolutionConfig->TestData.value(), testUserIt->second);
	const auto ySecure = mEvolutionConfig->TestData->YServerSecure;

	// create test server client
	const auto client = CreateClient(yAddress, ySecure, {}, req.UserId);
	if (!client)
		throw std::runtime_error("Failed to connect to Tronius backend");

	auto reqCopy = req;
	reqCopy.Sid = client->GetSessionID();
	const auto resp = mEvolutionService->CheckUser(reqCopy);
	return resp.ToJSON();
}

std::shared_ptr<EvolutionServerClient> EvolutionServerApp::ReconnectClientIfDisconnected(const std::string& userId, const std::string& sessionId)
{
	if (const auto user = mEvolutionService->GetClient(userId, sessionId, false).first; !user)
		if (const auto clientConfig = mEvolutionDao->GetSession(sessionId); clientConfig && clientConfig->PlayerId == userId)
			return CreateClient(clientConfig->YAddress, clientConfig->bYSecure, {}, userId);

	return nullptr;
}

std::string EvolutionServerApp::CreateClientEndpoint(const EvolutionTestDataDto& testData, const TestUserDto& testUser) const
{
	return CreateClientEndpoint(mNanoClient, *mYServerAdminAPIClient, testUser, testData.Currency, testData.Locale, testData.DemoUser, testData.EvolutionHostId,
	                            testData.EvolutionGame, false, true);
}

std::string EvolutionServerApp::CreateClientEndpoint(std::shared_ptr<NanoClient> nanoClient, YServerAdminAPIClient& yserverAdminApiClient, const TestUserDto& testUser,
                                                     ECurrency currency, const std::string& locale, bool demo, const std::string& hostId, const std::string& gameId,
                                                     const bool allowUnfinished, const bool createUserIfNotExists) const
{
	if (!demo && !nanoClient)
		throw std::runtime_error("Cannot create client endpoint. Nano client is not set");

	web::QueryString str;
	str["locale"] = locale;
	str["drop-on-disconnect"] = "true";
	if (demo)
	{
		str["demo"] = "true";
		str["user"] = testUser.Username;
		str["currency"] = currency._to_string();
	}
	else
	{
		str["provider"] = nanoClient->NanoProviderId;
		str["sid"] = nanoClient->CreateGameSession(testUser, allowUnfinished, createUserIfNotExists);
	}
	str["host"] = hostId;
	str["game"] = gameId;
	const web::json_response endp = yserverAdminApiClient.CreateProtectedEndpoint(yserver::EClientType::Player, str).get();
	if (endp.get_status_code() != web::http::status::ok || endp.get_json().is_null())
		throw std::runtime_error(std::format("Failed to create endpoint: {}", endp.get_status_msg()));

	const std::string endpoint = endp.get_json()["endpoint"].get<std::string>();
	const std::string challenge = endp.get_json()["challenge"].get<std::string>();

	return std::format("{}/Player/{}/{}", mEvolutionConfig->TestData->YServerHost, endpoint, challenge);
}

std::shared_ptr<EvolutionServerClient> EvolutionServerApp::CreateClient(const std::string& yAddress, bool ySecure, std::optional<security::JWToken> token,
                                                                        const std::optional<std::string>& overridePlayerId, const bool storeSession)
{
	auto yClient = std::make_shared<EvolutionClient>(yAddress, ySecure);
	if (!yClient->Connect())
		return nullptr;

	// create test server client
	std::shared_ptr<EvolutionServerClient> client = std::make_shared<EvolutionServerClient>(this, RequestHandler, yClient, std::move(token), overridePlayerId);
	mEvolutionService->AddClient(client);

	if (storeSession)
		mEvolutionDao->SaveSession(client->GetSessionID(), client->GetConfig());

	return client;
}

json EvolutionServerApp::HandleWsRequest(const EvolutionServerClient& client, const yprotocol::Request& req, const WsHandler& handler)
{
	try
	{
		const uint64_t startTS = ytime::GetSystemTimeMsec();
		const auto& reqMethod = req.Method();
		const auto& body = req.GetParam();
		Log(Info, std::format("Received WS request {}: {}", reqMethod.c_str(), JsonSchema::PrintValueInline(body)).c_str());
		const auto response = handler(client, req.GetParam());
		Log(Info, "Sending WS response %s (took %lums)", reqMethod.c_str(), ytime::GetSystemTimeMsec() - startTS);
		return response;
	}
	catch (const std::exception& e)
	{
		throw yprotocol::RequestError(req, e.what());
	}
}

void EvolutionServerApp::FormatDoubles(json& j, const int precision)
{
	// Multiplier for rounding to the specified precision
	const double factor = std::pow(10, precision);

	for (auto& el : j.items())
	{
		if (el.value().is_object())
			FormatDoubles(el.value(), precision);
		else if (el.value().is_number_float())
			el.value() = std::round(el.value().get<double>() * factor) / factor;
	}
}

std::shared_ptr<NanoClient> EvolutionServerApp::GetNanoClient(const std::string& providerDomain)
{
	const web::QueryString listProvidersQuery = { { "provider-domain", providerDomain }, { "provider", "-1" }, { "download-config", "true" } };
	const web::json_response listProvidersResp = mYServerAdminAPIClient->APIRequest("/list/providers?" + listProvidersQuery.ToString()).get();
	if (listProvidersResp.get_status_code() != web::http::status::ok || listProvidersResp.get_json().is_null())
		throw std::runtime_error(std::format("Failed to list providers: {}", listProvidersResp.get_status_msg()));

	if (listProvidersResp.get_json().empty())
		throw std::runtime_error("No providers found");
	if (listProvidersResp.get_json().size() > 1)
		throw std::runtime_error("Multiple providers found");
	const auto& nanoProvider = listProvidersResp.get_json()[0];
	const auto endpoint = nanoProvider["raw-config"]["client"]["endpoint"].get<std::string>();
	const auto secure = nanoProvider["raw-config"]["secure"].get<bool>();
	const auto nanoSalt = nanoProvider["raw-config"]["admin-salt"].get<std::string>();
	const auto nanoProviderId = nanoProvider["uid"].get<std::string>();

	return NanoClient::New(endpoint, secure, nanoProviderId, nanoSalt);
}

NanoClient::NanoClient(const WebClientConfig& cfg, const std::string& nanoProviderId, const std::string& nanoSalt) :
    WebClient(cfg), NanoProviderId(nanoProviderId), NanoSalt(nanoSalt)
{
	LogComponentName = "NanoClient";
	SetLogCategory(LogEvolutionServer);
}

std::string NanoClient::CreateGameSession(const TestUserDto& testUser, const bool allowUnfinished, const bool createUserIfNotExists)
{
	auto sessionResp = CreateSessionReq(testUser, allowUnfinished).get();
	if (sessionResp.get_status_code() == web::http::status::ok)    // user exists
	{
		const std::string sessionId = sessionResp.get_body();
		TLOG(LogEvolutionServer, Info, "Nano session created for existing user %s: %s", testUser.Username.c_str(), sessionId.c_str());
		return sessionId;
	}

	if (createUserIfNotExists && sessionResp.get_status_code() == web::http::status::not_found)    // no such player, create one!
	{
		json RequestBody(json::value_t::object);
		RequestBody["type"] = ENanoPlayerType(ENanoPlayerType::Kiosk)._to_string();
		RequestBody["pass"] = crypto::Hash(testUser.Password, EHashAlgorithm::SHA256);
		RequestBody["nickname"] = testUser.Username;
		RequestBody["username"] = testUser.Username;

		const auto registerUserResp = Request("/register", PrepareNanoRequest(RequestBody)).get();
		if (registerUserResp.get_error_code())
			throw std::runtime_error(std::format("Failed to create user: {}", sessionResp.get_error_code().message()));
		if (registerUserResp.get_status_code() != web::http::status::ok)
			throw std::runtime_error(std::format("Failed to create user: {}", registerUserResp.get_status_msg()));

		TLOG(LogEvolutionServer, Info, "Created new nano user %s. This guy has 0 balance, you should give him some $$$.", testUser.Username.c_str());

		sessionResp = CreateSessionReq(testUser, allowUnfinished).get();
	}

	if (sessionResp.get_error_code())
		throw std::runtime_error(std::format("Failed to create session: {}", sessionResp.get_error_code().message()));
	if (sessionResp.get_status_code() != web::http::status::ok)
		throw std::runtime_error(std::format("Failed to create session: {}", sessionResp.get_status_msg()));

	const std::string sessionId = sessionResp.get_body();
	TLOG(LogEvolutionServer, Info, "Nano session created for new user %s: %s", testUser.Username.c_str(), sessionId.c_str());
	return sessionId;
}

boost::future<web::http::response> NanoClient::CreateSessionReq(const TestUserDto& testUser, const bool allowUnfinished)
{
	json createSessionReq(json::value_t::object);
	createSessionReq["username"] = testUser.Username;
	createSessionReq["pass"] = crypto::Hash(testUser.Password, EHashAlgorithm::SHA256);
	createSessionReq["allow-unfinished"] = allowUnfinished;
	createSessionReq["timeout"] = false;

	return Request("/session", PrepareNanoRequest(createSessionReq));
}

web::http::request NanoClient::PrepareNanoRequest(const json& body) const
{
	web::http::request req;
	if (!body.is_null())
	{
		req.set_json_body(body);
		req.set_method(web::http::method(web::http::verb::post));
	}
	else
	{
		req.set_method(web::http::method(web::http::verb::get));
	}
	req.append_header(web::http::field::user_agent, "IGPlatform commander " + pApp->ClientID());
	const uint64_t time = ytime::GetSystemTimeMsec() + 2000;
	req.append_header(web::http::field::expires, std::to_string(time));
	req.append_header(web::http::field::authorization, crypto::Hash(NanoSalt + std::to_string(time) + req.get_body(), EHashAlgorithm::SHA256));
	return req;
}
