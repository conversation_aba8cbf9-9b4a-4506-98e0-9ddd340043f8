#include "evolution/EvolutionServerClient.h"

EvolutionServerClient::EvolutionServerClient(yprotocol::YProtocol* server, std::shared_ptr<yprotocol::YRequestHandler> requestHandler,
                                             std::shared_ptr<EvolutionClient> yClient, std::optional<security::JWToken> token,
                                             const std::optional<std::string>& overrideId) :
    YProtocolClient(server, std::move(requestHandler), yClient->SessionID, std::move(token)), mYClient(std::move(yClient))
{
	if (overrideId && !overrideId->empty())
		PlayerID = *overrideId;
	else
	{
		// if mYClient->PlayerID length is greater than 50, then trim to 50 characters and assign to PlayerID
		PlayerID = mYClient->PlayerID;
		if (PlayerID.length() > 50)
			PlayerID = PlayerID.substr(0, 50);
	}

	SessionID = mYClient->SessionID;
}

std::string EvolutionServerClient::GetPlayerID() const
{
	return PlayerID;
}
std::string EvolutionServerClient::GetSessionID() const
{
	return SessionID;
}

EvolutionServerClientConfig EvolutionServerClient::GetConfig() const
{
	return EvolutionServerClientConfig(mYClient->Client->HostAddress, mYClient->Client->bIsSecure, PlayerID);
}

EvolutionServerClientConfig::EvolutionServerClientConfig(const std::string& yAddress, bool ySecure, const std::string& playerId) :
    YAddress(yAddress), bYSecure(ySecure), PlayerId(playerId)
{
}

json EvolutionServerClientConfig::ToJSON() const
{
	json j(json::value_t::object);
	j["yAddress"] = YAddress;
	j["ySecure"] = bYSecure;
	j["playerId"] = PlayerId;
	return j;
}

EvolutionServerClientConfig EvolutionServerClientConfig::FromJSON(const json& j)
{
	std::string yAddress;
	if (const auto* yAddressJson = FindMember(j, "yAddress"))
		yAddress = yAddressJson->get<std::string>();

	bool ySecure = false;
	if (const auto* ySecureJson = FindMember(j, "ySecure"))
		ySecure = ySecureJson->get<bool>();

	std::string playerId;
	if (const auto* playerIdJson = FindMember(j, "playerId"))
		playerId = playerIdJson->get<std::string>();

	return EvolutionServerClientConfig(yAddress, ySecure, playerId);
}
