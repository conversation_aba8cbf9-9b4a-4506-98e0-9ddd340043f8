#include "jackpot/dto/GetProbabilitiesReqDto.h"

const JsonSchema GetProbabilitiesReqDto::GetProbabilitiesReqSchema =
  RequestDtoSchema() + JsonSchema({ { "numProbabilities", JsonSchema(json::value_t::number_unsigned, "The number of probabilities to return (optional, default: 1)", 1)
                                                            .AddConstraint(limits::ValueLimit(limits::ELimitType::MoreThan, 0U)) } });

GetProbabilitiesReqDto::GetProbabilitiesReqDto(uint32_t numProbabilities) : NumProbabilities(numProbabilities) {};

GetProbabilitiesReqDto::GetProbabilitiesReqDto(const json& req) : RequestDto(req)
{
	if (const auto* numProbabilitiesJson = FindMember(req, "numProbabilities"))
		NumProbabilities = numProbabilitiesJson->get<uint32_t>();
}

json GetProbabilitiesReqDto::ToJSON() const
{
	json val = RequestDto::ToJSON();
	val["numProbabilities"] = NumProbabilities;
	return val;
}
