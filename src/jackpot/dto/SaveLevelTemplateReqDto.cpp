#include "jackpot/dto/SaveLevelTemplateReqDto.h"

const JsonSchema& SaveLevelTemplateReqDto::SaveLevelTemplateSchema()
{
	const auto levelInfoBaseSchema = JackpotLevelInfoBase::JackpotLevelInfoBaseSchema();
	const_cast<JsonSchema&>(levelInfoBaseSchema.GetMember(jackpotlevelkey::CLIENT_GROUP_ID)).SetRequired(false).SetDefault(std::string());
	const static JsonSchema schema = JsonSchema({
	  { "levelInfo", levelInfoBaseSchema },
	});

	__attribute__((unused)) volatile int dummy {};
	return schema;
}

SaveLevelTemplateReqDto::SaveLevelTemplateReqDto()
{
	Schema() += SaveLevelTemplateSchema();
}

SaveLevelTemplateReqDto::SaveLevelTemplateReqDto(const JackpotLevelInfoBase& info) : JackpotLevelInfo(info)
{
	Schema() += SaveLevelTemplateSchema();
}

void SaveLevelTemplateReqDto::OnConfigLoaded(const std::filesystem::path& loc)
{
	RequestDtoTConf::OnConfigLoaded(loc);
	JackpotLevelInfo = JackpotLevelInfoBase::FromJSON(GetConfig("levelInfo"));
}

json SaveLevelTemplateReqDto::ToJSON() const
{
	json val = RequestDtoTConf::ToJSON();
	val["levelInfo"] = JackpotLevelInfo.ToJSON();
	return val;
}
