#include "jackpot/dto/ListArchivedLevelsRespDto.h"

json ListArchivedLevelsRespDto::ToJSON() const
{
	return *this;
}

ListArchivedLevelsRespDto ListArchivedLevelsRespDto::FromJSON(const json& val)
{
	return val.get<ListArchivedLevelsRespDto>();
}

void to_json(json& sourceJson, const ListArchivedLevelsRespDto& dto)
{
	sourceJson["levels"] = dto.Levels;
}

void from_json(const json& sourceJson, ListArchivedLevelsRespDto& dto)
{
	if (const auto* levelsJson = FindMember(sourceJson, "levels"); levelsJson && levelsJson->is_array())
		levelsJson->get_to(dto.Levels);
}
