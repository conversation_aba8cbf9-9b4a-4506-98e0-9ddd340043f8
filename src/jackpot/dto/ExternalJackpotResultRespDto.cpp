#include "jackpot/dto/ExternalJackpotResultRespDto.h"

ExternalJackpotResultRespDto::ExternalJackpotResultRespDto(std::string levelId, const EJackpotLevelStatus jackpotStatus, const EJackpotLevelStatus clientStatus,
                                                           std::vector<BetProcessedDto> betsProcessed) :
    LevelID(std::move(levelId)), JackpotStatus(jackpotStatus), ClientStatus(clientStatus), BetsProcessed(std::move(betsProcessed))
{
}

ExternalJackpotResultRespDto::ExternalJackpotResultRespDto(std::string levelId) : LevelID(std::move(levelId)) {}

json ExternalJackpotResultRespDto::ToJSON() const
{
	json val(json::value_t::object);
	val["levelId"] = LevelID;
	val["jackpotStatus"] = JackpotStatus._to_string();
	val["clientStatus"] = ClientStatus._to_string();
	val["bets"] = BetsProcessed;
	return val;
}

ExternalJackpotResultRespDto ExternalJackpotResultRespDto::FromJSON(const json& val)
{
	return { val["levelId"].get<std::string>(), EJackpotLevelStatus::_from_string(val["jackpotStatus"].get<std::string>().c_str()),
		     EJackpotLevelStatus::_from_string(val["clientStatus"].get<std::string>().c_str()), val["bets"].get<std::vector<BetProcessedDto>>() };
}
