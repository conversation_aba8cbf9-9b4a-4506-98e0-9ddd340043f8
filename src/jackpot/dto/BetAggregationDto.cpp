#include "jackpot/dto/BetAggregationDto.h"

#include "JsonSchema.h"

void to_json(json& j, const ClientBetAggregationDto& dto)
{
	j["counters"] = dto.Counters;
	j["externalTriggerTypeCounters"] = dto.ExternalTriggerTypeCounters;
}

void from_json(const json& j, ClientBetAggregationDto& dto)
{
	if (const auto* countersJson = FindMember(j, "counters"))
		countersJson->get_to(dto.Counters);
	if (const auto* extTriggerTypeCountersJson = FindMember(j, "externalTriggerTypeCounters"); extTriggerTypeCountersJson && extTriggerTypeCountersJson->is_object())
		extTriggerTypeCountersJson->get_to(dto.ExternalTriggerTypeCounters);
}

BetAggregationDto::BetAggregationDto(const CountersDto& counters, const AggPeriod period, const uint64_t fromTimeMs, const double initialPot, const double initialBackPot,
                                     const double finalPot, const double finalBackPot,
                                     const std::unordered_map<std::string, ClientBetAggregationDto>& clientAggregations) :
    Counters(counters), Period(period), FromTimeMs(fromTimeMs), InitialPot(initialPot), InitialBackPot(initialBackPot), FinalPot(finalPot), FinalBackPot(finalBackPot),
    ClientAggregations(clientAggregations)
{
}

json BetAggregationDto::ToJSON() const
{
	json val;
	val["counters"] = Counters;
	val["period"] = Period;
	val["fromTimeMs"] = FromTimeMs;
	val["initialPot"] = InitialPot;
	val["initialBackPot"] = InitialBackPot;
	val["finalPot"] = FinalPot;
	val["finalBackPot"] = FinalBackPot;
	val["clientAggregations"] = ClientAggregations;
	return val;
}

BetAggregationDto BetAggregationDto::FromJSON(const json& val)
{
	const json* countersJson = FindMember(val, "counters");
	const json* periodJson = FindMember(val, "period");
	const json* fromTimeMsJson = FindMember(val, "fromTimeMs");
	const json* initialPotJson = FindMember(val, "initialPot");
	const json* initialBackPotJson = FindMember(val, "initialBackPot");
	const json* finalPotJson = FindMember(val, "finalPot");
	const json* finalBackPotJson = FindMember(val, "finalBackPot");
	const json* clientAggregationsJson = FindMember(val, "clientAggregations");

	return BetAggregationDto(
	  countersJson ? countersJson->get<CountersDto>() : CountersDto(),
	  periodJson ? AggPeriod::_from_string(periodJson->get<std::string>().c_str()) : AggPeriod(AggPeriod::Min15), fromTimeMsJson ? fromTimeMsJson->get<uint64_t>() : 0,
	  initialPotJson ? initialPotJson->get<double>() : 0, initialBackPotJson ? initialBackPotJson->get<double>() : 0, finalPotJson ? finalPotJson->get<double>() : 0,
	  finalBackPotJson ? finalBackPotJson->get<double>() : 0,
	  clientAggregationsJson && clientAggregationsJson->is_object() ? clientAggregationsJson->get<std::unordered_map<std::string, ClientBetAggregationDto>>() :
	                                                                  std::unordered_map<std::string, ClientBetAggregationDto>());
}
