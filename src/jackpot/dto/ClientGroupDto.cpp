#include "jackpot/dto/ClientGroupDto.h"

#include <regex>

#include "JsonSchema.h"

ClientGroupFilter::ClientGroupFilter(const std::string& regex) : Regex(regex) {};

web::QueryString ClientGroupFilter::ToQueryString() const
{
	web::QueryString query = {};

	// regex string
	if (!Regex.empty())
		query.insert_or_assign("regex", Regex);

	return query;
}

ClientGroupFilter ClientGroupFilter::FromQueryString(const web::QueryString& query)
{
	ClientGroupFilter filter;
	// regex
	const auto regex = query.Get("regex");
	if (!regex.empty())
		filter.Regex = regex;

	return filter;
}

ClientGroupDto::ClientGroupDto(const std::string& ClientGroupId) : ClientGroupID(ClientGroupId) {}

json ClientGroupDto::ToJSON() const
{
	json val(json::value_t::object);
	val["clientGroupId"] = ClientGroupID;
	return val;
}

ClientGroupDto ClientGroupDto::FromJSON(const json& val)
{
	const auto* clientGroupId = FindMember(val, "clientGroupId");

	return { clientGroupId ? clientGroupId->get<std::string>() : std::string() };
}

bool ClientGroupDto::Match(const ClientGroupFilter& filter) const
{
	if (!filter.Regex.empty() && !std::regex_match(ClientGroupID, std::regex(filter.Regex)))
		return false;

	return true;
}
