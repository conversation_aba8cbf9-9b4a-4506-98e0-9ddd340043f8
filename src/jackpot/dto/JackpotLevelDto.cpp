#include "jackpot/dto/JackpotLevelDto.h"

JackpotLevelDto::JackpotLevelDto(const JackpotLevelInfoBase& info, const std::string& serverId, const JackpotValuesDto& vals, const bool bCanBeWon) :
    Info(info), Values(vals), ServerID(serverId), bCanBeWon(bCanBeWon)
{
}

json JackpotLevelDto::ToJSON() const
{
	json levelDesc = Values.ToJSON();
	levelDesc["info"] = Info.ToJSON();
	levelDesc["canBeWon"] = bCanBeWon;
	levelDesc["serverId"] = ServerID;
	return levelDesc;
}

JackpotLevelDto JackpotLevelDto::FromJSON(const json& val)
{
	const json* infoJson = FindMember(val, "info");
	const json* serverIdJson = FindMember(val, "serverId");
	const json* canBeWonJson = FindMember(val, "canBeWon");

	return JackpotLevelDto(JackpotLevelInfoBase::FromJSON(infoJson ? *infoJson : json()), serverIdJson ? serverIdJson->get<std::string>() : std::string(),
	                       JackpotValuesDto::FromJSON(val), canBeWonJson ? canBeWonJson->get<bool>() : false);
}
