#include "jackpot/dto/ListBetsReqDto.h"

#include "YUtils.h"

ListBetsReqDto::ListBetsReqDto(const web::QueryString& query) : LevelID(query.Get("level-id")), Filter(BetFilter::FromQueryString(query)) {}

ListBetsReqDto::ListBetsReqDto(const std::string& levelID, const BetFilter& filter) : LevelID(levelID), Filter(filter) {}

web::QueryString ListBetsReqDto::ToQueryString() const
{
	// filter
	web::QueryString query = Filter.ToQueryString();

	// level-id
	query.insert_or_assign("level-id", LevelID);

	return query;
}
