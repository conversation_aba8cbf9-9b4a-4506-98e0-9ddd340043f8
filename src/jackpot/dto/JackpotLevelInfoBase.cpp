#include "jackpot/dto/JackpotLevelInfoBase.h"

#include "LocalDateTime.h"

const JsonSchema& JackpotLevelInfoBase::JackpotLevelInfoBaseSchema()
{
	static const JsonSchema schema {
		{ { jackpotlevelkey::ID,
		    JsonSchema(json::value_t::string, "The level ID to configure (if empty, we are saving new level; if defined, we are editing existing one)", std::string()) },
		  { jackpotlevelkey::TYPE, JsonSchema(json::value_t::string, "The type of jackpot level").SetToEnumType<EJackpotLevelType>() },
		  { jackpotlevelkey::POT_TYPE, JsonSchema(json::value_t::string, "The type of jackpot pot (progressive vs fixed)").SetToEnumType<EJackpotPotType>() },
		  { jackpotlevelkey::CURRENCY,
		    JsonSchema(json::value_t::string, "The currency code in which pot, backpot and bets are being tracked", ECurrency(ECurrency::EUR)._to_string())
		      .SetToEnumType<ECurrency>() },
		  { jackpotlevelkey::CLIENT_GROUP_ID, JsonSchema(json::value_t::string, "The client group ID to which this jackpot level is assigned") },
		  { jackpotlevelkey::INCREMENT,
		    JsonSchema(json::value_t::number_float, "What percentage of every bet to add to the pot (visible and hidden) (0.01 for 1% units)", 0) },
		  { jackpotlevelkey::INCREMENT_VISIBLE_REL,
		    JsonSchema(json::value_t::number_float, "What percentage of increment to add to the visible pot (0.01 for 1% units)").SetRequired(false) },
		  { jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA,
		    JsonSchema(json::value_t::object, "Map of external trigger types with their data", json::value_t::object)
		      .SetChildSchema(JsonSchema({ { "paidIn", JsonSchema(json::value_t::number_float, "The paid in amount (in currency units)", 0) },
		                                   { "winProbability", JsonSchema(json::value_t::number_float, "The win probability", 0) } })) },
		  { jackpotlevelkey::MIN_POT, JsonSchema(json::value_t::number_float, "The minimum value the pot must reach before it can be won (in currency units)") },
		  { jackpotlevelkey::MAX_POT,
		    JsonSchema(
		      json::value_t::number_float,
		      "The maximum value the pot may reach - zero for unlimited (in currency units). This being non-zero also theoretically affects RTP (reduces it).") },
		  { jackpotlevelkey::AVG_PAYOUT, JsonSchema(json::value_t::number_float, "The average payout of the jackpot level (in currency units)", 0) },
		  { jackpotlevelkey::MIN_BET,
		    JsonSchema(json::value_t::number_float,
		               "The minimum bet with which the bet contributes to jackpot. Any bets lower than this will skip level (in currency units)", 0.0) },
		  { jackpotlevelkey::MAX_BET,
		    JsonSchema(
		      json::value_t::number_float,
		      "The maximum bet with which the bet contributes to jackpot. Any bets higher than this will skip level (in currency units). Value 0 means unlimited.",
		      0.0) },
		  { jackpotlevelkey::MIN_WINNABLE_BET,
		    JsonSchema(json::value_t::number_float,
		               "The minimum bet with which the jackpot can be won. Any bets lower than this will never win the jackpot (in currency units)", 0.0) },
		  { jackpotlevelkey::MAX_WINNABLE_BET,
		    JsonSchema(json::value_t::number_float,
		               "The maximum bet with which the jackpot can be won. Any bets higher than this will never win the jackpot (in currency units)", 0.0) },
		  { jackpotlevelkey::TIME_FROM,
		    JsonSchema(json::value_t::number_unsigned,
		               "Beginning of interval in which jackpot can be won (HotSeat) or played (other types) (hours and minutes in HMM format)", 0) },
		  { jackpotlevelkey::TIME_TO, JsonSchema(json::value_t::number_unsigned,
		                                         "End of interval in which jackpot can be won (HotSeat) or played (other types) (hours and minutes in HMM format)", 0) },
		  { jackpotlevelkey::TIMEZONE, JsonSchema(json::value_t::string, "The timezone in which `from`, `to` and `weekDays` are given") },
		  { jackpotlevelkey::WEEK_DAYS, JsonSchema(json::value_t::array, "Weekdays on which jackpot can be won (HotSeat) or played (other types)")
		                                  .SetChildSchema(JsonSchema(json::value_t::string, "The weekday").SetToEnumType<EWeekday>())
		                                  .SetRequired(false) },
		  { jackpotlevelkey::NAME, JsonSchema(json::value_t::string, "The display name of this jackpot (eg. Gold, Silver, ...)") },
		  { jackpotlevelkey::AUTO_ACTION_AFTER_WIN,
		    JsonSchema(json::value_t::string, "Action to perform after after winning the level (with offset autoActionAfterWinOffsetSec)",
		               EActionAfterWin(EActionAfterWin::Nothing)._to_string())
		      .SetToEnumType<EActionAfterWin>() },
		  { jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET,
		    JsonSchema(json::value_t::number_unsigned,
		               "Number of seconds to wait after a level is won before automatically performing an action from autoActionAfterWin (default: 50s)", 50U) },
		  { jackpotlevelkey::MIN_TIME_BETWEEN_WINS,
		    JsonSchema(json::value_t::number_unsigned, "Number of seconds to wait before a level can be won again (default: 2 hours)", 7200U) },
		  { jackpotlevelkey::USE_FIXED_PAID_IN,
		    JsonSchema(json::value_t::boolean, "If true, the level will use fixed paid in amount (from ExternalTriggerTypeData) instead of increment.", false) },
		  { jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT,
		    JsonSchema(
		      json::value_t::boolean,
		      "If true, the level will reset pot to at least MinPot value after win, and could have negative backPot as a result. Valid only for External levels.",
		      false) },
		  { jackpotlevelkey::TEMPLATE_ID, JsonSchema(json::value_t::string, "The template ID of a template the level was created from. Optional.", std::string()) },
		  { jackpotlevelkey::LEVEL_REFERENCE, JsonSchema(json::value_t::string, "The reference to the level. Optional.", std::string()) },
		  { jackpotlevelkey::DEMO, JsonSchema(json::value_t::boolean, "If true, the level is a demo level. Optional.", false) },
		  { jackpotlevelkey::CUSTOM_DATA,
		    JsonSchema(json::value_t::object, "Custom user-defined data as json object with any content. Optional", json::value_t::object) } }
	};
	__attribute__((unused)) volatile int dummy {};
	return schema;
}

void to_json(json& sourceJson, const ExternalTriggerTypeValues& dto)
{
	sourceJson["paidIn"] = dto.PaidIn;
	sourceJson["winProbability"] = dto.WinProbability;
}

void from_json(const json& sourceJson, ExternalTriggerTypeValues& dto)
{
	if (const json* paidInJson = FindMember(sourceJson, "paidIn"))
		paidInJson->get_to(dto.PaidIn);
	if (const json* winProbabilityJson = FindMember(sourceJson, "winProbability"))
		winProbabilityJson->get_to(dto.WinProbability);
}

json JackpotLevelInfoBase::ToJSON() const
{
	return *this;
}

JackpotLevelInfoBase JackpotLevelInfoBase::FromJSON(const json& val)
{
	return val.get<JackpotLevelInfoBase>();
}

void to_json(json& sourceJson, const JackpotLevelInfoBase& dto)
{
	json weekDaysJson(json::value_t::array);
	for (const auto weekDay : EWeekday::_values())
		if (dto.WeekDays.HasFlag(weekDay))
			weekDaysJson.push_back(weekDay._to_string());

	sourceJson[jackpotlevelkey::ID] = dto.ID;
	sourceJson[jackpotlevelkey::TYPE] = dto.Type;
	sourceJson[jackpotlevelkey::POT_TYPE] = dto.PotType;
	sourceJson[jackpotlevelkey::CURRENCY] = dto.Currency;
	sourceJson[jackpotlevelkey::CLIENT_GROUP_ID] = dto.ClientGroupID;
	sourceJson[jackpotlevelkey::INCREMENT] = dto.Increment;
	sourceJson[jackpotlevelkey::INCREMENT_VISIBLE_REL] = dto.IncrementVisibleRel;
	sourceJson[jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA] = dto.ExternalTriggerTypeData;
	sourceJson[jackpotlevelkey::MIN_POT] = dto.MinPot;
	sourceJson[jackpotlevelkey::MAX_POT] = dto.MaxPot;
	sourceJson[jackpotlevelkey::AVG_PAYOUT] = dto.AvgPayout;
	sourceJson[jackpotlevelkey::TIME_FROM] = dto.TimeFrom;
	sourceJson[jackpotlevelkey::TIME_TO] = dto.TimeTo;
	sourceJson[jackpotlevelkey::TIMEZONE] = dto.Timezone.empty() ? json() : json(dto.Timezone);
	sourceJson[jackpotlevelkey::WEEK_DAYS] = std::move(weekDaysJson);
	sourceJson[jackpotlevelkey::MIN_BET] = dto.MinBet;
	sourceJson[jackpotlevelkey::MAX_BET] = dto.MaxBet;
	sourceJson[jackpotlevelkey::MIN_WINNABLE_BET] = dto.MinWinnableBet;
	sourceJson[jackpotlevelkey::MAX_WINNABLE_BET] = dto.MaxWinnableBet;
	sourceJson[jackpotlevelkey::NAME] = dto.Name;
	sourceJson[jackpotlevelkey::AUTO_ACTION_AFTER_WIN] = dto.AutoActionAfterWin;
	sourceJson[jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET] = dto.AutoActionAfterWinOffsetSec;
	sourceJson[jackpotlevelkey::MIN_TIME_BETWEEN_WINS] = dto.MinTimeBetweenWinsSec;
	sourceJson[jackpotlevelkey::USE_FIXED_PAID_IN] = dto.bUseFixedPaidIn;
	sourceJson[jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT] = dto.bKeepPotAboveMinPot;
	if (dto.TemplateID)
		sourceJson[jackpotlevelkey::TEMPLATE_ID] = *dto.TemplateID;
	if (dto.LevelReference)
		sourceJson[jackpotlevelkey::LEVEL_REFERENCE] = *dto.LevelReference;
	sourceJson[jackpotlevelkey::DEMO] = dto.bDemo;
	sourceJson[jackpotlevelkey::CUSTOM_DATA] = dto.CustomData;
}

void from_json(const json& sourceJson, JackpotLevelInfoBase& dto)
{
	dto.WeekDays.Clear();
	if (const json* weekDaysJson = FindMember(sourceJson, jackpotlevelkey::WEEK_DAYS); weekDaysJson && !weekDaysJson->is_null())
		for (const auto& weekday : *weekDaysJson) dto.WeekDays.SetFlag(EWeekday::_from_string(weekday.get<std::string>().c_str()), true);
	else
		dto.WeekDays.Clear(true);

	dto.Timezone = DEFAULT_TIMEZONE;

	if (const json* idJson = FindMember(sourceJson, jackpotlevelkey::ID))
		idJson->get_to(dto.ID);
	if (const json* typeJson = FindMember(sourceJson, jackpotlevelkey::TYPE))
		typeJson->get_to(dto.Type);
	if (const json* potTypeJson = FindMember(sourceJson, jackpotlevelkey::POT_TYPE))
		potTypeJson->get_to(dto.PotType);
	if (const json* currencyJson = FindMember(sourceJson, jackpotlevelkey::CURRENCY))
		currencyJson->get_to(dto.Currency);
	if (const json* clientGroupIdJson = FindMember(sourceJson, jackpotlevelkey::CLIENT_GROUP_ID))
		clientGroupIdJson->get_to(dto.ClientGroupID);
	if (const json* increment = FindMember(sourceJson, jackpotlevelkey::INCREMENT))
		increment->get_to(dto.Increment);
	if (const json* incrementToVisible = FindMember(sourceJson, jackpotlevelkey::INCREMENT_VISIBLE_REL); incrementToVisible && incrementToVisible->is_number_float())
		incrementToVisible->get_to(dto.IncrementVisibleRel);
	if (const json* externalTriggerTypeJson = FindMember(sourceJson, jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA))
		externalTriggerTypeJson->get_to(dto.ExternalTriggerTypeData);
	if (const json* minPotJson = FindMember(sourceJson, jackpotlevelkey::MIN_POT))
		minPotJson->get_to(dto.MinPot);
	if (const json* maxPotJson = FindMember(sourceJson, jackpotlevelkey::MAX_POT))
		maxPotJson->get_to(dto.MaxPot);
	if (const json* avgPayout = FindMember(sourceJson, jackpotlevelkey::AVG_PAYOUT))
		avgPayout->get_to(dto.AvgPayout);
	if (const json* timeFrom = FindMember(sourceJson, jackpotlevelkey::TIME_FROM))
		timeFrom->get_to(dto.TimeFrom);
	if (const json* timeTo = FindMember(sourceJson, jackpotlevelkey::TIME_TO))
		timeTo->get_to(dto.TimeTo);
	if (const json* timezoneNameJson = FindMember(sourceJson, jackpotlevelkey::TIMEZONE); timezoneNameJson && !timezoneNameJson->is_null())
		timezoneNameJson->get_to(dto.Timezone);
	if (const json* minBetJson = FindMember(sourceJson, jackpotlevelkey::MIN_BET))
		minBetJson->get_to(dto.MinBet);
	if (const json* maxBet = FindMember(sourceJson, jackpotlevelkey::MAX_BET))
		maxBet->get_to(dto.MaxBet);
	if (const json* minWinnableBetJson = FindMember(sourceJson, jackpotlevelkey::MIN_WINNABLE_BET))
		minWinnableBetJson->get_to(dto.MinWinnableBet);
	if (const json* maxWinnableBet = FindMember(sourceJson, jackpotlevelkey::MAX_WINNABLE_BET))
		maxWinnableBet->get_to(dto.MaxWinnableBet);
	if (const json* name = FindMember(sourceJson, jackpotlevelkey::NAME))
		name->get_to(dto.Name);
	if (const auto* autoAction = FindMember(sourceJson, jackpotlevelkey::AUTO_ACTION_AFTER_WIN))
		autoAction->get_to(dto.AutoActionAfterWin);
	if (const json* autoActionSec = FindMember(sourceJson, jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET))
		autoActionSec->get_to(dto.AutoActionAfterWinOffsetSec);
	if (const json* minTimeBetweenWins = FindMember(sourceJson, jackpotlevelkey::MIN_TIME_BETWEEN_WINS))
		minTimeBetweenWins->get_to(dto.MinTimeBetweenWinsSec);
	if (const json* useFixedPayin = FindMember(sourceJson, jackpotlevelkey::USE_FIXED_PAID_IN))
		useFixedPayin->get_to(dto.bUseFixedPaidIn);
	if (const json* potAboveMinPot = FindMember(sourceJson, jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT))
		potAboveMinPot->get_to(dto.bKeepPotAboveMinPot);
	if (const json* templateId = FindMember(sourceJson, jackpotlevelkey::TEMPLATE_ID))
		templateId->get_to(dto.TemplateID);
	if (const json* levelReference = FindMember(sourceJson, jackpotlevelkey::LEVEL_REFERENCE))
		levelReference->get_to(dto.LevelReference);
	if (const json* demo = FindMember(sourceJson, jackpotlevelkey::DEMO))
		demo->get_to(dto.bDemo);
	if (const json* customData = FindMember(sourceJson, jackpotlevelkey::CUSTOM_DATA); customData && !customData->is_null())
		dto.CustomData = *customData;
}
