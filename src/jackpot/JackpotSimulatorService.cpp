#include "jackpot/JackpotSimulatorService.h"

#include "Cryptography.h"
#include "jackpot/JackpotUtils.h"
#include "web/error/BadRequestError.h"

DEFINE_LOG_CATEGORY(LogJackpotSimulatorService, "jackpotSimulatorService")

JackpotSimulatorService::JackpotSimulatorService(const std::shared_ptr<JackpotDao>& jackpotDao, const std::shared_ptr<JackpotConfigDto>& jackpotConfig) :
    mJackpotDao(jackpotDao), mJackpotConfig(jackpotConfig)
{
	SetLogCategory(LogJackpotSimulatorService);
}

StartJackpotSimulationRespDto JackpotSimulatorService::StartSimulation(const StartJackpotSimulationReqDto& req)
{
	// validation
	{
		SharedScopedLock lock(mSimulations);
		if (mSimulations->size() >= mJackpotConfig->SimulatorConfig.MaxParallelSimulations)
			throw BadRequestError(std::format("Only {} simulations are allowed at the same time", mJackpotConfig->SimulatorConfig.MaxParallelSimulations));
	}

	if (req.Info.NumRounds <= 0)
		throw BadRequestError("NumRounds must be greater than 0");

	if (req.Info.Levels.size() == 0)
		throw BadRequestError("At least one level must be specified");

	if (req.Info.Clients.size() == 0)
		throw BadRequestError("At least one client must be specified");

	for (const auto& client : req.Info.Clients)
	{
		if (client.ClientID.empty())
			throw BadRequestError("Client ID must be specified for each client");
		if (client.ClientGroupIDs.empty())
			throw BadRequestError("At least one client group ID must be specified for client " + client.ClientID);
		if (client.NumBetsPerRound <= 0)
			throw BadRequestError("NumBetsPerRound must be greater than 0 for client " + client.ClientID);
		if (client.BetAmount <= 0)
			throw BadRequestError("BetAmount must be greater than 0 for client " + client.ClientID);
	}

	for (auto& level : req.Info.Levels)
	{
		// validate that all levels have the same currency as the simulation currency
		if (level.Currency != req.Info.Currency)
			throw BadRequestError("All levels must have the same currency as the simulation currency");
		if (level.AutoActionAfterWin != EActionAfterWin::Reset)
			throw BadRequestError("Only Reset autoActionAfterWin is supported in simulation");
		if (level.AutoActionAfterWinOffsetSec > 0)
			throw BadRequestError("Only 0 autoActionAfterWinOffsetSec is supported in simulation");
		if (level.MinTimeBetweenWinsSec > 0)
			throw BadRequestError("Only 0 minTimeBetweenWinsSec is supported in simulation");
		if (level.Type != EJackpotLevelType::Mystery && level.Type != EJackpotLevelType::External)
			throw std::runtime_error("Only Mystery and External jackpot levels are supported in simulation!");

		for (const auto& client : req.Info.Clients)
		{
			if (!client.ExternalTriggerType.empty() && !level.ExternalTriggerTypeData.contains(client.ExternalTriggerType))
				throw BadRequestError("External trigger type " + client.ExternalTriggerType + " not found in level " + level.ID);
			if (!client.ClientGroupIDs.contains(level.ClientGroupID))
				throw BadRequestError("Level's client group ID " + level.ClientGroupID + " not found in client " + client.ClientID);
		}
	}

	// create simulation and store to memory
	const auto simulation = std::make_shared<JackpotSimulation>(req.Info);
	const auto simulationId = simulation->Data().ID;
	ScopedLock lock(mSimulations);
	mSimulations->emplace(simulationId, simulation);
	lock.unlock();

	// start simulation asynchronously and on success save to DB
	simulation->StartAsync(boost::chrono::seconds(mJackpotConfig->SimulatorConfig.SimulationsTimeoutSec))
	  .then(boost::launch::sync, [this, simulation](boost::future<JackpotSimulationDto> fut) {
		  JackpotSimulationDto dto = fut.get();
		  Log(Info, "Finished simulation %s in %u seconds", dto.ID.c_str(), (dto.EndTimeMs - dto.StartTimeMs) / 1000);
		  mJackpotDao->SaveJackpotSimulation(dto);
		  ScopedLock lock(mSimulations);
		  mSimulations->erase(dto.ID);
		  lock.unlock();
		  Log(Debug, "Moved simulation results %s from memory to DB", dto.ID.c_str());
	  });

	return { simulationId };
}

void JackpotSimulatorService::DeleteSimulation(const DeleteJackpotSimulationReqDto& req)
{
	// delete simulation in db if exists
	mJackpotDao->WorkOn([this, &req](rocksdb::WriteBatch& batch) -> void { mJackpotDao->DeleteJackpotSimulation(batch, req.SimulationID); });

	// delete simulation in memory if exists
	ScopedLock lock(mSimulations);
	if (const auto it = mSimulations->find(req.SimulationID); it != mSimulations->end())
	{
		it->second->Stop();
		mSimulations->erase(req.SimulationID);
	}

	// TODO: if we stop running simulation, it is stored in DB with status "Failed" and not deleted. If we delete it again, it is successfully deleted
}

ListJackpotSimulationsRespDto JackpotSimulatorService::ListSimulations(const ListJackpotSimulationsReqDto& req) const
{
	auto simulations = mJackpotDao->ListJackpotSimulations(req.Filter);

	SharedScopedLock lock(mSimulations);
	for (const auto& runningSimulation : &mSimulations | std::views::values)
		if (runningSimulation->Data().Match(req.Filter))
			simulations.emplace_front(runningSimulation->Data());

	return { simulations };
}
