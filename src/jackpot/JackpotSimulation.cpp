#include "jackpot/JackpotSimulation.h"

#include "jackpot/JackpotUtils.h"
#include "jackpot/dto/ListJackpotLevelsReqDtoAdmin.h"

DEFINE_LOG_CATEGORY(LogJackpotSimulation, "jackpotSimulation")

JackpotSimulation::JackpotSimulation(JackpotSimulationInfoDto info) :
    ID(crypto::GenRandUUIDv4()), mInfo(info), mStartTimeMs(ytime::GetSystemTimeMsec()), mStatus(ESimulationStatus::Running)
{
	mJackpotConfig = std::make_shared<JackpotConfigDto>();
	mJackpotDao = std::make_shared<JackpotDaoMock>();
	mJackpotServerClientManager = std::make_shared<JackpotServerClientManager>(mJackpotDao);
	mJackpotService = std::make_unique<JackpotService>(mJackpotServerClientManager, mJackpotConfig, mJackpotDao);

	// store levels and enable them
	for (const auto& level : info.Levels)
	{
		const auto saveLevelResp = mJackpotService->saveJackpotLevel(SaveJackpotLevelReqDto(level));
		mLevels.emplace(saveLevelResp.LevelInfo.ID, saveLevelResp.LevelInfo);
		mJackpotService->toggleJackpotLevel(ToggleJackpotLevelReqDto(saveLevelResp.LevelInfo.ID, true));
	}
	mInfo.Levels = std::vector<JackpotLevelInfoBase>(std::views::values(mLevels).begin(), std::views::values(mLevels).end());

	// store clients
	for (const auto& client : info.Clients)
	{
		const auto clientConfig = JackpotClientConfig(client.ClientID, mInfo.Currency, 0.01, client.ExternalTriggerType, EJackpotClientType::Client);
		mClients.emplace(client.ClientID, clientConfig);
		mJackpotService->saveClientGroupIds(SaveClientGroupIdsReqDto(client.ClientID, client.ClientGroupIDs));
		const auto levelIds = std::unordered_set<std::string>(std::views::keys(mLevels).begin(), std::views::keys(mLevels).end());
		mJackpotService->setClientEnabledLevels(clientConfig, std::move(levelIds), true);
	}
}

JackpotSimulation::~JackpotSimulation()
{
	Stop();
}

JackpotSimulationDto JackpotSimulation::Data() const
{
	return JackpotSimulationDto(ID, mInfo, mStatus, mStatusMsg, mProgress.load(std::memory_order_relaxed), mStartTimeMs, mEndTimeMs, mResults);
}

boost::future<JackpotSimulationDto> JackpotSimulation::StartAsync(std::optional<boost::chrono::milliseconds> timeout)
{
	auto self = shared_from_this();
	return boost::async(boost::launch::async, [self, timeout]() -> JackpotSimulationDto {
		self->Run(timeout);
		return self->Data();
	});
}

void JackpotSimulation::Stop()
{
	mStopRequested = true;
	if (mFuture.valid())
		mFuture.wait();    // Optional: block until complete
}

bool JackpotSimulation::IsRunning() const
{
	return mFuture.valid() && mFuture.wait_for(boost::chrono::seconds(0)) != boost::future_status::ready;
}

void JackpotSimulation::Run(std::optional<boost::chrono::milliseconds> timeout)
{
	try
	{
		auto start = boost::chrono::steady_clock::now();

		std::unordered_map<std::string, std::list<JackpotSimulationWinDto>> level2wins;

		for (uint32_t i = 0; i < mInfo.NumRounds; ++i)
		{
			if (mStopRequested)
				throw std::runtime_error("Simulation stopped by user");

			if (timeout && boost::chrono::steady_clock::now() - start > *timeout)
				throw std::runtime_error("Simulation timed out");

			const auto roundId = std::to_string(i);

			for (const auto& client : mInfo.Clients)
			{
				const auto clientConfigIt = mClients.find(client.ClientID);
				if (clientConfigIt == mClients.end())
					continue;

				const auto& clientConfig = clientConfigIt->second;

				std::vector<ClientBetDto> clientBets;
				for (uint32_t j = 0; j < client.NumBetsPerRound; ++j)
				{
					const auto playerName = std::format("Player{}", j);
					auto bet = ClientBetDto(client.BetAmount, playerName, playerName, playerName, playerName, roundId);
					clientBets.emplace_back(std::move(bet));
				}

				const auto betResp = mJackpotService->bet(clientConfig, clientBets);
				for (const auto& betLevelData : betResp.LevelsData)
				{
					const auto& levelId = betLevelData.LevelID;
					const auto& levelInfo = mLevels.at(levelId);

					// mystery flow
					if (levelInfo.Type == EJackpotLevelType::Mystery)
					{
						if (const auto simulationWinOpt = GetJackpotSimulationWin(clientConfig.ClientId, betLevelData.LevelID, roundId, betLevelData.BetsData))
							level2wins[betLevelData.LevelID].emplace_back(*simulationWinOpt);
					}
					else if (levelInfo.Type == EJackpotLevelType::External)    // external flow
					{
						// determine if won
						const std::unordered_set<std::string> betsWon = GetBetsWon(levelInfo, client, betLevelData.BetsData);
						const bool isWon = betsWon.size() > 0;

						// send external result
						const auto externalResultResp = mJackpotService->externalJackpotResult(clientConfig, { levelId, isWon });

						if (!isWon)
							continue;

						// send confirm external win
						const auto confirmExternalJackpotWinResp = mJackpotService->confirmExternalJackpotWin(clientConfig, { levelId, true, betsWon });

						if (const auto simulationWinOpt =
						      GetJackpotSimulationWin(clientConfig.ClientId, betLevelData.LevelID, roundId, confirmExternalJackpotWinResp.BetsProcessed))
							level2wins[betLevelData.LevelID].emplace_back(*simulationWinOpt);
					}
					else
					{
						throw std::runtime_error(std::format("Unsupported jackpot level type: {}", levelInfo.Type._to_string()));
					}
				}
			}

			mProgress.store(static_cast<int>((i + 1) * 100.0 / mInfo.NumRounds), std::memory_order_relaxed);
		}

		std::list<JackpotSimulationLevelDto> levelsResults;
		const auto listLevelsResp = mJackpotService->listJackpotLevels({}, { { JACKPOT_ZERO_CLIENT_GROUP_ID }, std::unordered_set<std::string>() });
		for (const auto& level : listLevelsResp.Levels)
		{
			const auto levelId = level.Level.Info.ID;
			levelsResults.emplace_back(JackpotSimulationLevelDto(levelId, level.Counters, level.ClientCounters, level.ExternalTriggerTypeCounters, level2wins[levelId]));
		}

		mStatus = ESimulationStatus::Finished;
		mResults = std::move(levelsResults);
	}
	catch (const std::exception& e)
	{
		Log(Warning, "Simulation %s crashed: %s", ID.c_str(), e.what());
		mStatus = ESimulationStatus::Failed;
		mStatusMsg = e.what();
	}
	mEndTimeMs = ytime::GetSystemTimeMsec();
}

std::optional<JackpotSimulationWinDto> JackpotSimulation::GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
                                                                                  const std::vector<BetDataDto>& betsData) const
{
	std::vector<JackpotSimulationWonBet> wonBets;
	for (const auto& betData : betsData)
		if (betData.Status == EJackpotBetStatus::Won)
			wonBets.emplace_back(betData.BetID, betData.Username, betData.AmountWon);

	return GetJackpotSimulationWin(clientId, levelId, roundId, wonBets);
}

std::optional<JackpotSimulationWinDto> JackpotSimulation::GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
                                                                                  const std::vector<BetProcessedDto>& betsData) const
{
	std::vector<JackpotSimulationWonBet> wonBets;
	for (const auto& betData : betsData)
		if (betData.BetStatus == EJackpotBetStatus::Won)
			wonBets.emplace_back(betData.BetID, betData.PlayerReference, betData.AmountWon);

	return GetJackpotSimulationWin(clientId, levelId, roundId, wonBets);
}

std::optional<JackpotSimulationWinDto> JackpotSimulation::GetJackpotSimulationWin(const std::string& clientId, const std::string& levelId, const std::string& roundId,
                                                                                  const std::vector<JackpotSimulationWonBet>& wonBets) const
{
	if (wonBets.size() == 0)
		return {};

	double totalWonAmount = 0;
	for (const auto& bet : wonBets) totalWonAmount += bet.WonAmount;

	const auto levels = mJackpotService->listJackpotLevels({}, { { JACKPOT_ZERO_CLIENT_GROUP_ID }, { levelId } }).Levels;
	const std::optional<JackpotValuesDto> levelValues = levels.size() == 1 ? levels.front().Level.Values : std::optional<JackpotValuesDto>();

	JackpotSimulationWinDto simulationWin;
	simulationWin.ClientID = clientId;
	simulationWin.WonBets = wonBets;
	simulationWin.WonAmount = totalWonAmount;
	simulationWin.NewPotValue = levelValues ? levelValues->Pot : 0;
	simulationWin.NewBackPotValue = levelValues ? levelValues->BackPot : 0;
	simulationWin.RoundID = roundId;
	return simulationWin;
}

std::unordered_set<std::string> JackpotSimulation::GetBetsWon(const JackpotLevelInfoBase& levelInfo, const SimulationClientInfoDto& client,
                                                              const std::vector<BetDataDto>& betsData) const
{
	if (levelInfo.Type != EJackpotLevelType::External)
		return {};

	std::unordered_set<std::string> betsWon;
	if (client.ExternalTriggerType.starts_with("fireball-"))    // fireball
	{
		// win probability is based on the bet amount (greater bet amount, greater chance to win)
		const auto winProbability = client.BetAmount * levelInfo.Increment / levelInfo.AvgPayout;

		// for each bet in round, determine if won. Several bets can win
		for (const auto& betData : betsData)
			if (crypto::RandomChance(winProbability))
				betsWon.emplace(betData.BetID);
	}
	else if (client.ExternalTriggerType.starts_with("lucky-number-"))    // lucky number
	{
		// win probability from level config is divided by the number of clients (e.g. number of roulette cylinders)
		const auto winProbability = levelInfo.ExternalTriggerTypeData.at(client.ExternalTriggerType).WinProbability / mClients.size() * client.ProbabilityMultiplier;

		// for each bet in round, determine if won. Several bets can win
		for (const auto& betData : betsData)
			if (crypto::RandomChance(winProbability))
				betsWon.emplace(betData.BetID);
	}
	else    // default fallback
	{
		const auto winProbability = levelInfo.ExternalTriggerTypeData.at(client.ExternalTriggerType).WinProbability;

		// for each bet in round, determine if won. Only one bet can win (this is done for live table JP)
		for (const auto& betData : betsData)
			if (crypto::RandomChance(winProbability))
			{
				betsWon.emplace(betData.BetID);
				break;    // only one bet can win
			}
	}

	return betsWon;
}
