#include "jackpot/JackpotService.h"

#include <ranges>

#include "Cryptography.h"
#include "TApplication.h"
#include "Timing.h"
#include "jackpot/JackpotSharedTypes.h"
#include "jackpot/JackpotUtils.h"
#include "jackpot/dto/DeleteJackpotLevelRespDto.h"
#include "jackpot/dto/JackpotLevelReconfiguredEventDto.h"
#include "jackpot/dto/JackpotLevelUpdatedEventDto.h"
#include "jackpot/dto/JackpotWonEventDto.h"
#include "jackpot/dto/ListJackpotLevelsRespDtoAdmin.h"
#include "web/error/BadRequestError.h"
#include "web/error/NotAcceptableError.h"
#include "web/error/NotFoundError.h"


DEFINE_LOG_CATEGORY(LogJackpotService, "jackpot")

using namespace imaxa::jackpot;

JackpotService::JackpotService(const std::shared_ptr<JackpotServerClientManager>& clientManager, const std::shared_ptr<JackpotConfigDto>& jackpotConfig,
                               const std::shared_ptr<JackpotDao>& jackpotDao, const std::shared_ptr<JackpotChildClient>& parent,
                               const std::shared_ptr<JackpotLockService>& jackpotLockService, const std::shared_ptr<JackpotLevelTemplateService>& levelTemplateService) :
    mClientManager(clientManager), mJackpotConfig(jackpotConfig), mJackpotDao(jackpotDao), mParent(parent), mJackpotLockService(jackpotLockService),
    mLevelTemplateService(levelTemplateService)
{
	mJackpotLevelReconfiguredEvent =
	  std::make_shared<yprotocol::EventTemplate>(jackpot::event::JackpotLevelReconfiguredEventName, "Fired when jackpot level is reconfigured",
	                                             JackpotLevelReconfiguredEventDto::JackpotLevelReconfiguredEventSchema);
	mJackpotLevelUpdatedEvent = std::make_shared<yprotocol::EventTemplate>(jackpot::event::JackpotLevelUpdatedEventName, "Fired when jackpot is updated",
	                                                                       JackpotLevelUpdatedEventDto::JackpotLevelUpdatedEventSchema);
	mJackpotWonEvent =
	  std::make_shared<yprotocol::EventTemplate>(jackpot::event::JackpotWonEventName, "Fired when jackpot is won", JackpotWonEventDto::JackpotWonEventSchema);

	SetLogCategory(LogJackpotService);

	RegisterParentEventHandlers();

	InitLockService();

	LoadJackpot();
}

JackpotService::~JackpotService()
{
	// clear jackpot update broadcast task
	if (auto task = mJackpotUpdateBroadcastTask.lock())
		task->Remove();
	mJackpotUpdateBroadcastTask.reset();

	// clear jackpot backup task
	if (auto task = mJackpotBackupTask.lock())
		task->Remove();
	mJackpotBackupTask.reset();

	// clear all jackpot levels (will be reloaded below)
	mJackpotLevels->clear();

	// clear all reset tasks
	ClearAllLevelResetTasks();
}

void JackpotService::RegisterParentEventHandlers()
{
	if (mParent)
	{
		mParent->OnInitialized += [this](const InitRespDto& initResp) -> void {
			TLOG(LogJackpotService, Info, "Jackpot connected to parent server with version %s", initResp.ServerVersion.c_str());

			// notify on all directly connected clients
			for (const auto& [clientId, client] : mClientManager->GetClients())
			{
				InitReqDto requestDto(mClientManager->GetClientGroupIds(clientId));
				requestDto.ClientConfig = client->GetJackpotClientConfig();
				mParent->InitReq(requestDto);
			}

			// notify on all indirectly connected clients
			for (const auto& [clientId, clientPath] : mClientManager->GetClientPaths())
			{
				InitReqDto requestDto(mClientManager->GetClientGroupIds(clientId));
				requestDto.ClientConfig = JackpotClientConfig(clientId);    // we just need clientId and clientType which is by default Client.
				requestDto.Path = clientPath;
				mParent->InitReq(requestDto);
			}
		};
		mParent->OnJackpotLevelReconfiguredEvent += [this](const JackpotLevelReconfiguredEventDto& data) -> void {
			mClientManager->Multicast(data.Recipients, data.EventClientGroupID, *mJackpotLevelReconfiguredEvent, data);
		};
		mParent->OnJackpotLevelUpdatedEvent += [this](const JackpotLevelUpdatedEventDto& data) -> void {
			mClientManager->Multicast(data.Recipients, data.EventClientGroupID, *mJackpotLevelUpdatedEvent, data);
		};
		mParent->OnJackpotWonEvent += [this](const JackpotWonEventDto& data) -> void {
			mClientManager->Multicast(data.Recipients, data.EventClientGroupID, *mJackpotWonEvent, data);
		};
	}
}

/**
 * @brief Save jackpot level service
 *
 * @param req The request instance (see SaveJackpotLevelReqDto)
 * @return Created/updated jackpot level
 */
SaveJackpotLevelRespDto JackpotService::saveJackpotLevel(const SaveJackpotLevelReqDto& req)
{
	auto info = req.JackpotLevelInfo;

	jackpotutils::ValidateLevelInfo(info);

	ESaveJackpotLevelResult status = ESaveJackpotLevelResult::NoChange;

	if (info.ID.empty())    // create new level
	{
		// set new ID
		info.ID = crypto::GenRandUUIDv4();
		TLOG(LogJackpotService, Info, "Creating new jackpot level %s with ID: %s", info.Name.c_str(), info.ID.c_str())
		mJackpotDao->SaveJackpotLevelConfig(info);
		mJackpotDao->SaveCountersSinceTS(info.ID, ytime::GetSystemTimeMsec());
		LoadJackpotLevel(info.ID, true);
		status = ESaveJackpotLevelResult::Created;
	}
	else    // update existing level
	{
		// get existing level and throw if not found
		const auto existingLevel = GetJackpotLevel(info.ID, true);
		mJackpotDao->WorkOnJackpotLevel(existingLevel, [this, &existingLevel, &info, &status](rocksdb::WriteBatch& batch) -> void {
			// validate that we only update fields that are allowed to be updated unless the level is in a ramclean state where we can update everything
			const auto currentInfo = existingLevel->GetInfo_AssumeLocked();
			if (!existingLevel->IsRamClear_AssumeLocked())
			{
				if (currentInfo.Type != info.Type)
					throw NotAcceptableError("Can't change jackpot level type!");
				if (currentInfo.PotType != info.PotType)
					throw NotAcceptableError("Can't change jackpot level pot type!");
				if (currentInfo.Currency != info.Currency)
					throw NotAcceptableError("Can't change jackpot level currency!");
				if (currentInfo.ClientGroupID != info.ClientGroupID)
					throw NotAcceptableError("Can't change jackpot level client group ID!");
				if (currentInfo.bDemo != info.bDemo)
					throw NotAcceptableError("Can't change jackpot level demo status!");
			}

			// we leave templateID it as it is on level update
			info.TemplateID = currentInfo.TemplateID;

			// if level is disabled or won, we can update it immediately
			if (existingLevel->Status_AssumeLocked() == EJackpotLevelStatus::Disabled || existingLevel->Status_AssumeLocked() == EJackpotLevelStatus::Won)
				status = UpdateJackpotLevel_AssumeLocked(batch, existingLevel, info);
			else    // if level is not disabled or won, we save the info to the level as pending config and it will be updated when it is disabled or won
			{
				if (currentInfo == info)
				{
					if (existingLevel->DeletePendingInfo_AssumeLocked(batch))
					{
						TLOG(LogJackpotService, Info, "Deleting pending config for jackpot level %s with ID %s", info.Name.c_str(), info.ID.c_str())
						status = ESaveJackpotLevelResult::DeletedPending;
					}
				}
				else
				{
					existingLevel->SavePendingInfo_AssumeLocked(batch, info);
					TLOG(LogJackpotService, Info, "Saving pending config for jackpot level %s with ID %s", info.Name.c_str(), info.ID.c_str())
					status = ESaveJackpotLevelResult::SavedPending;
				}
			}
		});

		if (status == ESaveJackpotLevelResult::Updated)
		{
			if (auto task = mJackpotUpdateBroadcastTask.lock())
				task->EnableAndExecuteImmediate();

			if (auto task = mJackpotBackupTask.lock())
				task->EnableAndExecuteImmediate();
		}
	}

	return { info, status };
}

CreateLevelFromTemplateRespDto JackpotService::createLevelFromTemplate(const CreateLevelFromTemplateReqDto& req)
{
	if (req.TemplateID.empty())
		throw BadRequestError("templateId must not be empty");

	// get existing template; if not found, throw
	if (!mLevelTemplateService)
		throw BadRequestError("Level template service is not available");
	const auto levelInfo = mLevelTemplateService->GetLevelTemplate(req.TemplateID, true).value();

	std::vector<JackpotLevelInfoBase> createdLevels;

	for (const auto& templateLevel : req.Levels)
	{
		// make a copy of level
		auto level = levelInfo;

		// update level info
		level.ID.clear();    // clear ID to create a new level
		level.TemplateID = req.TemplateID;    // set template ID (this was under ID before)
		level.Name = templateLevel.LevelName;    // set new level name
		if (!templateLevel.ClientGroupID.empty())
			level.ClientGroupID = templateLevel.ClientGroupID;    // set new client group ID if not empty. If empty, the one from template will be used
		level.LevelReference = templateLevel.LevelReference.empty() ?
		                         level.ClientGroupID :
		                         templateLevel.LevelReference;    // set new level reference if not empty. If empty, the client group ID will be used
		level.bDemo = templateLevel.bDemo;    // set new demo status

		// save new level
		const auto resp = saveJackpotLevel({ level });

		// add created level to response
		createdLevels.push_back(resp.LevelInfo);
	}

	return CreateLevelFromTemplateRespDto(createdLevels);
}

/** It assumes locked 'level->Values' */
ESaveJackpotLevelResult JackpotService::UpdateJackpotLevel_AssumeLocked(rocksdb::WriteBatch& batch, const std::shared_ptr<TJackpotLevel>& existingLevel,
                                                                        const JackpotLevelInfoBase& newInfo)
{
	TLOG(LogJackpotService, Info, "Updating jackpot level %s", existingLevel->ID.c_str())

	// delete pending info if it exists (just in case, should not happen though)
	existingLevel->DeletePendingInfo_AssumeLocked(batch);

	const ESaveJackpotLevelResult status = existingLevel->UpdateInfo_AssumeLocked(newInfo);

	if (status != ESaveJackpotLevelResult::NoChange)
	{
		// save updated level to DB
		JackpotDao::SaveJackpotLevelConfig(batch, newInfo);
		existingLevel->OnReconfigured_AssumeLocked(batch);
		broadcastJackpotLevelReconfigured(*existingLevel, newInfo);
		TLOG(LogJackpotService, Info, "Level %s updated", existingLevel->ID.c_str())
	}
	else
	{
		TLOG(LogJackpotService, Info, "No change in level %s config", existingLevel->ID.c_str())
	}

	return status;
}

/**
 * @brief Delete jackpot level
 *
 * @param req The request instance (see DeleteJackpotLevelReqDto)
 */
DeleteJackpotLevelRespDto JackpotService::deleteJackpotLevel(const DeleteJackpotLevelReqDto& req)
{
	auto resp = DeleteJackpotLevelRespDto(req.LevelID);

	// first check if jackpot level exists (we write lock mJackpotLevels as we might delete the level)
	ScopedLock lock(mJackpotLevels);
	const auto level = GetJackpotLevel_AssumeReadLocked(req.LevelID, true);

	mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &req, &resp](rocksdb::WriteBatch& batch) -> void {
		// first we disable the level if it is not already disabled
		const auto toggleLevelResp = ToggleJackpotLevel_AssumeWriteLocked(batch, level, false);
		if (toggleLevelResp.Status != EJackpotLevelStatus::Disabled && !toggleLevelResp.bPendingDisable)
			throw BadRequestError("Got into a weird state that should not happen. Level is not disabled and not pending disable!");

		if (toggleLevelResp.Status == EJackpotLevelStatus::Disabled && level->CountPendingBets_AssumeLocked() == 0)
			resp.Status = ArchiveJackpotLevel_AssumeWriteLocked(batch, level);
		else
		{
			level->SetPendingArchive_AssumeWriteLock(batch, true);
			resp.Status = EJackpotLevelArchivedStatus::PendingArchived;
			Log(Info, std::format("Set level {} to pending archive.", req.LevelID).c_str());
		}
	});

	lock.unlock();

	if (resp.Status != EJackpotLevelArchivedStatus::PendingArchived)    // archived or deleted
		if (auto task = mJackpotBackupTask.lock())
			task->EnableAndExecuteImmediate();

	return resp;
}

EJackpotLevelArchivedStatus JackpotService::ArchiveJackpotLevel_AssumeWriteLocked(rocksdb::WriteBatch& batch, std::shared_ptr<TJackpotLevel> level)
{
	if (!level)
		throw NotFoundError("Jackpot level not defined");

	// send all clients msg that participation in level was removed for them
	const JackpotLevelUpdatedEventDto event(level->GetJackpotLevel_AssumeLocked(), EJackpotLevelStatus::Disabled);
	mClientManager->Multicast(level->GetEnabledClientIds_AssumeLocked(), std::nullopt, *mJackpotLevelUpdatedEvent, event);

	const auto levelId = level->GetInfo_AssumeLocked().ID;
	EJackpotLevelArchivedStatus status = EJackpotLevelArchivedStatus::Deleted;

	if (level->IsRamClear_AssumeLocked())    // if level is disabled and ramclear, we can safely delete it
	{
		// delete level from DB
		JackpotDao::DeleteJackpotLevelConfig(batch, levelId);
		// delete everything from DB for this level
		mJackpotDao->CleanLevelData(levelId, false);

		status = EJackpotLevelArchivedStatus::Deleted;
		Log(Info, "Deleted jackpot level %s", levelId.c_str());
	}
	else    // if level is disabled but not ramclear, we archive it
	{
		// save to archive table and delete from main table
		JackpotDao::SaveArchivedJackpotLevel(batch, level->GetListLevelData_AssumeLocked());
		JackpotDao::DeleteJackpotLevelConfig(batch, levelId);

		// TODO: should we also delete any data related to this level from DB?
		//  With SaveArchivedJackpotLevel we already store info, values and counters, but not bets, wins,...

		status = EJackpotLevelArchivedStatus::Archived;
		Log(Info, "Archived jackpot level %s", levelId.c_str());
	}

	ClearJackpotLevel_AssumeWriteLocked(levelId);

	return status;
}

/**
 * @brief Toggle jackpot level
 *
 * @param req The request instance (see ToggleJackpotLevelReqDto)
 */
ToggleJackpotLevelRespDto JackpotService::toggleJackpotLevel(const ToggleJackpotLevelReqDto& req)
{
	// checks if level exists and if jackpot is already won
	auto level = GetJackpotLevel(req.LevelID, true);

	auto resp = ToggleJackpotLevelRespDto(req.LevelID);

	mJackpotDao->WorkOnJackpotLevel(
	  level, [this, &level, &req, &resp](rocksdb::WriteBatch& batch) -> void { resp = ToggleJackpotLevel_AssumeWriteLocked(batch, level, req.bEnabled); });

	if (const auto task = mJackpotUpdateBroadcastTask.lock())
		task->EnableAndExecuteImmediate();

	if (auto task = mJackpotBackupTask.lock())
		task->EnableAndExecuteImmediate();

	return resp;
}

ToggleJackpotLevelRespDto JackpotService::ToggleJackpotLevel_AssumeWriteLocked(rocksdb::WriteBatch& batch, std::shared_ptr<TJackpotLevel> level, const bool bEnabled)
{
	if (!level)
		throw NotFoundError("Jackpot level not defined");

	const auto status = level->Status_AssumeLocked();
	const auto levelInfo = level->GetInfo_AssumeLocked();

	auto resp = ToggleJackpotLevelRespDto(levelInfo.ID, status);

	if (bEnabled == (status != EJackpotLevelStatus::Disabled))
		return resp;    // no need to disable if it is already disabled or enable if it is not disabled

	if (bEnabled && mJackpotLockService)
		if (const auto lockStatus = mJackpotLockService->GetLockStatus())
			throw BadRequestError(std::format("Can't enable jackpot level when jackpot lock is in state: {}", *lockStatus));

	if (bEnabled && levelInfo.bUseFixedPaidIn && levelInfo.ExternalTriggerTypeData.empty())
		throw BadRequestError("Can't enable a jackpot level with 'useFixedPaidIn=true' with empty 'externalTriggerTypeData' map!");

	if (bEnabled && levelInfo.Type == EJackpotLevelType::External && levelInfo.ExternalTriggerTypeData.empty())
		throw BadRequestError("Can't enable 'External' jackpot level with empty 'externalTriggerTypeData' map!");

	if (status == EJackpotLevelStatus::Won || status == EJackpotLevelStatus::PendingWin)
	{
		Log(Info, std::format("Can't disable a Won/PendingWin level {}. Setting level to pending disable instead.", levelInfo.ID).c_str());
		level->SetPendingDisable_AssumeWriteLock(batch, true);
		resp.bPendingDisable = true;
		return resp;
	}

	// All checks have passed. Toggle jackpot level
	resp.Status = bEnabled ? EJackpotLevelStatus::Counting : EJackpotLevelStatus::Disabled;
	level->UpdateJackpotStatus_AssumeLocked(batch, resp.Status);

	// if we disabled the level, we need to check if we need to update it if we have a pending config state (normally we already do this after win)
	if (!bEnabled)
	{
		if (const auto pendingInfo = level->GetPendingInfo_AssumeLocked())
			UpdateJackpotLevel_AssumeLocked(batch, level, pendingInfo.value());
	}

	return resp;
}

/**
 * @brief List jackpot levels (through WS or HTTP)
 *
 * @param client The client instance
 * @param req The request instance (see ListJackpotLevelsReqDto)
 * @return
 */
ListJackpotLevelsRespDtoAdmin JackpotService::listJackpotLevels(const JackpotClientConfig& client, const ListJackpotLevelsReqDto& req)
{
	// list levels from this server instance
	std::vector<ListLevelDataAdminDto> levels;
	const bool bShowForAllClientGroups = req.ClientGroupIDs.contains(JACKPOT_ZERO_CLIENT_GROUP_ID);

	SharedScopedLock lock(mJackpotLevels);
	for (const auto& level : &mJackpotLevels | std::views::values)
	{
		SharedScopedLock lock2(*level);
		const auto info = level->GetInfo_AssumeLocked();

		if (!bShowForAllClientGroups && !req.ClientGroupIDs.contains(info.ClientGroupID))
			continue;

		if (!req.LevelIDs.empty() && !req.LevelIDs.contains(info.ID))    // if jackpotLevelIDs is empty, we append all levels
			continue;

		if (!req.bIncludeDisabled && level->Status_AssumeLocked() == EJackpotLevelStatus::Disabled)    // if bIncludeDisabled is false, we don't append disabled levels
			continue;

		if (const auto templateId = info.TemplateID; !req.TemplateIDs.empty() && (!templateId || !req.TemplateIDs.contains(*templateId)))
			continue;

		if (req.bDemo.has_value() && info.bDemo != *req.bDemo)
			continue;

		ListLevelDataAdminDto listLevelData = level->GetListLevelDataAdmin_AssumeLocked();

		// add online clients count
		if (const auto enabledClientIds = level->GetEnabledClientIds(); !enabledClientIds.empty())
		{
			const auto connectedClients =
			  mClientManager->ListClientInfo({ .ClientIDs = enabledClientIds, .OnlyConnected = true, .ClientTypes = { EJackpotClientType::Client } });

			std::unordered_set<std::string> connectedClientIds;
			for (const auto& clientInfo : connectedClients) connectedClientIds.emplace(clientInfo.ClientID);

			listLevelData.OnlineClientIDs = std::move(connectedClientIds);
		}

		if (!client.ClientId.empty())
			listLevelData.ClientStatus = level->GetClientStatus(client.ClientId);

		levels.emplace_back(std::move(listLevelData));
	}
	lock.unlock();

	// add levels from parent
	if (mParent && mParent->IsConnected())
	{
		// make a copy of the request, because we need to send it to parent with additional client config data
		ListJackpotLevelsReqDto parentReq = req;
		parentReq.ClientConfig = client;

		auto resp = mParent->ListJackpotLevels(parentReq).get();
		if (resp.IsOK())
		{
			auto parentLevels = resp.Value()->Levels;
			levels.insert(levels.end(), parentLevels.begin(), parentLevels.end());
		}
		else
			TLOG(LogJackpotService, Warning, "Failed to get jackpot levels from parent, using only local levels: %s", resp.Error()->what())
	}

	ListJackpotLevelsRespDtoAdmin resp;
	resp.Levels = std::move(levels);
	resp.TimezoneOffset = ytime::GetUTCOffsetSeconds() / 60;

	return resp;
}

ListArchivedLevelsRespDto JackpotService::listArchivedLevels(const ListArchivedLevelsReqDto& req)
{
	// listing archived levels will be rare so we don't need to cache them in memory
	return { mJackpotDao->ListArchivedJackpotLevels(req.LevelIDs) };
}

/**
 * @brief List jackpot history (through WS or HTTP)
 * @param req The request instance (see ListJackpotHistoryReqDto)
 * @return
 */
ListJackpotHistoryRespDto JackpotService::listJackpotHistory(const ListJackpotHistoryReqDto& req)
{
	// get local levels
	std::list<ListLevelHistoryDto> levels;
	const bool bShowForAllClientGroups = req.ClientGroupIDs.contains(JACKPOT_ZERO_CLIENT_GROUP_ID);

	SharedScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		SharedScopedLock lock2(*level);
		if (const auto levelInfo = level->GetInfo_AssumeLocked();
		    (bShowForAllClientGroups || req.ClientGroupIDs.contains(levelInfo.ClientGroupID)) &&
		    (req.LevelIDs.empty() || req.LevelIDs.contains(level->ID))    // if jackpotLevelIDs is empty, we append all levels
		    &&
		    (req.bIncludeDisabled || level->Status_AssumeLocked() != EJackpotLevelStatus::Disabled))    // if bIncludeDisabled is false, we don't append disabled levels
		{
			const auto history = req.bOnlyTop ? level->GetTopHistory_AssumeLocked(req.Limit) : level->GetHistory_AssumeLocked(req.Limit, req.Filter);
			levels.emplace_back(ListLevelHistoryDto(level->ID, levelInfo.Name, history));
		}
	}
	lock.unlock();

	// add levels from parent
	if (mParent && mParent->IsConnected())
	{
		auto resp = mParent->ListJackpotHistory(req).get();
		if (resp.IsOK())
		{
			const auto& parentLevels = resp.Value()->Levels;
			levels.insert(levels.end(), parentLevels.begin(), parentLevels.end());
		}
		else
			TLOG(LogJackpotService, Warning, "Failed to get jackpot history from parent, using only local history: %s", resp.Error()->what())
	}

	return ListJackpotHistoryRespDto(levels);
}

ListClientInfoRespDto JackpotService::listClientInfo(const ListClientInfoReqDto& req) const
{
	auto clients = mClientManager->ListClientInfo(req.Filter);

	SharedScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		SharedScopedLock lock2(*level);
		level->ExecuteOnReadClientsLock([&clients, &levelId, &level]() {
			for (auto& client : clients)
			{
				if (client.ClientGroupIDs.contains(level->GetInfo_AssumeLocked().ClientGroupID))
					client.ClientLevelsStatus.emplace(levelId, level->GetClientStatus_AssumeLocked(client.ClientID));
			}
		});
	}
	lock.unlock();

	// TODO: currently clients[].ClientLevelsStatus is not filled for parent levels, we need to add it if needed

	return clients;
}

ListBetsRespDto JackpotService::listBets(const ListBetsReqDto& req) const
{
	auto filter = req.Filter;    // make a copy to modify it
	const bool bPendingBets =
	  filter.Statuses.empty() || filter.Statuses.contains(EJackpotBetStatus::PendingResult) || filter.Statuses.contains(EJackpotBetStatus::WonPendingConfirmation);
	const bool bFinalBets = filter.Statuses.empty() || filter.Statuses.contains(EJackpotBetStatus::Lost) || filter.Statuses.contains(EJackpotBetStatus::Won);

	if (req.LevelID.empty())
		throw BadRequestError("Missing required member 'level-id'");

	if (filter.ToTimeMs == 0)
		filter.ToTimeMs = ytime::GetSystemTimeMsec();
	// if we want only pending bets, we don't need to limit time (we allow all pending bets to be listed)
	if (bFinalBets)
	{
		if (filter.FromTimeMs == 0)
			throw BadRequestError("Missing required member 'from-time'");
		if (filter.ToTimeMs > filter.FromTimeMs + 1000 * 60 * 60 * 24)
			throw BadRequestError("'to-time' should be within 24 hours after 'from-time' when requesting non-pending bets");
	}

	const auto level = GetJackpotLevel(req.LevelID, true);

	// get bets
	auto bets = bFinalBets ? mJackpotDao->ListClientBets(req.LevelID, filter) : std::list<BetDto>();

	// get pending bets
	if (bPendingBets)
	{
		const auto pendingBets = level->ListPendingBets(filter);
		// add pending bets to the list (on beginning) // TODO if needed, add sorting later
		bets.insert(bets.begin(), std::make_move_iterator(pendingBets.begin()), std::make_move_iterator(pendingBets.end()));
	}

	return ListBetsRespDto(bets, bets.size());
}

ListBetAggregationsRespDto JackpotService::listBetAggregations(const ListBetAggregationsReqDto& req) const
{
	std::list<BetAggregationLevelData> data;

	constexpr AggPeriod aggPeriod = AggPeriod::Min15;
	const uint64_t interval = ytime::GetIntervalForAggPeriod(aggPeriod);
	const auto [from, to] = ytime::GetTimeRangeForAggPeriod(aggPeriod, req.FromTimeMs);
	const auto initialFromTime = req.FromTimeMs == from ? from : to;

	std::set<uint64_t> expectedFromTimes;
	for (uint64_t fromTime = initialFromTime; fromTime <= req.ToTimeMs - interval; fromTime += interval) expectedFromTimes.emplace(fromTime);

	for (const auto& levelId : req.LevelIDs)
	{
		// skip empty levelId
		if (levelId.empty())
			continue;

		std::list<BetAggregationDto> betAggregations;

		// get data from DB
		auto betAggregationsMap = mJackpotDao->ListBetAggregations(levelId, aggPeriod, req.FromTimeMs, req.ToTimeMs, req.ClientIDs, req.ExternalTriggerTypes);

		// check if we have the first fromTime in the map, if not, we get the previous entry in DB and use initialPot and initialBackPot from there for the first entry
		if (!betAggregationsMap.contains(initialFromTime))
		{
			if (const auto prevBetAggregation = mJackpotDao->GetPreviousBetAggregation(levelId, aggPeriod, initialFromTime))
				betAggregationsMap.emplace(
				  initialFromTime, BetAggregationDto(CountersDto(), aggPeriod, initialFromTime, prevBetAggregation->InitialPot, prevBetAggregation->InitialBackPot,
				                                     prevBetAggregation->FinalPot, prevBetAggregation->FinalBackPot, {}));
			else
				betAggregationsMap.emplace(initialFromTime, BetAggregationDto(CountersDto(), aggPeriod, initialFromTime, 0, 0, 0, 0, {}));
		}

		BetAggregationDto previousAggregation;
		// go through all expected fromTimes and add the data to the list. If entry in betAggregationsMap is missing, we add an empty entry using finalPot and
		// finalBackpot from previous entry
		for (const auto& fromTime : expectedFromTimes)
		{
			const auto it = betAggregationsMap.find(fromTime);
			const auto current = it != betAggregationsMap.end() ?
			                       it->second :
			                       BetAggregationDto(CountersDto(), aggPeriod, fromTime, previousAggregation.FinalPot, previousAggregation.FinalBackPot,
			                                         previousAggregation.FinalPot, previousAggregation.FinalBackPot, {});
			betAggregations.emplace_back(current);
			previousAggregation = current;
		}

		data.emplace_back(levelId, std::move(betAggregations));
	}

	return ListBetAggregationsRespDto(std::move(data));
}

ListClientGroupsRespDto JackpotService::listClientGroups(const ListClientGroupsReqDto& req) const
{
	std::unordered_set<std::string> clientGroupIds;

	// get all client group ids from all levels and store them to set (to remove duplicates)
	SharedScopedLock lock(mJackpotLevels);
	for (const auto& level : &mJackpotLevels | std::views::values)
	{
		SharedScopedLock lock2(*level);
		clientGroupIds.emplace(level->GetInfo_AssumeLocked().ClientGroupID);
	}
	lock.unlock();

	// convert to ClientGroupDtos and filter using req.Filter
	std::list<ClientGroupDto> clientGroups;
	for (const auto& clientGroupId : clientGroupIds)
	{
		if (const auto clientGroup = ClientGroupDto(clientGroupId); clientGroup.Match(req.Filter))
			clientGroups.emplace_back(clientGroup);
	}

	return ListClientGroupsRespDto(clientGroups, clientGroups.size());
}

/**
 * @brief Set jackpot level value
 *
 * @param req The pot value to set for desired jackpot level
 */
SetJackpotLevelValueRespDto JackpotService::setJackpotLevelValue(const SetJackpotLevelValueReqDto& req)
{
	auto level = GetJackpotLevel(req.LevelID, true);

	double potChangedBy = 0;
	double backPotChangedBy = 0;

	mJackpotDao->WorkOnJackpotLevel(level, [&level, &req, &potChangedBy, &backPotChangedBy](rocksdb::WriteBatch& batch) -> void {
		const auto levelInfo = level->GetInfo_AssumeLocked();

		if (req.Pot < 0)
			throw NotAcceptableError("Could not reload jackpot to value - newPot must be greater or equal 0");

		if (levelInfo.MaxPot != 0 && req.Pot > levelInfo.MaxPot)
			throw NotAcceptableError("Could not reload jackpot to value - newPot must be less than or equal to maxPot");

		if (level->Status_AssumeLocked() != EJackpotLevelStatus::Counting && level->Status_AssumeLocked() != EJackpotLevelStatus::Disabled)
			throw BadRequestError("Could not reload jackpot to value - jackpot is not in Counting or Disabled state");

		// returned is the amount the pot and backpot were changed by (can be positive or negative)
		std::tie(potChangedBy, backPotChangedBy) = level->ReloadToValue_AssumeLocked(batch, req.Pot, req.BackPot);
		level->UpdateUserAddedCounter_AssumeLocked(batch, potChangedBy + backPotChangedBy);
	});

	if (const auto task = mJackpotUpdateBroadcastTask.lock())
		task->EnableAndExecuteImmediate();

	if (const auto task = mJackpotBackupTask.lock())
		task->EnableAndExecuteImmediate();

	return SetJackpotLevelValueRespDto(req.LevelID, potChangedBy, backPotChangedBy);
}

/**
 * @brief Reset jackpot level. Jackpot is reset only if it is in win state.
 *
 * @param req The jackpot reset request instance (see JackpotResetReqDto)
 */
void JackpotService::jackpotReset(const JackpotResetReqDto& req)
{
	auto level = GetJackpotLevel(req.LevelID, true);

	mJackpotDao->WorkOnJackpotLevel(level, [&level](rocksdb::WriteBatch& batch) -> void { level->Reset_AssumeLocked(batch); });

	if (const auto task = mJackpotUpdateBroadcastTask.lock())
		task->EnableAndExecuteImmediate();

	if (const auto task = mJackpotBackupTask.lock())
		task->EnableAndExecuteImmediate();
}

void JackpotService::setClientEnabledLevels(const JackpotClientConfig& clientConfig, const std::unordered_set<std::string>& levelIds, bool bAddLevels)
{
	std::unordered_set<std::string> parentLevels;
	std::list<std::shared_ptr<TJackpotLevel>> localLevels;

	const auto clientGroupIds = mClientManager->GetClientGroupIds(clientConfig.ClientId);

	/** VALIDATION AND SPLIT LOCAL/PARENT LEVELS */
	for (const auto& levelId : levelIds)
	{
		if (const auto level = GetJackpotLevel(levelId, false))    // local level
		{
			if (bAddLevels && !clientGroupIds.contains(level->GetInfo().ClientGroupID))
				throw BadRequestError(std::format("Jackpot level {} is not enabled for any of client groups [{}]", levelId, yutils::Join(clientGroupIds, ",")));
			if (bAddLevels != level->IsLevelEnabledForClient(clientConfig.ClientId))
				localLevels.emplace_back(level);
		}
		else if (mParent && mParent->IsConnected())    // parent level
			parentLevels.emplace(levelId);
		else    // if parent is not connected and level is not local, we throw error
			throw NotFoundError(std::format("No jackpot level found with id {}", levelId));
	}

	/** PARENT PROCESSING */
	// if we have parent levels, we send the request to parent (we do this before local processing, because we can get error from parent and not update locally)
	if (!parentLevels.empty())
	{
		// compose and make a request to parent
		auto parentReq = SetClientEnabledLevelsReqDto(parentLevels);
		parentReq.ClientConfig = clientConfig;
		auto resp = bAddLevels ? mParent->AddClientEnabledLevels(parentReq).get() : mParent->RemoveClientEnabledLevels(parentReq).get();

		if (!resp.IsOK())
			throw resp.Error().value();
	}

	/** LOCAL PROCESSING */
	SharedScopedLock lock(mJackpotLevels);

	std::unordered_set<std::string> localLevelIds;
	for (const auto& level : localLevels)
	{
		mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &clientConfig, bAddLevels](rocksdb::WriteBatch& batch) -> void {
			// add/remove the client where req originated from (e.g. live table) to the level
			if (bAddLevels)
				level->AddClient(batch, clientConfig.ClientId);
			else
				level->RemoveClient(batch, clientConfig.ClientId);

			emitJackpotLevelUpdated_AssumeReadOnlyLocked(*level, clientConfig.ClientId);
		});
		localLevelIds.emplace(level->ID);
	}
	lock.unlock();

	if (!localLevels.empty())
		TLOG(LogJackpotService, Info, "Level(s) %s %s for client %s", yutils::Join(localLevelIds, ", ").c_str(), bAddLevels ? "enabled" : "disabled",
		     clientConfig.ClientId.c_str())
}

void JackpotService::saveClientGroupIds(const SaveClientGroupIdsReqDto& req, const bool bIndirectClient) const
{
	if (mParent && mParent->IsConnected())
	{
		if (const auto resp = mParent->SaveClientGroupIds(req).get(); !resp.IsOK())
			throw resp.Error().value();
	}

	mClientManager->SaveClientGroupIds(req.ClientID, req.ClientGroupIDs, bIndirectClient);
}

bool JackpotService::ramClear(const RamClearReqDto& req)
{
	const auto singleLevel = req.LevelID.empty() ? nullptr : GetJackpotLevel(req.LevelID, true);

	// check if requested levels are disabled
	if (!req.bForce)
	{
		if (!singleLevel && !checkIfAllLevelsDisabled())
			throw BadRequestError("Can't clear RAM when there are any active levels!");
		if (singleLevel && singleLevel->Status() != EJackpotLevelStatus::Disabled)
			throw BadRequestError("Can't clear RAM if level is not Disabled!");
	}

	if (req.Type == ERamClearType::FactoryReset)
	{
		if (!req.LevelID.empty())
			throw BadRequestError("Factory reset is not allowed for single level!");

		// delete DB folder
		const auto dbPath = mJackpotDao->GetJackpotDbFolder();
		std::error_code ec;
		std::filesystem::remove_all(dbPath, ec);
		if (ec)
			TLOG(LogJackpotService, Error, "Failed to delete jackpot DB folder %s: %s", dbPath.c_str(), ec.message().c_str())
		else
			TLOG(LogJackpotService, Warning, "Cleared all jackpot DB data (factory reset) on path %s", dbPath.c_str())

		// restart server so that all stuff in memory is cleared/synced with DB
		pApp->Defer([]() -> void { pApp->terminate(ESystemActionOnExit::RestartApp, true); }, "jp-ram-clear");

		return true;
	}

	if (req.Type == ERamClearType::Full)
	{
		// check if ram is already clear
		if (singleLevel ? singleLevel->IsRamClear() : isRamClear())
		{
			TLOG(LogJackpotService, Info, "Not clearing anything as RAM is already clear.")
			return false;
		}

		RamClearDto ramClear;

		if (singleLevel)
		{
			ramClear.LevelsData.emplace_back(RamClearLevel(singleLevel));
			LoadJackpotLevel(singleLevel->ID, true);
		}
		else
		{
			ScopedLock lock(mJackpotLevels);
			for (const auto& [levelId, level] : &mJackpotLevels)
			{
				if (!level->IsRamClear())
					ramClear.LevelsData.emplace_back(RamClearLevel(level));
				else
					TLOG(LogJackpotService, Info, "Not clearing anything as RAM is already clear for level %s.", levelId.c_str())
			}
			LoadJackpot_AssumeLocked(true);
		}

		// store current counters from memory to DB
		mJackpotDao->SaveRamClearHistory(ramClear);

		return true;
	}

	return false;
}

RamClearLevelData JackpotService::RamClearLevel(std::shared_ptr<TJackpotLevel> level)
{
	const auto ramClearLevelData = RamClearLevelData(level->ID, level->GetCounterValues());

	// set all enabled clients statuses to Counting
	mJackpotDao->WorkOnJackpotLevel(level, [&level](rocksdb::WriteBatch& batch) -> void {
		for (const auto clientIds = level->GetEnabledClientIds_AssumeLocked(); const auto& clientId : clientIds)
			level->SetClientStatus_AssumeLocked(batch, clientId, EJackpotLevelStatus::Counting, 0);
	});

	mJackpotDao->CleanLevelData(level->ID, true);
	TLOG(LogJackpotService, Warning, "Cleared all non-config data (full ram clear) for level: %s", level->ID.c_str())

	mJackpotDao->SaveCountersSinceTS(level->ID, ytime::GetSystemTimeMsec());

	return ramClearLevelData;
}

ClearPendingBetsRespDto JackpotService::clearPendingBets(const ClearPendingBetsReqDto& req) const
{
	const auto level = GetJackpotLevel(req.LevelID, true);

	auto resp = ClearPendingBetsRespDto(req.LevelID, {});

	mJackpotDao->WorkOnJackpotLevel(level, [&level, &req, &resp](rocksdb::WriteBatch& batch) -> void {
		const auto pendingWinBets = level->ListPendingBets_AssumeLocked(BetFilter(req.ClientID, { EJackpotBetStatus::WonPendingConfirmation }));
		if (!pendingWinBets.empty())
		{
			std::optional<JackpotHistoryDto> jackpotWonPendingOpt = level->GetPendingWinForClient_AssumeLocked(req.ClientID);
			if (!jackpotWonPendingOpt)
				throw BadRequestError(std::format("No pending win for client {} with {} pending bets", req.ClientID, pendingWinBets.size()));

			const auto jackpotWonPending = jackpotWonPendingOpt.value();
			if (jackpotWonPending.Timestamp > ytime::GetSystemTimeMsec() - 1000 * 60 * 5)
				throw BadRequestError("Can't clear pending bets when pending win is less than 5 minutes old!");

			level->ConfirmWin_External_AssumeLocked(batch, req.ClientID, false, {}, jackpotWonPending);
			TLOG(LogJackpotService, Info, std::format("Cleared {} pending win bets for client {} in level {}", pendingWinBets.size(), req.ClientID, req.LevelID).c_str())
		}

		const auto pendingResultBets = level->ListPendingBets_AssumeLocked(BetFilter(req.ClientID, { EJackpotBetStatus::PendingResult }));
		if (!pendingResultBets.empty())
		{
			for (const auto& bet : pendingResultBets)
				if (bet.BetTimeMs > ytime::GetSystemTimeMsec() - 1000 * 60 * 5)
					throw BadRequestError("Can't clear pending bets when any pending bet is less than 5 minutes old!");

			const auto [jackpotWon, amountWon, bets] = level->Gamble_External_AssumeLocked(batch, req.ClientID, false, true);
			for (const auto& bet : bets) resp.BetsProcessed.emplace_back(bet.ToBetProcessed());

			TLOG(LogJackpotService, Info,
			     std::format("Cleared {} pending result bets for client {} in level {}", pendingResultBets.size(), req.ClientID, req.LevelID).c_str())
		}
	});


	return resp;
}

void JackpotService::addExternalTriggerType(const AddExternalTriggerTypeReqDto& req)
{
	const auto existingLevel = GetJackpotLevel(req.LevelID, true);

	mJackpotDao->WorkOnJackpotLevel(existingLevel, [this, &existingLevel, &req](rocksdb::WriteBatch& batch) -> void {
		auto levelInfo = existingLevel->GetInfo_AssumeLocked();
		auto externalTriggerTypes = req.ExternalTriggerTypeData;    // make a copy so that we can modify it
		if (externalTriggerTypes.size() == 0)
			return;

		// validate external trigger data
		jackpotutils::ValidateTriggerTypeData(levelInfo, externalTriggerTypes);

		// save new external trigger types with the existing level
		for (auto& [triggerDataName, triggerData] : externalTriggerTypes) levelInfo.ExternalTriggerTypeData.emplace(triggerDataName, triggerData);
		existingLevel->UpdateInfo_AssumeLocked(levelInfo);

		// save updated level to DB
		mJackpotDao->SaveJackpotLevelConfig(batch, levelInfo);

		// TODO: should we broadcast this (we only added new external trigger types and it does not affect any live setting)?
		broadcastJackpotLevelReconfigured(*existingLevel, levelInfo);
	});
}

bool JackpotService::isRamClear() const
{
	SharedScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		if (!level->IsRamClear())
			return false;
	}
	return true;
}

std::unordered_set<std::string> JackpotService::disableAllLevels()
{
	std::unordered_set<std::string> disabledLevels;
	ScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &disabledLevels](rocksdb::WriteBatch& batch) -> void {
			if (level->Status_AssumeLocked() != EJackpotLevelStatus::Disabled)
			{
				ToggleJackpotLevel_AssumeWriteLocked(batch, level, false);
				disabledLevels.emplace(level->ID);
			}
		});
	}
	lock.unlock();

	if (const auto task = mJackpotUpdateBroadcastTask.lock())
		task->EnableAndExecuteImmediate();

	return disabledLevels;
}

void JackpotService::enableLevels(const std::unordered_set<std::string>& levelIds)
{
	ScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		if (!levelIds.contains(levelId))
			continue;

		mJackpotDao->WorkOnJackpotLevel(level, [this, &level](rocksdb::WriteBatch& batch) -> void {
			if (level->Status_AssumeLocked() == EJackpotLevelStatus::Disabled)
				ToggleJackpotLevel_AssumeWriteLocked(batch, level, true);
		});
	}

	if (const auto task = mJackpotUpdateBroadcastTask.lock())
		task->EnableAndExecuteImmediate();
}

bool JackpotService::checkIfAllLevelsDisabled() const
{
	SharedScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		if (level->Status() != EJackpotLevelStatus::Disabled)
			return false;
	}
	return true;
}

InitRespDto JackpotService::init(const TJackpotServerClient& client, const std::optional<InitReqDto>& req)
{
	auto clientConfig = req.has_value() && req->ClientConfig.has_value() ? req->ClientConfig.value() : client.GetJackpotClientConfig();
	const auto clientId = clientConfig.ClientId;

	if (req)
		mClientManager->SaveClientGroupIds(clientId, req->ClientGroupIDs, true);

	const auto groupIds = req ? req->ClientGroupIDs : mClientManager->GetClientGroupIds(clientId);
	auto clientPath = req.has_value() ? req->Path : std::vector<std::string>();

	// list levels from this server instance
	const bool isServerChildWithAccessToAllLevels = clientConfig.ClientType == EJackpotClientType::ServerChild && groupIds.contains(JACKPOT_ZERO_CLIENT_GROUP_ID);
	std::vector<InitLevelDataDto> levels;
	SharedScopedLock lock(mJackpotLevels);
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		SharedScopedLock lock2(*level);
		const auto levelInfo = level->GetInfo_AssumeLocked();
		const auto enabledClient = level->GetEnabledClient(clientId);
		const auto clientStatus = enabledClient.has_value() ? enabledClient->Status : EJackpotLevelStatus(EJackpotLevelStatus::Disabled);
		if (isServerChildWithAccessToAllLevels || (clientStatus != EJackpotLevelStatus::Disabled && groupIds.contains(levelInfo.ClientGroupID)))
		{
			InitLevelDataDto levelData;
			levelData.LevelID = level->ID;
			levelData.LevelStatus = level->Status_AssumeLocked();
			levelData.LevelCurrency = levelInfo.Currency;
			if (levelInfo.Type == EJackpotLevelType::External)
				levelData.ClientStatus = clientStatus;
			for (const auto& bet : level->ListPendingBets_AssumeLocked(BetFilter(clientId))) levelData.PendingBetsData.emplace_back(bet.ToInitBet());
			levelData.ExpectedWin = clientStatus != EJackpotLevelStatus::Disabled && enabledClient->WinValue > 0 ? std::optional(enabledClient->WinValue) : std::nullopt;
			levels.emplace_back(std::move(levelData));
		}
	}
	lock.unlock();

	// if we are on a non-direct server, add client path
	if (req.has_value())    // init was already forwarded through at least one jps
	{
		clientPath.push_back(client.ID);
		mClientManager->AddClientPath(clientId, clientPath);
	}

	// add levels from parent(s)
	if (mParent && mParent->IsConnected())
	{
		InitReqDto parentReq(groupIds);
		if (!req.has_value())    // if req is undefined, then this is the first request from client
			parentReq.ClientConfig = clientConfig;
		else    // req was already forwarded through at least one jps
		{
			parentReq = req.value();
			parentReq.Path = clientPath;
		}

		auto resp = mParent->InitReq(parentReq).get();
		if (resp.IsOK())
		{
			auto& parentLevels = resp.Value()->LevelsData;
			levels.insert(levels.end(), parentLevels.begin(), parentLevels.end());
		}
		else
			throw resp.Error().value();
	}

	// return init response
	return { levels, clientConfig.Currency, clientConfig.Denomination, VERSION_STRING, clientConfig.ExternalTriggerType };
}

void JackpotService::removeClient(const TJackpotServerClient& client, const std::optional<RequestDto>& req)
{
	const auto& clientConfig = req.has_value() && req->ClientConfig.has_value() ? req->ClientConfig.value() : client.GetJackpotClientConfig();
	auto clientPath = req.has_value() ? req->Path : std::vector<std::string>();

	// remove client locally if local client was removed
	if (req.has_value())
	{
		clientPath.push_back(client.ID);
		mClientManager->RemoveClientPath(clientConfig.ClientId, clientPath);
		mClientManager->SaveClientGroupIds(clientConfig.ClientId, {}, true);
	}
	else
		mClientManager->RemoveClient(clientConfig.ClientId);

	// remove client in parents
	if (mParent && mParent->IsConnected())
	{
		RequestDto parentReq = RequestDto();
		if (!req.has_value())    // if req is undefined, then this is the first request from client
			parentReq.ClientConfig = clientConfig;
		else    // req was already forwarded through at least one jps
		{
			parentReq = req.value();
			parentReq.Path = clientPath;
		}

		auto resp = mParent->DisconnectReq(parentReq).get();
		if (!resp.IsOK())
			throw resp.Error().value();
	}

	// for server child also remove all client paths that contain this server child on path
	if (clientConfig.ClientType == EJackpotClientType::ServerChild)
		mClientManager->RemoveClientPathsWithElement(clientConfig.ClientId);
}

ClientBetsRespDto JackpotService::bet(const JackpotClientConfig& clientConfig, const ClientBetsReqDto& req)
{
	// if there are no bets in request, we do nothing
	if (req.ClientBets.empty())
		return {};

	// validation of bet references and session references - they must be unique
	std::unordered_set<std::string> uniqueBetReferences;
	std::unordered_map<std::string, double> uniqueSessionReferences2PaidIn;
	for (const auto& clientBet : req.ClientBets)
	{
		// if client bet reference is empty, we skip it
		if (!clientBet.BetReference.empty() && !uniqueBetReferences.emplace(clientBet.BetReference).second)
			throw BadRequestError("Bet reference " + clientBet.BetReference + " is not unique!");

		if (!uniqueSessionReferences2PaidIn.emplace(clientBet.SessionReference, 0).second)
			throw BadRequestError("Session reference " + clientBet.SessionReference + " is not unique!");
	}

	// send to parent if connected and then process local bets. We will wait for parent resp at the end
	boost::future<JackpotClientResponse<ClientBetsRespDto>> parentBetRespFuture;
	if (mParent && mParent->IsConnected())
	{
		auto parentReq = req;
		parentReq.ClientConfig = clientConfig;
		parentBetRespFuture = mParent->Bet(parentReq);
	}
	else
		parentBetRespFuture = boost::make_ready_future(JackpotClientResponse<ClientBetsRespDto>(ClientBetsRespDto()));

	const auto clientGroupIds = mClientManager->GetClientGroupIds(clientConfig.ClientId);

	std::list<std::shared_ptr<TJackpotLevel>> levelsToBetOn;
	SharedScopedLock jpLock(mJackpotLevels);
	// first we just validate the levels and get list od levels to bet on
	for (const auto& [levelId, level] : &mJackpotLevels)
	{
		SharedScopedLock lock(*level);
		JackpotLevelInfoBase levelInfo = level->GetInfo_AssumeLocked();
		std::vector<BetDto> pendingBets = level->ListPendingBets_AssumeLocked(BetFilter(clientConfig.ClientId));
		lock.unlock();

		if (!level->IsLevelEnabledForClient(clientConfig.ClientId) || !clientGroupIds.contains(levelInfo.ClientGroupID))
			continue;

		if (!levelInfo.ExternalTriggerTypeData.empty() && !levelInfo.ExternalTriggerTypeData.contains(clientConfig.ExternalTriggerType))
		{
			std::vector<std::string> externalTriggerTypes;
			for (const auto& externalTriggerType : levelInfo.ExternalTriggerTypeData | std::views::keys) externalTriggerTypes.emplace_back(externalTriggerType);
			throw BadRequestError(std::format("Jackpot level {} is not enabled for client's external trigger type '{}'! Should be one of: [{}]", levelId,
			                                  clientConfig.ExternalTriggerType, yutils::Join(externalTriggerTypes, ", ")));
		}

		if (levelInfo.Type == EJackpotLevelType::External && level->GetClientStatus(clientConfig.ClientId) != EJackpotLevelStatus::Counting)
			throw BadRequestError("Client is not in status Counting!");

		for (const auto& pendingBet : pendingBets)
		{
			if (uniqueSessionReferences2PaidIn.contains(pendingBet.SessionReference))
				throw BadRequestError("Session reference " + pendingBet.SessionReference + " already exists among existing pending bets for client!");
		}

		for (auto& clientBet : req.ClientBets)
		{
			// if bet currency is not the same as client currency, we throw error
			// TODO: maybe this check is not needed and we should just skip levels that are in other currencies?
			const ECurrency betCurrency = clientBet.Currency.value_or(clientConfig.Currency);
			if (levelInfo.Currency != betCurrency)
				throw BadRequestError(std::format("Jackpot level {} ({}) is not in the same currency as bet {} ({})", levelId, levelInfo.Currency._to_string(),
				                                  clientBet.BetReference, betCurrency._to_string()));

			// add paidIn amount to session reference
			uniqueSessionReferences2PaidIn[clientBet.SessionReference] += GetPaidInAmount(levelInfo, clientBet.Amount, clientConfig.ExternalTriggerType);
		}

		levelsToBetOn.push_back(level);
	}
	jpLock.unlock();

	// validate uniqueSessionReferences2PaidIn that all paidIn amounts are at most as much as Bet amount
	for (auto& clientBet : req.ClientBets)
	{
		const auto totalPaidIn = uniqueSessionReferences2PaidIn[clientBet.SessionReference];
		if (totalPaidIn > clientBet.Amount)
			throw BadRequestError(
			  std::format("Total paidIn amount {} for session reference {} is greater than bet amount {}!", totalPaidIn, clientBet.SessionReference, clientBet.Amount));
		if (clientBet.PaidInSum.has_value() && std::abs(totalPaidIn - clientBet.PaidInSum.value()) / totalPaidIn > 0.0001)
			throw BadRequestError(std::format("Total paidIn amount {} for bet with session reference {} is different than sum of paidIn amounts {}!", totalPaidIn,
			                                  clientBet.SessionReference, clientBet.PaidInSum.value()));
	}

	double paidInTotal = 0.0;
	const auto betTime = ytime::GetSystemTimeMsec();
	std::vector<LevelDataDto> levels;
	bool bAnyLevelWon = false;

	for (const auto& level : levelsToBetOn)
	{
		mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &clientConfig, &req, &paidInTotal, &levels, &betTime, &bAnyLevelWon](rocksdb::WriteBatch& batch) -> void {
			bool bJackpotWon = false;
			const auto& clientExternalTriggerType = clientConfig.ExternalTriggerType;
			const auto& clientId = clientConfig.ClientId;
			const auto& clientLocation = clientConfig.Location;
			const auto info = level->GetInfo_AssumeLocked();

			std::vector<BetDataDto> betsData;

			for (const auto& clientBet : req.ClientBets)
			{
				const double amount = clientBet.Amount;
				const double denomination = clientBet.Denomination.value_or(clientConfig.Denomination);
				const double paidInAmount = GetPaidInAmount(info, amount, clientExternalTriggerType);

				const auto bet = BetDto(crypto::GenRandUUIDv4(), clientId, level->ID, clientBet.PlayerName, betTime, amount, paidInAmount, denomination,
				                        EJackpotBetStatus::Lost, clientBet.BetReference, clientBet.PlayerReference, clientBet.SessionReference, clientBet.RoundReference,
				                        clientLocation, clientExternalTriggerType);
				const auto resultBetOpt = level->BetAndGamble_AssumeLocked(batch, std::move(bet));
				if (!resultBetOpt)
					continue;

				const BetDto& resultBet = resultBetOpt.value();

				level->SaveBet_AssumeLocked(batch, resultBet);
				level->UpdateBetAndPaidInAmountCounters_AssumeLocked(batch, amount, resultBet.PaidInAmount, clientId, clientConfig.ExternalTriggerType);

				betsData.emplace_back(resultBet.ToBetData());

				paidInTotal += resultBet.PaidInAmount;

				// only in case of Mystery Jackpot. If we have a HotSeat jackpot, we have status PendingWin, and we trigger OnJackpotWon() on task JackpotPotUpdateTask().
				if (resultBet.Status == EJackpotBetStatus::Won)
				{
					bJackpotWon = true;
					bAnyLevelWon = true;

					JackpotHistoryDto entry =
					  JackpotHistoryDto::WonPending(level->ID, resultBet.CreditedAmount, resultBet.ClientID, level->GetCounterValues_AssumeLocked().WinCounter)
					    .ToWon(resultBet);
					level->SaveWonHistory_AssumeLocked(batch, resultBet.ClientID, entry);

					broadcastJackpotWon(*level, info, resultBet.CreditedAmount, clientId, EJackpotLevelStatus::Won, { resultBet }, entry.ID);
					level->UpdateWinAmountCounter_AssumeLocked(batch, resultBet.CreditedAmount, clientId, clientExternalTriggerType);
					level->ResetPot_AssumeLocked(batch, resultBet.CreditedAmount);
				}
			}

			LevelDataDto levelData;
			if (info.Type == EJackpotLevelType::External && !betsData.empty())
			{
				// we need to write down current displayPot because if in parallel there is another win on another live table,
				// and that table is credited and pot/display pot is reset, we still need to credit this client (live table) with the amount it was
				// displayed at the time of bets placed
				const double expectedWin = level->GetValues_AssumeLocked().Pot;
				level->SetClientWinValue(batch, clientId, expectedWin);
				levelData.ExpectedWin = expectedWin;
			}

			levelData.LevelID = level->ID;
			levelData.Status = level->Status_AssumeLocked();
			levelData.BetsData = std::move(betsData);
			levels.emplace_back(std::move(levelData));

			// only possible for Mystery Jackpot at this point
			if (bJackpotWon)
				// if in the future we do anything with the level below OnJackpotWon, we need to set `level = OnJackpotWon()` here since level could be updated and
				// reloaded
				OnJackpotWon_AssumeLocked(batch, level, clientId);
		});
	}

	if (bAnyLevelWon)
		if (const auto task = mJackpotBackupTask.lock())
			task->EnableAndExecuteImmediate();

	// wait for parent response
	// TODO: should we only perform bets in case everything is OK with parent(s)? What if global is OK but next one below has an issue? What do to
	//  with bets in subset of levels only?
	const auto parentBetsResp = parentBetRespFuture.get();
	if (!parentBetsResp.IsOK())
	{
		TLOG(LogJackpotService, Error, "Parent bet failed (betting only from this server and below): %s", parentBetsResp.Error().value().what());
	}
	else
	{
		paidInTotal += parentBetsResp.Value()->PaidIn;
		levels.insert(levels.end(), parentBetsResp.Value()->LevelsData.begin(), parentBetsResp.Value()->LevelsData.end());
	}

	ClientBetsRespDto resp;
	resp.PaidIn = paidInTotal;
	resp.LevelsData = std::move(levels);
	return resp;
}

/** It assumes locked 'level->Values' */
void JackpotService::OnJackpotWon_AssumeLocked(rocksdb::WriteBatch& batch, const std::shared_ptr<TJackpotLevel>& level, const std::string& clientId)
{
	const auto levelInfo = level->GetInfo_AssumeLocked();

	// if level is External, we need to check if there are any remaining won pending bets for clientId
	// if level is not External, we need to check if there are any remaining won pending bets for anyone
	const std::optional<std::string> clientIdOpt = levelInfo.Type == EJackpotLevelType::External ? std::optional(clientId) : std::nullopt;
	size_t numRemainingPendingBets = level->CountPendingBets_AssumeLocked(BetFilter(clientIdOpt, { EJackpotBetStatus::WonPendingConfirmation }));
	if (numRemainingPendingBets == 0)
	{
		if (const auto pendingInfo = level->GetPendingInfo_AssumeLocked())
			UpdateJackpotLevel_AssumeLocked(batch, level, pendingInfo.value());

		// External: if there are no more pending won bets, set client status to Counting
		if (levelInfo.Type == EJackpotLevelType::External)
		{
			// if there are no more pending won bets, set client status to Counting
			level->SetClientStatus(batch, clientId, EJackpotLevelStatus::Counting, 0);
			// if there are no more pending won bets, trigger jackpot reset
			level->Reset_AssumeLocked(batch);
		}
		else
		{
			// try resetting jackpot
			if (levelInfo.AutoActionAfterWin != EActionAfterWin::Nothing)
				SetLevelResetTask_AssumeLocked(level, batch);
		}
	}
}

/** It assumes locked 'level->Values' */
void JackpotService::SetLevelResetTask_AssumeLocked(const std::shared_ptr<TJackpotLevel>& level, rocksdb::WriteBatch& batch)
{
	if (level && !IsLevelResetTaskActive(level->ID))
	{
		const auto delayMs = level->GetInfo_AssumeLocked().AutoActionAfterWinOffsetSec * 1000;

		if (delayMs > 0)    // if delay is in the future (>0) then we set the task
		{
			mJPResetTasks[level->ID] = pApp->Delay(
			  [this, level] {
				  mJackpotDao->WorkOnJackpotLevel(level, [&level](rocksdb::WriteBatch& batch) -> void {
					  auto statusAfterWin = EJackpotLevelStatus::Counting;
					  if (level->GetInfo_AssumeLocked().AutoActionAfterWin == EActionAfterWin::ResetAndDisable || level->IsPendingDisable_AssumeReadLock())
						  statusAfterWin = EJackpotLevelStatus::Disabled;

					  level->Reset_AssumeLocked(batch, statusAfterWin);
				  });
			  },
			  delayMs);
		}
		else    // if delay is 0, we just reset the level (we only use batch here)
		{
			auto statusAfterWin = EJackpotLevelStatus::Counting;
			if (level->GetInfo_AssumeLocked().AutoActionAfterWin == EActionAfterWin::ResetAndDisable || level->IsPendingDisable_AssumeReadLock())
				statusAfterWin = EJackpotLevelStatus::Disabled;

			level->Reset_AssumeLocked(batch, statusAfterWin);
		}
	}
}

void JackpotService::ClearLevelResetTask(const std::string& levelId)
{
	if (mJPResetTasks.contains(levelId))
	{
		if (auto task = mJPResetTasks.at(levelId).lock())
			task->Remove();

		mJPResetTasks.erase(levelId);
	}
}

void JackpotService::ClearAllLevelResetTasks()
{
	for (const auto& [levelId, weakTask] : mJPResetTasks)
	{
		if (auto task = weakTask.lock())
			task->Remove();
	}
	mJPResetTasks.clear();
}

bool JackpotService::IsLevelResetTaskActive(const std::string& levelId) const
{
	const auto it = mJPResetTasks.find(levelId);
	return it != mJPResetTasks.end() && !it->second.expired();
}

ExternalJackpotResultRespDto JackpotService::externalJackpotResult(const JackpotClientConfig& clientConfig, const ExternalJackpotResultReqDto& req)
{
	// if level does not exist, we forward the request to parent (if it does not exist, we throw error)
	const auto level = GetJackpotLevel(req.LevelID, false);
	if (!level)
	{
		if (mParent && mParent->IsConnected())
		{
			auto parentReq = req;
			parentReq.ClientConfig = clientConfig;
			const auto resp = mParent->ExternalJackpotResult(parentReq).get();
			if (resp.IsOK())
				return resp.Value().value();
			else
				throw resp.Error().value();
		}
		else
			throw NotFoundError(std::format("No jackpot level found with id {}", req.LevelID));
	}

	auto resp = ExternalJackpotResultRespDto(req.LevelID);

	mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &clientConfig, &req, &resp](rocksdb::WriteBatch& batch) -> void {
		const auto levelInfo = level->GetInfo_AssumeLocked();

		// validation: level should be External type
		if (levelInfo.Type != EJackpotLevelType::External)
			throw BadRequestError("Jackpot level is not of type External!");

		// validation: level should be enabled for client
		if (!level->IsLevelEnabledForClient(clientConfig.ClientId) || !mClientManager->GetClientGroupIds(clientConfig.ClientId).contains(levelInfo.ClientGroupID))
			throw BadRequestError("Jackpot level is not enabled for client!");

		if (level->GetClientStatus(clientConfig.ClientId) != EJackpotLevelStatus::Counting)
			throw BadRequestError("Client is not in status Counting!");

		if (req.IsWon && level->GetValues_AssumeLocked().Pot < levelInfo.MinPot)
			throw BadRequestError("Jackpot cannot be won because pot is below min pot!");

		if (req.IsWon && req.IsFlagged)
			throw BadRequestError("Jackpot cannot be won and flagged at the same time!");

		const auto& clientID = clientConfig.ClientId;

		const auto [jackpotWon, amountWon, bets] = level->Gamble_External_AssumeLocked(batch, clientID, req.IsWon, req.IsFlagged);

		resp.JackpotStatus = level->Status_AssumeLocked();
		resp.ClientStatus = level->GetClientStatus(clientID);
		for (const auto& bet : bets) resp.BetsProcessed.emplace_back(bet.ToBetProcessed());

		if (jackpotWon)
		{
			const JackpotHistoryDto entry = JackpotHistoryDto::WonPending(level->ID, amountWon, clientID, level->GetCounterValues_AssumeLocked().WinCounter);
			level->SaveWonHistory_AssumeLocked(batch, clientID, entry);

			// the amount won here is not delimited yet, because we don't know how many bets were won in the round. At "confirm" time we sum the delimited amounts from
			// bets and report that one. We could delimit the win here, but with more than 1 bet, there could be a difference with the actual win
			broadcastJackpotWon(*level, levelInfo, amountWon, clientID, EJackpotLevelStatus::PendingWin, {}, entry.ID);
		}

		TLOG(LogJackpotService, Info, "%u bets processed for level %s and client %s with result '%s'", bets.size(), req.LevelID.c_str(), clientID.c_str(),
		     jackpotWon ? "won" : "lost")
	});

	return resp;
}

ConfirmExternalJackpotWinRespDto JackpotService::confirmExternalJackpotWin(const JackpotClientConfig& clientConfig, const ConfirmExternalJackpotWinReqDto& req)
{
	// schema validation that cannot be covered in dto
	if (req.Confirmed && req.BetsWon.empty())
		throw SchemaError(ESchemaErrorCode::VALUE_NOT_VALID, "BetsWon must not be empty if jackpot is confirmed!");

	// if level does not exist, we forward the request to parent (if it does not exist, we throw error)
	const auto level = GetJackpotLevel(req.LevelID, false);
	if (!level)
	{
		if (mParent && mParent->IsConnected())
		{
			auto parentReq = req;
			parentReq.ClientConfig = clientConfig;
			const auto resp = mParent->ConfirmExternalJackpotWin(parentReq).get();
			if (resp.IsOK())
				return resp.Value().value();
			else
				throw resp.Error().value();
		}
		else
			throw NotFoundError(std::format("No jackpot level found with id {}", req.LevelID));
	}

	auto resp = ConfirmExternalJackpotWinRespDto(req.LevelID);
	bool jackpotWonOuter = false;

	mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &clientConfig, &req, &resp, &jackpotWonOuter](rocksdb::WriteBatch& batch) -> void {
		const auto levelInfo = level->GetInfo_AssumeLocked();

		// validation: level should be External type
		if (levelInfo.Type != EJackpotLevelType::External)
			throw BadRequestError("Jackpot level is not of type External!");

		// validation: level should be enabled for client
		if (!level->IsLevelEnabledForClient(clientConfig.ClientId) || !mClientManager->GetClientGroupIds(clientConfig.ClientId).contains(levelInfo.ClientGroupID))
			throw BadRequestError("Jackpot level is not enabled for client!");

		// validation: client status should be PendingWin
		if (level->GetClientStatus(clientConfig.ClientId) != EJackpotLevelStatus::PendingWin)
			throw BadRequestError("Client is not in status PendingWin!");

		const auto clientPendingBets = level->ListPendingBets_AssumeLocked(BetFilter(clientConfig.ClientId, { EJackpotBetStatus::WonPendingConfirmation }));
		if (clientPendingBets.empty())
			throw BadRequestError("Client has no bets in status WonPendingConfirmation!");

		// validation: all bets in req.BetsWon should exist and be in status WonPendingConfirmation
		std::unordered_set<std::string> pendingBetIds;
		for (const auto& bet : clientPendingBets) { pendingBetIds.insert(bet.ID); }
		for (const auto& betId : req.BetsWon)
		{
			if (!pendingBetIds.contains(betId))
				throw BadRequestError("Bet " + betId + " is not in status WonPendingConfirmation or does not exist!");
		}

		// validation: there should be a pending win entry for client
		std::optional<JackpotHistoryDto> jackpotWonPendingOpt = level->GetPendingWinForClient_AssumeLocked(clientConfig.ClientId);
		if (!jackpotWonPendingOpt)
			throw BadRequestError("No pending win for client " + clientConfig.ClientId);

		const auto clientID = clientConfig.ClientId;
		const auto jackpotWonPending = jackpotWonPendingOpt.value();

		auto [jackpotWon, amountWon, bets] = level->ConfirmWin_External_AssumeLocked(batch, clientID, req.Confirmed, req.BetsWon, jackpotWonPending);
		jackpotWonOuter = jackpotWon;

		resp.JackpotStatus = level->Status_AssumeLocked();
		resp.ClientStatus = level->GetClientStatus(clientID);
		for (const auto& bet : bets) resp.BetsProcessed.emplace_back(bet.ToBetProcessed());

		if (jackpotWon)
		{
			broadcastJackpotWon(*level, levelInfo, amountWon, clientID, EJackpotLevelStatus::Won, bets, jackpotWonPending.ID);
			level->UpdateWinAmountCounter_AssumeLocked(batch, amountWon, clientID, clientConfig.ExternalTriggerType);
			OnJackpotWon_AssumeLocked(batch, level, clientID);
		}
		else
		{
			// Since we are broadcasting the pending win at external jackpot result, we need to broadcast the same event with status Counting in case win is not confirmed
			// so that clients can remove the pending win. Client should check the wonStatus when processing the event so it knows how to properly handle it.
			broadcastJackpotWon(*level, levelInfo, 0, clientID, EJackpotLevelStatus::Counting, {}, jackpotWonPending.ID);
		}

		TLOG(LogJackpotService, Info, "%u bets processed for level %s and client %s with jackpot confirmed '%b'", bets.size(), req.LevelID.c_str(), clientID.c_str(),
		     req.Confirmed)
	});

	if (jackpotWonOuter)
		if (const auto task = mJackpotBackupTask.lock())
			task->EnableAndExecuteImmediate();

	return resp;
}

GetRandomCombinationsRespDto JackpotService::getRandomCombinations(const GetRandomCombinationsReqDto& req)
{
	std::map<std::string, std::vector<std::string>> combinations;

	switch (req.Repetitions)
	{
		case ERepetitions::Yes: {
			if (req.Values.empty())
				throw BadRequestError("Values must not be empty for repetitions = Yes!");

			for (const auto& key : req.Keys)
			{
				std::vector<std::string> keyValues;
				for (size_t i = 0; i < req.NumValuesPerKey; i++)
				{
					auto it = req.Values.begin();
					std::advance(it, crypto::GetRandomInRange<uint32_t>(0, req.Values.size() - 1));
					keyValues.emplace_back(*it);
				}
				combinations.insert_or_assign(key, std::move(keyValues));
			}
			break;
		}
		case ERepetitions::NoPerKey: {
			if (req.Values.size() < req.NumValuesPerKey)
				throw BadRequestError("Values size must be at least numValuesPerKey for repetitions = NoPerKey!");

			for (const auto& key : req.Keys)
			{
				std::unordered_set<std::string> values = req.Values;    // Copy of the set
				std::vector<std::string> keyValues;
				for (size_t i = 0; i < req.NumValuesPerKey; i++)
				{
					auto it = values.begin();
					std::advance(it, crypto::GetRandomInRange<uint32_t>(0, values.size() - 1));
					keyValues.emplace_back(*it);
					values.erase(it);    // Remove value so it is not repeated for key
				}
				combinations.insert_or_assign(key, std::move(keyValues));
			}
			break;
		}
		case ERepetitions::No: {
			if (req.Values.size() < req.NumValuesPerKey * req.Keys.size())
				throw BadRequestError("Values size must be at least numValuesPerKey * numKeys for repetitions = No!");

			std::unordered_set<std::string> remainingValues = req.Values;    // Copy of the set
			for (const auto& key : req.Keys)
			{
				std::vector<std::string> keyValues;
				for (size_t i = 0; i < req.NumValuesPerKey; i++)
				{
					auto it = remainingValues.begin();
					std::advance(it, crypto::GetRandomInRange<uint32_t>(0, remainingValues.size() - 1));
					keyValues.emplace_back(*it);
					remainingValues.erase(it);    // Remove value so it is not repeated
				}
				combinations.insert_or_assign(key, std::move(keyValues));
			}
			break;
		}
		default: throw BadRequestError("Unsupported repetitions value: " + (req.Repetitions ? std::string(req.Repetitions._to_string()) : "null"));
	}

	return GetRandomCombinationsRespDto(combinations);
}

GetProbabilitiesRespDto JackpotService::getProbabilities(const GetProbabilitiesReqDto& req)
{
	if (req.NumProbabilities <= 0 || req.NumProbabilities > 100)
		throw BadRequestError("NumProbabilities must be greater than 0 and less than or equal to 100!");
	std::vector<double> results;
	for (uint32_t i = 0; i < req.NumProbabilities; i++) results.emplace_back(crypto::GetRandomInRange(0.0, 1.0));
	return GetProbabilitiesRespDto(results);
}

void JackpotService::broadcastJackpotLevelReconfigured(const TJackpotLevel& level, const JackpotLevelInfoBase& levelInfo) const
{
	const JackpotLevelReconfiguredEventDto event(levelInfo);
	// only send to clients that are part of this level
	mClientManager->Multicast(level.GetEnabledClientIds(), levelInfo.ClientGroupID, *mJackpotLevelReconfiguredEvent, event);
}

void JackpotService::broadcastJackpotLevelUpdated(const TJackpotLevel& level, const JackpotLevelDto& jackpotLevelDto) const
{
	const JackpotLevelUpdatedEventDto event(jackpotLevelDto);
	// only send to clients that are part of this level
	mClientManager->Multicast(level.GetEnabledClientIds(), jackpotLevelDto.Info.ClientGroupID, *mJackpotLevelUpdatedEvent, event);
}

void JackpotService::emitJackpotLevelUpdated_AssumeReadOnlyLocked(const TJackpotLevel& level, const std::string& clientId) const
{
	const auto clientStatus = level.GetClientStatus(clientId);
	const JackpotLevelUpdatedEventDto event(level.GetJackpotLevel_AssumeLocked(), clientStatus);
	mClientManager->Multicast({ clientId }, std::nullopt, *mJackpotLevelUpdatedEvent, event);
}

void JackpotService::broadcastJackpotWon(const TJackpotLevel& level, const JackpotLevelInfoBase& levelInfo, const double amountWon, const std::string& wonClientId,
                                         const EJackpotLevelStatus wonStatus, const std::vector<BetDto>& betsWon, const std::string& wonEventId) const
{
	std::vector<JackpotWinnerDto> winners;
	for (const auto& bet : betsWon)
	{
		if (bet.Status == EJackpotBetStatus::Won)
			winners.emplace_back(bet.ToJackpotWinner());
	}

	const JackpotWonEventDto event { wonEventId, levelInfo.ID, amountWon, levelInfo.Currency, wonClientId, wonStatus, std::move(winners) };

	mClientManager->Multicast(level.GetEnabledClientIds(), levelInfo.ClientGroupID, *mJackpotWonEvent, event);
}

/**
 * @brief Load jackpot levels from disk
 *
 * @return
 */
void JackpotService::LoadJackpot(const bool bReconfigure)
{
	ScopedLock lock(mJackpotLevels);
	LoadJackpot_AssumeLocked(bReconfigure);
}

void JackpotService::LoadJackpot_AssumeLocked(const bool bReconfigure)
{
	Log(Info, "Loading jackpot levels...");

	// clear jackpot update broadcast task
	if (auto task = mJackpotUpdateBroadcastTask.lock())
		task->Remove();
	mJackpotUpdateBroadcastTask.reset();

	// clear jackpot backup task
	if (auto task = mJackpotBackupTask.lock())
		task->Remove();
	mJackpotBackupTask.reset();

	size_t numLevels = 0;

	// clear all jackpot levels (will be reloaded below)
	mJackpotLevels->clear();

	// clear all reset tasks
	ClearAllLevelResetTasks();

	std::vector<JackpotLevelInfoBase> jackpotLevelConfigs = mJackpotDao->ListJackpotLevelConfigs();
	for (const JackpotLevelInfoBase& levelInfo : jackpotLevelConfigs)
	{
		LoadJackpotLevel_AssumeLocked(levelInfo, bReconfigure);
		numLevels++;
	}

	Log(Info, "Successfully loaded %lu jackpot levels!", numLevels);

	mJackpotUpdateBroadcastTask = pApp->AddTimedTask<TAsyncTimedTaskHandler>([this] { JackpotPotUpdateTask(); }, 2500, TTimedTaskHandler::ENDLESS, "jackpot pot update");

	if (mJackpotConfig->JackpotBackup)
		mJackpotBackupTask = pApp->AddTimedTask<TAsyncTimedTaskHandler>([this] { JackpotBackup(); }, mJackpotConfig->JackpotBackup->IntervalSec * 1000,
		                                                                TTimedTaskHandler::ENDLESS, "jackpot backup");
}

void JackpotService::ClearJackpotLevel_AssumeWriteLocked(const std::string& levelId)
{
	// clear jackpot level (will be reloaded below)
	mJackpotLevels->erase(levelId);

	// clear reset task if exists
	ClearLevelResetTask(levelId);
}

void JackpotService::LoadJackpotLevel(const std::string& levelId, bool bReconfigure)
{
	ScopedLock lock(mJackpotLevels);
	ClearJackpotLevel_AssumeWriteLocked(levelId);

	if (const auto levelInfoOpt = mJackpotDao->GetJackpotLevelConfig(levelId))
		LoadJackpotLevel_AssumeLocked(levelInfoOpt.value(), bReconfigure);
}

void JackpotService::LoadJackpotLevel_AssumeLocked(const JackpotLevelInfoBase& levelInfo, bool bReconfigure)
{
	auto level = std::make_shared<TJackpotLevel>(mJackpotDao, levelInfo, mJackpotConfig->ServerID);
	mJackpotDao->WorkOnJackpotLevel(level, [this, &level, &levelInfo, bReconfigure](rocksdb::WriteBatch& batch) -> void {
		if (bReconfigure)
		{
			level->OnReconfigured_AssumeLocked(batch);
			broadcastJackpotLevelReconfigured(*level, levelInfo);
		}

		if (level->Status_AssumeLocked() == EJackpotLevelStatus::Won && levelInfo.AutoActionAfterWin != EActionAfterWin::Nothing)
			SetLevelResetTask_AssumeLocked(level, batch);

		mJackpotLevels->emplace(levelInfo.ID, level);

		const auto values = level->GetValues_AssumeLocked();

		Log(Debug, "Loaded jackpot level %s (%s), status: %s, pot: %.4f, backPot: %.4f, counters: { %s }, info: %s", levelInfo.Name.c_str(), levelInfo.ID.c_str(),
		    values.Status._to_string(), values.Pot, values.BackPot, yutils::jsonToStr(level->GetCounterValues_AssumeLocked()).c_str(),
		    yutils::jsonToStr(levelInfo).c_str());
	});
}

/**
 * Jackpot pot update task for single levels
 * @param batch The rocksdb batch to use
 * @param level The jackpot level to update
 * @return true if backup task should be run
 */
bool JackpotService::JackpotPotUpdateTask_AssumeWriteLocked(rocksdb::WriteBatch& batch, std::shared_ptr<TJackpotLevel> level)
{
	if (!level)
		return false;

	// check if level data has changed and broadcast if yes
	const bool bLevelChanged = level->bChanged.exchange(false);
	const bool bCanBeWon = level->CanBeWon_AssumeLocked();
	const bool bLevelCanBeWonChanged = level->bLastCanBeWon.exchange(bCanBeWon) != bCanBeWon;
	if (bLevelChanged || bLevelCanBeWonChanged)
		broadcastJackpotLevelUpdated(*level, level->GetJackpotLevel_AssumeLocked());

	if (level->IsPendingArchive_AssumeReadLock() && level->Status_AssumeLocked() == EJackpotLevelStatus::Disabled && level->CountPendingBets_AssumeLocked() == 0)
	{
		ArchiveJackpotLevel_AssumeWriteLocked(batch, level);
		return false;
	}

	// reset bet aggregations if needed (after x:00, x:15, x:30, x:45) TODO: could we do this at more accurate times and not by check every 2.5s?
	level->ResetBetAggregations_AssumeLocked();

	// check if hotseat level is won
	if (level->IsHotSeatWon_AssumeLocked())
	{
		// get all pending bets for this level
		std::vector<BetDto> pendingBets = level->ListPendingBets_AssumeLocked(BetFilter({}, { EJackpotBetStatus::PendingResult }));

		// get set of client ids that have pending bets
		std::unordered_set<std::string> clientIds;
		for (const auto& bet : pendingBets) { clientIds.insert(bet.ClientID); }

		// get all connected clients with client ids
		const auto clients = mClientManager->GetClients(clientIds);

		// filter pending bets by clients that are connected and get set of bet ids
		std::unordered_set<std::string> filteredBetIds;
		for (const auto& bet : pendingBets)
		{
			if (clients.contains(bet.ClientID))
				filteredBetIds.insert(bet.ID);
		}

		if (const std::optional<BetDto> wonBetOpt = level->GetHotSeatWinner_AssumeLocked(batch, pendingBets, filteredBetIds))
		{
			const auto& wonBet = wonBetOpt.value();
			const auto& wonClientIt = clients.find(wonBet.ClientID);
			const auto externalTriggerType = wonClientIt != clients.end() && wonClientIt->second ? wonClientIt->second->ExternalTriggerType : std::string();

			const JackpotHistoryDto entry =
			  JackpotHistoryDto::WonPending(level->ID, wonBet.CreditedAmount, wonBet.ClientID, level->GetCounterValues_AssumeLocked().WinCounter).ToWon(wonBet);
			level->SaveWonHistory_AssumeLocked(batch, wonBet.ClientID, entry);

			broadcastJackpotWon(*level, level->GetInfo_AssumeLocked(), wonBet.CreditedAmount, wonBet.ClientID, EJackpotLevelStatus::Won, { wonBet }, entry.ID);
			level->UpdateWinAmountCounter_AssumeLocked(batch, wonBet.CreditedAmount, wonBet.ClientID, externalTriggerType);
			level->ResetPot_AssumeLocked(batch, wonBet.CreditedAmount);
			OnJackpotWon_AssumeLocked(batch, level, wonBet.ClientID);
			return true;
		}
	}
	return false;
}

/**
 * @brief Jackpot pot update task. This function is called every 2.5s and performs the following actions:
 * 1. Checks if level data has changed. If yes, calls broadcastJackpotLevelUpdated()
 * 2. Resets bet aggregations if needed (after x:00, x:15, x:30, x:45)
 * 3. Checks if any jackpot level is won. If yes, calls OnJackpotWon()
 *
 */
void JackpotService::JackpotPotUpdateTask()
{
	bool bShouldRunBackup = false;

	ScopedLock lock(mJackpotLevels);
	// we are using iterator here because we can also delete level from a map inside JackpotPotUpdateTask_AssumeWriteLocked
	auto it = mJackpotLevels->begin();
	while (it != mJackpotLevels->end())
	{
		std::shared_ptr<TJackpotLevel> level = it->second;
		++it;

		mJackpotDao->WorkOnJackpotLevel(
		  level, [this, level, &bShouldRunBackup](rocksdb::WriteBatch& batch) { bShouldRunBackup |= JackpotPotUpdateTask_AssumeWriteLocked(batch, level); });
	}
	lock.unlock();

	if (bShouldRunBackup)
		if (const auto task = mJackpotBackupTask.lock())
			task->EnableAndExecuteImmediate();
}

/**
 * @brief Jackpot backup task. This function is called every mJackpotConfig.JackpotBackup->IntervalSec seconds and performs the following actions:
 * 1. Backups jackpot levels to disk (config, counters, pot/backpot)
 */
void JackpotService::JackpotBackup()
{
	// list levels from this server instance
	json levels(json::value_t::array);
	SharedScopedLock lock(mJackpotLevels);
	for (const auto& level : &mJackpotLevels | std::views::values)
	{
		SharedScopedLock lock2(*level);
		const auto levelData = level->GetListLevelDataAdmin_AssumeLocked();
		levels.emplace_back(levelData.ToJSON());
	}
	lock.unlock();

	if (levels.empty())
		return;

	// save levels to disk to backup location with timestamp in name
	const std::filesystem::path backupPath = mJackpotConfig->JackpotBackup->Dir;
	const std::string backupFile = backupPath / std::format("backup_{}.json", yutils::FormatTimeMs("%Y%m%d-%H%M%S", std::chrono::system_clock::now()));

	std::ofstream outFile(backupFile, std::ios_base::trunc | std::ios_base::out);
	if (outFile.is_open())
	{
		outFile << yutils::jsonToStr(levels, true);
		outFile.close();
		TLOG(LogJackpotService, Debug, "Jackpot levels backed up to %s", backupFile.c_str());
	}
	else
		TLOG(LogJackpotService, Error, "Failed to backup jackpot levels to %s", backupFile.c_str());

	// remove the oldest backup files if their count exceeds mJackpotConfig.JackpotBackup->MaxBackups.
	std::vector<std::filesystem::path> backupFiles;
	for (const auto& entry : std::filesystem::directory_iterator(backupPath))
	{
		if (entry.is_regular_file() && entry.path().extension() == ".json" && entry.path().filename().string().starts_with("backup_"))
			backupFiles.emplace_back(entry.path());
	}
	if (backupFiles.size() > mJackpotConfig->JackpotBackup->MaxBackups)
	{
		std::ranges::sort(backupFiles, std::greater<>());
		for (size_t i = mJackpotConfig->JackpotBackup->MaxBackups; i < backupFiles.size(); i++)
		{
			try
			{
				std::filesystem::remove(backupFiles[i]);
			}
			catch (const std::filesystem::filesystem_error& e)
			{
				std::cerr << "Failed to remove file: " << backupFiles[i] << ", error: " << e.what() << '\n';
			}
		}
	}
}

std::shared_ptr<TJackpotLevel> JackpotService::GetJackpotLevel(const std::string& id, bool throwIfNotFound) const
{
	SharedScopedLock lock(mJackpotLevels);
	return GetJackpotLevel_AssumeReadLocked(id, throwIfNotFound);
}

std::shared_ptr<TJackpotLevel> JackpotService::GetJackpotLevel_AssumeReadLocked(const std::string& id, bool throwIfNotFound) const
{
	auto it = mJackpotLevels->find(id);
	if (it == mJackpotLevels->end())
	{
		if (throwIfNotFound)
			throw NotFoundError("No jackpot level exists with ID " + id);
		else
			return {};
	}
	else
		return it->second;
}

bool JackpotService::LevelExists(const std::string& id) const
{
	SharedScopedLock lock(mJackpotLevels);
	return mJackpotLevels->contains(id);
}


double JackpotService::GetPaidInAmount(const JackpotLevelInfoBase& levelInfo, const double betAmount, const std::string& clientExternalTriggerType)
{
	const auto it = levelInfo.ExternalTriggerTypeData.find(clientExternalTriggerType);
	return levelInfo.bUseFixedPaidIn && it != levelInfo.ExternalTriggerTypeData.end() ? it->second.PaidIn : std::max(betAmount, 0.0) * levelInfo.Increment;
}

void JackpotService::InitLockService()
{
	if (!mJackpotLockService)
		return;

	ScopedLock lock(mLockedData);
	&mLockedData = mJackpotDao->GetLockedData();
	if (mLockedData->has_value())
		Log(Warning, "Jackpot is currently locked! Levels are disabled and cannot be played until unlocked.");
	lock.unlock();

	// register OnLocked and OnUnlocked handlers
	mJackpotLockService->OnLocked += [this]() -> void {
		// TODO: should we do anything else besides disabling all levels? Maybe disconnect parent, clients, disable admin actions, shut down server...
		ScopedLock lock(mLockedData);
		if (!mLockedData->has_value())
		{
			const auto disabledLevelIds = disableAllLevels();
			&mLockedData = LockedDataDto { .LevelIDs = std::move(disabledLevelIds) };
			mJackpotDao->SaveLockedData(mLockedData->value());
		}
	};
	mJackpotLockService->OnUnlocked += [this]() -> void {
		ScopedLock lock(mLockedData);
		if (mLockedData->has_value() && mJackpotLockService->GetLockStatus().value_or(std::string()).empty())
		{
			enableLevels(mLockedData->value().LevelIDs);    // re-enable all levels that were disabled when jackpot lock was acquired;
			mLockedData->reset();
			mJackpotDao->DeleteLockedData();
		}
	};

	// connect to jackpot lock service
	mJackpotLockService->Connect();
}
