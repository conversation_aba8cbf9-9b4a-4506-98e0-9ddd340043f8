
#include "cef-support/browser_client.h"

#include <base/cef_callback.h>
#include <cef_parser.h>
#include <wrapper/cef_helpers.h>

#include <iostream>

#include "TGuiApplication.h"
#include "cef-support/application.h"
#include "cef-support/dialog_handler.h"

/**
 * Prevents a context menu appearing
 */
class NoContextMenu : public CefContextMenuHandler
{
	void OnBeforeContextMenu(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, CefRefPtr<CefContextMenuParams> params, CefRefPtr<CefMenuModel> model) override
	{
		model->Clear();
	}
	IMPLEMENT_REFCOUNTING(NoContextMenu);
};

browser_client::browser_client(CefRefPtr<CefRenderHandler> graphics, CefRefPtr<CefAudioHandler> audio, const CefMessageRouterConfig& config) :
    renderHandler(std::move(graphics)), audioHandler(std::move(audio)), contextMenuHandler(new NoContextMenu), resourceManager(new CefResourceManager()), config(config)
{
	dialogHandler = new ClientDialogHandler();
}

browser_client::~browser_client() = default;

CefRefPtr<CefLifeSpanHandler> browser_client::GetLifeSpanHandler()
{
	return this;
}

CefRefPtr<CefRenderHandler> browser_client::GetRenderHandler()
{
	return renderHandler;
}

CefRefPtr<CefAudioHandler> browser_client::GetAudioHandler()
{
	return audioHandler;
}
CefRefPtr<CefLoadHandler> browser_client::GetLoadHandler()
{
	return this;
}

CefRefPtr<CefJSDialogHandler> browser_client::GetJSDialogHandler()
{
	return dialogHandler;
}

CefRefPtr<CefContextMenuHandler> browser_client::GetContextMenuHandler()
{
	return contextMenuHandler;
}

CefRefPtr<CefDisplayHandler> browser_client::GetDisplayHandler()
{
	return this;
}

// CefLifeSpanHandler methods.
void browser_client::OnAfterCreated(CefRefPtr<CefBrowser> browser)
{
	CEF_REQUIRE_UI_THREAD();

	browser_id = browser->GetIdentifier();

	if (!messageRouterBrowserSide)
	{
		// Create the browser-side router for query handling.
		messageRouterBrowserSide = CefMessageRouterBrowserSide::Create(config);
	}

	OnBrowserCreated(browser);
}

void browser_client::OnBeforeClose(CefRefPtr<CefBrowser> browser)
{
	CEF_REQUIRE_UI_THREAD();
	messageRouterBrowserSide->OnBeforeClose(browser);

	messageRouterBrowserSide = nullptr;

	OnBrowserDestroyed(browser);
}

bool browser_client::OnConsoleMessage(CefRefPtr<CefBrowser> browser, cef_log_severity_t level, const CefString& message, const CefString& source, int line)
{
	return ConsoleMessageHandler && ConsoleMessageHandler(browser, level, message, source, line);
}

void browser_client::OnStatusMessage(CefRefPtr<CefBrowser> browser, const CefString& value)
{
	if (LoadingStatusHandler)
		LoadingStatusHandler(browser, value);
}

void browser_client::OnLoadingProgressChange(CefRefPtr<CefBrowser> browser, double progress)
{
	if (LoadingProgressHandler)
		LoadingProgressHandler(browser, progress);
}

void browser_client::OnLoadEnd(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, int httpStatusCode)
{
	if (frame->IsMain())
	{
		const std::string url = frame->GetURL();
		TLOG(LogApp, Important, "Load of %s ended with %d", url.c_str(), httpStatusCode);
	}
}

void browser_client::OnLoadError(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, ErrorCode errorCode, const CefString& errorText, const CefString& failedUrl)
{
	const std::string url = failedUrl;
	const std::string error = errorText;
	TLOG(LogApp, Error, "Load error %d for %s: %s", errorCode, url.c_str(), error.c_str());
}

bool browser_client::OnBeforeBrowse(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, CefRefPtr<CefRequest> request, bool user_gesture, bool is_redirect)
{
	CEF_REQUIRE_UI_THREAD();

	messageRouterBrowserSide->OnBeforeBrowse(browser, frame);
	return false;
}

CefRefPtr<CefResourceHandler> browser_client::GetResourceHandler(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, CefRefPtr<CefRequest> request)
{
	CEF_REQUIRE_UI_THREAD();

	return resourceManager->GetResourceHandler(browser, frame, request);
}

void browser_client::OnRenderProcessTerminated(CefRefPtr<CefBrowser> browser, TerminationStatus status, int error_code, const CefString& error_string)
{
	CEF_REQUIRE_UI_THREAD();

	messageRouterBrowserSide->OnRenderProcessTerminated(browser);
}

bool browser_client::OnProcessMessageReceived(CefRefPtr<CefBrowser> browser, CefRefPtr<CefFrame> frame, CefProcessId source_process, CefRefPtr<CefProcessMessage> message)
{
	CEF_REQUIRE_UI_THREAD();

	return messageRouterBrowserSide->OnProcessMessageReceived(browser, frame, source_process, message);
}
