set(SERVER_SOURCES
        ${PROJECT_SOURCE_DIR}/src/common/THash.cpp

        ${PROJECT_SOURCE_DIR}/src/jackpot/client/JackpotAdminClient.cpp

        ${PROJECT_SOURCE_DIR}/src/roulette/ThunderLogic.cpp
        ${PROJECT_SOURCE_DIR}/rmclient/rmclient_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/common/TTimetable.cpp
        ${PROJECT_SOURCE_DIR}/rmclient/rmclient_cmd.cpp
        ${PROJECT_SOURCE_DIR}/rmclient/TRpcCommand.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TGameHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TLiveGameHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TBaseRouletteHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TRouletteHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TVirtualWheelHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/gameart/TGameartHostBase.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TGameartSlotHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TGameartTableGameHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TRouletteBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/roulette/RouletteJackpot.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/roulette/LuckyNumberJackpot.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/roulette/FireballDeluxeJackpot.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/roulette/dto/LuckyNumberJackpotHistoryDto.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/roulette/dto/RouletteJackpotConfigDto.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TBlackjackHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaseBaccaratHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TVirtualBaccaratHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TVirtualBaccaratGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/TBaseVirtualCardGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/CardGameBetStatistics.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/EventQueue.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratStake.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/baccarat/TBaccaratGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/evolution/TEvolutionGameHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/evolution/TEvolutionGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/evolution/dto/EvolutionGameHostConfigDto.cpp
        ${PROJECT_SOURCE_DIR}/src/ladyroulette/TRouletteRNG.cpp
        ${PROJECT_SOURCE_DIR}/src/common/TRouletteTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealersHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealersStake.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealersBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealersHostSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealerBaccaratGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealerBaccaratBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealerDragonTigerBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealerThreeHeadedDragonBets.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/CardGameSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/CardGameBetStatistics.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/cards/TCardBetSecurityManager.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TBetsSharedTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerHostSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TVirtualDragonTigerGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TVirtualDragonTigerHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerStake.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dragontiger/TDragonTigerBets.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonStake.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonBets.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonVirtualGame.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonVirtualHost.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/threeheadeddragon/TThreeHeadedDragonGame.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/api/YEndpointAPI.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/api/YEndpoint.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/api/YPlayerAPI.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/api/YCommanderAPI.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/api/YViewerAPI.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/api/YLoggerAPI.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/v8-inspector/Inspector.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/v8-inspector/v8inspector_channel.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/v8-inspector/v8inspector_client.cpp

        ${PROJECT_SOURCE_DIR}/libXmlRpc++/XmlRpcClient.cpp
        ${PROJECT_SOURCE_DIR}/libXmlRpc++/XmlRpcValue.cpp
        ${PROJECT_SOURCE_DIR}/libXmlRpc++/XmlRpcUtil.cpp

        ${PROJECT_SOURCE_DIR}/src/currency-api/TCurrencyAPI.cpp

        ${PROJECT_SOURCE_DIR}/src/common/CreditArray.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TCasinoProvider.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TJackpotModule.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TroniusJackpotModule.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/providers/TGameartProvider.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/providers/TNanoProvider.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/providers/TBerghainProvider.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/providers/TGameHubProvider.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YServerClient.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TDealerGamesExtraData.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TRouletteGamesExtraData.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YServer.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TCroupierDatabase.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistInitResponseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistStateUpdateDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ReportNumOfActivePlayersDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GetAvailableGamesDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/SelectDealerGameRequestDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/AddCardDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ChatMessageDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/FlagStateChangeDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/RoundsUntilCloseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/CardBurnDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ToggleChatDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/BetStatisticsDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/ResultChangeRequestDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/ScannedCard.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GameRecordDto.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YTransaction.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YRoundSnapshot.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YGameInstance.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YPlayerAccount.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YModule.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YModuleContainerBase.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YModuleContainer.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YModuleBranch.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YServerTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YGameClient.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YPlayer.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TYServerApp.cpp)

add_executable(yserver ${SERVER_SOURCES})

find_library(ROCKS_DB_LIB rocksdb)

target_compile_definitions(yserver PRIVATE YSERVER V8_COMPRESS_POINTERS V8_ENABLE_SANDBOX)

target_include_directories(yserver PRIVATE ${CMAKE_SYSROOT}/usr/include/v8 ${CMAKE_SOURCE_DIR}/include/yserver ${CMAKE_SOURCE_DIR}/libXmlRpc++ ${CMAKE_SOURCE_DIR}/rmclient ${CMAKE_SOURCE_DIR}/include/imaxanano ${CMAKE_SOURCE_DIR}/include/ladyroulette)

find_library(V8_MONOLITH_LIB v8_monolith)
target_include_directories(yserver PRIVATE /usr/local/include/v8)
target_link_libraries(yserver loki-log jackpot-client evolution-core yprotocol-server ${V8_MONOLITH_LIB} ${ROCKS_DB_LIB})

add_version_define(yserver)
