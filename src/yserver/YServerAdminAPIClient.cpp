#include "yserver/YServerAdminAPIClient.h"

#include "Cryptography.h"
#include "YUtils.h"

YServerAdminAPIClient::YServerAdminAPIClient(std::shared_ptr<YClient> client) : Client(client) {}

boost::future<web::http::response> YServerAdminAPIClient::DoYServerRequest(web::http::request req, const json& body) const
{
	if (!Client->LogComponentName.empty())
		req.set_header(web::http::field::user_agent, Client->LogComponentName);
	req.set_header(web::http::field::authorization, mAPIKey);
	req.set_json_body(body);

	return Client->Client->Request(std::move(req));
}

bool YServerAdminAPIClient::ConnectToProtectedEndpoint(yserver::EClientType type, const web::QueryString& query, std::string* outError)
{
	const auto endpointResponse = CreateProtectedEndpoint(type, query).get();
	if (endpointResponse.get_status_code() != web::http::status::ok || endpointResponse.get_json().is_null())
	{
		if (outError)
			*outError = endpointResponse.get_status_msg();
		return false;
	}

	const std::string endp = endpointResponse.get_json()["endpoint"].get<std::string>();
	const std::string challenge = endpointResponse.get_json()["challenge"].get<std::string>();

	std::error_code ec;
	const bool bSuccess =
	  Client->Connect(yutils::Format("/%s/%s/%s", type._to_string(), endp.c_str(), crypto::Hash(mSecret + challenge, EHashAlgorithm::SHA1).c_str()), &ec);

	if (outError)
		*outError = "Connection failed: " + ec.message();

	return bSuccess;
}

const std::string& YServerAdminAPIClient::Secret() const
{
	return mSecret;
}

const JsonSchema EndpointCreationResponseSchema =
  JsonSchema({ { "endpoint", JsonSchema(json::value_t::string) }, { "challenge", JsonSchema(json::value_t::string, "A challenge string", std::string()) } });
const JsonSchema EndpointCreationErrorResponseSchema =
  JsonSchema({ { "error", JsonSchema(json::value_t::number_integer) }, { "error-msg", JsonSchema(json::value_t::string) } });
boost::future<web::json_response> YServerAdminAPIClient::CreateProtectedEndpoint(yserver::EClientType type, const web::QueryString& query)
{
	std::string targetURL = "/api/" + std::string(type._to_string());
	if (!query.empty())
		targetURL += "?" + query.ToString();

	return APIRequest(targetURL).then(boost::launch::sync, [](boost::future<web::json_response> fut) -> web::json_response {
		web::json_response result { fut.get() };
		if (result.get_status_code() == web::http::status::ok)
		{
			try
			{
				result.set_json(EndpointCreationResponseSchema.GenerateConfig(result.get_json(), { .bFillEmptyOptionalsWithDefaults = true }));
			}
			catch (const SchemaError& err)
			{
				if (EndpointCreationErrorResponseSchema.Test(result.get_json()))
					result.set_status(web::http::status::ok,
					                  std::format("Error code {}: {}", result.get_json()["error"].get<int>(), result.get_json()["error-msg"].get<std::string>()));
				else
					result.set_status(web::http::status::ok, std::string("Bad response: ") + err.what());
				result.set_json(json());
			}
		}

		return result;
	});
}

boost::future<web::json_response> YServerAdminAPIClient::APIRequest(const std::string& apiEndpoint, const json& body)
{
	web::http::request request(body.empty() ? web::http::verb::get : web::http::verb::post);
	request.set_uri(apiEndpoint);
	return DoYServerRequest(std::move(request), body).then(boost::launch::sync, [this, apiEndpoint](boost::future<web::http::response> fut) -> web::json_response {
		web::json_response response = fut.get();
		if (response.get_error_code())
		{
			Client->Log(Debug, "API request %s failed: %s", apiEndpoint.c_str(), response.get_error_code().message().c_str());
			response.set_status(response.get_status_code(), response.get_error_code().message());
			return response;
		}

		if (response.get_status_code() != web::http::status::ok)
		{
			Client->Log(Debug, "API request %s returned error code %d: %s", apiEndpoint.c_str(), (int)response.get_status_code(), response.get_status_msg().c_str());
			return response;
		}

		return response;
	});
}

void YServerAdminAPIClient::SetAPIKeys(const std::string& apiKey, const std::string& secret)
{
	mAPIKey = apiKey;
	mSecret = secret;
	Client->Log(Info, "API key set to %s", mAPIKey.c_str());
}

std::string YServerAdminAPIClient::APIKeySign(const std::string& data, EHashAlgorithm algo) const
{
	return mSecret.empty() ? std::string() : crypto::Hash(mSecret + data, algo);
}