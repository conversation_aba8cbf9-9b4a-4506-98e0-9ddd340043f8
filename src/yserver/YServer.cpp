#include "YServer.h"

#include "Cryptography.h"
#include "Markdown.h"
#include "TApplication.h"
#include "YUtils.h"
#include "api/YCommanderAPI.h"
#include "api/YLoggerAPI.h"
#include "api/YPlayerAPI.h"
#include "api/YViewerAPI.h"
#include "hosts/TBlackjackHost.h"
#include "hosts/TGameartSlotHost.h"
#include "hosts/TGameartTableGameHost.h"
#include "hosts/TRouletteHost.h"
#include "hosts/TVirtualWheelHost.h"
#include "hosts/baccarat/TBaccaratHost.h"
#include "hosts/baccarat/TVirtualBaccaratHost.h"
#include "hosts/dealer/TDealersHost.h"
#include "hosts/dragontiger/TVirtualDragonTigerHost.h"
#include "hosts/evolution/TEvolutionGameHost.h"
#include "hosts/threeheadeddragon/TThreeHeadedDragonVirtualHost.h"
#include "providers/TBerghainProvider.h"
#include "providers/TGameHubProvider.h"
#include "providers/TGameartProvider.h"
#include "providers/TNanoProvider.h"
#include "rocksdb/db.h"
#include "rocksdb/iterator.h"

DEFINE_LOG_CATEGORY(LogStatus, "status")

const version::Version Version = version::Version::FromString(VERSION_STRING);

using namespace yserver;

const JsonSchema YServerSchema = JsonSchema(
  { { "name", JsonSchema(json::value_t::string, "The publicly visible name of this YServer (visible in response headers)", std::string()) },
    { "server-id", JsonSchema(json::value_t::string, "The server ID to use for this yserver. Default: hostname", boost::asio::ip::host_name()) },
    { "host", JsonSchema(json::value_t::string, "The hostname of this server (eg. test.imaxagaming.com), used for launching games") },
    { "max-clients", JsonSchema(json::value_t::number_unsigned, "The maximum number of clients the server may host (0 for unlimited)", 0U) },
    { "pilot-seat", JsonSchema(json::value_t::boolean, "Should commanders be allowed to control players", false) },
    { "disk-sync", JsonSchema(json::value_t::boolean, "Should disk operations be synchronized (flushed)", false) },
    { "storage-directory", JsonSchema(json::value_t::string, "The directory in which to store persistent data", "yserver_data") },
    { "config-dir",
      JsonSchema(json::value_t::string,
                 "The directory where there are sub-directories 'hosts' and 'providers' with configuration files (empty or . mean directory of this config file)",
                 std::string()) },
    { "user-config-dir",
      JsonSchema(
        json::value_t::string,
        "The directory where there are sub-directories 'hosts' and 'providers' with user configuration files (merged into base config) (empty or . mean directory of this config file)",
        std::string()) },
    { "database", JsonSchema(json::value_t::string, "The directory where to store persistent key:value pairs saved by hosts & providers", "db") },
    { "report-interval", JsonSchema(json::value_t::number_unsigned, "How often the console client list should print (seconds)", 20U) },
    { "log-history-size", JsonSchema(json::value_t::number_unsigned, "How many logs to keep in history", 1000U) },
    { "max-latency", JsonSchema(json::value_t::number_unsigned,
                                "How much a received packet timestamp can deviate from the server before not being accepted anymore (milliseconds)", 3500U) },
    { "image-cache-duration", JsonSchema(json::value_t::number_unsigned, "How long game images can remain in browser cache (hours)", 72U) },
    { "async-workers",
      JsonSchema(json::value_t::number_unsigned, "Number of async worker threads to spawn", 8U).AddConstraint(limits::ValueLimit(limits::ELimitType::MoreThan, 0U)) },
    { "reload-timeout",
      JsonSchema(
        { { "game-host", JsonSchema(json::value_t::number_unsigned,
                                    "How long to wait for a game host to close gracefully when reloading it, before just giving up and unloading it (seconds)", 5 * 60) },
          { "casino-provider",
            JsonSchema(json::value_t::number_unsigned,
                       "How long to wait for a casino provider to close gracefully when reloading it, before just giving up and unloading it (seconds)", 5 * 60) } },
        "The time to wait for modules to gracefully exit", false) },
    { "session",
      JsonSchema({ { "activity-timeout", JsonSchema(json::value_t::number_unsigned, "How long before the client is kicked due to inactivity (seconds)", 600) },
                   { "allow-reconnect", JsonSchema(json::value_t::boolean, "Allow reconnecting to an open endpoint", false) },
                   { "secure", JsonSchema(json::value_t::boolean, "Should the session cookie be saved with the Secure attribute?", true) },
                   { "global-multi-session", JsonSchema(json::value_t::string, "The global rule for how multiple sessions of the same player account are handled",
                                                        EPlayerMultiSessionMode(EPlayerMultiSessionMode::Unlimited)._to_string())
                                               .SetToEnumType<EPlayerMultiSessionMode>() } },
                 "Session configuration")
        .SetRequired(false) },
    { "http-auth-keys", JsonSchema(json::value_t::array, "List of keys to use as authentication for YServer admin API", json(json::value_t::array))
                          .SetChildSchema(JsonSchema(
                            { { "key", JsonSchema(json::value_t::string, "A unique identifier of this key (publicly visible)") },
                              { "secret", JsonSchema(json::value_t::string, "A 'password' for this auth key, not publicly visible - keep this safe!", std::string()) },
                              { "tag", JsonSchema(json::value_t::string, "A human-readable tag(name) for this key, useful in logs", std::string()) } },
                            "Auth key configuration")) },
    { "api", JsonSchema(json::value_t::object, "Configuration for YServer admin APIs per-api-types (player, commander, logger, viewer)", json(json::value_t::object)) },
    { "archive-changed-configs", JsonSchema(json::value_t::boolean, "Weather to archive changed configurations", true) },
    { "round-archive-expire",
      JsonSchema({ { "age-days", JsonSchema(json::value_t::number_unsigned, "How many days before old rounds are deleted (0 for never)", 14U) } }).SetRequired(false) },
    { "language-dir", JsonSchema(json::value_t::string, "The directory in which to look for language files (translations)", std::string()) },
    { "play-for-fun.credits", JsonSchema(json::value_t::number_unsigned, "How many credits to give to Play for Fun sessions?", 1234567890UL) },
    { "play-for-fun.currency", JsonSchema(json::value_t::string, "Which currency to select by default when playing for fun?", ECurrency(ECurrency::EUR)._to_string())
                                 .SetToEnumType<ECurrency>() } });

using namespace yutils;

const std::string ErrorHead = R"(
<div style="position:relative;width:100%;height:100%;display:flex;justify-content:center;align-items:center;background-color:black;">
	<div style="width:80%;height:80%;display:flex;flex-direction:column;">
		<span style="flex:1;"></span>
		<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAScAAAE5CAYAAADShuaqAAAAAXNSR0IB2cksfwAAAAlwSFlzAAALEwAACxMBAJqcGAAAsxBJREFUeNrsnQd4VNX29q/gvf/vNq+Fmp5MeiOhgxQRlQ7SFZDee1FRLNTQe1PBCiIiSAepNhTpIEVAsfdCSzKTkJk561vvPmefOTOZhExIg5z9PPs5k8lkMpmc85t3rf2utf/2N3OYwxzmMIc5zGEOc5jDHOYwhznMYQ5zmMMc5jCHOcxhDnOYwxzmMIc5zGEOc5jDHOYwhznMYQ5zmMMc5jCHOcxhDnOY49YZcztE3Ga+C+Ywhzlu+jGufoXbpjYPKsOz7KyHQ2+f095y+9z2lrI8y8zvFFFmQeeI2xZ2NoFnDnOY4wbG+IZ33hH0t7/9b6jlb3esHP3AHS8PrHvHnPbhd8xuG3bH/I7h/2bw/Gt+p/D/Y+Dc/u7Ymj4DJ6VZYNm5HSxl57SzlOXnK7Oka7QJLXOYwxw5jwNvP1MWx8/eenrEe/N608IucX+lNK10ZVpz/ysMpisMKBx/4fkTzws8T/M8zPPDOe3CtvFxNQPnRVZL0xhgY+Z1DO/Os8niLlEJS7pF+y3rFfef3H7/zDYhUF1lGXxlXu2fYALLHOYwhzoU6yEBBCL6V+pPO7+7/OU6+nTFE/TG8IY0q1UgTW/uR3PbhdH8DuE0v71FzAUdLLSQ56KOFlrcMZyWdAqnpZ3DaTEfcf88fgyDCzOdwXWBj/sYXqtZMU1h5dVt0aOR1V/qGevn7fUMivkPQsWyDLeyr/YzYWUOc5TqkfbLbqGe/vxyQzfHXx9Q+q97sy5/u9V5eucc59rnH1Zmt7M4U5pUcs5qE+JkJeVkteRgtePgo12bWfw1ph3383TyVOa0DaN5DCkAC+DCBNDm89essv5iYB1jlbVq4SORIxd1iWr45rDk8p6vrU/Y/7tt1fCqt1/8uGcZ8z9lDnOU4nHx680HMn7eQXx0pP28i1hNKRc+eYm2zOhK8zpGEUOKOBRjuIQTQ4kAIRx5KjlMATNAzAgvgGuBpr6gvASw2ll+5/DuY1ZNKRwSNtswrnY5z9eXcWxAWduR/iaozGGOUgSlstqxqe3nnYCT868Lm5SLPK/+uINYXdH3h1+j7XN70rxOKqQYMgCKhBRpkPKcymz1ewJWGsy8QcuJ8BHAQoi4iIHF9/3FId52DgFH/b7nsUSipW5hHkPKBJU5zFFKAHWbdtyR9fseuvTNFjvfJgCKQUVXfniP0n/Zw5B6nZVUNwGmqU0rqxDi27kAKrepK6857cKcPB3TWwZnpTQLdL7wWDR9PKsRXdzTjRhCivVwv2M8p9kO96vLX9/uCSrrkf5mjsoc5rgVx6VvNgsVcunrzfemfr8dcFIYTop2BKTor682CkhBSV349CVa+/zDNL1lIE1vEWAM9fIEJvk4JM9n85zWIlhhpaS80i+BDr7YTLn8SS+ncmaI3X5ykJ3hQ3R6MNH5oZR1YiBgdZrnVAZSdQ9IleH7ypr/TXOY49ZTTyqgvtnyjuPPvUI98USYJwAlbgNSFzYSwr3Un3bSye0z6ZXB91JKUw71kI/SVFROkPKE0tQWQQy4YFo5NJlOrmxD6Yf6kePUYCXj6AAl/VBfYrWkaMrJwdPOysmpnBokQJVxtD9AdZDnGP5eiPFvsYqwr5+ct/PXt1sPi1lGzEP9blhp8e+9jV9PGZ5l+fffbsOEiuOvsz4fVPbQS83KMHRvE4bUR0xDqjnMkX/15IJTMispqCa69PUWl3rSpoQUJhLnOH6wbBjN7RBB05r7eVVR3qA0jaG0anhV+mJ1WwbNAEAJsCENSuI2jgCUhJQ2VVDxbamo+HY6z3V8fwurR9h3Xcgc7Xeb7RgA0/8fDLK/8/N4nfy8ZWw3GD4+36hyGTjq4abn96LsnPYWuOnLLOsVZ8LLHObIPbzbUlY7rjCqJw1S7oDiI1QUbqf/uoe+3r+cVoxqLBLmxlzUrIfDdDU1zQNKmccGEIduAkJQTR5QErc9pgQU6aA60t+O56GzQ0T4xwrmC77vSQZNW54DeY61Huk3jY+Leb7Ocy3/3DZ+zF6+vZ/nYZ6fM5zO8fEs359t8nPieIx/jh/bfx/P3Tw382t8m4+v8Pfn83EiP2YU39fz2omBrTk8rcuqMGJx16iKy3rH/fertZ2uCyCYUme2CS3L719ZVltl+OdMaJnDHBqUtNzTlgQoJgEm9Shvk1dIfaWGepe/3Up7XhhMM1sH04xWQUJFqSt7qmp6Y3BSfqGUI6gMqgpqykEI+xhUIvz7goF1bijRV8PUySqLzg5V7z8zWM1l8ePwWOfJ3Kd4Xkz8DH72rPbc57Xn/3KYep8IO4fRsVda0NTmQcQq6Qr/7V/zPMhzy9wOluXzOoQ/N79j+GOsmuotejQygmF9d27/l5RmgWVmtAoBsMq+0D3GBJY5Sm3uqawW5r3m+PN9l3ryAJQnpLCqh9tQUad3zKZF3RJEmDe/UwRNeDCAVo6oRk4O3bI+v2Eo5aymDjOsGFCsZOyshJw8OQTsj1xVFqsiTDsm1JZ43JF++sTj+cizv9u0ytuH+znEFD+nh5Z4bhyz8DtwH/9dDsfJQU6GsKLBSRhSYZVYrHm74K6Hz2uB8HiFXePH/MDHQwyud+Z3Cp/I0Hpk4SORNV8fVKViTv+np+qWL8M/d/uSbtFlFneJMoFljlIAJ009MWiiGTpZl7/1rp5yU1FY0fvx2Ep6bVhDeqLOPTSmnh+NqlORDi1vKXJLaQf7FgSUrhPy9TNMt1CQCngan1thOCmsCpWvN3ZSOKzLydslTam4T3fTq2VBqqt+rqo2f2cAHWeVtYKB9TirrKbLe8cFEi3MBqM2//zbbfxctzOoypjqyhylIff0svOv9+mivnLnHVASTPK2DPNwXNT/fhqcdAfDqRI9Xq8yfb2pM7GyKCxAFfu0siKE5eGHbY96Lgy4OeoNplQ3eBkApkhzKqC1VHfTh6XN62A5xQoLwBrxYo+Ye18bkHin5/9wYNS/b0PiHQl3DqdNWJnjVss9bY5h6Aj1pIEpRzhlC/Pgi/pum7i9dPBDNCT5fzSiVkWa1DKULn7ck64dHyhCu1sNUPg78Lf9uqsbh7ThNLONTwbVHKGl1TCK8p/57cP0EBHF1gyh3+d1DN/JIJrMsGryxqAqlTz/p881rFRmdruwsgwy01lvjps+vJPq6RWop9xyT7nloQAoHOf1qk/Dqt1FQ6tXoIXd48SFDK+SEVC3CpyQ8P/j/e7EYRjNbB2SH/d8XoGlK6xFWpH1wo4CVhfndQjfu6hL5AQO8e5b/3TtbMoq06xVNMdNq55cvqdInhlYidP8T7nCyZuCuvrDe/TL6TU0qU08jaxVjgYnl6PXhlajrM8Hku1o/1srrOO/JYPhBHW49LFomnHjcMoJVuQBLD2XBXUlO0IsUGH1M6uqDUu6Rg18fVCVGM//9bUTA8t+sbpd2ckPBZjhnzluFvW0Waqnxe7qabNPgPqTAZX+y246uXO+SIwj9zQo6R5aM7a2UBm3WlgHRXjl0970Us9Ymt6qwOGUI6xmtfVaXO1Ewh15q4UcDvJrunb1sz5HGKBTWLXWyzg64B/G//nEB/zLYgXQPPvNUdJDu9s0OFn4dsYVVk8XXXV35CugbL/vpbUp3WlA/L/piQb+NLRaOfry3Y7C8yTDu5tiAqby6GXiMakH+tDy3nGiPEcmxvMzfalZ9JJ4Jx1W7cLs01sF21/uE0eOEwOFN+ua+sHwJQNq0YX1HR+a3dbyL+P/H4ZQDgtNUJmjxCbHpXp6wSf1BCgBTtrti19vokvfbhOQWtqvNvWN/S+lPBxOf37QQ1dPJQ5AHj6sbPNQznACvFDIDDiJsp3rFEIXMrwEqBhOyst945W0g33h1YIvS7EDVN+PoiMvNoN7/xsORV94uU984/Nr27spqjntLWU5JDTDPnOUKDhpuafNcI07hCLKxffkCaVLDCXcvvjtDrp4YSOlfbWMUg8No+Ovt6Lf3+9O1/jiKAlgyhFEh9yhYzs6gGzHB5LtxCB1Hh+kfn1soPieVebQDvalV/rGU0qzILFiN6O1+8R9mJ5gmaPVIHpOX2HmDVoMJ9LgJFvRKOkH+4oOEDunNnROaxZISztHiDzV/I7hXy3pGjWH/4ZaxvPhyVrlyjDAzO4P5ihZgOIQbZP9j73GlbtsYMoOpff49ga6+sVCSj8+mi/2Xnxh9CXn6cGUeXyAGxRKhDI65AVCAJDwMPUl674eZN39KFm3tSfr+tZkfbsZWVc8QNaXG5L1xXpkXVSb0hfUpGuLatLclkE0vHZFeqJeZXr83kruE/fxfKK+Osc28KOnGvrRuEb+9Gxjf3r+gQCa8FAgTWoaRFOaB7OqCRFQ04uo23qHV46wQmsaVnFvDKpi/LthGkVYrWx8po6S0jTQOaedRc9TSavCgk4Rh5d0ix6zelS1EON5gRpAc3MKcxRv7unrLVq3zC2tM3/dJVr5CuUkc0/Z1NJmDUqbGEoLyHpsONkApcN9RLhjPey1+0CRwMiq5Ys8wzAbh5a6GsL9n/Yk687ODKBWZF35IFlfYvDMq0HWmclknZZI6VPiKX1SLKVPjlMnvsZMiac0nlkzEml2swAaUqMCja5bSSwE5DZHYtb2PvF9PMcYhhpAB4g9cz8D7MEAmsjwSmkRLFYFjd0fvCkudIN4c1gy/739xJR/P1YX3x5VXfTU0n7GuAKoLGiveqrmdrBYFz0auX55n7g2X73bQQ/7nqpTvsyLPWLN3JQ5ilVB/ZPn1+k/7YBScmYHE9QSkubb6cq5ZZR+bLQBSgNUKBgTykWkjrzmhdAPSlNG4r6PHiPr5rYMIlZBS+uSdVYypU9NcIcPvgaYeFqnswKZoc3prpnOM21aFcqalUSLWgfSkJoVaAyDZfQNztxAJuEFBfb0fX70XGOGVpNAhlGwUFuz24aKGr/pDKd3nqhJmVooDSWI9whh3msDEtXkfTuvdgWU2tjxPFj1W9QxHGHf+Re6x4xfPbKaxXiOLOoSVXbNmBqmmjJH0SfGL36zeYGeGEcjOjUHpYFpO126sJ7STk7kk753sUPJ5k0haTki62e9WRl1Iuuqh8i6uI6AjAAQQMTKRwfQ9OwA0kFkfIzhvjSeWbOq0OLWQTcEJwkdX+DlCS2oLYSMz3GYOI7BtXZ0Nco6xP8X5Mf4fcg4OZiufNaHlnZzebI8clfKLEMveE1NOWTYx2rKxkBaw+HdA8bzBe1fWKWZkDJHkSbG70//ESHbZqewFYjwbpMI4y5/uZJDuBGslnpmA5K1iJLaXsO2Y5pCOsAX5fYOZH39frLOra5CyBNGM/IGoZymC05J9FLbYBHWFYRyyi+4JLBG83FAtfL01iOh5FxYndJYHaYzmDN3dKA/tnUCZFSFlXP+KtvuOkJNPayqqYWsphY+EnHkpV6x/T9Z+NAd8ryZ3CTALJkxR5Hln/7DYPo+jQF1EaGdBqYr518RKsmolopNKcmkNvJHUAh7u6gKaV4NF5A4RMsNMHmFUU5wsjOcXm4fTIMLCU6+QgtJ+IHVK9DGHhai2VUodXI8pU2Ko2vT4umrcXH0JIeEyGMhAT+tVYhbHy4PUBnLacigppwL2qtqan6niB855Juw/unagXpeqm557OxsQsochayevt78tgjtvt5kZzApV86/pkGirwaoIlJLuUBJrKxtbScS2SJPlAOQbgRE14PTG51CihxOOU28BryWvf0jyDknSeTF0vj12mcn0fFR0TS8liscxOOfbFCZnm0cQJObBenhnhdYqaByhX0iNzVXrvR1DE/lcHHp26Orx8pzqG/Y/yvzSj8TUuYotLzTlpH233cjx2S//OUqxXpkUDYwFXVOSYcSQrcNrcm6sJa+guYZrhU0kLzCiS/6tx4NVeF0b/HDCXMYA+jQ8ChyzE4SrzFtaiI5+fa+wZFqbuxe7/krgA0rhFgdRJJ9pndVZcxNOWUCHfV98zqE25d0i35z1fCq1eS5NDLpzjLL+8SbkDJHAcFJFgN/vaG+9edddOnCu07rsRGKDOWKAkxGy4EOJqmU4DtC6CbySAmFBqC8wAkAWNctrETBaQSD5vzYWJGsx2tMZTgRq6j3+oSLkO/xe3PPW8kk+xNaCChVlfRcyZ2gDaCSdgR1O/qO4bS4a9T61wdVqS3PqcGx/zGVlDkKIN/0zVZ1881v3gtGrZ315DOwCihFEsp5C+GkKxvhG5TSdXJJRQ2nLT0tNKh6yYAT7AdP1qtEv4yPp8yZ2uuEcmI4rWWISjjJnFVuCXc3VcU/A/sCrAuyyHmOwY5gSKYLzxRau6Bp3qIuURveGJyku8+fbVCp7GsDq5iQMkf+xuWvVgk4/fHr2XvSTk760X4coZzor12oisnqASY9hHu/K1lfqq+ZHxP00K0ww7Y8wUkLl3b1Cy8xcEJOKeVBf7o8JYFsHpaH1zuG5E3h1ckdVKO18A/OdgOoZOdPTVUZIMVKisO9tSuHJifJc2xq86Cybw6raloQzOHbsJ3sK06ajDN9A2xH+qRmosQDvbMLC07e1BI8Sgjh3m4mDI96TklCqRjB5AYnViQfD47MpkiKegqvk5YMX9I6SFdNcmbMyJ8fy9vfcx1QKRqoZOinQorDvXkMqiVdo15f83iNCHmufbroIbN+zxw+wOl4P3HC2D7v2wfbIomdTY70U6xFoJb03BJsAahf4xDOCKL0YoaScUrldGREVKH5nHxaqWM4DuLX8XaXUFIYmsg1yfcLxxlNA0SyvCDNotlApYV+M1qHCEgZQj+Rk1osIBWeufSxmLnbJtavIM857J5sXnnmuO5I3TxYKCfbsX4fYO83bUslpdDDOCg0tFVZ11Kta0uJLxEh3PWsBKcfjxG5npJiI/hoUIS6UqfBCeHdVb49/n4/UaBcWD4rzxwVipsnNwtSZrYJVbRVP5mTkq7ziweWNh1LtOP/ifPtSP/b+Dww81HmyDGkEydHxum+ibajYn83Mmy9VDircTKMO9BbVP2LhLfhE78kQclNOfFru8bh07fPxIkkdEkAlGulTrURyJDuj4kJNK5BZfH9Qgkr61bMEVToyvBs4wAlpYXYQktAam57SxZat3zzTjtSzgz58ur+3p3lOfhSr7iy5i7I5sg2WCGpId2JfinY5VaGdAUJJx1MMr+EMO7DbmRdUFMN40qoWvKmnnDh/zkpgZ69r/Au/Dwnw/n3Qx3h9eB1yXwTAPr9s0UHUE9VJUGF49gGlZUJDwVCTaHvlPP06nZ2sYvyKVGYvdd2tH8NeS4u6Bxxu3lFmsNtXPuj5z9YNZ3H9tzq7rgFDya3/NKOjmSdmaSFcUnuHQFKOJzk7WkP+d9QPqcgQjrkvZa2CdL9TcaVunOspnwtMi6M0E92WuCwT3mKldyZ5U3J8flAR/qxgQ5s7566vze9Pbr60tntLOXk+Wi2EDYH1FJZLbRrTqfxSdbfWZCqySuYNj2stiiZmnhTqCVPOEllsqxdcLElxWVXAtgZYLR0avkmGXrCxX5idHSxKzvP18yvRznzeLRif6EOpa1toWQe6GX/86OeyvJuURz6Wf5Y0i16gNF68Er/RDPUK7Uh3QEtpDvebyVW6RhK9kJVTO+2Ur1L024+MHnaCTZ0Dyt2OwFUybknY9zyTXJF8eDwqALrOVVQcHqC36uvx8XRtekJSurEWCVrfjU6N7OOMqlZcBZatSx9JII4tPtked+EmvIcXfhIpLmqV9pGxhnN23S+T0UO6S5mHR8gQrqCWKXzDqaWDKa4EmkRyI+d4LOhkcV28Y/R6ummcmiZ6qFA8bXi4cUqCXBCaPdU/cr08/NwsidR6rQq5JxVRfmwn0WBAn36Pj/npKZBdnRA0Mph5q175t5/4xx9suY9ZZb1iTdVVKkJ6Y5Jb1O/3nRuSIElwnNTTDc7mGRYl6klnItDNYn9AbWQDuUpToOFIF2rqwOcdvcPL3Zl57mqiEWES5qTXbzOuUn0bncLXqcyuq6am3qiXmU7CpEXtrdg2/dvlvWKe1ies3M7WMyEeWkY6XsHSm/TLultsh0uQB/TIUOOKeXmB5MRTgVlcrzRi/0sh3R2Q0hnDOu29bKUKOU0TCuzSZuW6GbLWN5O7Y3Fr5MBVUnRVvoUfk+zJjCk5rYNw/bvq9c/VdMP52vtv/3ttjkdIsyE+S3sbZLlKhEc0l3DTrYFqpokmN7rqPfnvtnB5C3vBOUyqIgBABAO5XBybvNAvZbOOGVx8vruYSWm/k++5oWt+DXPcJ0Had4Bb4SUg4+OyQ8F0rTWIZde6B7TV57Dzz0YaKqoWzqkO9HvcZe3qeDyTMJg+UE31S4w9dYBk9GMKZ3iw2tXLPJVOtUVHpktpDP2nHqna6gobSkRcNJeMwqRhe1hquoX+2tyAo1rmN0vxn+nokFK3GZI2QGviQ8CUqE7FneLEZsvrBySdBs/xlRRtyikDtDpwTKkUwoETGh1gk0G5tfUw7lbCUyeod3MIg7t0IVgYmM/ujwl0U05eZbYvPlISInoOSVzZAgxscKJfBhKaxDSfTMuVry+nHJiRkjxdDLE7E83EPsCpvL3Bsvz+JtxcWX5by/DwCvDRzNxfhOHdOKTxnaqbxKHdKSuzt1ASOeZAAecljfQnd+3EpiyrdrxhfbBgAihUAo78awnwvl37UFLXoO3yRucVnYOKTEN8WQN4IcD1VbCV1NUdXcMbYTzoDw1SMnbQkVh5Y+//pA/GKrmdr73q1q+DD++7GjMOqbKKtlwOqKHdBPdy1Xyp5yy5ZneaX7Lg0mqFVFgyxfa5AdcbvHCAJRsjzKUf8eMJgFuUEr36OAgHeKvdgguMX3OZQL/89HRApx4z6CgACsf8mKKAVJOnnb4prAC+ONzcU/RvOQEft54fg9CaG7Sf653HYyuW1EFVt2KptIqSYOo1W0c0p1AfZMI6fKpmrLlmXY/InJM6bfAylxeylgACSSfDw4rPMOjEXYA4Mkx0UJ1pHlZZDDCqTh2iLme6vvpebVbZ6qmOjf3tPhod3CFeZoas6N8hxZUxd/u4JnB8zJ/aHzPYe+ZGU0DjvFj1jDEJvDzd+afS+L5r9xgxY8z1VUxh3S1rx0fYAjp8qGaPDpYWg/2UQt5tR7ftyqYPNWTDKNWdAopFF+RMWeD3uUOL+FcSYcT8mRQl1dSVLWJ14/3bNUj+UvaQ0GhVg8/u6NvuMIqzI6/GyuBgB8c8zQ/WXjRnm1YWYSAmAgJ+ed/4rmT5wyenXhaONzLtvLH92nKygwFi2QYOhBM9Qjpbkw1IZx788FbPpzzpp6M/ZPmtQh0sxbcCKTkTinScImult5+d0mGk3F18cWHg0QSPE17jYDIS22D8l2fqL0/yoWnxcYOCj+nkq5OJ78XTlZnDoaW49MhkXaGWBbCQLnP39h6lUU7GXRtGFWnUhbPU/y911k9DRhVt2IC3/4/z2vnyfuCyj7f1GKCqnBDuk5lOKQ7pagdCPLVVC5bOLerc7FvQFCs4R0uNobyxckJNL9lIA1gmMgl9BsJ5SSYZjcPEM5qLL+n5eIZM+5KrO+td2/x2wgGGVbqZLdOqJy5DPOhNX1f6VR9UxVpdrMANxXruTAAWLGCUvC7h9asAGOnouWr4J8CsBxiLz+G1dMN/OgpnmPurUxj6vmdfby+/xtjGwU9NqFVTIRJjaIL6eq4h3Q+KifjduDy68W1b0nbgC+2AgkoHNd0DRWhDFSBhMyYPHSVNO7ii6JehHJQHAiHMme6gyk3OJUkK4EEyeERUWqubKrrdcMx7qsNw9iRYVtvi7qR6NTEbD238B7gw+LMEzFiN2T9OTgcBIAer+eHo6L6qCo4RtYqbx9e4x7HsGp3KyOq30Wjqt1JI5LvoIEJ/84cmPCfw32j/2/qoIR/N/5gbL3/mTQp6JBOrtId7zf5RoyX2Vbn1rUsVeHc9QCFI/JCMGgiFANkcCHhIhypXVhjNFgZpwQYVrXweIQcaIeCC81TMeX0PhtNmGu6lAwTpuhGwH/Lj8/F6WGdVQuDJ6CNcK2K+c7DoSOpfE7jxg6/jo8Xhs/xjfxoRJ1KNKRWJcBIXTWsWY6GVr2TBiX+h4ZWu0usAj7RIICeahyiTGwVq8x4tIaysN99juWjWtrffLaTfd3UnrR9wSDav+pZOrduHF1ZeH87XEffvfKYGeIVgvHyOJ0anL+Qzqia4Gf6pCdZZ1XVXeCldaZP926EhNpB0zeUksCsiYsUagbwwdE4cR+sAs/zBQXlhYsZXibPcCW3DwBj+cqGElC+IlXTLA6/PHN0l1jV4G/1xWFv3HFmqSGH5Vm6gw0f+iSXp1EajIYJGP2XWB3Rs00jaEGfBrSWofPJ6vF0atcCurD/Zfr55Nv0+7l36a8Lm5TL321TUn/aodh+20uZf35gz7r0MWX+vsdKv2xoJcDk+N20HxTUOL31UdknvEo6wKIW+CoMGSX/uSZWTSsfLNWqKbckubxokP8BZHA/PukPDIsSm3Ku6Bwiil4xYZhEiHJ8VDT9NSlBPF6EcR67qfhS97ejT/F2JdDDLwYJVJwMv6S6+X1i/lodyzo94/brnnDCvoL9q5ZnMN1D41tE04tDm9D2JcPpxPY5AkJXf9hO1t/2iJn2007++j268t02uvztVrr0zRbl4teblYsXNtFfX210XOX7/zq/LvPXTxc2wDX02w97TMVUsEnwFBVOn/d7hr4bAcDYjRsO5BlOEkzHBooe4OnTTShdr7wlTQMGvsYnPcCDCwjKyjjl/UgUe0Ipr++xsWUKau+KuyuBBMnRkdFuIJFweqahb3CSfaymN/H3Gua6/GcJdHhUHB1ZO4F+/WIdpf+8S4Ao/ZddxKqIGD4AjzoZQgAR7tOmckk9OlIZYpcubMy4fPzlerh+Lp175+8mTQphhAYGz6ieEGFfOO5BunZ8gFBNvgBKPFaGdFihe/1+VTWZgMoTpIygEnOax9Tu9+wy4Ot7K42h2FsvPythhdHDCTvBGDdgQFh30cewzmhL+NhQ9JztvdbeB8fMBLIdXUKXf97DENrgApELQlBJbrcvAUzq0Xn5u610+ZvNdPnUiocEmC5sNLsgFOSwBIcIxRQWFFwzMjSMKlcMpPhIi/OXD3pSFgMq3Uf1pKsmdBwwoZTvkC+n6atSytlKUEX0eirqDQ685Ybgt/LsOSU7duY1IS6fD6ppGlRTLuU7VuT/ZiRT2oQIurJpJF38YRerH3co6TAyTheYlIsc2tl+3kGXvljdBdcPP94EUyHAqax2TIkKA5wCsrq2qirA5ItyshoT4cg1vdHYVE0lGIKwNPwyPl64okcW0yYHcncYtDN2eGntAvWERYK8qjvxfMZcU04ueTGTyDo5mlJXdKaL3zNkvt4E6GQHkncw2R1/7iUO4Z4UYPpxtwmmwhqsmm7j+TngVL6cv2P5xCZEZwdT6sG++VBNA8j6cXe1T5MJpRKt0KBMJj2Qv6X6ggrpELYhwW+bkV0xyi6Y13OIG7sx4PFQhbmFvDqcpsRS2uL7BZguquDJEUxQSiLE+2ZLlvOv9+nSl+8uw7XzV9ppM/ldOKopuIymmqrzpNDAYIoKC1XObOqqOD8fSOmH+lGelJNRNcHXtLqpuUJ3M/Q55//PCw8H6QXJRRXeGUtuYInwpprSvBT+5hbOAbDIXf02IV78XddbwRR5J3RgnVOTLp18gy5+t12FUzZAieS3VE1Z9j9YMX21fq+8hi4ff9m0DBRySDceqqlShYCsDk2SFPuJAZTu6fLOS1/wo/3VJnJzq5fKUpWbCU6y6n+9Yfuqos454ajVven5Js9Ooqdy6SQqE+Aj1f3uXN0Y8tBZNV0CKiWOLh9cRBd/2KmGdjmHc3bbLzuxMvft5UOL7xYJ8C9Wm6qpCMK6Q4BTuXv8HYueeVBBSJd2sJ/vpku5WYFheydzltxGeA5t77qiXLEzrqihqNcIpvQclv7Rnwqv0QhQ6ZhHAhxggjfMkUcwiYnfMYNDu0mRdOX9SXTxx90iKe6mnAwrc/A4MbyyLp19O1mA6fw6M89U2Kt0fIzjaWdAER+Vo2sfUZST+QzpoJxeuFff4indhECJD+uwR9zY+qr6KOp8E2ra5CqdN6BI9YSdiQEh+KHk1uX4GooP5SdoUGcMDfMCJ6GcBJyi6Oqm4fqK3aXslgHkmxzXfuPvn1/bTYDpqw0mmIoopBsB1eRXKdDerGF89pW3vIZ0sA+838XMMd1kgIK3CDueFMXmn0bVBPtAlhf7gLduDlBXMGli5Q6bHcCYCTWFfBSS6XYfweQGp8lRlLqqm8tO4JEAR57J+dcHSIDPEytzP+w0dxcuglBO+pt2RVvUkG7K8PsU+kIN6fKsmoz9mt5qYibCb6Ip804o5ZAFwIWVFJfPK5Ph3z3jXuSbE5yMCgr3oWD31wnx4j68dmPBs6+2FWknSFveki5+954rIW5IgGchAX5+3Ufyurl0aoWZAC/kVbrbNNVUmefl8JAQCvQLcr7/anuFzgzyDU56l8u+bl0uTcd3oluS90aMk4W9M/FPRRDaGbt1bunl3sYktw4KRkDhiNeLcFTmzW7k/RVwSoml9MWNoIxYNW01gsmR9vNOuvTF239c3TxSbNZ5+cQrZgK8CEO69nCFB/kFOeokR9Gf+3pT5tH+vod0KFXZ2alUg8kzFMHRPst7XVdJayGMfA1aCBfWDjESTPAroXmcN5j7UuJTYG553U5Qiy6dfhN2AkUqJ9xO/2ot8lFNNMuAmWcqEjgFqXDikG4ZQrry5fztQ7rUUrCZQdqhvOeb3Frwyu4DpdwRLrfQRi+i8+MSxKe8rQS/J/L1fj0utlBKWWSeCT4k7Cfn2bOpON+XdAOgLh9bDqe4IvJO327LUn59j65uGTVB5Jl+ft8EUxGrp//jeTYyLJQqlg9wrpzeXJGucDOku7EVsN8nJVKHWhbCCujTzSMpa2Z2H49PHQsMsyCVmOcOMdg6vSBbqEgwya4CaK6H35NaQnZ4djnFo+nK/rmK6nXanGX/fQ9d3f7kTpMSxWchSBau8KBgBa7ws5u7Kg4fXOFuIZ3Y7sk0XeKio7lV6I3uMXRXhSDy9w+m+vGheU7WGqEj+2hDZSA8xMRtm/Z7oMxSp954XstYx4ZWv1Mf8hcrdzcCKOPGC7I05sjIaNElIHVqycnBGWrslCt7xsOI6Uj7ZTdd2TPhj2ut/1YR10n6vLpmnqkI4XS7FtKNFhaCioH2Vo0SFKvnrrx5CelkG963m7pCulLuugachjSOID+/YKrM87mWkURzquiwyWtHAjwPVNiP4xPoq2cS6AJP3AZA8Hz4Po54jIRUfi56zyV7rKIh/DKaHn2BlCwnkZYBLP1DMTlnZ+/jXfzdSYWdQLFOjlKubhquXPxtn5JxaB68Ty1wjaS91t4M54rWQhAiV+o2ahYC+7MD61N+QjodaEtdxsvSHtZB3XSsbaEAVk3lKgbRi49GE81PEkonr88BlbSmdwy159CwWlQohbPCjQwJoep8u1nVMOp/XzjN7xhFHw2Po18nqrCyz3JfUs9Xf3NtrziUlMDcKFr4+rBDjOx7DoMkwLSkdZAwedoNiqkk5SR1r9PECEpdN8BOP22m9IUNZ+D6uPJhigmmYlJP/+P5a0RoKFqkODcvbqNbCHxpj6J3IJhhdiDwBqfylYJpbocoonkqnK638YBQXgyaWe2j6G4OC6G+OOwWM4RncGAwBQaoigwTv6NObCj1aRhOh8fE64DypStmToBCF0oYJRGWYRVP5o0krIybMEjHN4CEkBCh4SdDIoWqk62ES6KdQpSwTGflNCnS4Vj9KKWu7bfPpEPx55vug7cpNDBYiY+0KF+/95jiOOFDyYpxlW5L21KvmjzDuqEc1lWqHCxyTl3qWgQ0Uq8DpzRNMZ0eG08RrJSQTMcEgKCaMPE1nrMCQ0+CK4SB9d9yQdS6ukVfFcxr4l0er2o5rDQDoAAWhHmnxsTQax05PNV6eQM+UFRyIvyDPwqtTwAz1Lilagl2mdAvkWByqSeFIeWgedVQBNwR14d1SoypmorR3/S0bCzXpnGi4nOuydhUbsUDuivcTIircFrZI0YABEABPD4ZEScU0dVcFAS+B4W1qqf6s1isqBoZSuv6xNC5p+Ppi6fiaf/IOFrLX09rG0U96odTlYgQEfIBYFBrNh+8Q8ZdTkT+in+3Z6uRdM2ZjYkyEdTCfTw4UjjKMXfz3D80ks6PjRUtdVGO4tA2arhRg2QRqieFAeVQANPpSY8KOE1LNEtUijHftBVwqlDO3z6uf33fGssZc024vah2qTdfGpf9cYEjD1QjOlSACeBAngjwkf4nbxeshBNW+gAn/FybGhZxX5a2Yuec7QIJ7mtZzUJBHOZVZJWGUNAYPuY1z4THz+affbZFJP3wfEI2QEkLQ4ZQUuomC9gcQU7ACApLlpHcNFDyVE5zk3G7r1ihm17FhFMxGTD/w+HBT5GhoYCTc928Vj6VrLjtSWfmm3JUT8u7RouEeExYiIAHlA7eJ8csdcne8wIWO6IwfD4eHidyS1BEOL4/NE7cj5/BxX9xipqXQsIceacIVmdQWccejxfPnZbHkO6q9jqhzO4sH0T/vDuIRj4YIe7zDEE9N19INcy0qYm5tjy5CabCU8CJj32EcjLhVGz5pjoWNaehRISGKCfXdyGfWqQY801b2+tbjJvTvbwCygaravcwoGI1QLWraRGWACgcqVq8VeU3Z6UF5YTZslqYa5NJDUx4DoR8yEHhefvx74GqSsvDbixGGOK5ACQ/Lbk+6iF3OJWS+kQDnBI1OJlhXXHlmwYhpPOvFGhvVCtWufJZX8o42t/3fJPZjjdXzxDCHMDn0boWASgoKEAgMTyEXukWrSssWcqBx0IdQSVtGRAroIOcFUK8p5qpXilADSUx9yeq8MKHDJTTySd9U02yVcpvHH7WilHDT6iwDf1idTiVhB1obEVTn2hUTn1N5VS8ymkF4FSxfIC9T/saCp121dP5BCeEdS/VN1fqctk8AODBcdgDEcJWoDX0E+B5MCmMXubQ79vnEly5JM2vBF/UxNaRYsUvKlR9/PS2USIZnhAeoq/g4TlffyzaDSh5MXrKkO7NHjHid2DVD36qn8YnuG3fXdy7wwDWMKACUhmF9wEIONlpXjKef4QJp2JNiqu7rKB/09yxjX3v3yST4Qf6kHWe2Ss8t7AEwMBF5WD4ACJJESH6ShwS2QAD7uvVIJwWdoqiPUNi6XNWQVBHWJ1rmqwqJNg+cIS6gcoBTAAmGDEBtLyGc56AeqBKmJ5QHwt1VsyqyaiY8Poeqxcu/uYnm0boifpCUk4CTnwcpcHJtBIUIZDkKl0wz6vCfFkhwLnthYfJJzi5bZrZ1Qzn8uK61vImuPC/Y5U0oVWkCO0ABAAnWAupZN4HiXDkqKCQ4i2qVQDqCRNqCWBKjgylnYPVEEwmynPKyXiGRKL8hZUZlBheQ4TmoTKGhsX5P5W5MCwMAN4wncIy8cekxEJRT+nThZXADU58nwmnYgjpGgNM/OnriI0IU05v7EqOfGwBJfJN29qbGxn4kNiVVgIA5ZtnE2hx52hhFQB0pLFSQkrW5eHiBECQs8IxXDNn4mKFEpPhmS2X5LoxAS4ufAbTu31jhGLC78ZzA5h5DQ2LarXz1W7Rugu+673h4r0rLOXEMDKVUwlIho+O5JAuoHKgvV61aDImw32yEQBO7zQvdclw/e+cgRM5yecWKGkaJLI0SAEqZ8bGC+PleAZEbw7vACyEW5i4jdKUOR2i6PmWkQJigArCQtgUmiSF0Tu9Y1Q1NC9J5K6k3+iqYblfGC1ZjSDZDh8V1BcUEy7+Jhw6YhUww7ipZQmwYkxpEykgDWhPah1ZmKuIhrAu0YRTMcJphbY/nb1bq6pu+9P55gznsO7VRqUqGe5SQQmUPtFC6SkAc1WfISWXx6UZUyTA56oT0JIXaKrx+wwefB+reDB3It8EuMi8VWMGGYyUqK/7c3KiK8GuJdnhjfpgWJzwWuGCRxiHI8LDs0+r4VxJ6bEk4fRE0wgBT8AJnrG8GkwLKOdkJsSLIff0WZS2mcHzgxqQT/3CPVfqltQpdR6n9KkMpPn1KXP7eHFMHx/C70GMqqSEmvLNC2UElbG2zbPmTn4fFy3apjzeJEKon0paqCeT2rjvvoQw6sZhEFYIh/PsxUBqlBgmcjdQXhHaKh/6TCH5DjV1tQQV5ko44fXL+kQYTnFfocJpPj5oqjxjKqeiVU0yGV4enQjQ+ZLh5HxlclOFzg3Je5sUmQxHGLi/V+nb1Zfhkz4pgjLe6EoYStoflLXvBbIteYiVVBh/L1J7nKqm8tpcLqf+2N76Zet5K1ZDUEnoGxUd6kquh3h0LZB5K5l4x208BsZLlNg4SxiYjHAa0yRCf+2wUHgL6wqoS6gKpwUCTuNNOBVPMjwBuQpsnBnkH6TsXNaWfGmT4rZS92G30pdrApwmhNG1XdPIOJTMdLKfeFdAKx07eUwI1UI+l5oqiHa6nnkrEboxpGA5WNApShT+YhUwVOtcgAtbXtz4v6O1Ci74z0bFuTWpK2nlJhJOSNLnppxkHaPDS5dQX3pHpU8zwinxebFaN81crSvqfFO78JBQtElxoC3vmU0+rtRJOCHftKtz6TNfanCyn9muQslhJ8VpdwOV4/vDlLnteTXkmwA1Fa6BrWqew7685b1cF6HMSeFChVUBuaW3e8XQa49F0+uPxYiE+X4GEnJRMil+I50ziwpOWM2UCfFlXVw5J/makZ+DaRQtZmDU/P75BL3uUJntS+O9RKNyel4rXzHhVMRwGhsRGkbB/kH2avERyh8f96ZraBaXRzDZjG1SNj3sshGUGvXEIezMquT8/bwKJ6dTPSqKgBSOuppK/4vsx9dSxqo+GtRCRUgoFZgRVOn57F5prD27aug3rhg6F8hEulzFk0BzCyVLaOH0xn6xAk4IWbGSKVsdp2nF00c4rEV9IcJU5NFgZsUK56D7I0SH0Lx3BjXhVJxwul07LtNq6rKaNYhXZDjnM5xgI1jTrJR5nJL4740m2wvNSLFnuqCkTZK3nQ4xjcP559eUtf9lDvu6qc8jQBWu5usEqFw5Kp93rPXSNUBufmCcOXUZKKlbVQGmx5+IF6uKCO0617HoO9jI1jLY1QbdFKQxFeGr9IvhuGNQrA40E04ldYXOtbvvDllT17Ntdco6McC3BnPGbgRvNNY3NCg9yfBwynhnsAYmp7tSMoBKnU4VVIrTHVS/f0lZn73Kiqo3WWfXpPSJ4SqspsR4hH9JN6SsCs3fhdflg8frRspX4L3CyiOS+jCdwrhqNGI+XMOirz4ip4b+6nGWELFAAGOp6G+Vp97tJpyK20LwD54nRU3d3f6Ox3vVVXwtW7EZPU7LGhiUU2IpSIZXFbaBax8u0EI6hxuc3BLkUk3pt53Zwj4Bqis/k/**********************************************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" alt="Error" style="max-width:fit-content;">
		<h1 style="color:white;font-size:3rem;align-self:center;margin-top:0;">
)";

const std::string ErrorTail = R"(
		</h1>
	</div>
</div>
)";

std::string YServer::GetLaunchErrorDesc(YError error, ELanguage lang) const
{
	auto find = LaunchErrorTranslations.find(lang);
	if (find != LaunchErrorTranslations.end())
	{
		auto find2 = find->second.find(error);
		if (find2 != find->second.end())
			return find2->second;
	}

	if (lang == ELanguage::English)
		return yutils::Format("Error %d", (int)error);
	else
		return GetLaunchErrorDesc(error, ELanguage::English);
}

YServer::PersistentClientInfo::PersistentClientInfo()
{
	BanTimestamp = 0;
}

YServer::PersistentClientInfo::PersistentClientInfo(uint64_t banDuration, const std::string& butWhy) : PersistentClientInfo()
{
	Status = butWhy;
	BanTimestamp = yprotocol::Timestamp() + banDuration;
}

void YServer::PersistentClientInfo::AddAttempt(const std::string& client)
{
	BanRule.NewOccurance();
	// If not banned, mark this attempt
	if (!isBanned() && BanRule.ShouldBan())
	{
		TLOG(LogY, Important, "Banning client %s for violating rule '%s'!", client.c_str(), BanRule.Name.c_str());
		Ban(BanRule.Duration, "Violated rule '" + BanRule.Name + "'");
		BanRule.RelevantTimestamps.clear();
	}
}

void YServer::PersistentClientInfo::Ban(uint64_t duration, const std::string& message)
{
	Status = message;
	BanTimestamp = std::max(BanTimestamp + duration, yprotocol::Timestamp() + duration);
}

bool YServer::PersistentClientInfo::isBanned() const
{
	return BanTimestamp >= yprotocol::Timestamp();
}

void YServer::YAPIEndpointHandler::CreateMarkdownDocs(std::ostream& outDocs, size_t indent) const
{
	md::Headline(outDocs, Name, indent++);
	std::string endp;
	size_t paramNum = 0;
	for (const auto& frag : RelativeResourcePath.Parts())
	{
		if (frag.Idx() == std::string::npos)    // named parameter
		{
			std::string label;
			if (frag.Name() == "*" && paramNum < ResourcePathNames.size())
			{
				label = "[" + ResourcePathNames[paramNum] + "]";
				paramNum++;
			}
			else
			{
				label = frag.Name();
			}
			endp += '/' + label;
		}
		else    // number parameter
		{
			endp += '/' + std::to_string(frag.Idx());
		}
	}

	std::string methodsText;
	if (Methods.empty())
	{
		methodsText = "any HTTP method";
	}
	else
	{
		bool bFirst = true;
		for (const std::string& method : Methods)
		{
			if (!bFirst)
				methodsText += ", ";
			methodsText += method;
			bFirst = false;
		}
	}
	outDocs << md::Bold(md::Italic(methodsText)) << " | ";
	outDocs << md::Bold(endp) << '\n' << '\n';
	outDocs << Description << '\n';


	if (ParameterSchema.GetType() == json::value_t::object)
	{
		outDocs << '\n';
		md::Headline(outDocs, "Query Parameters", indent++);
		outDocs << "The following query parameters are parsed:" << '\n';
		outDocs << '\n';
		for (const auto& member : ParameterSchema.GetMembers())
		{
			if (member.first.Idx() != std::string::npos)
				continue;
			outDocs << md::Bullet() << md::Bold(member.first.Name()) << " | " << JsonSchema::ValueTypeAsString(member.second.GetType()) << " ";
			outDocs << md::Italic(member.second.Required() ?
			                        "[required]" :
			                        ("[optional" +
			                         (member.second.HasExplicitDefault() ? (" - default " + md::PrintJsonForMarkdown(member.second.ExplicitDefault())) : std::string()) +
			                         "]"))
			        << " | ";
			outDocs << member.second.GetDescription();
			if (member.second.GetLimits().Limit || member.second.GetLimits().Interval > 0.0)
				outDocs << " | " << member.second.GetLimits().ToString();
			outDocs << '\n';
		}
		indent--;    // end Parameters headline
	}
	else if (ParameterSchema.GetType() == json::value_t::null && !ParameterSchema.GetDescription().empty())
	{
		outDocs << '\n';
		md::Headline(outDocs, "Request Body", indent++);
		outDocs << ParameterSchema.GetDescription() << '\n';
		indent--;    // end Request Body headline
	}

	if (!ResponseSchema.empty())
	{
		outDocs << '\n';
		md::Headline(outDocs, "Responses", indent++);
		outDocs << "The following HTTP response codes can occur:" << '\n';
		outDocs << '\n';
		for (const auto& [statusCode, responseSchema] : ResponseSchema)
		{
			outDocs << md::Bullet()
			        << md::Bold(std::to_string(statusCode) + " " +
#if BOOST_VERSION >= 108000
			                    md::Italic("(" + std::string(boost::beast::http::obsolete_reason(boost::beast::http::int_to_status(statusCode))) + ")"))
#else
			                    md::Italic("(" + boost::beast::http::obsolete_reason(boost::beast::http::int_to_status(statusCode)).to_string() + ")"))
#endif
			        << " | ";
			if (responseSchema.GetType() == json::value_t::null)
				outDocs << responseSchema.GetDescription();
			else
			{
				outDocs << "The body of the HTTP response is a JSON value with the following schema" << '\n';
				md::CodeBlock(outDocs, responseSchema.GenerateBlankConfig(yprotocol::YRequestHandler::MethodInfo::MarkdownOptions_Return));
			}
			outDocs << '\n';
		}
		indent--;    // end Response headline
	}

	outDocs << '\n';
}

void YServer::AddHTTPListener(const JsonPathPattern& resource, const std::vector<std::string>& resourcePathNames, const std::string& name, const std::string& desc,
                              const YAPIHandlerFunction& handler, const std::set<web::http::verb>& methods, EYAPIAccessLevel accessLevel, const JsonSchema& paramSchema,
                              const std::map<web::http::status::value, JsonSchema>& responseSchemas, const std::string& responseDesc)
{
	YAPIEndpointHandler info;
	info.Name = name;
	info.Description = desc;
	info.ResponseDescription = responseDesc;
	info.RelativeResourcePath = resource;
	info.ResourcePathNames = resourcePathNames;
	info.Handler = handler;
	for (web::http::verb v : methods) info.Methods.insert(web::http::method(v));
	info.AccessLevel = accessLevel;
	info.ParameterSchema = paramSchema;
	info.ResponseSchema = responseSchemas;
	HTTPHandlers.push_back(std::move(info));
}

std::shared_ptr<YServer::PersistentClientInfo> YServer::CheckPersistentClientInfo(ThreadSafeProperty<std::map<std::string, std::shared_ptr<PersistentClientInfo>>>& map,
                                                                                  const yprotocol::IntervalBanRule& baseRule, const std::string& key)
{
	ScopedLock lock(map);
	auto emplaceRes = map->try_emplace(key, std::make_shared<PersistentClientInfo>());
	std::shared_ptr<YServer::PersistentClientInfo> banInfo = emplaceRes.first->second;
	if (emplaceRes.second)
		banInfo->BanRule = baseRule;

	banInfo->AddAttempt(key);
	return banInfo;
}

void YServer::ban(ThreadSafeProperty<std::map<std::string, std::shared_ptr<PersistentClientInfo>>>& map, const yprotocol::IntervalBanRule& baseRule,
                  const std::string& key, uint64_t duration, const std::string& butWhy)
{
	ScopedLock lock(map);
	auto banInfo = map->try_emplace(key, std::make_shared<PersistentClientInfo>(duration, butWhy));
	// if doesn't exist, ban from NOW
	if (banInfo.second)
	{
		banInfo.first->second->BanRule = baseRule;
	}
	else
	{    // if it does exist, extend ban by duration
		banInfo.first->second->Ban(duration, butWhy);
	}
}

bool readFileBytes(const std::string& name, std::string& out)
{
	std::ifstream fl(name);
	fl.seekg(0, std::ios::end);
	int64_t len = fl.tellg();
	if (len < 0)
		return false;
	out.resize(len);
	fl.seekg(0, std::ios::beg);
	if (len)
		fl.read(out.data(), len);
	fl.close();
	return true;
}

void YServer::OnGameResourceDownloadRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() != 2)
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
		return;
	}

	ScopedLock lock(mResourceEndpoints);
	auto find = mResourceEndpoints->find(fragments[1]);
	if (find == mResourceEndpoints->end())
	{
		con->set_status(web::http::status::not_found, "No such resource!", ec);
		return;
	}

	auto res = find->second;
	ScopedLock resLock(*res);
	if (res->Owner.expired() || !res->Owner.lock())
	{
		mResourceEndpoints->erase(find);
		resLock.unlock();
	}
	else
	{
		lock.unlock();

		if (res->Size == 0)
		{
			con->set_status(web::http::status::no_content, "Resource not yet loaded!", ec);
			return;
		}

		con->append_header(web::http::field::content_type, res->ResourceType.empty() ? "application/octet-stream" : res->ResourceType, ec);

		std::string bytes;
		if (readFileBytes(res->Location, bytes))
		{
			con->set_body(std::move(bytes), {}, ec);
			ec.clear();
			con->set_status(web::http::status::ok, ec);
		}
		else
		{
			con->set_status(web::http::status::internal_server_error, "Could not read file!", ec);
		}
	}
}

void YServer::OnGameResourceUploadRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() != 2)
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
		return;
	}

	std::shared_ptr<ResourceInfo> resource;
	{
		SharedScopedLock lock(mResourceEndpoints);
		auto find = mResourceEndpoints->find(fragments[1]);
		if (find != mResourceEndpoints->end())
			resource = find->second;
	}

	if (!resource)
	{
		con->set_status(web::http::status::not_found, "No such resource!", ec);
		return;
	}

	ScopedLock resourceLock(*resource);
	if (auto ownerPlayer = resource->Owner.lock())
	{
		resource->Size = con->get_request_body().size();

		if (!resource->Size)
		{
			con->set_status(web::http::status::no_content, "Cannot save an empty resource!", ec);
			return;
		}

		resource->ResourceType = con->get_request_header(web::http::header(web::http::field::content_type));

		std::ofstream outFile(resource->Location, std::ios_base::openmode::_S_trunc | std::ios_base::openmode::_S_bin);
		if (!outFile.is_open())
		{
			resource->Size = 0;
			resource->ResourceType.clear();
			con->set_status(web::http::status::internal_server_error, "Failed saving the resource!", ec);
			return;
		}
		outFile.write(con->get_request_body().c_str(), resource->Size);
		outFile.close();
		con->set_status(web::http::status::ok, "Resource saved!", ec);


		ScopedLock lock(*ownerPlayer);
		ownerPlayer->BroadcastEvent(yprotocol::Event(*std::dynamic_pointer_cast<YPlayerRequestHandler>(ownerPlayer->RequestHandler)->ResourceLoadedEvent, fragments[1]));
	}
	else
	{
		resourceLock.unlock();
		ScopedLock lock(mResourceEndpoints);
		mResourceEndpoints->erase(fragments[1]);
	}
}

void YServer::OnUpdateServer(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::ofstream outFile(std::filesystem::current_path() / "yserver_update", std::ios_base::openmode::_S_trunc | std::ios_base::openmode::_S_bin);
	outFile.write(con->get_request_body().c_str(), con->get_request_body().size());
	outFile.close();

	std::error_code ec;
	con->set_status(web::http::status::ok, "Update saved - restarting server!", ec);

	// Raise signal 1, closing the app! this is done just to be sure that the response to this HTTP request goes through
	rtfwk_sdl2::pApp->safeShutdown();
}

void YServer::OnLaunchGame(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() < 3)
	{
		con->set_status(web::http::status::bad_request, "Missing information!", ec);
		return;
	}
	else if (fragments.size() > 3)
	{
		con->set_status(web::http::status::bad_request, "Junk resource path!", ec);
		return;
	}

	std::shared_ptr<GameEndpoint> endpoint;
	const EClientType ApiType = (query.Get("replay") == "true") ? EClientType::StaticViewer : EClientType::Player;
	{
		YEndpointAPI* api = GetAPI(ApiType);
		SharedScopedLock lock(api->Endpoints);
		auto find = api->Endpoints->find(fragments[1]);
		if (find != api->Endpoints->end())
			endpoint = std::static_pointer_cast<GameEndpoint>(find->second);
	}

	if (!endpoint || !endpoint->IsValid())
	{
		con->set_status(web::http::status::not_found, "No such endpoint!", ec);
		return;
	}
	else if (!endpoint->CheckAuth(EHashAlgorithm::SHA1, fragments[2]))
	{
		con->set_status(web::http::status::unauthorized, "Challenge failed!", ec);
		return;
	}

	const std::string url = GetClientLaunchURL(ApiType, *endpoint, fragments[2]);
	if (url.empty())
	{
		con->set_status(web::http::status::not_found, "Game not found!", ec);
	}
	else
	{
		con->append_header(web::http::field::location, url, ec);
		con->set_status(web::http::status::found, ec);
	}
}

void YServer::OnLegacyLaunch(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() < 3)
	{
		con->set_status(web::http::status::bad_request, "Missing information!", ec);
		return;
	}
	else if (fragments.size() > 3)
	{
		con->set_status(web::http::status::bad_request, "Junk resource path!", ec);
		return;
	}

	try
	{
		web::QueryString launchQuery = query;
		if (fragments[1] == "0")
		{
			launchQuery["demo"] = "true";
		}
		else
		{
			launchQuery["provider"] = fragments[1];
		}
		launchQuery["host"] = fragments[2];

		const std::string url = CreateLaunchURL(mLegacyKey, launchQuery);
		if (url.empty())
		{
			con->set_status(web::http::status::not_found, "Game not found!", ec);
			return;
		}

		con->append_header(web::http::field::location, url, ec);
		con->set_status(web::http::status::found, ec);
	}
	catch (const YEndpointError& e)
	{
		web::http::status::value code;
		switch (static_cast<yprotocol::YProtocolError>(e.ErrorCode()))
		{
			case yprotocol::YProtocolError::ERR_NOT_OK:
			case yprotocol::YProtocolError::ERR_SESSION_TIMED_OUT: code = web::http::status::not_acceptable; break;

			case yprotocol::YProtocolError::ERR_CONNECTION_UNAVAILABLE: code = web::http::status::service_unavailable; break;

			case yprotocol::YProtocolError::ERR_BAD_RECONNECT: code = web::http::status::conflict; break;

			case yprotocol::YProtocolError::ERR_UNAUTHORIZED:
			case yprotocol::YProtocolError::ERR_BANNED: code = web::http::status::unauthorized; break;
			default:
				code = web::http::status::internal_server_error;
				Log(Warning, "Couldn't launch game due to server error: %s", e.what());
				break;
		}
		con->set_status(code, e.what(), ec);
	}
}

void YServer::OnIconRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() == 3)
	{
		int idx;
		if (yutils::strToInt2(fragments[2], idx) && idx >= 0)
		{
			std::filesystem::path imageFile;

			int hostOrProviderID = 0;
			if (strToInt2(fragments[1], hostOrProviderID) && hostOrProviderID > 0)
			{
				auto host = find_game_host(hostOrProviderID);
				if (host)
				{
					const StaticGameInformation info = GetGame(host, query.Get("game"));
					if ((int)info.Icons.size() > idx)
					{
						const std::filesystem::path target = info.Icons[idx].ResolveRelative((std::string)host->RootDir());
						if (!target.empty() && std::filesystem::exists(target))
							imageFile = target;
					}
				}

				if (imageFile.empty())
				{
					con->set_status(web::http::status::not_found, "Icon not found", ec);
				}
				else
				{
					std::string bytes;
					if (std::filesystem::exists(imageFile) && readFileBytes(imageFile, bytes))
					{
						if (mImageCacheDuration)
							con->append_header(web::http::field::cache_control, "public, max-age=" + std::to_string(mImageCacheDuration), ec);
						con->append_header(web::http::field::content_type, "image/" + std::filesystem::path(imageFile).extension().string().substr(1), ec);

						con->set_body(std::move(bytes), {}, ec);
						ec.clear();
						con->set_status(web::http::status::ok, ec);
					}
					else
					{
						con->set_status(web::http::status::internal_server_error, "Could not read icon file!", ec);
					}
				}
			}
			else
			{
				con->set_status(web::http::status::bad_request, "Could not parse host/provider ID!", ec);
			}
		}
		else
		{
			con->set_status(web::http::status::bad_request, "No icon index specified", ec);
		}
	}
	else
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
	}
}

void YServer::OnSchemaRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() == 3)
	{
		if (fragments[1] == "games" || fragments[1] == "providers")
		{
			std::filesystem::path imageFile;

			int hostOrProviderID = 0;
			if (strToInt2(fragments[2], hostOrProviderID) && hostOrProviderID > 0)
			{
				json schemaJSON;
				if (fragments[1] == "games")
				{
					auto opt = gamehost::HostType::_from_integral_nothrow(hostOrProviderID);
					if (opt)
					{
						auto templateHost = mGameHosts->LoadSingle(*opt, {});
						JsonSchema HostSchema = templateHost->GetSchema();
						HostSchema.Member("games").GetChildSchema().AddMember("config", templateHost->GetGameSchema());
						schemaJSON = HostSchema.ToJSON();
					}
					else
					{
						con->set_status(web::http::status::not_found, "No such game host type exists", ec);
					}
				}
				else
				{
					auto opt = provider::ProviderType::_from_integral_nothrow(hostOrProviderID);
					if (opt)
					{
						schemaJSON = mCasinoProviders->LoadSingle(*opt, {})->GetSchema().ToJSON();
					}
					else
					{
						con->set_status(web::http::status::not_found, "No such casino provider type exists", ec);
					}
				}

				if (!schemaJSON.is_null())
					reply_with_body(con, schemaJSON);
			}
			else
			{
				con->set_status(web::http::status::bad_request, "Could not parse host/provider ID!", ec);
			}
		}
		else if (fragments[1] == "yserver")
		{
			reply_with_body(con, GetSchema().ToJSON());
		}
		else
		{
			con->set_status(web::http::status::bad_request, "Schema only exsist for types 'games' and 'providers' or 'yserver'!", ec);
		}
	}
	else
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
	}
}

void YServer::OnServerInfoRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	json serverInfo(json::value_t::object);
	serverInfo["version"] = VersionJSON();
	serverInfo["domain"] = HostAddress();
	serverInfo["admin"] = !key.Secret.empty();
	serverInfo["id"] = mServerID;
	reply_with_body(con, serverInfo);
}

void YServer::OnQueryModuleRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	if (fragments.size() != 4)
	{
		std::error_code ec;
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
		return;
	}

	YModuleContainerBase* container = NULL;
	if (fragments[1] == "host")
		container = HostContainer();
	else if (fragments[1] == "provider")
		container = ProviderContainer();

	if (!container)
	{
		std::error_code ec;
		con->set_status(web::http::status::bad_request, "Unknown module name", ec);
		return;
	}

	std::shared_ptr<YModule> moduleToQuery;
	uint32_t module_id = 0;
	if (yutils::strToUInt2(fragments[2], module_id))
		moduleToQuery = container->Find(module_id);
	else
		moduleToQuery = container->FindByUID(fragments[2]);

	if (!moduleToQuery)
	{
		std::error_code ec;
		con->set_status(web::http::status::not_found, "Module with given ID was not found", ec);
		return;
	}

	const YModuleQueryHandler* handler = moduleToQuery->GetQueryHandler(fragments[3]);
	if (!handler)
	{
		std::error_code ec;
		con->set_status(web::http::status::not_implemented,
		                yutils::Format("%s %u:'%s' does not have a handler for method '%s'", container->ModuleLogType.c_str(), moduleToQuery->ID(),
		                               moduleToQuery->Name().c_str(), fragments[3].c_str()),
		                ec);
		return;
	}

	std::string error("unknown error");
	try
	{
		(*handler)(con, query);
		error = "no response status";
	}
	catch (const std::exception& e)
	{
		error = e.what();
	}

	if (con->get_response().get_status_code() == web::http::status::uninitialized)
	{
		std::error_code ec;
		con->set_status(web::http::status::internal_server_error, error, ec);
	}
}

void YServer::OnListModulesRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	bool bValid = true;

	ModuleListOptions options;
	options.bSkipGames = query.Get("skip-host-games") == "true";
	options.bIncludeDisabled = query.Get("include-disabled-games") == "true";
	options.language = ISOLanguages::FromLocale(query.Get("locale"));
	if (query.contains("provider"))
	{
		const std::string filter = query.Get("provider");
		int outID;
		if (yutils::strToInt2(filter, outID))
		{
			if (outID > 0)
				options.providerID = outID;
			else if (outID < 0)
			{
				auto opt = provider::ProviderType::_from_integral_nothrow(-outID);
				bValid = opt.has_value();
				if (bValid)
					options.providerType = *opt;
			}
		}
		else
		{
			auto provider = get_provider(filter);
			if (!provider)
				provider = find_provider(filter);

			bValid = (bool)provider;
			if (bValid)
				options.providerID = provider->ID();
		}
	}

	options.providerDomain = query.Get("provider-domain");

	std::error_code ec;
	if (!bValid)
	{
		con->set_status(web::http::status::not_found, "Could not find the specified provider. Remove any filters in the query", ec);
		return;
	}

	if (query.contains("host"))
	{
		const std::string filter = query.Get("host");
		int outID;
		if (yutils::strToInt2(filter, outID))
		{
			if (outID > 0)
				options.hostID = outID;
			else if (outID < 0)
			{
				auto opt = gamehost::HostType::_from_integral_nothrow(-outID);
				bValid = (bool)opt;
				if (bValid)
					options.hostType = *opt;
			}
		}
		else
		{
			auto host = get_game_host(filter);
			if (!host)
				host = find_game_host(filter);

			bValid = (bool)host;
			if (bValid)
				options.hostID = host->ID();
		}
	}

	if (query.contains("bet-unit"))
	{
		options.betUnit = yutils::strToDouble(query.Get("bet-unit"), 0);

		auto rules = yutils::Split(query.Get("max-bet-rules"), ";", true);
		for (const std::string& rule : rules)
		{
			auto params = yutils::Split(rule, "=", true);
			if (params.size() != 2)
				continue;
			auto hostTypeOpt = gamehost::HostType::_from_string_nothrow(params[0].c_str());
			if (!hostTypeOpt)
				continue;
			double maxBet = 0.0;
			if (!yutils::strToDouble2(params[1], maxBet))
				continue;
			options.maxBetForGameType[*hostTypeOpt] = maxBet;
		}
	}

	options.hostDomain = query.Get("host-domain");
	options.bAdmin = !key.Secret.empty();
	options.bDownloadConfig = query.contains("download-config");
	options.bShowHidden = query.contains("show-hidden");

	if (!bValid)
	{
		con->set_status(web::http::status::not_found, "Could not find the specified game host. Remove any filters in the query", ec);
		return;
	}

	if (fragments.size() < 2 || fragments.size() > 3)
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
	}
	else if (fragments[1] == "all")
	{
		json listAll(json::value_t::object);
		listAll["games"] = ListGameHosts(options);
		listAll["providers"] = ListProviders(options);
		reply_with_body(con, listAll);
	}
	else if (fragments[1] == "games" || fragments[1] == "hosts")
	{
		if (fragments.size() == 3)
		{
			int gameType = 0;
			if (fragments[2] == "types")
			{
				json types(json::value_t::array);
				for (auto type : gamehost::HostType::_values())
				{
					json typeDesc(json::value_t::object);
					typeDesc["type"] = type._to_integral();
					typeDesc["name"] = type._to_string();
					// typeDesc["schema"] = gamehost::ConfigurationSchemas[type];
					types.push_back(std::move(typeDesc));
				}
				reply_with_body(con, types);
			}
			else if (strToInt2(fragments[2], gameType))
			{
				auto val = gamehost::HostType::_from_integral_nothrow(gameType);
				if (val)
				{
					options.hostType = *val;
					reply_with_body(con, ListGameHosts(options));
				}
				else
				{
					con->set_status(web::http::status::not_found, "Unknown game type " + fragments[2], ec);
				}
			}
			else
			{
				con->set_status(web::http::status::bad_request, "Unknown games action '" + fragments[2] + "'", ec);
			}
		}
		else
		{
			reply_with_body(con, ListGameHosts(options));
		}
	}
	else if (fragments[1] == "providers")
	{
		if (fragments.size() == 3)
		{
			int providerType = 0;
			if (fragments[2] == "types")
			{
				json types(json::value_t::array);
				for (auto type : provider::ProviderType::_values())
				{
					json typeDesc(json::value_t::object);
					typeDesc["type"] = type._to_integral();
					typeDesc["name"] = type._to_string();
					// typeDesc["schema"] = provider::ConfigurationSchemas[type];
					types.push_back(std::move(typeDesc));
				}
				reply_with_body(con, types);
			}
			else if (strToInt2(fragments[2], providerType))
			{
				auto val = provider::ProviderType::_from_integral_nothrow(providerType);
				if (val)
				{
					options.providerType = *val;
					reply_with_body(con, ListProviders(options));
				}
				else
				{
					con->set_status(web::http::status::not_found, "Unknown provider type " + fragments[2], ec);
				}
			}
			else
			{
				con->set_status(web::http::status::bad_request, "Unknown providers action '" + fragments[2] + "'", ec);
			}
		}
		else
		{
			reply_with_body(con, ListProviders(options));
		}
	}
}

void YServer::OnRestartRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	rtfwk_sdl2::pApp->safeShutdown();
	std::error_code ec;
	con->set_status(web::http::status::ok, ec);
}

void YServer::OnHistoryRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	const std::string account_uid = query.Get("p");
	const std::string gameroundId = query.Get("r");
	const std::string accountingroundId = query.Get("a");
	const std::string signature = query.Get("s");

	std::error_code ec;
	if (key.Sign(EHashAlgorithm::SHA1, account_uid + gameroundId + accountingroundId) != signature)
	{
		con->set_status(web::http::status::forbidden, "Bad signature!", ec);
		return;
	}

	const AccountID playerAccount(account_uid);

	if (!gameroundId.empty())
	{
		const GameRoundSnapshot gameround = HostContainer()->GetRoundOfPlayer(playerAccount, gameroundId);
		if (!gameround.IsValid())
		{
			con->set_status(web::http::status::not_found, "Can not find the requested game round", ec);
		}
		else
		{
			reply_with_body(con, gameround);
		}
	}
	else if (!accountingroundId.empty())
	{
		const AccountingRoundSnapshot accountingRound = ProviderContainer()->GetRoundOfPlayer(playerAccount, accountingroundId);
		if (accountingRound.RoundID.empty())
		{
			con->set_status(web::http::status::not_found, "Can not find the requested accounting round", ec);
		}
		else
		{
			reply_with_body(con, accountingRound);
		}
	}
	else
	{
		std::list<GameRoundSnapshot> gameRounds;
		HostContainer()->GetRoundsOfPlayerAccount(playerAccount, gameRounds, dynamic_cast<PlayerAPI*>(GetAPI(EClientType::Player))->MaxHistorySize, {});

		std::list<AccountingRoundSnapshot> accountingRounds;
		ProviderContainer()->GetRoundsOfPlayerAccount(playerAccount, accountingRounds, dynamic_cast<PlayerAPI*>(GetAPI(EClientType::Player))->MaxHistorySize, {});

		json response;
		response["gameRounds"] = gameRounds;
		response["accountingRounds"] = accountingRounds;
		reply_with_body(con, response);
	}
}

void YServer::OnReplayRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() != 1)
	{
		con->set_status(web::http::status::bad_request, "Bad request format", ec);
		return;
	}

	json body;
	if (!con->get_request_body().empty())
	{
		try
		{
			body = json::parse(con->get_request_body());
		}
		catch (const std::exception& e)
		{
			con->set_status(web::http::status::bad_request, "Body could not be parsed to JSON", ec);
			return;
		}
	}

	web::QueryString queryToUse(query);
	queryToUse.erase(QUERY_PARAM_DEV_LAUNCH);
	queryToUse.erase(QUERY_PARAM_GAME_LAUNCH_OPTION_KEY);

	try
	{
		auto Endpoint = std::dynamic_pointer_cast<GameEndpoint>(GetAPI(EClientType::StaticViewer)->CreateEndpoint(key, queryToUse, body));
		if (!Endpoint)
			throw YEndpointError(YError::ERR_INTERNAL_ERROR, "Endpoint creation is broken");

		const std::string url = GetClientLaunchURL(EClientType::StaticViewer, *Endpoint);

		if (url.empty())
			throw YEndpointError(YError::ERR_INTERNAL_ERROR, "Empty game launch URL");

		con->append_header(web::http::field::location, url, ec);
		con->set_status(web::http::status::found, ec);
	}
	catch (const YEndpointError& e)
	{
		web::http::status::value code = web::http::status::bad_request;
		switch (static_cast<yprotocol::YProtocolError>(e.ErrorCode()))
		{
			case yprotocol::YProtocolError::ERR_NOT_OK:
			case yprotocol::YProtocolError::ERR_SESSION_TIMED_OUT: code = web::http::status::not_found; break;

			case yprotocol::YProtocolError::ERR_CONNECTION_UNAVAILABLE: code = web::http::status::no_content; break;

			case yprotocol::YProtocolError::ERR_BAD_RECONNECT: code = web::http::status::conflict; break;
			case yprotocol::YProtocolError::ERR_INTERNAL_ERROR: code = web::http::status::internal_server_error; break;
			case yprotocol::YProtocolError::ERR_UNAUTHORIZED:
			case yprotocol::YProtocolError::ERR_BANNED: code = web::http::status::unauthorized; break;
			default: break;
		}
		con->set_status(code, e.what(), ec);
	}
}

bool CreateMarkdownDocs(std::ostream& out, const EventTemplate& event, size_t indent, std::set<std::string>& documentedEvents)
{
	if (!documentedEvents.insert(event.Name).second)
		return false;

	md::Headline(out, event.Name, indent++);
	out << event.Description << '\n';
	out << '\n';
	md::Headline(out, "Event Data", indent++);
	if (event.ContextSchema.GetType() == json::value_t::null && event.ContextSchema.GetDescription().empty())
		out << "No data passed with this event." << '\n';
	else
		md::CodeBlock(out, event.ContextSchema.GenerateBlankConfig(yprotocol::YRequestHandler::MethodInfo::MarkdownOptions_Return));

	indent--;
	out << '\n';

	md::HRule(out);
	out << '\n';

	return true;
}

void YServer::OnCreateEndpointRequest(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	std::error_code ec;
	if (fragments.size() != 2)
	{
		con->set_status(web::http::status::bad_request, "API endpoint requires a single resource fragment", ec);
		return;
	}

	if (mMaxClients && (mNumClients.load() + 1 > mMaxClients))
	{
		con->set_status(web::http::status::service_unavailable, "The server is full!", ec);
		return;
	}

	std::optional<EClientType> typ = EClientType::_from_string_nocase_nothrow(fragments[1].c_str());
	if (!typ)
	{
		con->set_status(web::http::status::not_found, "Unknown API", ec);
		return;
	}

	YEndpointAPI* api = GetAPI(*typ);
	if (!api)
	{
		con->set_status(web::http::status::not_implemented, "API not implemented", ec);
		return;
	}

	json body;
	if (!con->get_request_body().empty())
	{
		try
		{
			body = json::parse(con->get_request_body());
		}
		catch (const std::exception& e)
		{
			con->set_status(web::http::status::bad_request, "Body could not be parsed to JSON", ec);
			return;
		}
	}

	std::shared_ptr<YEndpoint> Endpoint;
	std::optional<YEndpointError> OptError;
	try
	{
		Endpoint = api->CreateEndpoint(key, query, body);

		if (GameEndpoint* gameEndpoint = dynamic_cast<GameEndpoint*>(Endpoint.get()))
		{
			const std::string launchUrl = gameEndpoint->GameInfo.GetLaunchURL(query.Get(QUERY_PARAM_GAME_LAUNCH_OPTION_KEY), query.Get(QUERY_PARAM_DEV_LAUNCH) == "true");
			if (!launchUrl.empty())
			{
				con->append_header(web::http::field::location, launchUrl, ec);
				web::QueryString gameQuery(gameEndpoint->ExtraParams);
				if (!gameQuery.contains("secure") && !YServer::IsSecure())
					gameQuery["secure"] = "false";
				con->append_header("X-Game-Query", gameQuery.ToString(), ec);
			}
		}
	}
	catch (const YEndpointError& e)
	{
		OptError = e;
	}

	reply_with_body(con, api->ReplyToEndpointRequest(query, Endpoint, OptError));
}

void YServer::OnCroupierLogin(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	const std::string badge_id = query.Get("id");

	std::error_code ec;
	if (badge_id.empty())
	{
		con->set_status(web::http::status::bad_request, "Missing id!", ec);
		return;
	}

	FCroupier croupier;
	if (Croupiers.Authenticate(badge_id, croupier))
	{
		reply_with_body(con, croupier.ToJSON());
	}
	else
	{
		con->set_status(web::http::status::unauthorized, "Invalid badge ID!", ec);
	}
}

void YServer::OnCroupierAdd(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	json data(json::value_t::object);
	data["name"] = query.Get("name");
	data["location"] = query.Get("location");
	data["birthdate"] = query.Get("birthdate");

	try
	{
		FCroupier croupier;
		croupier.LoadFromJSON(data);

		reply_with_body(con, Croupiers.AddCroupier(croupier));
	}
	catch (const std::exception& e)
	{
		std::error_code ec;
		con->set_status(web::http::status::bad_request, "Missing name or birthdate!", ec);
	}
}

void YServer::OnErrorPage(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	const ELanguage lang = ISOLanguages::FromLocale(query.Get("locale"));
	const json errorCode = query.GetAsJson("code");

	std::error_code ec;
	con->set_body(
	  ErrorHead + GetLaunchErrorDesc(errorCode.is_number_integer() ? static_cast<YError>(errorCode.get<int>()) : YError::ERR_INTERNAL_ERROR, lang) + ErrorTail, {}, ec);
	con->set_status(web::http::status::ok, ec);
}

void YServer::GenerateDocumentation()
{
	const std::filesystem::path docsDir = mYServerDataDir / "docs";
	Log(Important, "Generating YServer documentation and outputting to '%s'", docsDir.c_str());
	std::filesystem::create_directories(docsDir);
	std::ofstream httpReq(docsDir / "HTTPRequests.md", std::ios_base::openmode::_S_trunc);

	size_t indent = 0;
	md::Headline(httpReq, "HTTP Requests", indent++);
	httpReq << "HTTP requests on the YServer have 3 access levels:" << '\n';
	httpReq << '\n' << md::Bullet() << md::Bold("Public:") << '\n';
	httpReq << "These requests are available for everyone and don't require any authorization" << '\n';
	httpReq << '\n' << md::Bullet() << md::Bold("API Key") << '\n';
	httpReq << "These requests are only available when a valid API key is given in the " << md::Bold(md::Italic(web::http::header(web::http::field::authorization)))
	        << " HTTP request header." << '\n';
	httpReq << '\n' << md::Bullet() << md::Bold("Admin") << '\n';
	httpReq << "These requests are like API key request, only that the API key must be flagged as an admin key on the server side." << '\n';
	httpReq << '\n';

	bool bFirst = true;
	md::Headline(httpReq, "Public", indent++);
	for (const auto& handler : HTTPHandlers)
		if (handler.AccessLevel == EYAPIAccessLevel::All)
		{
			if (!bFirst)
				httpReq << '\n';
			bFirst = false;
			handler.CreateMarkdownDocs(httpReq, indent);
			md::HRule(httpReq);
		}
	indent--;    // End Public headline
	httpReq << '\n';
	md::Headline(httpReq, "API Key", indent++);
	bFirst = true;
	for (const auto& handler : HTTPHandlers)
		if (handler.AccessLevel == EYAPIAccessLevel::PublicKey)
		{
			if (!bFirst)
				httpReq << '\n';
			bFirst = false;
			handler.CreateMarkdownDocs(httpReq, indent);
			md::HRule(httpReq);
		}
	indent--;    // End API Key headline
	httpReq << '\n';
	md::Headline(httpReq, "Admin", indent++);
	bFirst = true;
	for (const auto& handler : HTTPHandlers)
		if (handler.AccessLevel == EYAPIAccessLevel::PrivateKey)
		{
			if (!bFirst)
				httpReq << '\n';
			bFirst = false;
			handler.CreateMarkdownDocs(httpReq, indent);
			md::HRule(httpReq);
		}
	indent--;    // End Admin headline
	httpReq.close();

	indent = 0;
	std::ofstream wsReq(docsDir / "WSRequests.md", std::ios_base::openmode::_S_trunc);
	md::Headline(wsReq, "WebSocket Requests", indent++);
	wsReq
	  << "WebSocket requests on the YServer are executed over the imaxa protocol as messages with type 'request'. See the protocol documentation section for more information."
	  << '\n';
	wsReq << '\n';
	md::Headline(wsReq, "Universal Player Requests", indent++);
	wsReq << "Regardless of the type of game or casino provider, these requests are always available to the game client." << '\n';
	wsReq << "They are used for game client logic that is not sensitive to game-specific or provider-specific behavior." << '\n';
	wsReq << '\n';
	for (const auto& p : PlayerAdminAPI->RequestHandler->Methods()) p.second.CreateMarkdownDocs(wsReq, p.first, indent);
	provider::TEmptyCasinoProvider BaseProvider;
	for (const auto& p : BaseProvider.Methods()) p.second.CreateMarkdownDocs(wsReq, p.first, indent);
	gamehost::TEmptyGameHost BaseHost;
	for (const auto& p : BaseHost.Methods()) p.second.CreateMarkdownDocs(wsReq, p.first, indent);
	indent--;

	md::Headline(wsReq, "Game-Specific Player Requests", indent++);
	wsReq << "Each game type is a little different, so there are some requests which are only accessible by that game type." << '\n';
	wsReq << "Additionally, the type of the response to the " << md::Bold(md::Italic("bet")) << " request (see above) is also determined by the type of game." << '\n';
	wsReq << '\n';
	for (auto type : gamehost::HostType::_values())
	{
		if (type == gamehost::HostType::Null)
			continue;
		auto tenplateHost = mGameHosts->LoadSingle(type, {});
		if (!tenplateHost)
			continue;
		md::Headline(wsReq, type._to_string(), indent - 1);
		wsReq << "The parameters of " << md::Bold("bet") << " requests on this game host should conform with the following schema:" << '\n';
		md::CodeBlock(wsReq, tenplateHost->GetBetSchema().GenerateBlankConfig(yprotocol::YRequestHandler::MethodInfo::MarkdownOptions_Param));
		wsReq << '\n';
		wsReq << "The return value of " << md::Bold("bet") << " requests on this game host look like this:" << '\n';
		wsReq << '\n';
		md::CodeBlock(wsReq, tenplateHost->GetBetResponseSchema().GenerateBlankConfig(yprotocol::YRequestHandler::MethodInfo::MarkdownOptions_Return));
		wsReq << '\n';

		size_t numEntries = 0;
		for (const auto& p : tenplateHost->Methods())
			if (!BaseHost.Methods().contains(p.first))
			{
				p.second.CreateMarkdownDocs(wsReq, p.first, indent);
				numEntries++;
			}
		if (numEntries == 0)
			wsReq << "No special requests for this host" << '\n' << '\n';
	}
	indent--;

	md::Headline(wsReq, "Provider-Specific Player Requests", indent++);
	wsReq << "Each casino provider works differently and can have special features, so there are some requests which are only accessible when using that provider type."
	      << '\n';
	wsReq << '\n';
	for (auto type : provider::ProviderType::_values())
	{
		if (type == provider::ProviderType::Null)
			continue;
		auto tenplateProvider = mCasinoProviders->LoadSingle(type, {});
		if (!tenplateProvider)
			continue;
		md::Headline(wsReq, type._to_string(), indent - 1);
		size_t numEntries = 0;
		for (const auto& p : tenplateProvider->Methods())
			if (!BaseProvider.Methods().contains(p.first))
			{
				p.second.CreateMarkdownDocs(wsReq, p.first, indent);
				numEntries++;
			}
		if (numEntries == 0)
			wsReq << "No special requests for this provider" << '\n' << '\n';
	}
	indent--;


	indent = 0;
	std::ofstream events(docsDir / "Events.md", std::ios_base::openmode::_S_trunc);
	md::Headline(events, "YServer Events", indent++);
	events << "Events are sent to the client by messages with type 'event'. See the yserver documentation for more information." << '\n';
	events << '\n';
	md::Headline(events, "Universal Player Events", indent++);
	events << "Regardless of the type of game or casino provider, these events are common to all game clients." << '\n';
	events << "They are used for game client logic that is not sensitive to game-specific or provider-specific behavior." << '\n';
	events << '\n';
	std::set<std::string> DocumentedEvents;
	for (const auto& p : PlayerAdminAPI->RequestHandler->Events()) CreateMarkdownDocs(events, *p.second, indent, DocumentedEvents);
	for (const auto& p : BaseProvider.Events()) CreateMarkdownDocs(events, *p.second, indent, DocumentedEvents);
	for (const auto& p : BaseHost.Events()) CreateMarkdownDocs(events, *p.second, indent, DocumentedEvents);
	indent--;

	md::Headline(events, "Game-Specific Player Events", indent++);
	events << "Each game type is a little different, so there are some events which are only trigegred by that game host type." << '\n';
	events << '\n';
	for (auto type : gamehost::HostType::_values())
	{
		if (type == gamehost::HostType::Null)
			continue;
		auto tenplateHost = mGameHosts->LoadSingle(type, {});
		if (!tenplateHost)
			continue;
		md::Headline(events, type._to_string(), indent - 1);
		size_t numEntries = 0;
		std::set<std::string> DocumentedEventsForThisHost = DocumentedEvents;
		for (const auto& p : tenplateHost->Events())
			if (CreateMarkdownDocs(events, *p.second, indent, DocumentedEventsForThisHost))
				numEntries++;

		if (numEntries == 0)
			events << "No special events for this host" << '\n' << '\n';
	}
	indent--;

	md::Headline(events, "Provider-Specific Player Events", indent++);
	events << "Each casino provider works differently and can have special features, so there are some events which are only triggered by that provider type." << '\n';
	events << '\n';
	for (auto type : provider::ProviderType::_values())
	{
		if (type == provider::ProviderType::Null)
			continue;
		auto tenplateProvider = mCasinoProviders->LoadSingle(type, {});
		if (!tenplateProvider)
			continue;
		md::Headline(events, type._to_string(), indent - 1);
		size_t numEntries = 0;
		std::set<std::string> DocumentedEventsForThisProvider = DocumentedEvents;
		for (const auto& p : tenplateProvider->Events())
			if (CreateMarkdownDocs(events, *p.second, indent, DocumentedEventsForThisProvider))
				numEntries++;
		if (numEntries == 0)
			events << "No special events for this provider" << '\n' << '\n';
	}
	indent--;

	Log(Important, "YServer documentation generation completed. Exiting...");
}

web::websockets::session::validation::value YServer::accept_ws(const imaxa_connection_ptr& con, std::optional<security::JWToken> token)
{
	std::shared_ptr<PersistentClientInfo> banInfo = CheckPersistentClientInfo(mIPBlacklist, mIPMaxConnectAttempts, con->get_remote_ip());
	if (banInfo->isBanned())
	{
		std::error_code ec;
		con->set_body(make_error_object(yprotocol::YProtocolError::ERR_BANNED, "ban", banInfo->Status).dump(), {}, ec);
		ec.clear();
		con->set_status(web::websockets::http::status_code::forbidden, ec);
		return web::websockets::session::validation::reject;
	}

	con->set_open_handler([this, token = std::move(token)](imaxa_connection_hdl_ref hdl) { on_open(hdl, std::move(token)); });

	return web::websockets::session::validation::accept;
}

void YServer::on_open(imaxa_connection_hdl_ref hdl, std::optional<security::JWToken> token)
{
	auto con = hdl.lock();

	std::string errMsg;
	try
	{
		std::shared_ptr<YServerClient> client = AddClient(con, token);
		if (client)
		{
			GetAPI(client->Type)->PostInit(client);
			return;    // all ok!
		}
		else
		{
			errMsg = "Client creation failed";
		}
	}
	catch (const yprotocol::InitError& err)
	{
		SendMessage(hdl, 0, yprotocol::EServerMessageType::Error, make_error_object(YError::ERR_CONNECTION_UNAVAILABLE, "open-connection", err.what(), err.Context()));
		errMsg = err.what();
	}

	if (con)
	{
		std::error_code ec;
		con->close(websocketpp::close::status::policy_violation, "Registration failed: " + errMsg, ec);
	}
}

void YServer::reply_with_body(const imaxa_connection_ptr& con, const json& body) const
{
	std::error_code ec;
	con->set_json_body(yutils::getRoundedDoublesJson(body), {}, ec);
	con->set_status(web::http::status::ok, ec);
}

void YServer::on_http(const imaxa_connection_ptr& con, const std::vector<std::string>& resource, const web::QueryString& query)
{
	if (resource.empty())
	{
		std::error_code ec;
		con->set_status(web::http::status::bad_request, ec);
		return;
	}

	std::string authKeyPublic = con->get_request_header(web::http::header(web::http::field::authorization));
	if (authKeyPublic.empty() && query.contains("key"))
		authKeyPublic = query.Get("key");

	yserver::YAuthKey key;
	{
		SharedScopedLock lock(mHTTPAuthKeys);
		auto find = authKeyPublic.empty() ? mHTTPAuthKeys->end() : mHTTPAuthKeys->find(authKeyPublic);
		if (find != mHTTPAuthKeys->end())
			key = find->second;
	}

	EYAPIAccessLevel accessLevel = EYAPIAccessLevel::All;
	if (key.Valid())
		accessLevel = key.Secret.empty() ? EYAPIAccessLevel::PublicKey : EYAPIAccessLevel::PrivateKey;

	for (const YAPIEndpointHandler& info : HTTPHandlers)
	{
		if (info.Methods.contains(con->get_request_method()) &&
		    info.RelativeResourcePath.Match(std::vector<JsonPath::Argument>(resource.begin(), resource.end()), info.bCaseSensitive))
		{
			if (accessLevel >= info.AccessLevel)
			{
				info.Handler(con, resource, query, key);
			}
			else
			{
				std::error_code ec;
				con->set_status(web::http::status::forbidden, "Access denied", ec);
			}
			return;
		}
	}

	std::error_code ec;
	con->set_status(web::http::status::bad_request, "Bad endpoint", ec);
}

std::shared_ptr<provider::TCasinoProvider> YServer::YProviderContainer::LoadSingle(provider::ProviderType type, const std::string& name)
{
	std::shared_ptr<provider::TCasinoProvider> createdProvider;
	switch (type)
	{
		case provider::ProviderType::nano: {
			createdProvider = std::make_shared<provider::TNanoProvider>(this, name);
			break;
		}
		case provider::ProviderType::HG4: {
			createdProvider = std::make_shared<provider::TGameartProvider>(this, name);
			break;
		}
		case provider::ProviderType::berghain: {
			createdProvider = std::make_shared<provider::TBerghainProvider>(this, name);
			break;
		}
		case provider::ProviderType::GameHub: {
			createdProvider = std::make_shared<provider::TGameHubProvider>(this, name);
			break;
		}
		default: break;
	}
	return createdProvider;
}

std::shared_ptr<gamehost::TGameHost> YServer::YHostContainer::LoadSingle(gamehost::HostType type, const std::string& name)
{
	std::shared_ptr<gamehost::TGameHost> createdHost;
	switch (type)
	{
		case gamehost::HostType::Roulette: {
			createdHost = std::make_shared<gamehost::TRouletteHost>(this, name);
			break;
		}
		case gamehost::HostType::GameartSlotGame: {
			createdHost = std::make_shared<gamehost::TGameartSlotHost>(this, name);
			break;
		}
		case gamehost::HostType::VirtualRoulette: {
			createdHost = std::make_shared<gamehost::TVirtualWheelHost>(this, name);
			break;
		}
		case gamehost::HostType::GameartTableGame: {
			createdHost = std::make_shared<gamehost::TGameartTableGameHost>(this, name);
			break;
		}
		case gamehost::HostType::Blackjack: {
			createdHost = std::make_shared<gamehost::TBlackjackHost>(this, name);
			break;
		}
		case gamehost::HostType::VirtualBaccarat: {
			createdHost = std::make_shared<gamehost::baccarat::TVirtualBaccaratHost>(this, name);
			break;
		}
		case gamehost::HostType::Baccarat: {
			createdHost = std::make_shared<gamehost::baccarat::TBaccaratHost>(this, name);
			break;
		}
		case gamehost::HostType::Evolution: {
			createdHost = std::make_shared<gamehost::TEvolutionGameHost>(this, name);
			break;
		}
		case gamehost::HostType::VirtualDragonTiger: {
			createdHost = std::make_shared<gamehost::dragontiger::TVirtualDragonTigerHost>(this, name);
			break;
		}

		case gamehost::HostType::VirtualOpenDragonTiger: {
			createdHost = std::make_shared<gamehost::dragontiger::TVirtualDragonTigerHost>(this, name);
			break;
		}
		case gamehost::HostType::VirtualThreeHeadedDragon: {
			createdHost = std::make_shared<gamehost::TVirtualThreeHeadedDragonHost>(this, name);
			break;
		}
		case gamehost::HostType::VirtualOpenBaccarat: {
			createdHost = std::make_shared<gamehost::baccarat::TVirtualBaccaratHost>(this, name);
			break;
		}

		case gamehost::HostType::DealersGame: {
			createdHost = std::make_shared<gamehost::TDealersHost>(this, name);
			break;
		}

		default: break;
	}
	return createdHost;
}

bool YServer::YHostContainer::PostLoad(const std::shared_ptr<YModule>& loadedModule)
{
	std::dynamic_pointer_cast<gamehost::TGameHost>(loadedModule)->GenerateGameConfigurations();

	return YModuleContainer::PostLoad(loadedModule);
}

void YServer::OnMessage(yprotocol::YProtocolClient* client, const std::string& msg)
{
	/*Log(VeryVerbose, "Received message from %s(%s): %s", client->Account()->ID().Username.c_str(), client->Account()->Session().c_str(),
	    msg->get_payload().c_str());*/
	if (client->MessageRate.ShouldBan())
	{
		Log(Important, "Banning %s %s for exceeding message rate!", dynamic_cast<YServerClient*>(client)->Type._to_string(), client->LogComponentName.c_str());
		Player* player = dynamic_cast<Player*>(client);
		if (player)
		{
			ban(mPlayerBlacklist, mPlayerMaxConnectAttempts, player->Account()->ID().AsString(), client->MessageRate.Duration, "Message rate exceeded");
		}
		else
		{
			std::error_code ec;
			if (auto con = client->ConnectionHandle().lock())
				ban(mIPBlacklist, mIPMaxConnectAttempts, con->get_remote_ip(), client->MessageRate.Duration, "Message rate exceeded");
		}
		client->Disconnect(websocketpp::close::status::policy_violation, "Banned");
		return;
	}

	YProtocol::OnMessage(client, msg);
}

const JsonSchema CreatePlayerEndpointSchema =
  JsonSchema({ { "host", JsonSchema(json::value_t::number_unsigned, "The ID of the target host") },
               { "provider", JsonSchema(json::value_t::number_unsigned, "The ID of the target provider", 0U) },
               { "demo", JsonSchema(json::value_t::boolean, "True to start game as demo (no provider)", false) },
               { "sid", JsonSchema(json::value_t::string, "The session to use on the provider", std::string()) },
               { "credit-scale",
                 JsonSchema(json::value_t::number_unsigned, "Virtual denomination multiplier", 1U).AddConstraint(limits::IValueLimit::MakeRange(1U, 100U, true, true)) },
               { "game", JsonSchema(json::value_t::string, "The name of the target game configuration on the given host") } });

YServer::YServer() : YProtocolWebServer()
{
	SetLogCategory(LogY);
	gamehost::gameart::V8Interface::GetV8Platform();

	mChecksum.first = rtfwk_sdl2::pApp->GetBinaryPath();
	mChecksum.second = crypto::File_Checksum(EHashAlgorithm::SHA1, mChecksum.first);

	Schema() += YServerSchema;

	JsonSchema banSchema(json::value_t::object, "Configuration of ban rules");
	banSchema.AddMember(MaxMessageRate.Name, MaxMessageRate.GetSchema());
	banSchema.AddMember(mIPMaxConnectAttempts.Name, mIPMaxConnectAttempts.GetSchema());
	banSchema.AddMember(mPlayerMaxConnectAttempts.Name, mPlayerMaxConnectAttempts.GetSchema());
	Schema().AddMember("ban", banSchema);

	CurrencyAPI = std::make_shared<TCurrencyAPI>();
	Schema().AddMember("currency-api", CurrencyAPI->GetSchema());

	mPort = DEFAULT_YSERVER_PORT;
	mMaxClients = 0;
	bSessionCookieSecure = false;
	mNumClients = 0;
	mLastReport = 0;
	bAllowReconnect = false;
	mReportInterval = 20;
	bAllowPilotSeat = false;

	SET_LOG_COLOR(LogY, Info, 220);    // yellowish-orange

	LogAdminAPI =
	  std::make_unique<LoggerAPI>(this, EClientType::Logger, std::dynamic_pointer_cast<yprotocol::YRequestHandler>(std::make_shared<YLoggerRequestHandler>()));
	PlayerAdminAPI = std::make_unique<PlayerAPI>(this);
	CommanderAdminAPI = std::make_unique<CommanderAPI>(this);
	ViewAdminAPI =
	  std::make_unique<ViewerAPI>(this, EClientType::Viewer, std::dynamic_pointer_cast<yprotocol::YRequestHandler>(std::make_shared<YViewerRequestHandler>(this)));
	StaticViewAdminAPI = std::make_unique<StaticViewerAPI>(this);

	mProtectedAPIs[EClientType::Logger] = LogAdminAPI.get();
	mProtectedAPIs[EClientType::Viewer] = ViewAdminAPI.get();
	mProtectedAPIs[EClientType::StaticViewer] = StaticViewAdminAPI.get();
	mProtectedAPIs[EClientType::Commander] = CommanderAdminAPI.get();
	mProtectedAPIs[EClientType::Player] = PlayerAdminAPI.get();

	mCasinoProviders = std::make_unique<YProviderContainer>(this, yserver::provider::TYPE_NAME);
	mGameHosts = std::make_unique<YHostContainer>(this, yserver::gamehost::TYPE_NAME);

	JsonSchema staticProviderSchema(json::value_t::object, "Static configuration of casino provider types");
	bool bRequired = false;
	for (provider::ProviderType type : provider::ProviderType::_values())
	{
		if (type != provider::ProviderType::Null)
		{
			auto templateProvider = mCasinoProviders->LoadSingle(type, {});
			if (templateProvider && templateProvider->GetStaticConfigSchema().GetType() != json::value_t::null)
			{
				staticProviderSchema.AddMember(type._to_string(), templateProvider->GetStaticConfigSchema());
				bRequired = bRequired || templateProvider->GetStaticConfigSchema().Required();
			}
		}
	}
	staticProviderSchema.SetRequired(bRequired);
	Schema().AddMember("static-provider-config", staticProviderSchema);

	JsonSchema staticHostSchema(json::value_t::object, "Static configuration of game host types");
	bRequired = false;
	for (gamehost::HostType type : gamehost::HostType::_values())
	{
		if (type != gamehost::HostType::Null)
		{
			auto templateHost = mGameHosts->LoadSingle(type, {});
			if (templateHost && templateHost->GetStaticConfigSchema().GetType() != json::value_t::null)
			{
				staticHostSchema.AddMember(type._to_string(), templateHost->GetStaticConfigSchema());
				bRequired = bRequired || templateHost->GetStaticConfigSchema().Required();
			}
		}
	}
	staticHostSchema.SetRequired(bRequired);
	Schema().AddMember("static-host-config", staticHostSchema);

	for (const auto& api : mProtectedAPIs)
	{
		if (!api.second)
			continue;

		Schema().Member("api").AddMember(api.first._to_string(), api.second->GetSchema());
		api.second->OnInstanceAddedRemoved += std::bind(&YServer::OnInstanceAddedOrRemoved, this, std::placeholders::_1, std::placeholders::_2);
	}

	mLegacyKey.Public = crypto::Hash("DerFischAusDerHolle", EHashAlgorithm::SHA256);
	mLegacyKey.Secret = "Ro\u017Ele";
	mLegacyKey.Tag = "YServer Legacy Key";

	AddHTTPListener(
	  "res.*", { "resourceID" }, "Resource Upload",
	  "This endpoint is used by games to upload resources to the server, which can be used by other APIs on the yserver (mainly Viewer in the case of slot machine top screens)",
	  std::bind(&YServer::OnGameResourceUploadRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	  { web::http::verb::put }, EYAPIAccessLevel::All, {},
	  { { web::http::status::bad_request, JsonSchema(json::value_t::null, "resourceID was not given") },
	    { web::http::status::not_found,
	      JsonSchema(json::value_t::null, "A resource with the given ID was not found (a place for it has to be reserved by a Player API request)") },
	    { web::http::status::no_content, JsonSchema(json::value_t::null, "The resource being uploaded seems to be empty (zero Content-Length or empty HTTP body)") },
	    { web::http::status::internal_server_error, JsonSchema(json::value_t::null, "An error occurred while saving the resource to the server") },
	    { web::http::status::ok, JsonSchema(json::value_t::null, "Success! The resource was saved.") } },
	  "Returns a status code indicating success/failure");

	AddHTTPListener(
	  "res.*", { "resourceID" }, "Resource Download",
	  "This endpoint is used by APIs like Viewer, ehich download resources uploaded by games to display various information (eg. paytables, info screens, etc.)",
	  std::bind(&YServer::OnGameResourceDownloadRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	  { web::http::verb::get }, EYAPIAccessLevel::All, {},
	  { { web::http::status::bad_request, JsonSchema(json::value_t::null, "resourceID was not given") },
	    { web::http::status::not_found, JsonSchema(json::value_t::null, "A resource with the given ID was not found") },
	    { web::http::status::no_content, JsonSchema(json::value_t::null, "The resource has not yet loaded") },
	    { web::http::status::internal_server_error, JsonSchema(json::value_t::null, "An error occurred while reading the resource") },
	    { web::http::status::ok, JsonSchema(json::value_t::null, "Success! The resource is in the HTTp response body") } },
	  "Returns a status code indicating success/failure. I successfull, the resource is the HTTP response body. Content-Type is set accordingly.");

	AddHTTPListener("update", {}, "Update YServer", "Upload a new yserver binary and restart the server",
	                std::bind(&YServer::OnUpdateServer, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::put }, EYAPIAccessLevel::PrivateKey, JsonSchema(json::value_t::null, "Must contain the binary file"),
	                { { web::http::status::internal_server_error, JsonSchema(json::value_t::null, "An error occurred while writing the file") },
	                  { web::http::status::ok, JsonSchema(json::value_t::null, "Successfully uploaded new binary!") } },
	                "Server will restart if the response was OK");

	AddHTTPListener(
	  "player.*", { "endpoint" }, "Launch Game", "Launch a game client for the given endpoint",
	  std::bind(&YServer::OnLaunchGame, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4), { web::http::verb::get },
	  EYAPIAccessLevel::All,
	  JsonSchema({ { "secure", JsonSchema(json::value_t::boolean, "True/false to indicate that the client should connect to the server via WSS instead of WS", true) },
	               { "replay", JsonSchema(json::value_t::boolean, "True/false to launch the game client in replay mode", false) } }),
	  { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Endpoint or signature was not given, or there was extra path fragments in the URL") },
	    { web::http::status::not_found, JsonSchema(json::value_t::null, "Endpoint not found or endpoint has an invalid game") },
	    { web::http::status::unauthorized, JsonSchema(json::value_t::null, "Endpoint signature does not match") },
	    { web::http::status::found, JsonSchema(json::value_t::null, "Success - Game accessible at URL saved into the Location header") } },
	  "Returns a status code indicating a success or failure");

	AddHTTPListener("game.*.*", { "providerID", "hostID" }, "Launch New Game (Legacy)", "Launch a game given a provider and host",
	                std::bind(&YServer::OnLegacyLaunch, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::All, CreatePlayerEndpointSchema,
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Provider or host ID not given or too many URL fragments") },
	                  { web::http::status::not_found, JsonSchema(json::value_t::null, "Host:game combo not found or provider not found or session timed out") },
	                  { web::http::status::internal_server_error, JsonSchema(json::value_t::null, "Unknown error occurred") },
	                  { web::http::status::no_content, JsonSchema(json::value_t::null, "Connection already taken by someone else") },
	                  { web::http::status::unauthorized, JsonSchema(json::value_t::null, "No permission to launch games or account/IP is banned") },
	                  { web::http::status::found, JsonSchema(json::value_t::null, "Success - Game accessible at URL saved into the Location header") } },
	                "Redirects to the final game URL or returns an error code");

	AddHTTPListener("icons.*.*", { "hostID", "iconIdx" }, "Download Icons", "Get the icon of a given host",
	                std::bind(&YServer::OnIconRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::All,
	                JsonSchema({ { "game", JsonSchema(json::value_t::string, "The name of the game to get the icon for") } }),
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Could not parse hostID, icon index, or wrong number of URL fragments") },
	                  { web::http::status::not_found, JsonSchema(json::value_t::null, "No such icon found (most likely iconIdx is too large for this host:game pair)") },
	                  { web::http::status::internal_server_error, JsonSchema(json::value_t::null, "Error reading image file") },
	                  { web::http::status::ok, JsonSchema(json::value_t::null, "Success - Icon is sent as regular image file in the HTTP response") } },
	                "Returns the image as a regular file");

	const JsonSchema SchemaOfSchema(
	  { { "schema", JsonSchema(json::value_t::object, "member tree of schemas", json(json::value_t::object)).SetChildSchema(JsonSchema::Schema) },
	    { "default", JsonSchema(json::value_t::object, "member tree of default values", json(json::value_t::object)) } });

	AddHTTPListener("schema.*.*", { "module", "type" }, "Get Module Schema",
	                "Get the schema of the specified module (host or provider) and type (eg. GameartSlotGame, Roulette, ...)",
	                std::bind(&YServer::OnSchemaRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PrivateKey, {},
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "module or type not given, or could not be parsed") },
	                  { web::http::status::not_found, JsonSchema(json::value_t::null, "Invalid module type") },
	                  { web::http::status::ok, SchemaOfSchema } },
	                "Returns the requested schema in JSON format or a status code on error");

	AddHTTPListener(
	  "info", {}, "Get Info", "Return server status and core information",
	  std::bind(&YServer::OnServerInfoRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	  { web::http::verb::get }, EYAPIAccessLevel::All, {},
	  { { web::http::status::ok, JsonSchema({ { "version", JsonSchema({ { "major", JsonSchema(json::value_t::number_unsigned, "Major version number") },
	                                                                    { "minor", JsonSchema(json::value_t::number_unsigned, "Minor version number") },
	                                                                    { "revision", JsonSchema(json::value_t::number_unsigned, "Revision build number") } },
	                                                                  "The version of the server") },
	                                          { "domain", JsonSchema(json::value_t::string, "Externally accessible address (public name)") },
	                                          { "admin", JsonSchema(json::value_t::boolean, "True/false if the key used has admin rights") },
	                                          { "id", JsonSchema(json::value_t::string, "The yserver ID") } }) } });

	AddHTTPListener("query.*.*.*", { "module" }, "Query specific host Instances", "Query special methods on specific module instances (host or provider)",
	                std::bind(&YServer::OnQueryModuleRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PublicKey, JsonSchema(),
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Module type invalid or too many URL fragments") },
	                  { web::http::status::not_found, JsonSchema(json::value_t::null, "Host or provider not found") },
	                  { web::http::status::ok, JsonSchema(json::value_t::null, "Response for the specified resource") } },
	                "Tries to query a particular host or provider for custom per-module-type information");

	AddHTTPListener(
	  "list.*", { "module" }, "List Module Instances", "List all module instances (hosts or providers)",
	  std::bind(&YServer::OnListModulesRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	  { web::http::verb::get }, EYAPIAccessLevel::PublicKey,
	  JsonSchema(
	    { { "provider", JsonSchema(json::value_t::number_unsigned, "The ID of the provider to use for filtering", 0U) },
	      { "provider-domain", JsonSchema(json::value_t::string, "The provider domain to use for filtering (incompatible with 'provider')", std::string()) },
	      { "locale", JsonSchema(json::value_t::string, "The desired locale to translate texts where applicable", ISOLanguages::GetLocale(ELanguage::English)) },
	      { "skip-host-games", JsonSchema(json::value_t::boolean, "If true, will not list games of returned hosts", false) },
	      { "host", JsonSchema(json::value_t::number_unsigned, "The ID of the host to use for filtering", 0U) },
	      { "host-domain", JsonSchema(json::value_t::string, "The host domain to use for filtering (incompatible with 'host')", std::string()) },
	      { "bet-unit", JsonSchema(json::value_t::number_float,
	                               "The minimal bet unit in units of currency of the selected provider. Used to filter-out games with a higher min-bet.", 0.0) },
	      { "max-bet-rules",
	        JsonSchema(
	          json::value_t::string,
	          "List of max bet rules to apply to the listing (do not list hosts which are unplayable due to the rule). Requires bet-unit. Format is [HostType1]=[max bet in currency];[HostType2]=[max bet];...",
	          std::string()) },
	      { "download-config", JsonSchema(json::value_t::boolean, "If given, also return module configurations", false) } }),
	  { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Module type invalid or too many URL fragments") },
	    { web::http::status::not_found, JsonSchema(json::value_t::null, "Filtered host or provider not found") },
	    { web::http::status::ok,
	      JsonSchema(json::value_t::array, "List of modules")
	        .SetChildSchema(JsonSchema(
	          { { "type", JsonSchema(json::value_t::number_unsigned, "The type of this module") },
	            { "type-name", JsonSchema(json::value_t::string, "The type name of this module") },
	            { "name", JsonSchema(json::value_t::string, "The name of this module") },
	            { "owner", JsonSchema(json::value_t::string, "The UID of the owner of this module (if any)", std::string()) },
	            { "maxPlayers", JsonSchema(json::value_t::number_unsigned, "The maximum allowed number of players on this module") },
	            { "players", JsonSchema(json::value_t::number_unsigned, "The current number of players on this module") },
	            { "isFull", JsonSchema(json::value_t::boolean, "True/false if the module has no more capacity for new players") },
	            { "ready", JsonSchema(json::value_t::boolean, "True/false if the module is ready for games to be played on it") },
	            { "configured", JsonSchema(json::value_t::boolean, "True/false if the module is configured (had no config errors)") },
	            { "config-error", JsonSchema(json::value_t::string, "The configuration error which occurred while loading this module", std::string()) },
	            { "uid", JsonSchema(json::value_t::string, "[ADMIN ONLY] The UID of this module") },
	            { "actions",
	              JsonSchema(json::value_t::object, "[ADMIN ONLY] list of actions which can be performed on this module", json(json::value_t::object))
	                .SetChildSchema(JsonSchema(
	                  { { "schema", SchemaOfSchema }, { "type", JsonSchema(json::value_t::string, "The type of action").SetToEnumType<EModuleAdminActionTarget>() } })) },
	            { "raw-config", JsonSchema(json::value_t::object, "The configuration of this module (only if download-config was requested)") },
	            { "domain", JsonSchema(json::value_t::string, "The domain of this module", std::string()) },
	            { "status", JsonSchema(json::value_t::string, "The status of this module").SetToEnumType<EModuleStatus>() },
	            { "id", JsonSchema(json::value_t::number_unsigned, "The ID of this module") } })) } },
	  "Returns a list of requested modules and their information");

	AddHTTPListener("restart", {}, "Restart", "Gracefully restart the server",
	                std::bind(&YServer::OnRestartRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PrivateKey, {},
	                { { web::http::status::ok, JsonSchema(json::value_t::null, "The server will restart") } });

	AddHTTPListener(
	  "history", {}, "Game History", "Get a list of game rounds (optionally filtered by accounting round ID or game round ID)",
	  std::bind(&YServer::OnHistoryRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4), { web::http::verb::get },
	  EYAPIAccessLevel::PrivateKey,
	  JsonSchema({ { "p", JsonSchema(json::value_t::string, "Player account UID") },
	               { "r", JsonSchema(json::value_t::string, "Game round ID (incompatible with 'a')") },
	               { "a", JsonSchema(json::value_t::string, "Accounting round ID (incompatible with 'r')") },
	               { "s", JsonSchema(json::value_t::string, "Signature of this request") } }),
	  { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Missing 'p' player account ID") },
	    { web::http::status::forbidden, JsonSchema(json::value_t::null, "Bad signature") },
	    { web::http::status::not_found, JsonSchema(json::value_t::null, "No gameround found with given parameters") },
	    { web::http::status::ok, GameRoundSnapshot::GetSchema() | AccountingRoundSnapshot::GetSchema() |
	                               JsonSchema({ { "gameRounds", JsonSchema(json::value_t::array).SetChildSchema(GameRoundSnapshot::GetSchema()) },
	                                            { "accountingRounds", JsonSchema(json::value_t::array).SetChildSchema(AccountingRoundSnapshot::GetSchema()) } }) } });

	AddHTTPListener("replay", {}, "Replay", "Replay a specific game round",
	                std::bind(&YServer::OnReplayRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PublicKey,
	                JsonSchema({ { "provider", JsonSchema(json::value_t::string, "Casino provider ID or UID") },
	                             { "player", JsonSchema(json::value_t::string, "Player username") },
	                             { "round", JsonSchema(json::value_t::string, "Gameround ID") } }),
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Missing 'provider', 'player' or 'round'") },
	                  { web::http::status::not_found, JsonSchema(json::value_t::null, "No gameround found with given parameters") },
	                  { web::http::status::found, JsonSchema(json::value_t::null, "The launch url is given in the Location header") } });

	for (const EClientType type : EClientType::_values())
	{
		YEndpointAPI* api = GetAPI(type);
		if (!api)
			continue;

		const std::string clientTypeStr(type._to_string());
		JsonSchema schemaForAPI(json::value_t::object, "Set of query parameters that are used for " + clientTypeStr + " clients");
		for (const auto& param : api->QueryParams) schemaForAPI.AddMember(param.first, param.second);

		AddHTTPListener("api." + clientTypeStr, {}, "Create Endpoint", "Create a " + clientTypeStr + " endpoint",
		                std::bind(&YServer::OnCreateEndpointRequest, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
		                { web::http::verb::get, web::http::verb::post }, type == EClientType::Player ? EYAPIAccessLevel::PublicKey : EYAPIAccessLevel::PrivateKey,
		                schemaForAPI,
		                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Bad structure of request URL or invalid body content") },
		                  { web::http::status::service_unavailable, JsonSchema(json::value_t::null, "The server is full") },
		                  { web::http::status::not_implemented, JsonSchema(json::value_t::null, "API is disabled") },
		                  { web::http::status::unauthorized, JsonSchema(json::value_t::null, "No permission to create endpoint or banned") },
		                  { web::http::status::not_found, JsonSchema(json::value_t::null, "Unknown API type") } },
		                "Creates and returns a new endpoint for the specified API");
	}


	AddHTTPListener("croupier.auth", {}, "Croupier Login", "Try to authenticate a croupier badge",
	                std::bind(&YServer::OnCroupierLogin, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::All, JsonSchema({ { "id", JsonSchema(json::value_t::string, "The badge ID to authenticate with") } }),
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Missing 'id'") },
	                  { web::http::status::unauthorized, JsonSchema(json::value_t::null, "No croupier matches the given badge") },
	                  { web::http::status::ok, FCroupier::Schema } });

	AddHTTPListener("croupier.add", {}, "Add Croupier", "Register a new croupier and return the badge ID",
	                std::bind(&YServer::OnCroupierAdd, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PrivateKey,
	                JsonSchema({ { "name", JsonSchema(json::value_t::string, "The full name of the croupier to add") },
	                             { "location", JsonSchema(json::value_t::string, "The location the croupier is from") },
	                             { "birthdate", JsonSchema(json::value_t::string, "The date of birth of the croupier (in the format day/month/year)") } }),
	                { { web::http::status::bad_request, JsonSchema(json::value_t::null, "Missing 'name', 'location' or 'birthdate'") },
	                  { web::http::status::conflict, JsonSchema(json::value_t::null, "Croupier already exists") },
	                  { web::http::status::ok, JsonSchema(json::value_t::string, "The badge ID which was created") } });

	AddHTTPListener("error", {}, "Display error page", "Load an embedded version of the game on the given endpoint",
	                std::bind(&YServer::OnErrorPage, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::All,
	                JsonSchema({ { "code", JsonSchema(json::value_t::number_integer, "The error code", 0) },
	                             { "msg", JsonSchema(json::value_t::string, "The error message", std::string()) },
	                             { "locale", JsonSchema(json::value_t::string, "The locale to use when displaying the error", std::string()) } }),
	                { { web::http::status::ok, JsonSchema(json::value_t::null, "HTML") } });

	AddHTTPListener("ram-clear", {}, "Clear cached round data and shutdown", "Delete the yserver_data folder and shutdown the server",
	                std::bind(&YServer::OnRAMClear, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3, std::placeholders::_4),
	                { web::http::verb::get }, EYAPIAccessLevel::PrivateKey, {},
	                { { web::http::status::ok, JsonSchema(json::value_t::string, "YServer shutdown successfully") } });
}

YServer::~YServer()
{
	Close("Shutting down forcefully!");
	if (mCurrencyUpdateThread.joinable())
	{
		mShouldQuit.release();
		mCurrencyUpdateThread.join();
	}

	if (Database)
	{
		// Database->Close();
		Database.reset();
	}
	gamehost::gameart::V8Interface::DisposeV8();
}

void YServer::OnNewLog(const LogEntry& entry)
{
	if (!LogAdminAPI || !Endpoint)
		return;

	std::unordered_map<uint8_t, std::set<imaxa_connection_hdl, std::owner_less<imaxa_connection_hdl>>> toSendTo;
	SharedScopedLock lock(LogAdminAPI->Instances);
	for (const auto& val : &LogAdminAPI->Instances | std::views::values)
	{
		auto logger = LogAdminAPI->InstanceCast(val);
		if (!logger->ShouldSkip(*entry.Category, entry.Verbosity))
			toSendTo[val->SerializationType._to_index()].insert(val->ConnectionHandle());
	}

	if (!toSendTo.empty())
	{
		json logEntry(json::value_t::object);
		MakeLogEntryJson(entry, logEntry);
		const json messageJson =
		  formMessage(yprotocol::EServerMessageType::Event, 0,
		              yprotocol::Event(*std::dynamic_pointer_cast<YLoggerRequestHandler>(LogAdminAPI->RequestHandler)->LogEvent, logEntry).AsJsonObject());
		for (const auto& [serializationType, clients] : toSendTo)
		{
			const std::string payload = yprotocol::SerializeJSON(yprotocol::EJSONSerializationType::_from_index(serializationType), messageJson);
			std::error_code ec;
			for (imaxa_connection_hdl_ref hdl : clients)
			{
				Endpoint->send(hdl, payload, websocketpp::frame::opcode::TEXT, ec);
				ec.clear();
			}
		}
	}
}

void YServer::DatabaseForEach(const std::string& keyMatch, const std::function<void(const std::string&, const std::string&)>& work)
{
	rocksdb::Slice startKey(keyMatch);
	rocksdb::ReadOptions opts;
	opts.async_io = true;
	opts.iterate_lower_bound = &startKey;
	std::unique_ptr<rocksdb::Iterator> iter(Database->NewIterator(opts));
	for (iter->SeekToFirst(); iter->Valid() && iter->key().starts_with(startKey); iter->Next())
		work(iter->key().ToString().substr(keyMatch.length()), iter->value().ToString());
}

bool YServer::AuthenticateCroupier(const std::string& croupierDatabaseAddress, const std::string& badge, FCroupier& outCroupierData, std::string& outStatus)
{
	if (croupierDatabaseAddress.empty() || croupierDatabaseAddress == ((IsSecure() ? "https://" : "http://") + HostAddress()))
	{
		const bool bSuccess = Croupiers.Authenticate(badge, outCroupierData);
		outStatus = bSuccess ? "Croupier found in the local database" : "Could not authenticate this badge in the local database";
		return bSuccess;
	}

	web::websockets::uri uri(croupierDatabaseAddress);
	if (uri.get_type() != web::websockets::uri::http)
	{
		outStatus = "Croupier remote database has a bad URL";
		return false;
	}


	auto cli = web::WebClient::New(uri);

	auto response = cli->Request(web::http::verb::get, "/croupier/auth?id=" + badge).get();
	if (response.get_error_code())
	{
		outStatus = response.get_error_code().message();
		return false;
	}

	if (response.get_status_code() == web::http::status::ok)
	{
		json val;
		try
		{
			val = json::parse(response.get_body());
		}
		catch (const std::exception& e)
		{
			throw std::runtime_error("Authentication was successful, but could not read croupier data: " + std::string(e.what()));
		}

		outCroupierData.LoadFromJSON(val);
		return true;
	}

	outStatus = response.get_status_msg();

	return false;
}

bool YServer::Listen()
{
	// Listen on port mPort
	std::error_code ec;
	Endpoint->listen(mPort, ec);
	if (ec)
	{
		Log(Error, "Server could not bind to port %d (%s)", mPort, ec.message().c_str());
		return false;
	}

	rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda, rtfwk_sdl2::ETimedTaskType::TASK_THREAD_SAFE>(
	  [this]() {
		  const uint64_t now = yprotocol::Timestamp();
		  for (ThreadSafeProperty<std::map<std::string, std::shared_ptr<PersistentClientInfo>>>& list : { std::ref(mIPBlacklist), std::ref(mPlayerBlacklist) })
		  {
			  ScopedLock lock(list);
			  for (auto banIt = list->begin(); banIt != list->end();)
			  {
				  std::shared_ptr<PersistentClientInfo> infoPtr = banIt->second;
				  if (infoPtr->BanTimestamp < now && (infoPtr->BanRule.RelevantTimestamps.empty() ||
				                                      yprotocol::IsTimedOut(infoPtr->BanRule.RelevantTimestamps.back(), infoPtr->BanRule.IntervalDuration, now)))
				  {
					  banIt = list->erase(banIt);
				  }
				  else
				  {
					  banIt++;
				  }
			  }
		  }
	  },
	  5000, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Ban list refresher");

	CurrencyAPI->Update(ECurrency(ECurrency::EUR)._to_string());
	mCurrencyUpdateThread = std::thread([this]() {
		do {
			CurrencyAPI->CheckForUpdates();
		} while (!mShouldQuit.try_acquire_for(std::chrono::minutes(1)));
	});
	yutils::SetThreadName(mCurrencyUpdateThread, "currency-api");

	// Start the server accept loop, can fail if port bind fails
	try
	{
		Start("yserver-ws");
		Log(Critical, "Server started on port %d.", mPort);
	}
	catch (const websocketpp::exception& e)
	{
		Log(Error, "Server could not start on port %d: %s", mPort, e.what());
		return false;
	}

	return true;
}

const std::string& YServer::HostAddress() const
{
	return mHostAddress;
}

bool YServer::Running() const
{
	return Endpoint && Endpoint->is_listening();
}

void YServer::ShutdownServer(const std::string& msg)
{
	if (bShuttingDown)
		return;

	SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(CommanderAdminAPI->RequestHandler)->ShutdownEvent, msg);

	Log(Critical, "Shutting down server: %s!", msg.c_str());
	bShuttingDown = true;

	if (mNumClients.load())
		Log(Info, "Closing connection to %u clients because server is shutting down...", mNumClients.load());

	mGameHosts->Shutdown(msg);
	mCasinoProviders->Shutdown(msg);

	for (auto api : mProtectedAPIs)
	{
		if (api.second)
			api.second->OnServerShutdown(msg);
	}
}

int YServer::ResolveResourceMultiOption(const std::vector<YResourcePath>& resourceOptions, std::vector<unsigned char>& outData) const
{
	outData.clear();

	std::vector<std::string> Warnings;

	size_t idx = 0;
	for (const YResourcePath& resource : resourceOptions)
	{
		idx++;
		if (resource.is_web())
		{
			const web::websockets::uri uri(resource);
			if (uri.get_type() != web::websockets::uri::http)
			{
				Warnings.push_back(yutils::Format("Resource lookup of %s failed because the resource URI is invalid", resource.c_str()));
				continue;
			}

			auto client = web::WebClient::New(uri.get_host_port(), uri.get_secure());

			auto response = client->Request(web::http::verb::get, uri.get_resource()).get();
			if (response.get_error_code())
			{
				Warnings.push_back(yutils::Format("Resource lookup request for %s failed: %s", resource.c_str(), response.get_error_code().message().c_str()));
				continue;
			}

			if (response.get_status_code() != web::http::status::ok)
			{
				Warnings.push_back(yutils::Format("Resource lookup response for %s returned bad code %d: %s", resource.c_str(), (int)response.get_status_code(),
				                                  response.get_status_msg().c_str()));
				continue;
			}

			outData = std::vector<unsigned char>(response.get_body().data(), response.get_body().data() + response.get_body().size());
		}
		else
		{
			if (!std::filesystem::exists(resource.as_fspath()))
			{
				Warnings.push_back("No such resource found: " + resource);
				continue;
			}

			if (!std::filesystem::is_regular_file(resource.as_fspath()))
			{
				Warnings.push_back("Resource is not a file: " + resource);
				continue;
			}

			std::ifstream file(resource.as_fspath(), std::ios::binary);
			if (!file.is_open())
			{
				Warnings.push_back("Could not read resource: " + resource);
				continue;
			}

			yutils::FileToVector(file, outData);
		}

		return idx - 1;
	}

	for (const std::string& warning : Warnings) Log(Warning, "%s", warning.c_str());

	return -1;
}

void YServer::OnInitialize(std::unique_ptr<web::websockets::imaxa_endpoint>& e)
{
	WebServer::OnInitialize(e);

	e->set_max_http_body_size(8L << 20);    // 8 MiB
	e->set_max_message_size(8L << 20);    // 8 MiB

	e->set_user_agent(Name());
}

void YServer::PreRun()
{
	web::WebServer::PreRun();

	{
		LogScope Scope;
		rocksdb::ReadOptions opts;
		opts.async_io = true;
		std::unique_ptr<rocksdb::Iterator> iter(Database->NewIterator(rocksdb::ReadOptions()));
		iter->SeekToFirst();
		for (; iter->Valid(); iter->Next())
		{
			if (iter->key().size() > 30)
				Scope.Log(*this, Verbose, "%s : %s", iter->key().ToString().c_str(), iter->value().ToString().c_str());
			else
				Scope.Log(*this, Verbose, "%30s : %s", iter->key().ToString().c_str(), iter->value().ToString().c_str());
		}
	}

	mCasinoProviders->LoadUnfinishedRounds();
	mGameHosts->LoadUnfinishedRounds();

	if (!mCasinoProviders->LoadAll())
		Log(Warning, "No casino provider configurations found in folder %s!", mCasinoProviders->ModuleConfigDir.c_str());

	if (!mGameHosts->LoadAll())
		Log(Warning, "No game host configurations found in folder %s!", mGameHosts->ModuleConfigDir.c_str());
}

void YServer::ClearSessionCookie(imaxa_connection_ptr con, const std::string& path)
{
	std::vector<std::string> Options = { "Max-Age=0" };
	if (bSessionCookieSecure)
		Options.push_back("Secure");
	SendMessage(con, 0, yprotocol::EServerMessageType::Cookie, yprotocol::TCookie("SESSION", "", Options, path).AsJson());
}

void YServer::SpectatorEvent(const EventTemplate& event, const json& ctx) const noexcept
{
	ViewAdminAPI->BroadcastEvent(event, ctx);
	CommanderAdminAPI->BroadcastEvent(event, ctx);
}

YModuleContainer<provider::TCasinoProvider>* YServer::ProviderContainer() const
{
	return mCasinoProviders.get();
}

YModuleContainer<gamehost::TGameHost>* YServer::HostContainer() const
{
	return mGameHosts.get();
}

json YServer::ListProviders(const ModuleListOptions& options) const
{
	auto providers = find_providers(options);

	json outProviders(json::value_t::array);
	for (const auto& provider : providers)
		outProviders.push_back(provider->GetDescriptor({ .bAdmin = options.bAdmin, .bIncludeRawConfig = options.bDownloadConfig, .Language = options.language }));
	return outProviders;
}

json YServer::ListGameHosts(const ModuleListOptions& options) const
{
	auto hosts = find_game_hosts(options);

	std::shared_ptr<provider::TCasinoProvider> provider;
	if (options.providerID)
	{
		provider = find_provider(options.providerID);
	}
	else if (options.providerType != provider::ProviderType::Null && !options.providerDomain.empty())
	{
		provider = get_provider(provider::TCasinoProvider::GenUID(options.providerType, options.providerDomain));
	}

	gamehost::FGameConfigurationCollection collection;

	struct FMappedHostData
	{
		size_t Index = 0;
		std::shared_ptr<gamehost::TGameHost> Host;
	};

	json outHosts(json::value_t::array);
	std::map<uint32_t, FMappedHostData> hostIDtoIndexMap;
	for (const auto& host : hosts)
	{
		if (!options.bShowHidden && !host->CanAccessFrom(provider))
			continue;

		outHosts.push_back(host->GetDescriptor({ .bAdmin = options.bAdmin, .bIncludeRawConfig = options.bDownloadConfig, .Language = options.language }));
		if (!options.bSkipGames)
		{
			outHosts.back()["games"] = json(json::value_t::object);
			hostIDtoIndexMap.insert_or_assign(host->ID(), FMappedHostData({ outHosts.size() - 1, host }));
			collection.Add(host, host->LaunchInfo().Games);
		}
	}

	if (!options.bSkipGames)
	{
		std::map<gamehost::HostType, double> maxBets;
		if (options.betUnit > 0.0)
		{
			maxBets = options.maxBetForGameType;
			for (gamehost::HostType type : gamehost::HostType::_values())
				if (type != gamehost::HostType::Null)
					maxBets.try_emplace(type, 0.0);

			if (provider)
			{
				const double jurisdictionalMaxBet = provider->GetMaxBet();
				if (jurisdictionalMaxBet > 0.0)
				{
					for (auto& [hostType, maxBet] : maxBets)
					{
						if (maxBet > 0.0)
							maxBet = std::min(maxBet, jurisdictionalMaxBet);
						else
							maxBet = jurisdictionalMaxBet;
					}
				}
			}

			// convert to credits
			for (auto& p : maxBets) p.second /= options.betUnit;
		}

		const gamehost::PopularitySortedGameLaunchInfos allGames(collection, provider ? provider->UniqueIdentifier() : std::string(), maxBets);
		for (const auto& gamePair : allGames)
		{
			if (!options.bIncludeDisabled && !gamePair.Game.bEnabled)
				continue;
			const FMappedHostData& hostData = hostIDtoIndexMap[gamePair.HostID];
			outHosts[hostData.Index]["games"][gamePair.Game.Key] = gamePair.ToJSON(provider, *hostData.Host, options.bAdmin, options.language);
		}
	}

	return outHosts;
}

json YServer::VersionJSON() const
{
	json version(json::value_t::object);
	version["major"] = Version.major;
	version["minor"] = Version.minor;
	version["revision"] = Version.patch;
	return version;
}

EPlayerMultiSessionMode YServer::GetMultiSessionMode() const
{
	return mMultiSessionMode;
}

bool YServer::AllowReconnect() const
{
	return bAllowReconnect;
}

bool YServer::IsSessionCookieSecure() const
{
	return bSessionCookieSecure;
}

uint64_t YServer::ActivityTimeout() const
{
	return mActivityTimeout;
}

const std::filesystem::path& YServer::EndpointSaveDir() const
{
	return mEndpointDir;
}

std::shared_ptr<YServerClient> YServer::AddClient(imaxa_connection_ptr con, std::optional<security::JWToken> token)
{
	if (bShuttingDown)
		throw yprotocol::InitError("The server is shutting down - new connections are disabled.");

	if (!con)
		throw yprotocol::InitError("Bad connection while adding client");

	std::string path = con->get_resource();
	path = path.substr(0, path.find_first_of('?'));
	const std::vector<std::string> pathFragments = Split(path, "/", true);
	if (pathFragments.empty())
		throw yprotocol::InitError("Failed getting connection - no resource endpoint provided!");

	if (pathFragments.size() < 2)
		throw yprotocol::InitError(
		  "Where's your keys, huh? Oh did you forget them? Oh no I'm so sorry that happened to you... GIVE ME THOSE FUCKING KEYS YOU CRAZY BASTARD");

	std::string signature;
	if (pathFragments.size() >= 3)
		signature = pathFragments[2];

	std::optional<EClientType> typ = EClientType::_from_string_nocase_nothrow(pathFragments[0].c_str());
	if (!typ)
		throw yprotocol::InitError("No such API '" + pathFragments[0] + "'");

	YEndpointAPI* api = GetAPI(*typ);
	if (!api)
		throw yprotocol::InitError("Not implemented");


	bool bFound = false;
	std::shared_ptr<YEndpoint> auth = api->Authenticate(pathFragments[1], signature, bFound);
	try
	{
		std::shared_ptr<YAPIClient> instance;
		if (bFound)
		{
			if (!auth)
				throw YEndpointError(YError::ERR_UNAUTHORIZED, "Authorization failed!");

			instance = api->AddInstance(con, auth, std::move(token));
		}
		else if (AllowReconnect())
		{
			instance = api->FindInstanceByEndpoint(pathFragments[1]);

			if (!instance)
				throw YEndpointError(YError::ERR_UNAUTHORIZED, "No such endpoint exists!");

			if (!instance->Endpoint->CheckAuth(EHashAlgorithm::SHA1, signature))
				throw YEndpointError(YError::ERR_UNAUTHORIZED, "Authorization failed!");

			try
			{
				instance->OnReconnected(con);
			}
			catch (const std::runtime_error& e)
			{
				throw YEndpointError(YError::ERR_CONNECTION_UNAVAILABLE, std::string("This endpoint is unavailable: ") + e.what());
			}

			instance->Reconnect(con);
			Log(Normal, "Client %s reconnected!", instance->UniqueIdentifier().c_str());
		}
		else
		{
			throw YEndpointError(YError::ERR_UNAUTHORIZED, "No such endpoint exists or reconnects are disallowed!");
		}

		return instance;
	}
	catch (const YEndpointError& e)
	{
		SendMessage(con, 0, yprotocol::EServerMessageType::Error, make_error_object(e.ErrorCode(), "add-instance", e.what()));

		if (e.ErrorCode() == static_cast<uint16_t>(yprotocol::YProtocolError::ERR_INTERNAL_ERROR))
			Log(Warning, "Couldn't open %s client on endpoint %s due to server error: %s", typ->_to_string(), pathFragments[1].c_str(), e.what());

		json ctx(json::value_t::object);
		ctx["type"] = typ->_to_string();
		ctx["endpoint"] = pathFragments[1];
		ctx["error"] = e.what();
		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(CommanderAdminAPI->RequestHandler)->InstanceCreationFailedEvent, ctx);
		return {};
	}
	catch (const yprotocol::InternalError& e)
	{
		Log(Warning, "Couldn't open %s client on endpoint %s due to internal server error: %s", typ->_to_string(), pathFragments[1].c_str(), e.what());

		SendMessage(con, 0, yprotocol::EServerMessageType::Error, make_error_object(YError::ERR_INTERNAL_ERROR, "add-instance", e.what()));

		json ctx(json::value_t::object);
		ctx["type"] = typ->_to_string();
		ctx["endpoint"] = pathFragments[1];
		ctx["error"] = e.what();
		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(CommanderAdminAPI->RequestHandler)->InstanceCreationFailedEvent, ctx);
		return {};
	}
}

int YServer::RunOnce()
{
	if (!Running())
		return 0;

	const uint64_t now = yprotocol::Timestamp();
	Time = now;

	const bool bShouldReport = mReportInterval && yprotocol::IsTimedOut(mLastReport, mReportInterval * 1000, now);
	if (bShouldReport)
	{
		mLastReport = now;
	}

	const bool bIsInShutdown = bShuttingDown;

	bool bHasPlayers = false;
	std::set<PlayerPtr> createReportsForPlayers;
	const uint64_t nowSystem = ytime::GetSystemTimeMsec();
	LogScope reportScope;
	{
		SharedScopedLock lock(PlayerAdminAPI->Instances);
		bHasPlayers = !PlayerAdminAPI->Instances->empty();

		if (bShouldReport)
		{
			const uint32_t clients = mNumClients.load();
			reportScope.Log(LogStatus, Normal, "There are %u clients on the server.", clients);
			if (bHasPlayers)
			{
				reportScope.Log(LogStatus, Normal, "Of those, there are %lu players:", PlayerAdminAPI->Instances->size());
			}
			else
			{
				if (clients)
					reportScope.Log(LogStatus, Normal, "None of those are players.");
			}
		}

		for (YEndpointAPI::InstanceMap_t::const_reference clientInfo : &PlayerAdminAPI->Instances)
		{
			auto player = PlayerAPI::InstanceCast(clientInfo.second);

			const bool bConnected = player->Persistence(EPlayerPersistenceMode::Connection);
			const bool bSessionExpired = player->Account()->SessionExpired();

			if (!bConnected || bSessionExpired)
			{
				if (bShouldReport)
				{
					reportScope.Log(LogStatus, Normal, "\t%s/%s on %s (age ~%ldmin  ping %ums): disconnected, persistence required(%s) [%s]",
					                player->Account()->ID().ProviderUID.c_str(), player->Account()->ID().Username.c_str(), player->Host()->Name().c_str(),
					                (nowSystem - player->Joined()) / 60000, player->Latency(), player->GetPersistence().Print(", ").c_str(),
					                player->Game()->BetStatus()._to_string());
				}
				continue;
			}

			if (bShouldReport)
				createReportsForPlayers.insert(player);

			if (!player->Connected())
				continue;

			if (player->IsRegistered() && !player->IsActive())
			{
				if (bConnected)
				{
					player->BroadcastEvent(yprotocol::Event(*std::dynamic_pointer_cast<YPlayerRequestHandler>(player->RequestHandler)->ClientClosingEvent));
					player->SendError(player->ServerContext(true), make_error_object(YError::ERR_SESSION_TIMED_OUT, "time-out", "Kicked due to inactivity"), true);
				}
			}
		}
	}

	for (const PlayerPtr& player : createReportsForPlayers)
	{
		ScopedLock lock(*player);
		reportScope.Log(LogStatus, Normal, "\t%s/%s on %s (age ~%ldmin  ping %ums): (%s) [%s]", player->Account()->ID().ProviderUID.c_str(),
		                player->Account()->ID().Username.c_str(), player->Host()->Name().c_str(), (nowSystem - player->Joined()) / 60000, player->Latency(),
		                player->GetPersistence().Print(", ").c_str(), player->Game()->BetStatus()._to_string());
	}

	reportScope.Flush();    // log it!

	if (bIsInShutdown && !bHasPlayers)
		Close("Shutting down!");

	return 0;
}

void YServer::Close(const std::string& msg)
{
	if (!Initialized() || !Running())
		return;

	if (!bShuttingDown)
		ShutdownServer(msg);

	mProtectedAPIs.clear();

	mGameHosts->Dispose();
	mCasinoProviders->Dispose();

	if (bRAMClear)
	{
		std::error_code ec;
		std::filesystem::remove_all(mYServerDataDir, ec);
		if (ec)
			Log(Warning, "Could not ram-clear yserver data: %s", ec.message().c_str());
		else
			Log(Important, "Successfully cleared yserver data!");
	}

	Stop();

	Log(Critical, "Server is closed.");
}

StaticGameInformation YServer::GetGame(std::shared_ptr<gamehost::TGameHost> host, const std::string& gameName) const
{
	StaticGameInformation ret(gameName, gamehost::HostType::Null);
	if (host)
	{
		ret = host->GetGame(gameName);
		if (ret.Type != gamehost::HostType::Null)
			return ret;
	}
	return ret;
}

void YServer::OnConfigLoaded(const std::filesystem::path& filename)
{
	YProtocolWebServer::OnConfigLoaded(filename);

	mName = GetConfig("name").get<std::string>();

	const std::string YServerName = yutils::Format("YServer %d.%d.%d", Version.major, Version.minor, Version.patch);
	if (!mName.empty())
		mName = YServerName;
	else
		mName = YServerName + " | " + mName;

	mServerID = GetConfig("server-id").get<std::string>();

	mHostAddress = GetConfig("host").get<std::string>();

	mMaxClients = GetConfig("max-clients").get<uint32_t>();
	bDiskSync = GetConfig("disk-sync").get<bool>();

	mMaxLatency = GetConfig("max-latency").get<uint32_t>();

	SpawnWorkers(GetConfig("async-workers").get<uint32_t>(), "yworker");

	mGameHosts->DefaultReloadTimeout = 1000 * GetConfig("reload-timeout.game-host").get<uint64_t>();
	mCasinoProviders->DefaultReloadTimeout = 1000 * GetConfig("reload-timeout.casino-provider").get<uint64_t>();

	bAllowPilotSeat = GetConfig("pilot-seat").get<bool>();
	Log(bAllowPilotSeat ? Important : Info, "Remote game control is %s", bAllowPilotSeat ? "ENABLED" : "DISABLED");

	mYServerDataDir = GetConfig("storage-directory").get<std::string>();
	if (mYServerDataDir.is_relative())
		mYServerDataDir = filename.parent_path() / mYServerDataDir;
	Log(Info, "Game hosts and casino providers will save thier data to %s", mYServerDataDir.c_str());

	mCasinoProviders->ModuleStorageDir = mYServerDataDir / "providers";
	std::filesystem::create_directories(mCasinoProviders->GetRoundStorageDir());
	mGameHosts->ModuleStorageDir = mYServerDataDir / "hosts";
	std::filesystem::create_directories(mGameHosts->GetRoundStorageDir());

	mEndpointDir = mYServerDataDir / "endpoints";
	std::filesystem::create_directories(mEndpointDir);

	mResourceDir = mYServerDataDir / "resources";
	if (std::filesystem::exists(mResourceDir))
	{
		std::error_code ec;
		std::filesystem::remove_all(mResourceDir, ec);
		if (ec)
			Log(Warning, "Could not remove resource dir %s: %s", mResourceDir.c_str(), ec.message().c_str());
	}
	std::filesystem::create_directories(mResourceDir);

	mConfigDir = GetConfig("config-dir").get<std::string>();
	if (mConfigDir.is_relative() && filename.has_parent_path())
		mConfigDir = filename.parent_path() / mConfigDir;
	std::filesystem::create_directories(mConfigDir);

	mUserConfigDir = GetConfig("user-config-dir").get<std::string>();
	if (!mUserConfigDir.empty())
	{
		if (mUserConfigDir.is_relative() && filename.has_parent_path())
			mUserConfigDir = filename.parent_path() / mUserConfigDir;

		if (mUserConfigDir == mConfigDir)
			mUserConfigDir.clear();
		else
			std::filesystem::create_directories(mUserConfigDir);
	}

	bEnableArchiving = GetConfig("archive-changed-configs").get<bool>();

	struct ModuleLoadStruct
	{
		yserver::YModuleContainerBase* Container;
		std::string SubFolder;
	};
	for (const ModuleLoadStruct& load : std::list<ModuleLoadStruct>({ { mCasinoProviders.get(), "providers" }, { mGameHosts.get(), "hosts" } }))
	{
		load.Container->bEnableArchiving = bEnableArchiving;
		load.Container->ModuleConfigDir = mConfigDir / load.SubFolder;
		if (!mUserConfigDir.empty())
			load.Container->ModuleUserConfigDir = mUserConfigDir / load.SubFolder;
		if (bEnableArchiving)
			std::filesystem::create_directories(load.Container->ModuleConfigDir / ARCHIVE_FOLDER);
	}

	DatabaseFilepath = GetConfig("database").get<std::string>();
	if (DatabaseFilepath.is_relative())
		DatabaseFilepath = mYServerDataDir / DatabaseFilepath;

	rocksdb::Options dbOptions;
	dbOptions.create_if_missing = true;

	rocksdb::DB* LoadedDB = NULL;
	rocksdb::Status status = rocksdb::DB::Open(dbOptions, DatabaseFilepath, &LoadedDB);
	Database.reset(LoadedDB);

	if (!status.ok())
	{
		Log(Critical, "Unable to open or create database at %s: %s", DatabaseFilepath.c_str(), status.ToString().c_str());
		throw ConfigError("Unable to open local DB");
	}

	Log(Info, "Successfully opened database at %s.", DatabaseFilepath.c_str());

	mReportInterval = GetConfig("report-interval").get<uint32_t>();

	const size_t logSize = GetConfig("log-history-size").get<uint32_t>();
	Logger::SetCachedHistorySize(logSize);

	MaxMessageRate.LoadSubconfiguration(GetConfig("ban.max-message-rate"));
	mIPMaxConnectAttempts.LoadSubconfiguration(GetConfig("ban.ip-max-connect-attempts"));
	mPlayerMaxConnectAttempts.LoadSubconfiguration(GetConfig("ban.player-max-connect-attempts"));

	mActivityTimeout = GetConfig("session.activity-timeout").get<uint64_t>() * 1000;
	Log(Info, "Activity timeout is set to %d seconds", mActivityTimeout);
	bAllowReconnect = GetConfig("session.allow-reconnect").get<bool>();
	if (!bAllowReconnect)
		Log(Warning, "Reconnecting to a live session is disabled");
	bSessionCookieSecure = GetConfig("session.secure").get<bool>();
	const std::string multiSessionStr = GetConfig("session.global-multi-session").get<std::string>();
	auto opt = EPlayerMultiSessionMode::_from_string_nocase_nothrow(multiSessionStr.c_str());
	if (!opt)
		throw ConfigError("multi-session configured incorrectly. unknown value '" + multiSessionStr + "'");

	mMultiSessionMode = *opt;
	if (mMultiSessionMode == EPlayerMultiSessionMode::Default)
		throw ConfigError("server multi session mode cannot be 'default' - it must be set to something specific");

	json keys = GetConfig("http-auth-keys");
	{
		ScopedLock lock(mHTTPAuthKeys);
		mHTTPAuthKeys->emplace(mLegacyKey.Public, mLegacyKey);
		if (keys.is_array() && keys.size())
		{
			for (const json& keyJSON : keys)
			{
				if (keyJSON.is_string())
				{
					yserver::YAuthKey key({ keyJSON.get<std::string>(), std::string(), std::string() });
					mHTTPAuthKeys->emplace(key.Public, key);
				}
				else if (keyJSON.is_object() && keyJSON.contains("key"))
				{
					yserver::YAuthKey key({ keyJSON["key"].get<std::string>(), keyJSON.contains("secret") ? keyJSON["secret"].get<std::string>() : "",
					                        keyJSON.contains("tag") ? keyJSON["tag"].get<std::string>() : "" });
					mHTTPAuthKeys->emplace(key.Public, key);
				}
				else
				{
					Log(Warning, "Detected a bad authorizatzion key in the configuration - should be a string or an object with members 'key', 'secret' and 'tag'");
				}
			}
		}

		if (mHTTPAuthKeys->empty())
		{
			Log(Warning, "No valid HTTP authorization keys found. YServer plain HTTP requests will be disabled.");
		}
		else
		{
			Log(Info, "Loaded %lu HTTP authorization keys.", mHTTPAuthKeys->size());
		}
	}

	mCasinoProviders->OnModuleRegistration += std::bind(&YServer::OnModuleRegistration, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);
	mGameHosts->OnModuleRegistration += std::bind(&YServer::OnModuleRegistration, this, std::placeholders::_1, std::placeholders::_2, std::placeholders::_3);

	json staticProviderConf = GetConfig("static-provider-config");
	for (provider::ProviderType type : provider::ProviderType::_values())
	{
		if (type != provider::ProviderType::Null)
		{
			auto templateProvider = mCasinoProviders->LoadSingle(type, {});
			if (templateProvider)
			{
				TConfiguration& staticConf = mCasinoProviders->StaticConfigs.emplace(type, templateProvider->GetStaticConfigSchema()).first->second;
				staticConf.LoadSubconfiguration(staticProviderConf[type._to_string()]);
				Log(Info, "Loaded static configuration of casino provider type %s", type._to_string());
			}
		}
	}

	json staticHostConf = GetConfig("static-host-config");
	for (gamehost::HostType type : gamehost::HostType::_values())
	{
		if (type != gamehost::HostType::Null)
		{
			auto templateHost = mGameHosts->LoadSingle(type, {});
			if (templateHost)
			{
				TConfiguration& staticConf = mGameHosts->StaticConfigs.emplace(type, templateHost->GetStaticConfigSchema()).first->second;
				staticConf.LoadSubconfiguration(staticHostConf[type._to_string()]);
				Log(Info, "Loaded static configuration of game host type %s", type._to_string());
			}
		}
	}

	for (const auto& api : mProtectedAPIs)
	{
		if (!api.second)
			continue;

		const json apiConfig = GetConfig(JsonPath("api").Child(std::string(api.first._to_string())));
		api.second->LoadSubconfiguration(apiConfig, filename);
	}

	const uint32_t expireDays = GetConfig("round-archive-expire.age-days").get<uint32_t>();
	mCasinoProviders->MinNumRounds = PlayerAdminAPI->MaxHistorySize;
	mCasinoProviders->ExpireDays = expireDays;
	mGameHosts->MinNumRounds = PlayerAdminAPI->MaxHistorySize;
	mGameHosts->ExpireDays = expireDays;

	mImageCacheDuration = GetConfig("image-cache-duration").get<uint32_t>() * 60 * 60;    // hours to seconds

	mPlayForFunCredits = GetConfig("play-for-fun.credits").get<uint64_t>();
	GetConfig("play-for-fun.currency").get_to(mPlayForFunCurrency);

	CurrencyAPI->LoadSubconfiguration(GetConfig("currency-api"), filename);

	const std::filesystem::path croupierListFileLocation = mYServerDataDir / "croupiers.list";
	bool bResave = true;    // re-save to create badge ID comments
	if (std::filesystem::exists(croupierListFileLocation))
	{
		std::ifstream croupierListFile(croupierListFileLocation);
		try
		{
			json listJson = json::parse(croupierListFile, nullptr, true, true);
			Croupiers.Load(listJson);
		}
		catch (const std::exception& e)
		{
			bResave = false;    // don't resave on error to prevent wiping the entire file
			TLOG(LogCroupierDB, Error, "Could not load croupier DB file: %s", e.what());
		}
	}

	if (bResave)
	{
		std::ofstream croupierListFile(croupierListFileLocation);
		croupierListFile << Croupiers.Save();
	}
}

void YServer::OnEventTriggered(const yprotocol::Event& event)
{
	SharedScopedLock lock(PlayerAdminAPI->Instances);
	for (const auto& [id, client] : &PlayerAdminAPI->Instances)
	{
		ScopedLock lock2(*client);
		client->TriggerEvent(event);
	}
}

std::shared_ptr<ResourceInfo> YServer::AddResourceEndpoint(const Player& player, const std::string& resource)
{
	auto resInfo = std::make_shared<ResourceInfo>(mResourceDir / resource, std::dynamic_pointer_cast<const YGameClient>(player.shared_from_this()));
	ScopedLock lock(mResourceEndpoints);
	(&mResourceEndpoints)[resource] = resInfo;
	return resInfo;
}

void YServer::RemoveAllResourceEndpoints(Player& player)
{
	std::map<std::string, std::weak_ptr<ResourceInfo>> playerEndpoints;
	{
		ScopedLock lock(player);
		playerEndpoints = player.HelpScreenResources();
		player.ClearHelpScreenResources();
	}

	std::list<std::map<std::string, std::shared_ptr<ResourceInfo>>::node_type> toErase;    // will destroy shared pointers when it goes out of scope!
	{
		ScopedLock lock(mResourceEndpoints);
		auto find = mResourceEndpoints->begin();
		for (const auto& p : playerEndpoints)
		{
			while (find != mResourceEndpoints->end() && find->first < p.first) find++;

			if (find == mResourceEndpoints->end())
				break;

			if (find->first == p.first)
				toErase.push_back(mResourceEndpoints->extract(find));
		}
	}

	for (const auto& find : toErase) SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(ViewAdminAPI->RequestHandler)->ResourceUnloadedEvent, find.key());
}

void YServer::SetParam(const std::string& name, const std::string& value) const
{
	rocksdb::WriteOptions options;
	options.sync = false;
	Database->Put(options, name, value);
}

void YServer::DeleteParam(const std::string& name) const
{
	rocksdb::WriteOptions options;
	options.sync = false;
	Database->Delete(options, name);
}

std::string YServer::GetParam(const std::string& name, const std::string& fallback) const
{
	rocksdb::ReadOptions options;
	options.fill_cache = true;
	std::string outVal(fallback);
	Database->Get(options, name, &outVal);
	return outVal;
}

std::string YServer::GetOrSetParam(const std::string& name, const std::string& value) const
{
	rocksdb::ReadOptions options;
	options.fill_cache = false;
	std::string outVal;
	if (!Database->Get(options, name, &outVal).ok())
	{
		outVal = value;
		SetParam(name, value);
	}
	return outVal;
}

void YServer::OnInstanceAddedOrRemoved(const std::shared_ptr<YAPIClient>& instance, bool bAdded)
{
	const json clientObj(instance->GetAdminPacket());
	if (bAdded)
	{
		const uint32_t numClientsPrev = mNumClients++;
		Log(Info, "Added new '%s' client at %s (%s) [%u->%u of %u]", instance->Type._to_string(), instance->ConInfo->IP.c_str(), instance->Describe().c_str(),
		    numClientsPrev, numClientsPrev + 1, mMaxClients);

		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(ViewAdminAPI->RequestHandler)->NewClientEvent, clientObj);

		if (CommanderAdminAPI)
		{
			SharedScopedLock lock(CommanderAdminAPI->Instances);
			for (const auto& inst : &CommanderAdminAPI->Instances) CommanderAPI::InstanceCast(inst.second)->OnNewClientAdded(instance);
		}
	}
	else
	{
		const uint nClients = mNumClients--;
		Log(Info, "Dropped '%s' client (%s) owned by %s(%s). [%d->%d of %s]", instance->Type._to_string(), instance->Describe().c_str(),
		    instance->Endpoint->Key.Public.c_str(), instance->Endpoint->Key.Tag.c_str(), nClients, nClients - 1,
		    (mMaxClients > 0) ? std::to_string(mMaxClients).c_str() : "unlimited");

		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(ViewAdminAPI->RequestHandler)->DroppedClientEvent, clientObj);
	}
}

void YServer::OnRAMClear(imaxa_connection_ptr con, const std::vector<std::string>& fragments, const web::QueryString& query, const YAuthKey& key)
{
	bRAMClear = true;
	rtfwk_sdl2::pApp->Delay([]() { rtfwk_sdl2::pApp->safeShutdown(); }, 1000);
	// rtfwk_sdl2::pApp->safeShutdown();

	std::error_code ec;
	con->set_status(web::http::status::ok, ec);
}

YEndpointAPI* YServer::GetAPI(EClientType type) const
{
	auto find = mProtectedAPIs.find(type);
	if (find != mProtectedAPIs.end())
		return find->second;
	return NULL;
}

std::shared_ptr<PlayerEndpoint> YServer::GetPlayerEndpoint(const std::string& address) const
{
	// first try searching for endpoints alerady registered with the server
	{
		SharedScopedLock lock(PlayerAdminAPI->Endpoints);
		auto find = PlayerAdminAPI->Endpoints->find(address);
		if (find != PlayerAdminAPI->Endpoints->end())
			return std::dynamic_pointer_cast<PlayerEndpoint>(find->second);
	}

	// then proceed to check if any such endpoints are being used by online players
	{
		SharedScopedLock lock(PlayerAdminAPI->Instances);
		auto find = PlayerAdminAPI->Instances->find(address);
		if (find != PlayerAdminAPI->Instances->end())
			return std::dynamic_pointer_cast<PlayerEndpoint>(find->second->Endpoint);
	}

	// if that fails, go and search for the endpoint on disk

	std::ifstream endpointFile(mEndpointDir / address);
	if (!endpointFile.is_open())
		return {};

	json endpointJson;
	try
	{
		endpointFile >> endpointJson;
	}
	catch (const std::exception& e)
	{
		Log(Warning, "Endpoint file %s could not be parsed to JSON: %s", address.c_str(), e.what());
		return {};
	}

	std::shared_ptr<PlayerEndpoint> ret = std::make_shared<PlayerEndpoint>();
	ret->Address = address;
	try
	{
		ret->Created = endpointJson["created"].get<uint64_t>();

		ret->Key.Public = endpointJson["created-by"].get<std::string>();
		{
			SharedScopedLock lock(mHTTPAuthKeys);
			auto find = ret->Key.Public.empty() ? mHTTPAuthKeys->end() : mHTTPAuthKeys->find(ret->Key.Public);
			if (find != mHTTPAuthKeys->end())
				ret->Key = find->second;
		}

		ret->ProviderUID = endpointJson["provider"].get<std::string>();
		ret->HostUID = endpointJson["host"].get<std::string>();
		ret->Session = endpointJson["session"].get<std::string>();
		const json* isDemo = FindMember(endpointJson, "demo");
		ret->bDemo = isDemo && isDemo->get<bool>();
		const json& params = endpointJson["params"];
		for (auto param = params.begin(); param != params.end(); param++) ret->ExtraParams[param.key()] = param->get<std::string>();
		if (const json* playerParams = FindMember(endpointJson, "player-params"))
		{
			for (auto param = playerParams->begin(); param != playerParams->end(); param++) ret->Params[param.key()] = param->get<std::string>();
		}

		auto opt = gamehost::HostType::_from_string_nothrow(endpointJson["game"]["type"].get<std::string>().c_str());
		if (!opt)
		{
			Log(Warning, "Player endpoint has an invalid game type '%s'", endpointJson["game"]["type"].get<std::string>().c_str());
		}
		else
		{
			auto templateHost = mGameHosts->LoadSingle(*opt, {});
			if (!templateHost)
			{
				Log(Warning, "Player endpoint has an invalid game configuration of type '%s'", opt->_to_string());
			}
			else
			{
				ret->GameInfo.Schema() = templateHost->GetGameSchema();
			}

			try
			{
				ret->GameInfo.FromJSON(endpointJson["game"]);
			}
			catch (const SchemaError& err)
			{
				Log(Warning, "Player endpoint %s has a bad game configuration for a game of type '%s': %s", address.c_str(), opt->_to_string(), err.what());
			}
		}
		return ret;
	}
	catch (...)
	{
	}

	return {};
}

void YServer::AddPlayerEndpoint(std::shared_ptr<PlayerEndpoint> endpoint) const
{
	ScopedLock lock(PlayerAdminAPI->Endpoints);
	PlayerAdminAPI->Endpoints->emplace(endpoint->Address, endpoint);
}

void YServer::OnModuleRegistration(std::shared_ptr<YModule> module, EModuleRegistrationEvent event, bool bIsPartOfReload)
{
	json moduleLoadCtx(json::value_t::object);
	moduleLoadCtx["type"] = module->Container()->ModuleLogType;
	moduleLoadCtx["id"] = module->ID();
	moduleLoadCtx["uid"] = module->UniqueIdentifier();
	moduleLoadCtx["causedByReload"] = bIsPartOfReload;

	if (event == EModuleRegistrationEvent::ModuleAdded)
	{
		moduleLoadCtx["info"] = module->GetDescriptor({ .bAdmin = true });
		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(ViewAdminAPI->RequestHandler)->LoadedModuleEvent, moduleLoadCtx);
	}
	else
	{
		SpectatorEvent(*std::dynamic_pointer_cast<YAdminRequestHandler>(ViewAdminAPI->RequestHandler)->UnloadedModuleEvent, moduleLoadCtx);
	}

	ScopedLock lock(mHTTPAuthKeys);
	switch (event)
	{
		case EModuleRegistrationEvent::ModuleAdded: (&mHTTPAuthKeys)[module->Authorization().Public] = module->Authorization(); break;
		case EModuleRegistrationEvent::ModuleRemoved: mHTTPAuthKeys->erase(module->Authorization().Public); break;
	}
}

std::string YServer::GetClientLaunchURL(EClientType type, const GameEndpoint& gameEndpoint, const std::optional<std::string>& signature) const
{
	const auto& game = gameEndpoint.GameInfo;
	const std::string launchUrl =
	  game.GetLaunchURL(gameEndpoint.Params.Get(QUERY_PARAM_GAME_LAUNCH_OPTION_KEY), gameEndpoint.Params.Get(QUERY_PARAM_DEV_LAUNCH) == "true");
	if (launchUrl.empty())
		return {};

	const std::string endpointSignature = signature ? *signature : gameEndpoint.Signature(EHashAlgorithm::SHA1);

	web::QueryString gameQuery(gameEndpoint.ExtraParams);

	// add secure flag if not present
	if (!gameQuery.contains("secure") && !IsSecure())
		gameQuery["secure"] = "false";

	// add server flag if not present
	if (!gameQuery.contains("server"))
	{
		gameQuery["server"] =
		  yutils::Join<std::vector<std::string>>({ HostAddress().c_str(), type._to_string(), gameEndpoint.Address.c_str(), endpointSignature.c_str() }, "/");
	}

	// add launch params from the host
	if (const auto host = gameEndpoint.Host.lock())
		gameQuery = gameQuery.Merge(host->GetLaunchParams(game));

	// add launch params from the provider
	if (const auto provider = gameEndpoint.Provider.lock())
		gameQuery = gameQuery.Merge(provider->GetLaunchParams(game));

	const std::string entireLaunchUrl = std::format("{}?{}", launchUrl, gameQuery.ToString());
	Log(Info, "Game launch URL generated: %s", entireLaunchUrl.c_str());

	return entireLaunchUrl;
}

ECurrency YServer::PlayForFunCurrency() const
{
	return mPlayForFunCurrency;
}

double YServer::DefaultDenomination() const
{
	return 0.01;
}

PlayerAccountPtr YServer::CreateDemoAccount(const GameClientCreationContext& ctx, const web::QueryString& str) const
{
	PlayerAccountPtr account = std::make_shared<YPlayerAccount>(ctx, crypto::GenRandID(16), AccountID(std::string(), str.Get("user", "DEMO_PLAYER")), "Demo Player",
	                                                            str.Get("locale", "en_US"), str.Get("currency", PlayForFunCurrency()._to_string()),
	                                                            std::clamp((int)yutils::strToInt(str.Get("denom", "1"), 1), 1, *********) * DefaultDenomination(), true);
	account->SetRawBalance(CreditArray(CREDIT_CASHABLE, mPlayForFunCredits));

	auto it = str.find("decimalChar");
	if (it != str.end())
		account->SetDecimalChar(str.Get("decimalChar", std::string()));
	it = str.find("thousandChar");
	if (it != str.end())
		account->SetThousandChar(str.Get("thousandChar", std::string()));
	it = str.find("decimalPlaces");
	if (it != str.end())
		account->SetDecimalPlaces(yutils::strToInt(str.Get("decimalPlaces", "2"), 2));
	it = str.find("alignLeft");
	if (it != str.end())
		account->SetAlignLeft(yutils::strToInt(str.Get("alignLeft", "false"), 0));
	it = str.find("currencySymbol");
	if (it != str.end())
		account->SetCurrencySymbol(str.Get("currencySymbol", std::string()));

	return account;
}

uint64_t YServer::GetPlayerMaxBetCredits(const std::shared_ptr<YEndpoint>& endpoint, const std::shared_ptr<provider::TCasinoProvider>& provider,
                                         const YPlayerAccount& account, bool bInVirtualCredits)
{
	double MaxBet = yutils::strToDouble(endpoint->Params.Get("max-bet"), 0.0);
	if (provider && provider->GetMaxBet() > 0.0)
	{
		const double MaxBetOnProviderInPlayerCurrency = provider->GetMaxBet() * CurrencyAPI->GetExchangeRate2(provider->Currency(), account.Currency(), 1.0);

		if (MaxBet && MaxBetOnProviderInPlayerCurrency)
			MaxBet = std::min(MaxBet, MaxBetOnProviderInPlayerCurrency);
		else if (MaxBetOnProviderInPlayerCurrency)
			MaxBet = MaxBetOnProviderInPlayerCurrency;
	}


	return std::floor(MaxBet / account.CreditToMoney(1, bInVirtualCredits));
}

uint64_t YServer::GetPlayerMaxWinCredits(const std::shared_ptr<YEndpoint>& endpoint, const std::shared_ptr<provider::TCasinoProvider>& provider,
                                         const YPlayerAccount& account, bool bInVirtualCredits)
{
	double MaxWin = yutils::strToDouble(endpoint->Params.Get("max-win"), 0.0);
	if (provider && MaxWin > 0.0)
		MaxWin *= CurrencyAPI->GetExchangeRate2(provider->Currency(), account.Currency(), 1.0);

	return std::floor(MaxWin / account.CreditToMoney(1, bInVirtualCredits));
}

std::string YServer::CreateLaunchURL(const YAuthKey& key, const web::QueryString& query)
{
	const auto Endpoint = std::dynamic_pointer_cast<PlayerEndpoint>(PlayerAdminAPI->CreateEndpoint(key, query));
	return GetClientLaunchURL(EClientType::Player, *Endpoint);
}

const std::string& YServer::Name() const
{
	return mName;
}

const std::string& YServer::ServerID() const
{
	return mServerID;
}

std::list<std::shared_ptr<provider::TCasinoProvider>> YServer::find_providers(const ModuleListOptions& options) const
{
	std::shared_ptr<gamehost::TGameHost> hostToFilter =
	  options.hostID ? find_game_host(options.hostID) : (options.hostDomain.empty() ? NULL : mGameHosts->FindByDomain(options.hostType, options.hostDomain));

	std::list<std::shared_ptr<provider::TCasinoProvider>> outProviders;
	mCasinoProviders->ForEach([&outProviders, &options, hostToFilter](const std::shared_ptr<provider::TCasinoProvider>& provider) {
		if ((options.providerType == provider::ProviderType::Null || provider->Type() == options.providerType) &&
		    (!options.providerID || options.providerID == provider->ID()) && (options.providerDomain.empty() || options.providerDomain == provider->Domain()) &&
		    (!hostToFilter || hostToFilter->CanAccessFrom(provider)) && (!options.bRequireReady || provider->Status().Status == EModuleStatus::Ready))
		{
			outProviders.push_back(provider);
		}
	});

	return outProviders;
}

std::shared_ptr<provider::TCasinoProvider> YServer::find_provider(uint32_t ID) const
{
	return std::static_pointer_cast<provider::TCasinoProvider>(mCasinoProviders->Find(ID));
}

std::shared_ptr<provider::TCasinoProvider> YServer::find_provider(const std::string& name) const
{
	auto provider = std::static_pointer_cast<provider::TCasinoProvider>(mCasinoProviders->FindByName(name));
	if (provider && provider->IsInitialized() && provider->Status().Status < EModuleStatus::Reloading)
		return provider;

	return {};
}

std::shared_ptr<provider::TCasinoProvider> YServer::get_provider(const std::string& uid) const
{
	return std::static_pointer_cast<provider::TCasinoProvider>(mCasinoProviders->FindByUID(uid));
}

std::shared_ptr<provider::TCasinoProvider> YServer::try_get_provider(const json& id_or_uid) const
{
	if (id_or_uid.is_number_unsigned())
		return find_provider(id_or_uid.get<uint32_t>());
	else if (id_or_uid.is_string())
		return get_provider(id_or_uid.get<std::string>());

	return {};
}

std::list<std::shared_ptr<gamehost::TGameHost>> YServer::find_game_hosts(const ModuleListOptions& options) const
{
	std::list<std::shared_ptr<gamehost::TGameHost>> outHosts;

	std::shared_ptr<provider::TCasinoProvider> providerToFilter =
	  options.providerID ? find_provider(options.providerID) :
	                       (options.providerDomain.empty() ? NULL : mCasinoProviders->FindByDomain(options.providerType, options.providerDomain));

	mGameHosts->ForEach([&outHosts, &options, providerToFilter](const std::shared_ptr<gamehost::TGameHost>& host) {
		if ((options.hostType == gamehost::HostType::Null || host->Type() == options.hostType) && (!options.hostID || options.hostID == host->ID()) &&
		    (options.hostDomain.empty() || options.hostDomain == host->Domain()) && (!providerToFilter || host->CanAccessFrom(providerToFilter)) &&
		    (!options.bRequireReady || (host->IsOnline() && (!providerToFilter || providerToFilter->Status().Status == EModuleStatus::Ready))))
		{
			outHosts.push_back(host);
		}
	});
	return outHosts;
}

std::shared_ptr<gamehost::TGameHost> YServer::find_game_host(uint32_t ID) const
{
	return std::static_pointer_cast<gamehost::TGameHost>(mGameHosts->Find(ID));
}

std::shared_ptr<gamehost::TGameHost> YServer::find_game_host(const std::string& name) const
{
	auto host = std::static_pointer_cast<gamehost::TGameHost>(mGameHosts->FindByName(name));
	if (host && host->IsInitialized() && host->Status().Status < EModuleStatus::Reloading)
		return host;

	return {};
}

std::shared_ptr<gamehost::TGameHost> YServer::get_game_host(const std::string& uid) const
{
	return std::static_pointer_cast<gamehost::TGameHost>(mGameHosts->FindByUID(uid));
}

std::shared_ptr<gamehost::TGameHost> YServer::try_get_game_host(const json& id_or_uid) const
{
	if (id_or_uid.is_number_unsigned())
		return find_game_host(id_or_uid.get<uint32_t>());
	else if (id_or_uid.is_string())
		return get_game_host(id_or_uid.get<std::string>());

	return {};
}

bool YServer::ResolveResource(const YResourcePath& resource, std::vector<unsigned char>& outData) const
{
	return ResolveResourceMultiOption({ resource }, outData) == 0;
}

void YServer::OnPingTimer()
{
	PingClients();
}

std::shared_ptr<yserver::jackpot::TJackpotModule> YServer::GetJackpotFor(const std::shared_ptr<provider::TCasinoProvider>& provider, const gamehost::TGameHost& host,
                                                                         const StaticGameInformation& game, bool bDemo)
{
	std::shared_ptr<yserver::jackpot::TJackpotModule> jpModule = host.Jackpot(provider, game, bDemo);
	if (!jpModule && host.AllowJackpot() && provider)
		jpModule = provider->Jackpot(bDemo);
	return jpModule;
}
