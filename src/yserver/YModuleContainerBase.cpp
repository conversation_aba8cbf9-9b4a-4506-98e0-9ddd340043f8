#include "YModuleContainerBase.h"

#include "YServer.h"

using namespace yserver;

YModuleContainerBase::YModuleContainerBase(YServer* server, const std::string& moduleType) : Server(server), ModuleLogType(moduleType) {}

void YModuleContainerBase::SaveConfig(const YModule& module)
{
	if (module.SettingsPath().empty())
		return;

	Server->DoAsyncTask(
	  [this, newConf = module.RootConfigNode(), filepath = module.SettingsPath(), moduleName = module.Name(), moduleType = std::string(module.TypeStr())]() {
		  if (bEnableArchiving && std::filesystem::exists(filepath))    // if old config exists, we move it to archive
		  {
			  std::error_code ec;
			  std::filesystem::rename(filepath,
			                          ModuleConfigDir / ARCHIVE_FOLDER /
			                            ("backup_" + yutils::FormatTime("%d-%m-%Y %H-%M-%S", std::chrono::system_clock::now()) + "_" + filepath.filename().string()),
			                          ec);
			  if (ec)
				  Server->Log(Warning, "Could not archive settings of %s %s '%s' to %s: %s", moduleType.c_str(), ModuleLogType.c_str(), moduleName.c_str(),
				              filepath.c_str(), ec.message().c_str());
		  }

		  std::ofstream outFile(filepath, std::ios_base::trunc | std::ios_base::out);
		  if (outFile.is_open())
		  {
			  outFile << newConf.dump(1, '\t');
			  outFile.close();
			  Server->Log(Important, "Configuration of %s %s '%s' saved to %s", moduleType.c_str(), ModuleLogType.c_str(), moduleName.c_str(), filepath.c_str());
		  }
		  else
		  {
			  Server->Log(Warning, "Configuration of %s %s '%s' could not be saved to %s", moduleType.c_str(), ModuleLogType.c_str(), moduleName.c_str(),
			              filepath.c_str());
		  }
	  },
	  module.SettingsPath());
}

void YModuleContainerBase::Shutdown(const std::string& msg) const
{
	ScopedLock lock(ModuleList);
	for (const auto& module : &ModuleList) module.second.Module->Shutdown(msg);
}

void YModuleContainerBase::Dispose()
{
	YModuleList switcharoo;
	{
		ScopedLock lock(ModuleList);
		ModuleList->swap(switcharoo);
	}
}

std::shared_ptr<YModule> YModuleContainerBase::FindByUID(const std::string& uid) const
{
	ScopedLock lock(ModuleList);
	for (const auto& module : &ModuleList)
	{
		if (module.second.Module->UniqueIdentifier() == uid)
			return module.second.Module;
	}

	return {};
}

std::shared_ptr<YModule> YModuleContainerBase::FindByName(const std::string& name) const
{
	ScopedLock lock(ModuleList);
	for (const auto& module : &ModuleList)
	{
		if (module.second.Module->Name() == name)
			return module.second.Module;
	}

	return {};
}

std::shared_ptr<YModule> YModuleContainerBase::Find(uint32_t id) const
{
	if (id)
	{
		ScopedLock lock(ModuleList);
		auto found = ModuleList->find(id);
		if (found != ModuleList->end())
			return found->second.Module;
	}

	return {};
}

json YModuleContainerBase::ModuleLoadResult::ToJSON(const std::optional<ELanguage>& lang) const
{
	json jsonResult(json::value_t::object);
	jsonResult["id"] = ID;
	jsonResult["module"] = Result.Module ? Result.Module->GetDescriptor({ .bAdmin = true, .bIncludeRawConfig = false, .Language = lang }) : json();
	if (Result.Error)
		jsonResult["error"] = Result.Error->what();

	return jsonResult;
}

YModuleContainerBase::ModuleLoadResult YModuleContainerBase::Reload(uint32_t ID, uint64_t timeout, const json& newConf) noexcept
{
	if (!timeout)
		timeout = DefaultReloadTimeout;

	const std::string CapitalizedModuleType = yutils::CapitalizeFirst(ModuleLogType);
	ModuleLoadResult res;
	try
	{
		ScopedLock lock(ModuleList);
		auto find = ModuleList->find(ID);
		if (find == ModuleList->end())
			throw yprotocol::InternalError(CapitalizedModuleType + " not found");

		res.ID = ID;

		std::shared_ptr<YModule> reloadTarget = find->second.Module;
		lock.unlock();

		if (reloadTarget->Status().Status == EModuleStatus::Reloading)
			throw yprotocol::InternalError("This " + ModuleLogType + " is already being reloaded!");

		json moduleConf;
		if (newConf.is_null())    // if not edited
		{
			moduleConf = reloadTarget->RootConfigNode();
			if (!reloadTarget->SettingsPath().empty())
			{
				std::ifstream file(reloadTarget->SettingsPath());
				if (file.is_open())
				{
					try
					{
						moduleConf = json::parse(file, nullptr, true, true);
					}
					catch (const std::exception& e)
					{
						Log(Warning, "Can't read config file for %s %s at %s (%s), will reload the module with the same config!", ModuleLogType.c_str(),
						    reloadTarget->Name().c_str(), reloadTarget->SettingsPath().c_str(), e.what());
						moduleConf = reloadTarget->RootConfigNode();
					}
				}
				else
				{
					Log(Warning, "Can't open config file for %s %s at %s, will reload the module with the same config!", ModuleLogType.c_str(),
					    reloadTarget->Name().c_str(), reloadTarget->SettingsPath().c_str());
				}
			}
		}
		else    // try to load settings directly without requiring a module reload
		{
			moduleConf = newConf;
			json moduleConfAsSchema;
			try
			{
				moduleConfAsSchema = reloadTarget->GetSchema().GenerateConfig(newConf);
			}
			catch (const SchemaError& e)
			{
				res.Result.Error = ConfigError(e.what());
				return res;
			}

			if (reloadTarget->RootConfigNode() == moduleConfAsSchema)
			{
				res.Result.Error = ConfigError("The new configuration is the same as the current one.");
				return res;
			}

			if (reloadTarget->GetSchema().GenerateConfig(reloadTarget->RootConfigNode(),
			                                             { .bSkipUnknownMembers = true, .RequiredFlags = { yserver::ModuleRestartRequiredSettingFlag } }) !=
			    reloadTarget->GetSchema().GenerateConfig(newConf, { .bSkipUnknownMembers = true, .RequiredFlags = { yserver::ModuleRestartRequiredSettingFlag } }))
			{
				// restart is not required because the configurations that would require a restart have not changed -> just update the module settings in place!
				Log(Info, "The new configuration for %s %s '%s' does not require a restart, will update the module settings in place!", reloadTarget->TypeStr(),
				    ModuleLogType.c_str(), reloadTarget->Name().c_str());

				try
				{
					reloadTarget->LoadSubconfiguration(newConf, reloadTarget->SettingsPath());
				}
				catch (const ConfigError& err)
				{
					res.Result.Error = err;
					return res;
				}
				catch (const std::exception& err)
				{
					res.Result.Error = ConfigError(err.what());
					return res;
				}

				if (!PostLoad(reloadTarget))
				{
					res.Result.Error = ConfigError("Failed post load check!");
					return res;
				}

				SaveConfig(*reloadTarget);

				res.Result.Module = reloadTarget;
				return res;
			}
		}

		Server->Log(Important, "Creating a new instance of %s %s(%u)...", ModuleLogType.c_str(), reloadTarget->Name().c_str(), ID);
		ModuleLoadResult loaded = Load(moduleConf, reloadTarget->SettingsPath(), reloadTarget->Owner(), false);

		if (!loaded.Result.Module)
			throw yprotocol::InternalError(loaded.Result.Error->what());

		if (strcmp(loaded.Result.Module->TypeStr(), reloadTarget->TypeStr()) != 0)
			throw yprotocol::InternalError(yutils::Format("Can't edit the configuration of a %s %s by replacing it with a different type(%s)!", reloadTarget->TypeStr(),
			                                              ModuleLogType.c_str(), loaded.Result.Module->TypeStr()));

		if (!newConf.is_null())    // if edited and the settings path is non-empty => save the config file
			SaveConfig(*loaded.Result.Module);

		res.Result.Error = loaded.Result.Error;
		if (loaded.Result.Error)
		{
			if (!reloadTarget->GetConfigError())    // if the previous configuration didn't have a config error, then don't allow this change which is also an error
				throw yprotocol::InternalError(loaded.Result.Error->what());
			loaded.Result.Module->SetError(loaded.Result.Error);
		}

		{
			ScopedLock lock2(ModuleList);
			if (loaded.ID)
			{
				if (mReservedReloadIDs.contains(loaded.ID))
					throw yprotocol::InternalError("There is already a " + ModuleLogType + " being reloaded with ID " + std::to_string(loaded.ID));

				if (loaded.ID != reloadTarget->ID())
				{
					auto findOther = ModuleList->find(loaded.ID);
					if (findOther != ModuleList->end())
						throw yprotocol::InternalError(yutils::Format("Cannot load %s with forced ID %u, because that is taken by '%s'", ModuleLogType.c_str(), loaded.ID,
						                                              findOther->second.Module->Name().c_str()));
				}
			}
			else if (reloadTarget->ID())
			{
				loaded.ID = reloadTarget->ID();
			}
			else
			{
				uint32_t FreeID = 1;
				for (const auto& module : &ModuleList)
				{
					while (mReservedReloadIDs.contains(FreeID)) FreeID++;
					if (FreeID < module.first)
						break;
					else if (FreeID == module.first)
						FreeID++;
				}
				loaded.ID = FreeID;
			}
			mReservedReloadIDs.insert(loaded.ID);
		}

		res.Result.Module = loaded.Result.Module;
		loaded.Result.Module->SetID(loaded.ID);

		Server->Log(Important, "%s %s %s created and assigned ID %u.", loaded.Result.Module->TypeStr(), CapitalizedModuleType.c_str(),
		            loaded.Result.Module->Name().c_str(), loaded.ID);

		Server->Log(Important, "Waiting for old %s [%u] to shut down...", ModuleLogType.c_str(), reloadTarget->ID());

		reloadTarget->Shutdown("Reloading", true);
		reloadTarget->OnReloaded(loaded.Result.Module);

		Server->Log(Important, "New %s %s '%s' loaded on ID %d.", loaded.Result.Module->TypeStr(), ModuleLogType.c_str(), loaded.Result.Module->Name().c_str(),
		            loaded.ID);
		{
			ScopedLock lock2(ModuleList);
			{
				ModuleList->erase(reloadTarget->ID());
				ModuleList->emplace(loaded.ID, loaded.Result);
				mReservedReloadIDs.erase(loaded.ID);
			}
		}
		OnModuleRegistration(reloadTarget, EModuleRegistrationEvent::ModuleRemoved, true);
		OnModuleRegistration(loaded.Result.Module, EModuleRegistrationEvent::ModuleAdded, true);

		reloadTarget->Destroy();

		loaded.Result.Module->StartService();
	}
	catch (const std::exception& err)
	{
		res.Result.Error = ConfigError(err.what());
	}

	return res;
}

YModuleContainerBase::ModuleLoadResult YModuleContainerBase::Load(const json& module, const std::filesystem::path& filename, const std::string& owner,
                                                                  bool bDoNameCheck) noexcept
{
	ModuleLoadResult ret;
	try
	{
		const std::string type = PreLoad(module, bDoNameCheck);

		const std::string name = module["name"].get<std::string>();

		Log(Important, "Loading %s '%s' (%s)...", ModuleLogType.c_str(), name.c_str(), type.c_str());
		ret.Result.Module = LoadSingleStr(type, name);
		if (!ret.Result.Module)
			throw ConfigError("Failed to create a new instance of " + ModuleLogType + " type '" + type + "'");

		ret.Result.Module->SetStorageDir(ModuleStorageDir / ret.Result.Module->TypeStr() / name);
		std::filesystem::create_directories(ret.Result.Module->StorageDir());

		const json* forceId = FindMember(module, "force-id");
		if (forceId && !forceId->is_null())
		{
			if (!forceId->is_number_unsigned())
				throw ConfigError("force-id should be a positive integer!", module);
			ret.ID = forceId->get<uint32_t>();
			if (!ret.ID)
				throw ConfigError("0 is not a valid forced ID!", module);
		}

		ret.Result.Module->LoadSubconfiguration(module, filename);

		if (!PostLoad(ret.Result.Module))
			throw ConfigError("Failed post load check!");

		ret.Result.Module->SetOwner(owner);
		ret.Result.Module->PostLoad();
	}
	catch (const ConfigError& err)
	{
		ret.Result.Error = err;
	}
	catch (const SchemaError& e)
	{
		ret.Result.Error = ConfigError("Schema error: " + std::string(e.what()), module);
	}
	catch (const json::exception& e)
	{
		ret.Result.Error = ConfigError("Json error: " + std::string(e.what()), module);
	}
	catch (const std::exception& e)
	{
		ret.Result.Error = ConfigError("General error: " + std::string(e.what()), module);
	}
	return ret;
}

std::filesystem::path YModuleContainerBase::GetRoundStorageDir() const
{
	return ModuleStorageDir / "rounds";
}

size_t YModuleContainerBase::LoadAll() noexcept
{
	ScopedLock lock(ModuleList);
	std::set<std::string> Names;
	std::list<YModuleInfo> UnassignedModules;
	size_t numToInitialize = 0, numErrored = 0;
	for (const auto& entry : std::filesystem::directory_iterator(ModuleConfigDir))
	{
		if (entry.is_regular_file() && entry.path().has_extension() && entry.path().extension() == ".conf")
		{
			if (entry.path().filename().string().starts_with('_'))
			{
				Log(Normal, "Skipping %s config %s because it starts with '_' (yserver understands this means the %s is disabled)", ModuleLogType.c_str(),
				    entry.path().filename().c_str(), ModuleLogType.c_str());
				continue;
			}
			else if (ModuleList->end() != std::find_if(ModuleList->begin(), ModuleList->end(), [file = entry.path()](typename YModuleList::const_reference v) {
				         return v.second.Module->SettingsPath() == file;
			         }))
			{
				continue;    // skipping already loaded modules
			}
			Log(Info, "Loading %s with config %s...", ModuleLogType.c_str(), entry.path().filename().c_str());

			ModuleLoadResult loaded;
			try
			{
				std::ifstream infile(entry.path());

				if (!infile.is_open())
					throw ConfigError("Can't open file \"" + entry.path().string() + "\", but it does exist (permissions maybe?)");

				json ModuleConfig;
				try
				{
					ModuleConfig = json::parse(infile, nullptr, true, true);
				}
				catch (const std::exception& e)
				{
					throw ConfigError(e.what());
				}

				if (!ModuleUserConfigDir.empty() && std::filesystem::exists(ModuleUserConfigDir / entry.path().filename()))
				{
					std::ifstream userConfigFile(ModuleUserConfigDir / entry.path().filename());

					if (userConfigFile.is_open())
					{
						try
						{
							json ModuleUserConfig;
							userConfigFile >> ModuleUserConfig;
							if (!ModuleUserConfig.is_null())
							{
								try
								{
									ModuleConfig.patch_inplace(ModuleUserConfig);
								}
								catch (const std::exception& e)
								{
									Log(Warning, "Could not apply patch %s to config file: %s", entry.path().filename().c_str(), e.what());
								}
							}
						}
						catch (const std::exception& e)
						{
							Log(Warning, "Could not read patch config file %s: %s", entry.path().filename().c_str(), e.what());
						}
					}
				}

				loaded = Load(ModuleConfig, entry.path());

				if (!loaded.Result.Error)
				{
					if (Names.contains(loaded.Result.Module->Name()) ||
					    std::find_if(ModuleList->begin(), ModuleList->end(), [name = loaded.Result.Module->Name()](const typename YModuleList::value_type& pair) -> bool {
						    return pair.second.Module->Name() == name;
					    }) != ModuleList->end())
						throw ConfigError(ModuleLogType + " name '" + loaded.Result.Module->Name() + "' already exists - duplicate names are not allowed!", ModuleConfig);
					Names.insert(loaded.Result.Module->Name());

					if (loaded.ID)
					{
						if (ModuleList->contains(loaded.ID))
							throw ConfigError("Forced ID " + std::to_string(loaded.ID) + " is already taken!");

						ModuleList->emplace(loaded.ID, loaded.Result);
						numToInitialize++;
						continue;
					}
				}
			}
			catch (const ConfigError& err)
			{
				loaded.Result.Error = err;
			}

			if (loaded.Result.Error)
			{
				if (loaded.Result.Module)
				{
					loaded.Result.Module->SetError(loaded.Result.Error);
					Log(Error, "Error loading %s %s '%s': %s", loaded.Result.Module->TypeStr(), ModuleLogType.c_str(), loaded.Result.Module->Name().c_str(),
					    loaded.Result.Error->what());
					numErrored++;
				}
				else
				{
					Log(Critical, "Encountered fatal error loading %s config '%s' (this module will not be loaded): %s", ModuleLogType.c_str(),
					    entry.path().filename().c_str(), loaded.Result.Error->what());
					continue;
				}
			}
			else
			{
				numToInitialize++;
			}

			UnassignedModules.push_back(loaded.Result);
		}
	}

	if (numErrored + numToInitialize == 0)
		return 0;

	Log(Info, "Configuration complete, initializing %lu/%lu %ss...", numToInitialize, numErrored + numToInitialize, ModuleLogType.c_str());

	uint32_t lowestFreeID = 1;
	auto nextUnassigned = UnassignedModules.begin();
	for (auto it = ModuleList->begin(); it != ModuleList->end() || nextUnassigned != UnassignedModules.end();)
	{
		const bool bHasAnyMoreFixedIDs = it != ModuleList->end();
		const uint32_t nextFixedID = bHasAnyMoreFixedIDs ? it->first : (std::distance(nextUnassigned, UnassignedModules.end()) + lowestFreeID);

		while (nextFixedID > lowestFreeID || (nextFixedID == lowestFreeID && bHasAnyMoreFixedIDs))
		{
			if (nextFixedID > lowestFreeID)
			{
				if (nextUnassigned != UnassignedModules.end())
				{
					it = ModuleList->emplace_hint(it, lowestFreeID, *nextUnassigned);
					nextUnassigned++;
				}
				else if (!bHasAnyMoreFixedIDs)
				{
					break;
				}
				else
				{
					lowestFreeID = nextFixedID;
				}
			}
			lowestFreeID++;

			auto modulePtr = it->second.Module;
			modulePtr->SetID(it->first);
			OnModuleRegistration(modulePtr, EModuleRegistrationEvent::ModuleAdded, false);
			auto added = *it;
			it++;

			Log(Important, "Added %s '%s' (%s) with %s ID %d", ModuleLogType.c_str(), modulePtr->Name().c_str(), modulePtr->TypeStr(),
			    (bHasAnyMoreFixedIDs && (nextFixedID + 1 == lowestFreeID)) ? "forced" : "dynamic", added.first);
			if (added.second.Error)
			{
				Log(Warning, "Will not connect service of %s %s '%s' (%s) because it encountered a configuration error (%s)", modulePtr->TypeStr(), ModuleLogType.c_str(),
				    modulePtr->Name().c_str(), modulePtr->TypeStr(), modulePtr->GetConfigError()->what());
				continue;
			}
			else if (!modulePtr->Enabled())
			{
				Log(Warning, "Will not connect service of %s %s '%s' (%s) because it is disabled", modulePtr->TypeStr(), ModuleLogType.c_str(), modulePtr->Name().c_str(),
				    modulePtr->TypeStr());
				continue;
			}

			modulePtr->StartService();
		}
	}

	if (numToInitialize)
		Log(Info, "%lu %s%s loaded!", numToInitialize, ModuleLogType.c_str(), numToInitialize == 1 ? "" : "s");
	if (numErrored)
		Log(Info, "%lu %s%s game hosts encountered errors and are kept back (service not started)!", numErrored, ModuleLogType.c_str(), numErrored == 1 ? "" : "s");

	return numErrored + numToInitialize;
}

void YModuleContainerBase::RemoveOwnedModules(const std::string& uid)
{
	std::set<std::shared_ptr<YModule>> Children;
	{
		ScopedLock lock(ModuleList);
		for (const auto& module : &ModuleList)
			if (module.second.Module->Owner() == uid)
				Children.insert(module.second.Module);
	}

	for (const std::shared_ptr<YModule>& module : Children) UnloadModule(module, "owner left", true);
}

std::string YModuleContainerBase::PreLoad(const json& config, bool bDoNameCheck) const
{
	if (!config.is_object())
		throw ConfigError(ModuleLogType + " configuration should be an object!");

	const json* nameMember = FindMember(config, "name");
	if (!nameMember)
		throw ConfigError(ModuleLogType + " config missing 'name'!", config);

	if (!config.contains("type"))
		throw ConfigError(ModuleLogType + " config missing 'type'!", config);

	if (!config.contains("enabled"))
		throw ConfigError(ModuleLogType + " config missing 'enabled'!", config);

	if (!nameMember->is_string() || nameMember->get<std::string>().empty())
		throw ConfigError(ModuleLogType + " 'name' should be a non-empty string!", config);

	const std::string name = nameMember->get<std::string>();
	if (name.find('|') < name.length())
		throw ConfigError(ModuleLogType + " 'name' cannot contain pipes ( | )", config);

	if (!config["type"].is_string())
		throw ConfigError(ModuleLogType + " 'type 'should be a string!", config);

	if (!config["enabled"].is_boolean())
		throw ConfigError(ModuleLogType + " 'enabled' should be a boolean!", config);

	if (bDoNameCheck)
	{
		if (auto module = FindByName(name))
		{
			if (module->Status().Status != EModuleStatus::Reloading)
				throw ConfigError(ModuleLogType + " with name '" + name + "' already exists - duplicate names are not allowed!");
		}
	}

	return config["type"].get<std::string>();
}

void YModuleContainerBase::Unload(uint32_t ID, uint64_t timeout, const std::string& msg, bool bDelete)
{
	if (!timeout)
		timeout = DefaultReloadTimeout;

	if (!ID)
		throw yprotocol::InternalError("0 is an invalid ID");

	const std::string CapitalizedModuleType = yutils::CapitalizeFirst(ModuleLogType);

	ScopedLock lock(ModuleList);
	auto find = ModuleList->find(ID);
	if (find == ModuleList->end())
		throw yprotocol::InternalError(CapitalizedModuleType + " not found");

	std::shared_ptr<YModule> shutdownTarget = find->second.Module;
	lock.unlock();

	UnloadModule(shutdownTarget, msg, bDelete);
}

void YModuleContainerBase::UnloadModule(const std::shared_ptr<YModule>& module, const std::string& msg, bool bDeleteConfig)
{
	Server->Log(Normal, "Unloading %s %s '%s' (%s)...", module->TypeStr(), ModuleLogType.c_str(), module->Name().c_str(), msg.c_str());

	const std::string shutdownMsg = msg.empty() ? "admin requested shutdown" : msg;
	module->Shutdown(shutdownMsg);

	{
		ScopedLock lock(ModuleList);
		ModuleList->erase(module->ID());
	}
	OnModuleRegistration(module, EModuleRegistrationEvent::ModuleRemoved, false);

	Server->DoAsyncTask([srv = Server, module, bDeleteConfig, moduleType = ModuleLogType]() {
		module->Destroy();

		if (bDeleteConfig && !module->SettingsPath().empty())
		{
			std::error_code ec;
			std::filesystem::remove(module->SettingsPath(), ec);
			if (ec)
				srv->Log(Warning, "Error deleting %s %s '%s' config file at %s: %s", module->TypeStr(), moduleType.c_str(), module->Name().c_str(),
				         module->SettingsPath().c_str(), ec.message().c_str());
		}

		srv->Log(Normal, "Fully unloaded %s %s '%s'", module->TypeStr(), moduleType.c_str(), module->Name().c_str());
	});
}

YModuleContainerBase::ModuleLoadResult YModuleContainerBase::CreateNew(const json& module_conf, bool bPersistent, const std::string& owner)
{
	ModuleLoadResult loaded;
	const json* nameJson = FindMember(module_conf, "name");
	if (!nameJson || !nameJson->is_string())
	{
		loaded.Result.Error = ConfigError("Config missing 'name'");
		return loaded;
	}

	std::string name = nameJson->get<std::string>();
	if (name.empty())
	{
		loaded.Result.Error = ConfigError("'name' cannot be empty!");
		return loaded;
	}
	name = yutils::ReplaceAll(name, " ", "_");
	std::filesystem::path configLocation;
	if (bPersistent)
	{
		int i = 1;
		do {
			configLocation = ModuleConfigDir / (name + (i > 1 ? std::to_string(i) : std::string()) + ".conf");
			i++;
		} while (std::filesystem::exists(configLocation));
	}

	loaded = Load(module_conf, configLocation, owner);

	auto loadedModule = loaded.Result.Module;

	if (!loadedModule)
		return loaded;

	loadedModule->SetError(loaded.Result.Error);

	ScopedLock lock(ModuleList);
	if (loaded.ID)
	{
		auto findOther = ModuleList->find(loaded.ID);
		if (findOther != ModuleList->end())
		{
			loaded.Result.Module = NULL;
			loaded.Result.Error = ConfigError(yutils::Format("Cannot load %s with forced ID %u, because that is taken by '%s'", ModuleLogType.c_str(), loaded.ID,
			                                                 findOther->second.Module->Name().c_str()));
			return loaded;
		}
	}
	else
	{
		uint32_t FreeID = 1;
		for (const auto& module : &ModuleList)
		{
			if (FreeID < module.first)
				break;
			else if (FreeID == module.first)
				FreeID++;
		}
		loaded.ID = FreeID;
	}

	ModuleList->emplace(loaded.ID, loaded.Result);
	lock.unlock();

	loadedModule->SetID(loaded.ID);
	OnModuleRegistration(loaded.Result.Module, EModuleRegistrationEvent::ModuleAdded, false);
	Server->Log(Important, "%s '%s' created and assigned ID %u. Initializing...", yutils::CapitalizeFirst(ModuleLogType).c_str(), loadedModule->Name().c_str(),
	            loaded.ID);

	if (loaded.Result.Error)
	{
		Server->Log(Important, "New %s %s '%s' loaded on ID %u with errors: %s", loadedModule->TypeStr(), ModuleLogType.c_str(), loadedModule->Name().c_str(), loaded.ID,
		            loaded.Result.Error->what());
	}
	else if (loadedModule->Enabled())
	{
		Server->Log(Important, "New %s %s '%s' loaded on ID %u! Will now start polling and service connection.", loadedModule->TypeStr(), ModuleLogType.c_str(),
		            loadedModule->Name().c_str(), loaded.ID);

		loadedModule->StartService();
	}
	else
	{
		Server->Log(Important, "New %s %s '%s' loaded on ID %u! It is disabled so no service or polling will be started.", loadedModule->TypeStr(), ModuleLogType.c_str(),
		            loadedModule->Name().c_str(), loaded.ID);
	}

	if (bPersistent && !loaded.Result.Error)
	{
		std::ofstream outFile(configLocation, std::ios_base::trunc | std::ios_base::out);
		if (outFile.is_open())
		{
			outFile << loadedModule->RootConfigNode();
			outFile.close();
			Server->Log(Important, "%s '%s' configuration saved to %s", ModuleLogType.c_str(), loadedModule->Name().c_str(), configLocation.c_str());
		}
		else
		{
			Server->Log(Warning, "%s '%s' configuration failed to save to %s", ModuleLogType.c_str(), loadedModule->Name().c_str(), configLocation.c_str());
		}
	}

	return loaded;
}
