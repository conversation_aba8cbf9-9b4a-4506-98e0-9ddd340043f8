#include "TroniusJackpotModule.h"

#include "TCasinoProvider.h"
#include "YPlayer.h"
#include "jackpot/client/JackpotClient.h"

using namespace yserver::jackpot;

constexpr size_t MAX_HISTORY_SIZE = 35;

TroniusJackpotModuleBase::TroniusJackpotModuleBase(const std::string& jackpotAddress, bool bSecure, const JackpotClientConfig& config, bool bAutoConnect,
                                                   bool bAutoEnableLevels, const std::optional<AuthDto>& auth, const std::vector<std::filesystem::path>& certAuthorities)
{
	SetLogCategory(LogY);

	Client = std::make_unique<JackpotClient>(jackpotAddress, bSecure, config, auth, certAuthorities);
	Client->OnInitialized += [this, bAutoEnableLevels](const InitRespDto&) {
		if (bAutoEnableLevels)
		{
			Client->ListJackpotLevels(ListJackpotLevelsReqDto({}, {}, true))
			  .then(boost::launch::sync, [this](boost::future<JackpotClientResponse<ListJackpotLevelsRespDto>> fut) {
				  const JackpotClientResponse<ListJackpotLevelsRespDto> response = fut.get();
				  if (!response.IsOK())
				  {
					  Log(Warning, "Failed to list jackpot levels: %s", response.Error()->what());
					  return;
				  }

				  std::unordered_set<std::string> levelsToEnable;
				  for (const auto& levelInfo : response.Value()->Levels)
					  if (levelInfo.ClientStatus.value_or(EJackpotLevelStatus::Disabled) == EJackpotLevelStatus::Disabled)
						  levelsToEnable.insert(levelInfo.Level.Info.ID);

				  if (!levelsToEnable.empty())
					  Client->AddClientEnabledLevels(SetClientEnabledLevelsReqDto(levelsToEnable));
			  });
		}

		Client->ListJackpotHistory({ {}, true, MAX_HISTORY_SIZE, false, {} })
		  .then(boost::launch::sync, [this](boost::future<JackpotClientResponse<ListJackpotHistoryRespDto>> fut) {
			  const JackpotClientResponse<ListJackpotHistoryRespDto> response = fut.get();
			  if (!response.IsOK())
			  {
				  Log(Warning, "Failed to list jackpot win history: %s", response.Error()->what());
				  return;
			  }

			  ScopedLock lock(History);
			  History->clear();
			  for (const auto& levelInfo : response.Value()->Levels) History->emplace(levelInfo.ID, levelInfo.History);
		  });
	};

	if (bAutoConnect)
		Client->Connect();
}

TroniusJackpotModuleBase::~TroniusJackpotModuleBase() {}

std::string TroniusJackpotModuleBase::Currency() const
{
	return Client->GetClientConfig().Currency._to_string();
}

std::map<std::string, yserver::jackpot::YJackpotLevel> TroniusJackpotModuleBase::GetLevels() const
{
	auto levels = Client->GetLevels();
	std::map<std::string, yserver::jackpot::YJackpotLevel> ret;
	for (const auto& [levelId, level] : levels)
	{
		const JackpotValuesDto vals = level->Values;
		const auto levelInfo = level->Info;
		yserver::jackpot::YJackpotLevel ylevel;
		ylevel.bCanBeWon = level->bCanBeWon;
		ylevel.LevelID = levelId;
		ylevel.Pot = vals.GetDisplayedPot(levelInfo);
		switch (vals.Status)
		{
			case EJackpotLevelStatus::PendingWin:
			case EJackpotLevelStatus::Counting: ylevel.State = jackpot::EJackpotState::Counting; break;
			case EJackpotLevelStatus::Won: ylevel.State = jackpot::EJackpotState::Won; break;
			default: ylevel.State = jackpot::EJackpotState::Disabled; break;
		}
		ylevel.Info.MinBet = levelInfo.MinBet;
		ylevel.Info.MaxBet = levelInfo.MaxBet;
		ylevel.Info.MinWinnableBet = levelInfo.MinWinnableBet;
		ylevel.Info.MaxWinnableBet = levelInfo.MaxWinnableBet;
		ylevel.Info.MinPot = levelInfo.MinPot;
		ylevel.Info.MaxPot = levelInfo.MaxPot;
		ylevel.Info.Name = levelInfo.Name;
		ylevel.Info.Percentage = levelInfo.Increment;
		ylevel.Info.ExtraInformation = levelInfo.ToJSON();

		ret.emplace(levelId, std::move(ylevel));
	}


	SharedScopedLock lock2(History);
	for (const auto& [levelId, history] : &History)
	{
		auto level = ret.find(levelId);
		if (level == ret.end())
			continue;

		for (const auto& win : history)
		{
			jackpot::YJackpotHistoryEntry won;
			won.Pot = win.Pot;
			won.Timestamp = win.Timestamp;
			for (auto& player : win.WonBets) won.Winners.push_back({ player.ClientLocation, player.PlayerName });
			won.Description = "This was won as a result of a fight to the death between two players.";
			level->second.WinHistory.push_back(std::move(won));
		}
	}

	return ret;
}

std::map<std::string, JackpotWinInformation> TroniusJackpotModuleBase::ExtractWinsInGameRound(const yserver::YGameClient& player)
{
	ScopedLock lock(Winners);
	auto find = Winners->extract(player.UniqueIdentifier());
	if (!find.empty())
		return find.mapped();

	return {};
}

TroniusJackpotModule::TroniusJackpotModule(const std::string& jackpotAddress, bool bSecure, const JackpotClientConfig& config, const std::optional<AuthDto>& auth,
                                           const std::vector<std::filesystem::path>& certAuthorities) :
    TroniusJackpotModuleBase(jackpotAddress, bSecure, config, true, true, auth, certAuthorities)
{
}

boost::future<double> TroniusJackpotModule::CommitBetsToPot(YGameClient& player, double totalBetMoney)
{
	ClientBetsReqDto betReqs({ ClientBetDto(totalBetMoney, player.Account()->Nickname(), player.UniqueIdentifier(), player.Account()->ID().AsString(),
	                                        player.Account()->Session(), player.Game()->GameRound()) });
	return Client->Bet(betReqs).then(boost::launch::sync, [this](boost::future<JackpotClientResponse<ClientBetsRespDto>> fut) -> double {
		JackpotClientResponse<ClientBetsRespDto> response = fut.get();
		if (!response.IsOK())
			return 0.0;

		ScopedLock lock(Winners);
		for (const auto& level : response.Value()->LevelsData)
			for (const auto& bet : level.BetsData)
				if (bet.Status == EJackpotBetStatus::Won)
					(&Winners)[bet.BetReference][level.LevelID] = { bet.AmountWon, false };

		return response.Value()->PaidIn;
	});
}
