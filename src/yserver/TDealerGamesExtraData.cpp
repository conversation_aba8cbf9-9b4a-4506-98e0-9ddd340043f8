#include "yserver/TDealerGamesExtraData.h"

#include "roulette/TRouletteTypes.h"
#include "yserver/YSharedTypes.h"

using namespace yserver::gamehost;

json BetAmountsDto::BetsAsJSON(uint32_t creditMultiplier) const noexcept
{
	json betTable(json::value_t::object);
	for (const auto& [betType, betAmount] : mBets) betTable[betType] = betAmount / creditMultiplier;

	return betTable;
}

void BetAmountsDto::FromJSON(const json& val, BetAmountsDto& out, uint32_t credit_multiplier)
{
	for (const auto& [key, value] : val.items())
	{
		const uint64_t amount = value.get<uint64_t>() * credit_multiplier;
		if (amount)
		{
			out.mBets.emplace(key, amount);
		}
	}
}

template <class Phase>
json CardPositionDto<Phase>::AsJSON() const noexcept
{
	json cards(json::value_t::array);

	for (const auto& card : Cards)
	{
		json cardDto(json::value_t::object);
		cardDto["index"] = card.first._to_integral();
		cardDto["card"] = card.second;
		cards.push_back(cardDto);
	}
	return cards;
}

template <class Phase>
void CardPositionDto<Phase>::FromJSON(const json& val, CardPositionDto<Phase>& out)
{
	for (const json& gameRound : val["cards"])
	{
		Phase index = Phase::_from_integral(gameRound["index"].get<int32_t>());
		int32_t card = gameRound["card"].get<int32_t>();
		out.Cards.insert({ index, card });
	}
}

json TDealerCardGameExtraDataDto::AsJSON() const
{
	json extraData(json::value_t::object);
	extraData["gameType"] = gameType._to_string();
	extraData["bets"] = bets.BetsAsJSON(creditMultiplier);
	extraData["raise"] = raise.BetsAsJSON(creditMultiplier);
	extraData["multiplier"] = creditMultiplier;
	if (stakeInfoID.has_value())
	{
		extraData["stake"] = stakeInfoID.value();
		extraData["chipValues"] = chipValues.value();
	}
	extraData["result"] = gameRecord.ToJSON();
	extraData["cards"] = cards;
	extraData["extraData"] = cards;
	extraData["wins"] = CalculateGameWins.ToJSON();

	return extraData;
}
void TDealerCardGameExtraDataDto::FromJSON(const json& val, TDealerCardGameExtraDataDto& out)
{
	out.creditMultiplier = val["multiplier"].get<int32_t>();
	BetAmountsDto::FromJSON(val["bets"], out.bets, out.creditMultiplier);
	BetAmountsDto::FromJSON(val["raise"], out.raise, out.creditMultiplier);
	if (const json* confEnabled = FindMember(val, "stake"))
	{
		out.stakeInfoID = confEnabled->get<int32_t>();
		size_t count = 0;
		for (const auto& card : val["chipValues"])
		{
			if (count < out.cards.size())
			{
				out.chipValues.value()[count] = card.get<int32_t>();
				count++;
			}
		}
	}
	out.gameRecord = dealer_assist::GameRecordDto::FromJSON(val["result"]);
	out.cards = val["cards"];
	out.extraData = val["extraData"];

	auto gameType = FindMember(val, "gameType");
	if (gameType && gameType->is_string())
		out.gameType = dealer_assist::EGameType::_from_string(gameType->get<std::string>().c_str());
	CalculateCardGameWinsResult::FromJson(val["wins"], out.CalculateGameWins);
}

json TThreeHeadedDragonExtraDataDto::AsJSON() const
{
	json data = TDealerCardGameExtraDataDto::AsJSON();
	if (goldenCard.has_value())
		data["goldenCard"] = goldenCard.value();

	return data;
}

void TThreeHeadedDragonExtraDataDto::FromJSON(const json& val, TThreeHeadedDragonExtraDataDto& out)
{
	TDealerCardGameExtraDataDto::FromJSON(val, out);
	if (const json* goldenCardJson = FindMember(val, "goldenCard"))
		out.goldenCard = goldenCardJson->get<int32_t>();
}

json WonField::ToJSON() const
{
	json betPosition(json::value_t::object);
	betPosition["credits"] = credits;
	betPosition["type"] = type;
	betPosition["winAmount"] = won;
	return betPosition;
}
void WonField::FromJson(const json& json, WonField& field)
{
	field.credits = json["credits"].get<uint32_t>();
	field.type = json["type"].get<std::string>();
	field.won = json["winAmount"].get<uint64_t>();
}

json CalculateCardGameWinsResult::ToJSON() const
{
	json calculateWins(json::value_t::object);
	calculateWins["totalWon"] = TotalWon;

	json betNumbers(json::value_t::array);
	for (auto it = WinningFields.begin(); it != WinningFields.end(); ++it) { betNumbers.push_back(it->ToJSON()); }
	calculateWins["WonFields"] = std::move(betNumbers);
	return calculateWins;
}

void CalculateCardGameWinsResult::FromJson(const json& jsonIn, CalculateCardGameWinsResult& field)
{
	field.TotalWon = jsonIn["totalWon"].get<uint64_t>();
	if (const json* val = FindMember(jsonIn, "WonFields"))
	{
		for (auto conf = val->begin(); conf != val->end(); ++conf)
		{
			WonField wonField;
			WonField::FromJson(*conf, wonField);
			field.WinningFields.emplace_back(wonField);
		}
	}
}

namespace yserver::gamehost
{
template struct CardPositionDto<baccarat::EBaccaratDealingPhase>;
template struct CardPositionDto<dragontiger::EDragonTigerDealingPhase>;
template struct CardPositionDto<threeheadeddragon::EThreeHeadedDragonDealingPhase>;
}    // namespace yserver::gamehost
