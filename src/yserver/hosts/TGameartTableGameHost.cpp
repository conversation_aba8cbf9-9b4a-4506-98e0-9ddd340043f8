#include "hosts/TGameartTableGameHost.h"

#include "Cryptography.h"
#include "YServer.h"

using namespace yserver;
using namespace yserver::gamehost;

DEFINE_LOG_CATEGORY(LogGameartTableGames, "gameart-table-games")

using namespace yserver::gamehost::gameart;

// this is called from V8
void DoEngineRequest(const v8::FunctionCallbackInfo<v8::Value>& args)
{
	if (args.Length() < 1)
		return;

	v8::Isolate* isolate = args.GetIsolate();
	v8::HandleScope scope(isolate);
	v8::String::Utf8Value phpServerAddress(isolate, args.Data());

	auto client = web::WebClient::New(*phpServerAddress);
	auto response = client->Request(web::http::verb::post, "/index.php", *v8::String::Utf8Value(isolate, args[0])).get();

	int result;
	if (response.get_error_code())
		result = -1;
	else if (response.get_status_code() == web::http::status::ok)
		result = 0;
	else
		result = response.get_status_code();

	if (result)
	{
		TLOG(LogGameartTableGames, Error, "Error response %d(%s) from PHP server at %s: %s", result, response.get_status_msg().c_str(), client->HostAddress.c_str(),
		     response.get_body().c_str());
	}

	json ret(json::value_t::object);
	ret["result"] = result;
	try
	{
		ret["body"] = json::parse(response.get_body());
	}
	catch (const std::exception& e)
	{
		TLOG(LogGameartTableGames, Error, "Error parsing response from PHP server at %s: %s", client->HostAddress.c_str(), e.what());
	}

	args.GetReturnValue().Set(V8Interface::FromJSON(isolate, ret));
}

TableGame::TableGame(const std::string& host_uid, const GameInformation& info, const std::vector<unsigned char>& engineScript,
                     const std::vector<unsigned char>& gameScript, const std::string& phpAddress, const json& engineConfig, const json& gameConfig) :
    GameartGame(host_uid, info, engineScript, gameScript, engineConfig, gameConfig),
    GID(Config().is_object() ? Config()["platform"]["gid"].get<std::string>() : std::string())
{
	v8::Locker lock(V8());
	v8::Isolate::Scope isolate_scope(V8());
	v8::HandleScope handle_scope(V8());

	v8::Local<v8::Context> ctx(GetContext());
	ctx->Global()
	  ->Set(ctx, v8::String::NewFromUtf8Literal(V8(), "sendEngineRequest"),
	        v8::Function::New(ctx, DoEngineRequest, v8::String::NewFromUtf8(V8(), phpAddress.c_str()).ToLocalChecked()).ToLocalChecked())
	  .Check();
}

json TableGame::GetGameRoundData() const
{
	json tableGameData = GameartGame::GetGameRoundData();
	tableGameData["gid"] = GID;
	return tableGameData;
}

const JsonSchema GameartTableGameHostSchema = JsonSchema({ { "php-server-address", JsonSchema(json::value_t::string, "The address of the PHP server to conenct to") } });
TGameartTableGameHost::TGameartTableGameHost(YModuleContainerBase* container, const std::string& name) : TGameartGameHost(container, name, HostType::GameartTableGame)
{
	SetLogCategory(LogGameartTableGames);
	Schema() += GameartTableGameHostSchema;
	bRequiresService = true;
}

void TGameartTableGameHost::OnConfigLoaded(const std::filesystem::path& filename)
{
	TGameartGameHost::OnConfigLoaded(filename);

	mPHPServerAddress = GetConfig("php-server-address").get<std::string>();
}

bool TGameartTableGameHost::ConnectToService(std::string& outError)
{
	auto client = web::WebClient::New(mPHPServerAddress, mPHPServerAddress.starts_with("https"));

	std::error_code ec;
	web::http::response response = client->Request(web::http::verb::get, "/index.php").get();

	if (ec)
	{
		outError = "Could not connect to PHP table game server: " + ec.message();
		Log(Error, "Could not connect to PHP table game server at %s: %s", mPHPServerAddress.c_str(), ec.message().c_str());
		return false;
	}

	if (response.get_status_code() != web::http::status::ok)
	{
		outError = "Failure connecting to PHP table game server: " + response.get_status_msg();
		Log(Error, "Could not connect to PHP table game server at %s: HTTP response %d(%s)", mPHPServerAddress.c_str(), (int)response.get_status_code(),
		    response.get_status_msg().c_str());
		return false;
	}

	try
	{
		json info = json::parse(response.get_body());
	}
	catch (const std::exception& e)
	{
		outError = "Bad response from table game server: " + std::string(e.what());
		Log(Error, "Error parsing response from PHP server at %s: %s", mPHPServerAddress.c_str(), e.what());
		return false;
	}

	return true;
}

std::shared_ptr<GameartGame> TGameartTableGameHost::CreateGameartGameInstance(const GameInformation& game, const std::shared_ptr<GameEndpoint>& endp,
                                                                              const YPlayerAccount& account, const std::vector<unsigned char>& engineScript,
                                                                              const std::vector<unsigned char>& gameScript, const json& engineConfig,
                                                                              const json& gameConfig) const
{
	return std::make_shared<TableGame>(endp->HostUID, game, engineScript, gameScript, mPHPServerAddress, engineConfig, gameConfig);
}

const JsonSchema BetSchema =
  JsonSchema(json::value_t::array, "List of actions to execute")
    .AddMember(JsonPath::Argument(0),
               JsonSchema({ { "action", JsonSchema(json::value_t::string, "The action to execute").SetPossibleVals({ "bet" }) }, { "context", JsonSchema() } }));
const JsonSchema& TGameartTableGameHost::GetBetSchema() const
{
	return BetSchema;
}

const JsonSchema TableGameSchema =
  (TGameHost::BaseGameSchema() +
   JsonSchema({ { "math-file", JsonSchema(json::value_t::string, "The location of the math file to use for this game (if relative, resolved based on math-dir setting)")
                                 .Flag(CriticalSettingFlag) },
                { "limits", JsonSchema(json::value_t::string, "Gameart limit string for this game (lim1:lim2:lim3....)", json()) } }));
const JsonSchema& TGameartTableGameHost::GetGameSchema() const
{
	return TableGameSchema;
}

uint64_t TGameartTableGameHost::VerifyBets(const YGameClient& player, const yprotocol::Request& req, json& response) const
{
	json Results;
	try
	{
		Results = ((TableGame&)(*player.Game())).ExecuteActions(req.RawParameters());
	}
	catch (const HostError& er)
	{
		Log(Error, "%s", er.what());
		throw BetRequestError(req, "Bet request went wrong! oh no! " + std::string(er.what()), FBetErrorInfo("InternalError"));
	}

	if (!Results.is_object() || Results.empty())
		throw BetRequestError(req, "no results", FBetErrorInfo("InternalError"));

	response = Results;
	return std::abs(Results["actionCost"].get<int64_t>()) * player.Account()->CreditValueMultiplier();
}

json TGameartTableGameHost::GetGameState(const std::shared_ptr<const YGameClient>& client) const
{
	if (!client || !client->Game())
		return json();
	return std::dynamic_pointer_cast<TableGame>(client->Game())->State();
}

void TGameartTableGameHost::BetPlaced(YGameClient& client, const FBetPlacedResult& betResult, const json& response)
{
	TableGame& game = dynamic_cast<TableGame&>(*client.Game());
	if (!betResult.Error && betResult.TotalAmount)
	{
		client.GameRoundBegin(crypto::GenRandID(8));
		client.CommitBets();
		if (game.State()["status"] == "COMPLETE")
		{
			client.ProcessGameResult(game.State()["win"].get<uint64_t>());
		}
	}
}

json TGameartTableGameHost::OnRegister(const YServerClient& client)
{
	json initResult = TGameartGameHost::OnRegister(client);

	if (const Player* player = dynamic_cast<const Player*>(&client))
	{
		TableGame& game = (TableGame&)(*player->Game());
		initResult["config"] = game.Config();
		if (!game.State().is_null())
		{
			try
			{
				game.RestoreV8State(game.State(), false);
			}
			catch (const HostError& e)
			{
				throw yprotocol::InitError(e.what());
			}
		}
	}

	return initResult;
}
