#include "yserver/hosts/roulette/RouletteJackpot.h"

#include "jackpot/client/JackpotClient.h"
#include "yserver/YServer.h"
#include "yserver/api/YPlayerAPI.h"
#include "yserver/hosts/TRouletteHost.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::roulette;

DEFINE_LOG_CATEGORY(LogRouletteJackpot, "rouletteJackpot")

const std::string JackpotIDSuffixDemo = "_demo";

void RouletteJackpot::setProvider(const std::shared_ptr<provider::TCasinoProvider>& provider, bool bIsDemo)
{
	if (mProvider.lock() == provider && bDemo == bIsDemo)
		return;

	mProvider = provider;
	bDemo = bIsDemo;
	mProviderReloadedHandle.reset();
	mProviderUIDUpdateHandle.reset();
	if (provider)
	{
		mProviderReloadedHandle =
		  provider->OnReloaded.bind([this](const std::shared_ptr<YModule>& module) { setProvider(std::dynamic_pointer_cast<provider::TCasinoProvider>(module), bDemo); });
		mProviderUIDUpdateHandle = provider->OnUIDChanged.bind([this](const std::string&) { updateUID(); });
	}

	updateUID();
}

JackpotClientConfig RouletteJackpot::GetConfigFromIdAndProvider(ERouletteJackpotType type, TRouletteHost* host,
                                                                const std::shared_ptr<provider::TCasinoProvider>& provider, const std::string& gameKey, bool bDemo)
{
	JackpotClientConfig ret;
	ret.ClientId = std::format("{}:{}:{}:{}:{}:{}", GetGameTypeStr(type, host->bConfiguredAsDoubleZero), gameKey, host->Name(), host->Container()->Server->ServerID(),
	                           provider ? provider->UniqueIdentifier() : std::string(), GetDemoStr(bDemo));
	ret.Currency = provider ? ECurrency::_from_string_nocase_nothrow(provider->Currency().c_str(), ECurrency::NONE) : host->Container()->Server->PlayForFunCurrency();
	ret.Denomination = provider ? provider->Denomination() : host->Container()->Server->DefaultDenomination();
	ret.ExternalTriggerType = GetGameTypeStr(type, host->bConfiguredAsDoubleZero);
	return ret;
}

void RouletteJackpot::ValidateClientLevels()
{
	ScopedLock jackpotConfigErrorLock(clientConfigError);
	clientConfigError->clear();

	std::vector<std::string> errors;

	const auto clientLevels = Client->GetLevels();

	// levels count check
	if (clientLevels.size() != mConfig.JackpotLevels.size())
		errors.emplace_back(std::format("Jackpot levels count mismatch (expected {}, got {})", mConfig.JackpotLevels.size(), clientLevels.size()));

	std::unordered_set<std::uint16_t> clientLevelRanks;
	for (const auto& [levelId, level] : clientLevels)
	{
		if (!level)
		{
			errors.emplace_back(std::format("Jackpot level {} is null", levelId));
			continue;
		}

		// ClientGroupID check
		if (level->Info.ClientGroupID != PotID)
			errors.emplace_back(std::format("Jackpot level {} clientGroupId mismatch (expected {}, got: {})", levelId, PotID, level->Info.ClientGroupID));

		// status check
		if (level->Values.Status == EJackpotLevelStatus::Disabled)
			errors.emplace_back(std::format("Jackpot level {} is disabled", levelId));

		// ExternalTriggerTypeData check
		if (const std::string& triggerType = GetGameTypeStr(mType, mHost->bConfiguredAsDoubleZero);
		    !level->Info.ExternalTriggerTypeData.contains(GetGameTypeStr(mType, mHost->bConfiguredAsDoubleZero)))
			errors.emplace_back(std::format("Jackpot level {} does not have trigger type data for {}", levelId, triggerType));

		// rank existence in level check
		if (const auto* rankJson = FindMember(level->Info.CustomData, "rank"); rankJson->is_null() || !rankJson->is_number_unsigned())
			errors.emplace_back(std::format("Jackpot level {} does not have a valid rank value", levelId));
		else
			clientLevelRanks.emplace(rankJson->get<uint16_t>());
	}

	// rank existence in config check
	for (const auto configRank : mConfig.JackpotLevels | std::views::keys)
		if (!clientLevelRanks.contains(configRank))
			errors.emplace_back(std::format("Jackpot level with rank {} is missing", configRank));

	if (!errors.empty())
	{
		&clientConfigError = yutils::Join(errors, "; ");
		Client->Log(Error, "Jackpot levels validation failed: [%s]", clientConfigError->c_str());
	}
}

void RouletteJackpot::UpdateJackpotLevelRoundProbability(const JackpotLevelInfoBase& levelInfo)
{
	if (levelInfo.ClientGroupID != PotID)
		return;

	const std::string& triggerType = GetGameTypeStr(mType, mHost->bConfiguredAsDoubleZero);
	if (const auto& triggerTypeData = levelInfo.ExternalTriggerTypeData.find(triggerType); triggerTypeData != levelInfo.ExternalTriggerTypeData.end())
	{
		const uint16_t numbersCount = mHost->bConfiguredAsDoubleZero ? 38 : 37;
		const auto& winProbability = triggerTypeData->second.WinProbability;
		const auto roundProbability = winProbability * numbersCount;
		LevelsWinMetadata.insert_or_assign(levelInfo.ID, LevelWinMetadata(winProbability, roundProbability, levelInfo.AvgPayout, levelInfo.Increment));
	}
}

std::string RouletteJackpotTypeStr(ERouletteJackpotType type)
{
	switch (type)
	{
		case ERouletteJackpotType::LuckyNumber: return "lucky-number";
		case ERouletteJackpotType::FireballDeluxe: return "fireball";
		default: throw std::runtime_error("Invalid jackpot type");
	}
}

std::string RouletteJackpot::GetGameTypeStr(ERouletteJackpotType type, const bool doubleZero)
{
	const auto doubleZeroStr = doubleZero ? "double-zero" : "single-zero";
	return std::format("{}-{}", RouletteJackpotTypeStr(type), doubleZeroStr);
}

std::string RouletteJackpot::GetDemoStr(const bool bDemo)
{
	return bDemo ? "demo" : "live";
}

/**
 * Pot key is a combination of the provider UID (if any) and a demo suffix if the provider is in demo mode
 */
std::string RouletteJackpot::GetUID(ERouletteJackpotType type, bool doubleZero, const std::string& gameKey, const std::shared_ptr<provider::TCasinoProvider>& provider,
                                    bool bDemo)
{
	return std::format("{}:{}:{}:{}", GetGameTypeStr(type, doubleZero), gameKey, provider ? provider->UniqueIdentifier() : std::string(), GetDemoStr(bDemo));
}

RouletteJackpot::RouletteJackpot(ERouletteJackpotType type, const RouletteJackpotConfig& config, TRouletteHost* host,
                                 const std::shared_ptr<provider::TCasinoProvider>& provider, const std::string& gameKey, bool bDemo) :
    TroniusJackpotModuleBase(config.JackpotAddress, config.bSecure, GetConfigFromIdAndProvider(type, host, provider, gameKey, bDemo), false, false, config.Auth,
                             config.TLSCertAuthorities),
    mGameKey(gameKey), mHost(host), mConfig(config), mType(type), PotID(GetUID(type, host->bConfiguredAsDoubleZero, gameKey, provider, bDemo))
{
	SetLogCategory(LogRouletteJackpot);
	LogComponentName = type._to_string();

	Log(Info, "Initializing jackpot instance for %s", PotID.c_str());

	AdminClient = std::make_unique<JackpotAdminClient>(mConfig.JackpotAddress, mConfig.bSecure, mConfig.Auth, mConfig.TLSCertAuthorities, type._to_string());

	setProvider(provider, bDemo);
	mHostUIDUpdateHandle = mHost->OnUIDChanged.bind([this](const std::string&) { updateUID(); });

	HistoryJSON = GetHistoryFromDB(host);

	PrepareJackpotClient();
	Client->Connect();
}

RouletteJackpot::~RouletteJackpot()
{
	shutdown();
}

void RouletteJackpot::shutdown()
{
	if (Client)
	{
		Client->Disconnect();
		Client.reset();
	}
}

void RouletteJackpot::PrepareJackpotClient()
{
	Client->OnInitialized += [this](const InitRespDto& init) {
		for (const auto& level : init.LevelsData)
		{
			// 1. Clear and pending bets
			// FIXME: this will be removed once we implement SW-3444
			bool bShouldClearPendingBets = false;
			for (const auto& pendingBet : level.PendingBetsData)
			{
				if (pendingBet.Status == EJackpotBetStatus::WonPendingConfirmation)
				{
					// If at least 1 WonPendingConfirmation bet, then we should clear pending bets

					// We are not currently checking for for current gameID, because in normal scenario, the messages external-jackpot-result and
					// confirm-external-jackpot-win are sent within a very small time (e.g. millisecond) and if anything went wrong there, then we are sure that
					// something went very wrong and we should clear pending bets. In this case jackpot win should be resolved manually.
					bShouldClearPendingBets = true;
				}
			}

			if (bShouldClearPendingBets)
				ResolvePendingWinBetsAsLost(level.LevelID);
		}

		// 2. Validate if we have correct levels
		ValidateClientLevels();

		// 3. Update round probability for each level
		for (const auto& level : Client->GetLevels() | std::views::values)
			if (level)
				UpdateJackpotLevelRoundProbability(level->Info);
	};

	Client->OnJackpotLevelUpdatedEvent += [this](const JackpotLevelUpdatedEventDto& req) {
		mHost->SendJackpotUpdates(req.LevelID, weak_from_this());

		// Validate in case if level status changed (e.g. was disabled/enabled)
		ValidateClientLevels();
	};

	Client->OnJackpotLevelReconfiguredEvent += [this](const JackpotLevelReconfiguredEventDto& req) {
		// Validate in case if e.g. customData.rank or clientGroupId changed
		ValidateClientLevels();

		UpdateJackpotLevelRoundProbability(req);
	};
}

boost::future<void> RouletteJackpot::ResolvePendingWinBetsAsLost(const std::string& levelId) const
{
	return Client->ConfirmExternalJackpotWin(ConfirmExternalJackpotWinReqDto(levelId, false, {}))
	  .then(boost::launch::sync, [this, levelId](boost::future<JackpotClientResponse<ConfirmExternalJackpotWinRespDto>> fut) {
		  if (const auto doNotConfirmResp = fut.get(); !doNotConfirmResp.IsOK())
			  Client->Log(Warning, "JP confirm win request failed: %s", doNotConfirmResp.Error()->what());

		  Client->ExternalJackpotResult(ExternalJackpotResultReqDto(levelId, false))
		    .then(boost::launch::sync, [this, levelId](boost::future<JackpotClientResponse<ExternalJackpotResultRespDto>> fut) {
			    if (const auto externalResultResp = fut.get(); !externalResultResp.IsOK())
				    Client->Log(Warning, "JP external result request failed: %s", externalResultResp.Error()->what());
		    });
	  });
}

std::shared_ptr<provider::TCasinoProvider> RouletteJackpot::GetProvider() const
{
	return mProvider.lock();
}

std::string RouletteJackpot::GetGameKey() const
{
	return mGameKey;
}

bool RouletteJackpot::IsDemo() const
{
	return bDemo;
}

boost::future<double> RouletteJackpot::CommitBetsToPot(YGameClient& player, double totalBetMoney)
{
	std::unique_lock lock(BetsMutex);
	BatchedBetReqParams& Params = PendingBets[player.UniqueIdentifier()];
	Params.BetAmount = totalBetMoney;
	Params.PlayerName = player.Account()->Nickname();
	Params.PlayerRef = player.Account()->ID();
	Params.SessionRef = player.Account()->Session();
	return Params.Promise.get_future();
}

void RouletteJackpot::SendOutPendingBets(const std::function<void(bool bDoubleZero)>& preProcess, const uint64_t roundId)
{
	if (!Client->IsConnected())
	{
		Client->Log(Error, "JP client is not connected, cannot send out pending bets to JP server");
		return;
	}

	SharedScopedLock jackpotConfigErrorLock(clientConfigError);
	if (!clientConfigError->empty())
	{
		Client->Log(Error, "Jackpot config error: [%s]", clientConfigError->c_str());
		PendingBets.clear();
		return;
	}
	jackpotConfigErrorLock.unlock();

	preProcess(mHost->bConfiguredAsDoubleZero);

	std::unique_lock lock(BetsMutex);
	if (PendingBets.empty())
		return;

	std::vector<ClientBetDto> bets;
	bets.reserve(PendingBets.size());
	for (const auto& [betRef, bet] : PendingBets)
		bets.emplace_back(bet.BetAmount, bet.PlayerName, betRef, bet.PlayerRef.AsString(), bet.SessionRef, std::to_string(roundId));
	Client->Bet(bets).then(boost::launch::sync, [this](boost::future<JackpotClientResponse<ClientBetsRespDto>> fut) {
		JackpotClientResponse<ClientBetsRespDto> response = fut.get();

		std::unordered_map<std::string, double> betRefToContribution;
		std::unique_lock lock(BetsMutex);

		if (response.IsOK())
		{
			for (const auto& level : response.Value()->LevelsData)
				for (const auto& bet : level.BetsData)
				{
					betRefToContribution[bet.BetReference] += bet.PaidInAmount;    // this also handles the case when the bet was not found in PendingBets

					if (PendingBets.contains(bet.BetReference))
						BetRefToBetResultPerLevel[level.LevelID][bet.BetReference] = { bet.BetID, bet.PlayerReference, bet.Username };
				}
		}
		else
		{
			Client->Log(Warning, "JP bet request failed: %s", response.Error()->what());
		}

		// pending bets that were not handled are given 0 as their contribution amount
		for (auto& [betRef, bet] : PendingBets)
		{
			if (const auto find = betRefToContribution.find(betRef); find != betRefToContribution.end())
				bet.Promise.set_value(find->second);
			else
				bet.Promise.set_value(0.0);
		}
		PendingBets.clear();
	});
}

void RouletteJackpot::ResetRound()
{
	Winners = std::map<std::string, std::map<std::string, jackpot::JackpotWinInformation>>();
	std::unique_lock lock(BetsMutex);
	WonJackpotLevels.clear();
	PendingBets.clear();
	BetRefToBetResultPerLevel.clear();
}

void RouletteJackpot::updateUID()
{
	const auto provider = mProvider.lock();
	std::string uidKey = mHost->UniqueIdentifier() + "/";
	if (provider)
	{
		uidKey += provider->UniqueIdentifier();
		if (bDemo)
			uidKey += JackpotIDSuffixDemo;
	}
	SetUID(uidKey);
	PotID = GetUID(mType, mHost->bConfiguredAsDoubleZero, mGameKey, provider, bDemo);
}

std::list<JackpotLevelInfoBase> RouletteJackpot::NotifyExternalTrigger(const std::function<bool(const JackpotLevelDto&)>& levelWon, const bool isFlagged)
{
	const auto levels = Client->GetLevels();
	std::unique_lock lock(BetsMutex);
	WonJackpotLevels.clear();
	for (const auto& [levelId, level] : levels)
	{
		if (level && levelWon(*level))
		{
			WonJackpotLevels.push_back(level->Info);
			Client->ExternalJackpotResult(ExternalJackpotResultReqDto(levelId, true));
		}
		else
		{
			Client->ExternalJackpotResult(ExternalJackpotResultReqDto(levelId, false, isFlagged));
		}
	}

	return WonJackpotLevels;
}

RouletteJackpot::LevelWinInfo RouletteJackpot::ProcessPotentialWinners(const std::list<std::string>& winners)
{
	std::unique_lock lock(BetsMutex);
	if (WonJackpotLevels.empty())
	{
		BetRefToBetResultPerLevel.clear();
		return {};
	}

	LevelWinInfo info;
	for (const auto& activeLevel : WonJackpotLevels)
	{
		auto betRefToBetResult = BetRefToBetResultPerLevel.find(activeLevel.ID);
		if (betRefToBetResult == BetRefToBetResultPerLevel.end())
			continue;

		std::unordered_set<std::string> betIDsThatWon;
		for (const std::string& winnerRef : winners)
		{
			const auto find = betRefToBetResult->second.find(winnerRef);
			if (find != betRefToBetResult->second.end())
				betIDsThatWon.insert(find->second.BetID);
		}

		if (betIDsThatWon.empty())
		{
			ResolvePendingWinBetsAsLost(activeLevel.ID);
			continue;
		}

		auto response = Client->ConfirmExternalJackpotWin(ConfirmExternalJackpotWinReqDto(activeLevel.ID, true, betIDsThatWon)).get();
		if (response.IsOK())
		{
			std::map<std::string, double> betRefToWonAmount;
			for (const auto& bet : response.Value()->BetsProcessed)
			{
				if (bet.BetStatus == EJackpotBetStatus::Won)
					betRefToWonAmount[bet.BetReference] = bet.AmountWon;
			}

			for (auto it = betRefToBetResult->second.begin(); it != betRefToBetResult->second.end();)
			{
				const auto find = betRefToWonAmount.find(it->first);
				if (find != betRefToWonAmount.end() && find->second > 0.0)
				{
					it->second.WonAmount = find->second;
					++it;
				}
				else
				{
					it = betRefToBetResult->second.erase(it);
				}
			}
		}
		else
		{
			Client->Log(Warning, "JP confirm win request failed: %s", response.Error()->what());
		}

		ScopedLock lock2(Winners);
		for (const auto& [betRef, result] : betRefToBetResult->second)
		{
			info.Winners.push_back({ betRef, result.PlayerRef, result.PlayerName, result.WonAmount });
			(&Winners)[betRef][activeLevel.ID] = { result.WonAmount, false };
			info.TotalWon += result.WonAmount;
		}
	}
	WonJackpotLevels.clear();
	return info;
}

json RouletteJackpot::GetHistoryJSON() const
{
	SharedScopedLock lock(HistoryJSON);
	return &HistoryJSON;
}

std::string RouletteJackpot::GetHistoryKey() const
{
	return std::format("{}_history_{}", mType._to_string(), PotID);
}

void RouletteJackpot::AddHistory(const json& historyEntry)
{
	if (!mHost)    // if there is no mHost on the jackpot, there is no point in saving the history (as JP is not played)
		return;

	ScopedLock lock(HistoryJSON);
	if (HistoryJSON->size() >= mHost->Container()->Server->PlayerAdminAPI->MaxHistorySize)
		HistoryJSON->erase(0);
	HistoryJSON->push_back(historyEntry);

	// save history to DB
	mHost->SetParam(GetHistoryKey(), yutils::jsonToStr(&HistoryJSON));
}

json RouletteJackpot::GetHistoryFromDB(TRouletteHost* host) const
{
	return yutils::strToJson(host->GetParam(GetHistoryKey()), true);
}
