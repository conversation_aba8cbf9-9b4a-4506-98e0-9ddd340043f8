#include "yserver/hosts/roulette/LuckyNumberJackpot.h"

using namespace yserver::gamehost::roulette;

LuckyNumberJackpot::LuckyNumberJackpot(const RouletteJackpotConfig& config, TRouletteHost* host, const std::shared_ptr<provider::TCasinoProvider>& provider,
                                       const std::string& gameKey, bool bDemo) :
    RouletteJackpot(ERouletteJackpotType::LuckyNumber, config, host, provider, gameKey, bDemo)
{
}

LuckyNumberJackpot::~LuckyNumberJackpot()
{
	Log(Info, "Destroying LuckyNumberJackpot");
}

std::unordered_map<std::string, int> LuckyNumberJackpot::GetLuckyNumbers() const
{
	return JackpotLuckyNumbers;
}

void LuckyNumberJackpot::AddHistory(const LuckyNumberJackpotHistory& historyEntry)
{
	RouletteJackpot::AddHistory(historyEntry.ToJSON());
}

std::list<JackpotLevelInfoBase> LuckyNumberJackpot::NotifyExternalTrigger(const int winNumber, const bool isFlagged)
{
	const auto isLevelWinnableFunc = [this, winNumber](const JackpotLevelDto& level) {
		if (winNumber < 0)
			return false;
		const auto luckyLevelIt = JackpotLuckyNumbers.find(level.Info.ID);
		return luckyLevelIt != JackpotLuckyNumbers.end() && luckyLevelIt->second == winNumber;
	};

	return RouletteJackpot::NotifyExternalTrigger(isLevelWinnableFunc, isFlagged);
}

void LuckyNumberJackpot::GenerateLuckyRound(const bool bDoubleZero)
{
	const auto levelDataResp = AdminClient->ListJackpotLevels(ListJackpotLevelsReqDtoAdmin({ PotID }, {}, false)).get();
	if (!levelDataResp.IsOK())
	{
		Log(Warning, "Failed to get JP levels: %s", levelDataResp.Error()->what());
		return;
	}

	std::vector<ListLevelDataAdminDto> levels;
	for (const auto& level : levelDataResp.Value()->Levels)
		if (level.Level.Info.ClientGroupID == PotID)
			levels.emplace_back(level);

	const uint16_t numbersCount = bDoubleZero ? 38 : 37;

	// need to decide which level (if at all) will have a lucky number drawn and store that in JackpotLuckyNumbers
	for (const auto& level : levels)
	{
		if (level.OnlineClientIDs.empty())
			return;

		const auto& levelInfo = level.Level.Info;
		const auto& levelID = levelInfo.ID;
		const std::string key = GetGameTypeStr(mType, bDoubleZero);
		const auto triggerTypeData = levelInfo.ExternalTriggerTypeData.find(key);
		if (triggerTypeData == levelInfo.ExternalTriggerTypeData.end())
		{
			Log(Warning, "Level %s does not have trigger type data for %s", levelID.c_str(), key.c_str());
			return;
		}
		const auto winProbability = triggerTypeData->second.WinProbability;
		const auto luckyJackpotProb = winProbability * numbersCount / level.OnlineClientIDs.size();
		const bool bLuckyNumberRound = crypto::RandomChance(luckyJackpotProb);
		Log(Debug, "Lucky number JP for level %s with %f probability will play this round: %s", levelID.c_str(), luckyJackpotProb, bLuckyNumberRound ? "YES" : "NO");

		if (bLuckyNumberRound)
		{
			const auto luckyNumber = crypto::GetRandomInRange(0, numbersCount - 1);    // inclusive [0,36] or [0,37]
			JackpotLuckyNumbers[levelID] = luckyNumber;
			Log(Debug, "Lucky number JP for level %s is %d", levelID.c_str(), luckyNumber);
		}
	}
}

void LuckyNumberJackpot::SendOutPendingBets(const uint64_t roundId)
{
	RouletteJackpot::SendOutPendingBets([this](bool bDoubleZero) { GenerateLuckyRound(bDoubleZero); }, roundId);
}

void LuckyNumberJackpot::ResetRound()
{
	RouletteJackpot::ResetRound();
	JackpotLuckyNumbers.clear();
}

json LuckyNumberJackpot::GetCurrentState() const
{
	auto state = RouletteJackpot::GetCurrentState();
	state["luckyLevels"] = GetLuckyNumbers();
	state["luckyHistory"] = GetHistoryJSON();
	return state;
}
