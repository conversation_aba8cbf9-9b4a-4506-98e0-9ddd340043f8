//
// Created by <PERSON><PERSON><PERSON> on 26. 9. 24.
//

#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonStake.h"

#include <MyUtils.h>

#include <format>

using namespace yserver::gamehost;
using namespace yserver::gamehost::threeheadeddragon;

FieldLimit TThreeHeadedDragonStake::GetFieldLimit(EThreeHeadedDragonBetType fieldType) const
{
	return mLimitsTable[fieldType._to_index()];
}

bool TThreeHeadedDragonStake::IsValid() const
{
	for (const uint64_t& chipVal : ChipValues)
	{
		if (chipVal)
			return true;
	}
	// if all the chip values are 0, it's an invalid stake for sure
	return false;
}

json BetMetadata::ToJson() const
{
	json root(json::value_t::array);
	for (const auto& item : Multipliers) { root.push_back({ item.first._to_string(), item.second }); }

	return root;
}

std::optional<uint32_t> BetMetadata::GetMultiplier(EThreeHeadedDragonMultiplierType type) const
{
	auto it = Multipliers.find(type);
	if (it == Multipliers.end())
		return std::nullopt;
	return it->second;
}

const JsonSchema& TThreeHeadedDragonStake::MagicTieMultiplierSchema()
{
	static const JsonSchema Schema({ { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::UnsuitedTie)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Tie - unsuited", 4) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::SuitedTie)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Tie - suited", 25) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::MagicTieUnsuited)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on MagicTie - unsuited", 50) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::MagicTieSuited)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on MagicTie - suited", 500) } });

	return Schema;
}

const JsonSchema& TThreeHeadedDragonStake::ThreeHeadedDragonMultiplierSchema()
{
	static const JsonSchema Schema({ { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsQueen)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's Queen", 1000) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsJack)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's Jack", 400) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats10)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  10", 100) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats9)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  9", 35) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats8)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  8", 25) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats7)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  7", 15) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats6)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  6", 10) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats5)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  5", 5) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats4)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  4", 5) } });

	return Schema;
}

const JsonSchema& TThreeHeadedDragonStake::ThreeHeadedDragonWesternMultiplierSchema()
{
	static const JsonSchema Schema({ { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsKing)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's king", 1000) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsQueen)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's Queen", 400) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsJack)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's Jack", 100) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats10)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  10", 35) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats9)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  9", 25) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats8)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  8", 15) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats7)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  7", 10) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats6)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  6", 5) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats5)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on 3HeadedDragon - beats tiger's  5", 5) } });

	return Schema;
}

const JsonSchema& TThreeHeadedDragonStake::DragonMultiplierSchema()
{
	static const JsonSchema Schema({ { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Default)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - Default", 1) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Beats10)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - Beats10", 1) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsJack)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - BeatsJack", 2) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsQueen)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - BeatsQueen", 3) } });

	return Schema;
}

const JsonSchema& TThreeHeadedDragonStake::DragonWesternMultiplierSchema()
{
	static const JsonSchema Schema({ { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::Default)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - Default", 1) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsJack)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - BeatsJack", 1) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsQueen)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - BeatsQueen", 2) },
	                                 { EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::BeatsKing)._to_string(),
	                                   JsonSchema(json::value_t::number_unsigned, "Multiplier on Dragon - BeatsKing", 3) } });

	return Schema;
}

const JsonSchema& TThreeHeadedDragonStake::StakeSchema()
{
	static const JsonSchema Schema(
	  { { "maxNumBets", JsonSchema(json::value_t::number_unsigned, "The maximum number of bet fields that can be bet in a single game (0 for unlimited)", 0) },
	    { "tableWinLimit", JsonSchema(json::value_t::number_unsigned, "The maximum number of credits that can be won in a single game (0 for unlimited)", 0) },
	    { "minBet", JsonSchema(json::value_t::number_unsigned, "The minimum total bet", 0) },
	    { "maxBet", JsonSchema(json::value_t::number_unsigned, "The maximum total bet (0 for unlimited)", 0) },
	    { "chipValues", JsonSchema(json::value_t::array, "The chip values in credits", json::array({ 1, 2, 5, 10, 20 }))
	                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The value of a chip in credits", 1), 5, 5) },
	    { "betTypes",
	      JsonSchema({ { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::GoldenDragon)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on GoldenDragon", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on GoldenDragon (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema({ { "asian", DragonMultiplierSchema() }, { "western", DragonWesternMultiplierSchema() } }) } },
	                                "Limits for bets on GoldenDragon") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::RedDragon)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on RedDragon", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on RedDragon (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema({ { "asian", DragonMultiplierSchema() }, { "western", DragonWesternMultiplierSchema() } }) } },
	                                "Limits for bets on RedDragon") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::BlackDragon)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on BlackDragon", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on BlackDragon (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema({ { "asian", DragonMultiplierSchema() }, { "western", DragonWesternMultiplierSchema() } }) } },
	                                "Limits for bets on BlackDragon") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::ThreeHeadedDragon)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on ThreeHeadedDragon", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on ThreeHeadedDragon (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier",
	                                    JsonSchema({ { "asian", ThreeHeadedDragonMultiplierSchema() }, { "western", ThreeHeadedDragonWesternMultiplierSchema() } }) } },
	                                "Limits for bets on ThreeHeadedDragon") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::GoldenDragonMagicTie)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Magic Tie", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Magic Tie (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", MagicTieMultiplierSchema() } },
	                                "Limits for bets on Magic Tie") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::BlackDragonMagicTie)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Magic Tie", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Magic Tie (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", MagicTieMultiplierSchema() } },
	                                "Limits for bets on Magic Tie") },
	                   { EThreeHeadedDragonBetType(EThreeHeadedDragonBetType::RedDragonMagicTie)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Magic Tie", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Magic Tie (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", MagicTieMultiplierSchema() } },
	                                "Limits for bets on Magic Tie") } },
	                 "The configuration of the stake") } });

	return Schema;
}

void TThreeHeadedDragonStake::SetMaxBet(EThreeHeadedDragonBetType fieldType, uint64_t maxBet)
{
	mLimitsTable[fieldType._to_index()].Max = maxBet;
}

void TThreeHeadedDragonStake::SetMinBet(EThreeHeadedDragonBetType fieldType, uint64_t minBet)
{
	mLimitsTable[fieldType._to_index()].Min = minBet;
}

void TThreeHeadedDragonStake::SetFieldLimit(EThreeHeadedDragonBetType fieldType, const FieldLimit& limit)
{
	mLimitsTable[fieldType._to_index()] = limit;
}

void TThreeHeadedDragonStake::SetMultiple(EThreeHeadedDragonBetType fieldType, uint64_t multiple)
{
	mLimitsTable[fieldType._to_index()].Multiple = multiple;
}

void TThreeHeadedDragonStake::SetCardBetSecuritySettings(EThreeHeadedDragonBetType fieldType, const CardGameBetSecuritySettings& security)
{
	mSecurityManager.SetSecuritySettings(fieldType._to_string(), security);
}

std::optional<CardGameBetSecuritySettings> TThreeHeadedDragonStake::GetCardBetSecuritySettings(EThreeHeadedDragonBetType fieldType) const
{
	return mSecurityManager.GetSecuritySettings(fieldType._to_string());
}

json TThreeHeadedDragonStake::BetSecuritySettingsToJson() const
{
	return mSecurityManager.ToJson();
}

TThreeHeadedDragonStake::TThreeHeadedDragonStake() : TConfiguration(StakeSchema()), PlayboardLimitMin(0), PlayboardLimitMax(0), BetsCountMax(0), MaxTableWinLimit(0)
{
	ChipValues.fill(0);
}

void TThreeHeadedDragonStake::OnConfigLoaded(const std::filesystem::path& filename)
{
	BetsCountMax = GetConfig("maxNumBets").get<uint32_t>();
	MaxTableWinLimit = GetConfig("tableWinLimit").get<uint64_t>();
	PlayboardLimitMin = GetConfig("minBet").get<uint64_t>();
	PlayboardLimitMax = GetConfig("maxBet").get<uint64_t>();

	const json& chipVals = GetConfig("chipValues");
	for (uint i = 0; i < chipVals.size(); i++) ChipValues[i + 1] = chipVals[i].get<uint64_t>();

	const json& betTypes = GetConfig("betTypes");
	for (auto betType = betTypes.begin(); betType != betTypes.end(); ++betType)
	{
		uint64_t min = 0, max = 0, multiple = 0;
		if (!betType->is_object())
			throw std::runtime_error("Bet type object '" + betType.key() + "' is invalid - value needs to be an object containing 'min' and 'max' members");

		auto typ = EThreeHeadedDragonBetType::_from_string_nothrow(betType.key().c_str());
		if (!typ)
			throw std::runtime_error("Unknown bet type '" + betType.key() + "'");

		min = (*betType)["min"].get<uint64_t>();
		max = (*betType)["max"].get<uint64_t>();
		multiple = (*betType)["multiple"].get<uint64_t>();

		if (betType->contains("multiplier"))
		{
			const auto& multiplierValue = (*betType)["multiplier"];

			// If multiplier is a number, it's a default multiplier for all the bet types
			if (multiplierValue.is_number())
			{
				std::map<EThreeHeadedDragonMultiplierType, uint32_t> items;
				items[EThreeHeadedDragonMultiplierType::Default] = multiplierValue.get<uint32_t>();
				MultiplierOverrides[typ.value()._to_string()] = BetMetadata(items);
			}
			else if (multiplierValue.is_object())    // If multiplier is an object, it's a multiplier for specific bet types
			{
				std::map<EThreeHeadedDragonMultiplierType, uint32_t> items;
				for (auto betMultiplierItem = multiplierValue.begin(); betMultiplierItem != multiplierValue.end(); ++betMultiplierItem)
				{
					auto multiplierTyp = EThreeHeadedDragonMultiplierType::_from_string_nothrow(betMultiplierItem.key().c_str());
					if (!multiplierTyp)
					{
						throw std::runtime_error(std::string("Unknown bet type '") + betMultiplierItem.key() + "'");
					}
					items[*multiplierTyp] = betMultiplierItem.value().get<uint32_t>();
					MultiplierOverrides[typ.value()._to_string()] = BetMetadata(items);
				}
			}
		}

		const json* securityJson = FindMember(*betType, "security");
		if (securityJson && !securityJson->is_null())
		{
			CardGameBetSecuritySettings security = CardGameBetSecuritySettings();
			security.bAllowOnlyWithAnte = (*securityJson)["onlyWithAnte"].get<bool>();

			uint32_t maxPercent = (*securityJson)["maxPercentOfAnteLimit"].get<uint32_t>();
			if (maxPercent != 0)
				security.mMaxPercentOfAnte = maxPercent;

			uint32_t disableAfter = (*securityJson)["disableAfter"].get<uint32_t>();
			if (disableAfter != 0)
				security.mDisableAfterNumOfBets = disableAfter;

			uint32_t maxHandsForTotalBet = (*securityJson)["maxHandsForTotalBetLimit"].get<uint32_t>();
			if (maxHandsForTotalBet != 0)
				security.mMaxBetAfterNumOfBets = std::make_pair(maxHandsForTotalBet, (*securityJson)["totalBetLimit"].get<uint32_t>());

			uint32_t maxHandsForPercentLimit = (*securityJson)["maxHandsForPercentLimit"].get<uint32_t>();
			if (maxHandsForPercentLimit != 0)
				security.mMaxBetPercentOfAnteAfterNumOfBets = std::make_pair(maxHandsForPercentLimit, (*securityJson)["percentLimit"].get<uint32_t>());

			SetCardBetSecuritySettings(*typ, security);
		}

		SetMinBet(*typ, min);
		SetMaxBet(*typ, max);
		SetMultiple(*typ, multiple);
	}
}
