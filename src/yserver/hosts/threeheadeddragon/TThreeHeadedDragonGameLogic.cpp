//
// Created by <PERSON><PERSON><PERSON> on 24. 9. 24.
//

#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"

#include "Cryptography.h"
#include "Json.h"
#include "JsonSchema.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::threeheadeddragon;

DEFINE_LOG_CATEGORY(LogThreeHeadedDragonGameLogic, "three-headed-dragon-game-logic")

uint32_t ThreeHeadedDragonGameSide::CardValue(const uint32_t card, const ECardRule cardRule)
{
	uint32_t value = card % 100;

	// In Western card rule Ace is 14
	return (cardRule == ECardRule::Western && value == 1) ? 14 : value;
}

uint32_t ThreeHeadedDragonGameSide::HandValue(const ECardRule cardRule) const
{
	if (mHand.empty())
		return 0;

	return CardValue(mHand[0], cardRule);
}

ThreeHeadedDragonGameLogic::ThreeHeadedDragonGameLogic() : TBaseGameLogic(ECardRule::Asian)
{
	bShowCutCard = false;
	mGameSide = { ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide() };
}

ThreeHeadedDragonGameLogic::ThreeHeadedDragonGameLogic(const ECardRule cardRule) : TBaseGameLogic(cardRule)
{
	bShowCutCard = false;
	mGameSide = { ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide(), ThreeHeadedDragonGameSide() };
}

ThreeHeadedDragonGameLogic::ThreeHeadedDragonGameLogic(const bool showCutCard, const ECardRule cardRule) : ThreeHeadedDragonGameLogic(cardRule)
{
	bShowCutCard = showCutCard;
}

void ThreeHeadedDragonGameLogic::ClearGame()
{
	ClearCards();

	mCutCardPosition = -1;

	mGoldenCard.reset();
}

void ThreeHeadedDragonGameLogic::ClearCards()
{
	mGameSide[EThreeHeadedDragonSide::GoldenDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::BlackDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::RedDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::Tiger].ClearCards();

	mWinners = { EThreeHeadedDragonWinner::Null };
}

void ThreeHeadedDragonGameLogic::AddCard(const uint32_t card, const uint8_t side)
{
	mGameSide[side].AddCard(card);
	TLOG(LogThreeHeadedDragonGameLogic, EVerbosity::Info, "%s add card %i.", EThreeHeadedDragonSide::_from_integral(side)._to_string(), card);
}

void ThreeHeadedDragonGameLogic::AddCutCard(const uint32_t position)
{
	mCutCardPosition = position;
	TLOG(LogThreeHeadedDragonGameLogic, EVerbosity::Info, "Added cut card at index %i.", position);
}

void ThreeHeadedDragonGameLogic::AddOrReplaceCard(const uint32_t card, const uint8_t side, const uint32_t position)
{
	TLOG(LogThreeHeadedDragonGameLogic, EVerbosity::Info, "Add/change card %i at position %i on %s side.", card, position,
	     EThreeHeadedDragonSide::_from_integral(side)._to_string());
	mGameSide[side].AddOrReplaceCard(card, position);
}

void ThreeHeadedDragonGameLogic::ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards)
{
	for (const auto& card : cards)
	{
		switch (const auto dealingPhase = EThreeHeadedDragonDealingPhase::_from_integral(card.position))
		{
			case EThreeHeadedDragonDealingPhase::TigerCard: AddOrReplaceCard(card.newValue, EThreeHeadedDragonSide::Tiger, 0); break;
			case EThreeHeadedDragonDealingPhase::BlackDragonCard: AddOrReplaceCard(card.newValue, EThreeHeadedDragonSide::BlackDragon, 0); break;
			case EThreeHeadedDragonDealingPhase::GoldenDragonCard: AddOrReplaceCard(card.newValue, EThreeHeadedDragonSide::GoldenDragon, 0); break;
			case EThreeHeadedDragonDealingPhase::RedDragonCard: AddOrReplaceCard(card.newValue, EThreeHeadedDragonSide::RedDragon, 0); break;

			default: break;
		}
	}
}

uint8_t ThreeHeadedDragonGameLogic::GetDealingPhase() const
{
	if (mGameSide[EThreeHeadedDragonSide::GoldenDragon].NumberOfCards() == 0)
		return EThreeHeadedDragonDealingPhase::GoldenDragonCard;
	if (mGameSide[EThreeHeadedDragonSide::BlackDragon].NumberOfCards() == 0)
		return EThreeHeadedDragonDealingPhase::BlackDragonCard;
	if (mGameSide[EThreeHeadedDragonSide::RedDragon].NumberOfCards() == 0)
		return EThreeHeadedDragonDealingPhase::RedDragonCard;
	if (mGameSide[EThreeHeadedDragonSide::Tiger].NumberOfCards() == 0)
		return EThreeHeadedDragonDealingPhase::TigerCard;

	return EThreeHeadedDragonDealingPhase::Finished;
}

std::optional<uint8_t> ThreeHeadedDragonGameLogic::GetWinner() const
{
	return std::nullopt;
}

std::vector<uint8_t> ThreeHeadedDragonGameLogic::GetWinners() const
{
	std::vector<uint8_t> winners;
	winners.reserve(mWinners.size());
	for (const auto& winner : mWinners) { winners.push_back(winner._to_integral()); }
	return winners;
}

std::vector<uint8_t> ThreeHeadedDragonGameLogic::Evaluate()
{
	mWinners.clear();

	auto goldenDragonValue = mGameSide[EThreeHeadedDragonSide::GoldenDragon].HandValue(mCardRule);
	auto blackDragonValue = mGameSide[EThreeHeadedDragonSide::BlackDragon].HandValue(mCardRule);
	auto redDragonValue = mGameSide[EThreeHeadedDragonSide::RedDragon].HandValue(mCardRule);
	auto tigerValue = mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule);

	if (goldenDragonValue > tigerValue)
		mWinners.push_back(EThreeHeadedDragonWinner::DragonWin);
	else if (tigerValue == goldenDragonValue)
		mWinners.push_back(EThreeHeadedDragonWinner::Tie);
	else
		mWinners.push_back(EThreeHeadedDragonWinner::TigerWin);

	if (blackDragonValue > tigerValue)
		mWinners.push_back(EThreeHeadedDragonWinner::DragonWin);
	else if (tigerValue == blackDragonValue)
		mWinners.push_back(EThreeHeadedDragonWinner::Tie);
	else
		mWinners.push_back(EThreeHeadedDragonWinner::TigerWin);

	if (redDragonValue > tigerValue)
		mWinners.push_back(EThreeHeadedDragonWinner::DragonWin);
	else if (tigerValue == redDragonValue)
		mWinners.push_back(EThreeHeadedDragonWinner::Tie);
	else
		mWinners.push_back(EThreeHeadedDragonWinner::TigerWin);

	std::vector<uint8_t> winners;
	winners.reserve(mWinners.size());
	for (const auto& winner : mWinners) { winners.push_back(winner._to_integral()); }
	return winners;
}

uint8_t ThreeHeadedDragonGameLogic::GetNumberOfCardsOnTable() const
{
	return mGameSide[EThreeHeadedDragonSide::GoldenDragon].NumberOfCards() + mGameSide[EThreeHeadedDragonSide::BlackDragon].NumberOfCards() +
	       mGameSide[EThreeHeadedDragonSide::RedDragon].NumberOfCards() + mGameSide[EThreeHeadedDragonSide::Tiger].NumberOfCards();
}

json ThreeHeadedDragonGameLogic::GetGameResult() const
{
	json result(json::value_t::object);
	json winners(json::value_t::array);

	for (auto winner : mWinners) winners.push_back(winner._to_string());

	result["winners"] = std::move(winners);

	json goldenDragon(json::value_t::object);
	goldenDragon["sum"] = mGameSide[EThreeHeadedDragonSide::GoldenDragon].HandValue(mCardRule);
	goldenDragon["card"] = mGameSide[EThreeHeadedDragonSide::GoldenDragon].CardAt(0).value();
	result["goldenDragon"] = std::move(goldenDragon);

	json blackDragon(json::value_t::object);
	blackDragon["sum"] = mGameSide[EThreeHeadedDragonSide::BlackDragon].HandValue(mCardRule);
	blackDragon["card"] = mGameSide[EThreeHeadedDragonSide::BlackDragon].CardAt(0).value();
	result["blackDragon"] = std::move(blackDragon);

	json redDragon(json::value_t::object);
	redDragon["sum"] = mGameSide[EThreeHeadedDragonSide::RedDragon].HandValue(mCardRule);
	redDragon["card"] = mGameSide[EThreeHeadedDragonSide::RedDragon].CardAt(0).value();
	result["redDragon"] = std::move(redDragon);

	json tiger(json::value_t::object);
	tiger["sum"] = mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule);
	tiger["card"] = mGameSide[EThreeHeadedDragonSide::Tiger].CardAt(0).value();
	result["tiger"] = std::move(tiger);

	return result;
}

// TODO: Implement this method
json ThreeHeadedDragonGameLogic::GetFreeHandGameResult(bool showCardFaces) const
{
	return GetGameResult();
}

dealer_assist::GameRecordDto ThreeHeadedDragonGameLogic::GetGameRecord() const
{
	dealer_assist::GameRecordDto dto;
	std::vector<uint8_t> winnersInt;
	for (const auto& winner : mWinners) { winnersInt.push_back(winner._to_integral()); }
	dto.Winners = winnersInt;
	dto.Cards = GetCurrentStateOfCards();
	dto.HandValues = GetHandValues();
	return dto;
}


void ThreeHeadedDragonGameLogic::SetCurrentStateOfCards(const json& cards)
{
	mGameSide[EThreeHeadedDragonSide::GoldenDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::BlackDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::RedDragon].ClearCards();
	mGameSide[EThreeHeadedDragonSide::Tiger].ClearCards();

	for (const auto& card : cards)
	{
		const EThreeHeadedDragonDealingPhase phase = EThreeHeadedDragonDealingPhase::_from_integral(card["index"].get<int>());
		const uint32_t cardValue = card.at("card").get<uint32_t>();

		switch (phase)
		{
			case EThreeHeadedDragonDealingPhase::GoldenDragonCard: mGameSide[EThreeHeadedDragonSide::GoldenDragon].AddCard(cardValue); break;
			case EThreeHeadedDragonDealingPhase::RedDragonCard: mGameSide[EThreeHeadedDragonSide::RedDragon].AddCard(cardValue); break;
			case EThreeHeadedDragonDealingPhase::BlackDragonCard: mGameSide[EThreeHeadedDragonSide::BlackDragon].AddCard(cardValue); break;
			case EThreeHeadedDragonDealingPhase::TigerCard: mGameSide[EThreeHeadedDragonSide::Tiger].AddCard(cardValue, 0); break;
			default: throw std::invalid_argument("Invalid dealing phase");
		}
	}
}

json ThreeHeadedDragonGameLogic::GetCurrentStateOfCards() const
{
	CardPositionDto<EThreeHeadedDragonDealingPhase> cards;

	auto addCard = [&](const EThreeHeadedDragonSide side, EThreeHeadedDragonDealingPhase phase, CardPositionDto<EThreeHeadedDragonDealingPhase>& items) {
		if (auto cardOpt = mGameSide[side].CardAt(0))
		{
			items.Cards.insert({ phase, cardOpt.value() });
		}
	};

	addCard(EThreeHeadedDragonSide::GoldenDragon, EThreeHeadedDragonDealingPhase::GoldenDragonCard, cards);
	addCard(EThreeHeadedDragonSide::BlackDragon, EThreeHeadedDragonDealingPhase::BlackDragonCard, cards);
	addCard(EThreeHeadedDragonSide::RedDragon, EThreeHeadedDragonDealingPhase::RedDragonCard, cards);
	addCard(EThreeHeadedDragonSide::Tiger, EThreeHeadedDragonDealingPhase::TigerCard, cards);

	return cards.AsJSON();
}

uint32_t ThreeHeadedDragonGameLogic::GenerateGoldenCard()
{
	mGoldenCard = crypto::GetRandomInRange(1, 13);
	return mGoldenCard.value();
}

std::optional<uint32_t> ThreeHeadedDragonGameLogic::GetGoldenCard() const
{
	return mGoldenCard;
}

bool ThreeHeadedDragonGameLogic::IsUnsuitedTie(const EThreeHeadedDragonSide side) const
{
	return mGameSide[side].HandValue(mCardRule) == mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule);
}
bool ThreeHeadedDragonGameLogic::IsSuitedTie(const EThreeHeadedDragonSide side) const
{
	const auto dragonCard = mGameSide[side].CardAt(0);
	const auto tigerCard = mGameSide[EThreeHeadedDragonSide::Tiger].CardAt(0);

	if (dragonCard && tigerCard)
		return *dragonCard == *tigerCard;

	return false;
}

bool ThreeHeadedDragonGameLogic::IsMagicSuitedTie(const EThreeHeadedDragonSide side) const
{
	const auto dragonCard = mGameSide[side].CardAt(0);
	const auto tigerCard = mGameSide[EThreeHeadedDragonSide::Tiger].CardAt(0);

	if (tigerCard && dragonCard && mGoldenCard.has_value())
	{
		TLOG(LogThreeHeadedDragonGameLogic, EVerbosity::Info, "Dragon card %i, Tiger card %i, Golden card %i", *dragonCard, *tigerCard, mGoldenCard.value());
		return *dragonCard == *tigerCard && (*tigerCard % 100) == (mGoldenCard.value() % 100);
	}

	return false;
}

bool ThreeHeadedDragonGameLogic::IsMagicUnsuitedTie(const EThreeHeadedDragonSide side) const
{
	const auto dragonCard = mGameSide[side].CardAt(0);
	const auto tigerCard = mGameSide[EThreeHeadedDragonSide::Tiger].CardAt(0);

	if (dragonCard && tigerCard && mGoldenCard.has_value())
	{
		const uint32_t dragonValue = *dragonCard % 100;
		const uint32_t tigerValue = *tigerCard % 100;
		const uint32_t goldenValue = mGoldenCard.value() % 100;
		return dragonValue == tigerValue && dragonValue == goldenValue;
	}

	return false;
}

bool ThreeHeadedDragonGameLogic::Is3HeadedDragonWin() const
{
	if (GetDealingPhase() != EThreeHeadedDragonDealingPhase::Finished)
		return false;

	const auto goldenDragonValue = mGameSide[EThreeHeadedDragonSide::GoldenDragon].HandValue(mCardRule);
	const auto blackDragonValue = mGameSide[EThreeHeadedDragonSide::BlackDragon].HandValue(mCardRule);
	const auto redDragonValue = mGameSide[EThreeHeadedDragonSide::RedDragon].HandValue(mCardRule);
	const auto tigerValue = mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule);

	// 3 Headed Dragon wins if all three dragons have higher value than tiger
	return goldenDragonValue > tigerValue && blackDragonValue > tigerValue && redDragonValue > tigerValue;
}

bool ThreeHeadedDragonGameLogic::Is3HeadedDragonWinFor(const EThreeHeadedDragonSide side, const EThreeHeadedDragonMultiplierType type) const
{
	if (mGameSide[side].HandValue(mCardRule) <= mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule))
		return false;

	switch (type)
	{
		case EThreeHeadedDragonMultiplierType::Beats4: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 4;
		case EThreeHeadedDragonMultiplierType::Beats5: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 5;
		case EThreeHeadedDragonMultiplierType::Beats6: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 6;
		case EThreeHeadedDragonMultiplierType::Beats7: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 7;
		case EThreeHeadedDragonMultiplierType::Beats8: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 8;
		case EThreeHeadedDragonMultiplierType::Beats9: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 9;
		case EThreeHeadedDragonMultiplierType::Beats10: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 10;
		case EThreeHeadedDragonMultiplierType::BeatsJack: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 11;
		case EThreeHeadedDragonMultiplierType::BeatsQueen: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 12;
		case EThreeHeadedDragonMultiplierType::BeatsKing: return mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule) == 13;
		default: return false;
	}
}

bool ThreeHeadedDragonGameLogic::IsWinner(const EThreeHeadedDragonSide& side) const
{
	return mWinners.at(side._to_integral()) == EThreeHeadedDragonWinner::DragonWin;
}

bool ThreeHeadedDragonGameLogic::HasFaceDownCard(const EThreeHeadedDragonSide side) const
{
	return mGameSide[side].HasFaceDownCard();
}

bool ThreeHeadedDragonGameLogic::HasFaceDownCard(const uint32_t side) const
{
	return mGameSide[side].HasFaceDownCard();
}

uint32_t ThreeHeadedDragonGameLogic::GetHandValue(const uint8_t side) const
{
	return mGameSide[side].HandValue(mCardRule);
}

bool ThreeHeadedDragonGameLogic::IsGameFinished() const
{
	if (HasFaceDownCard(EThreeHeadedDragonSide::Tiger))
		return false;

	if (GetDealingPhase() == EThreeHeadedDragonDealingPhase::Finished)
		return true;

	return false;
}

std::unordered_map<std::string, uint32_t> ThreeHeadedDragonGameLogic::GetHandValues() const
{
	std::unordered_map<std::string, uint32_t> handValues;
	handValues[EThreeHeadedDragonSide(EThreeHeadedDragonSide::RedDragon)._to_string()] = mGameSide[EThreeHeadedDragonSide::RedDragon].HandValue(mCardRule);
	handValues[EThreeHeadedDragonSide(EThreeHeadedDragonSide::GoldenDragon)._to_string()] = mGameSide[EThreeHeadedDragonSide::GoldenDragon].HandValue(mCardRule);
	handValues[EThreeHeadedDragonSide(EThreeHeadedDragonSide::BlackDragon)._to_string()] = mGameSide[EThreeHeadedDragonSide::BlackDragon].HandValue(mCardRule);
	handValues[EThreeHeadedDragonSide(EThreeHeadedDragonSide::Tiger)._to_string()] = mGameSide[EThreeHeadedDragonSide::Tiger].HandValue(mCardRule);

	return handValues;
}
