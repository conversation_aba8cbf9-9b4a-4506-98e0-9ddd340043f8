//
// Created by <PERSON><PERSON><PERSON> on 27. 9. 24.
//

#include "hosts/threeheadeddragon/TThreeHeadedDragonVirtualHost.h"

#include "Cryptography.h"
#include "YServer.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::threeheadeddragon;
using namespace dealer_assist;

DEFINE_LOG_CATEGORY(LogVir3HeadedDragonHost, "virt-3-headed-dragon-host")

const JsonSchema VirtualDragonTigerHostSchema = JsonSchema({
  { "stakes", JsonSchema(json::value_t::array, "List of stakes on this 3 headed dragon host", json(), true).SetChildSchema(TThreeHeadedDragonStake::StakeSchema(), 1) },
});

const JsonSchema VirtualDragonTigerGameSchema =
  TGameHost::BaseGameSchema() + TBaseVirtualCardGame::BaseVirtualCardGameSchema() +
  JsonSchema(
    { { "card-rule", JsonSchema(json::value_t::string, "Card rule to use for the game", ECardRule(ECardRule::Asian)._to_string()).SetToEnumType<ECardRule>() } });

TVirtualThreeHeadedDragonHost::TVirtualThreeHeadedDragonHost(YModuleContainerBase* container, const std::string& name) :
    TGameHost(container, name, HostType::VirtualThreeHeadedDragon)
{
	SetLogCategory(LogVir3HeadedDragonHost);
	Schema() += VirtualDragonTigerHostSchema;

	StakeChangedEvent = RegisterEvent("stakeChanged", "Triggered when the selected stake changes");

	mDefaultStake = 0;
	bAllowStakeChange = true;
	bForceStakeSelect = true;

	GameStateChangedEvent = RegisterEvent("game-state-changed", "Sent new card");

	RegisterPlayerOnlyMethod("free-game", "Play game without bet", [this](Player& player, const yprotocol::Request& req) -> json {
		VirtualThreeHeadedDragonGame& game = dynamic_cast<VirtualThreeHeadedDragonGame&>(*player.Game().get());

		if (game.CanPlayFreeHand())
		{
			json roundResult = game.PlayFreeHandRound();
			player.TriggerEvent(*GameStateChangedEvent, roundResult);

			json response(json::value_t::object);
			response["_ok"] = true;
			return response;
		}
		else
		{
			throw yprotocol::RequestError(req, "No free hands left!");
		}
	});


	RegisterPlayerOnlyMethod("shuffleCards", "Player requested reshuffeling of cards", [](Player& player, const yprotocol::Request& req) -> json {
		VirtualThreeHeadedDragonGame& game = dynamic_cast<VirtualThreeHeadedDragonGame&>(*player.Game().get());
		game.PlayerRequestedReshuffle();

		return {};
	});
}

void TVirtualThreeHeadedDragonHost::OnConfigLoaded(const std::filesystem::path& filename)
{
	TGameHost::OnConfigLoaded(filename);

	mBets.ClearStakes();

	const std::string accesModeStr = GetConfigOptional("stake-mode", EHostAccessMode(EHostAccessMode::All)._to_string()).get<std::string>();
	auto opt = EHostAccessMode::_from_string_nocase_nothrow(accesModeStr.c_str());
	if (!opt)
		throw ConfigError(std::format("Unknown stake-mode '{}'", accesModeStr));
	mStakeAccessMode = *opt;

	json accessList = GetConfigOptional("stake-access-list", {});
	if (accessList.is_array())
	{
		for (const json& member : accessList)
		{
			if (member.is_number_integer())
				mStakeAccess.insert(member.get<uint8_t>());
		}
	}

	mDefaultStake = GetConfigOptional("default-stake", mDefaultStake).get<uint8_t>();
	Log(Info, "The default stake is set to %u.", mDefaultStake);

	const json stakesJSON = GetConfig("stakes");
	if (!stakesJSON.is_array())
		throw ConfigError("No array of stakes exists in config, but it is required (with at least one entry)!");

	std::vector<TThreeHeadedDragonStake> stakes;
	for (uint stakeID = 0; stakeID < stakesJSON.size(); stakeID++)
	{
		try
		{
			TThreeHeadedDragonStake stake;
			stake.LoadSubconfiguration(stakesJSON[stakeID]);
			stakes.push_back(stake);
		}
		catch (const std::exception& e)
		{
			Log(Warning, "Stake %u is invalid: %s", stakeID, e.what());
		}
	}

	if (stakes.empty())
		throw ConfigError("No valid stakes available in the config");

	const size_t allStakesNum = stakes.size();
	Log(Info, "Successfully read %lu stakes from config!", allStakesNum);
	size_t numStakes = 0;
	for (size_t stakeIdx = 0; stakeIdx < stakes.size(); stakeIdx++)
	{
		if (!stakes[stakeIdx].IsValid())
		{
			Log(Warning, "Stake %lu is invalid and will be skipped! This means chip values are all 0.", stakeIdx);
			continue;
		}
		if (IsValidStake(stakeIdx))
		{
			try
			{
				mBets.AddStake(stakes[stakeIdx]);
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Stake %d is invalid: %s", stakeIdx, e.what());
			}
			if (mDefaultStake == stakeIdx && mDefaultStake != numStakes)
			{
				Log(Warning, "The default stake is set to %u, due because some stakes before %u are not used, it is effectively translated to %lu.", mDefaultStake,
				    mDefaultStake, numStakes);
				mDefaultStake = numStakes;
			}
			numStakes++;
		}
		else if (stakeIdx == mDefaultStake)
		{
			Log(Warning, "The default stake is set to %u, this stake is disabled due to stake-mode and stake-access-list being configured to ignore it.", mDefaultStake);
		}
	}

	if (!numStakes)
		throw ConfigError("No valid stakes were found that conform with the conditions defined by stake-mode and stake-access-list!");


	Log(Info, "%lu of the total %lu stakes will be used on this game host!", numStakes, allStakesNum);

	if (mDefaultStake >= numStakes)
	{
		Log(Warning, "The default stake is set to %u, but no such stake exists on the roulette host! Will use 0 as the default stake.", mDefaultStake);
	}

	bAllowStakeChange = GetConfigOptional("allow-stake-change", bAllowStakeChange).get<bool>();
	Log(Info, "Changing stake during game is %s", bAllowStakeChange ? "ENABLED" : "DISABLED");

	bForceStakeSelect = GetConfigOptional("force-stake-select", bForceStakeSelect).get<bool>();
	if (bForceStakeSelect)
	{
		Log(Info, "If there are multiple stakes, the player will be forced to select one uppon joining.");
		if (mDefaultStake)
			Log(Warning, "The 'default-stake' is set to %u, but wil be ignored because 'force-stake-select' is ON", mDefaultStake);
	}
}

json TVirtualThreeHeadedDragonHost::OnRegister(const YServerClient& client)
{
	json initResult = TGameHost::OnRegister(client);

	if (const YGameClient* player = dynamic_cast<const YGameClient*>(&client))
	{
		VirtualThreeHeadedDragonGame& game = (VirtualThreeHeadedDragonGame&)(*player->Game());
		game.GamePhase = EDealerAssistPhase::BetsOpen;
		initResult["config"] = GetGameConfig(game);
	}

	return initResult;
}

GameInstancePtr TVirtualThreeHeadedDragonHost::CreateGameInstance(const GameInformation& game, const std::shared_ptr<GameEndpoint>& endp,
                                                                  const YPlayerAccount& account) const
{
	auto instance = std::make_shared<VirtualThreeHeadedDragonGame>(endp->HostUID, game);
	instance->CreditMultiplier = account.CreditValueMultiplier();
	return instance;
}

// Return cards on table, stat of game, ect.
json TVirtualThreeHeadedDragonHost::GetGameState(const std::shared_ptr<const YGameClient>& client) const
{
	return json();
}

const JsonSchema BetSchema =
  JsonSchema({ { "bets", JsonSchema(json::value_t::array, "Array of bet placements", json::array()).SetChildSchema(JsonSchema(json::value_t::number_unsigned, "Bets")) },
               { "betPresets", JsonSchema(json::value_t::object, "Map of bet placements on preset fields", json())
                                 .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The amount to bet on this field")) } });
const JsonSchema& TVirtualThreeHeadedDragonHost::GetBetSchema() const
{
	return BetSchema;
}

const JsonSchema& TVirtualThreeHeadedDragonHost::GetGameSchema() const
{
	return VirtualDragonTigerGameSchema;
}

uint64_t TVirtualThreeHeadedDragonHost::VerifyBets(const YGameClient& client, const yprotocol::Request& req, json& response) const
{
	VirtualThreeHeadedDragonGame* gameInst = (VirtualThreeHeadedDragonGame*)client.Game().get();

	const TThreeHeadedDragonBets& bets = GetBets();

	try
	{
		// Will throw a BetParseError if unsuccessful
		ThreeHeadedDragonBetAmounts betAmounts;
		try
		{
			betAmounts = ThreeHeadedDragonBetAmounts::FromUserJSON(req.GetParam(), gameInst->CreditMultiplier);
		}
		catch (const BetParseError& err)
		{
			throw BetRequestError(req, err.what(), FBetErrorInfo("InvalidBetObject", err.Context));
		}

		FStakeInfo stake = gameInst->StakeInfo;
		stake.Multiplier *= gameInst->CreditMultiplier;

		const FBetVerifyResult verifiedBets = bets.VerifyBets(betAmounts, stake, gameInst->GetNumberOfPlayedRounds(), gameInst->ConfirmedBets);
		if (!verifiedBets.Violation)
		{
			if (verifiedBets.TotalBet)
			{
				gameInst->PlaceBets(betAmounts);
				response = betAmounts.BetsAsJSON(gameInst->CreditMultiplier);
				return verifiedBets.TotalBet;
			}
			else
			{
				throw BetRequestError(req, "Cannot bet 0", FBetErrorInfo("InvalidBetObject"));
			}
		}
		else if (*verifiedBets.Violation == EBetRuleViolation::BadSetup)
		{
			throw BetRequestError(req, "Invalid stake", FBetErrorInfo("InternalError"));
		}
		else
		{
			FBetErrorInfo err("BetRuleViolation");
			err.Details = json(json::value_t::object);
			err.Details["x"] = verifiedBets.ErrorX;
			err.Details["violation"] = verifiedBets.Violation->_to_string();
			throw BetRequestError(req, verifiedBets.Message, err);
		}
	}
	catch (const json::exception& err)
	{
		throw BetRequestError(req, "Problem executing commitBets: " + std::string(err.what()) + "!", FBetErrorInfo("InternalError"));
	}
}

void TVirtualThreeHeadedDragonHost::BetPlaced(YGameClient& client, const FBetPlacedResult& betResult, const json& response)
{
	Log(Info, "TVirtualThreeHeadedDragonHost::BetPlaced");
	VirtualThreeHeadedDragonGame& virtGame = dynamic_cast<VirtualThreeHeadedDragonGame&>(*client.Game());

	UpdateGamePhase(client, virtGame, EDealerAssistPhase::BetsClosed);
	bool bGameInProgress = virtGame.IsGameInProgress();

	if (!betResult.Error)
	{
		if (!bGameInProgress)
			client.GameRoundBegin(crypto::GenRandID(8));

		if (client.CommitBets().BetTransaction->Result != ETransactionResult::Success)
		{
			client.Log(Info, "Failed to start game, ignoring it!");
			virtGame.FinishGameRound();
		}
		else
		{
			if (client.Game()->IsVoid())
			{
				client.ProcessGameResult({}, FGameroundError({ "InstantVoid", "Game is void" }));
				virtGame.FinishGameRound();
			}
			UpdateGamePhase(client, virtGame, EDealerAssistPhase::DealingCards);

			// First round is already played
			if (bGameInProgress)
			{
				DrawTigerCard(client, virtGame);

				UpdateGamePhase(client, virtGame, EDealerAssistPhase::RoundEnd);
				client.Log(Info, "Processing win with winners %s...", yutils::PrintContainer(virtGame.Evaluate()).c_str());

				const uint64_t wins = GetBets().CalculateWins(virtGame.GetPlacedBets(), virtGame.StakeInfo, virtGame.GetGameLogic()).TotalWon;
				virtGame.FinishGameRound();
				client.ProcessGameResult(wins);
				client.Log(Debug, "Processing win done.");

				UpdateGamePhase(client, virtGame, EDealerAssistPhase::BetsOpen);
			}
			else
			{
				// Currently verified bets become confirmed, so in next dealing phase we can use them
				virtGame.ConfirmedBets = virtGame.GetPlacedBets();
				client.ProcessGameResult(0);

				// First round - draw golden card and dragon cards and send them to client
				DrawGoldenCard(client, virtGame);
				DrawDragonsCards(client, virtGame);
			}
		}
	}
	else
	{
		virtGame.FinishGameRound();
		UpdateGamePhase(client, virtGame, EDealerAssistPhase::BetsOpen);
	}
}

json TVirtualThreeHeadedDragonHost::GetGameConfig(const VirtualThreeHeadedDragonGame& game) const
{
	json conf(json::value_t::object);
	json stakes(json::value_t::array);

	if (mBets.GetValidStakes().size() > 0)
	{
		for (auto stake : mBets.GetValidStakes()) stakes.push_back(TThreeHeadedDragonBets::StakeAsJson(*stake, 1));
	}

	const TThreeHeadedDragonBets& bets = mBets;

	conf["phase"] = game.GamePhase._to_index();
	conf["stakes"] = std::move(stakes);
	Log(Info, "selectedStake %i.", mDefaultStake);
	conf["selectedStake"] = mDefaultStake;
	conf["numberOfCardsInShoe"] = game.NumberOfCardsInShoe();
	conf["numberOfFreeHandsAtStart"] = game.NumberOfFreeHandsAtStart();
	conf["numberOfFreeHands"] = game.NumberOfFreeHands();

	json betTypes(json::value_t::array);
	for (EThreeHeadedDragonBetType betType : EThreeHeadedDragonBetType::_values())
	{
		json betTypeInfo(json::value_t::object);
		betTypeInfo["index"] = betType._to_index();
		betTypeInfo["name"] = betType._to_string();
		betTypeInfo["multiplier"] = bets.GetMultiplier(betType, -1);
		betTypes.push_back(std::move(betTypeInfo));
	}
	conf["betTypes"] = std::move(betTypes);
	return conf;
}

json TVirtualThreeHeadedDragonHost::GetDescriptorInfo(const FModuleDescriptorOptions& options) const
{
	json conf(json::value_t::object);
	conf["stakeChangeAllowed"] = bAllowStakeChange;

	return conf;
}

void TVirtualThreeHeadedDragonHost::TryRoundRestore(YGameClient& client, const GameRoundSnapshot& snap)
{
	VirtualThreeHeadedDragonGame& game = (VirtualThreeHeadedDragonGame&)*client.Game();

	if (!snap.GeneratedRandoms.empty())
		throw yprotocol::InternalError("Gameround data indicates the game is completed!");

	try
	{
		game.PlaceBets(ThreeHeadedDragonBetAmounts::FromJSON(snap.ExtraData, game.CreditMultiplier));
	}
	catch (const BetParseError& e)
	{
		throw yprotocol::InternalError(e.what());
	}

	if (game.GetPlacedBets().Empty())
		throw yprotocol::InternalError("There are no placed bets to restore!");

	throw yprotocol::InternalError("Cannot restore virtual baccarat game!");
}

bool TVirtualThreeHeadedDragonHost::IsValidStake(uint32_t stakeIdx) const
{
	switch (mStakeAccessMode)
	{
		case EHostAccessMode::All: return true;
		case EHostAccessMode::OptIn: return mStakeAccess.find(stakeIdx) != mStakeAccess.end();
		case EHostAccessMode::OptOut: return mStakeAccess.find(stakeIdx) == mStakeAccess.end();
	}
	return false;
}

const TThreeHeadedDragonBets& TVirtualThreeHeadedDragonHost::GetBets() const
{
	return mBets;
}

void TVirtualThreeHeadedDragonHost::OnPlayerAdded(Player& player, bool bReconnected) noexcept
{
	TGameHost::OnPlayerAdded(player, bReconnected);

	auto game = dynamic_cast<VirtualThreeHeadedDragonGame*>(player.Game().get());

	const TThreeHeadedDragonBets& bets = GetBets();

	UpdateGamePhase(player, *game, EDealerAssistPhase::BetsOpen);

	if (!bReconnected)
	{
		if (player.ConInfo->Query.contains("stake"))
		{
			const std::string stake = player.ConInfo->Query.Get("stake");
			int stakeID;
			if (yutils::strToInt2(stake, stakeID) && stakeID >= 0 && stakeID < (int)bets.GetValidStakes().size())
				game->StakeInfo = { stakeID, bets.GetStake(stakeID), 1 };
		}
	}

	// If a stake is already selected, then we're ok!
	if (game->StakeInfo.ID != -1 && game->StakeInfo.Stake)
		return;

	// If we only have one stake or we aren't forcing a stake select, go to default stake
	if (bets.GetValidStakes().size() == 1 || !bForceStakeSelect)
	{
		const int stakeID = (mDefaultStake >= bets.GetValidStakes().size()) ? 0U : mDefaultStake;
		game->StakeInfo = { stakeID, bets.GetStake(stakeID), 1 };
		json stakeChangeData(json::value_t::object);
		stakeChangeData["id"] = stakeID;
		stakeChangeData["stake"] = TThreeHeadedDragonBets::StakeAsJson(*game->StakeInfo.Stake, 1);
		player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
	}
	else
	{
		std::vector<yprotocol::ChoiceQuestion::Choice> choices;
		int i = 0;
		for (auto stake : bets.GetValidStakes())
		{
			yprotocol::ChoiceQuestion::Choice thisStake;
			thisStake.Title = "Stake " + std::to_string(i++);
			thisStake.Data = TThreeHeadedDragonBets::StakeAsJson(*stake, 1);
			choices.push_back(thisStake);
		}
		auto question = std::make_shared<yprotocol::ChoiceQuestion>("stake", "Select a stake", choices);
		// game->SetLock(player, RLOCK_STAKE_SELECT, true);
		player.Ask(question,
		           [this, bets](yprotocol::YProtocolClient& client, yprotocol::QuestionPtr q, const json& answer, json& finalResponse) -> yprotocol::EQuestionResult {
			           Player& player = dynamic_cast<Player&>(client);
			           auto game = dynamic_cast<VirtualThreeHeadedDragonGame*>(player.Game().get());
			           if (answer.size())
			           {
				           int stakeID = answer[0].get<uint8_t>();
				           game->StakeInfo = { stakeID, bets.GetStake(stakeID), 1 };
				           // game->SetLock(player, RLOCK_STAKE_SELECT, false);
				           json stakeChangeData(json::value_t::object);
				           stakeChangeData["id"] = stakeID;
				           stakeChangeData["stake"] = TThreeHeadedDragonBets::StakeAsJson(*game->StakeInfo.Stake, 1);
				           player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
				           finalResponse = stakeChangeData;
				           return yprotocol::EQuestionResult::OK;
			           }
			           else
			           {
				           return yprotocol::EQuestionResult::ASK_AGAIN;
			           }
		           });
	}
}

void TVirtualThreeHeadedDragonHost::OnAddGameToHost(const StaticGameInformation& info)
{
	TGameHost::OnAddGameToHost(info);

	auto stakes = GetBets().GetValidStakes();
	if (stakes.empty())
		throw ConfigError("There is no valid stakes for this host!");
}

void TVirtualThreeHeadedDragonHost::DrawGoldenCard(YGameClient& client, VirtualThreeHeadedDragonGame& game) const
{
	json obj(json::value_t::object);
	obj["name"] = "goldenCard";
	obj["card"] = game.GetGoldenCard();
	client.TriggerEvent(*GameStateChangedEvent, obj);
}

void TVirtualThreeHeadedDragonHost::DrawDragonsCards(YGameClient& client, VirtualThreeHeadedDragonGame& game) const
{
	// Send empty card for Tiger
	SendCardToClient(client, 0, EThreeHeadedDragonSide(EThreeHeadedDragonSide::Tiger)._to_index());

	uint32_t goldenDragonCard = game.DrawAndAddCard(EThreeHeadedDragonSide::GoldenDragon, 0);
	SendCardToClient(client, goldenDragonCard, EThreeHeadedDragonSide(EThreeHeadedDragonSide::GoldenDragon)._to_index());

	uint32_t redDragonCard = game.DrawAndAddCard(EThreeHeadedDragonSide::RedDragon, 1);
	SendCardToClient(client, redDragonCard, EThreeHeadedDragonSide(EThreeHeadedDragonSide::RedDragon)._to_index());

	uint32_t blackDragonCard = game.DrawAndAddCard(EThreeHeadedDragonSide::BlackDragon, 2);
	SendCardToClient(client, blackDragonCard, EThreeHeadedDragonSide(EThreeHeadedDragonSide::BlackDragon)._to_index());

	UpdateGamePhase(client, game, EDealerAssistPhase::Decision);
}

void TVirtualThreeHeadedDragonHost::DrawTigerCard(YGameClient& client, VirtualThreeHeadedDragonGame& game) const
{
	uint32_t tigerCard = game.DrawAndAddCard(EThreeHeadedDragonSide::Tiger, 3);
	std::vector<EThreeHeadedDragonWinner> winners = game.Evaluate();
	SendCardToClient(client, tigerCard, EThreeHeadedDragonSide(EThreeHeadedDragonSide::Tiger)._to_index(), winners);
}

void TVirtualThreeHeadedDragonHost::SendCardToClient(YGameClient& client, uint8_t card, uint8_t position, std::vector<EThreeHeadedDragonWinner> winners) const
{
	json obj(json::value_t::object);
	obj["name"] = "addNewCard";
	obj["card"] = card;
	obj["index"] = position;

	if (!winners.empty())
	{
		json winnersJson(json::value_t::array);
		for (const auto& winner : winners) winnersJson.push_back(winner._to_integral());
		obj["winners"] = std::move(winnersJson);
	}

	client.TriggerEvent(*GameStateChangedEvent, obj);
}

void TVirtualThreeHeadedDragonHost::UpdateGamePhase(YGameClient& client, threeheadeddragon::VirtualThreeHeadedDragonGame& game,
                                                    dealer_assist::EDealerAssistPhase newPhase) const
{
	json phaseChangeData;
	phaseChangeData["name"] = "phase";
	phaseChangeData["old"] = game.GamePhase._to_index();
	phaseChangeData["new"] = newPhase._to_index();
	client.TriggerEvent(*GameStateChangedEvent, phaseChangeData);

	game.GamePhase = newPhase;
}
