//
// Created by <PERSON><PERSON><PERSON> on 27. 9. 24.
//

#include "yserver/hosts/threeheadeddragon/TThreeHeadedDragonBets.h"

#include <format>

#include "YSharedTypes.h"
#include "hosts/threeheadeddragon/TThreeHeadedDragonGameLogic.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::threeheadeddragon;

DEFINE_LOG_CATEGORY(Log3HeadedDragonBets, "three-headed-dragon-bets")

static const std::array<uint32_t, EThreeHeadedDragonBetType::_size() + 1> WinMultipliers = {
	1,    // BET_GOLDEN_DRAGON
	1,    // BET_BLACK_DRAGON
	1,    // BET_RED_DRAGON
	1,    // MAGIC_TIE
	1    // 3_HEAD_DRAGON
};

// <---- BaccaratBetAmounts ---->

const std::map<EThreeHeadedDragonBetType, uint32_t>& ThreeHeadedDragonBetAmounts::Get() const
{
	return mBets;
}

uint64_t ThreeHeadedDragonBetAmounts::GetBetAmount(EThreeHeadedDragonBetType type) const
{
	auto bet = mBets.find(type);
	if (bet == mBets.end())
	{
		TLOG(Log3HeadedDragonBets, EVerbosity::Warning, "Couldn't find bet type %s", type._to_string());
		return 0;
	}
	return bet->second;
}

void ThreeHeadedDragonBetAmounts::Clear() noexcept
{
	mBets.clear();
}

json ThreeHeadedDragonBetAmounts::BetsAsJSON(uint32_t creditMultiplier) const noexcept
{
	json betTable(json::value_t::object);
	for (const auto& [betType, betAmount] : mBets) betTable[betType._to_string()] = betAmount / creditMultiplier;
	return betTable;
}

bool ThreeHeadedDragonBetAmounts::Empty() const noexcept
{
	return mBets.empty();
}

ThreeHeadedDragonBetAmounts ThreeHeadedDragonBetAmounts::FromJSON(const json& val, uint32_t credit_multiplier)
{
	ThreeHeadedDragonBetAmounts toReturn;

	const json& bets = val["bets"];

	for (auto bet = bets.begin(); bet != bets.end(); bet++)
	{
		auto typ = EThreeHeadedDragonBetType::_from_string_nothrow(bet.key().c_str());
		if (!typ)
			throw BetParseError("Unknown bet type '" + bet.key() + "'");

		const uint64_t amount = bet->get<uint64_t>() * credit_multiplier;
		if (amount)
		{
			toReturn.mBets.emplace(typ.value(), amount);
		}
	}

	return toReturn;
}

ThreeHeadedDragonBetAmounts ThreeHeadedDragonBetAmounts::FromUserJSON(const json& val, uint32_t credit_multiplier)
{
	const json& bets = val["bets"];
	if (bets.is_array())
		throw BetParseError("Invalid payload: Should be an array of bet objects!", bets);

	ThreeHeadedDragonBetAmounts toReturn;

	for (auto bet = bets.begin(); bet != bets.end(); bet++)
	{
		auto typ = EThreeHeadedDragonBetType::_from_string_nothrow(bet.key().c_str());
		if (!typ)
			throw BetParseError("Unknown bet type '" + bet.key() + "'");

		const uint64_t amount = bet->get<uint64_t>() * credit_multiplier;
		if (amount)
		{
			toReturn.mBets.emplace(typ.value(), amount);
		}
	}

	return toReturn;
}

TThreeHeadedDragonBets::TThreeHeadedDragonBets() {}

void TThreeHeadedDragonBets::ClearStakes()
{
	mStakes.clear();
}

int TThreeHeadedDragonBets::AddStake(const TThreeHeadedDragonStake& stake)
{
	if (!stake.IsValid())
		throw std::runtime_error("All chip values are 0!");

	mStakes.push_back(std::make_shared<TThreeHeadedDragonStake>(stake));
	return mStakes.size() - 1;
}

const std::vector<std::shared_ptr<TThreeHeadedDragonStake>>& TThreeHeadedDragonBets::GetAllStakes() const
{
	return mStakes;
}

std::vector<std::shared_ptr<TThreeHeadedDragonStake>> TThreeHeadedDragonBets::GetValidStakes() const
{
	std::vector<std::shared_ptr<TThreeHeadedDragonStake>> validStakes;
	std::copy_if(mStakes.begin(), mStakes.end(), std::back_inserter(validStakes),
	             [](const std::shared_ptr<TThreeHeadedDragonStake>& stake) { return stake && stake->IsValid(); });

	return validStakes;
}

std::shared_ptr<TThreeHeadedDragonStake> TThreeHeadedDragonBets::GetStake(uint32_t stakeID) const
{
	if (stakeID < mStakes.size())
		return mStakes[stakeID];

	return nullptr;
}

FBetVerifyResult TThreeHeadedDragonBets::VerifyBets(const ThreeHeadedDragonBetAmounts& bets, const FStakeInfo& stake, uint32_t numOfRounds,
                                                    const std::optional<ThreeHeadedDragonBetAmounts>& confirmedBets) const
{
	FBetVerifyResult ret;
	if (!stake.Stake)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::BadSetup);
		ret.Message = "Bad game setup.";
		return ret;
	}

	if (confirmedBets)
		VerifyBetRaise(bets, *confirmedBets, ret);

	if (ret.Violation.has_value())
		return ret;

	uint64_t totalBetAmount = 0;
	uint64_t totalBetCost = 0;
	std::array<uint64_t, EThreeHeadedDragonBetType::_size()> possibleWinAmounts;
	possibleWinAmounts.fill(0);

	for (const auto& [betType, amount] : bets.Get())
	{
		if (amount == 0)
			continue;

		const FieldLimit fieldLimit = stake.Stake->GetFieldLimit(betType) * stake.Multiplier;
		if (fieldLimit.Min > amount)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MinimumFieldLimit);
			ret.Message =
			  "Bet " + std::to_string(amount) + " on '" + betType._to_string() + "' not allowed: Violates limits (min " + std::to_string(fieldLimit.Min) + ")";
			return ret;
		}

		if (fieldLimit.Max && (fieldLimit.Max < amount))
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MaximumFieldLimit);
			ret.Message =
			  "Bet " + std::to_string(amount) + " on '" + betType._to_string() + "' not allowed: Violates limits (max " + std::to_string(fieldLimit.Max) + ")";
			return ret;
		}

		if (fieldLimit.Multiple > 1U && amount % fieldLimit.Multiple)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
			ret.Message =
			  "Bet " + std::to_string(amount) + " on '" + betType._to_string() + "' not allowed: Must be a multiple of " + std::to_string(fieldLimit.Multiple);
			return ret;
		}

		totalBetAmount += amount;
		totalBetCost += amount;

		// check what this does

		if (betType == EThreeHeadedDragonBetType::GoldenDragonMagicTie)
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType::SuitedTie) * amount;

		if (betType == EThreeHeadedDragonBetType::BlackDragonMagicTie)
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType::SuitedTie) * amount;

		if (betType == EThreeHeadedDragonBetType::RedDragonMagicTie)
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType::SuitedTie) * amount;

		if (betType == EThreeHeadedDragonBetType::ThreeHeadedDragon)
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType::BeatsKing) * amount;
		else
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID) * amount;
	}

	const uint64_t playboardmin = stake.Stake->PlayboardLimitMin * stake.Multiplier;
	if (totalBetAmount && playboardmin > totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMin);
		ret.Message = "Minimum total bet limit violated (min " + std::to_string(playboardmin) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	const uint64_t playboardmax = stake.Stake->PlayboardLimitMax * stake.Multiplier;
	if (playboardmax && playboardmax < totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMax);
		ret.Message = "Maximum total bet limit exceeded (max " + std::to_string(playboardmax) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	const uint64_t maxWin = *std::max_element(possibleWinAmounts.begin(), possibleWinAmounts.end());
	const uint64_t maxWinLimit = stake.Stake->MaxTableWinLimit * stake.Multiplier;
	if (maxWinLimit && (maxWinLimit < maxWin))
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::WinLimit);
		ret.Message = "Maximum table win limit exceeded (max " + std::to_string(maxWinLimit) + ", max possible win was " + std::to_string(maxWin) + ")";
		return ret;
	}

	ret.TotalBet = totalBetCost;
	return ret;
}

CalculateCardGameWinsResult TThreeHeadedDragonBets::CalculateWins(const ThreeHeadedDragonBetAmounts& bets, const FStakeInfo& stake,
                                                                  const ThreeHeadedDragonGameLogic& gameLogic) const noexcept
{
	CalculateCardGameWinsResult Result;

	std::vector<uint8_t> winners = gameLogic.GetWinners();

	if (winners.empty())
	{
		TLOG(Log3HeadedDragonBets, Info, "No winner");
		return Result;
	}

	if (bets.Empty())
	{
		TLOG(Log3HeadedDragonBets, Info, "Bets empty");
		return Result;
	}

	// Go through all bets and calculate wins
	for (const auto& bet : bets.Get())
	{
		const uint64_t betOnField = bet.second;
		if (!betOnField)
			continue;

		TLOG(Log3HeadedDragonBets, Info, "Index %i betOnField %u", bet.first._value, betOnField);

		uint64_t win = 0;
		double multiplier = 0;

		if (bet.first == EThreeHeadedDragonBetType::GoldenDragon && gameLogic.IsWinner(EThreeHeadedDragonSide::GoldenDragon))
		{
			multiplier = GetMultiplier(bet.first, stake.ID);
			win = betOnField + (betOnField * multiplier);    // bet + (win)
		}

		if (bet.first == EThreeHeadedDragonBetType::RedDragon && gameLogic.IsWinner(EThreeHeadedDragonSide::RedDragon))
		{
			multiplier = GetMultiplier(bet.first, stake.ID);
			win = betOnField + (betOnField * multiplier);    // bet + (win)
		}

		if (bet.first == EThreeHeadedDragonBetType::BlackDragon && gameLogic.IsWinner(EThreeHeadedDragonSide::BlackDragon))
		{
			multiplier = GetMultiplier(bet.first, stake.ID);
			win = betOnField + (betOnField * multiplier);    // bet + (win)
		}

		if (bet.first == EThreeHeadedDragonBetType::GoldenDragonMagicTie || bet.first == EThreeHeadedDragonBetType::BlackDragonMagicTie ||
		    bet.first == EThreeHeadedDragonBetType::RedDragonMagicTie)
		{
			CalculateMagicTie(betOnField, bet.first, stake, gameLogic, win);
		}

		if (bet.first == EThreeHeadedDragonBetType::ThreeHeadedDragon)
		{
			if (gameLogic.IsWinner(EThreeHeadedDragonSide::GoldenDragon) && gameLogic.IsWinner(EThreeHeadedDragonSide::BlackDragon) &&
			    gameLogic.IsWinner(EThreeHeadedDragonSide::RedDragon))
			{
				multiplier = GetMultiplier(bet.first, stake.ID);
				win = betOnField + (betOnField * multiplier);    // bet + (win)
			}
		}

		if (win)
		{
			TLOG(Log3HeadedDragonBets, EVerbosity::Info, "Bet win %d!\n", win);
			Result.WinningFields.emplace_back(bet.second, bet.first._to_string(), win);
			TLOG(Log3HeadedDragonBets, EVerbosity::Info, "Bet of %ld on field %s(%d, %d) wins %ld(%lux%d)!\n", bet.second, bet.first._to_string(), 0, bet.first._value,
			     win, bet.second, multiplier);

			Result.TotalWon += win;
		}
	}

	return Result;
}

void TThreeHeadedDragonBets::CalculateMagicTie(const uint64_t betOnField, const EThreeHeadedDragonBetType betType, const FStakeInfo& stake,
                                               const ThreeHeadedDragonGameLogic& gameLogic, uint64_t& win) const
{
	double multiplier = 0;
	auto side = betType == EThreeHeadedDragonBetType::GoldenDragonMagicTie ? EThreeHeadedDragonSide::GoldenDragon : EThreeHeadedDragonSide::RedDragon;
	if (gameLogic.IsMagicSuitedTie(side))
	{
		multiplier = GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::MagicTieSuited));
		TLOG(Log3HeadedDragonBets, EVerbosity::Info, std::format("MagicSuited - Multiplier {}", multiplier).c_str());
		win = betOnField + (betOnField * multiplier);    // bet + (win)
	}
	else if (gameLogic.IsMagicUnsuitedTie(side))
	{
		multiplier = GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::MagicTieUnsuited));
		TLOG(Log3HeadedDragonBets, EVerbosity::Info, std::format("MagicUnsuited - Multiplier {}", multiplier).c_str());
		win = betOnField + (betOnField * multiplier);    // bet + (win)
	}

	else if (gameLogic.IsSuitedTie(side))
	{
		multiplier = GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::SuitedTie));
		TLOG(Log3HeadedDragonBets, EVerbosity::Info, std::format("SuitedTie - Multiplier {}", multiplier).c_str());
		win = betOnField + (betOnField * multiplier);    // bet + (win)
	}

	else if (gameLogic.IsUnsuitedTie(side))
	{
		multiplier = GetMultiplier(betType, stake.ID, EThreeHeadedDragonMultiplierType(EThreeHeadedDragonMultiplierType::UnsuitedTie));
		TLOG(Log3HeadedDragonBets, EVerbosity::Info, std::format("UnsuitedTie - Multiplier {}", multiplier).c_str());
		win = betOnField + (betOnField * multiplier);    // bet + (win)
	}
}
void TThreeHeadedDragonBets::CalculateThreeHeadedDragonSidebetWin(uint64_t betOnField, EThreeHeadedDragonBetType betType, const FStakeInfo& stake,
                                                                  const ThreeHeadedDragonGameLogic& gameLogic, uint64_t& win) const
{
	if (betOnField && gameLogic.Is3HeadedDragonWin())
	{
		double multiplier = 0;

		const EThreeHeadedDragonSide side =
		  betType == EThreeHeadedDragonBetType::ThreeHeadedDragon ? EThreeHeadedDragonSide::GoldenDragon : EThreeHeadedDragonSide::BlackDragon;

		// From highest to lowest payout of sidebet
		const std::array<EThreeHeadedDragonMultiplierType, 9> threeHeadedDragonWinMultipliers =
		  (gameLogic.GetCardRule() == ECardRule::Asian) ?
		    std::array<EThreeHeadedDragonMultiplierType, 9> { EThreeHeadedDragonMultiplierType::BeatsQueen, EThreeHeadedDragonMultiplierType::BeatsJack,
			                                                  EThreeHeadedDragonMultiplierType::Beats10,    EThreeHeadedDragonMultiplierType::Beats9,
			                                                  EThreeHeadedDragonMultiplierType::Beats8,     EThreeHeadedDragonMultiplierType::Beats7,
			                                                  EThreeHeadedDragonMultiplierType::Beats6,     EThreeHeadedDragonMultiplierType::Beats5,
			                                                  EThreeHeadedDragonMultiplierType::Beats4 } :
		    std::array<EThreeHeadedDragonMultiplierType, 9> { EThreeHeadedDragonMultiplierType::BeatsKing, EThreeHeadedDragonMultiplierType::BeatsQueen,
			                                                  EThreeHeadedDragonMultiplierType::BeatsJack, EThreeHeadedDragonMultiplierType::Beats10,
			                                                  EThreeHeadedDragonMultiplierType::Beats9,    EThreeHeadedDragonMultiplierType::Beats8,
			                                                  EThreeHeadedDragonMultiplierType::Beats7,    EThreeHeadedDragonMultiplierType::Beats6,
			                                                  EThreeHeadedDragonMultiplierType::Beats5 };

		// Check if mystery win is won
		for (const auto& type : threeHeadedDragonWinMultipliers)
		{
			if (gameLogic.Is3HeadedDragonWinFor(side, type))
			{
				multiplier = GetMultiplier(EThreeHeadedDragonBetType::ThreeHeadedDragon, stake.ID, type);
				TLOG(Log3HeadedDragonBets, EVerbosity::Info, std::format("3HeadedDragonWin -{} - Multiplier {}", type._to_string(), multiplier).c_str());
				win = betOnField + (betOnField * multiplier);    // bet + (win)

				break;
			}
		}
	}
}

double TThreeHeadedDragonBets::GetMultiplier(EThreeHeadedDragonBetType type, int stakeID, EThreeHeadedDragonMultiplierType multiplierType) const
{
	if (stakeID >= 0 && stakeID < static_cast<int>(mStakes.size()))
	{
		auto find = mStakes[stakeID]->MultiplierOverrides.find(type._to_string());
		if (find != mStakes[stakeID]->MultiplierOverrides.end())
		{
			auto multiplier = find->second.GetMultiplier(multiplierType);
			if (multiplier)
			{
				return *multiplier;
			}
			return WinMultipliers[static_cast<int>(type)];
		}
	}

	return WinMultipliers[static_cast<int>(type)];
}

json TThreeHeadedDragonBets::StakeAsJson(const TThreeHeadedDragonStake& stake, uint32_t multiplier)
{
	json stakeObj(json::value_t::object);

	json Minimals(json::value_t::array);
	json Limits(json::value_t::array);
	json Multiples(json::value_t::array);
	for (EThreeHeadedDragonBetType bet_type : EThreeHeadedDragonBetType::_values())
	{
		const FieldLimit limit = stake.GetFieldLimit(bet_type) * multiplier;
		Minimals.push_back(limit.Min);
		Limits.push_back(limit.Max);
		Multiples.push_back(limit.Multiple);
	}
	stakeObj["betTypeMin"] = std::move(Minimals);
	stakeObj["betTypeMax"] = std::move(Limits);
	stakeObj["betTypeMultiples"] = std::move(Multiples);
	stakeObj["playboardMax"] = stake.PlayboardLimitMax * multiplier;
	stakeObj["playboardMin"] = stake.PlayboardLimitMin * multiplier;
	stakeObj["maxNumBets"] = stake.BetsCountMax;
	stakeObj["maxWin"] = stake.MaxTableWinLimit * multiplier;

	json multOverrides(json::value_t::object);
	for (const auto& p : stake.MultiplierOverrides) multOverrides[p.first] = p.second.ToJson();
	stakeObj["multiplierOverrides"] = std::move(multOverrides);

	json chips(json::value_t::array);
	for (uint64_t chipVal : stake.ChipValues) { chips.push_back((chipVal == MAX_BET_CHIP_VALUE ? chipVal : (chipVal * multiplier))); }
	stakeObj["chipValues"] = std::move(chips);

	return stakeObj;
}

void TThreeHeadedDragonBets::CheckBetSecurity(const TThreeHeadedDragonStake& stake, const ThreeHeadedDragonBetAmounts& bets, const uint32_t numOfRounds,
                                              FBetVerifyResult& result) const
{
	for (const auto& [betType, amount] : bets.Get())
	{
		auto security = stake.GetCardBetSecuritySettings(betType);
		if (!security.has_value() || amount == 0)
			continue;

		uint64_t totalAnteBet = bets.GetBetAmount(EThreeHeadedDragonBetType::BlackDragon) + bets.GetBetAmount(EThreeHeadedDragonBetType::RedDragon) +
		                        bets.GetBetAmount(EThreeHeadedDragonBetType::GoldenDragon);

		// Allow sidebet only with active ante bet
		if (security->bAllowOnlyWithAnte && totalAnteBet == 0)
		{
			result.Violation = EBetRuleViolation::SidebetWithoutAnte;
			result.Message = std::format("Bet type {} not allowed: Ante bet required", betType._to_string());
			return;
		}

		// Side bet can be only % of ante bet
		if (security->mMaxPercentOfAnte.has_value() && (amount * 100 / totalAnteBet) > *security->mMaxPercentOfAnte)
		{
			result.Violation = EBetRuleViolation::MaximumBetOfAnte;
			result.Message = std::format("Bet type {} not allowed: Exceeds {}% of ante {}", betType._to_string(), *security->mMaxPercentOfAnte, totalAnteBet);
			return;
		}

		// Disable bet type after certain number of rounds
		if (security->mDisableAfterNumOfBets && numOfRounds >= *security->mDisableAfterNumOfBets)
		{
			result.Violation = EBetRuleViolation::MaximumRoundsLimit;
			result.Message = std::format("Bet type {} not allowed: Maximum number of rounds reached {}", betType._to_string(), *security->mDisableAfterNumOfBets);
			return;
		}

		// After certain number of rounds, limit the bet amount
		if (security->mMaxBetAfterNumOfBets && numOfRounds <= security->mMaxBetAfterNumOfBets->first && amount > security->mMaxBetAfterNumOfBets->second)
		{
			result.Violation = EBetRuleViolation::MaximumFieldLimit;
			result.Message = std::format("Bet type {} not allowed: Exceeds {} after {} rounds", betType._to_string(), security->mMaxBetAfterNumOfBets->second,
			                             security->mMaxBetAfterNumOfBets->first);
			return;
		}

		// After certain number of rounds, limit the bet amount to % of ante
		if (security->mMaxBetPercentOfAnteAfterNumOfBets && numOfRounds <= security->mMaxBetPercentOfAnteAfterNumOfBets->first)
		{
			uint64_t percentage = amount * 100 / totalAnteBet;
			if (percentage > security->mMaxBetPercentOfAnteAfterNumOfBets->second)
			{
				result.Violation = EBetRuleViolation::MaximumFieldLimit;
				result.Message = std::format("Bet type {} not allowed: Exceeds {}% after {} rounds", betType._to_string(),
				                             security->mMaxBetPercentOfAnteAfterNumOfBets->second, security->mMaxBetPercentOfAnteAfterNumOfBets->first);
				return;
			}
		}
	}
}

void TThreeHeadedDragonBets::VerifyBetRaise(const ThreeHeadedDragonBetAmounts& currentBets, const ThreeHeadedDragonBetAmounts& confirmedBets,
                                            FBetVerifyResult& result) const
{
	auto checkRaise = [&](const EThreeHeadedDragonBetType betType) {
		uint64_t confirmedBet = confirmedBets.GetBetAmount(betType);
		if (confirmedBet > 0)
		{
			uint64_t currentBet = currentBets.GetBetAmount(betType);
			if (currentBet != confirmedBet * 2)
			{
				result.Violation = EBetRuleViolation::InvalidBetRaise;
				result.Message =
				  std::format("{} Raise should be double the previous bet amount. Previous bet: {}, Raise: {}", betType._to_string(), confirmedBet, currentBet);
			}
		}
	};

	checkRaise(EThreeHeadedDragonBetType::GoldenDragon);
	checkRaise(EThreeHeadedDragonBetType::RedDragon);
	checkRaise(EThreeHeadedDragonBetType::BlackDragon);
}