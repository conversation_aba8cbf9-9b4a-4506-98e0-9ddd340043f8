#include "roulette/TRouletteBets.h"

#include <algorithm>

#include "Cryptography.h"
#include "YUtils.h"

using namespace yserver::gamehost;
using namespace roulette;

static const std::array<uint32_t, EBetStyle::_size() + 1> WinMultipliers = {
	0,    // BET_NONE
	36,    // BET_PLAIN
	18,    // BET_CAVAL
	12,    // BET_TRASV
	9,    // BET_CARRE
	7,    // BET_FIFTH
	6,    // BET_SIXTH
	3,    // BET_HLINE
	3,    // BET_THIRD
	2,    // BET_COLOR
	2,    // BET_ODEVN
	2,    // BET_PHALF
	9,    // BET_LES_FIGURES
	81,    // BET_TOUT_VA
	12,    // BET_LE_FINALI_3
	9,    // BET_LE_FINALI_4
	7,    // BET_LE_FINALI_5
	145,    // BET_LE_FINALI_TOUT_VA_3
	81,    // BET_LE_FINALI_TOUT_VA_4
	50    // BET_LE_FINALI_TOUT_VA_5
};

FThunderRoundHistory::FThunderRoundHistory(uint16_t boardMultiplier, const std::map<uint16_t, uint16_t>& ThunderNumbers) :
    BoardMultiplier(boardMultiplier), ThunderNumbers(ThunderNumbers)
{
}

uint16_t FThunderRoundHistory::GetBoardMultiplier(const bool bStandardBoardMultiplier) const
{
	return bStandardBoardMultiplier ? 36 : BoardMultiplier;
}

void FThunderRoundHistory::SetBoardMultiplier(uint16_t baseMultiplier)
{
	BoardMultiplier = baseMultiplier;
}

const std::array<std::string, 3> rowNames = { "Top", "Middle", "Bottom" };
const std::array<std::string, 3> thirdSuffix = { "st", "nd", "rd" };
TRouletteBets::TRouletteBets(bool bDoubleZero) : bCreatedForDoubleZero(bDoubleZero)
{
	BettingTable[0][3] = bDoubleZero ? FBetPosition({ 37, 0 }) : FBetPosition(0);
	if (bDoubleZero)
	{
		BettingTable[0][1] = FBetPosition(37);
		BettingTable[0][5] = FBetPosition(0);
	}
	BettingTable[1][0] = bDoubleZero ? FBetPosition({ 0, 1, 2, 3, 37 }) : FBetPosition({ 0, 1, 2, 3 });
	BettingTable[1][1] = FBetPosition({ 3U, bDoubleZero ? 37U : 0U });
	BettingTable[1][2] = FBetPosition({ 2U, 3U, bDoubleZero ? 37U : 0U });
	BettingTable[1][3] = bDoubleZero ? FBetPosition({ 0, 2, 37 }) : FBetPosition({ 0, 2 });
	BettingTable[1][4] = FBetPosition({ 0, 1, 2 });
	BettingTable[1][5] = FBetPosition({ 0, 1 });
	BettingTable[1][6] = BettingTable[1][0];

	// All columns of full numbers
	for (uint32_t x = 2; x <= 24; x += 2)
	{
		uint32_t minNumberInCol = ((x - 2) / 2) * 3 + 1;
		for (uint32_t y = 0; y < 3; y++) BettingTable[x][1 + 2 * y] = FBetPosition(minNumberInCol + 2 - y);
		for (uint32_t y = 2; y <= 4; y += 2) BettingTable[x][y] = BettingTable[x][y - 1] | BettingTable[x][y + 1];
		BettingTable[x][0] = BettingTable[x][6] = FBetPosition({ minNumberInCol, minNumberInCol + 1, minNumberInCol + 2 });
	}

	// All columns in between
	for (uint32_t x = 3; x <= 24; x += 2)
	{
		// Union of neighboring hotspots
		for (uint32_t y = 0; y < 7; y++) BettingTable[x][y] = BettingTable[x - 1][y] | BettingTable[x + 1][y];
	}

	// 2 to 1's
	for (uint32_t y = 0; y < 3; y++)
	{
		std::set<uint32_t> numbers;
		for (uint32_t n = 0; n < 12; n++) numbers.insert(n * 3 + (3 - y));
		BettingTable[25][1 + 2 * y] = FBetPosition(numbers, EBetStyle::HorizontalLine);
		BettingTable[25][1 + 2 * y].SetOverrideName(rowNames[y] + "2to1");
	}

	// thirds
	for (uint32_t x = 5; x < 25; x += 8)
	{
		uint32_t start = ((x - 5) / 8) + 1;
		std::set<uint32_t> numbers;
		for (uint32_t n = (start - 1) * 12 + 1; n <= start * 12; n++) numbers.insert(n);
		BettingTable[x][8] = FBetPosition(numbers, EBetStyle::Third);
		BettingTable[x][8].SetOverrideName(std::to_string(start) + thirdSuffix[start - 1] + " 12");
	}

	// black
	BettingTable[11][7] = FBetPosition({ 2, 4, 6, 8, 10, 11, 13, 15, 17, 20, 22, 24, 26, 28, 29, 31, 33, 35 }, EBetStyle::Color);
	BettingTable[11][7].SetOverrideName("Black");
	// red
	BettingTable[15][7] = FBetPosition({ 1, 3, 5, 7, 9, 12, 14, 16, 18, 19, 21, 23, 25, 27, 30, 32, 34, 36 }, EBetStyle::Color);
	BettingTable[15][7].SetOverrideName("Red");

	// 1 to 18 and 19 to 36
	for (uint32_t half = 0; half < 2; half++)
	{
		std::set<uint32_t> numbers;
		for (uint32_t n = half * 18 + 1; n <= (half + 1) * 18; n++) numbers.insert(n);
		BettingTable[3 + 20 * half][7] = FBetPosition(numbers, EBetStyle::Half);
		BettingTable[3 + 20 * half][7].SetOverrideName(half ? "19-36" : "1-18");
	}

	// odd and even
	for (uint32_t even = 0; even < 2; even++)
	{
		std::set<uint32_t> numbers;
		for (uint32_t n = even + 1; n <= 36; n += 2) numbers.insert(n);
		BettingTable[19 - even * 12][7] = FBetPosition(numbers, EBetStyle::OddEven);
		BettingTable[19 - even * 12][7].SetOverrideName(even ? "Even" : "Odd");
	}

	// Track
	std::vector<uint32_t> TrackLayout;
	if (bDoubleZero)
	{
		TrackLayout = { 0, 28, 9, 26, 30, 11, 7, 20, 32, 17, 5, 22, 34, 15, 3, 24, 36, 13, 1, 37, 27, 10, 25, 29, 12, 8, 19, 31, 18, 6, 21, 33, 16, 4, 23, 35, 14, 2, 0 };
	}
	else
	{
		TrackLayout = { 0, 32, 15, 19, 4, 21, 2, 25, 17, 34, 6, 27, 13, 36, 11, 30, 8, 23, 10, 5, 24, 16, 33, 1, 20, 14, 31, 9, 22, 18, 29, 7, 28, 12, 35, 3, 26, 0 };
	}

	constexpr size_t COLS_AVAILABLE_FOR_TRACK = BETS_TABLE_COLS - 2 - 2;    // two columns are for les figures, two more for Le Finali
	for (uint32_t i = 0; i < TrackLayout.size() - 1; i++)
		BettingTable[i % COLS_AVAILABLE_FOR_TRACK][9 + i / COLS_AVAILABLE_FOR_TRACK] = FBetPosition({ TrackLayout[i], TrackLayout[i + 1] });

	static const std::vector<std::set<uint32_t>> LesFiguresSpots = { { 1, 10, 19, 28 }, { 2, 11, 20, 29 }, { 3, 12, 21, 30 }, { 4, 13, 22, 31 }, { 5, 14, 23, 32 },
		                                                             { 6, 15, 24, 33 }, { 7, 16, 25, 34 }, { 8, 17, 26, 35 }, { 9, 18, 27, 36 } };
	if (LesFiguresSpots.size() > BETS_TABLE_ROWS)
		throw std::runtime_error("Cannot support more than " + std::to_string(BETS_TABLE_ROWS) + " les figures!");

	for (size_t les_figure = 0; les_figure < LesFiguresSpots.size(); les_figure++)
	{
		BettingTable[26][les_figure] = FBetPosition(LesFiguresSpots[les_figure], EBetStyle::LesFigures);
		BettingTable[27][les_figure] = FBetPosition(LesFiguresSpots[les_figure], EBetStyle::ToutVa);
	}

	std::vector<std::set<uint32_t>> LeFinaliSpots = { { 0, 10, 20, 30 }, { 1, 11, 21, 31 }, { 2, 12, 22, 32 }, { 3, 13, 23, 33 }, { 4, 14, 24, 34 },
		                                              { 5, 15, 25, 35 }, { 6, 16, 26, 36 }, { 7, 17, 27 },     { 8, 18, 28 },     { 9, 19, 29 } };
	if (bDoubleZero)
		LeFinaliSpots[0].insert(37);    // add double zero

	if (LesFiguresSpots.size() > BETS_TABLE_ROWS)
		throw std::runtime_error("Cannot support more than " + std::to_string(BETS_TABLE_ROWS) + " le finali!");

	for (size_t le_finali = 0; le_finali < LeFinaliSpots.size(); le_finali++)
	{
		if (LeFinaliSpots[le_finali].size() < 3 || LeFinaliSpots[le_finali].size() > 5)
			throw std::runtime_error("Le finali spots with less than 3 or more than 5 numbers are invalid!");

		BettingTable[28][le_finali] =
		  FBetPosition(LeFinaliSpots[le_finali], EBetStyle::_from_index(EBetStyle(EBetStyle::LeFinali3)._to_index() + (LeFinaliSpots[le_finali].size() - 3)));
		BettingTable[29][le_finali] =
		  FBetPosition(LeFinaliSpots[le_finali], EBetStyle::_from_index(EBetStyle(EBetStyle::LeFinaliToutVa3)._to_index() + (LeFinaliSpots[le_finali].size() - 3)));
	}

	if (bDoubleZero)
	{
		// according to the italians, no presets in double zero roulette
	}
	else
	{
		BetPresetFields.emplace(EBetPresetType::NassaBet, BetPresetField({ { coordOfBoardCaval(0, 3), 1 },
		                                                                   { coordOfBoardCaval(12, 15), 1 },
		                                                                   { coordOfPlainNumber(19), 1 },
		                                                                   { coordOfPlainNumber(26), 1 },
		                                                                   { coordOfBoardCaval(32, 35), 1 } }));
		BetPresetFields.emplace(
		  EBetPresetType::ZeroGame,
		  BetPresetField({ { coordOfBoardCaval(0, 3), 1 }, { coordOfBoardCaval(12, 15), 1 }, { coordOfPlainNumber(26), 1 }, { coordOfBoardCaval(32, 35), 1 } }));

		BetPresetFields.emplace(EBetPresetType::BigSeries, BetPresetField({ { sVector2D(1, 2), 2 } /* 3-way bet on 0,2,3 */,
		                                                                    { coordOfBoardCaval(4, 7), 1 },
		                                                                    { coordOfBoardCaval(12, 15), 1 },
		                                                                    { coordOfBoardCaval(18, 21), 1 },
		                                                                    { coordOfBoardCaval(19, 22), 1 },
		                                                                    { coordOfCorner({ 26, 29, 25, 28 }), 2 },
		                                                                    { coordOfBoardCaval(32, 35), 1 } }));

		BetPresetFields.emplace(EBetPresetType::Orphans, BetPresetField({ { coordOfPlainNumber(1), 1 },
		                                                                  { coordOfBoardCaval(6, 9), 1 },
		                                                                  { coordOfBoardCaval(14, 17), 1 },
		                                                                  { coordOfBoardCaval(17, 20), 1 },
		                                                                  { coordOfBoardCaval(31, 34), 1 } }));

		BetPresetFields.emplace(EBetPresetType::Serie58, BetPresetField({ { coordOfBoardCaval(5, 8), 1 },
		                                                                  { coordOfBoardCaval(11, 10), 1 },
		                                                                  { coordOfBoardCaval(13, 16), 1 },
		                                                                  { coordOfBoardCaval(24, 23), 1 },
		                                                                  { coordOfBoardCaval(27, 30), 1 },
		                                                                  { coordOfBoardCaval(33, 36), 1 } }));
	}
}

int TRouletteBets::addStake(const TRouletteStake& stake, const bool anyGameWithFireballFee)
{
	if (!stake.IsValid())
		throw std::runtime_error("All chip values are 0!");

	for (const auto& p : stake.MultiplierOverrides)
	{
		bool bValid = true;
		switch (p.first)
		{
			case EBetStyle::ToutVa:
			case EBetStyle::LeFinaliToutVa4: bValid = p.second >= 81 && p.second <= (bCreatedForDoubleZero ? 86 : 85); break;
			case EBetStyle::LeFinaliToutVa3: bValid = p.second >= 145 && p.second <= 150; break;
			case EBetStyle::LeFinaliToutVa5: bValid = p.second >= 50 && p.second <= 57; break;
			default: throw std::runtime_error("Cannot override multiplier for bet type " + std::string(p.first._to_string()));
		}

		if (!bValid)
			throw std::runtime_error("Multiplier for bet type " + std::string(p.first._to_string()) + " is outside the allowed range!");
	}

	if (anyGameWithFireballFee)
		validateChipValuesForFireballFee(stake);

	Stakes.push_back(std::make_shared<TRouletteStake>(stake));
	return Stakes.size() - 1;
}

void TRouletteBets::validateChipValuesForFireballFee(const TRouletteStake& stake)
{
	if (std::ranges::any_of(stake.ChipValues, [](int chipValue) { return chipValue % 10 != 0; }))
		throw std::runtime_error("Stake chip values must be multiples of 10 when fireball fee is enabled");
}

void TRouletteBets::clearStakes()
{
	Stakes.clear();
}

const std::vector<std::shared_ptr<const TRouletteStake>>& TRouletteBets::getAllStakes() const
{
	return Stakes;
}

FBetVerifyResult TRouletteBets::verifyBets(BetAmounts& Bets, const FStakeInfo& Stake, EPlayboardMode mode, const CalculateWinsResult& prevWins,
                                           const double fireballFee) const
{
	FBetVerifyResult ret;
	if (!Stake.Stake)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::BadSetup);
		ret.Message = "Bad game setup.";
		return ret;
	}

	unsigned int numBets = 0;
	uint64_t totalBetAmount = 0;
	uint64_t totalBetCost = 0;
	uint64_t totalFireballFeeBase = 0;
	std::array<uint64_t, 38> possibleWinAmounts;
	possibleWinAmounts.fill(0);
	for (uint32_t x = 0; x < BETS_TABLE_COLS; x++)
	{
		for (uint32_t y = 0; y < BETS_TABLE_ROWS; y++)
		{
			const uint64_t ThisBet = Bets.Get(x, y);
			if (ThisBet == 0)
				continue;


			if (!FBetPosition::IsValidFor(BettingTable[x][y].Type, mode))
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::InvalidBetField);
				ret.Message = std::format("Bet at ({}, {}) is invalid for mode {} - not a valid bet field", x, y, mode._to_string());
				ret.ErrorX = x;
				ret.ErrorY = y;
				return ret;
			}

			const EBetStyle ThisType = BettingTable[x][y].Type;

			const TRouletteStake::FFieldLimit fieldLimit = Stake.Stake->GetFieldLimit(ThisType) * Stake.Multiplier;
			if (fieldLimit.Min > ThisBet)
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::MinimumFieldLimit);
				ret.Message = std::format("Bet {} on '{}' not allowed: Violates limits (min {})", ThisBet, BettingTable[x][y].Name, fieldLimit.Min);
				ret.ErrorX = x;
				ret.ErrorY = y;
				return ret;
			}

			if (fieldLimit.Max && (fieldLimit.Max < ThisBet))
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::MaximumFieldLimit);
				ret.Message = std::format("Bet {} on '{}' not allowed: Violates limits (max {})", ThisBet, BettingTable[x][y].Name, fieldLimit.Max);
				ret.ErrorX = x;
				ret.ErrorY = y;
				return ret;
			}

			if (fieldLimit.Multiple > 1U && ThisBet % fieldLimit.Multiple)
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
				ret.Message = std::format("Bet {} on '{}' not allowed: Must be a multiple of {}", ThisBet, BettingTable[x][y].Name, fieldLimit.Multiple);
				ret.ErrorX = x;
				ret.ErrorY = y;
				return ret;
			}

			uint64_t BetCost = ThisBet;
			if (ThisType == EBetStyle::ToutVa)
			{
				// Multiply bet on Tout Va by the win multiplier of Les Figures, that's how much it costs to bet here!
				BetCost *= getMultiplier(Stake.Stake, EBetStyle(EBetStyle::LesFigures));

				if (!prevWins.TotalWon || prevWins.WonLesFigures(y) != BetCost)
				{
					ret.Violation = EBetRuleViolation(EBetRuleViolation::ToutVaLocked);
					ret.Message = std::format("Bet at ({}, {}) is invalid: Les Figures on this field was not won last round or was won with a different bet", x, y);
					ret.ErrorX = x;
					ret.ErrorY = y;
					return ret;
				}
			}
			else if (ThisType >= EBetStyle::LeFinaliToutVa3 && ThisType <= EBetStyle::LeFinaliToutVa5)
			{
				// Multiply bet on Tout Va by the win multiplier of Le Finali, that's how much it costs to bet here!
				BetCost *= getMultiplier(Stake.Stake, EBetStyle::_from_index(ThisType._to_index() - 3));

				if (!prevWins.TotalWon || prevWins.WonLeFinali(y) != BetCost)
				{
					ret.Violation = EBetRuleViolation(EBetRuleViolation::ToutVaLocked);
					ret.Message = std::format("Bet at ({}, {}) is invalid: Le Finali on this field was not won last round or was won with a different bet", x, y);
					ret.ErrorX = x;
					ret.ErrorY = y;
					return ret;
				}
			}

			// If fireball fee is enabled for game and for this bet and we are on straight/split, add the fee to the cost
			if ((ThisType == EBetStyle::Plain || ThisType == EBetStyle::Caval) && Bets.HasFireballFee() && fireballFee > 0)
				totalFireballFeeBase += BetCost;

			numBets++;

			if (Stake.Stake->BetsCountMax && numBets > Stake.Stake->BetsCountMax)
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::ToutVaLocked);
				ret.Message = std::format("Maximum number of bets exceeded ({})", Stake.Stake->BetsCountMax);
				return ret;
			}

			totalBetAmount += ThisBet;
			totalBetCost += BetCost;

			// TODO: getMultiplier method here does not consider thunderRNG. Is it even possible to consider thunderRNG here?
			for (uint32_t winNum : BettingTable[x][y].Numbers()) possibleWinAmounts[winNum] += getMultiplier(Stake.Stake, BettingTable[x][y]) * ThisBet;
		}
	}

	const uint64_t totalFireballFeeCost = static_cast<uint64_t>(std::round(totalFireballFeeBase * fireballFee));
	// Check if the rounding was OK so that we can add it to the total bet cost.
	// We already have validated the fireballFee that it is a multiple of 0.1.
	if (totalFireballFeeBase % 10 && totalFireballFeeCost > 0)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
		ret.Message = "Total straight/split bet cost (before adding fee) must be divisible by 10 when fireball fee is enabled";
		return ret;
	}
	totalBetCost += totalFireballFeeCost;

	const uint64_t playboardmin = Stake.Stake->PlayboardLimitMin * Stake.Multiplier;
	if (totalBetAmount && playboardmin > totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMin);
		ret.Message = std::format("Minimum total bet limit violated (min {}, total bet was {})", playboardmin, totalBetAmount);
		return ret;
	}

	const uint64_t playboardmax = Stake.Stake->PlayboardLimitMax * Stake.Multiplier;
	if (playboardmax && playboardmax < totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMax);
		ret.Message = std::format("Maximum total bet limit exceeded (max {}, total bet was {})", playboardmax, totalBetAmount);
		return ret;
	}

	const uint64_t maxWin = *std::max_element(possibleWinAmounts.begin(), possibleWinAmounts.end());
	const uint64_t maxWinLimit = Stake.Stake->MaxTableWinLimit * Stake.Multiplier;
	if (maxWinLimit && (maxWinLimit < maxWin))
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::WinLimit);
		ret.Message = std::format("Maximum table win limit exceeded (max {}, max possible win was {})", maxWinLimit, maxWin);
		return ret;
	}

	ret.TotalBet = totalBetCost;
	return ret;
}

CalculateWinsResult TRouletteBets::calculateWins(const BetAmounts& Bets, const FStakeInfo& Stake, uint32_t WinNumber, const ThunderRNG* thunderRNG,
                                                 const std::optional<FThunderRoundHistory>& fromHistory, const EThunderPaytableType thunderPaytableType) const noexcept
{
	CalculateWinsResult Result;

	if (WinNumber > (bCreatedForDoubleZero ? 37 : 36))
		return Result;

	if (Bets.Empty())
		return Result;

	for (uint32_t x = 0; x < BETS_TABLE_COLS; x++)
	{
		for (uint32_t y = 0; y < BETS_TABLE_ROWS; y++)
		{
			const uint64_t betOnField = Bets.Get(x, y);
			if (betOnField && BettingTable[x][y].Contains(WinNumber))
			{
				const uint64_t win = getMultiplier(Stake.Stake, BettingTable[x][y], thunderRNG, thunderPaytableType, fromHistory, WinNumber) * betOnField;

				if (win)
				{
					Result.WinningFields.emplace_back(x, y, BettingTable[x][y], win);
					/*printf("Bet of %ld on field %s(%d, %d) wins %ld(%lux%d)!\n", Bets.Bets[x][y], BettingTable[x][y].Name().c_str(), x, y, win, Bets.Bets[x][y],
					       getMultiplier(BettingTable[x][y].Type()));*/
					Result.TotalWon += win;
				}
			}
		}
	}
	return Result;
}

json TRouletteBets::betPositionsTable(EPlayboardMode mode) const
{
	static const json emptyBetFieldJson = FBetPosition().ToJSON();
	json betTable(json::value_t::array);
	for (const auto& betColumn : BettingTable)
	{
		json betColumnJson(json::value_t::array);
		for (const FBetPosition& betField : betColumn)
		{
			if (FBetPosition::IsValidFor(betField.Type, mode))
				betColumnJson.push_back(betField.ToJSON());
			else
				betColumnJson.push_back(emptyBetFieldJson);
		}
		betTable.push_back(std::move(betColumnJson));
	}
	return betTable;
}

json TRouletteBets::betPresets() const
{
	json presetList(json::value_t::object);
	for (const auto& preset : BetPresetFields)
	{
		json coveredBetFields(json::value_t::array);
		for (const auto& pos : preset.second)
		{
			json posObj(json::value_t::object);
			posObj["X"] = pos.first.X();
			posObj["Y"] = pos.first.Y();
			posObj["A"] = pos.second;
			coveredBetFields.push_back(std::move(posObj));
		}
		presetList[preset.first._to_string()] = std::move(coveredBetFields);
	}

	return presetList;
}

json TRouletteBets::stakeAsJson(const TRouletteStake& stake, uint32_t multiplier)
{
	json stakeObj(json::value_t::object);

	json Minimals(json::value_t::array);
	json Limits(json::value_t::array);
	json Multiples(json::value_t::array);
	for (EBetStyle bet_type : EBetStyle::_values())
	{
		if (bet_type != EBetStyle::None)
		{
			const TRouletteStake::FFieldLimit limit = stake.GetFieldLimit(bet_type) * multiplier;
			Minimals.push_back(limit.Min);
			Limits.push_back(limit.Max);
			Multiples.push_back(limit.Multiple);
		}
	}
	stakeObj["betTypeMin"] = std::move(Minimals);
	stakeObj["betTypeMax"] = std::move(Limits);
	stakeObj["betTypeMultiples"] = std::move(Multiples);
	stakeObj["playboardMax"] = stake.PlayboardLimitMax * multiplier;
	stakeObj["playboardMin"] = stake.PlayboardLimitMin * multiplier;
	stakeObj["maxNumBets"] = stake.BetsCountMax;
	stakeObj["maxWin"] = stake.MaxTableWinLimit * multiplier;

	json multOverrides(json::value_t::object);
	for (const auto& p : stake.MultiplierOverrides) multOverrides[p.first._to_string()] = p.second;
	stakeObj["multiplierOverrides"] = std::move(multOverrides);

	json chips(json::value_t::array);
	for (uint64_t chipVal : stake.ChipValues) { chips.push_back(chipVal == MAX_BET_CHIP_VALUE ? chipVal : (chipVal * multiplier)); }
	stakeObj["chipValues"] = std::move(chips);

	return stakeObj;
}

uint64_t TRouletteBets::amountOnPlainNumber(const BetAmounts& Bets, uint32_t Number) const noexcept
{
	const sVector2D coord = coordOfPlainNumber(Number);
	if (coord.isZero())
		return 0;

	return Bets.Get(coord.X(), coord.Y());
}

sVector2D TRouletteBets::coordOfPlainNumber(uint32_t Number) const
{
	if (Number > (bCreatedForDoubleZero ? 37 : 36))
		return sVector2D();

	if (Number == 0)
		return bCreatedForDoubleZero ? sVector2D(0, 5) : sVector2D(0, 3);

	if (Number == 37)
		return sVector2D(0, 1);

	const int triade = (Number - 1) / 3;
	int x = 2 + 2 * triade;
	int y = 1 + 2 * (3 * (triade + 1) - Number);
	return sVector2D(x, y);
}

sVector2D TRouletteBets::coordOfBoardCaval(uint32_t Number1, uint32_t Number2) const
{
	if (Number1 == Number2)
		return sVector2D();

	const std::set<uint32_t> nums({ Number1, Number2 });
	if (nums.contains(0) || nums.contains(37))
	{
		if (bCreatedForDoubleZero)
		{
			if (nums == std::set<uint32_t>({ 37, 3 }))
				return sVector2D(1, 1);
			if (nums == std::set<uint32_t>({ 0, 1 }))
				return sVector2D(1, 5);
		}
		else
		{
			if (nums == std::set<uint32_t>({ 0, 1 }))
				return sVector2D(1, 5);
			if (nums == std::set<uint32_t>({ 0, 2 }))
				return sVector2D(1, 3);
			if (nums == std::set<uint32_t>({ 0, 3 }))
				return sVector2D(1, 1);
		}
	}

	const iVector2D coord1(coordOfPlainNumber(Number1));
	const iVector2D coord2(coordOfPlainNumber(Number2));
	if (coord1.isZero() || coord2.isZero())
		return sVector2D();

	const iVector2D toNext = coord2 - coord1;

	if (toNext.isZero() || !toNext.isCoaxial(iVector2D()))
		return sVector2D();

	if ((toNext.X() + toNext.Y()) % 2)
		return sVector2D();

	return sVector2D(coord1 + toNext / 2);
}

sVector2D TRouletteBets::coordOfCorner(const std::set<uint32_t>& Numbers) const
{
	if (Numbers.size() != 4 || *Numbers.rbegin() > (bCreatedForDoubleZero ? 37 : 36))
		return sVector2D();

	sVector2D totalCoord;
	for (uint32_t Number : Numbers) totalCoord += coordOfPlainNumber(Number);

	if (totalCoord.X() % 4 || totalCoord.Y() % 4)
		return sVector2D();

	return totalCoord / 4;
}

FBetPosition TRouletteBets::getFirstBetPosition(const EBetStyle type) const
{
	for (const auto& betColumn : BettingTable)
		for (const FBetPosition& betField : betColumn)
			if (betField.Type == type)
				return betField;

	return FBetPosition();    // EBetStyle::None
}

uint32_t TRouletteBets::getMultiplier(const std::shared_ptr<const TRouletteStake>& stake, const FBetPosition& betPosition, const ThunderRNG* thunderRNG,
                                      const EThunderPaytableType thunderPaytableType, const std::optional<FThunderRoundHistory>& fromHistory,
                                      const std::optional<uint32_t> winNumber) const
{
	uint32_t win = WinMultipliers[static_cast<int>(betPosition.Type)];

	if (stake)
		if (const auto find = stake->MultiplierOverrides.find(betPosition.Type); find != stake->MultiplierOverrides.end())
			win = find->second;

	if (thunderRNG)
	{
		if (fromHistory)
			win = thunderRNG->GetWinMultiplier(betPosition, win, winNumber, thunderPaytableType, fromHistory->ThunderNumbers,
			                                   fromHistory->GetBoardMultiplier(thunderPaytableType != Fireball));
		else
			win = thunderRNG->GetWinMultiplier(betPosition, win, winNumber, thunderPaytableType);
	}

	return win;
}

uint32_t TRouletteBets::getMultiplier(const std::shared_ptr<const TRouletteStake>& stake, EBetStyle type, const ThunderRNG* thunderRNG,
                                      const EThunderPaytableType thunderPaytableType, const std::optional<FThunderRoundHistory>& fromHistory,
                                      const std::optional<uint32_t> winNumber) const
{
	return getMultiplier(stake, getFirstBetPosition(type), std::move(thunderRNG), thunderPaytableType, fromHistory, winNumber);
}

const std::map<EBetPresetType, BetPresetField>& TRouletteBets::getPresets() const
{
	return BetPresetFields;
}

void TRouletteBets::forAllBets(const BetAmounts& bets, const std::function<void(uint64_t, const FBetPosition&)>& worker) const
{
	if (bets.Empty())
		return;

	for (uint32_t x = 0; x < BETS_TABLE_COLS; x++)
	{
		for (uint32_t y = 0; y < BETS_TABLE_ROWS; y++)
		{
			if (const uint64_t bet = bets.Get(x, y))
				worker(bet, BettingTable[x][y]);
		}
	}
}

uint64_t TRouletteBets::amountOnBetStyles(const BetAmounts& bets, const std::set<EBetStyle>& betStyles) const
{
	uint64_t total = 0;
	forAllBets(bets, [&total, &betStyles](const uint64_t betOnField, const FBetPosition& position) {
		if (betStyles.contains(position.Type))
			total += betOnField;
	});
	return total;
};

bool CalculateWinsResult::HasAnyWinsAffectingNextRound() const
{
	return std::find_if(WinningFields.begin(), WinningFields.end(), [](const WonField& won) -> bool {
		       return won.Won && (won.Position.Type == EBetStyle::LesFigures || (won.Position.Type >= EBetStyle::LeFinali3 && won.Position.Type <= EBetStyle::LeFinali5));
	       }) != WinningFields.end();
}

uint64_t CalculateWinsResult::WonLesFigures(uint32_t y) const
{
	auto it = std::find_if(WinningFields.begin(), WinningFields.end(),
	                       [y](const WonField& won) -> bool { return won.Won && won.Y == y && won.Position.Type == EBetStyle::LesFigures; });
	return it == WinningFields.end() ? 0UL : it->Won;
}
uint64_t CalculateWinsResult::WonLeFinali(uint32_t y) const
{
	auto it = std::find_if(WinningFields.begin(), WinningFields.end(), [y](const WonField& won) -> bool {
		return won.Won && won.Y == y && won.Position.Type >= EBetStyle::LeFinali3 && won.Position.Type <= EBetStyle::LeFinali5;
	});
	return it == WinningFields.end() ? 0UL : it->Won;
}
