//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "hosts/dragontiger/TDragonTigerGame.h"

#include "TDealerGamesExtraData.h"

using namespace yserver::gamehost;
using namespace yserver::gamehost::dragontiger;

DEFINE_LOG_CATEGORY(LogDragonTigerGame, "dragon-tiger-game")

TDragonTigerGame::TDragonTigerGame(const std::string& host_uid, const GameInformation& info, const ECardRule cardRule) :
    TDealersGame(host_uid, info), mGameLogic(cardRule)
{
	mDragonTailLength = info.GetConfigOptional("dragonTailLength", 4).get<uint32_t>();
}

void TDragonTigerGame::SetCurrentStateOfCards(const json& cards)
{
	mGameLogic.SetCurrentStateOfCards(cards);
}

json TDragonTigerGame::GetCurrentStateOfCards() const
{
	return mGameLogic.GetCurrentStateOfCards();
}

void TDragonTigerGame::AddCard(const uint32_t card, const uint8_t position)
{
	switch (EDragonTigerDealingPhase::_from_integral(position))
	{
		case EDragonTigerDealingPhase::DragonCard: mGameLogic.AddOrReplaceCard(card, EDragonTigerSide::Dragon, 0); break;
		case EDragonTigerDealingPhase::TigerCard: mGameLogic.AddOrReplaceCard(card, EDragonTigerSide::Tiger, 0); break;

		default: break;
	}
}

uint8_t TDragonTigerGame::GetDealingPhase() const
{
	return mGameLogic.GetDealingPhase();
}

std::vector<uint8_t> TDragonTigerGame::Evaluate()
{
	return mGameLogic.Evaluate();
}

std::vector<uint8_t> TDragonTigerGame::GetWinners() const
{
	return mGameLogic.GetWinners();
}

ECardRule TDragonTigerGame::GetCardRule() const
{
	return mGameLogic.GetCardRule();
}

dealer_assist::GameRecordDto TDragonTigerGame::GetGameRecord() const
{
	return mGameLogic.GetGameRecord();
}

void TDragonTigerGame::FinishGameRound()
{
	mGameLogic.ClearGame();
}

const DragonTigerGameLogic& TDragonTigerGame::GetGameLogic() const
{
	return mGameLogic;
}

void TDragonTigerGame::Unregister(bool bClearWins)
{
	LastVerifiedBets.Clear();
	mConfirmedBets.Clear();
	ClearUncommittedBets();
	if (bClearWins)
		WonResult = {};
}

void TDragonTigerGame::ConfirmBets(const BetAmounts& placedBet)
{
	mConfirmedBets = placedBet;
}

BetAmounts TDragonTigerGame::GetConfirmedBets() const
{
	return mConfirmedBets;
}

void TDragonTigerGame::ResetConfirmedBets()
{
	mConfirmedBets.Clear();
}

bool TDragonTigerGame::IsLastCard()
{
	return mGameLogic.IsGameFinished();
}

uint32_t TDragonTigerGame::GenerateGoldenCard()
{
	return mGameLogic.GenerateGoldenCard();
}

std::optional<uint32_t> TDragonTigerGame::GetGoldenCard()
{
	return mGameLogic.GetGoldenCard();
}

bool TDragonTigerGame::ShouldDealtGoldenCard()
{
	return true;
}

uint32_t TDragonTigerGame::GetNumberOfCardsOnTable()
{
	return mGameLogic.GetNumberOfCardsOnTable();
}

bool TDragonTigerGame::IsPlayerDecisionNeeded()
{
	return mConfirmedBets.IsActiveOpenGameBet();
}

json TDragonTigerGame::GetGameExtraData() const
{
	json data(json::value_t::object);
	data["tailLengthAnimation"] = mDragonTailLength;
	return data;
}

json TDragonTigerGame::GetGameRoundData() const
{
	TDealerCardGameExtraDataDto data;

	if (mConfirmedBets.Empty())
		data.bets = LastVerifiedBets;
	else
	{
		data.bets = mConfirmedBets;

		if (!LastVerifiedBets.Empty() && IsOpenVersion())
			data.raise = LastVerifiedBets;
	}

	data.gameInfo = GameInfo;
	data.creditMultiplier = CreditMultiplier;
	data.CalculateGameWins = WonResult;

	if (StakeInfo.Stake)
	{
		data.chipValues = StakeInfo.Stake->ChipValues;
		data.stakeInfoID = StakeInfo.ID;
	}

	if (const auto winner = mGameLogic.GetWinner(); winner != EDragonTigerWinner::Null)
	{
		data.gameRecord = mGameLogic.GetGameRecord();
	}
	data.cards = mGameLogic.GetCards();

	TLOG(LogDragonTigerGame, EVerbosity::Info, "Returning game round data %s.", JsonSchema::PrintValueInline(data.AsJSON()).c_str());
	return data.AsJSON();
}
