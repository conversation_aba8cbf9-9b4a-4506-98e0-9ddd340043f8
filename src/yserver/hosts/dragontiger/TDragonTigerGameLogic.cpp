//
// Created by <PERSON><PERSON><PERSON> on 20. 8. 24.
//

#include "hosts/dragontiger/TDragonTigerGameLogic.h"

#include "Cryptography.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::dragontiger;

DEFINE_LOG_CATEGORY(LogDragonTigerGameLogic, "dragontiger-game-logic")

uint32_t DragonTigerGameSide::CardValue(const uint32_t card, const ECardRule cardRule)
{
	const uint32_t value = card % 100;
	return (cardRule == ECardRule::Western && value == 1) ? 14 : value;
}

uint32_t DragonTigerGameSide::HandValue(const ECardRule cardRule) const
{
	if (mHand.empty())
		return 0;

	return CardValue(mHand[0], cardRule);
}

DragonTigerGameLogic::DragonTigerGameLogic() : TBaseGameLogic(ECardRule::Asian)
{
	bShowCutCard = false;
	mGameSide = { DragonTigerGameSide(), DragonTigerGameSide() };
}

DragonTigerGameLogic::DragonTigerGameLogic(const ECardRule cardRule) : TBaseGameLogic(cardRule)
{
	bShowCutCard = false;
	mGameSide = { DragonTigerGameSide(), DragonTigerGameSide() };
}

DragonTigerGameLogic::DragonTigerGameLogic(const bool showCutCard, const ECardRule cardRule) : DragonTigerGameLogic(cardRule)
{
	bShowCutCard = showCutCard;
}

void DragonTigerGameLogic::ClearGame()
{
	ClearCards();

	mGoldenTieCard.reset();
	mCutCardPosition = -1;
}

void DragonTigerGameLogic::ClearCards()
{
	mGameSide[EDragonTigerSide::Dragon].ClearCards();
	mGameSide[EDragonTigerSide::Tiger].ClearCards();
	mWinner = EDragonTigerWinner::Null;
}

void DragonTigerGameLogic::AddCard(const uint32_t card, const uint8_t side)
{
	mGameSide[side].AddCard(card);
	TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "%s add card %i.", EDragonTigerSide::_from_integral(side)._to_string(), card);
}

void DragonTigerGameLogic::AddOrReplaceCard(const uint32_t card, const uint8_t side, const uint32_t position)
{
	TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "Add/change card %i at position %i on %s side.", card, position, EDragonTigerSide::_from_integral(side)._to_string());
	mGameSide[side].AddOrReplaceCard(card, position);
}

void DragonTigerGameLogic::AddCutCard(const uint32_t position)
{
	mCutCardPosition = position;
	TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "Added cut card at index %i.", position);
}

void DragonTigerGameLogic::ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards)
{
	for (const auto& card : cards)
	{
		switch (const auto dealingPhase = EDragonTigerDealingPhase::_from_integral(card.position))
		{
			case EDragonTigerDealingPhase::DragonCard: AddOrReplaceCard(card.newValue, EDragonTigerSide::Dragon, 0); break;
			case EDragonTigerDealingPhase::TigerCard: AddOrReplaceCard(card.newValue, EDragonTigerSide::Tiger, 0); break;

			default: break;
		}
	}
}

uint32_t DragonTigerGameLogic::GenerateGoldenCard()
{
	mGoldenTieCard = crypto::GetRandomInRange(1, 13);
	return mGoldenTieCard.value();
}

std::optional<uint32_t> DragonTigerGameLogic::GetGoldenCard() const
{
	return mGoldenTieCard;
}

std::optional<uint8_t> DragonTigerGameLogic::GetWinner() const
{
	return mWinner;
}

std::vector<uint8_t> DragonTigerGameLogic::GetWinners() const
{
	return { mWinner };
}

uint8_t DragonTigerGameLogic::GetDealingPhase() const
{
	if (mGameSide[EDragonTigerSide::Dragon].NumberOfCards() == 0)
		return EDragonTigerDealingPhase::DragonCard;
	if (mGameSide[EDragonTigerSide::Tiger].NumberOfCards() == 0)
		return EDragonTigerDealingPhase::TigerCard;

	return EDragonTigerDealingPhase::Finished;
}

std::vector<uint8_t> DragonTigerGameLogic::Evaluate()
{
	if (!mGameSide[EDragonTigerSide::Dragon].CardAt(0).has_value() || !mGameSide[EDragonTigerSide::Tiger].CardAt(0).has_value())
	{
		TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "Card does not exist!");
		return { EDragonTigerWinner::Null };
	}

	TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "Dragon value: %i | Tiger value %i.", mGameSide[EDragonTigerSide::Dragon].CardAt(0).value(),
	     mGameSide[EDragonTigerSide::Tiger].CardAt(0).value());

	if (mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule) > mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule))
	{
		mWinner = EDragonTigerWinner::DragonWin;
	}
	else if (mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule) > mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule))
	{
		mWinner = EDragonTigerWinner::TigerWin;
	}
	else
	{
		mWinner = EDragonTigerWinner::Tie;
	}

	TLOG(LogDragonTigerGameLogic, Info, "Game winner is %s", mWinner._to_string());

	return { mWinner };
}

uint8_t DragonTigerGameLogic::GetNumberOfCardsOnTable() const
{
	return mGameSide[EDragonTigerSide::Dragon].NumberOfCards() + mGameSide[EDragonTigerSide::Tiger].NumberOfCards();
}

bool DragonTigerGameLogic::IsUnsuitedTie() const
{
	return mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule) == mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);
}

bool DragonTigerGameLogic::IsSuitedTie() const
{
	const auto dragonCard = mGameSide[EDragonTigerSide::Dragon].CardAt(0);
	const auto tigerCard = mGameSide[EDragonTigerSide::Tiger].CardAt(0);

	if (dragonCard && tigerCard)
		return *dragonCard == *tigerCard;

	return false;
}

bool DragonTigerGameLogic::IsMagicSuitedTie() const
{
	const auto dragonCard = mGameSide[EDragonTigerSide::Dragon].CardAt(0);
	const auto tigerCard = mGameSide[EDragonTigerSide::Tiger].CardAt(0);

	if (tigerCard && dragonCard && mGoldenTieCard.has_value())
	{
		TLOG(LogDragonTigerGameLogic, EVerbosity::Info, "Dragon card %i, Tiger card %i, Golden card %i", *dragonCard, *tigerCard, mGoldenTieCard.value());
		return *dragonCard == *tigerCard && (*tigerCard % 100) == (mGoldenTieCard.value() % 100);
	}

	return false;
}

bool DragonTigerGameLogic::IsMagicUnsuitedTie() const
{
	const auto dragonCard = mGameSide[EDragonTigerSide::Dragon].CardAt(0);
	const auto tigerCard = mGameSide[EDragonTigerSide::Tiger].CardAt(0);

	if (dragonCard && tigerCard && mGoldenTieCard.has_value())
	{
		const uint32_t dragonValue = *dragonCard % 100;
		const uint32_t tigerValue = *tigerCard % 100;
		const uint32_t goldenValue = mGoldenTieCard.value() % 100;
		return dragonValue == tigerValue && dragonValue == goldenValue;
	}

	return false;
}

bool DragonTigerGameLogic::IsMysteryWin(EDragonTigerBetMultiplierType type) const
{
	if (mWinner == EDragonTigerWinner::Null)
		return false;

	EDragonTigerSide losingSide = mWinner == EDragonTigerWinner::DragonWin ? EDragonTigerSide::Tiger : EDragonTigerSide::Dragon;

	switch (type)
	{
		case EDragonTigerBetMultiplierType::LosesTo9: return mGameSide[losingSide].HandValue(mCardRule) == 9;
		case EDragonTigerBetMultiplierType::LosesTo10: return mGameSide[losingSide].HandValue(mCardRule) == 10;
		case EDragonTigerBetMultiplierType::LosesToJack: return mGameSide[losingSide].HandValue(mCardRule) == 11;
		case EDragonTigerBetMultiplierType::LosesToQueen: return mGameSide[losingSide].HandValue(mCardRule) == 12;
		case EDragonTigerBetMultiplierType::LosesToKing: return mGameSide[losingSide].HandValue(mCardRule) == 13;
		default: return false;
	}
}

uint32_t DragonTigerGameLogic::GetHandValue(const uint8_t side) const
{
	return mGameSide[side].HandValue(mCardRule);
}

bool DragonTigerGameLogic::HasFaceDownCard(const EDragonTigerSide side) const
{
	return mGameSide[side].HasFaceDownCard();
}

bool DragonTigerGameLogic::HasFaceDownCard(const uint32_t side) const
{
	return mGameSide[side].HasFaceDownCard();
}

std::unordered_map<std::string, uint32_t> DragonTigerGameLogic::GetHandValues() const
{
	std::unordered_map<std::string, uint32_t> handValues;
	handValues[EDragonTigerSide(EDragonTigerSide::Dragon)._to_string()] = mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule);
	handValues[EDragonTigerSide(EDragonTigerSide::Tiger)._to_string()] = mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);

	return handValues;
}

void DragonTigerGameLogic::SetCurrentStateOfCards(const json& cards)
{
	mGameSide[EDragonTigerSide::Dragon].ClearCards();
	mGameSide[EDragonTigerSide::Tiger].ClearCards();

	for (const auto& card : cards)
	{
		const EDragonTigerDealingPhase phase = EDragonTigerDealingPhase::_from_integral(card["index"].get<int>());
		const uint32_t cardValue = card.at("card").get<uint32_t>();

		switch (phase)
		{
			case EDragonTigerDealingPhase::DragonCard: mGameSide[EDragonTigerSide::Dragon].AddCard(cardValue, 0); break;
			case EDragonTigerDealingPhase::TigerCard: mGameSide[EDragonTigerSide::Tiger].AddCard(cardValue, 0); break;
			default: throw std::invalid_argument("Invalid dealing phase");
		}
	}
}

json DragonTigerGameLogic::GetCurrentStateOfCards() const
{
	CardPositionDto<EDragonTigerDealingPhase> cards;

	auto addCard = [&](EDragonTigerSide side, int cardIndex, EDragonTigerDealingPhase phase, CardPositionDto<EDragonTigerDealingPhase>& data) {
		if (auto cardOpt = mGameSide[side].CardAt(cardIndex))
		{
			data.Cards.insert({ phase, cardOpt.value() });
		}
	};

	addCard(EDragonTigerSide::Dragon, 0, EDragonTigerDealingPhase::DragonCard, cards);
	addCard(EDragonTigerSide::Tiger, 0, EDragonTigerDealingPhase::TigerCard, cards);

	return cards.AsJSON();
}

json DragonTigerGameLogic::GetGameResult() const
{
	json result(json::value_t::object);
	json dragon(json::value_t::object);
	json tiger(json::value_t::object);

	dragon["sum"] = mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule);
	json dragonCards(json::value_t::array);

	if (mGameSide[EDragonTigerSide::Dragon].NumberOfCards() == 1)
		dragonCards.push_back(mGameSide[EDragonTigerSide::Dragon].CardAt(0).value());

	dragon["cards"] = std::move(dragonCards);

	tiger["sum"] = mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);
	json tigerCards(json::value_t::array);

	if (mGameSide[EDragonTigerSide::Tiger].NumberOfCards() == 1)
		tigerCards.push_back(mGameSide[EDragonTigerSide::Tiger].CardAt(0).value());

	tiger["cards"] = std::move(tigerCards);

	result["dragon"] = std::move(dragon);
	result["tiger"] = std::move(tiger);

	if (mGoldenTieCard.has_value())
		result["mysteryTieCard"] = *mGoldenTieCard;

	result["winner"] = mWinner;

	return result;
}

json DragonTigerGameLogic::GetGameRecordJson() const
{
	json val;
	val["winner"] = mWinner;

	if (mWinner == EDragonTigerWinner::DragonWin)
		val["handValue"] = mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule);
	else
		val["handValue"] = mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);

	val["cards"] = GetCurrentStateOfCards();
	return val;
}

dealer_assist::GameRecordDto DragonTigerGameLogic::GetGameRecord() const
{
	dealer_assist::GameRecordDto record;
	record.Winners = { mWinner };

	if (mWinner == EDragonTigerWinner::DragonWin)
		record.WinningHandValue = mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule);
	else
		record.WinningHandValue = mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);

	record.Cards = GetCurrentStateOfCards();
	record.HandValues = GetHandValues();
	return record;
}

json DragonTigerGameLogic::GetFreeHandGameResult(const bool showCardFaces) const
{
	json freeHandResult(json::value_t::object);
	freeHandResult["name"] = "freeGame";
	freeHandResult["handValue"] =
	  mWinner == EDragonTigerWinner::DragonWin ? mGameSide[EDragonTigerSide::Dragon].HandValue(mCardRule) : mGameSide[EDragonTigerSide::Tiger].HandValue(mCardRule);
	freeHandResult["winner"] = mWinner._to_string();

	json cards(json::value_t::array);

	if (std::optional<uint32_t> dragonCard = mGameSide[EDragonTigerSide::Dragon].CardAt(0))
	{
		json card(json::value_t::object);
		card["card"] = showCardFaces ? *dragonCard : 0;
		card["index"] = EDragonTigerDealingPhase(EDragonTigerDealingPhase::DragonCard)._to_integral();
		cards.push_back(card);
	}

	if (std::optional<uint32_t> tigerCard = mGameSide[EDragonTigerSide::Tiger].CardAt(0))
	{
		json card(json::value_t::object);
		card["card"] = showCardFaces ? *tigerCard : 0;
		card["index"] = EDragonTigerDealingPhase(EDragonTigerDealingPhase::TigerCard)._to_integral();
		cards.push_back(card);
	}

	freeHandResult["cards"] = cards;
	return freeHandResult;
}

std::vector<json> DragonTigerGameLogic::GetCards(bool showCardFaces) const
{
	std::vector<json> cards;

	auto createCardObject = [this](int index, EDragonTigerSide side, bool showCardFaces) -> json {
		json card(json::value_t::object);
		card["name"] = "addNewCard";
		card["index"] = index;

		std::optional<uint32_t> cardValue = mGameSide[side].CardAt(0);
		if (cardValue)
		{
			card["card"] = showCardFaces ? *cardValue : 0;
		}

		return card;
	};

	cards.push_back(createCardObject(0, EDragonTigerSide::Dragon, showCardFaces));
	cards.push_back(createCardObject(1, EDragonTigerSide::Tiger, showCardFaces));

	if (mCutCardPosition != -1 && bShowCutCard)
	{
		json cutCard(json::value_t::object);
		cutCard["name"] = "addNewCard";
		cutCard["index"] = mCutCardPosition;
		cutCard["card"] = -1;

		if (cards.size() >= 4)
		{
			cards.insert(cards.begin() + mCutCardPosition, cutCard);
		}
		else
		{
			cards.push_back(cutCard);
		}
	}

	if (mWinner != EDragonTigerWinner::Null)
		cards.back()["winner"] = mWinner._to_integral();

	return cards;
}

json DragonTigerGameLogic::GetCards() const
{
	CardPositionDto<EDragonTigerDealingPhase> cards;

	auto AddCard = [this](const EDragonTigerSide side, const int cardIndex, const EDragonTigerDealingPhase dealingPhase,
	                      CardPositionDto<EDragonTigerDealingPhase>& data) {
		if (auto cardOpt = mGameSide[side].CardAt(cardIndex))
		{
			data.Cards.insert({ dealingPhase, cardOpt.value() });
		}
	};

	AddCard(EDragonTigerSide::Dragon, 0, EDragonTigerDealingPhase(EDragonTigerDealingPhase::DragonCard), cards);
	AddCard(EDragonTigerSide::Tiger, 0, EDragonTigerDealingPhase(EDragonTigerDealingPhase::TigerCard), cards);

	return cards.AsJSON();
}

bool DragonTigerGameLogic::IsGameFinished() const
{
	if (HasFaceDownCard(EDragonTigerSide::Dragon) || HasFaceDownCard(EDragonTigerSide::Tiger))
		return false;

	if (GetDealingPhase() == EDragonTigerDealingPhase::Finished)
		return true;

	return false;
}
