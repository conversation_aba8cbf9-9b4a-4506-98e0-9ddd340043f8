//
// Created by <PERSON><PERSON><PERSON> on 30. 8. 24.
//

#include "hosts/dragontiger/TDragonTigerStake.h"

#include "MyUtils.h"
#include "YSharedTypes.h"

using namespace yserver::gamehost;
using namespace yserver::gamehost::dragontiger;

FieldLimit TDragonTigerStake::GetFieldLimit(const EDragonTigerBetType fieldType) const
{
	return mLimitsTable[fieldType._to_index()];
}

void TDragonTigerStake::SetCardBetSecuritySettings(const EDragonTigerBetType fieldType, const CardGameBetSecuritySettings& security)
{
	mSecurityManager.SetSecuritySettings(fieldType._to_string(), security);
}

std::optional<CardGameBetSecuritySettings> TDragonTigerStake::GetCardBetSecuritySettings(EDragonTigerBetType fieldType) const
{
	return mSecurityManager.GetSecuritySettings(fieldType._to_string());
}

bool TDragonTigerStake::IsValid() const
{
	// MysticWin is only valid for 0 decks - infinite shoe
	/* if (MultiplierOverrides.contains(EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string()))
	{
	    if (numOfDecks != 0)
	        return false;
	}
	*/

	for (const uint64_t& chipVal : ChipValues)
	{
		if (!IsMultiplierValid(chipVal))    // If chip is not divisible by any of the multipliers represented as fraction, it's invalid
			return false;
		if (chipVal)
			return true;
	}
	// if all the chip values are 0, it's an invalid stake for sure
	return false;
}

bool TDragonTigerStake::IsMultiplierValid(const uint64_t chipValue) const
{
	for (const auto& multiplier : MultiplierOverrides)
	{
		for (const auto& item : multiplier.second.Items)
		{
			if (item.second == 0)
				return false;
		}
	}

	return true;
}



const JsonSchema& TDragonTigerStake::StakeSchema()
{
	static const JsonSchema MagicTieMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::SuitedTie)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - suited", 12) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::UnsuitedTie)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 3) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicSuited)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 270) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicUnsuited)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 40) } });

	static const JsonSchema MysticWinMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesTo10)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesTo10", 4) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesToJack)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesToJack", 8) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesToQueen)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesToQueen", 40) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesTo9)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesTo9", 1) } });

	static const JsonSchema OpenWinMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Beats9)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - Beats9", 1) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Beats10)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - Beats10", 2) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::BeatsJack)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - BeatsJack", 3) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::BeatsQueen)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - BeatsQueen", 4) } });

	static const JsonSchema Schema(
	  { { "maxNumBets", JsonSchema(json::value_t::number_unsigned, "The maximum number of bet fields that can be bet in a single game (0 for unlimited)", 0) },
	    { "tableWinLimit", JsonSchema(json::value_t::number_unsigned, "The maximum number of credits that can be won in a single game (0 for unlimited)", 0) },
	    { "minBet", JsonSchema(json::value_t::number_unsigned, "The minimum total bet", 0) },
	    { "maxBet", JsonSchema(json::value_t::number_unsigned, "The maximum total bet (0 for unlimited)", 0) },
	    { "chipValues", JsonSchema(json::value_t::array, "The chip values in credits", json::array({ 1, 2, 5, 10, 20 }))
	                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The value of a chip in credits", 1), 5, 5) },
	    { "betTypes", JsonSchema({ { EDragonTigerBetType(EDragonTigerBetType::Dragon)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Dragon", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Dragon (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                            "Limits for bets on Dragon") },
	                               { EDragonTigerBetType(EDragonTigerBetType::Tiger)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Tiger", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Tiger (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                            "Limits for bets on Tiger") },
	                               { EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on MysticWin", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on MysticWin (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", MysticWinMultiplierSchema },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "security", CardGameBetSecuritySettings::SettingsSchema() } },
	                                            "Limits for bets on MysticWin") },
	                               { EDragonTigerBetType(EDragonTigerBetType::MagicTie)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Magic Tie", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Magic Tie (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", MagicTieMultiplierSchema },
	                                              { "security", CardGameBetSecuritySettings::SettingsSchema() } },
	                                            "Limits for bets on Magic Tie") } },
	                             "The configuration of the stake") } });

	return Schema;
}

const JsonSchema& TDragonTigerStake::OpenStakeSchema()
{
	static const JsonSchema MagicTieMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::SuitedTie)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - suited", 12) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::UnsuitedTie)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 3) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicSuited)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 270) },
	                                                   { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicUnsuited)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Magic Tie - unsuited", 40) } });

	static const JsonSchema MysticWinMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesTo10)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesTo10", 4) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesToJack)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesToJack", 8) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesToQueen)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesToQueen", 40) },
	                                                    { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesTo9)._to_string(),
	                                                      JsonSchema(json::value_t::number_unsigned, "Multiplier on MysticWin - LosesTo9", 1) } });

	static const JsonSchema OpenWinMultiplierSchema({ { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Beats9)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - Beats9", 1) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Beats10)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - Beats10", 2) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::BeatsJack)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - BeatsJack", 3) },
	                                                  { EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::BeatsQueen)._to_string(),
	                                                    JsonSchema(json::value_t::number_unsigned, "Multiplier on Open - BeatsQueen", 4) } });

	static const JsonSchema Schema(
	  { { "maxNumBets", JsonSchema(json::value_t::number_unsigned, "The maximum number of bet fields that can be bet in a single game (0 for unlimited)", 0) },
	    { "tableWinLimit", JsonSchema(json::value_t::number_unsigned, "The maximum number of credits that can be won in a single game (0 for unlimited)", 0) },
	    { "minBet", JsonSchema(json::value_t::number_unsigned, "The minimum total bet", 0) },
	    { "maxBet", JsonSchema(json::value_t::number_unsigned, "The maximum total bet (0 for unlimited)", 0) },
	    { "chipValues", JsonSchema(json::value_t::array, "The chip values in credits", json::array({ 1, 2, 5, 10, 20 }))
	                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The value of a chip in credits", 1), 5, 5) },
	    { "betTypes", JsonSchema({ { EDragonTigerBetType(EDragonTigerBetType::Dragon)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Dragon", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Dragon (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                            "Limits for bets on Dragon") },
	                               { EDragonTigerBetType(EDragonTigerBetType::Tiger)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Tiger", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Tiger (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                            "Limits for bets on Tiger") },
	                               { EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on MysticWin", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on MysticWin (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", MysticWinMultiplierSchema },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "security", CardGameBetSecuritySettings::SettingsSchema() } },
	                                            "Limits for bets on MysticWin") },
	                               { EDragonTigerBetType(EDragonTigerBetType::MagicTie)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Magic Tie", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Magic Tie (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", MagicTieMultiplierSchema },
	                                              { "security", CardGameBetSecuritySettings::SettingsSchema() } },
	                                            "Limits for bets on Magic Tie") },
	                               { EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OpenDragon", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OpenDragon (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", OpenWinMultiplierSchema } },
	                                            "Limits for bets on Open Dragon") },
	                               { EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OpenTiger", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OpenTiger (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", OpenWinMultiplierSchema } },
	                                            "Limits for bets on Open Tiger") } },
	                             "The configuration of the stake") } });

	return Schema;
}

void TDragonTigerStake::SetMaxBet(const EDragonTigerBetType fieldType, const uint64_t maxBet)
{
	mLimitsTable[fieldType._to_index()].Max = maxBet;
}

void TDragonTigerStake::SetMinBet(const EDragonTigerBetType fieldType, const uint64_t minBet)
{
	mLimitsTable[fieldType._to_index()].Min = minBet;
}

void TDragonTigerStake::SetFieldLimit(const EDragonTigerBetType fieldType, const FieldLimit& limit)
{
	mLimitsTable[fieldType._to_index()] = limit;
}

void TDragonTigerStake::SetMultiple(const EDragonTigerBetType fieldType, const uint64_t multiple)
{
	mLimitsTable[fieldType._to_index()].Multiple = multiple;
}

TDragonTigerStake::TDragonTigerStake() : TConfiguration(StakeSchema()), PlayboardLimitMin(0), PlayboardLimitMax(0), BetsCountMax(0), MaxTableWinLimit(0)
{
	ChipValues.fill(0);
}

void TDragonTigerStake::OnConfigLoaded(const std::filesystem::path& filename)
{
	BetsCountMax = GetConfig("maxNumBets").get<uint32_t>();
	MaxTableWinLimit = GetConfig("tableWinLimit").get<uint64_t>();
	PlayboardLimitMin = GetConfig("minBet").get<uint64_t>();
	PlayboardLimitMax = GetConfig("maxBet").get<uint64_t>();

	const json& chipVals = GetConfig("chipValues");
	for (uint i = 0; i < chipVals.size(); i++) ChipValues[i + 1] = chipVals[i].get<uint64_t>();

	const json& betTypes = GetConfig("betTypes");
	for (auto betType = betTypes.begin(); betType != betTypes.end(); ++betType)
	{
		uint64_t min = 0, max = 0, multiple = 0;
		bool enabled = true;

		if (!betType->is_object())
			throw std::runtime_error("Bet type object '" + betType.key() + "' is invalid - value needs to be an object containing 'min' and 'max' members");

		auto typ = EDragonTigerBetType::_from_string_nothrow(betType.key().c_str());
		if (!typ)
			throw std::runtime_error("Unknown bet type '" + betType.key() + "'");

		auto enabledJson = FindMember(*betType, "enabled");
		if (enabledJson && enabledJson->is_boolean())
			enabled = enabledJson->get<bool>();

		if (!enabled)
			continue;

		min = (*betType)["min"].get<uint64_t>();
		max = (*betType)["max"].get<uint64_t>();
		multiple = (*betType)["multiple"].get<uint64_t>();

		if (betType->contains("multiplier"))
		{
			const auto& multiplierValue = (*betType)["multiplier"];
			if (multiplierValue.is_number())
			{
				std::map<std::string, double> items;
				items[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = multiplierValue.get<double>();
				MultiplierOverrides[typ.value()._to_string()] = BetMultiplier(items);
			}
			else if (multiplierValue.is_object())
			{
				std::map<std::string, double> items;
				for (auto betMultiplierItem = multiplierValue.begin(); betMultiplierItem != multiplierValue.end(); ++betMultiplierItem)
				{
					auto multiplierTyp = EDragonTigerBetMultiplierType::_from_string_nothrow(betMultiplierItem.key().c_str());
					if (!multiplierTyp)
					{
						throw std::runtime_error(std::string("Unknown bet type '") + betMultiplierItem.key() + "'");
					}
					items[multiplierTyp.value()._to_string()] = betMultiplierItem.value().get<double>();
					MultiplierOverrides[typ.value()._to_string()] = BetMultiplier(items);
				}
			}
		}

		const json* securityJson = FindMember(*betType, "security");
		if (securityJson && !securityJson->is_null())
			SetCardBetSecuritySettings(*typ, CardGameBetSecuritySettings(*securityJson));

		SetMinBet(*typ, min);
		SetMaxBet(*typ, max);
		SetMultiple(*typ, multiple);
	}
}

json TDragonTigerStake::BetSecuritySettingsToJson() const
{
	return mSecurityManager.ToJson();
}

json TDragonTigerStake::MultiplierOverridesToJson() const
{
	json multipliers(json::value_t::object);
	for (const auto& multiplier : MultiplierOverrides) { multipliers[multiplier.first] = multiplier.second.ToJson(); }
	return multipliers;
}
