//
// Created by <PERSON><PERSON><PERSON> on 5. 01. 24.
//

#include "yserver/hosts/baccarat/TBaccaratGame.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;

DEFINE_LOG_CATEGORY(LogBaccaratGame, "baccarat-game")

TBaccaratGame::TBaccaratGame() : TBaseCardGame(std::string(), GameInformation())
{
	mCommission = false;
	bShowCardFaces = true;
	mGameLogic = BaccaratGameLogic(false);
}

TBaccaratGame::TBaccaratGame(const std::string& host_uid, const GameInformation& info) : TBaseCardGame(host_uid, info)
{
	mCommission = info.GetConfig("commission").get<bool>();
	bShowCardFaces = info.GetConfigOptional("free-hands.show-card-faces", true).get<bool>();

	mGameLogic = BaccaratGameLogic(false);
}

json TBaccaratGame::GetGameRoundData() const
{
	// TLOG(LogBaccaratGame, EVerbosity::Info, "GetGameRoundData");
	if (BetStatus() == EGameBetState::Idle)
		return {};

	json data = ConfirmedBets.BetsAsJSON(CreditMultiplier);
	data["multiplier"] = CreditMultiplier;

	if (StakeInfo.Stake)
	{
		json chipVals(json::value_t::array);
		for (uint64_t chipVal : StakeInfo.Stake->ChipValues) { chipVals.push_back(chipVal == MAX_BET_CHIP_VALUE ? chipVal : (chipVal * StakeInfo.Multiplier)); }
		data["chipValues"] = std::move(chipVals);
		data["stake"] = StakeInfo.ID;
	}
	else
		data["chipValues"] = json();

	if (mGameLogic.GetWinner() != EBaccaratWinner::Null)
	{
		data["result"] = mGameLogic.GetGameRecord().ToJSON();
	}

	data["cards"] = mGameLogic.GetCards();
	// TLOG(LogBaccaratGame, EVerbosity::Info, "GetGameRoundData is: %s", JsonSchema::PrintValueInline(data).c_str());
	return data;
}

const baccarat::BaccaratGameLogic& TBaccaratGame::GetGameLogic() const
{
	return mGameLogic;
}

dealer_assist::GameRecordDto TBaccaratGame::GetGameRecord() const
{
	return mGameLogic.GetGameRecord();
}

void TBaccaratGame::Unregister(const bool bClearWins)
{
	LastVerifiedBets.Clear();
	ConfirmedBets.Clear();
	ClearUncommittedBets();
	if (bClearWins)
		WonResult = {};
}

void TBaccaratGame::AddCard(uint32_t card)
{
	switch (GetDealingPhase())
	{
		case EBaccaratDealingPhase::PlayerFirstCard:
		case EBaccaratDealingPhase::PlayerSecondCard:
		case EBaccaratDealingPhase::PlayerThirdCard: mGameLogic.AddCard(card, EBaccaratSide::Player); break;
		case EBaccaratDealingPhase::BankerFirstCard:
		case EBaccaratDealingPhase::BankerSecondCard:
		case EBaccaratDealingPhase::BankerThirdCard: mGameLogic.AddCard(card, EBaccaratSide::Banker); break;
		default:
			// Handle unexpected phase
			// throw std::runtime_error("Unexpected dealing phase");
			break;
	}
}

void TBaccaratGame::AddCutCard(uint32_t position)
{
	mGameLogic.AddCutCard(position);
}

uint8_t TBaccaratGame::Evaluate()
{
	const auto winners = mGameLogic.Evaluate();
	if (winners.empty())
		return EBaccaratWinner::Null;

	return winners.front();
}

uint8_t TBaccaratGame::GetDealingPhase() const
{
	return mGameLogic.GetDealingPhase();
}

json TBaccaratGame::GetGameResult() const
{
	return mGameLogic.GetGameResult();
}

json TBaccaratGame::GetCurrentStateOfCards() const
{
	return mGameLogic.GetCurrentStateOfCards();
}

void TBaccaratGame::SetCurrentStateOfCards(const json& cards)
{
	mGameLogic.SetCurrentStateOfCards(cards);
}

void TBaccaratGame::ClearCards()
{
	mGameLogic.ClearCards();
}

uint8_t TBaccaratGame::GetWinner() const
{
	if (auto winner = mGameLogic.GetWinner())
		return winner.value();

	return EBaccaratWinner::Null;
}

void TBaccaratGame::FinishGameRound()
{
	mGameLogic.ClearGame();
}

bool TBaccaratGame::ShouldPlayerDrawThirdCard() const
{
	return mGameLogic.ShouldPlayerDrawThirdCard();
}

bool TBaccaratGame::ShouldBankerDrawThirdCard() const
{
	return mGameLogic.ShouldBankerDrawThirdCard();
}

bool TBaccaratGame::IsCommission() const
{
	return mCommission;
}

EBaccaratPlayboardMode TBaccaratGame::GetPlayboardMode() const
{
	return mCommission ? EBaccaratPlayboardMode::Commission : EBaccaratPlayboardMode::NoCommission;
}
