#include "hosts/baccarat/TBaccaratGameLogic.h"

#include "Cryptography.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;
using namespace dealer_assist;

DEFINE_LOG_CATEGORY(LogBaccaratGameLogic, "baccarat-game-logic")

uint32_t BaccaratGameSide::HandValue() const
{
	uint32_t sum = 0;
	for (uint32_t card : mHand) { sum += CardValue(card); }
	return sum % 10;
}

uint32_t BaccaratGameSide::CardValue(const uint32_t card)
{
	uint32_t value = card % 100;

	// 10, Jack, <PERSON>, <PERSON> are 0
	// 1-9 as-is
	return value > 9 ? 0 : value;
}

// < ---- BaccaratGameLogic ---->

BaccaratGameLogic::BaccaratGameLogic() : TBaseGameLogic(ECardRule::Asian)
{
	bShowCutCard = false;
	mGameSide = { BaccaratGameSide(), BaccaratGameSide() };
}

BaccaratGameLogic::BaccaratGameLogic(const ECardRule cardRule) : TBaseGameLogic(cardRule)
{
	bShowCutCard = false;
	mGameSide = { BaccaratGameSide(), BaccaratGameSide() };
}

BaccaratGameLogic::BaccaratGameLogic(const bool showCutCard, const ECardRule cardRule = ECardRule::Asian) : BaccaratGameLogic(cardRule)
{
	bShowCutCard = showCutCard;
}

void BaccaratGameLogic::ClearGame()
{
	logs::Logger::Log(LogBaccaratGameLogic, Info, "Game cleared");

	ClearCards();

	mCutCardPosition = -1;
	mGoldenTieCard.reset();
}

void BaccaratGameLogic::ClearCards()
{
	Logger::Log(LogBaccaratGameLogic, Info, "Clearing cards");
	mGameSide[EBaccaratSide::Banker].ClearCards();
	mGameSide[EBaccaratSide::Player].ClearCards();

	mWinner = EBaccaratWinner::Null;
}

void BaccaratGameLogic::AddCard(const uint32_t card, const uint8_t side)
{
	mGameSide[side].AddCard(card);
	TLOG(LogBaccaratGameLogic, EVerbosity::Info, "%s add card %i.", EBaccaratSide::_from_integral(side)._to_string(), card);
}

void BaccaratGameLogic::AddCutCard(uint32_t position)
{
	mCutCardPosition = position;
	TLOG(LogBaccaratGameLogic, EVerbosity::Info, "Added cut card at index %i.", position);
}

void BaccaratGameLogic::AddOrReplaceCard(const uint32_t card, const uint8_t side, const uint32_t position)
{
	mGameSide[side].AddOrReplaceCard(card, position);
}

void BaccaratGameLogic::ChangeGameResult(std::vector<dealer_assist::UpdatedCard> cards)
{
	for (const auto& card : cards)
	{
		switch (const auto dealingPhase = EBaccaratDealingPhase::_from_integral(card.position))
		{
			case EBaccaratDealingPhase::PlayerFirstCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Player, 0); break;
			case EBaccaratDealingPhase::PlayerSecondCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Player, 1); break;
			case EBaccaratDealingPhase::PlayerThirdCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Player, 2); break;
			case EBaccaratDealingPhase::BankerFirstCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Banker, 0); break;
			case EBaccaratDealingPhase::BankerSecondCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Banker, 1); break;
			case EBaccaratDealingPhase::BankerThirdCard: AddOrReplaceCard(card.newValue, EBaccaratSide::Banker, 2); break;

			default: break;
		}
	}
}

std::optional<uint8_t> BaccaratGameLogic::GetWinner() const
{
	return mWinner._to_integral();
}

std::vector<uint8_t> BaccaratGameLogic::GetWinners() const
{
	return { mWinner._to_integral() };
}

uint8_t BaccaratGameLogic::GetDealingPhase() const
{
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() == 0)
		return EBaccaratDealingPhase::PlayerFirstCard;
	if (mGameSide[EBaccaratSide::Banker].NumberOfCards() == 0)
		return EBaccaratDealingPhase::BankerFirstCard;
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() == 1)
		return EBaccaratDealingPhase::PlayerSecondCard;
	if (mGameSide[EBaccaratSide::Banker].NumberOfCards() == 1)
		return EBaccaratDealingPhase::BankerSecondCard;

	// Check for naturals
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() == 2 && mGameSide[EBaccaratSide::Banker].NumberOfCards() == 2)
		if (IsNatural(EBaccaratSide::Player) || IsNatural(EBaccaratSide::Banker))
			return EBaccaratDealingPhase::Finished;

	if (mGameSide[EBaccaratSide::Player].NumberOfCards() >= 2 && mGameSide[EBaccaratSide::Banker].NumberOfCards() >= 2)
	{
		if (ShouldPlayerDrawThirdCard())
			return EBaccaratDealingPhase::PlayerThirdCard;
		if (ShouldBankerDrawThirdCard())
			return EBaccaratDealingPhase::BankerThirdCard;

		return EBaccaratDealingPhase::Finished;
	}

	return EBaccaratDealingPhase::Finished;
}

bool BaccaratGameLogic::ShouldPlayerDrawThirdCard() const
{
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() != 2)
		return false;

	return (mGameSide[EBaccaratSide::Player].HandValue() < 6U);
}

bool BaccaratGameLogic::IsNatural(EBaccaratSide side) const
{
	return mGameSide[side].HandValue() == 8 || mGameSide[side].HandValue() == 9;
}

std::vector<uint8_t> BaccaratGameLogic::Evaluate()
{
	TLOG(LogBaccaratGameLogic, EVerbosity::Info, "Player number of cards: %i | Banker number of cards %i.", mGameSide[EBaccaratSide::Player].NumberOfCards(),
	     mGameSide[EBaccaratSide::Banker].NumberOfCards());
	TLOG(LogBaccaratGameLogic, EVerbosity::Info, "Player value: %i | Banker value %i.", mGameSide[EBaccaratSide::Player].HandValue(),
	     mGameSide[EBaccaratSide::Banker].HandValue());

	if (mGameSide[EBaccaratSide::Player].HandValue() > mGameSide[EBaccaratSide::Banker].HandValue())
	{
		mWinner = baccarat::EBaccaratWinner::PlayerWin;
	}
	else if (mGameSide[EBaccaratSide::Banker].HandValue() > mGameSide[EBaccaratSide::Player].HandValue())
	{
		mWinner = EBaccaratWinner::BankerWin;
	}
	else
	{
		mWinner = EBaccaratWinner::Tie;
	}

	TLOG(LogBaccaratGameLogic, Info, "Game winner is %s", mWinner._to_string());

	return { mWinner._to_integral() };
}

bool BaccaratGameLogic::ShouldBankerDrawThirdCard() const
{
	if (mGameSide[EBaccaratSide::Banker].NumberOfCards() != 2)
		return false;

	auto thirdCard = mGameSide[EBaccaratSide::Player].CardAt(2);
	auto bankerHandValue = mGameSide[EBaccaratSide::Banker].HandValue();

	if (bankerHandValue <= 2)
		return true;
	if (bankerHandValue == 3)
		return !thirdCard || BaccaratGameSide::CardValue(*thirdCard) != 8;
	if (bankerHandValue == 4)
		return !thirdCard || (BaccaratGameSide::CardValue(*thirdCard) >= 2 && BaccaratGameSide::CardValue(*thirdCard) <= 7);
	if (bankerHandValue == 5)
		return !thirdCard || (BaccaratGameSide::CardValue(*thirdCard) >= 4 && BaccaratGameSide::CardValue(*thirdCard) <= 7);
	if (bankerHandValue == 6)
		return thirdCard && (BaccaratGameSide::CardValue(*thirdCard) == 6 || BaccaratGameSide::CardValue(*thirdCard) == 7);

	return false;
}

uint32_t BaccaratGameLogic::GetGoldenCard()
{
	if (mGoldenTieCard.has_value())
		return mGoldenTieCard.value();

	mGoldenTieCard = crypto::GetRandomInRange(1, 13);
	return mGoldenTieCard.value();
}

json BaccaratGameLogic::GetGameResult() const
{
	json result(json::value_t::object);
	json player(json::value_t::object);
	json banker(json::value_t::object);

	player["sum"] = mGameSide[EBaccaratSide::Player].HandValue();

	json playerCards(json::value_t::array);
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() >= 2)
	{
		playerCards.push_back(mGameSide[EBaccaratSide::Player].CardAt(0).value());
		playerCards.push_back(mGameSide[EBaccaratSide::Player].CardAt(1).value());
	}
	player["cards"] = std::move(playerCards);

	if (std::optional<uint32_t> playerThirdCard = mGameSide[EBaccaratSide::Player].CardAt(2))
	{
		player["additionalCard"] = *playerThirdCard;
	}

	banker["sum"] = mGameSide[EBaccaratSide::Banker].HandValue();

	json bankerCards(json::value_t::array);
	if (mGameSide[EBaccaratSide::Banker].NumberOfCards() >= 2)
	{
		bankerCards.push_back(mGameSide[EBaccaratSide::Banker].CardAt(0).value());
		bankerCards.push_back(mGameSide[EBaccaratSide::Banker].CardAt(1).value());
	}

	banker["cards"] = std::move(bankerCards);

	if (std::optional<uint32_t> bankerThirdCard = mGameSide[EBaccaratSide::Banker].CardAt(2))
	{
		banker["additionalCard"] = *bankerThirdCard;
	}

	if (mGoldenTieCard)
		result["mysteryTieCard"] = *mGoldenTieCard;

	result["player"] = std::move(player);
	result["banker"] = std::move(banker);
	result["winner"] = static_cast<int>(mWinner);

	return result;
}

dealer_assist::GameRecordDto BaccaratGameLogic::GetGameRecord() const
{
	dealer_assist::GameRecordDto dto;
	dto.Winners = { mWinner };

	auto extraData = dealer_assist::BaccaratGameRecordExtraDataDto();

	extraData.BankerPair = IsPair(EBaccaratSide::Banker);
	extraData.PlayerPair = IsPair(EBaccaratSide::Player);

	if (mWinner == EBaccaratWinner::PlayerWin)
	{
		extraData.Naturals = IsNatural(EBaccaratSide::Player);
		dto.WinningHandValue = mGameSide[EBaccaratSide::Player].HandValue();
	}
	else
	{
		extraData.Naturals = IsNatural(EBaccaratSide::Banker);
		dto.WinningHandValue = mGameSide[EBaccaratSide::Banker].HandValue();
	}

	dto.Cards = GetCurrentStateOfCards();
	dto.ExtraData = extraData;
	dto.HandValues = GetHandValues();

	return dto;
}

std::vector<json> BaccaratGameLogic::GetCards(bool showCardFaces)
{
	std::vector<json> cards;

	auto createCardObject = [this](int index, EBaccaratSide side, int cardIndex, bool showCardFaces) -> json {
		json card(json::value_t::object);
		card["name"] = "addNewCard";
		card["index"] = index;

		std::optional<uint32_t> cardValue = mGameSide[side].CardAt(cardIndex);
		if (cardValue)
		{
			card["card"] = showCardFaces ? *cardValue : 0;
		}

		return card;
	};

	// Always create the first 4 cards
	cards.push_back(createCardObject(0, EBaccaratSide::Player, 0, showCardFaces));
	cards.push_back(createCardObject(1, EBaccaratSide::Banker, 0, showCardFaces));
	cards.push_back(createCardObject(2, EBaccaratSide::Player, 1, showCardFaces));
	cards.push_back(createCardObject(3, EBaccaratSide::Banker, 1, showCardFaces));

	// Conditionally add the 5th and 6th cards
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() == 3)
	{
		cards.push_back(createCardObject(4, EBaccaratSide::Player, 2, showCardFaces));
	}

	if (mGameSide[EBaccaratSide::Banker].NumberOfCards() == 3)
	{
		if (mGameSide[EBaccaratSide::Player].NumberOfCards() != 3)
			cards.push_back(createCardObject(4, EBaccaratSide::Player, 2, showCardFaces));

		cards.push_back(createCardObject(5, EBaccaratSide::Banker, 2, showCardFaces));
	}

	if (mCutCardPosition != -1 && bShowCutCard)
	{
		json cutCard(json::value_t::object);
		cutCard["name"] = "addNewCard";
		cutCard["index"] = mCutCardPosition;
		cutCard["card"] = -1;

		if (cards.size() >= 4)
		{
			cards.insert(cards.begin() + mCutCardPosition, cutCard);
		}
		else
		{
			cards.push_back(cutCard);
		}
	}

	if (!cards.empty())
	{
		cards.back()["winner"] = static_cast<int>(mWinner);
	}

	return cards;
}

json BaccaratGameLogic::GetCards() const
{
	CardPositionDto<EBaccaratDealingPhase> cards;

	auto AddCard = [this](const EBaccaratSide side, const int cardIndex, const EBaccaratDealingPhase dealingPhase, CardPositionDto<EBaccaratDealingPhase>& data) {
		if (auto cardOpt = mGameSide[side].CardAt(cardIndex))
		{
			data.Cards.insert({ dealingPhase, cardOpt.value() });
		}
	};

	AddCard(EBaccaratSide::Player, 0, EBaccaratDealingPhase(EBaccaratDealingPhase::PlayerFirstCard), cards);
	AddCard(EBaccaratSide::Banker, 0, EBaccaratDealingPhase(EBaccaratDealingPhase::BankerFirstCard), cards);
	AddCard(EBaccaratSide::Player, 1, EBaccaratDealingPhase(EBaccaratDealingPhase::PlayerSecondCard), cards);
	AddCard(EBaccaratSide::Banker, 1, EBaccaratDealingPhase(EBaccaratDealingPhase::BankerSecondCard), cards);
	AddCard(EBaccaratSide::Player, 2, EBaccaratDealingPhase(EBaccaratDealingPhase::PlayerThirdCard), cards);
	AddCard(EBaccaratSide::Banker, 2, EBaccaratDealingPhase(EBaccaratDealingPhase::BankerThirdCard), cards);

	return cards.AsJSON();
}

json BaccaratGameLogic::GetCurrentStateOfCards() const
{
	CardPositionDto<EBaccaratDealingPhase> cards;

	auto addCard = [this](EBaccaratSide side, int cardIndex, EBaccaratDealingPhase phase, CardPositionDto<EBaccaratDealingPhase>& data) {
		if (auto cardOpt = mGameSide[side].CardAt(cardIndex))
		{
			data.Cards.insert({ phase, cardOpt.value() });
		}
	};

	addCard(EBaccaratSide::Player, 0, EBaccaratDealingPhase::PlayerFirstCard, cards);
	addCard(EBaccaratSide::Player, 1, EBaccaratDealingPhase::PlayerSecondCard, cards);
	addCard(EBaccaratSide::Player, 2, EBaccaratDealingPhase::PlayerThirdCard, cards);
	addCard(EBaccaratSide::Banker, 0, EBaccaratDealingPhase::BankerFirstCard, cards);
	addCard(EBaccaratSide::Banker, 1, EBaccaratDealingPhase::BankerSecondCard, cards);
	addCard(EBaccaratSide::Banker, 2, EBaccaratDealingPhase::BankerThirdCard, cards);

	return cards.AsJSON();
}

void BaccaratGameLogic::SetCurrentStateOfCards(const json& cards)
{
	for (const auto& card : cards)
	{
		const EBaccaratDealingPhase phase = EBaccaratDealingPhase::_from_integral(card["index"].get<int>());
		const uint32_t cardValue = card.at("card").get<uint32_t>();

		switch (phase)
		{
			case EBaccaratDealingPhase::PlayerFirstCard: mGameSide[EBaccaratSide::Player].AddCard(cardValue, 0); break;
			case EBaccaratDealingPhase::PlayerSecondCard: mGameSide[EBaccaratSide::Player].AddCard(cardValue, 1); break;
			case EBaccaratDealingPhase::PlayerThirdCard: mGameSide[EBaccaratSide::Player].AddCard(cardValue, 2); break;
			case EBaccaratDealingPhase::BankerFirstCard: mGameSide[EBaccaratSide::Banker].AddCard(cardValue, 0); break;
			case EBaccaratDealingPhase::BankerSecondCard: mGameSide[EBaccaratSide::Banker].AddCard(cardValue, 1); break;
			case EBaccaratDealingPhase::BankerThirdCard: mGameSide[EBaccaratSide::Banker].AddCard(cardValue, 2); break;
			default: throw std::invalid_argument("Invalid dealing phase");
		}
	}
}

json BaccaratGameLogic::GetFreeHandGameResult(const bool showCardFaces)
{
	json freeHandResult(json::value_t::object);
	freeHandResult["name"] = "freeGame";
	freeHandResult["winningNumber"] = mWinner == EBaccaratWinner::PlayerWin ? mGameSide[EBaccaratSide::Player].HandValue() : mGameSide[EBaccaratSide::Banker].HandValue();
	freeHandResult["bankerNaturals"] = mGameSide[EBaccaratSide::Banker].HandValue() == 8 || mGameSide[EBaccaratSide::Banker].HandValue() == 9;
	freeHandResult["playerNaturals"] = mGameSide[EBaccaratSide::Player].HandValue() == 8 || mGameSide[EBaccaratSide::Player].HandValue() == 9;
	freeHandResult["playerPair"] = IsPair(EBaccaratSide::Player);
	freeHandResult["bankerPair"] = IsPair(EBaccaratSide::Banker);
	freeHandResult["playerSum"] = mGameSide[EBaccaratSide::Player].HandValue();
	freeHandResult["bankerSum"] = mGameSide[EBaccaratSide::Banker].HandValue();
	freeHandResult["winner"] = static_cast<int>(mWinner);
	freeHandResult["cards"] = GetCards(showCardFaces);
	return freeHandResult;
}

bool BaccaratGameLogic::IsBetTypeWon(const EBaccaratBetType type, EBaccaratBetMultiplierType multiplierType) const
{
	// Check pairs
	if (type == EBaccaratBetType::PlayerPair || type == EBaccaratBetType::BankerPair)
	{
		auto side = (type == EBaccaratBetType::PlayerPair) ? EBaccaratSide::Player : EBaccaratSide::Banker;
		return IsPair(side);
	}

	// Check suited match
	if (type == EBaccaratBetType::SuitedMatch)
	{
		switch (multiplierType)
		{
			case EBaccaratBetMultiplierType::Pair: return IsPair(EBaccaratSide::Player) || IsPair(EBaccaratSide::Banker);
			case EBaccaratBetMultiplierType::SuitedPair: return IsSuitedPair(EBaccaratSide::Player) || IsSuitedPair(EBaccaratSide::Banker);
			case EBaccaratBetMultiplierType::TwoPairs: return IsPair(EBaccaratSide::Player) && IsPair(EBaccaratSide::Banker);
			case EBaccaratBetMultiplierType::FourCardsRank: return IsFourCardSameRank();
			case EBaccaratBetMultiplierType::SuitedAndUnsuitedPairs: return HasSuitedAndOffSuitedPairs();
			case EBaccaratBetMultiplierType::TwoSuitedPairs: return HasSuitedPairs();

			default: return false;
		}
	}

	if (type == EBaccaratBetType::Lucky6)
	{
		if (multiplierType == EBaccaratBetMultiplierType::Lucky6TwoCards)
			return mWinner == EBaccaratWinner::BankerWin && mGameSide[EBaccaratSide::Banker].HandValue() == 6;
		if (multiplierType == EBaccaratBetMultiplierType::Lucky6ThreeCards)
			return mWinner == EBaccaratWinner::BankerWin && mGameSide[EBaccaratSide::Banker].HandValue() == 6 && mGameSide[EBaccaratSide::Banker].NumberOfCards() == 3;
	}

	uint32_t winValue = 0;
	switch (type)
	{
		case EBaccaratBetType::WinWith1: winValue = 1; break;
		case EBaccaratBetType::WinWith2: winValue = 2; break;
		case EBaccaratBetType::WinWith3: winValue = 3; break;
		case EBaccaratBetType::WinWith4: winValue = 4; break;
		case EBaccaratBetType::WinWith5: winValue = 5; break;
		case EBaccaratBetType::WinWith6: winValue = 6; break;
		case EBaccaratBetType::WinWith7: winValue = 7; break;
		case EBaccaratBetType::WinWith8: winValue = 8; break;
		case EBaccaratBetType::WinWith9: winValue = 9; break;
		default: return false;
	}

	// Check for win with specific hand value
	if (winValue > 0)
	{
		return (mWinner == EBaccaratWinner::PlayerWin && mGameSide[EBaccaratSide::Player].HandValue() == winValue) ||
		       (mWinner == EBaccaratWinner::BankerWin && mGameSide[EBaccaratSide::Banker].HandValue() == winValue);
	}

	return false;
}

bool BaccaratGameLogic::IsPair(const EBaccaratSide side) const
{
	if (mGameSide[side].NumberOfCards() < 2)
		return false;

	return mGameSide[side].CardAt(0).value_or(0) % 100 == mGameSide[side].CardAt(1).value_or(1) % 100;
}

uint32_t BaccaratGameLogic::GetHandValue(const uint8_t side) const
{
	return mGameSide[side].HandValue();
}

uint32_t BaccaratGameLogic::GetNumberOfCardsOnTable() const
{
	return mGameSide[EBaccaratSide::Player].NumberOfCards() + mGameSide[EBaccaratSide::Banker].NumberOfCards();
}

bool BaccaratGameLogic::HasFaceDownCard(const EBaccaratSide side) const
{
	return mGameSide[side].HasFaceDownCard();
}

bool BaccaratGameLogic::HasFaceDownCard(const uint32_t side) const
{
	return mGameSide[side].HasFaceDownCard();
}

bool BaccaratGameLogic::IsGameFinished() const
{
	if (HasFaceDownCard(EBaccaratSide::Player) || HasFaceDownCard(EBaccaratSide::Banker))
		return false;

	if (GetDealingPhase() == EBaccaratDealingPhase::Finished)
		return true;

	return false;
}

bool BaccaratGameLogic::IsSuitedPair(const EBaccaratSide side) const
{
	if (mGameSide[side].NumberOfCards() < 2)
		return false;

	return mGameSide[side].CardAt(0).value_or(0) == mGameSide[side].CardAt(1).value_or(1);
}

bool BaccaratGameLogic::IsFourCardSameRank() const
{
	if (mGameSide[EBaccaratSide::Player].NumberOfCards() < 2 || mGameSide[EBaccaratSide::Banker].NumberOfCards() < 2)
		return false;

	const auto playerCard1 = mGameSide[EBaccaratSide::Player].CardAt(0).value() % 100;
	const auto playerCard2 = mGameSide[EBaccaratSide::Player].CardAt(1).value() % 100;

	const auto bankerCard1 = mGameSide[EBaccaratSide::Banker].CardAt(0).value() % 100;
	const auto bankerCard2 = mGameSide[EBaccaratSide::Banker].CardAt(1).value() % 100;

	return playerCard1 == playerCard2 && bankerCard1 == bankerCard2 && playerCard1 == bankerCard1;
}

bool BaccaratGameLogic::HasSuitedAndOffSuitedPairs() const
{
	bool playerSuitedPair = IsSuitedPair(EBaccaratSide::Player);
	bool bankerSuitedPair = IsSuitedPair(EBaccaratSide::Banker);
	bool playerOffSuitedPair = IsPair(EBaccaratSide::Player) && !IsSuitedPair(EBaccaratSide::Player);
	bool bankerOffSuitedPair = IsPair(EBaccaratSide::Banker) && !IsSuitedPair(EBaccaratSide::Banker);

	return ((playerSuitedPair && bankerOffSuitedPair) || (playerOffSuitedPair && bankerSuitedPair)) && IsFourCardSameRank();
}

bool BaccaratGameLogic::HasSuitedPairs() const
{
	return IsSuitedPair(EBaccaratSide::Player) && IsSuitedPair(EBaccaratSide::Banker) && IsFourCardSameRank();
}

std::unordered_map<std::string, uint32_t> BaccaratGameLogic::GetHandValues() const
{
	std::unordered_map<std::string, uint32_t> handValues;
	handValues[EBaccaratSide(EBaccaratSide::Banker)._to_string()] = mGameSide[EBaccaratSide::Banker].HandValue();
	handValues[EBaccaratSide(EBaccaratSide::Player)._to_string()] = mGameSide[EBaccaratSide::Player].HandValue();

	return handValues;
}
