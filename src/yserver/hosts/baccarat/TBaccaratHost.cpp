//
// Created by <PERSON><PERSON><PERSON> on 12. 12. 23.
//

#include "hosts/baccarat/TBaccaratHost.h"

#include <oneapi/tbb/concurrent_unordered_map.h>

#include <algorithm>
#include <execution>
#include <random>

#include "MyUtils.h"
#include "YServer.h"
#include "YServerTypes.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/dto/AddCardDto.h"
#include "dealer-assist/dto/ChatMessageDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/FlagStateChangeDto.h"
#include "dealer-assist/dto/GameRecordDto.h"
#include "dealer-assist/dto/ReportNumOfActivePlayersDto.h"
#include "dealer-assist/dto/ToggleChatDto.h"
#include "hosts/cards/CardGameBetStatistics.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;
using namespace dealer_assist;
using namespace yprotocol;

DEFINE_LOG_CATEGORY(LogBaccarat, "baccarat")

const JsonSchema BaccaratHostSchema = JsonSchema(
  { { "stakes", JsonSchema(json::value_t::array, "List of stakes on this baccarat host", json(), true).SetChildSchema(TBaccaratStake::StakeSchema(), 1) },
    { "bet-timeout", JsonSchema(json::value_t::number_unsigned, "If > 0, bets older than this amount of seconds are cleared if the player is offline", 60U) },
    { "late-bet-threshold", JsonSchema(json::value_t::number_unsigned, "How long before Bets Closed to close bets for the players (milliseconds)", 500U)
                              .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 1000U))
                              .SetInterval(50) },
    { "dealer-assist-table",
      JsonSchema(
        {
          { "address", JsonSchema(json::value_t::string, "The address of dealer assist table").Flag(CriticalSettingFlag) },
          { "secure", JsonSchema(json::value_t::boolean, "If true, use TLS to connect").Flag(CriticalSettingFlag) },
          { "event-delay-time", JsonSchema(json::value_t::number_unsigned, "How long should all event from dealer assist table should be delayed (ms)", 0U)
                                  .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 2001U)) },
        },
        "Dealer assist table configuration") },
    { "bet-statistics", JsonSchema({ { "enabled-in-game", JsonSchema(json::value_t::boolean, "Send statistic to frontend", true) },
                                     { "per-host", JsonSchema(json::value_t::boolean, "Calculate statistic per host or per table") },
                                     { "interval", JsonSchema(json::value_t::number_unsigned, "Interval of updating") },
                                     { "bet-types", JsonSchema(json::value_t::array, "Bet types valid for statistic", json::value_t::array)
                                                      .SetChildSchema(JsonSchema(json::value_t::string, "Bet type name").SetToEnumType<EBaccaratBetType>()) } },
                                   "Dealer assist table configuration") } });

const JsonSchema BaccaratGameSchema = TGameHost::BaseGameSchema() + TBaseBaccaratHost::BaseBaccaratGameSchema();

const JsonSchema BetSchema = JsonSchema(
  { { "bets",
      JsonSchema(json::value_t::array, "Array of bet placements", json(json::value_t::array)).SetChildSchema(JsonSchema(json::value_t::number_unsigned, "Bets")) },
    { "betPresets", JsonSchema(json::value_t::object, "Map of bet placements on preset fields", json(json::value_t::object))
                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The amount to bet on this field")) } });

const JsonSchema& TBaccaratHost::GetBetSchema() const
{
	return BetSchema;
}

const JsonSchema& TBaccaratHost::GetGameSchema() const
{
	return BaccaratGameSchema;
}

uint64_t TBaccaratHost::BaccaratState::GetTimeLeftMs(const BetsCloseBehavior& behavior) const
{
	const uint64_t now = yprotocol::Timestamp();
	const uint64_t TimeLeftMs = TotalBetTimeMs - (now - Timestamp);

	if (static_cast<uint64_t>(behavior.ReduceBetTimeByMs) > TimeLeftMs)
		return 0;

	return TimeLeftMs - behavior.ReduceBetTimeByMs;
}

uint64_t TBaccaratHost::BaccaratState::GetTotalTimeMs(const BetsCloseBehavior& behavior) const
{
	return TotalBetTimeMs - behavior.ReduceBetTimeByMs;
}

uint64_t TBaccaratHost::BaccaratState::GetBetCloseTimeMs(const BetsCloseBehavior& behavior) const
{
	return Timestamp + GetTotalTimeMs(behavior);
}

EDealerAssistPhase TBaccaratHost::BaccaratState::GetPhase(const BetsCloseBehavior betBehavior) const
{
	// Check if bets are open but the time has run out (time regarding bet behavior)
	if (Phase == EDealerAssistPhase::BetsOpen && GetTimeLeftMs(betBehavior) == 0)
		return EDealerAssistPhase::BetsClosed;

	return Phase;
}

TBaccaratHost::TBaccaratHost(YModuleContainerBase* container, const std::string& name) : TGameHost(container, name, HostType::Baccarat)
{
	SetLogCategory(LogBaccarat);
	bAllowStakeChange = false;
	bForceStakeSelect = false;
	bAllowMultiSession = false;
	bLiveGame = true;
	mDefaultStake = 0;

	GameStateChangedEvent = RegisterEvent("game-state-changed", "Sent when a variable describing the state of the game changes");
	BetStatisticsUpdateEvent = RegisterEvent("bet-statistics-update", "Sent when the bet statistics are updated");
	CardAddedEvent = RegisterEvent("add-card", "Sent when a card is added to the game");
	ConfigChangedEvent = RegisterEvent("config-changed", "If the configuration of the host changes, this is fired");
	StakeChangedEvent = RegisterEvent("stakeChanged", "Triggered when the selected stake changes");
	CroupierEvent = RegisterEvent("croupier", "Triggered when a croupier leaves a table or a new one joins.");
	WinReportEvent = RegisterEvent("win-report", "Triggered when a game ends to report wins that have occurred");
	CutCardEvent = RegisterEvent("cut-card-pulled", "Triggered when a cut card is drawn");
	ChatToggleEvent = RegisterEvent("chat-toggle", "Triggered when the chat is toggled");

	// Check this one
	OnStatusChanged += [this](const FModuleStatusInfo& oldStatus, const FModuleStatusInfo& newStatus) {
		if (oldStatus.Status == EModuleStatus::Ready && newStatus.Status != EModuleStatus::Ready)
		{
			ForEachPlayer([&](Player& player) {
				ScopedLock lock(player);
				if (player.Game()->BetStatus() == EGameBetState::BetPlaced)
					player.VoidGame(FGameroundError({ EVoidReason(EVoidReason::ClientLocked)._to_string(), "Croupier left" }), false);
			});
		}
	};


	OnServiceStatusChanged += [this](bool bOnline) {
		if (!bOnline)
		{
			Log(Warning, "Connection with dealer assist service dropped!");
			{
				SharedScopedLock lock(mPlayers);
				if (mGameToAccountFor)
				{
					Log(Critical, "Game %lu has not been accounted for yet and will be void!", mGameToAccountFor);
					// VoidLiveGame("Communication with game service was dropped");
				}
			}
		}
	};

	Schema() += BaccaratHostSchema;

	RegisterAdminAction(
	  "Void Game", EModuleAdminActionTarget::Player,
	  [this](const yprotocol::Request& request, const PlayerPtr& player, const json& val, const YAuthKey& instigator) -> json {
		  if (player->Game()->BetStatus() != EGameBetState::Idle)
			  VoidGame(*player, EVoidReason::NoGameResult, "Admin void: " + val.get<std::string>());

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Reason", std::string()));

	RegisterAdminAction(
	  "Void Game For All Players", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, PlayerPtr player, const json& val, const YAuthKey& instigator) -> json {
		  VoidLiveGame("Admin void: " + val.get<std::string>());
		  return {};
	  },
	  JsonSchema(json::value_t::string, "Reason", std::string()));
}

TBaccaratHost::~TBaccaratHost() {}

void TBaccaratHost::OnConfigLoaded(const std::filesystem::path& filename)
{
	TLiveGameHost::OnConfigLoaded(filename);

	mBets[EBaccaratPlayboardMode::Commission].ClearStakes();
	mBets[EBaccaratPlayboardMode::NoCommission].ClearStakes();

	mBetsClosedBehavior.ReduceBetTimeByMs = GetConfig("late-bet-threshold").get<uint32_t>();
	mBetTimeoutDuration = GetConfig("bet-timeout").get<uint64_t>() * 1000;

	bAllowStakeChange = GetConfig("allow-stake-change").get<bool>();

	mDealerAssistTableAddress = GetConfig("dealer-assist-table.address").get<std::string>();
	if (mDealerAssistTableAddress.empty())
		throw ConfigError("Dealer assist address is required!");
	bDealerAssistSecure = GetConfig("dealer-assist-table.secure").get<bool>();
	Log(Info, "Will connect to Live Table at %s", mDealerAssistTableAddress.c_str());

	mDealerAssistEventDelayTime = GetConfig("dealer-assist-table.event-delay-time").get<uint64_t>();

	// Bet statistics
	bBetStatisticsEnabled = GetConfig("bet-statistics.enabled-in-game").get<bool>();
	bBetStatisticsPerHost = GetConfigOptional("bet-statistics.per-host", true).get<bool>();
	mBetStatistics = std::make_unique<CardGameBetsStatistic>(GetConfig("bet-statistics"));
	mBetStatistics->PreUpdate += [this]() {
		std::unordered_map<std::string, BetTypeHostStats> betsInfo;
		ForEachPlayer([&betsInfo, this](const Player& player) {
			auto game = dynamic_cast<TBaccaratGame*>(player.Game().get());
			const auto& bets = game->ConfirmedBets.Get();

			for (const auto& [betType, betAmount] : bets)
			{
				if (!betAmount || !mBetStatistics->IsBetTypeSupported(betType._to_string()))
					continue;
				auto& mjolk = betsInfo[betType._to_string()];
				mjolk.TotalBet += betAmount;
				mjolk.NumOfPlayers++;
			}
		});
		mBetStatistics->SetBetAmounts(mDealerAssistHostIdentifier, betsInfo);
	};
	mBetStatistics->OnUpdate += [this](const BetStatisticsDto& stats) {
		OnBetStatisticsUpdate(stats);
	};

	mEventQueue.SetMaxEventAgeMs(mDealerAssistEventDelayTime);
	DealerAssistClient = std::make_shared<YClient>(mDealerAssistTableAddress, bDealerAssistSecure, "Dealer Assist Client");

	DealerAssistClient->OnInitialized += [this](const json& data) {
		mEventQueue.StartProcessing();

		auto initResponseDto = DealerAssistInitResponseDto::FromJSON(data);
		ScopedLock lock(mDealerAssistVersion);
		&mDealerAssistVersion = initResponseDto.mAppVersion;
		mReceivedDealerAssistVersion.notify_all();
		OnInitDealerAssist(initResponseDto);
	};

	mEventQueue.OnBroadcastEvent += [this](const std::string& eventName, const json& data) {
		Log(Info, "Received event: %s with data: %s", eventName.c_str(), JsonSchema::PrintValueInline(data).c_str());
		if (eventName == GAME_PHASE_CHANGED_EVENT_NAME)
		{
			OnGamePhaseChanged(DealerAssistStateUpdateDto::FromJSON(data));
		}
		else if (eventName == ADD_CARD_EVENT_NAME)
		{
			Log(Info, "Received card from dealer assist: %s", JsonSchema::PrintValueInline(data).c_str());

			auto card = AddCardDto::FromJSON(data);

			try
			{
				AddCard(card.GetScannedCard().ToInt() % 1000);
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Failed to add card: %s", e.what());
			}
		}
		else if (eventName == CUT_CARD_DRAWN_EVENT_NAME)
		{
			BroadcastEventToPlayers(*CutCardEvent);
		}
		else if (eventName == POST_CHAT_MESSAGE_EVENT_NAME)
		{
			TriggerEvent(*ChatEvent, data);
		}
		else if (eventName == REPORT_BET_STATISTICS_RESULT)
		{
			if (!bBetStatisticsPerHost && bBetStatisticsEnabled)
				TriggerEvent(*BetStatisticsUpdateEvent, data);
		}
		else if (eventName == TOGGLE_CHAT_EVENT_NAME)
		{
			auto dto = ToggleChatDto::FromJSON(data);
			TriggerEvent(*ChatToggleEvent, dto.IsEnabled());

			ScopedLock lock(CurrentState);
			CurrentState->ChatEnabled = dto.IsEnabled();
		}
		else if (eventName == GAME_STATE_CHANGED_EVENT_NAME)
		{
			const json* state = FindMember(data, "state");
			if (state && !state->is_null())
				OnGameStateChanged(DealerAssistInitResponseDto::FromJSON(*state));
		}
	};

	DealerAssistClient->OnEvent += [this](const std::string& eventName, const json& data) {
		mEventQueue.PushEvent(eventName, data);
	};

	DealerAssistClient->OnDisconnected += [this](bool bReconnect) {
		ServiceDisconnected("Dealer Assist service disconnected!");
		mBetStatistics->PauseUpdates();
		mEventQueue.StopProcessing();
	};

	Log(Info, "Changing stake during game is %s", bAllowStakeChange ? "ENABLED" : "DISABLED");

	bForceStakeSelect = GetConfigOptional("force-stake-select", true).get<bool>();
	if (bForceStakeSelect)
	{
		Log(Info, "If there are multiple stakes, the player will be forced to select one uppon joining.");
		/*if (mDefaultStakeRules.Default)
		    Log(Warning, "The 'default-stake' is set to %s, but wil be ignored because 'force-stake-select' is ON", stakeName.c_str());*/
	}

	const std::string accesModeStr = GetConfigOptional("stake-mode", EHostAccessMode(EHostAccessMode::All)._to_string()).get<std::string>();
	auto opt = EHostAccessMode::_from_string_nocase_nothrow(accesModeStr.c_str());
	if (!opt)
		throw ConfigError("Unknown stake-mode '" + accesModeStr + "'");
	mStakeAccessMode = *opt;

	json accessList = GetConfigOptional("stake-access-list", {});
	if (accessList.is_array())
	{
		for (const json& member : accessList)
		{
			if (member.is_number_unsigned())
				mStakeAccess.insert(member.get<uint32_t>());
		}
	}

	mDefaultStake = GetConfigOptional("default-stake", mDefaultStake).get<uint32_t>();
	Log(Info, "The default stake is set to %u.", mDefaultStake);

	const json stakesJSON = GetConfig("stakes");
	if (!stakesJSON.is_array())
		throw ConfigError("No array of stakes exists in config, but it is required (with at least one entry)!");

	for (uint stakeID = 0; stakeID < stakesJSON.size(); stakeID++)
	{
		try
		{
			TBaccaratStake stake;
			stake.LoadSubconfiguration(stakesJSON[stakeID]);
			mAllStakes.push_back(stake);
		}
		catch (const std::exception& e)
		{
			Log(Warning, "Stake %u is invalid: %s", stakeID, e.what());
		}
	}

	if (mAllStakes.empty())
		throw ConfigError("No valid stakes available in the config");

	const size_t allStakesNum = mAllStakes.size();
	Log(Info, "Successfully read %lu stakes from config!", allStakesNum);
	size_t numStakes = 0;
	for (size_t stakeIdx = 0; stakeIdx < mAllStakes.size(); stakeIdx++)
	{
		if (!mAllStakes[stakeIdx].IsValid())
		{
			Log(Warning, "Stake %lu is invalid and will be skipped! This means chip values are all 0.", stakeIdx);
			continue;
		}
		if (TBaccaratHost::IsValidStake(stakeIdx))
		{
			try
			{
				mBets[EBaccaratPlayboardMode::Commission].AddStake(mAllStakes[stakeIdx]);
				mBets[EBaccaratPlayboardMode::NoCommission].AddStake(mAllStakes[stakeIdx]);
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Stake %d is invalid: %s", stakeIdx, e.what());
			}
			if (mDefaultStake == stakeIdx && mDefaultStake != numStakes)
			{
				Log(Warning, "The default stake is set to %u, due because some stakes before %u are not used, it is effectively translated to %lu.", mDefaultStake,
				    mDefaultStake, numStakes);
				mDefaultStake = numStakes;
			}
			numStakes++;
		}
		else if (stakeIdx == mDefaultStake)
		{
			Log(Warning, "The default stake is set to %u, this stake is disabled due to stake-mode and stake-access-list being configured to ignore it.", mDefaultStake);
		}
	}

	if (!numStakes)
		throw ConfigError("No valid stakes were found that conform with the conditions defined by stake-mode and stake-access-list!");


	Log(Info, "%lu of the total %lu stakes will be used on this game host!", numStakes, allStakesNum);

	if (mDefaultStake >= numStakes)
	{
		Log(Warning, "The default stake is set to %u, but no such stake exists on the roulette host! Will use 0 as the default stake.", mDefaultStake);
	}

	RegisterAdminActions();
}

void TBaccaratHost::OnAddGameToHost(const StaticGameInformation& info)
{
	TGameHost::OnAddGameToHost(info);
	GameInformation gameInformation(info);
	GetGameDetails(gameInformation);
	ScopedLock lock(CurrentState);
	CurrentState->BaccaratGame = std::make_shared<TBaccaratGame>(UniqueIdentifier(), gameInformation);

	auto isCommission = info.GetConfig("commission").get<bool>();
	EBaccaratPlayboardMode mode = isCommission ? EBaccaratPlayboardMode::Commission : EBaccaratPlayboardMode::NoCommission;

	auto stakes = GetBets(mode).GetValidStakes();
	if (stakes.empty())
		throw ConfigError("There is no valid stakes for this host!");
}

bool TBaccaratHost::ConnectToService(std::string& outError)
{
	if (!TLiveGameHost::ConnectToService(outError))
		return false;

	std::error_code ec;

	mDealerAssistHostIdentifier = std::format("{}_{}", boost::asio::ip::host_name(), ID());
	DealerAssistClient->Connect(std::format("/{}/{}", DEALER_ASSIST_GAMEHOST_CLIENT_TYPE, mDealerAssistHostIdentifier), &ec);

	if (ec)
	{
		Log(Critical, "Could not connect connect to service at %s: %s", mDealerAssistTableAddress.c_str(), ec.message().c_str());
		return false;
	}

	SharedScopedLock lock(mDealerAssistVersion);
	mReceivedDealerAssistVersion.wait(lock, [&]() -> bool { return mDealerAssistVersion->has_value(); });

	if (EXPECTED_DEALER_ASSIST_VERSION.major != mDealerAssistVersion->value().major || EXPECTED_DEALER_ASSIST_VERSION.minor != mDealerAssistVersion->value().minor)
	{
		Log(Critical, "Dealer Assist app version mismatch! Expected %s, got %s", EXPECTED_DEALER_ASSIST_VERSION.to_string().c_str(),
		    mDealerAssistVersion->value().to_string().c_str());

		return false;
	}

	return true;
}

GameInstancePtr TBaccaratHost::CreateGameInstance(const GameInformation& game, const std::shared_ptr<GameEndpoint>& endp, const YPlayerAccount& account) const
{
	auto instance = std::make_shared<TBaccaratGame>(endp->HostUID, game);
	instance->CreditMultiplier = account.CreditValueMultiplier();

	if (endp->ExtraParams.contains("stake"))
	{
		const std::string stake = endp->ExtraParams.Get("stake");
		int stakeID;
		if (yutils::strToInt2(stake, stakeID))
			SetStake(*instance, stakeID);
	}
	return instance;
}

json TBaccaratHost::OnRegister(const YServerClient& client)
{
	json initResult = TGameHost::OnRegister(client);

	const YGameClient* gameClient = dynamic_cast<const YGameClient*>(&client);
	if (gameClient)
	{
		const TBaccaratGame& game = dynamic_cast<const TBaccaratGame&>(*gameClient->Game());
		initResult["config"] = GetGameConfig(game);
	}

	json history = json::value_t::array;
	const auto gameRecords = CurrentState.getCopy().GameRecords;
	for (const auto& record : gameRecords) { history.push_back(record.ToJSON()); }

	initResult["history"] = std::move(history);
	initResult["stream"] = GetVideoStreamInfo();

	return initResult;
}

json TBaccaratHost::GetGameState(const std::shared_ptr<const YGameClient>& client) const
{
	json gameState = TLiveGameHost::GetGameState(client);
	SharedScopedLock lock(CurrentState);
	gameState["gameID"] = CurrentState->GameID;

	if (CurrentState->GetPhase(mBetsClosedBehavior) == EDealerAssistPhase::BetsOpen)
	{
		gameState["timeLeft"] = CurrentState->GetTimeLeftMs(mBetsClosedBehavior);
		gameState["betCloseAt"] = CurrentState->GetBetCloseTimeMs(mBetsClosedBehavior);
	}

	gameState["totalTime"] = CurrentState->GetTotalTimeMs(mBetsClosedBehavior);
	gameState["phase"] = CurrentState->GetPhase(mBetsClosedBehavior)._to_index();
	gameState["phaseSubID"] = CurrentState->BaccaratGame->GetDealingPhase();
	gameState["winner"] = CurrentState->Winner._to_index();
	gameState["chatEnabled"] = CurrentState->ChatEnabled;
	gameState["cards"] = CurrentState->BaccaratGame->GetCurrentStateOfCards();

	Log(Info, "GetGameState: %s", JsonSchema::PrintValueInline(gameState).c_str());

	return gameState;
}

json TBaccaratHost::GetGameConfig(const TBaccaratGame& game) const
{
	json conf(json::value_t::object);
	json stakes(json::value_t::array);

	if (mBets[game.GetPlayboardMode()].GetAllStakes().size() > 0)
	{
		for (const auto& stake : mBets[game.GetPlayboardMode()].GetAllStakes()) stakes.push_back(TBaccaratBets::StakeAsJson(*stake, 1));
	}

	const auto& bets = mBets[game.GetPlayboardMode()];

	conf["stakes"] = std::move(stakes);
	Log(Info, "selectedStake %i.", mDefaultStake);
	conf["selectedStake"] = mDefaultStake;
	conf["commission"] = game.IsCommission();
	conf["enableBetStatistics"] = bBetStatisticsEnabled;

	json betTypes(json::value_t::array);
	for (const auto& [betType, betMultiplier] : bets.GetStake(mDefaultStake)->Multipliers)
	{
		json betTypeInfo(json::value_t::object);
		betTypeInfo["index"] = betType._to_index();
		betTypeInfo["name"] = betType._to_string();
		betTypeInfo["multiplier"] = betMultiplier.GetValue(EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Default)._to_string()).value_or(0);
		betTypes.push_back(std::move(betTypeInfo));
	}

	conf["betTypes"] = std::move(betTypes);
	return conf;
}

void TBaccaratHost::OnPlayerAdded(Player& player, bool bReconnected) noexcept
{
	TGameHost::OnPlayerAdded(player, bReconnected);

	auto game = dynamic_cast<TBaccaratGame*>(player.Game().get());

	if (game->BetTimeout)
	{
		game->BetTimeout->cancel();
		game->BetTimeout.reset();
	}

	OnRoundFailureResolved(player);    // to start gameround

	if (!bReconnected)
	{
		if (player.ConInfo->Query.contains("stake"))
		{
			const std::string stake = player.ConInfo->Query.Get("stake");
			int stakeID;
			if (yutils::strToInt2(stake, stakeID) && stakeID >= 0 && stakeID < static_cast<int>(mBets[game->GetPlayboardMode()].GetValidStakes().size()))
				game->StakeInfo = { stakeID, mBets[game->GetPlayboardMode()].GetStake(stakeID), 1 };
		}
	}

	// If a stake is already selected, then we're ok!
	if (game->StakeInfo.ID != -1 && game->StakeInfo.Stake)
		return;

	// If we only have one stake or we aren't forcing a stake select, go to default stake
	if (mBets[game->GetPlayboardMode()].GetValidStakes().size() == 1 || !bForceStakeSelect)
	{
		const int stakeID = (mDefaultStake >= mBets[game->GetPlayboardMode()].GetValidStakes().size()) ? 0U : mDefaultStake;
		SetStake(*game, stakeID);
		json stakeChangeData(json::value_t::object);
		stakeChangeData["id"] = stakeID;
		stakeChangeData["stake"] = TBaccaratBets::StakeAsJson(*game->StakeInfo.Stake, 1);
		player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
	}
	else
	{
		std::vector<ChoiceQuestion::Choice> choices;
		int i = 0;
		for (auto stake : mBets[game->GetPlayboardMode()].GetValidStakes())
		{
			ChoiceQuestion::Choice thisStake;
			thisStake.Title = "Stake " + std::to_string(i++);
			thisStake.Data = TBaccaratBets::StakeAsJson(*stake, 1);
			choices.push_back(thisStake);
		}
		auto question = std::make_shared<ChoiceQuestion>("stake", "Select a stake", choices);
		player.Ask(question, [this](YProtocolClient& client, QuestionPtr q, const json& answer, json& finalResponse) -> EQuestionResult {
			Player& player = dynamic_cast<Player&>(client);
			auto game = dynamic_cast<TBaccaratGame*>(player.Game().get());
			if (answer.size())
			{
				int stakeID = answer[0].get<uint32_t>();
				SetStake(*game, stakeID);
				json stakeChangeData(json::value_t::object);
				stakeChangeData["id"] = stakeID;
				stakeChangeData["stake"] = TBaccaratBets::StakeAsJson(*game->StakeInfo.Stake, 1);

				Log(Info, "Player selected stake %s", JsonSchema::PrintValueInline(stakeChangeData).c_str());
				player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
				finalResponse = stakeChangeData;
				return EQuestionResult::OK;
			}
			else
			{
				return EQuestionResult::ASK_AGAIN;
			}
		});
	}
}

void TBaccaratHost::ReportChangedNumberOfPlayers()
{
	const auto data = ReportNumOfActivePlayersDto(GetNumPlayersWithMoney(), NumPlayers());

	auto type = DealerAssistClient->Request(PLAYER_COUNT_CHANGED_EVENT_NAME, data.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		if (fut.get().Status == EMessageStatus::ResponseOk)
		{
			Log(Normal, "Received response from live table");
		}
	});
}

void TBaccaratHost::RegisterAdminActions()
{
	RegisterAdminAction(
	  "Login croupier with barcode", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Scanned code %s", params.get<std::string>().c_str());

			  DealerAssistClient->Request(ADD_CARD_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on sending code to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Croupier badge ID. Example: #FFFF0000", "#FFFF0000"));

	RegisterAdminAction("Play one round", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    std::vector<uint32_t> deck;

		                    for (uint32_t d = 0; d < mNumOfDecks; d++)
		                    {    // For Deck
			                    for (size_t i = 1; i <= ECardFace::_size(); ++i)
			                    {    // For each card type
				                    int colors = 100;
				                    for (size_t j = 0; j < ECardSuite::_size(); ++j)
				                    {    // Four of each type for each suit
					                    deck.push_back(colors + i);
					                    colors += 100;
				                    }
			                    }
		                    }
		                    crypto::Shuffle(deck);

		                    BaccaratGameLogic gameLogic = BaccaratGameLogic(ECardRule::Asian);

		                    while (gameLogic.GetDealingPhase() != EBaccaratDealingPhase::Finished)
		                    {
			                    auto cardInt = deck.back();
			                    deck.pop_back();

			                    auto card = FScannedCard(cardInt);
			                    auto side = EBaccaratSide::Banker;

			                    if (gameLogic.GetDealingPhase() == EBaccaratDealingPhase::PlayerFirstCard ||
			                        gameLogic.GetDealingPhase() == EBaccaratDealingPhase::PlayerSecondCard ||
			                        gameLogic.GetDealingPhase() == EBaccaratDealingPhase::PlayerThirdCard)
			                    {
				                    side = EBaccaratSide::Player;
			                    }

			                    gameLogic.AddCard(card.ToInt(), side);

			                    DealerAssistClient->Request(ADD_CARD_EVENT_NAME, card.ToBarcode()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    {
					                    Log(Normal, "Received response on sending code to dealer assist.");
				                    }
			                    });
		                    }

		                    return {};
	                    });

	RegisterAdminAction(
	  "Add one card", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Scanned code %s", params.get<std::string>().c_str());

			  DealerAssistClient->Request(ADD_CARD_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on sending card to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Add card with barcode. Barcode example: QD3, KS2, 4H2", "8S4"));

	RegisterAdminAction("Request table closing", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    FlagStateChangeDto dto = FlagStateChangeDto(EFlagStateType::TableClosingRequested, true);

		                    DealerAssistClient->Request(SET_FLAG_STATE_EVENT_NAME, dto.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on TableClosingRequested.");
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Dealer Void game", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = "ChangeShoe";

		                    DealerAssistClient->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on change shoe.");
			                    }
		                    });

		                    return {};
	                    });

	RegisterAdminAction("Redraw cards", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = EDealerAction(EDealerAction::RedrawCards)._to_string();

		                    DealerAssistClient->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on redraw cards.");
			                    }
		                    });

		                    return {};
	                    });

	RegisterAdminAction(
	  "Change game", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Game name %s", params.get<std::string>().c_str());

			  DealerAssistClient->Request(CHANGE_GAME_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on changing game to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Change game: Baccarat, OpenBaccarat, DragonTiger, OpenDragonTiger", "DragonTiger"));


	RegisterAdminAction("Shoe changed", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = EDealerAction(EDealerAction::ShoeChanged)._to_string();

		                    DealerAssistClient->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on shoe change.");
			                    }
		                    });

		                    return {};
	                    });
}

void TBaccaratHost::OnGamePhaseChanged(const DealerAssistStateUpdateDto& dto)
{
	auto phase = dto.GetPhase();
	auto gameId = dto.GetRoundId();

	{
		ScopedLock lock(CurrentState);

		if (CurrentState->Phase != phase)
		{
			Log(Info, "Phase changed from %s to %s", CurrentState->Phase._to_string(), phase._to_string());
			UpdateValue<uint32_t>(CurrentState->Phase._to_index(), phase._to_index(), "phase");
		}
		if (CurrentState->GameID != gameId)
		{
			UpdateValue<uint64_t>(CurrentState->GameID, gameId, "gameID");
			OpenNewRound(gameId);
		}

		CurrentState->Phase = phase;
		CurrentState->GameID = gameId;

		if (phase == EDealerAssistPhase::BetsClosed)
		{
			OnBetsClosed_AssumeLockedState();
			lock.unlock();
			mBetStatistics->TriggerUpdate();
			mBetStatistics->PauseUpdates();
			ReportChangedNumberOfPlayers();
		}

		if (phase == EDealerAssistPhase::BetsOpen)
		{
			CurrentState->Timestamp = Timestamp();
			auto betCloseAt = CurrentState->GetBetCloseTimeMs(mBetsClosedBehavior);
			lock.unlock();
			UpdateValue<uint64_t>(0, betCloseAt, "betCloseAt");
			mBetStatistics->ResumeUpdates();
			ReportChangedNumberOfPlayers();
		}

		if (phase == EDealerAssistPhase::ShoeChange)
		{
			CurrentState->GameRecords.clear();
			ClearCurrentGame_AssumeLockedState();
		}

		if (phase == EDealerAssistPhase::RoundVoid)
		{
			mBetStatistics->PauseUpdates();
			lock.unlock();
			VoidLiveGame("Game voided by dealer assist");
		}

		if (phase == EDealerAssistPhase::RoundEnd)
		{
			Log(Info, "Received RoundEnd - Calculating winner!");
			const auto winner = CurrentState->BaccaratGame->Evaluate();
			OnWinner_AssumedLockedState(EBaccaratWinner::_from_integral(winner));
		}
	}
}

void TBaccaratHost::OnGameStateChanged(const DealerAssistInitResponseDto& dto)
{
	ScopedLock lock(CurrentState);

	if (dto.mTableState.mRoundID.has_value())
		CurrentState->GameID = *dto.mTableState.mRoundID;

	CurrentState->BaccaratGame->ClearCards();
	if (dto.mTableState.mCards.size() > 0)
	{
		CurrentState->BaccaratGame->SetCurrentStateOfCards(dto.mTableState.mCards);
		Log(Info, "Cards: %s", JsonSchema::PrintValueInline(dto.mTableState.mCards).c_str());
	}

	json gameState = json::value_t::object;

	gameState["gameID"] = CurrentState->GameID;
	gameState["phase"] = CurrentState->Phase._to_index();
	gameState["phaseSubID"] = CurrentState->BaccaratGame->GetDealingPhase();

	auto cards = CurrentState->BaccaratGame->GetCurrentStateOfCards();
	gameState["cards"] = std::move(cards);

	UpdateValue<json>(json(), gameState, "gameState");
}

void TBaccaratHost::OnInitDealerAssist(const DealerAssistInitResponseDto& dto)
{
	Log(Info, "Dealer Assist initialized! %s", JsonSchema::PrintValueInline(dto.ToJSON()).c_str());

	SetDomain(dto.mTableId);
	ScopedLock lock(CurrentState);

	CurrentState->TotalBetTimeMs = dto.mBetTimeMs;
	CurrentState->Timestamp = dto.mTimestamp;
	CurrentState->Phase = dto.mTableState.mPhase;
	if (dto.mTableState.mRoundID.has_value())
		CurrentState->GameID = *dto.mTableState.mRoundID;
	CurrentState->ChatEnabled = dto.mActionsState.bChatEnabled;
	CurrentState->GameRecords = dto.mHistoryRecords;
	mVideoStreamType = dto.mStreamType;
	mVideoStreamUrl = dto.mStreamUrl;
	mVideoStreamId = dto.mStreamId;
	mNumOfDecks = dto.mNumOfDecks;
	mChecksums = dto.mChecksums;

	if (dto.mTableState.mCards.size() > 0)
	{
		CurrentState->BaccaratGame->SetCurrentStateOfCards(dto.mTableState.mCards);
		Log(Info, "Cards: %s", JsonSchema::PrintValueInline(dto.mTableState.mCards).c_str());
	}
}

void TBaccaratHost::OnRoundFailureResolved(Player& player) noexcept
{
	if (player.Game()->IsGameroundActive())
		return;

	uint64_t gid = 0;
	{
		SharedScopedLock lock(CurrentState);
		gid = CurrentState->GameID;
	}

	if (gid)
		player.GameRoundBegin(std::to_string(gid));
}

void TBaccaratHost::OnPlayerDisconnected(Player& player)
{
	Log(Info, "OnPlayerDisconnected - Player has left the game");
	TGameHost::OnPlayerDisconnected(player);

	if (!mBetTimeoutDuration)
		return;

	auto game = dynamic_cast<TBaccaratGame*>(player.Game().get());
	if (game->BetStatus() != EGameBetState::BetPlaced || !game->LastBetTime)
		return;

	const int64_t TimeUntilBetClear = static_cast<int64_t>(game->LastBetTime + mBetTimeoutDuration) - ytime::GetTimeMsec();
	if (TimeUntilBetClear <= 0)
		player.VoidGame(FGameroundError({ "BetsTooOld", "Connection lost with bets that are too old" }), false);
	else
		game->BetTimeout = Container()->Server->SetTimer(TimeUntilBetClear, [ref = player.weak_from_this(), gid = game->GameRound()](const std::error_code& ec) {
			if (ec)
				return;

			auto player = std::dynamic_pointer_cast<Player>(ref.lock());
			if (!player)
				return;

			ScopedLock lock(*player);
			if (player->Game()->GameRound() != gid)
				return;

			if (player->Game()->BetStatus() == EGameBetState::BetPlaced)
				player->VoidGame(FGameroundError({ "BetsTooOld", "Bets cleared because player has old bets and is offline" }), false);
		});
}

void TBaccaratHost::OnPlayerGone(Player& player) noexcept
{
	TGameHost::OnPlayerGone(player);
	Log(Info, "OnPlayerGone - Player has left the game");
}

void TBaccaratHost::UnregisterPlayer(Player& player, const std::optional<FGameroundError>& err)
{
	if (player.Game()->IsGameroundActive())
		player.ProcessGameResult({}, err);
	player.TryToEndAccountingRound();
}

bool TBaccaratHost::DisconnectService()
{
	TLiveGameHost::DisconnectService();

	ClearCurrentGame();

	if (DealerAssistClient)
	{
		DealerAssistClient->Stop();
	}

	return false;
}

// BETTING
const TBaccaratBets& TBaccaratHost::GetBets(const EBaccaratPlayboardMode mode) const
{
	return mBets[mode];
}

uint64_t TBaccaratHost::VerifyBets(const YGameClient& client, const yprotocol::Request& req, json& response) const
{
	const BaccaratState state = CurrentState.getCopy();

	if (!state.GameID || req.GetParam("gameID").get<uint64_t>() != state.GameID)
		throw BetRequestError(req, "Invalid game ID (playing " + std::to_string(state.GameID) + ")!", FBetErrorInfo("InvalidGame"));

	if (!client.Game()->IsGameroundActive())
		throw BetRequestError(req, "Not participating in a game round. Please wait for a round to start.", FBetErrorInfo("InvalidGame"));

	if (state.Phase != EDealerAssistPhase::BetsOpen)
		throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("BetsClosed"));

	/*{
	    SharedScopedLock lock2(mPlayers);
	    if (mGameToAccountFor)
	        throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("InvalidGame"));
	}*/

	TBaccaratGame* gameInst = static_cast<TBaccaratGame*>(client.Game().get());
	const TBaccaratBets& bets = GetBets(gameInst->GetPlayboardMode());

	try
	{
		// Will throw a BetParseError if unsuccessful
		BaccaratBetAmounts betAmounts;
		try
		{
			betAmounts = BaccaratBetAmounts::FromUserJSON(req.GetParam(), gameInst->CreditMultiplier);
		}
		catch (const BetParseError& err)
		{
			throw BetRequestError(req, err.what(), FBetErrorInfo("InvalidBetObject", err.Context));
		}

		baccarat::FStakeInfo stake = gameInst->StakeInfo;
		stake.Multiplier *= gameInst->CreditMultiplier;

		const FBetVerifyResult verifiedBets = bets.VerifyBets(betAmounts, stake, 0);
		if (!verifiedBets.Violation)
		{
			if (state.Phase != EDealerAssistPhase::BetsOpen)
				throw BetRequestError(req, "Cannot bet at this time! Bets are not open.", FBetErrorInfo("BetsClosed"));

			gameInst->LastVerifiedBets = betAmounts;
			response = betAmounts.BetsAsJSON(gameInst->CreditMultiplier);
			return verifiedBets.TotalBet;
		}
		else if (*verifiedBets.Violation == EBetRuleViolation::BadSetup)
		{
			throw BetRequestError(req, "Invalid stake", FBetErrorInfo("InternalError"));
		}
		else
		{
			FBetErrorInfo err("BetRuleViolation");
			err.Details = json(json::value_t::object);
			err.Details["x"] = verifiedBets.ErrorX;
			err.Details["violation"] = verifiedBets.Violation->_to_string();
			throw BetRequestError(req, verifiedBets.Message, err);
		}
	}
	catch (const json::exception& err)
	{
		throw BetRequestError(req, "Problem executing commitBets: " + std::string(err.what()) + "!", FBetErrorInfo("InternalError"));
	}
}

void TBaccaratHost::BetPlaced(YGameClient& client, const FBetPlacedResult& betResult, const json& response)
{
	// leave previous possible bet on the table, undo this one
	TBaccaratGame& game = dynamic_cast<TBaccaratGame&>(*client.Game());

	if (!betResult.Error)
	{
		game.ConfirmedBets = game.LastVerifiedBets;
		game.LastBetTime = ytime::GetTimeMsec();
	}

	game.LastVerifiedBets.Clear();
}


// Handling phases
void TBaccaratHost::AddCard(int card)
{
	ScopedLock stateLock(CurrentState);
	if (CurrentState->Phase != EDealerAssistPhase::DealingCards)
		throw InternalError(std::string("Can not add card, game is in wrong state: ") + CurrentState->Phase._to_string());

	EBaccaratDealingPhase dealingPhase = EBaccaratDealingPhase::_from_integral(CurrentState->BaccaratGame->GetDealingPhase());
	Log(Info, "Dealing to side %s card with id %d", dealingPhase._to_string(), card);
	ProcessCardAddition_AssumeLocked(card, dealingPhase);
}

void TBaccaratHost::ProcessCardAddition_AssumeLocked(int card, EBaccaratDealingPhase dealingPhase)
{
	try
	{
		CurrentState->BaccaratGame->AddCard(card);
	}
	catch (const std::exception& e)
	{
		Log(Error, "Error while processing card addition: %s", e.what());
	}

	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		players = &mPlayers;
	}

	ParallelFor(players.begin(), players.end(), [card](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		player.second->Game()->PushRandNumber(json(static_cast<int64_t>(card)));
		TBaccaratGame* playerGame = static_cast<TBaccaratGame*>(player.second->Game().get());
		playerGame->AddCard(card);

		if (playerGame->GetDealingPhase() == EBaccaratDealingPhase::Finished)
			playerGame->Evaluate();
	});

	if (CurrentState->BaccaratGame->GetDealingPhase() == EBaccaratDealingPhase::Finished)
	{
		Log(Info, "Calculating winner!");
		auto winner = CurrentState->BaccaratGame->Evaluate();
		SendCard(card, dealingPhase, winner);
	}
	else
	{
		SendCard(card, dealingPhase._to_integral());
	}
}

void TBaccaratHost::SendCard(int card, int position, std::optional<int> winner)
{
	json cardObj(json::value_t::object);
	cardObj["name"] = "addNewCard";
	cardObj["index"] = position;
	cardObj["card"] = card % 1000;    // Card without deck number

	if (winner)
		cardObj["winner"] = *winner;

	Log(Info, "Sending card %s to players", JsonSchema::PrintValueInline(cardObj).c_str());

	for (const auto& player : &mPlayers)
	{
		ScopedLock playerLock(*player.second);
		player.second->TriggerEvent(*CardAddedEvent, json(cardObj));
	}
}

template <typename T>
void TBaccaratHost::UpdateValue(const T& oldValue, T newValue, const std::string& eventTrigger)
{
	if (newValue != oldValue)
	{
		json phaseChangeData;
		phaseChangeData["name"] = eventTrigger;
		phaseChangeData["old"] = oldValue;
		phaseChangeData["new"] = newValue;
		TriggerEvent(*GameStateChangedEvent, phaseChangeData);
	}
}

void TBaccaratHost::OpenNewRound(const uint64_t roundId)
{
	std::string newRound = std::to_string(roundId);

	ForEachPlayer([&newRound](yserver::Player& player) {
		ScopedLock lock(player);
		if (player.Game()->IsGameroundActive() && player.Game()->BetStatus() < EGameBetState::PendingResult)
		{
			std::optional<FGameroundError> err;
			if (player.Game()->BetStatus() == EGameBetState::BetPlaced)
				err = FGameroundError({ "NewRoundStarted", "A new game round was started!" });
			player.ProcessGameResult({}, err);
		}

		if (!newRound.empty() && !player.Game()->IsGameroundActive())
			player.GameRoundBegin(newRound);
	});
}

void TBaccaratHost::OnBetsClosed_AssumeLockedState()
{
	uint64_t accountForGame = 0;
	{
		accountForGame = CurrentState->GameID;
	}

	// Unregister players who's accounting didn't go through
	ScopedLock lock(mPlayers);
	YPlayerList playersToIterate = &mPlayers;
	mGameToAccountFor = accountForGame;
	CurrentState->Phase = EDealerAssistPhase::DealingCards;
	CurrentState->DealingPhase = EBaccaratDealingPhase::PlayerFirstCard;
	lock.unlock();

	std::atomic<size_t> numBets = 0;

	ParallelFor(playersToIterate.begin(), playersToIterate.end(),
	            [this, &numBets, gameIDStr = std::to_string(accountForGame)](const std::pair<std::string, PlayerPtr>& player) {
		            CommitBetsResult result;
		            ScopedLock lock(*player.second);
		            auto game = std::dynamic_pointer_cast<TBaccaratGame>(player.second->Game());
		            if (game->GameRound() != gameIDStr)
			            return;

		            if (game->BetStatus() == EGameBetState::BetPlaced)
		            {
			            result = player.second->CommitBets();
		            }
		            else
		            {
			            result.Message = "No bets were placed";
		            }

		            if (game->BetTimeout)
		            {
			            game->BetTimeout->cancel();
			            game->BetTimeout.reset();
		            }

		            if (result.Success())
		            {
			            numBets++;
		            }
		            else
		            {
			            if (game->BetStatus() != EGameBetState::Idle)
				            UnregisterPlayer(*player.second, FGameroundError({ EVoidReason(EVoidReason::BetFailed)._to_string(), result.Message }));
			            else
				            UnregisterPlayer(*player.second, {});
		            }
	            });

	Log(Info, "Bets closed in game %lu with %lu bets", accountForGame, numBets.load());
}

void TBaccaratHost::OnWinner_AssumedLockedState(EBaccaratWinner winner)
{
	CurrentState->Winner = winner;
	CurrentState->GameRecords.push_back(CurrentState->BaccaratGame->GetGameRecord());

	UpdateValue<json>(json(), winner._to_integral(), "winner");

	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	const size_t numPlayersTotal = players.size();
	if (!numPlayersTotal)
	{
		Log(Info, "Got win number %s in game %i", winner._to_string(), accountForGame);
		ClearCurrentGame_AssumeLockedState();
		return;
	}

	const std::string accountForGameRound = std::to_string(accountForGame);

	for (auto it = players.begin(); it != players.end();)
	{
		yutils::ScopedAction skipPlayer([&it, &players]() { it = players.erase(it); });
		ScopedLock lock(*it->second);
		TBaccaratGame* playerGame = static_cast<TBaccaratGame*>(it->second->Game().get());

		if (playerGame->BetStatus() == EGameBetState::Idle || accountForGameRound != playerGame->GameRound() || it->second->Game()->IsVoid())    // not registered!
			continue;

		it->second->Log(Debug, "Processing Baccarat winner...");
		if (playerGame->BetStatus() != EGameBetState::PendingResult)
		{
			it->second->SetPersistence(EPlayerPersistenceMode::Game, false);
			it->second->Log(Warning, "Game %lu was not started correctly. This should not happen and indicates a problem somewhere.", accountForGame);
			UnregisterPlayer(*it->second, FGameroundError({ "GameStartFailed", "Game not started correctly" }));
			continue;
		}

		skipPlayer.reset();

		playerGame->WonResult =
		  mBets[playerGame->GetPlayboardMode()].CalculateWins(playerGame->ConfirmedBets, playerGame->StakeInfo, CurrentState->BaccaratGame->GetGameLogic());
		it++;
	}

	Log(Info, "Got win number %d in game %lu. Processing game result for %lu/%lu players...", winner, accountForGame, players.size(), numPlayersTotal);

	const std::string gameroundMessage = "The winning number is " + std::to_string(winner);
	std::atomic<double> wonTotalCents = 0.0;
	tbb::concurrent_unordered_map<AccountID, std::pair<double, double>, AccountIDHash> winEuroCents;
	ParallelFor(players.begin(), players.end(), [&](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);

		TBaccaratGame* playerGame = static_cast<TBaccaratGame*>(player.second->Game().get());

		const uint64_t WonCredits = playerGame->WonResult.TotalWon;
		if (winner != EBaccaratWinner::Null)
			player.second->ProcessGameResult(WonCredits);
		else
			player.second->ProcessGameResult(WonCredits, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), gameroundMessage }));

		if (WonCredits && !player.second->Account()->IsDemo())
		{
			double rate;
			if (Container()->Server->CurrencyAPI->GetExchangeRate(ECurrency(ECurrency::EUR)._to_string(), player.second->Account()->Currency(), rate))
			{
				const double wonEuroCents = WonCredits * 1e2 * player.second->Account()->Denomination() / rate;
				wonTotalCents += wonEuroCents;
				auto emplaced = winEuroCents.emplace(player.second->Account()->ID(), std::pair(wonEuroCents, rate));
				if (!emplaced.second)
					emplaced.first->second.first += wonEuroCents;
			}
		}
		Log(Debug, "Processing win done for player %s.", player.second->Account()->ID().Username.c_str());
	});

	ClearCurrentGame_AssumeLockedState();

	std::vector<std::pair<std::string, uint64_t>> winList;
	winList.reserve(winEuroCents.size());
	for (const auto& [aid, win] : winEuroCents) { winList.push_back({ aid.AsString(), static_cast<uint64_t>(std::round(win.first)) }); }
	std::sort(std::execution::par, winList.begin(), winList.end(), [](const auto& l, const auto& r) -> bool { return l.second > r.second; });
	json winReportList(json::value_t::object);
	size_t n = 0;
	for (auto it = winList.begin(); it != winList.end() && n < mPlayersPerPage; it++)
	{
		winReportList[it->first] = it->second;
		n++;
	}

	json winReport(json::value_t::object);
	winReport["totalWon"] = wonTotalCents.load();
	winReport["registeredPlayers"] = players.size();
	winReport["winList"] = winReportList;
	TriggerEvent(*WinReportEvent, winReport);
}

void TBaccaratHost::OnGameRoundBegin(YGameClient& player)
{
	TGameHost::OnGameRoundBegin(player);

	TBaccaratGame* gamePtr = dynamic_cast<TBaccaratGame*>(player.Game().get());
	gamePtr->ClearUncommittedBets();
}

void TBaccaratHost::OnGameRoundEnd(YGameClient& player, const GameRoundSnapshot& game)
{
	TGameHost::OnGameRoundEnd(player, game);
	TBaccaratGame* gamePtr = dynamic_cast<TBaccaratGame*>(player.Game().get());
	gamePtr->Unregister(game.VoidReason.has_value());
}

void TBaccaratHost::VoidGame(YGameClient& client, EVoidReason reason, const std::string& message)
{
	client.Log(Warning, "Voiding game for %s: %s", reason._to_string(), message.c_str());

	if (Player* player = dynamic_cast<Player*>(&client))
		UnregisterPlayer(*player, FGameroundError({ reason._to_string(), message }));
}

void TBaccaratHost::ClearCurrentGame()
{
	ScopedLock lock(CurrentState);
	ClearCurrentGame_AssumeLockedState();
}

void TBaccaratHost::ClearCurrentGame_AssumeLockedState()
{
	CurrentState->BaccaratGame->FinishGameRound();

	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		players = &mPlayers;
	}

	ParallelFor(players.begin(), players.end(), [](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		TBaccaratGame* playerGame = static_cast<TBaccaratGame*>(player.second->Game().get());
		playerGame->FinishGameRound();
	});
}

void TBaccaratHost::VoidLiveGame(const std::string& reason)
{
	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lockPlayers(mPlayers);
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	if (accountForGame)
	{
		{
			ScopedLock lock(CurrentState);
			bCurrentRoundVoid = (accountForGame == CurrentState->GameID);
		}

		Log(Warning, "No result for game %lu (%s). Will void any bets made by the existing %lu players.", accountForGame, reason.c_str(), players.size());
	}
	else
	{
		{
			SharedScopedLock lock(CurrentState);
			accountForGame = CurrentState->GameID;
		}

		if (accountForGame)
			Log(Warning, "Not in a game currently, but voiding it anyway (%s) to clear any placed bets made by the existing %lu players.", reason.c_str(),
			    players.size());
	}

	if (!accountForGame)
		return;

	ParallelFor(players.begin(), players.end(), [this, gid = std::to_string(accountForGame), &reason](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		if (gid == player.second->Game()->GameRound())
			VoidGame(*player.second, EVoidReason::NoGameResult, reason);
	});

	ClearCurrentGame();
}

bool TBaccaratHost::IsValidStake(uint32_t stakeIdx) const
{
	switch (mStakeAccessMode)
	{
		case EHostAccessMode::All: return true;
		case EHostAccessMode::OptIn: return mStakeAccess.contains(stakeIdx);
		case EHostAccessMode::OptOut: return !mStakeAccess.contains(stakeIdx);
	}
	return false;
}

void TBaccaratHost::SetStake(TBaccaratGame& game, uint32_t stakeID) const
{
	if (stakeID >= mBets[game.GetPlayboardMode()].GetAllStakes().size())
		return;

	game.StakeInfo.ID = stakeID;
	game.StakeInfo.Stake = mBets[game.GetPlayboardMode()].GetStake(stakeID);
	game.StakeInfo.Multiplier = 1;

	if (!game.HasBetsInGame())
	{
		/*if (game.StakeInfo.Stake)
		{
		    for (size_t idx = 0; idx < game.StakeInfo.Stake->ChipValues.size(); idx++)
		        game.SavedChipValues[idx] = game.StakeInfo.Multiplier * game.StakeInfo.Stake->ChipValues[idx];
		}*/
	}
}

void TBaccaratHost::OnBetStatisticsUpdate(const BetStatisticsDto& stats)
{
	if (bBetStatisticsPerHost && bBetStatisticsEnabled)
		TriggerEvent(*BetStatisticsUpdateEvent, stats.ToJSON());

	if (DealerAssistClient)
		DealerAssistClient->Request(POST_BET_STATISTICS, mBetStatistics->GetBetStatistics(mDealerAssistHostIdentifier).ToJSON());
}

void TBaccaratHost::GetGameDetails(GameInformation& info) const
{
	TGameHost::GetGameDetails(info);

	bool isCommission = info.GetConfig("commission").get<bool>();
	const EBaccaratPlayboardMode mode = isCommission ? EBaccaratPlayboardMode::Commission : EBaccaratPlayboardMode::NoCommission;

	auto totalRTP = mBets[mode].GetTotalRTP(mDefaultStake, mNumOfDecks);

	info.RTP = totalRTP;

	for (const auto& stake : mBets[mode].GetAllStakes())
	{
		if (info.MaxBet == 0 || stake->PlayboardLimitMax > info.MaxBet)
			info.MaxBet = stake->PlayboardLimitMax;

		if (info.MinBet == 0 || stake->PlayboardLimitMin < info.MinBet)
			info.MinBet = stake->PlayboardLimitMin;

		for (const auto& betType : stake->Multipliers) { info.MaxWin = std::max(info.MaxWin, static_cast<uint64_t>(betType.second.Items.end()->second)); }
	}

	Log(Normal, "Max bet: %i, Min bet: %i, Max win: %i", info.MaxBet, info.MinBet, info.MaxWin);
}

json TBaccaratHost::GetDescriptorInfo(const FModuleDescriptorOptions& options) const
{
	json conf(json::value_t::object);
	conf["stakeChangeAllowed"] = bAllowStakeChange;
	conf["stream"] = GetVideoStreamInfo();

	return conf;
}

json TBaccaratHost::GetVideoStreamInfo() const
{
	json stream(json::value_t::object);

	json impliedSource(json::value_t::object);
	impliedSource["url"] = mVideoStreamUrl;
	impliedSource["id"] = mVideoStreamId;
	stream["source"] = std::move(impliedSource);
	stream["type"] = mVideoStreamType._to_string();

	return stream;
}

// Jackpot
bool TBaccaratHost::AllowJackpot() const
{
	return TGameHost::AllowJackpot();
}

std::shared_ptr<jackpot::TJackpotModule> TBaccaratHost::Jackpot(const std::shared_ptr<provider::TCasinoProvider>& provider, const StaticGameInformation& game,
                                                                bool bDemo) const noexcept
{
	return std::shared_ptr<jackpot::TJackpotModule>();
}

std::map<std::string, std::string> TBaccaratHost::Checksums(const GameHostGameInformation& info, const std::optional<std::string>& launchOption) const
{
	std::map<std::string, std::string> Ret = TGameHost::Checksums(info, launchOption);

	for (auto [path, checksum] : mChecksums) Ret[path] = checksum;


	Log(Info, "Checksums: %s", JsonSchema::PrintValueInline(Ret).c_str());

	return Ret;
}

// Restoring game rounds
void TBaccaratHost::TryRoundRestore(YGameClient& client, const GameRoundSnapshot& snap)
{
	TGameHost::TryRoundRestore(client, snap);
	TBaccaratGame& game = static_cast<TBaccaratGame&>(*client.Game());

	if (!snap.GeneratedRandoms.empty())
		throw yprotocol::InternalError("Gameround data indicates the game is completed!");

	try
	{
		game.ConfirmedBets = BaccaratBetAmounts::FromJSON(snap.ExtraData, game.CreditMultiplier);
	}
	catch (const BetParseError& e)
	{
		throw yprotocol::InternalError(e.what());
	}

	if (game.ConfirmedBets.Empty())
		throw yprotocol::InternalError("There are no placed bets to restore!");

	if (auto stake = FindMember(snap.ExtraData, "stake"))
		if (stake && stake->is_number_integer())
			SetStake(game, stake->get<int>());
}

void TBaccaratHost::TryRoundResume(Player& player, const GameRoundSnapshot& snap)
{
	TGameHost::TryRoundResume(player, snap);

	if (!player.Game()->IsGameroundActive())
		return;

	TBaccaratGame& game = static_cast<TBaccaratGame&>(*player.Game());
	const auto latestState = CurrentState.getCopy();

	if (!game.StakeInfo.Stake || (snap.ExtraData.contains("stake") && game.StakeInfo.ID != snap.ExtraData["stake"].get<int>()))
		throw yprotocol::InternalError("The stake used in the game is not available anymore");

	const std::string latestGameID = std::to_string(latestState.GameID);
	// if not the current gameround or we already have the win number of the current gameround
	if (game.GameRound() != latestGameID || latestState.Phase == EDealerAssistPhase::RoundEnd)
	{
		EBaccaratWinner winner = EBaccaratWinner::Null;

		if (game.GameRound() == latestGameID)
		{
			winner = EBaccaratWinner::_from_integral(latestState.BaccaratGame->GetWinner());
		}
		else
		{
			const u_int64_t GameroundID = yutils::strToInt(game.GameRound(), 0);
			auto it = std::find_if(latestState.GameRecords.begin(), latestState.GameRecords.end(),
			                       [GameroundID](const GameRecordDto& record) { return record.GameRoundId == GameroundID; });

			if (it != latestState.GameRecords.end())
			{
				if (!it->Winners.empty())
				{
					winner = EBaccaratWinner::_from_integral(it->Winners.front());
				}
			}

			else
				Log(Critical, "No information exists in history for gameround %s", game.GameRound().c_str());
		}

		if (game.BetStatus() == EGameBetState::BetPlaced)
		{
			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}

		game.WonResult = mBets[game.GetPlayboardMode()].CalculateWins(game.ConfirmedBets, game.StakeInfo, CurrentState->BaccaratGame->GetGameLogic());

		if (winner != EBaccaratWinner::Null)
			player.ProcessGameResult(game.WonResult.TotalWon);
		else
			player.ProcessGameResult(game.WonResult.TotalWon, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), "No win number read" }));
	}
	else
	{
		SharedScopedLock lock(mPlayers);
		const bool bWaitingForWinNumber = mGameToAccountFor;
		lock.unlock();

		// if bBetsClosed matches game.bHasAccounted bets, then do nothing, game will be finished after OnRegister happens and after the win number is read
		const bool bHasAccountedBets = game.BetStatus() >= EGameBetState::PendingResult;
		if (bWaitingForWinNumber != bHasAccountedBets)
		{
			if (bHasAccountedBets)
				throw yprotocol::InternalError("Unfinished game bets were accounted but server has not accounted bets for this game yet!");

			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}
	}
}

void TBaccaratHost::PostChatMessage(const std::string& message, const AccountID& playerId)
{
	// Check if message contains URL
	if (MyUtils::ContainsURL(message))
		return;

	std::string username = playerId.AsString();
	auto players = mCachedPlayerList.getCopy();
	auto it = players.find(playerId);

	if (it != players.end())
		username = it->second.Nickname;

	ChatMessageDto dto = ChatMessageDto(message, username, playerId, ytime::GetSystemTimeMsec(), false);

	DealerAssistClient->Request(POST_CHAT_MESSAGE_EVENT_NAME, dto.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		if (fut.get().Status == EMessageStatus::ResponseOk)
		{
			Log(Normal, "Received response on chat posting");
		}
	});
}

void TBaccaratHost::StopTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer)
{
	if (auto task = timer.lock())
	{
		task->Disable();
	}
}
