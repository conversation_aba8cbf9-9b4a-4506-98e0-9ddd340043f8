#include "hosts/baccarat/TVirtualBaccaratGame.h"

#include <hosts/baccarat/TVirtualBaccaratHost.h>

#include "Cryptography.h"
#include "YServer.h"

using namespace yserver;
using namespace yserver::gamehost;

DEFINE_LOG_CATEGORY(LogVirGameBaccarat, "virt-baccarat-game")

using namespace yserver::gamehost::baccarat;

VirtualBaccaratGame::VirtualBaccaratGame(const std::string& host_uid, const GameInformation& info) : TBaseVirtualCardGame(host_uid, info)
{
	mCommission = info.GetConfig("commission").get<bool>();
	mOpenBaccarat = info.GetConfig("open-baccarat").get<bool>();
	bShowCardFaces = info.GetConfigOptional("free-hands.show-card-faces", true).get<bool>();

	LoadDealingConfiguration(info);

	TLOG(LogVirGameBaccarat, EVerbosity::Info, "Number of free hands at begining: %d!", mFreeHandsAtStart);
	TLOG(LogVirGameBaccarat, EVerbosity::Info, "Number of free hands during game: %d!", mFreeHands);

	bool showCutCard = info.GetConfig("deck-shuffling.show-cut").get<bool>();
	mReshuffleMode = mNumbersOfDecks == 0 ? EReshuffleMode::EveryHand : (showCutCard ? EReshuffleMode::CutCard : EReshuffleMode::CutCardHidden);

	mGameLogic = BaccaratGameLogic(showCutCard, ECardRule::Asian);

	ShuffleCards();
	mPlacedBet.Clear();
}

void VirtualBaccaratGame::LoadDealingConfiguration(const GameInformation& info)
{
	mCardDealingConfig = info.GetConfig("dealing-delay");
}

const json& VirtualBaccaratGame::GetCardDealingConfig() const
{
	return mCardDealingConfig;
}

void VirtualBaccaratGame::FinishGameRound()
{
	mGameLogic.ClearGame();
	mPlacedBet.Clear();
	CheckReshuffle();
}

bool VirtualBaccaratGame::ShouldPlayerDrawThirdCard() const
{
	return mGameLogic.ShouldPlayerDrawThirdCard();
}

bool VirtualBaccaratGame::ShouldBankerDrawThirdCard() const
{
	return mGameLogic.ShouldBankerDrawThirdCard();
}

uint32_t VirtualBaccaratGame::DrawAndAddCard(const EBaccaratSide side, const uint32_t position)
{
	if (uint32_t card = DrawCard(); card == 0)
	{
		AddCutCard(position);
		card = DrawCard();
		AddCard(card, side);
		return card;
	}
	else
	{
		AddCard(card, side);
		return card;
	}
}

void VirtualBaccaratGame::AddCard(const uint32_t card, const EBaccaratSide side)
{
	mGameLogic.AddCard(card, side);
}

void VirtualBaccaratGame::AddCutCard(const uint32_t position)
{
	mGameLogic.AddCutCard(position);
}

EBaccaratWinner VirtualBaccaratGame::Evaluate()
{
	auto winners = mGameLogic.Evaluate();
	return EBaccaratWinner::_from_integral(winners.front());
}

json VirtualBaccaratGame::GetGameResult() const
{
	return mGameLogic.GetGameResult();
}

std::vector<json> VirtualBaccaratGame::GetCards()
{
	return mGameLogic.GetCards(true);
}
EBaccaratWinner VirtualBaccaratGame::GetWinner() const
{
	auto winner = mGameLogic.GetWinner();

	if (winner)
		return EBaccaratWinner::_from_integral(*winner);

	return EBaccaratWinner::Null;
}

// void SetLock(Player& player, ERouletteLockReasons lock, bool bActive);
json VirtualBaccaratGame::VirtualBaccaratGame::GetGameRoundData() const
{
	TLOG(LogVirGameBaccarat, EVerbosity::Info, "VirtualBaccaratGame::GetGameRoundData()");
	if (mGameLogic.GetWinner() == EBaccaratWinner::Null)
		return {};

	json data = mPlacedBet.BetsAsJSON(CreditMultiplier);
	data["multiplier"] = CreditMultiplier;

	if (StakeInfo.Stake)
	{
		json chipVals(json::value_t::array);
		for (uint64_t chipVal : StakeInfo.Stake->ChipValues) { chipVals.push_back(chipVal == MAX_BET_CHIP_VALUE ? chipVal : (chipVal * StakeInfo.Multiplier)); }
		data["chipValues"] = std::move(chipVals);
		data["stake"] = StakeInfo.ID;
	}
	else
		data["chipValues"] = json();

	auto winner = mGameLogic.GetWinner();
	if (winner && winner != EBaccaratWinner::Null)
	{
		data["result"] = mGameLogic.GetGameRecord().ToJSON();
	}

	data["cards"] = mGameLogic.GetCards();

	return data;
}

void VirtualBaccaratGame::Restore(const GameRoundSnapshot& snap)
{
	TLOG(LogVirGameBaccarat, EVerbosity::Info, "VirtualBaccaratGame::Restore");
	GameInstance::Restore(snap);

	if (snap.ExtraData.is_null())
		return;

	if (snap.ExtraData.contains("multiplier"))
		CreditMultiplier = snap.ExtraData["multiplier"].get<uint32_t>();
	else
		CreditMultiplier = 1;

	if (snap.ExtraData.contains("stake"))
		StakeInfo.ID = snap.ExtraData["stake"].get<int>();
}

void VirtualBaccaratGame::PlaceBets(BaccaratBetAmounts placedBet)
{
	bInitialGamePlayed = true;
	mPlacedBet = placedBet;
	mFreeHandsCount = 0;
}

BaccaratBetAmounts VirtualBaccaratGame::GetPlacedBets() const
{
	return mPlacedBet;
}

uint32_t VirtualBaccaratGame::GetGoldenCard()
{
	return mGameLogic.GetGoldenCard();
}

const baccarat::BaccaratGameLogic& VirtualBaccaratGame::GetGameLogic() const
{
	return mGameLogic;
}

bool VirtualBaccaratGame::IsCommission() const
{
	return mCommission;
}

bool VirtualBaccaratGame::IsOpenBaccarat() const
{
	return mOpenBaccarat;
}

bool VirtualBaccaratGame::CanPlayFreeHand() const
{
	if (mFreeHands == -1)
		return true;
	else if (!bInitialGamePlayed && mFreeHandsAtStartCount < mFreeHandsAtStart)
		return true;
	else if (bInitialGamePlayed && int32_t(mFreeHandsCount) < mFreeHands)
		return true;
	else
		return false;
}

json VirtualBaccaratGame::PlayFreeHandRound()
{
	DrawAndAddCard(EBaccaratSide::Player, 0);
	DrawAndAddCard(EBaccaratSide::Banker, 1);
	DrawAndAddCard(EBaccaratSide::Player, 2);
	DrawAndAddCard(EBaccaratSide::Banker, 3);


	if (mGameLogic.ShouldPlayerDrawThirdCard())
	{
		DrawAndAddCard(EBaccaratSide::Player, 4);
	}

	if (mGameLogic.ShouldBankerDrawThirdCard())
	{
		DrawAndAddCard(EBaccaratSide::Banker, 5);
	}

	mGameLogic.Evaluate();
	json result = mGameLogic.GetFreeHandGameResult(bShowCardFaces);

	FinishGameRound();

	if (bInitialGamePlayed)
	{
		mFreeHandsCount++;
	}
	else
	{
		mFreeHandsAtStartCount++;
	}

	return result;
}

EBaccaratSide VirtualBaccaratGame::GetRandomSide()
{
	if (mRandomSide.has_value())
		return mRandomSide.value();

	auto sideIndex = crypto::GetRandomInRange(0, 1);
	mRandomSide = EBaccaratSide::_from_index_unchecked(sideIndex);
	return mRandomSide.value();
}

uint8_t VirtualBaccaratGame::GetNumberOfCardsOnTable() const
{
	return mGameLogic.GetNumberOfCardsOnTable();
}

void VirtualBaccaratGame::OnDealFirstCards(const boost::system::error_code& ec, const std::shared_ptr<TVirtualBaccaratHost>& host,
                                           const std::shared_ptr<YGameClient>& client)
{
	// Randomly select a side to send the first card
	const EBaccaratSide side = GetRandomSide();
	const auto firstCardPosition =
	  side == EBaccaratSide::Player ? EBaccaratDealingPhase(EBaccaratDealingPhase::PlayerFirstCard) : EBaccaratDealingPhase(EBaccaratDealingPhase::BankerFirstCard);
	const auto secondCardPosition =
	  side == EBaccaratSide::Player ? EBaccaratDealingPhase(EBaccaratDealingPhase::PlayerSecondCard) : EBaccaratDealingPhase(EBaccaratDealingPhase::BankerSecondCard);

	const auto firstCard = DrawAndAddCard(side, firstCardPosition._to_index());
	const auto secondCard = DrawAndAddCard(side, secondCardPosition._to_index());

	host->SendCardToClient(*client, firstCard, firstCardPosition._to_index());
	host->SendCardToClient(*client, secondCard, secondCardPosition._to_index());

	// If player has active open bet type than we should open bets again, otherwise we should deal remaining cards
	if (GetPlacedBets().HasActiveOpenGameBet())
	{
		host->UpdateGamePhase(*client, *this, dealer_assist::EDealerAssistPhase::Decision);
	}
	else
		host->DealLastCardsAndCloseGame(*client, *this);
}

bool VirtualBaccaratGame::IsPlayerDecisionNeeded()
{
	return ConfirmedBets.HasActiveOpenGameBet();
}
