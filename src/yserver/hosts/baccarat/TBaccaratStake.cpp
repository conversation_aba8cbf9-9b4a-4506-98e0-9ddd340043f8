//
// Created by <PERSON><PERSON><PERSON> on 16. 11. 23.
//
#include "hosts/baccarat/TBaccaratStake.h"

#include <format>

#include "MyUtils.h"
#include "YSharedTypes.h"

using namespace yserver::gamehost::baccarat;

TBaccaratStake::FieldLimit TBaccaratStake::GetFieldLimit(EBaccaratBetType fieldType) const
{
	return LimitsTable[fieldType._to_index()];
}

bool TBaccaratStake::IsValid(const bool isCommission) const
{
	for (const uint64_t& chipVal : ChipValues)
	{
		if (isCommission && chipVal % 20 != 0)    // If chip is not divisible by 20 (5% commission), it's invalid
			return false;
		if (!isCommission && chipVal % 2 != 0)    // If chip is not divisible by 2 (in case of no commission), it's invalid
			return false;
		if (!IsMultiplierValid(chipVal))    // If chip is not divisible by any of the multipliers represented as fraction, it's invalid
			return false;
		if (chipVal)
			return true;
	}
	// if all the chip values are 0, it's an invalid stake for sure
	return false;
}

bool TBaccaratStake::IsMultiplierValid(const uint64_t chipValue) const
{
	return true;
}

const JsonSchema& TBaccaratStake::LiveStakeSchema()
{
	static const JsonSchema SuitedMatchMultiplierSchema(
	  { { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Pair)._to_string(), JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - Pair", 3) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::SuitedPair)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - SuitedPair", 9) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::TwoPairs)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - TwoPairs", 20) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::FourCardsRank)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - FourCardsRank", 80) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::SuitedAndUnsuitedPairs)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - SuitedAndUnsuitedPairs", 400) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::TwoSuitedPairs)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on SuitedMatch - TwoSuitedPairs", 1000) } });

	static const JsonSchema LuckySixMultiplierSchema({ { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Lucky6TwoCards)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Lucky6 - with two cards", 12) },
	                                                   { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Lucky6ThreeCards)._to_string(),
	                                                     JsonSchema(json::value_t::number_unsigned, "Multiplier on Lucky6 - with three cards", 20) } });

	static const JsonSchema Schema(
	  { { "maxNumBets", JsonSchema(json::value_t::number_unsigned, "The maximum number of bet fields that can be bet in a single game (0 for unlimited)", 0) },
	    { "tableWinLimit", JsonSchema(json::value_t::number_unsigned, "The maximum number of credits that can be won in a single game (0 for unlimited)", 0) },
	    { "minBet", JsonSchema(json::value_t::number_unsigned, "The minimum total bet", 0) },
	    { "maxBet", JsonSchema(json::value_t::number_unsigned, "The maximum total bet (0 for unlimited)", 0) },
	    { "chipValues", JsonSchema(json::value_t::array, "The chip values in credits", json::array({ 1, 2, 5, 10, 20 }))
	                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The value of a chip in credits", 1), 5, 5) },
	    { "betTypes",
	      JsonSchema({ { EBaccaratBetType(EBaccaratBetType::Player)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on PLAYER", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on PLAYER (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                "Limits for bets on Player") },
	                   { EBaccaratBetType(EBaccaratBetType::Banker)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on BANKER", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on BANKER (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) } },
	                                "Limits for Banker") },
	                   { EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OPEN PLAYER", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OPEN PLAYER (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on OPEN Player") },
	                   { EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OPEN BANKER", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OPEN BANKER (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for OPEN Banker") },
	                   { EBaccaratBetType(EBaccaratBetType::Tie)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on TIE", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on TIE (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 8) } },
	                                "Limits for bets on Tie") },
	                   { EBaccaratBetType(EBaccaratBetType::PlayerPair)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on side bet PLAYER PAIR", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on CARRES field (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 11) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on PlayerPair") },
	                   { EBaccaratBetType(EBaccaratBetType::BankerPair)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet BANKER_PAIR", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on FIFTHS field (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 11) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on BankerPair") },
	                   { EBaccaratBetType(EBaccaratBetType::Lucky6)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet Lucky6", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet Lucky6 (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", LuckySixMultiplierSchema },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on Lucky6") },
	                   { EBaccaratBetType(EBaccaratBetType::Small6)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet Small6", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet Small6 (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 22) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on Small6") },
	                   { EBaccaratBetType(EBaccaratBetType::Big6)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet Big6", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet Big6 (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 50) },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on Big6") },
	                   { EBaccaratBetType(EBaccaratBetType::SuitedMatch)._to_string(),
	                     JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on Lucky6AdditionalCard", 0) },
	                                  { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on Lucky6Lucky6AdditionalCardthird (0 for unlimited)", 0) },
	                                  { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                  { "multiplier", SuitedMatchMultiplierSchema },
	                                  { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) } },
	                                "Limits for bets on Lucky6AdditionalCard") } },
	                 "The min/max bets per bet field type") } },
	  "The configuration of the stake");
	return Schema;
}

const JsonSchema& TBaccaratStake::StakeSchema()
{
	static const JsonSchema LuckySixMultiplierSchema(
	  { { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Lucky6TwoCards)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on Lucky6 - with two cards", 12).Flag(CriticalSettingFlag) },
	    { EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Lucky6ThreeCards)._to_string(),
	      JsonSchema(json::value_t::number_unsigned, "Multiplier on Lucky6 - with three cards", 20).Flag(CriticalSettingFlag) } });

	static const JsonSchema Schema(
	  { { "maxNumBets", JsonSchema(json::value_t::number_unsigned, "The maximum number of bet fields that can be bet in a single game (0 for unlimited)", 0) },
	    { "tableWinLimit", JsonSchema(json::value_t::number_unsigned, "The maximum number of credits that can be won in a single game (0 for unlimited)", 0) },
	    { "minBet", JsonSchema(json::value_t::number_unsigned, "The minimum total bet", 0) },
	    { "maxBet", JsonSchema(json::value_t::number_unsigned, "The maximum total bet (0 for unlimited)", 0) },
	    { "chipValues", JsonSchema(json::value_t::array, "The chip values in credits", json::array({ 1, 2, 5, 10, 20 }))
	                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The value of a chip in credits", 1), 5, 5) },
	    { "betTypes", JsonSchema({ { EBaccaratBetType(EBaccaratBetType::Player)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on PLAYER", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on PLAYER (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on Player") },
	                               { EBaccaratBetType(EBaccaratBetType::Banker)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on BANKER", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on BANKER (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2).Flag(CriticalSettingFlag) } },
	                                            "Limits for Banker") },
	                               { EBaccaratBetType(EBaccaratBetType::Tie)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on TIE", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on TIE (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 8).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on Tie") },
	                               { EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OpenPlayer", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OpenPlayer (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", false) } },
	                                            "Limits for bets on OpenPlayer") },
	                               { EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on OpenBanker", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on OpenBanker (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", false) } },
	                                            "Limits for OpenBanker") },
	                               { EBaccaratBetType(EBaccaratBetType::PlayerPair)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on side bet PLAYER PlayerPair", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on PlayerPair field (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 11).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on PlayerPair") },
	                               { EBaccaratBetType(EBaccaratBetType::BankerPair)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet BankerPair", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on BankerPair field (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 11).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on BankerPair") },
	                               { EBaccaratBetType(EBaccaratBetType::Lucky6)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet Lucky6", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet Lucky6 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", LuckySixMultiplierSchema } },
	                                            "Limits for bets on Lucky6") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith1)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith1", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith1 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 85).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith1") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith2)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith2", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith2 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 45).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith2") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith3)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith3", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith3 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 30).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith3") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith4)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith4", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith4 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 16).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith4") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith5)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith5", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith5 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 12).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith5") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith6)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith6", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith6 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 6).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith6") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith7)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith7", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith7 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 4).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith7") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith8)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith8", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith8 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 3).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith8") },
	                               { EBaccaratBetType(EBaccaratBetType::WinWith9)._to_string(),
	                                 JsonSchema({ { "min", JsonSchema(json::value_t::number_unsigned, "The minimum bet on sidebet WinWith9", 0) },
	                                              { "max", JsonSchema(json::value_t::number_unsigned, "The maximum bet on sidebet WinWith9 (0 for unlimited)", 0) },
	                                              { "multiple", JsonSchema(json::value_t::number_unsigned, "Multiple", 1) },
	                                              { "enabled", JsonSchema(json::value_t::boolean, "Bet enabled", true).Flag(CriticalSettingFlag) },
	                                              { "multiplier", JsonSchema(json::value_t::number_float, "Bet multiplier", 2).Flag(CriticalSettingFlag) } },
	                                            "Limits for bets on WinWith9") } },
	                             "The min/max bets per bet field type") } },
	  "The configuration of the stake");
	return Schema;
}

void TBaccaratStake::SetMaxBet(EBaccaratBetType fieldType, uint64_t maxBet)
{
	LimitsTable[fieldType._to_index()].Max = maxBet;
}

void TBaccaratStake::SetMinBet(EBaccaratBetType fieldType, uint64_t minBet)
{
	LimitsTable[fieldType._to_index()].Min = minBet;
}

void TBaccaratStake::SetFieldLimit(EBaccaratBetType fieldType, const FieldLimit& limit)
{
	LimitsTable[fieldType._to_index()] = limit;
}

void TBaccaratStake::SetMultiple(EBaccaratBetType fieldType, uint64_t multiple)
{
	LimitsTable[fieldType._to_index()].Multiple = multiple;
}

TBaccaratStake::TBaccaratStake() : TConfiguration(StakeSchema())
{
	ChipValues.fill(0);
}

void TBaccaratStake::OnConfigLoaded(const std::filesystem::path& filename)
{
	BetsCountMax = GetConfig("maxNumBets").get<uint32_t>();
	MaxTableWinLimit = GetConfig("tableWinLimit").get<uint64_t>();
	PlayboardLimitMin = GetConfig("minBet").get<uint64_t>();
	PlayboardLimitMax = GetConfig("maxBet").get<uint64_t>();

	const json& chipVals = GetConfig("chipValues");
	for (uint i = 0; i < chipVals.size(); i++) ChipValues[i + 1] = chipVals[i].get<uint64_t>();

	const json& betTypes = GetConfig("betTypes");
	for (auto betType = betTypes.begin(); betType != betTypes.end(); betType++)
	{
		uint64_t min = 0, max = 0, multiple = 0;
		bool enabled = false;
		if (!betType->is_object())
			throw std::runtime_error("Bet type object '" + betType.key() + "' is invalid - value needs to be an object containing 'min' and 'max' members");

		auto typ = EBaccaratBetType::_from_string_nothrow(betType.key().c_str());
		if (!typ)
			throw std::runtime_error("Unknown bet type '" + betType.key() + "'");

		auto enabledJson = FindMember(*betType, "enabled");
		if (enabledJson && enabledJson->is_boolean())
			enabled = enabledJson->get<bool>();

		// Ante bets are always enabled
		if (typ == EBaccaratBetType::Banker || typ == EBaccaratBetType::Player || typ == EBaccaratBetType::Tie)
			enabled = true;

		if (!enabled)
			continue;

		min = (*betType)["min"].get<uint64_t>();
		max = (*betType)["max"].get<uint64_t>();
		multiple = (*betType)["multiple"].get<uint64_t>();

		if (betType->contains("multiplier"))
		{
			const auto& multiplierValue = (*betType)["multiplier"];
			if (multiplierValue.is_number())
			{
				std::map<std::string, double> items;
				items[EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Default)._to_string()] = multiplierValue.get<double>();
				Multipliers[*typ] = BetMultiplier(items);
			}
			else if (multiplierValue.is_object())
			{
				std::map<std::string, double> items;
				for (auto betMultiplierItem = multiplierValue.begin(); betMultiplierItem != multiplierValue.end(); ++betMultiplierItem)
				{
					items[betMultiplierItem.key()] = betMultiplierItem.value().get<double>();
					Multipliers[*typ] = BetMultiplier(items);
				}
			}
		}

		SetMinBet(*typ, min);
		SetMaxBet(*typ, max);
		SetMultiple(*typ, multiple);
	}
}

TBaccaratStake::FieldLimit& TBaccaratStake::FieldLimit::operator*=(uint64_t multiply)
{
	Min *= multiply;
	Max *= multiply;
	Multiple *= multiply;
	return *this;
}

TBaccaratStake::FieldLimit TBaccaratStake::FieldLimit::operator*(uint64_t multiply) const
{
	FieldLimit lim(*this);
	lim *= multiply;
	return lim;
}

void TBaccaratStake::SetCardBetSecuritySettings(const EBaccaratBetType betType, const CardGameBetSecuritySettings& securitySettings)
{
	mSecurityManager.SetSecuritySettings(betType._to_string(), securitySettings);
}

std::optional<yserver::gamehost::CardGameBetSecuritySettings> TBaccaratStake::GetCardBetSecuritySettings(const EBaccaratBetType betType) const
{
	return mSecurityManager.GetSecuritySettings(betType._to_string());
}

json TBaccaratStake::BetSecuritySettingsToJson() const
{
	return mSecurityManager.ToJson();
}
