//
// Created by <PERSON><PERSON><PERSON> on 10. 12. 24.
//

#include "hosts/dealer/TDealerDragonTigerBets.h"

#include <format>

#include "dealer-assist/ScannedCard.h"

DEFINE_LOG_CATEGORY(LogDealersDragonTigerBets, "dealers-dt-bets")

using namespace yserver;
using namespace yserver::gamehost::dragontiger;
using namespace yserver::gamehost;

static const std::array<uint32_t, EDragonTigerBetType::_size() + 1> WinMultipliers = {
	1,    // BET_DRAGON
	1,    // BET_OPEN_DRAGON
	1,    // BET_TIGER
	1,    // BET_OPEN_TIGER
	1,    // MAGIC_TIE
	1    // MYSTIC_WIN
};

const JsonSchema& TDealerDragonTigerBets::RTPSchema()
{
	static const JsonSchema ThreeHeadedDragonRtpsSchema =
	  JsonSchema({ { "8-decks", JsonSchema({ { EDragonTigerBetType(EDragonTigerBetType::Dragon)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of Dragon", 96.27).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::Tiger)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of Tiger", 96.27).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::MagicTie)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of MagicTie", 94.86).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of MysticWin", 97.28).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of OpenDragon", 97.41).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of OpenTiger", 97.41).Flag(CriticalSettingFlag) } },
	                                       "RTP for 8 decks") },
	               { "6-decks", JsonSchema({ { EDragonTigerBetType(EDragonTigerBetType::Dragon)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of Dragon", 96.30).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::Tiger)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of Tiger", 96.30).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::MagicTie)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of MagicTie", 92.77).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of MysticWin", 97.35).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of OpenDragon", 97.44).Flag(CriticalSettingFlag) },
	                                         { EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string(),
	                                           JsonSchema(json::value_t::number_float, "RTP of OpenTiger", 97.44).Flag(CriticalSettingFlag) } },
	                                       "RTP for 6 decks") } });

	return ThreeHeadedDragonRtpsSchema;
}

TDealerDragonTigerBets::TDealerDragonTigerBets(bool isOpenVersion) : TDealersBets(isOpenVersion) {}

void TDealerDragonTigerBets::VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result) const
{
	uint64_t closedDragonBet = closedBet.GetBetAmount(EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string());

	if (closedDragonBet > 0)
	{
		uint64_t dragonRaise = bets.GetBetAmount(EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string());
		if (dragonRaise == 0)
		{
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "No raise on dragon - fold");
		}
		else if (dragonRaise != closedDragonBet * 2)
		{
			result.Violation = EBetRuleViolation::InvalidBetRaise;
			result.Message = std::format("{} Raise should be double the previous bet amount. Previous bet: {}, Raise: {}",
			                             EDragonTigerBetType(EDragonTigerBetType::OpenDragon)._to_string(), closedDragonBet, dragonRaise);
			return;
		}
	}

	uint64_t closedTigerBet = closedBet.GetBetAmount(EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string());
	if (closedTigerBet > 0)
	{
		uint64_t tigerRaise = bets.GetBetAmount(EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string());
		if (tigerRaise == 0)
		{
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "No raise on tiger - fold");
		}
		else if (tigerRaise != closedTigerBet * 2)
		{
			result.Violation = EBetRuleViolation::InvalidBetRaise;
			result.Message = std::format("{} Raise should be double the previous bet amount. Previous bet: {}, Raise: {}",
			                             EDragonTigerBetType(EDragonTigerBetType::OpenTiger)._to_string(), closedTigerBet, tigerRaise);
		}
	}
}

FBetVerifyResult TDealerDragonTigerBets::VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets) const
{
	FBetVerifyResult ret;
	if (!stake.Stake)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::BadSetup);
		ret.Message = "Bad game setup.";
		return ret;
	}

	if (ret.Violation.has_value())
		return ret;

	if (!confirmedBets.Empty() && confirmedBets.IsActiveOpenGameBet())
	{
		VerifyBetRaise(bets, confirmedBets, ret);
		if (ret.Violation.has_value())
			return ret;
	}

	uint64_t totalBetAmount = 0;
	uint64_t totalBetCost = 0;
	std::map<std::string, uint64_t> possibleWinAmounts;

	for (const auto& [betType, betAmount] : bets.Get())
	{
		const uint64_t ThisBet = betAmount;
		if (ThisBet == 0)
			continue;

		possibleWinAmounts[betType] = 0;
		const FieldLimit fieldLimit = stake.Stake->GetFieldLimit(betType) * stake.Multiplier;
		if (fieldLimit.Min > ThisBet)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MinimumFieldLimit);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Violates limits (min " + std::to_string(fieldLimit.Min) + ")";
			return ret;
		}

		if (fieldLimit.Max && (fieldLimit.Max < ThisBet))
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MaximumFieldLimit);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Violates limits (max " + std::to_string(fieldLimit.Max) + ")";
			return ret;
		}

		if (fieldLimit.Multiple > 1U && ThisBet % fieldLimit.Multiple)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Must be a multiple of " + std::to_string(fieldLimit.Multiple);
			return ret;
		}

		uint64_t BetCost = ThisBet;

		totalBetAmount += ThisBet;
		totalBetCost += BetCost;

		if (betType == EDragonTigerBetType(EDragonTigerBetType::MagicTie)._to_string())
			possibleWinAmounts[betType] +=
			  GetMultiplier(betType, stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::SuitedTie)._to_string()) * ThisBet;

		if (betType == EDragonTigerBetType(EDragonTigerBetType::MysticWin)._to_string())
			possibleWinAmounts[betType] +=
			  GetMultiplier(betType, stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::LosesToKing)._to_string()) * ThisBet;
		else
			possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID) * ThisBet;
	}

	const uint64_t playboardmin = stake.Stake->PlayboardLimitMin * stake.Multiplier;
	if (totalBetAmount && playboardmin > totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMin);
		ret.Message = "Minimum total bet limit violated (min " + std::to_string(playboardmin) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	const uint64_t playboardmax = stake.Stake->PlayboardLimitMax * stake.Multiplier;
	if (playboardmax && playboardmax < totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMax);
		ret.Message = "Maximum total bet limit exceeded (max " + std::to_string(playboardmax) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	auto maxWin = std::max_element(possibleWinAmounts.begin(), possibleWinAmounts.end(), [](const auto& lhs, const auto& rhs) {
		return lhs.second < rhs.second;    // Compare by value
	});

	const uint64_t maxWinLimit = stake.Stake->MaxTableWinLimit * stake.Multiplier;
	if (maxWinLimit && (maxWinLimit < maxWin->second))
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::WinLimit);
		ret.Message = "Maximum table win limit exceeded (max " + std::to_string(maxWinLimit) + ", max possible win was " + std::to_string(maxWin->second) + ")";
		return ret;
	}

	ret.TotalBet = totalBetCost;
	return ret;
}

CalculateCardGameWinsResult TDealerDragonTigerBets::CalculateWins(BetAmounts Bets, FDealersStakeInfo Stake, const dragontiger::DragonTigerGameLogic& gameLogic,
                                                                  const std::optional<BetAmounts>& LastBets) const noexcept
{
	CalculateCardGameWinsResult Result;

	auto winner = gameLogic.GetWinner();

	if (!winner || winner == EDragonTigerWinner::Null)
	{
		TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "No winner");
		return Result;
	}

	if (Bets.Empty())
	{
		TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Bets empty");
		return Result;
	}

	TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Winner %s", EDragonTigerWinner::_from_integral(*winner)._to_string());

	if (LastBets && !LastBets->Empty() && Bets.IsActiveOpenGameBet())
	{
		TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Calculate open game wins");
		CalculateOpenBetsWins(Bets, LastBets.value(), Stake, gameLogic, Result);
	}

	// Go through all bets and calculate wins
	for (const auto& [betName, betAmount] : Bets.Get())
	{
		EDragonTigerBetType betType = EDragonTigerBetType::_from_string(betName.c_str());
		const uint64_t betOnField = betAmount;

		TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("Bet type {} betOnField {}", betType._to_string(), betOnField).c_str());

		uint64_t win = 0;
		double multiplier = 0;

		if (betOnField && betType == EDragonTigerBetType::Dragon && winner == EDragonTigerWinner::DragonWin)
		{
			multiplier = GetMultiplier(betType._to_string(), Stake.ID);
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("DragonWin - Multiplier {}", multiplier).c_str());
			win = betOnField + (betOnField * multiplier);    // bet + (win)
		}

		if (betOnField && betType == EDragonTigerBetType::Tiger && winner == EDragonTigerWinner::TigerWin)
		{
			multiplier = GetMultiplier(betType._to_string(), Stake.ID);
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("TigerWin - Multiplier {}", multiplier).c_str());
			win = betOnField + (betOnField * multiplier);    // bet + (win)
		}

		if (betOnField && betType == EDragonTigerBetType::MagicTie)
		{
			if (gameLogic.IsMagicSuitedTie())
			{
				multiplier = GetMultiplier(betType._to_string(), Stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicSuited)._to_string());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("MagicSuited - Multiplier {}", multiplier).c_str());
				win = betOnField + (betOnField * multiplier);    // bet + (win)
			}
			else if (gameLogic.IsMagicUnsuitedTie())
			{
				multiplier = GetMultiplier(betType._to_string(), Stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::MagicUnsuited)._to_string());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("MagicUnsuited - Multiplier {}", multiplier).c_str());
				win = betOnField + (betOnField * multiplier);    // bet + (win)
			}

			else if (gameLogic.IsSuitedTie())
			{
				multiplier = GetMultiplier(betType._to_string(), Stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::SuitedTie)._to_string());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("SuitedTie - Multiplier {}", multiplier).c_str());
				win = betOnField + (betOnField * multiplier);    // bet + (win)
			}

			else if (gameLogic.IsUnsuitedTie())
			{
				multiplier = GetMultiplier(betType._to_string(), Stake.ID, EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::UnsuitedTie)._to_string());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("UnsuitedTie - Multiplier {}", multiplier).c_str());
				win = betOnField + (betOnField * multiplier);    // bet + (win)
			}
		}

		if (betOnField && betType == EDragonTigerBetType::MysticWin)
		{
			if (gameLogic.GetWinner() == EDragonTigerWinner::Tie)
			{
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "MysticWin in case of Tie wager pushes");
				win = betOnField;
			}
			else
			{
				// From highest to lowest payout of sidebet
				const std::array<EDragonTigerBetMultiplierType, 4> mysteryWinTypes = { EDragonTigerBetMultiplierType::LosesToQueen,
					                                                                   EDragonTigerBetMultiplierType::LosesToJack,
					                                                                   EDragonTigerBetMultiplierType::LosesTo10,
					                                                                   EDragonTigerBetMultiplierType::LosesTo9 };

				// Check if mystery win is won
				for (const auto& type : mysteryWinTypes)
				{
					if (gameLogic.IsMysteryWin(type))
					{
						multiplier = GetMultiplier(betType._to_string(), Stake.ID, type._to_string());
						TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("MysticWin-{} - Multiplier {}", type._to_string(), multiplier).c_str());
						win = betOnField + (betOnField * multiplier);    // bet + (win)

						break;
					}
				}
			}
		}

		if (win)
		{
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Bet win %d!\n", win);
			Result.WinningFields.emplace_back(betAmount, betType._to_string(), win);
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Bet of %ld on field %s(%d, %d) wins %ld(%lux%d)!\n", betAmount, betType._to_string(), 0, betType._value,
			     win, betAmount, multiplier);

			Result.TotalWon += win;
		}
		// If winner is tie in case of bet on tiger or banker we return the bet amount
		else if (betOnField && (betType == EDragonTigerBetType::Dragon || betType == EDragonTigerBetType::Tiger) && gameLogic.IsUnsuitedTie())
		{
			uint32_t betToReturn = betOnField / 2;
			Result.WinningFields.emplace_back(betAmount, betType._to_string(), betToReturn);
			Result.TotalWon += betToReturn;
			Result.WinningFields.emplace_back(betAmount, betType._to_string(), betToReturn);
			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Winner is Tie and wager pushes with half bet");
		}
	}

	TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Total won %ld", Result.TotalWon);
	return Result;
}

double TDealerDragonTigerBets::GetMultiplier(std::string betType, int stakeID, std::string multiplierType) const
{
	auto type = EDragonTigerBetType::_from_string(betType.c_str());

	if (stakeID >= 0 && stakeID < static_cast<int>(mStakes.size()))
	{
		auto find = mStakes[stakeID]->MultiplierOverrides.find(betType);
		if (find != mStakes[stakeID]->MultiplierOverrides.end())
		{
			auto multiplier = find->second.GetValue(multiplierType);
			if (multiplier)
			{
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("getMultiplier - GetValue exist {}", *multiplier).c_str());
				return *multiplier;
			}

			TLOG(LogDealersDragonTigerBets, EVerbosity::Info, std::format("getMultiplier - GetValue doesnt exist").c_str());
			return WinMultipliers[static_cast<int>(type)];
		}

		return WinMultipliers[static_cast<int>(type)];
	}

	return WinMultipliers[static_cast<int>(type)];
}

json TDealerDragonTigerBets::GetBetTypes() const
{
	json betTypes(json::value_t::array);
	for (EDragonTigerBetType betType : EDragonTigerBetType::_values())
	{
		if (!bOpenVersion && (betType == EDragonTigerBetType::OpenDragon || betType == EDragonTigerBetType::OpenTiger))
			continue;

		json betTypeInfo(json::value_t::object);
		betTypeInfo["index"] = betType._to_index();
		betTypeInfo["name"] = betType._to_string();
		betTypeInfo["multiplier"] = GetMultiplier(betType._to_string(), -1);
		betTypes.push_back(std::move(betTypeInfo));
	}

	return betTypes;
}

void TDealerDragonTigerBets::CalculateOpenBetsWins(const BetAmounts& firstRoundBets, const BetAmounts& bets, const FDealersStakeInfo& stake,
                                                   const dragontiger::DragonTigerGameLogic& gameLogic, CalculateCardGameWinsResult& result) const
{
	auto processBet = [&](EDragonTigerBetType betType, EDragonTigerWinner winner) {
		if (gameLogic.GetWinner() == winner)
		{
			if (auto betOnField = bets.GetBetAmount(betType._to_string()); betOnField > 0)
			{
				auto firstBet = firstRoundBets.GetBetAmount(betType._to_string());
				auto initialBetWin = betOnField + GetMultiplier(betType._to_string(), stake.ID) * betOnField;

				auto raiseWin = 0.0;

				if (const auto loosingHandValue =
				      gameLogic.GetHandValue(gameLogic.GetWinner() == EDragonTigerWinner::DragonWin ? EDragonTigerSide::Tiger : EDragonTigerSide::Dragon))
				{
					if (loosingHandValue == dealer_assist::ECardFace(dealer_assist::ECardFace::Queen)._to_index())
						raiseWin = firstBet + (3 * firstBet);
					else if (loosingHandValue == dealer_assist::ECardFace(dealer_assist::ECardFace::Jack)._to_index())
						raiseWin = firstBet + (2 * firstBet);
					else if (loosingHandValue == dealer_assist::ECardFace(dealer_assist::ECardFace::Ten)._to_index())
						raiseWin = firstBet + (1 * firstBet);
					else
						raiseWin = firstBet;
				}

				auto totalBet = betOnField + firstBet;
				auto win = initialBetWin + raiseWin;
				result.WinningFields.emplace_back(totalBet, betType._to_string(), win);
				result.TotalWon += win;

				TLOG(LogDealersDragonTigerBets, EVerbosity::Info,
				     std::format("First open bet {} on field {} wins {}!", firstBet, betType._to_string(), raiseWin).c_str());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info,
				     std::format("Raise bet {} on field {} wins {}!", betOnField, betType._to_string(), initialBetWin).c_str());
			}
		}

		if (gameLogic.GetWinner() == EDragonTigerWinner::Tie && (betType == EDragonTigerBetType::OpenDragon || betType == EDragonTigerBetType::OpenTiger))
		{
			if (auto betOnField = bets.GetBetAmount(betType._to_string()); betOnField > 0)
			{
				result.TotalWon += betOnField;
				result.TotalWon += firstRoundBets.GetBetAmount(betType._to_string());
				TLOG(LogDealersDragonTigerBets, EVerbosity::Info, "Winner is Tie and wager pushes");
			}
		}
	};

	processBet(EDragonTigerBetType::OpenDragon, EDragonTigerWinner::DragonWin);
	processBet(EDragonTigerBetType::OpenTiger, EDragonTigerWinner::TigerWin);
}

FRTPInfo TDealerDragonTigerBets::GetTotalRTP(const uint8_t stakeID, const uint8_t numOfDecks, const EPlayboardMode playboardMode) const
{
	FRTPInfo rtpInfo;
	rtpInfo.BaseGame.Max = numOfDecks == 8 ? 94.861 : 92.770;
	rtpInfo.BaseGame.Min = numOfDecks == 8 ? 94.861 : 92.770;
	rtpInfo.BaseGame.Average = numOfDecks == 8 ? 94.861 : 92.770;

	if (stakeID < static_cast<int>(mStakes.size()) && mRTPs.contains(numOfDecks))
	{
		const auto& rtpMap = mRTPs.at(numOfDecks);
		for (const auto& betName : mStakes[stakeID]->MultiplierOverrides | std::views::keys)
		{
			if (const auto rtp = rtpMap.find(betName); rtp != rtpMap.end())
			{
				rtpInfo.Variants[betName] = rtp->second;
			}
		}
	}

	return rtpInfo;
}

double_t TDealerDragonTigerBets::GetVolatility(const uint8_t stakeID, const uint8_t numOfDecks) const
{
	return 55.55555;    // Placeholder value, as volatility calculation is not implemented
}
