//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "yserver/hosts/dealer/TDealerBaccaratBets.h"

#include <format>

DEFINE_LOG_CATEGORY(LogDealersBaccaratBets, "dealers-baccarat-bets")

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;

static const std::array<uint32_t, EBaccaratBetType::_size() + 1> WinMultipliers = {
	2,    // BET_PLAYER
	2,    // BET_BANKER
	8,    // BET_TIE
	11,    // BET_PLAYER_PAIR
	11,    // BET_BANKER_PAIR
	12,    // BET_LUCKY6
	23,    // BET_LUCKY6ADDITIONALCARD
};

TDealerBaccaratBets::TDealerBaccaratBets(bool isOpenVersion) : TDealersBets(isOpenVersion)
{
	mBetProbabilities = { { 8,
		                    { { EBaccaratBetProbabilityType::PlayerWin, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin, 0.********* },
		                      { EBaccaratBetProbabilityType::Tie, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWinExcept6, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWinOn6, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerPair, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerPair, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin1, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin1, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin2, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin2, 0.******** },
		                      { EBaccaratBetProbabilityType::PlayerWin3, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin3, 0.******** },
		                      { EBaccaratBetProbabilityType::PlayerWin4, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin4, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin5, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin5, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin6, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin6, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin7, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin7, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin8, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin8, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin9, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin9, 0.********* },
		                      { EBaccaratBetProbabilityType::Lucky6TwoCard, 0.********* },
		                      { EBaccaratBetProbabilityType::Lucky6ThreeCards, 0.********* } } },
		                  { 6,
		                    { { EBaccaratBetProbabilityType::PlayerWin, 0.******** },
		                      { EBaccaratBetProbabilityType::BankerWin, 0.********* },
		                      { EBaccaratBetProbabilityType::Tie, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWinExcept6, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWinOn6, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerPair, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerPair, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin1, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin1, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin2, 0.******** },
		                      { EBaccaratBetProbabilityType::BankerWin2, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin3, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin3, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin4, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin4, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin5, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin5, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin6, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin6, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin7, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin7, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin8, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin8, 0.********* },
		                      { EBaccaratBetProbabilityType::PlayerWin9, 0.********* },
		                      { EBaccaratBetProbabilityType::BankerWin9, 0.********* },
		                      { EBaccaratBetProbabilityType::Lucky6TwoCard, 0.********* },
		                      { EBaccaratBetProbabilityType::Lucky6ThreeCards, 0.******** } } } };
}

const JsonSchema& TDealerBaccaratBets::RTPSchema()
{
	static const JsonSchema BaccaratRtpsSchema = JsonSchema(
	  { { "8-decks",
	      JsonSchema(
	        { { EBaccaratBetType(EBaccaratBetType::Player)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Player", 98.76).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Banker)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Banker", 98.54).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Tie)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Tie", 85.64).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of OpenPlayer", 98.87).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of OpenBanker", 99.00).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::PlayerPair)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of PlayerPair", 89.64).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::BankerPair)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of BankerPair", 89.64).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Small6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Small6", 85.67).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Big6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Big6", 84.75).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::SuitedMatch)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of SuitedMatch", 94.49).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Lucky6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Lucky6", 83.32).Flag(CriticalSettingFlag) } },
	        "RTP for 8 decks") },
	    { "6-decks",
	      JsonSchema(
	        { { EBaccaratBetType(EBaccaratBetType::Player)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Player", 98.76).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Banker)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Banker", 98.55).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Tie)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Tie", 85.56).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of OpenPlayer", 98.90).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of OpenBanker", 99.03).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::PlayerPair)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of PlayerPair", 88.75).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::BankerPair)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of BankerPair", 88.75).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Small6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Small6", 98.65).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Big6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Big6", 84.70).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::SuitedMatch)._to_string(),
	            JsonSchema(json::value_t::number_float, "RTP of SuitedMatch", 91.99).Flag(CriticalSettingFlag) },
	          { EBaccaratBetType(EBaccaratBetType::Lucky6)._to_string(), JsonSchema(json::value_t::number_float, "RTP of Lucky6", 83.28).Flag(CriticalSettingFlag) } },
	        "RTP for 6 decks") } });

	return BaccaratRtpsSchema;
}

void TDealerBaccaratBets::VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result) const
{
	uint64_t previousBankerBet = closedBet.GetBetAmount(EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string());
	uint64_t bankerRaise = bets.GetBetAmount(EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string());

	if (previousBankerBet > 0 && bankerRaise > 0 && (bankerRaise != previousBankerBet * 2))
	{
		result.Violation = EBetRuleViolation::InvalidBetRaise;
		result.Message = std::format("{} Raise should be double the previous bet amount. Previous bet: {}, Raise: {}",
		                             EBaccaratBetType(EBaccaratBetType::OpenBanker)._to_string(), previousBankerBet, bankerRaise);
		return;
	}

	uint64_t previousPlayerBet = closedBet.GetBetAmount(EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string());
	uint64_t playerRaise = bets.GetBetAmount(EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string());

	if (previousPlayerBet > 0 && playerRaise > 0 && (playerRaise != previousPlayerBet * 2))
	{
		result.Violation = EBetRuleViolation::InvalidBetRaise;
		result.Message = std::format("{} Raise should be double the previous bet amount. Previous bet: {}, Raise: {}",
		                             EBaccaratBetType(EBaccaratBetType::OpenPlayer)._to_string(), previousPlayerBet, playerRaise);
	}
}


FBetVerifyResult TDealerBaccaratBets::VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets) const
{
	FBetVerifyResult ret;
	if (!stake.Stake)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::BadSetup);
		ret.Message = "Bad game setup.";
		return ret;
	}

	// CheckBetSecurity(*stake.Stake, bets, numOfRounds, ret);
	if (ret.Violation.has_value())
		return ret;

	if (!confirmedBets.Empty() && confirmedBets.IsActiveOpenGameBet())
	{
		VerifyBetRaise(bets, confirmedBets, ret);
		if (ret.Violation.has_value())
			return ret;
	}

	unsigned int numBets = 0;
	uint64_t totalBetAmount = 0;
	uint64_t totalBetCost = 0;
	std::map<std::string, uint64_t> possibleWinAmounts;

	for (const auto& [betType, betAmount] : bets.Get())
	{
		const uint64_t ThisBet = betAmount;
		if (ThisBet == 0)
			continue;

		possibleWinAmounts[betType] = 0;

		// Check banker bet when playing with commission
		if (betType == EBaccaratBetType(EBaccaratBetType::Banker)._to_string() && isCommission)
		{
			auto bankerBetValue = GetMultiplier(betType, stake.ID) * ThisBet;

			if (std::fmod(bankerBetValue, 20) != 0)
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
				ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Must be a devision by 20 (5% commission)";
				return ret;
			}
		}

		if (betType == EBaccaratBetType(EBaccaratBetType::Banker)._to_string() && !isCommission)
		{
			auto totalWinAmount = GetMultiplier(betType, stake.ID) * ThisBet;
			auto bankerBetValue = totalWinAmount - ThisBet;

			if (std::fmod(bankerBetValue, 2) != 0)
			{
				ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
				ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Must be a devision by 2 (50% payout)";
				return ret;
			}
		}

		const FieldLimit fieldLimit = stake.Stake->GetFieldLimit(betType) * stake.Multiplier;
		if (fieldLimit.Min > ThisBet)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MinimumFieldLimit);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Violates limits (min " + std::to_string(fieldLimit.Min) + ")";
			return ret;
		}

		if (fieldLimit.Max && (fieldLimit.Max < ThisBet))
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::MaximumFieldLimit);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Violates limits (max " + std::to_string(fieldLimit.Max) + ")";
			return ret;
		}

		if (fieldLimit.Multiple > 1U && ThisBet % fieldLimit.Multiple)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::NotDivisible);
			ret.Message = "Bet " + std::to_string(ThisBet) + " on '" + betType + "' not allowed: Must be a multiple of " + std::to_string(fieldLimit.Multiple);
			return ret;
		}

		uint64_t BetCost = ThisBet;

		numBets++;

		if (stake.Stake->BetsCountMax && numBets > stake.Stake->BetsCountMax)
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::ToutVaLocked);
			ret.Message = "Maximum number of bets exceeded (" + std::to_string(stake.Stake->BetsCountMax) + ")";
			return ret;
		}

		totalBetAmount += ThisBet;
		totalBetCost += BetCost;

		possibleWinAmounts[betType] += GetMultiplier(betType, stake.ID) * ThisBet;
	}

	const uint64_t playboardmin = stake.Stake->PlayboardLimitMin * stake.Multiplier;
	if (totalBetAmount && playboardmin > totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMin);
		ret.Message = "Minimum total bet limit violated (min " + std::to_string(playboardmin) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	const uint64_t playboardmax = stake.Stake->PlayboardLimitMax * stake.Multiplier;
	if (playboardmax && playboardmax < totalBetAmount)
	{
		ret.Violation = EBetRuleViolation(EBetRuleViolation::PlayboardMax);
		ret.Message = "Maximum total bet limit exceeded (max " + std::to_string(playboardmax) + ", total bet was " + std::to_string(totalBetAmount) + ")";
		return ret;
	}

	if (!possibleWinAmounts.empty())
	{
		auto maxWin = std::max_element(possibleWinAmounts.begin(), possibleWinAmounts.end(), [](const auto& lhs, const auto& rhs) {
			return lhs.second < rhs.second;    // Compare by value
		});

		const uint64_t maxWinLimit = stake.Stake->MaxTableWinLimit * stake.Multiplier;
		if (maxWinLimit && (maxWinLimit < maxWin->second))
		{
			ret.Violation = EBetRuleViolation(EBetRuleViolation::WinLimit);
			ret.Message = "Maximum table win limit exceeded (max " + std::to_string(maxWinLimit) + ", max possible win was " + std::to_string(maxWin->second) + ")";
			return ret;
		}
	}

	ret.TotalBet = totalBetCost;
	return ret;
}

CalculateCardGameWinsResult TDealerBaccaratBets::CalculateWins(BetAmounts Bets, FDealersStakeInfo Stake, const baccarat::BaccaratGameLogic& gameLogic,
                                                               const std::optional<BetAmounts>& FirstBets) const noexcept
{
	auto winner = gameLogic.GetWinner();
	CalculateCardGameWinsResult Result;

	if (!winner)
	{
		TLOG(LogDealersBaccaratBets, EVerbosity::Error, "Missing winner");
		return Result;
	}

	TLOG(LogDealersBaccaratBets, EVerbosity::Info, "calculateWins for %s", isCommission ? "commission" : "no commission");
	if (winner == baccarat::EBaccaratWinner::Null)
	{
		TLOG(LogDealersBaccaratBets, EVerbosity::Info, "No winner");
		return Result;
	}

	if (Bets.Empty())
	{
		TLOG(LogDealersBaccaratBets, EVerbosity::Info, "Bets empty");
		return Result;
	}

	TLOG(LogDealersBaccaratBets, EVerbosity::Info, "Winner %s", baccarat::EBaccaratWinner::_from_integral(*winner)._to_string());

	CalculateAnteWins(Bets, Stake, gameLogic, Result);

	if (FirstBets && FirstBets->IsActiveOpenGameBet())
	{
		CalculateOpenBetsWins(FirstBets.value(), Bets, Stake, gameLogic, Result);
	}

	// Calculate side bets
	CalculateSidebetWins(Bets, Stake, gameLogic, Result);

	return Result;
}

double TDealerBaccaratBets::GetMultiplier(std::string betType, int stakeID, std::string multiplierType) const
{
	auto type = EBaccaratBetType::_from_string(betType.c_str());

	if (stakeID >= 0 && stakeID < static_cast<int>(mStakes.size()))
	{
		auto find = mStakes[stakeID]->MultiplierOverrides.find(type._to_string());
		if (find != mStakes[stakeID]->MultiplierOverrides.end())
		{
			auto multiplier = find->second.GetValue(multiplierType);
			if (multiplier)
			{
				TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("getMultiplier - GetValue exist {}", *multiplier).c_str());
				return *multiplier;
			}

			TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("getMultiplier - GetValue doesnt exist").c_str());
			return WinMultipliers[static_cast<int>(type)];
		}

		return WinMultipliers[static_cast<int>(type)];
	}

	return WinMultipliers[static_cast<int>(type)];
}

json TDealerBaccaratBets::GetBetTypes() const
{
	json betTypes(json::value_t::array);

	if (!mStakes.empty())
	{
		for (const auto& betType : mStakes.front()->MultiplierOverrides)
		{
			auto type = EBaccaratBetType::_from_string_nothrow(betType.first.c_str());
			if (!type)
				continue;

			if (!bOpenVersion && (type == EBaccaratBetType::OpenBanker || type == EBaccaratBetType::OpenPlayer))
				continue;

			json betTypeInfo(json::value_t::object);
			betTypeInfo["index"] = type.value()._to_index();
			betTypeInfo["name"] = type.value()._to_string();
			betTypeInfo["multiplier"] = GetMultiplier(type.value()._to_string(), -1);
			betTypes.push_back(std::move(betTypeInfo));
		}
	}
	else
	{
		for (EBaccaratBetType betType : EBaccaratBetType::_values())
		{
			if (!bOpenVersion && (betType == EBaccaratBetType::OpenBanker || betType == EBaccaratBetType::OpenPlayer))
				continue;

			json betTypeInfo(json::value_t::object);
			betTypeInfo["index"] = betType._to_index();
			betTypeInfo["name"] = betType._to_string();
			betTypeInfo["multiplier"] = GetMultiplier(betType._to_string(), -1);
			betTypes.push_back(std::move(betTypeInfo));
		}
	}

	return betTypes;
}

void TDealerBaccaratBets::CalculateSidebetWins(const BetAmounts& bets, const FDealersStakeInfo& Stake, const baccarat::BaccaratGameLogic& gameLogic,
                                               CalculateCardGameWinsResult& result) const
{
	// Side bets
	auto CalculateSideBetWin = [&](EBaccaratBetType betType, const BetAmounts& bets, EBaccaratBetMultiplierType multiplierType, CalculateCardGameWinsResult& Result) {
		uint64_t credits = bets.GetBetAmount(betType._to_string());
		if (credits)
		{
			auto multiplier = GetMultiplier(betType._to_string(), Stake.ID, multiplierType._to_string());
			auto win = credits + (credits * multiplier);
			TLOG(LogDealersBaccaratBets, EVerbosity::Info,
			     std::format("Bet of {} on field {} wins {} ({} x {})!", credits, betType._to_string(), win, credits, multiplier).c_str());
			Result.TotalWon += win;
		}
	};

	if (gameLogic.IsBetTypeWon(EBaccaratBetType::PlayerPair))
	{
		CalculateSideBetWin(EBaccaratBetType::PlayerPair, bets, EBaccaratBetMultiplierType::Default, result);
	}
	if (gameLogic.IsBetTypeWon(EBaccaratBetType::BankerPair))
	{
		CalculateSideBetWin(EBaccaratBetType::BankerPair, bets, EBaccaratBetMultiplierType::Default, result);
	}

	// First we check for lucky 6 additional card, because it has higher priority than lucky 6
	if (gameLogic.IsBetTypeWon(EBaccaratBetType::Lucky6))
	{
		CalculateSideBetWin(EBaccaratBetType::Lucky6, bets, EBaccaratBetMultiplierType::Lucky6ThreeCards, result);
		CalculateSideBetWin(EBaccaratBetType::Big6, bets, EBaccaratBetMultiplierType::Default, result);
	}
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::Lucky6, EBaccaratBetMultiplierType::Lucky6TwoCards))
	{
		CalculateSideBetWin(EBaccaratBetType::Lucky6, bets, EBaccaratBetMultiplierType::Lucky6TwoCards, result);
		CalculateSideBetWin(EBaccaratBetType::Small6, bets, EBaccaratBetMultiplierType::Default, result);
	}

	// Check for SuitedMatch
	if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::TwoSuitedPairs))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::TwoSuitedPairs, result);
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::SuitedAndUnsuitedPairs))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::SuitedAndUnsuitedPairs, result);
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::FourCardsRank))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::FourCardsRank, result);
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::TwoPairs))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::TwoPairs, result);
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::SuitedPair))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::SuitedPair, result);
	else if (gameLogic.IsBetTypeWon(EBaccaratBetType::SuitedMatch, EBaccaratBetMultiplierType::Pair))
		CalculateSideBetWin(EBaccaratBetType::SuitedMatch, bets, EBaccaratBetMultiplierType::Pair, result);

	// Check for sidebets that win with a specific hand
	static const std::array<EBaccaratBetType, 9> winWithHandSidebets = { EBaccaratBetType::WinWith1, EBaccaratBetType::WinWith2, EBaccaratBetType::WinWith3,
		                                                                 EBaccaratBetType::WinWith4, EBaccaratBetType::WinWith5, EBaccaratBetType::WinWith6,
		                                                                 EBaccaratBetType::WinWith7, EBaccaratBetType::WinWith8, EBaccaratBetType::WinWith9 };


	for (const auto& winWithHandSidebet : winWithHandSidebets)
	{
		uint64_t credits = bets.GetBetAmount(winWithHandSidebet._to_string());

		if (credits)
		{
			TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Checking bet of {} on field {}", credits, winWithHandSidebet._to_string()).c_str());
			if (gameLogic.IsBetTypeWon(winWithHandSidebet))
			{
				auto multiplier = GetMultiplier(winWithHandSidebet._to_string(), Stake.ID);
				auto win = credits + (credits * multiplier);
				TLOG(LogDealersBaccaratBets, EVerbosity::Info,
				     std::format("WIN bet of {} on field {} wins {} ({} x {})!", credits, winWithHandSidebet._to_string(), win, credits, multiplier).c_str());

				result.TotalWon += win;
			}
			else if (gameLogic.GetWinner() == baccarat::EBaccaratWinner::Tie)
			{
				// In case of tie we return the bet amount
				TLOG(LogDealersBaccaratBets, EVerbosity::Info, "Winner is Tie and wager pushes");
				result.TotalWon += credits;
			}
		}
	}
}

void TDealerBaccaratBets::CalculateAnteWins(const BetAmounts& bets, const FDealersStakeInfo& Stake, const baccarat::BaccaratGameLogic& gameLogic,
                                            CalculateCardGameWinsResult& result) const
{
	for (const auto& bet : bets.Get())
	{
		const uint64_t betOnField = bet.second;
		TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Index {} betOnField {}", bet.first, betOnField).c_str());

		EBaccaratBetType betType = EBaccaratBetType::_from_string(bet.first.c_str());

		if (betOnField && betType._to_integral() == gameLogic.GetWinner())
		{
			uint64_t win;

			win = betOnField + (betOnField * GetMultiplier(bet.first, Stake.ID));    // bet + (win)
			TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Bet win {}!", win).c_str());

			if (win)
			{
				result.WinningFields.emplace_back(bet.second, bet.first, win);
				TLOG(LogDealersBaccaratBets, EVerbosity::Info,
				     std::format("Bet of {} on field {}({}, {}) wins {}({}x{})!", bet.second, betType._to_string(), 0, betType._value, win, bet.second,
				                 GetMultiplier(bet.first, Stake.ID))
				       .c_str());

				// Commision enabled and banker wins
				if (isCommission && betType._value == baccarat::EBaccaratWinner::BankerWin)
				{
					TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Commission is selected. Bet return before 5 percent deduction {}!", win).c_str());
					win = CalculateWinWithDeductedCommission(betOnField, GetMultiplier(bet.first, Stake.ID), 5);
					TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Value after 5 percent deduction {}!", win).c_str());
				}
				else if (!isCommission && betType._value == baccarat::EBaccaratWinner::BankerWin &&
				         gameLogic.GetHandValue(baccarat::EBaccaratSide::Banker) == 6)    // In case of noncommission banker win with 6 we payout only 50 percent
				{
					TLOG(LogDealersBaccaratBets, EVerbosity::Info,
					     std::format("Banker win with hand value 6. Payout is only 50%. Winner before deduction: {}", win).c_str());
					win = CalculateWinWithDeductedCommission(betOnField, GetMultiplier(bet.first, Stake.ID), 50);
					TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("Value after 50 percent deduction {}!", win).c_str());
				}

				result.TotalWon += win;
			}
		}
		else if (betOnField && gameLogic.GetWinner() == baccarat::EBaccaratWinner::Tie &&
		         (betType == EBaccaratBetType(EBaccaratBetType::Banker) ||
		          betType == EBaccaratBetType(EBaccaratBetType::Player)))    // If winner is tie in case of bet on banker or player we return the bet amount
		{
			result.TotalWon += betOnField;
			TLOG(LogDealersBaccaratBets, EVerbosity::Info, "Winner is Tie and wager pushes");
		}
	}
}

void TDealerBaccaratBets::CalculateOpenBetsWins(const BetAmounts& firstRoundBets, const BetAmounts& bets, const FDealersStakeInfo& stake,
                                                const baccarat::BaccaratGameLogic& gameLogic, CalculateCardGameWinsResult& result) const
{
	auto processBet = [&](EBaccaratBetType betType, baccarat::EBaccaratWinner winner) {
		if (gameLogic.GetWinner() == winner)
		{
			if (auto betOnField = bets.GetBetAmount(betType._to_string()); betOnField > 0)
			{
				auto totalBet = betOnField + firstRoundBets.GetBetAmount(betType._to_string());
				auto win = totalBet + (totalBet * GetMultiplier(betType._to_string(), stake.ID));
				result.WinningFields.emplace_back(totalBet, betType._to_string(), win);
				result.TotalWon += win;

				TLOG(LogDealersBaccaratBets, EVerbosity::Info,
				     std::format("Bet of {} on field {} wins {}({} x {})!", totalBet, betType._to_string(), win, totalBet, GetMultiplier(betType._to_string(), stake.ID))
				       .c_str());
			}
		}
	};

	processBet(EBaccaratBetType::OpenBanker, baccarat::EBaccaratWinner::BankerWin);
	processBet(EBaccaratBetType::OpenPlayer, baccarat::EBaccaratWinner::PlayerWin);

	if (gameLogic.GetWinner() == baccarat::EBaccaratWinner::Tie)
	{
		auto handValue = gameLogic.GetHandValue(baccarat::EBaccaratSide::Player);

		auto processTieBet = [&](EBaccaratBetType betType, uint32_t validValue) {
			if (auto betOnField = bets.GetBetAmount(betType._to_string()); betOnField > 0 && handValue == validValue)
			{
				auto totalBet = betOnField + firstRoundBets.GetBetAmount(betType._to_string());
				result.TotalWon += totalBet;
				TLOG(LogDealersBaccaratBets, EVerbosity::Info, std::format("{} bet wager pushes for tie with hand value {}", betType._to_string(), handValue).c_str());
			}
		};

		processTieBet(EBaccaratBetType::OpenPlayer, 7);
		processTieBet(EBaccaratBetType::OpenPlayer, 8);
		processTieBet(EBaccaratBetType::OpenPlayer, 9);
		processTieBet(EBaccaratBetType::OpenBanker, 9);
	}
}

uint64_t TDealerBaccaratBets::CalculateWinWithDeductedCommission(uint64_t betOnField, uint32_t multiplier, double percent) const
{
	uint64_t won = (betOnField * multiplier);
	uint64_t deduction = won * percent / 100;

	// Ensure at least a minimum of 1 unit deduction (if totalWon is not zero),
	if (deduction < 1)
		deduction = 1;

	return betOnField + (won - deduction);
}

std::optional<EBaccaratBetProbabilityType> TDealerBaccaratBets::GetProbabilityType(EBaccaratBetType fieldType, EBaccaratSide side) const
{
	switch (fieldType)
	{
		case EBaccaratBetType::WinWith1: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin1 : EBaccaratBetProbabilityType::BankerWin1; break;
		case EBaccaratBetType::WinWith2: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin2 : EBaccaratBetProbabilityType::BankerWin2; break;
		case EBaccaratBetType::WinWith3: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin3 : EBaccaratBetProbabilityType::BankerWin3; break;
		case EBaccaratBetType::WinWith4: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin4 : EBaccaratBetProbabilityType::BankerWin4; break;
		case EBaccaratBetType::WinWith5: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin5 : EBaccaratBetProbabilityType::BankerWin5; break;
		case EBaccaratBetType::WinWith6: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin6 : EBaccaratBetProbabilityType::BankerWin6; break;
		case EBaccaratBetType::WinWith7: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin7 : EBaccaratBetProbabilityType::BankerWin7; break;
		case EBaccaratBetType::WinWith8: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin8 : EBaccaratBetProbabilityType::BankerWin8; break;
		case EBaccaratBetType::WinWith9: return side == EBaccaratSide::Player ? EBaccaratBetProbabilityType::PlayerWin9 : EBaccaratBetProbabilityType::BankerWin9; break;
		default: return std::nullopt; break;
	}
}

double_t TDealerBaccaratBets::GetRTP(const EBaccaratBetType betType, const BetMultiplier betMultiplier, const uint8_t numOfDecks,
                                     const EPlayboardMode playboardMode) const
{
	double_t rtp = 0.0;

	if (numOfDecks != 8 && numOfDecks != 6)
	{
		TLOG(LogDealersBaccaratBets, EVerbosity::Warning, "Invalid number of decks %d", numOfDecks);
		return rtp;
	}

	auto multiplier = betMultiplier.GetValue(EBaccaratBetMultiplierType(EBaccaratBetMultiplierType::Default)._to_string());

	if (!multiplier)
	{
		TLOG(LogDealersBaccaratBets, EVerbosity::Warning, "Multiplier not found for bet type %s", betType._to_string());
		return rtp;
	}

	*multiplier += 1;

	const auto& probabilities = mBetProbabilities.at(numOfDecks);

	switch (betType)
	{
		case EBaccaratBetType::Player: {
			const double_t playerWinProb = probabilities.at(EBaccaratBetProbabilityType::PlayerWin);
			const double_t tieProb = probabilities.at(EBaccaratBetProbabilityType::Tie);
			rtp = (playerWinProb * *multiplier) + tieProb;
			break;
		}
		case EBaccaratBetType::Banker: {
			const double_t tieProb = probabilities.at(EBaccaratBetProbabilityType::Tie);
			if (playboardMode == EPlayboardMode::Commission)
			{
				const double_t bankerWinProb = probabilities.at(EBaccaratBetProbabilityType::BankerWin);
				rtp = (bankerWinProb * 1.95) + tieProb;    // 5% commission
			}
			else
			{
				const double_t bankerWinProb = probabilities.at(EBaccaratBetProbabilityType::BankerWinExcept6);
				const double_t bankerWinOn6Prob = probabilities.at(EBaccaratBetProbabilityType::BankerWinOn6);
				rtp = (bankerWinProb * *multiplier) + (bankerWinOn6Prob * 1.5) + tieProb;    // 50% payout on banker win with 6
			}
			break;
		}
		case EBaccaratBetType::Tie: {
			const double_t tieProb = probabilities.at(EBaccaratBetProbabilityType::Tie);
			rtp = tieProb * *multiplier;
			break;
		}
		case EBaccaratBetType::PlayerPair: {
			const double_t playerPairProb = probabilities.at(EBaccaratBetProbabilityType::PlayerPair);
			rtp = playerPairProb * *multiplier;
			break;
		}
		case EBaccaratBetType::BankerPair: {
			const double_t bankerPairProb = probabilities.at(EBaccaratBetProbabilityType::BankerPair);
			rtp = bankerPairProb * *multiplier;
			break;
		}
		case EBaccaratBetType::Lucky6: {
			const double_t lucky6Prob = probabilities.at(EBaccaratBetProbabilityType::Lucky6TwoCard);
			rtp = lucky6Prob * *multiplier;
			break;
		}
		case EBaccaratBetType::Lucky6AdditionalCard: {
			const double_t lucky6AdditionalCardProb = probabilities.at(EBaccaratBetProbabilityType::Lucky6ThreeCards);
			rtp = lucky6AdditionalCardProb * *multiplier;
			break;
		}
		default: {
			double_t playerProb = 0.0, bankerProb = 0.0, tieProb = probabilities.at(EBaccaratBetProbabilityType::Tie);

			switch (betType)
			{
				case EBaccaratBetType::WinWith1:
				case EBaccaratBetType::WinWith2:
				case EBaccaratBetType::WinWith3:
				case EBaccaratBetType::WinWith4:
				case EBaccaratBetType::WinWith5:
				case EBaccaratBetType::WinWith6:
				case EBaccaratBetType::WinWith7:
				case EBaccaratBetType::WinWith8:
				case EBaccaratBetType::WinWith9: {
					const auto playerProbType = GetProbabilityType(betType, EBaccaratSide::Player);
					const auto bankerProbType = GetProbabilityType(betType, EBaccaratSide::Banker);
					if (playerProbType && bankerProbType)
					{
						playerProb = probabilities.at(playerProbType.value()) * *multiplier;
						bankerProb = probabilities.at(bankerProbType.value()) * *multiplier;
					}
					break;
				}
				default: break;
			}

			rtp = playerProb + bankerProb + tieProb;
			break;
		}
	}

	return rtp * 100.0;
}

FRTPInfo TDealerBaccaratBets::GetTotalRTP(const uint8_t stakeID, const uint8_t numOfDecks, const EPlayboardMode playboardMode) const
{
	FRTPInfo rtpInfo;
	auto stake = GetStake(stakeID);
	if (!stake)
		return rtpInfo;

	std::vector<double_t> rtps;
	rtps.reserve(3);    // Reserve space for efficiency


	if (mRTPs.contains(numOfDecks))
	{
		const auto& rtpMap = mRTPs.at(numOfDecks);

		for (const auto& [betTypeStr, betMultipliers] : stake->MultiplierOverrides)
		{
			auto type = EBaccaratBetType::_from_string_nothrow(betTypeStr.c_str());

			if (!type)
			{
				TLOG(LogDealersBaccaratBets, EVerbosity::Warning, "Invalid bet type: %s", betTypeStr);
				continue;
			}

			if (const auto rtp = rtpMap.find(type->_to_string()); rtp != rtpMap.end())
			{
				// For base game RTP use only Player, Banker and Tie and for variants use side bets
				if (*type == EBaccaratBetType::Player || *type == EBaccaratBetType::Banker || *type == EBaccaratBetType::Tie)
					rtps.push_back(rtp->second);
				else
					rtpInfo.Variants[betTypeStr] = rtp->second;
			}
		}
	}

	if (!rtps.empty())
	{
		auto [minIt, maxIt] = std::minmax_element(rtps.begin(), rtps.end());
		rtpInfo.BaseGame = { *minIt, *maxIt, (*minIt + *maxIt) / 2 };
	}

	return rtpInfo;
}

double_t TDealerBaccaratBets::GetVolatility(const uint8_t stakeID, const uint8_t numOfDecks) const
{
	return 66.66666;    // Placeholder value, as volatility calculation is not implemented
}
