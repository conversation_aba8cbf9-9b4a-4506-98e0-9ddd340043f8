//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "hosts/dealer/TDealersBets.h"

using namespace yserver::gamehost;

TDealersBets::TDealersBets(bool isOpenVersion) : bOpenVersion(isOpenVersion) {}

void TDealersBets::ClearStakes()
{
	mStakes.clear();
}

void TDealersBets::OnConfigLoaded(const std::filesystem::path& filename)
{
	for (const auto& [deckType, deckKey] : { std::pair { 8, "8-decks" }, std::pair { 6, "6-decks" } })
	{
		const json& rtps = GetConfig(deckKey);

		if (rtps.is_object())
		{
			for (const auto& rtp : rtps.items())
			{
				if (!rtp.value().is_number())
					throw std::runtime_error("RTP for bet type '" + rtp.key() + "' is invalid");

				mRTPs[deckType][rtp.key()] = rtp.value().get<double>();
			}
		}
	}
}

int TDealersBets::AddStake(const TDealersStake& stake)
{
	if (!stake.IsValid())
		throw std::runtime_error("All chip values are 0!");

	mStakes.push_back(std::make_shared<TDealersStake>(stake));
	return mStakes.size() - 1;
}

const std::vector<std::shared_ptr<TDealersStake>>& TDealersBets::GetAllStakes() const
{
	return mStakes;
}

const std::vector<std::shared_ptr<TDealersStake>> TDealersBets::GetValidStakes() const
{
	std::vector<std::shared_ptr<TDealersStake>> validStakes;
	std::copy_if(mStakes.begin(), mStakes.end(), std::back_inserter(validStakes), [](const std::shared_ptr<TDealersStake>& stake) { return stake && stake->IsValid(); });

	return validStakes;
}

std::shared_ptr<TDealersStake> TDealersBets::GetStake(uint32_t StakeID) const
{
	if (StakeID < mStakes.size())
		return mStakes[StakeID];

	return nullptr;
}

FBetVerifyResult TDealersBets::VerifyBets(BetAmounts& bets, const FDealersStakeInfo& stake, const BetAmounts& confirmedBets) const
{
	throw std::runtime_error("Not implemented");
}

double TDealersBets::GetMultiplier(std::string betType, int stakeID, std::string multiplierType) const
{
	throw std::runtime_error("Not implemented");
}

json TDealersBets::StakeAsJson(const TDealersStake& stake, uint32_t multiplier)
{
	json stakeObj(json::value_t::object);

	json Minimals(json::value_t::array);
	json Limits(json::value_t::array);
	json Multiples(json::value_t::array);
	for (auto bet_type : stake.GetBetTypes())
	{
		const FieldLimit limit = stake.GetFieldLimit(bet_type) * multiplier;
		Minimals.push_back(limit.Min);
		Limits.push_back(limit.Max);
		Multiples.push_back(limit.Multiple);
	}
	stakeObj["betTypeMin"] = std::move(Minimals);
	stakeObj["betTypeMax"] = std::move(Limits);
	stakeObj["betTypeMultiples"] = std::move(Multiples);
	stakeObj["playboardMax"] = stake.PlayboardLimitMax * multiplier;
	stakeObj["playboardMin"] = stake.PlayboardLimitMin * multiplier;
	stakeObj["maxNumBets"] = stake.BetsCountMax;
	stakeObj["maxWin"] = stake.MaxTableWinLimit * multiplier;

	json multOverrides(json::value_t::object);
	for (const auto& p : stake.MultiplierOverrides) multOverrides[p.first] = p.second.ToJson();
	stakeObj["multiplierOverrides"] = std::move(multOverrides);

	json chips(json::value_t::array);
	for (uint64_t chipVal : stake.ChipValues) { chips.push_back((chipVal == MAX_BET_CHIP_VALUE ? chipVal : (chipVal * multiplier))); }
	stakeObj["chipValues"] = std::move(chips);

	return stakeObj;
}

void TDealersBets::VerifyBetRaise(const BetAmounts& bets, const BetAmounts& closedBet, FBetVerifyResult& result) const
{
	throw std::runtime_error("Not implemented");
}
