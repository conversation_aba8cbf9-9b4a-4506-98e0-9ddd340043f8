//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "yserver/hosts/dealer/TDealerBaccaratGame.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;

TDealerBaccaratGame::TDealerBaccaratGame(const std::string& host_uid, const GameInformation& info) : TDealersGame(host_uid, info)
{
	mCommission = info.GetConfig("commission").get<bool>();
	bShowCardFaces = info.GetConfigOptional("free-hands.show-card-faces", true).get<bool>();

	mGameLogic = BaccaratGameLogic(false);
}

BetAmounts TDealerBaccaratGame::GetConfirmedBets() const
{
	return mConfirmedBets;
}

void TDealerBaccaratGame::ResetConfirmedBets()
{
	mConfirmedBets.Clear();
}

json TDealerBaccaratGame::GetGameRoundData() const
{
	TDealerCardGameExtraDataDto data;

	if (mConfirmedBets.Empty())
		data.bets = LastVerifiedBets;
	else
	{
		data.bets = mConfirmedBets;

		if (!LastVerifiedBets.Empty() && IsOpenVersion())
			data.raise = LastVerifiedBets;
	}

	data.gameInfo = GameInfo;
	data.creditMultiplier = CreditMultiplier;

	if (StakeInfo.Stake)
	{
		data.chipValues = StakeInfo.Stake->ChipValues;
		data.stakeInfoID = StakeInfo.ID;
	}

	auto winner = mGameLogic.GetWinner();
	if (winner != EBaccaratWinner::Null)
	{
		data.gameRecord = mGameLogic.GetGameRecord();
	}

	data.CalculateGameWins = WonResult;

	data.cards = mGameLogic.GetCurrentStateOfCards();

	return data.AsJSON();
}

const baccarat::BaccaratGameLogic& TDealerBaccaratGame::GetGameLogic() const
{
	return mGameLogic;
}

dealer_assist::GameRecordDto TDealerBaccaratGame::GetGameRecord() const
{
	return mGameLogic.GetGameRecord();
}

void TDealerBaccaratGame::ConfirmBets(const BetAmounts& placedBet)
{
	mConfirmedBets = placedBet;
}

void TDealerBaccaratGame::Unregister(const bool bClearWins)
{
	LastVerifiedBets.Clear();
	mConfirmedBets.Clear();
	ClearUncommittedBets();
	if (bClearWins)
		WonResult = {};
}

void TDealerBaccaratGame::AddCard(const uint32_t card, const uint8_t position)
{
	switch (EBaccaratDealingPhase::_from_integral(position))
	{
		case EBaccaratDealingPhase::PlayerFirstCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Player, 0); break;
		case EBaccaratDealingPhase::PlayerSecondCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Player, 1); break;
		case EBaccaratDealingPhase::PlayerThirdCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Player, 2); break;
		case EBaccaratDealingPhase::BankerFirstCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Banker, 0); break;
		case EBaccaratDealingPhase::BankerSecondCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Banker, 1); break;
		case EBaccaratDealingPhase::BankerThirdCard: mGameLogic.AddOrReplaceCard(card, EBaccaratSide::Banker, 2); break;
		default:
			// Handle unexpected phase
			// throw std::runtime_error("Unexpected dealing phase");
			break;
	}
}

void TDealerBaccaratGame::AddCutCard(uint32_t position)
{
	mGameLogic.AddCutCard(position);
}

std::vector<uint8_t> TDealerBaccaratGame::Evaluate()
{
	return mGameLogic.Evaluate();
}

uint8_t TDealerBaccaratGame::GetDealingPhase() const
{
	return mGameLogic.GetDealingPhase();
}

json TDealerBaccaratGame::GetGameResult() const
{
	return mGameLogic.GetGameResult();
}

json TDealerBaccaratGame::GetCurrentStateOfCards() const
{
	return mGameLogic.GetCurrentStateOfCards();
}

void TDealerBaccaratGame::SetCurrentStateOfCards(const json& cards)
{
	mGameLogic.SetCurrentStateOfCards(cards);
}

std::vector<uint8_t> TDealerBaccaratGame::GetWinners() const
{
	return mGameLogic.GetWinners();
}

ECardRule TDealerBaccaratGame::GetCardRule() const
{
	return mGameLogic.GetCardRule();
}

void TDealerBaccaratGame::FinishGameRound()
{
	mGameLogic.ClearGame();
	PlacedBet.Clear();
}

bool TDealerBaccaratGame::IsCommission() const
{
	return mCommission;
}

EBaccaratPlayboardMode TDealerBaccaratGame::GetPlayboardMode() const
{
	return mCommission ? EBaccaratPlayboardMode::Commission : EBaccaratPlayboardMode::NoCommission;
}

bool TDealerBaccaratGame::IsLastCard()
{
	return mGameLogic.IsGameFinished();
}

uint32_t TDealerBaccaratGame::GenerateGoldenCard()
{
	return mGameLogic.GetGoldenCard();
}

std::optional<uint32_t> TDealerBaccaratGame::GetGoldenCard()
{
	return std::nullopt;
}

bool TDealerBaccaratGame::ShouldDealtGoldenCard()
{
	return false;
}

uint32_t TDealerBaccaratGame::GetNumberOfCardsOnTable()
{
	return mGameLogic.GetNumberOfCardsOnTable();
}

bool TDealerBaccaratGame::IsPlayerDecisionNeeded()
{
	return mConfirmedBets.IsActiveOpenGameBet();
}

json TDealerBaccaratGame::GetGameExtraData() const
{
	json data(json::value_t::object);
	data["commission"] = mCommission;
	return data;
}
