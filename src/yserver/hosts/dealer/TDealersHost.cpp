//
// Created by <PERSON><PERSON><PERSON> on 7. 12. 24.
//

#include "hosts/dealer/TDealersHost.h"

#include <oneapi/tbb/concurrent_unordered_map.h>

#include <algorithm>
#include <execution>
#include <random>

#include "MyUtils.h"
#include "YServer.h"
#include "YServerTypes.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "dealer-assist/dto/AddCardDto.h"
#include "dealer-assist/dto/ChatMessageDto.h"
#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/ReportNumOfActivePlayersDto.h"
#include "dealer-assist/dto/SelectDealerGameRequestDto.h"
#include "dealer-assist/dto/ToggleChatDto.h"
#include "hosts/dealer/TDealerBaccaratBets.h"
#include "hosts/dealer/TDealerBaccaratGame.h"
#include "hosts/dealer/TDealerDragonTigerBets.h"
#include "hosts/dealer/TDealerThreeHeadedDragonBets.h"
#include "hosts/dragontiger/TDragonTigerStake.h"
#include "hosts/threeheadeddragon/TThreeHeadedDragonStake.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace yserver::gamehost::baccarat;
using namespace yserver::gamehost::dragontiger;
using namespace yserver::gamehost::threeheadeddragon;
using namespace dealer_assist;
using namespace yprotocol;

DEFINE_LOG_CATEGORY(LogDealerAssistHost, "dealer-assist-host")

const JsonSchema BaccaratStakes = JsonSchema(
  { { "stakes", JsonSchema(json::value_t::array, "List of stakes on this baccarat host", json(), true).SetChildSchema(TBaccaratStake::LiveStakeSchema(), 1) } });

const JsonSchema DragonTigerStakes =
  JsonSchema({ { "stakes", JsonSchema(json::value_t::array, "List of stakes on this dt host", json(), true).SetChildSchema(TDragonTigerStake::StakeSchema(), 1) } });

const JsonSchema OpenDragonTigerStakes = JsonSchema(
  { { "stakes", JsonSchema(json::value_t::array, "List of stakes on this open dt host", json(), true).SetChildSchema(TDragonTigerStake::OpenStakeSchema(), 1) } });

const JsonSchema ThreeHeadedDragonStakes = JsonSchema(
  { { "stakes",
      JsonSchema(json::value_t::array, "List of stakes on this 3headeddragon host", json(), true).SetChildSchema(TThreeHeadedDragonStake::StakeSchema(), 1) } });

const JsonSchema Stakes = JsonSchema({ { EGameType(EGameType::Baccarat)._to_string(), BaccaratStakes },
                                       { EGameType(EGameType::DragonTiger)._to_string(), DragonTigerStakes },
                                       { EGameType(EGameType::OpenDragonTiger)._to_string(), OpenDragonTigerStakes },
                                       { EGameType(EGameType::OpenBaccarat)._to_string(), BaccaratStakes },
                                       { EGameType(EGameType::ThreeHeadedDragon)._to_string(), ThreeHeadedDragonStakes } });

const JsonSchema DealerHostSchema =
  JsonSchema({ { "per-game-stakes", Stakes },
               { "allow-stake-change",
                 JsonSchema(json::value_t::boolean, "Weather to allow live-changing of the selected stake", true).Flag(ExportedSettingFlag).Flag(UserSettingFlag) },
               { "bet-timeout", JsonSchema(json::value_t::number_unsigned, "If > 0, bets older than this amount of seconds are cleared if the player is offline", 60U) },
               { "dealer-assist-table",
                 JsonSchema(
                   {
                     { "address", JsonSchema(json::value_t::string, "The address of dealer assist table") },
                     { "secure", JsonSchema(json::value_t::boolean, "If true, use TLS to connect") },
                     { "event-delay-time", JsonSchema(json::value_t::number_unsigned, "How long should all event from dealer assist table be delayed (ms)", 0U)
                                             .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 2001U)) },
                   },
                   "Dealer assist table configuration") } });

const JsonSchema DealersGameSchema =
  TGameHost::BaseGameSchema() +
  JsonSchema({ { "game-type", JsonSchema(json::value_t::string, "The type of the game this configuration relates to").SetToEnumType<dealer_assist::EGameType>() },
               { "commission",
                 JsonSchema(json::value_t::boolean, "Game is commission (in case of banker wins casino takes 5\%) or non-commission", false).Flag(CriticalSettingFlag) },
               { "dragonTailLength", JsonSchema(json::value_t::number_integer, "Length of dragon tail animation").SetRequired(false) } });

const JsonSchema BetSchema = JsonSchema(
  { { "bets",
      JsonSchema(json::value_t::array, "Array of bet placements", json(json::value_t::array)).SetChildSchema(JsonSchema(json::value_t::number_unsigned, "Bets")) },
    { "betPresets", JsonSchema(json::value_t::object, "Map of bet placements on preset fields", json(json::value_t::object))
                      .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The amount to bet on this field")) } });

const JsonSchema& TDealersHost::GetBetSchema() const
{
	return BetSchema;
}

const JsonSchema& TDealersHost::GetGameSchema() const
{
	return DealersGameSchema;
}

TDealersHost::TDealersHost(YModuleContainerBase* container, const std::string& name) : TGameHost(container, name, HostType::DealersGame)
{
	SetLogCategory(LogDealerAssistHost);
	bAllowStakeChange = false;
	bForceStakeSelect = false;
	bAllowMultiSession = false;
	bLiveGame = true;

	mGameRecords = std::vector<GameRecordDto>();

	GameStateChangedEvent = RegisterEvent("game-state-changed", "Sent when a variable describing the state of the game changes");
	ConfigChangedEvent = RegisterEvent("config-changed", "If the configuration of the host changes, this is fired");
	StakeChangedEvent = RegisterEvent("stakeChanged", "Triggered when the selected stake changes");
	CroupierEvent = RegisterEvent("croupier", "Triggered when a croupier leaves a table or a new one joins.");
	WinReportEvent = RegisterEvent("win-report", "Triggered when a game ends to report wins that have occurred");
	CutCardEvent = RegisterEvent("cut-card-pulled", "Triggered when a cut card is drawn");
	ChatToggleEvent = RegisterEvent("chat-toggle", "Triggered when the chat is toggled");
	RoundsUntilChangeEvent = RegisterEvent("rounds-until-change", "Triggered when the rounds until game change changes");

	// Check this one
	OnStatusChanged += [this](const FModuleStatusInfo& oldStatus, const FModuleStatusInfo& newStatus) {
		if (oldStatus.Status == EModuleStatus::Ready && newStatus.Status != EModuleStatus::Ready)
		{
			ForEachPlayer([&](Player& player) {
				ScopedLock lock(player);
				if (player.Game()->BetStatus() == EGameBetState::BetPlaced)
					player.VoidGame(FGameroundError({ EVoidReason(EVoidReason::ClientLocked)._to_string(), "Croupier left" }), false);
			});
		}
	};

	OnServiceStatusChanged += [this](bool bOnline) {
		if (!bOnline)
		{
			Log(Warning, "Connection with dealer assist service dropped!");
			{
				SharedScopedLock lock(mPlayers);
				if (mGameToAccountFor)
				{
					Log(Critical, "Game %lu has not been accounted for yet and will be void!", mGameToAccountFor);
					// VoidLiveGame("Communication with game service was dropped");
				}
			}
		}
	};

	Schema() += DealerHostSchema;

	RegisterAdminAction(
	  "Void Game For All Players", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, PlayerPtr player, const json& val, const YAuthKey& instigator) -> json {
		  VoidLiveGame("Admin void: " + val.get<std::string>());
		  return {};
	  },
	  JsonSchema(json::value_t::string, "Reason", std::string()));
}

TDealersHost::~TDealersHost() {}


void TDealersHost::OnConfigLoaded(const std::filesystem::path& filename)
{
	TLiveGameHost::OnConfigLoaded(filename);

	mBetTimeoutDuration = GetConfig("bet-timeout").get<uint64_t>() * 1000;
	bAllowStakeChange = GetConfig("allow-stake-change").get<bool>();

	mDealerAssistEventDelayTime = GetConfig("dealer-assist-table.event-delay-time").get<uint64_t>();
	mDealerAssistTableAddress = GetConfig("dealer-assist-table.address").get<std::string>();
	if (mDealerAssistTableAddress.empty())
		throw ConfigError("Dealer assist address is required!");
	bDealerAssistSecure = GetConfig("dealer-assist-table.secure").get<bool>();
	Log(Info, "Will connect to Live Table at %s", mDealerAssistTableAddress.c_str());

	mEventQueue.SetMaxEventAgeMs(mDealerAssistEventDelayTime);
	YClientPtr = std::make_shared<YClient>(mDealerAssistTableAddress, bDealerAssistSecure, "Dealer Assist Client");

	YClientPtr->OnInitialized += [this](const json& data) {
		mEventQueue.StartProcessing();
		try
		{
			DealerAssistInitResponseDto initData = DealerAssistInitResponseDto::FromJSON(data);
			ScopedLock lock(mDealerAssistVersion);
			&mDealerAssistVersion = initData.mAppVersion;
			mReceivedDealerAssistVersion.notify_all();
			OnInitDealerAssist(initData);
		}
		catch (const std::exception& e)
		{
			Log(Error, "Bad init packet - maybe Dealer Assist is out of date? (%s)", e.what());
			ScopedLock lock(mDealerAssistVersion);
			&mDealerAssistVersion = version::Version();
			mReceivedDealerAssistVersion.notify_all();
		}
	};

	YClientPtr->OnEvent += [this](const std::string& eventName, const json& data) {
		mEventQueue.PushEvent(eventName, data);
	};

	mEventQueue.OnBroadcastEvent += [this](const std::string& eventName, const json& data) {
		Log(Info, "DealerAssist received event: %s with data: %s", eventName.c_str(), JsonSchema::PrintValueInline(data).c_str());
		if (eventName == GAME_PHASE_CHANGED_EVENT_NAME)
		{
			OnGamePhaseChanged(DealerAssistStateUpdateDto::FromJSON(data));
		}
		else if (eventName == ADD_CARD_EVENT_NAME)
		{
			Log(Info, "Received card from dealer assist: %s", JsonSchema::PrintValueInline(data).c_str());

			auto card = AddCardDto::FromJSON(data);
			try
			{
				AddCard(card.GetCardValue(), card.mCardPosition);
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Failed to add card: %s", e.what());
			}
		}
		else if (eventName == CUT_CARD_DRAWN_EVENT_NAME)
		{
			BroadcastEventToPlayers(*CutCardEvent);
		}
		else if (eventName == POST_CHAT_MESSAGE_EVENT_NAME)
		{
			TriggerEvent(*ChatEvent, data);
		}
		else if (eventName == REPORT_BET_STATISTICS_RESULT)
		{
			TriggerEvent(*GameStateChangedEvent, data);
		}
		else if (eventName == ROUNDS_UNTIL_CHANGE_EVENT_NAME)
		{
			TriggerEvent(*RoundsUntilChangeEvent, data);

			ScopedLock lock(CurrentState);
			auto dto = RoundsUntilCloseDto::FromJSON(data);
			CurrentState->mRoundsUntilChange = dto;
		}
		else if (eventName == TOGGLE_CHAT_EVENT_NAME)
		{
			auto dto = ToggleChatDto::FromJSON(data);
			TriggerEvent(*ChatToggleEvent, dto.IsEnabled());

			ScopedLock lock(CurrentState);
			CurrentState->ChatEnabled = dto.IsEnabled();
		}
		else if (eventName == FLAG_STATE_CHANGED_EVENT_NAME)
		{
			const auto dto = FlagStateChangeDto::FromJSON(data);

			Log(Info, "Flag state changed: %s", Schema().PrintValueInline(data).c_str());

			ScopedLock lock(CurrentState);
			if (dto.GetFlag() == EFlagStateType::SupervisorCalled)
			{
				UpdateValue<bool>(CurrentState->bSupervisorCalled, dto.GetState(), "supervisorCalled");
				CurrentState->bSupervisorCalled = dto.GetState();
			}
			else if (dto.GetFlag() == EFlagStateType::GameChangingRequested)
			{
				UpdateValue<bool>(CurrentState->bGameChanging, dto.GetState(), "gameChangingRequested");
				CurrentState->bGameChanging = dto.GetState();
			}
			else if (dto.GetFlag() == EFlagStateType::TableClosingRequested)
			{
				UpdateValue<bool>(CurrentState->bTableClosing, dto.GetState(), "tableClosingRequested");
				CurrentState->bTableClosing = dto.GetState();
			}
		}
		else if (eventName == GAME_STATE_CHANGED_EVENT_NAME)
		{
			const json* state = FindMember(data, "state");
			if (state && !state->is_null())
				OnGameStateChanged(DealerAssistInitResponseDto::FromJSON(*state));
		}
	};

	YClientPtr->OnDisconnected += [this](bool bReconnect) {
		ServiceDisconnected("Dealer Assist service disconnected!");
		mEventQueue.StopProcessing();
	};

	Log(Info, "Changing stake during game is %s", bAllowStakeChange ? "ENABLED" : "DISABLED");

	bForceStakeSelect = GetConfigOptional("force-stake-select", true).get<bool>();
	if (bForceStakeSelect)
	{
		Log(Info, "If there are multiple stakes, the player will be forced to select one uppon joining.");
		/*if (mDefaultStakeRules.Default)
		    Log(Warning, "The 'default-stake' is set to %s, but wil be ignored because 'force-stake-select' is ON", stakeName.c_str());*/
	}

	const std::string accesModeStr = GetConfigOptional("stake-mode", EHostAccessMode(EHostAccessMode::All)._to_string()).get<std::string>();
	auto opt = EHostAccessMode::_from_string_nocase_nothrow(accesModeStr.c_str());
	if (!opt)
		throw ConfigError("Unknown stake-mode '" + accesModeStr + "'");
	mStakeAccessMode = *opt;

	json accessList = GetConfigOptional("stake-access-list", {});
	if (accessList.is_array())
	{
		for (const json& member : accessList)
		{
			if (member.is_number_unsigned())
				mStakeAccess.insert(member.get<uint32_t>());
		}
	}


	ParseStakes();

	// Load stakes
	auto defaultStake = GetConfigOptional("default-stake", 0).get<uint32_t>();
	Log(Info, "The default stake is set to %u.", defaultStake);
	for (const auto gameType : EGameType::_values())
	{
		switch (gameType)
		{
			case EGameType::Baccarat:
			case EGameType::OpenBaccarat: mBets[gameType] = std::make_shared<TDealerBaccaratBets>(gameType == EGameType::OpenBaccarat); break;

			case EGameType::DragonTiger:
			case EGameType::OpenDragonTiger: mBets[gameType] = std::make_shared<TDealerDragonTigerBets>(gameType == EGameType::OpenDragonTiger); break;

			case EGameType::ThreeHeadedDragon: mBets[gameType] = std::make_shared<TDealerThreeHeadedDragonBets>(true); break;

			default: throw ConfigError(std::format("Unknown game type: {}", gameType._to_string()));
		}

		mDefaultStakes[gameType] = defaultStake;
		LoadStakes(gameType);
	}

	RegisterAdminActions();
}

bool TDealersHost::ConnectToService(std::string& outError)
{
	if (!TLiveGameHost::ConnectToService(outError))
		return false;

	std::error_code ec;

	const std::string wsResource = std::format("/{}/{}_{}", DEALER_ASSIST_GAMEHOST_CLIENT_TYPE, boost::asio::ip::host_name(), ID());
	YClientPtr->Connect(wsResource, &ec);

	if (ec)
	{
		Log(Critical, "Could not connect connect to service at %s: %s", mDealerAssistTableAddress.c_str(), ec.message().c_str());
		return false;
	}

	SharedScopedLock lock(mDealerAssistVersion);
	mReceivedDealerAssistVersion.wait(lock, [&]() -> bool { return mDealerAssistVersion->has_value(); });

	if (EXPECTED_DEALER_ASSIST_VERSION.major != mDealerAssistVersion->value().major || EXPECTED_DEALER_ASSIST_VERSION.minor != mDealerAssistVersion->value().minor)
	{
		Log(Critical, "Dealer Assist app version mismatch! Expected %s, got %s", EXPECTED_DEALER_ASSIST_VERSION.to_string().c_str(),
		    mDealerAssistVersion->value().to_string().c_str());

		return false;
	}

	return true;
}

GameInstancePtr TDealersHost::CreateGameInstance(const GameInformation& game, const std::shared_ptr<GameEndpoint>& endp, const YPlayerAccount& account) const
{
	auto type = EGameType::_from_string_nothrow(game.GetConfig("game-type").get<std::string>().c_str());
	if (!type)
		throw std::runtime_error("Not a valid game type!");

	std::shared_ptr<TDealersGame> gameInstance;
	switch (type->_to_index())
	{
		case EGameType::Baccarat:
		case EGameType::OpenBaccarat: gameInstance = std::make_shared<TDealerBaccaratGame>(endp->HostUID, game, *type == EGameType::OpenBaccarat); break;
		case EGameType::DragonTiger:
		case EGameType::OpenDragonTiger: gameInstance = std::make_shared<TDragonTigerGame>(endp->HostUID, game, *type == EGameType::OpenDragonTiger); break;
		case EGameType::ThreeHeadedDragon: gameInstance = std::make_shared<TThreeHeadedDragonGame>(endp->HostUID, game); break;
		default: break;
	}

	if (!gameInstance)
		throw std::runtime_error("Unsupported game type!");

	gameInstance->CreditMultiplier = account.CreditValueMultiplier();

	if (endp->ExtraParams.contains("stake"))
	{
		const std::string stake = endp->ExtraParams.Get("stake");
		int stakeID;
		if (yutils::strToInt2(stake, stakeID))
			SetStake(*gameInstance, stakeID);
	}

	SharedScopedLock lock(CurrentState);
	if (CurrentState->DealersGame && CurrentState->DealersGame->GameType == gameInstance->GameType && CurrentState->Phase == EDealerAssistPhase::DealingCards)
	{
		Log(Info, "Set cards");
		gameInstance->SetCurrentStateOfCards(CurrentState->DealersGame->GetCurrentStateOfCards());
	}

	return gameInstance;
}

json TDealersHost::OnRegister(const YServerClient& client)
{
	json initResult = TGameHost::OnRegister(client);

	const YGameClient* gameClient = dynamic_cast<const YGameClient*>(&client);
	if (gameClient)
	{
		const TDealersGame& game = dynamic_cast<const TDealersGame&>(*gameClient->Game());
		initResult["config"] = GetGameConfig(game);
	}

	json history = json::value_t::array;
	for (const auto& record : mGameRecords.getCopy()) { history.push_back(record.ToJSON()); }

	initResult["history"] = std::move(history);
	initResult["stream"] = GetVideoStreamInfo();

	return initResult;
}

json TDealersHost::GetGameState(const std::shared_ptr<const YGameClient>& client) const
{
	json gameState = TLiveGameHost::GetGameState(client);
	SharedScopedLock lock(CurrentState);
	gameState["gameID"] = CurrentState->GameID;
	gameState["gameType"] = CurrentState->DealersGame ? json(CurrentState->DealersGame->GameType._to_string()) : json();
	gameState["virtual"] = CurrentState->bVirtualDealer;

	auto now = ytime::GetTimeMsec();
	if (CurrentState->Phase == EDealerAssistPhase::BetsOpen)
		gameState["timeLeft"] = CurrentState->TotalBetTimeMs - (now - CurrentState->Timestamp);
	else if (CurrentState->Phase == EDealerAssistPhase::Decision)
		gameState["timeLeft"] = CurrentState->TotalDecisionTimeMs - (now - CurrentState->Timestamp);

	gameState["totalTime"] = CurrentState->TotalBetTimeMs;
	gameState["animationTime"] = CurrentState->TotalAnimationTimeMs;
	gameState["decisionTime"] = CurrentState->TotalDecisionTimeMs;
	gameState["phase"] = CurrentState->Phase._to_index();

	auto records = mGameRecords.getCopy();
	gameState["handCount"] = records.size();
	gameState["cardCount"] = records.size() * 5;    // TODO: Just temporary
	gameState["numOfDecks"] = mNumOfDecks;
	gameState["cardRule"] = CurrentState->DealersGame ? CurrentState->DealersGame->GetCardRule()._to_string() : ECardRule(ECardRule::Asian)._to_string();
	gameState["supervisorCalled"] = CurrentState->bSupervisorCalled;
	gameState["gameChangingRequested"] = CurrentState->bGameChanging;
	gameState["tableClosingRequested"] = CurrentState->bTableClosing;

	if (CurrentState->mRoundsUntilChange)
		gameState["roundsUntilChange"] = CurrentState->mRoundsUntilChange->ToJSON();

	if (CurrentState->DealersGame)
	{
		gameState["cards"] = CurrentState->DealersGame->GetCurrentStateOfCards();
		gameState["phaseSubID"] = CurrentState->DealersGame->GetDealingPhase();
	}

	if (!CurrentState->Winners.empty())
		gameState["winner"] = CurrentState->Winners;

	if (client && client->Game())
	{
		if (auto playerGame = dynamic_cast<TDealersGame*>(client->Game().get()))
		{
			if (auto goldenCard = playerGame->GetGoldenCard(); goldenCard.has_value())
				gameState["goldenCard"] = *goldenCard;

			if (playerGame->GetConfirmedBets().Empty())
				gameState["bets"] = playerGame->LastVerifiedBets.BetsAsJSON(playerGame->CreditMultiplier);
			else
			{
				gameState["bets"] = playerGame->GetConfirmedBets().BetsAsJSON(playerGame->CreditMultiplier);

				if (!playerGame->LastVerifiedBets.Empty() && playerGame->IsOpenVersion())
					gameState["raise"] = playerGame->LastVerifiedBets.BetsAsJSON(playerGame->CreditMultiplier);
			}
		}
	}

	Log(Info, "GetGameState: %s", JsonSchema::PrintValueInline(gameState).c_str());

	return gameState;
}

json TDealersHost::GetGameConfig(const TDealersGame& game) const
{
	json conf = game.GetGameExtraData();
	json stakes(json::value_t::array);

	if (mBets[game.GameType]->GetAllStakes().size() > 0)
	{
		for (const auto& stake : mBets[game.GameType]->GetAllStakes()) stakes.push_back(TDealersBets::StakeAsJson(*stake, 1));
	}

	conf["stakes"] = std::move(stakes);
	Log(Info, "selectedStake %i.", mDefaultStakes[game.GameType]);
	conf["selectedStake"] = mDefaultStakes[game.GameType];
	conf["cardRule"] = game.GetCardRule()._to_string();
	conf["betTypes"] = mBets[game.GameType]->GetBetTypes();

	return conf;
}

void TDealersHost::OnPlayerAdded(Player& player, bool bReconnected) noexcept
{
	TGameHost::OnPlayerAdded(player, bReconnected);

	auto game = dynamic_cast<TDealersGame*>(player.Game().get());

	if (game->BetTimeout)
	{
		game->BetTimeout->cancel();
		game->BetTimeout.reset();
	}

	OnRoundFailureResolved(player);    // to start gameround

	if (!bReconnected)
	{
		if (player.ConInfo->Query.contains("stake"))
		{
			const std::string stake = player.ConInfo->Query.Get("stake");
			int stakeID;
			if (yutils::strToInt2(stake, stakeID) && stakeID >= 0 && stakeID < (int)mBets[game->GameType]->GetValidStakes().size())
				game->StakeInfo = { stakeID, mBets[game->GameType]->GetStake(stakeID), 1 };
		}
	}

	// If a stake is already selected, then we're ok!
	if (game->StakeInfo.ID != -1 && game->StakeInfo.Stake)
		return;

	// If we only have one stake or we aren't forcing a stake select, go to default stake
	if (mBets[game->GameType]->GetValidStakes().size() == 1 || !bForceStakeSelect)
	{
		const int stakeID = (mDefaultStakes[game->GameType] >= mBets[game->GameType]->GetValidStakes().size()) ? 0U : mDefaultStakes[game->GameType];
		SetStake(*game, stakeID);
		json stakeChangeData(json::value_t::object);
		stakeChangeData["id"] = stakeID;
		stakeChangeData["stake"] = TDealersBets::StakeAsJson(*game->StakeInfo.Stake, 1);
		player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
	}
	else
	{
		std::vector<ChoiceQuestion::Choice> choices;
		int i = 0;
		for (auto stake : mBets[game->GameType]->GetValidStakes())
		{
			ChoiceQuestion::Choice thisStake;
			thisStake.Title = "Stake " + std::to_string(i++);
			thisStake.Data = TDealersBets::StakeAsJson(*stake, 1);
			choices.push_back(thisStake);
		}
		auto question = std::make_shared<ChoiceQuestion>("stake", "Select a stake", choices);
		player.Ask(question, [this](YProtocolClient& client, QuestionPtr q, const json& answer, json& finalResponse) -> EQuestionResult {
			Player& player = dynamic_cast<Player&>(client);
			auto game = dynamic_cast<TDealersGame*>(player.Game().get());
			if (answer.size())
			{
				int stakeID = answer[0].get<uint32_t>();
				SetStake(*game, stakeID);
				json stakeChangeData(json::value_t::object);
				stakeChangeData["id"] = stakeID;
				stakeChangeData["stake"] = TDealersBets::StakeAsJson(*game->StakeInfo.Stake, 1);

				Log(Info, "Player selected stake %s", JsonSchema::PrintValueInline(stakeChangeData).c_str());
				player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
				finalResponse = stakeChangeData;
				return EQuestionResult::OK;
			}
			else
			{
				return EQuestionResult::ASK_AGAIN;
			}
		});
	}
}

void TDealersHost::ReportChangedNumberOfPlayers() const
{
	const auto data = ReportNumOfActivePlayersDto(GetNumPlayersWithMoney(), NumPlayers());

	auto type = YClientPtr->Request(PLAYER_COUNT_CHANGED_EVENT_NAME, data.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		if (fut.get().Status == EMessageStatus::ResponseOk)
		{
			Log(Normal, "Received response from live table");
		}
	});
}

void TDealersHost::RegisterAdminActions()
{
	RegisterAdminAction(
	  "Login worker with barcode", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Scanned code %s", params.get<std::string>().c_str());

			  YClientPtr->Request(ADD_CARD_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on sending code to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Badge ID. Croupier: #FFFF0000   Supervisor: #BBBBBBBB", "#FFFF0000"));

	RegisterAdminAction(
	  "Login supervisor", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Scanned code %s", params.get<std::string>().c_str());

			  YClientPtr->Request(ADD_CARD_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on sending code to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Badge ID. Croupier: #FFFF0000   Supervisor: #BBBBBBBB", "#BBBBBBBB"));

	RegisterAdminAction("Redraw cards", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = EDealerAction(EDealerAction::RedrawCards)._to_string();

		                    YClientPtr->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on redraw cards.");
			                    }
		                    });

		                    return {};
	                    });

	RegisterAdminAction(
	  "Add one card", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Scanned code %s", params.get<std::string>().c_str());

			  YClientPtr->Request(ADD_CARD_EVENT_NAME, params).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on sending card to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Add card with barcode. Barcode example: QD3, KS2, 4H2", "2S4"));

	RegisterAdminAction("Action [S] change game", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::ChangeGame)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    Log(Normal, "Received response on action change game.");
		                    });

		                    return {};
	                    });

	RegisterAdminAction("Action [D] call supervisor", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = EDealerAction(EDealerAction::CallSupervisor)._to_string();

		                    YClientPtr->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    Log(Normal, "Received response on call supervisor.");
		                    });

		                    return {};
	                    });

	RegisterAdminAction("Action [S] void game", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::VoidGame)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    Log(Normal, "Received response on change shoe and voiding game.");
		                    });

		                    return {};
	                    });

	RegisterAdminAction("Action [S] toggle change shoe", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::ChangeShoe)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    Log(Normal, "Received response on change shoe request.");
		                    });

		                    return {};
	                    });

	RegisterAdminAction("Action [S] close table", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::CloseTable)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
				                    Log(Normal, "Received response on close table.");
		                    });

		                    return {};
	                    });

	RegisterAdminAction(
	  "Change game with type", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Game name %s", params.get<std::string>().c_str());

			  auto gameType = EGameType::_from_string(params.get<std::string>().c_str());

			  auto dto = SelectDealerGameRequestDto(gameType, std::nullopt);

			  YClientPtr->Request(CHANGE_GAME_EVENT_NAME, dto.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				  if (fut.get().Status == EMessageStatus::ResponseOk)
				  {
					  Log(Normal, "Received response on changing game to dealer assist.");
				  }
			  });
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Change game: Baccarat, OpenBaccarat, DragonTiger, OpenDragonTiger, ThreeHeadedDragon", "DragonTiger"));



	RegisterAdminAction("Get available games", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    YClientPtr->Request(GET_GAMES_EVENT_NAME).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    const auto response = fut.get();
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Info, "Available games: %s", response.Message.Body().dump().c_str());
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Cancel supervisor action", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    YClientPtr->Request(CANCEL_SUPERVISOR_ACTION_EVENT_NAME).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    const auto response = fut.get();
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Info, "Cancel supervisor action: %s", response.Message.Body().dump().c_str());
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Send changed result", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    json card1(json::value_t::object);
		                    card1["new"] = 101;
		                    card1["old"] = 103;
		                    json cards(json::value_t::object);
		                    cards["0"] = std::move(card1);
		                    req["cards"] = std::move(cards);

		                    YClientPtr->Request(CHANGE_GAME_RESULT_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    const auto response = fut.get();
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Info, "Result changed: %s", response.Message.Body().dump().c_str());
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Action [S] change result", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::ChangeResult)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    const auto response = fut.get();
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Info, "Change result action triggered: %s", response.Message.Body().dump().c_str());
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Action [S] free hand result", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = ESupervisorAction(ESupervisorAction::FreeHand)._to_string();

		                    YClientPtr->Request(SUPERVISOR_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    const auto response = fut.get();
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Info, "Free hand action triggered: %s", response.Message.Body().dump().c_str());
			                    }
		                    });
		                    return {};
	                    });

	RegisterAdminAction("Action [D] shoe changed", EModuleAdminActionTarget::Module,
	                    [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    json req(json::value_t::object);
		                    req["action"] = EDealerAction(EDealerAction::ShoeChanged)._to_string();

		                    YClientPtr->Request(DEALER_ACTION_EVENT_NAME, req).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    if (fut.get().Status == EMessageStatus::ResponseOk)
			                    {
				                    Log(Normal, "Received response on shoe change.");
			                    }
		                    });

		                    return {};
	                    });



	RegisterAdminAction(
	  "Send chat message", EModuleAdminActionTarget::Module,
	  [this](const Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  if (!params.empty())
		  {
			  Log(Info, "Message content %s", params.get<std::string>().c_str());

			  PostChatMessage(params.get<std::string>(), AccountID("Test user"));
		  }

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Enter chat message."));

	RegisterAdminAction("Get chat messages", EModuleAdminActionTarget::Module,
	                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    YClientPtr->Request(GET_CHAT_MESSAGES).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
			                    auto response = fut.get();
			                    if (response.Status == EMessageStatus::ResponseOk)
				                    Log(Info, "Received response on get chat messages. %s", response.Message.Body().dump().c_str());
		                    });

		                    return {};
	                    });
}

void TDealersHost::OnGamePhaseChanged(const DealerAssistStateUpdateDto& dto)
{
	auto phase = dto.GetPhase();
	auto gameId = dto.GetRoundId();

	{
		ScopedLock lock(CurrentState);

		if (!CurrentState->DealersGame)
			return;

		auto currentPhase = CurrentState->Phase;

		if (CurrentState->Phase != phase)
		{
			Log(Info, "Phase changed from %s to %s", CurrentState->Phase._to_string(), phase._to_string());
			UpdateValue<uint32_t>(CurrentState->Phase._to_index(), phase._to_index(), "phase");
		}
		if (CurrentState->GameID != gameId)
		{
			UpdateValue<uint64_t>(CurrentState->GameID, gameId, "gameID");
			OpenNewRound(gameId);
		}

		CurrentState->Phase = phase;
		CurrentState->GameID = gameId;

		if (dto.subPhase.has_value())
			UpdateValue<uint32_t>(0, dto.subPhase.value(), "phaseSubID");

		if (phase == EDealerAssistPhase::BetsClosed)
		{
			OnBetsClosed_AssumeLockedState();
			ReportChangedNumberOfPlayers();

			if (CurrentState->DealersGame->ShouldDealtGoldenCard() && currentPhase == EDealerAssistPhase::BetsOpen)
				AddGoldenCard_AssumedWriteLock();
		}

		if (phase == EDealerAssistPhase::BetsOpen)
		{
			CurrentState->Timestamp = ytime::GetTimeMsec();
			ReportChangedNumberOfPlayers();
		}

		if (phase == EDealerAssistPhase::Decision)
		{
			CurrentState->Timestamp = ytime::GetTimeMsec();
		}

		if (phase == EDealerAssistPhase::ShoeChange)
		{
			ScopedLock lockGameRecords(mGameRecords);
			mGameRecords->clear();
			ClearCurrentGame_AssumeLockedState(true);
		}

		if (phase == EDealerAssistPhase::RoundVoid)
		{
			lock.unlock();
			VoidLiveGame("Game voided by dealer assist");
		}

		if (phase == EDealerAssistPhase::RoundEnd)
		{
			Log(Info, "Received RoundEnd - Calculating winner!");

			std::map players = mPlayers.getCopy();

			ParallelFor(players.begin(), players.end(), [](const std::pair<std::string, PlayerPtr>& player) {
				ScopedLock lock(*player.second);
				TDealersGame* playerGame = static_cast<TDealersGame*>(player.second->Game().get());
				playerGame->Evaluate();
			});

			auto winner = CurrentState->DealersGame->Evaluate();
			OnWinner_AssumedLockedState(winner);
		}
	}
}

void TDealersHost::OnGameStateChanged(const DealerAssistInitResponseDto& dto)
{
	Log(Info, "GameStateChanged");

	ScopedLock lock(CurrentState);

	CurrentState->TotalBetTimeMs = dto.mBetTimeMs;
	CurrentState->TotalAnimationTimeMs = dto.mAnimationTimeMs;
	CurrentState->TotalDecisionTimeMs = dto.mDecisionTimeMs;

	CurrentState->bSupervisorCalled = dto.mActionsState.bSupervisorCalled;
	CurrentState->bGameChanging = dto.mActionsState.bGameChangeRequested;
	CurrentState->bTableClosing = dto.mActionsState.bTableCloseRequested;

	CurrentState->mRoundsUntilChange = dto.mRoundsUntilClose;

	if (dto.mGameInfo)
		CurrentState->bVirtualDealer = dto.mGameInfo->IsVirtual;

	if (dto.mTableState.mRoundID.has_value())
		CurrentState->GameID = *dto.mTableState.mRoundID;

	mLastStateOfCards = dto.mTableState.mCards;

	try
	{
		EGameType type = EGameType::_from_string(dto.mGameType.c_str());
		if (!CurrentState->DealersGame || CurrentState->DealersGame->GameType != type)
			GameSwitched_AssumedWriteLock(type);
	}
	catch (const std::exception& e)
	{
		Log(Info, "Could not switch game (maybe game is not active on DAT): %s", e.what());
	}

	if (CurrentState->DealersGame)
		CurrentState->DealersGame->SetCurrentStateOfCards(mLastStateOfCards);

	json gameState = json::value_t::object;

	gameState["gameID"] = CurrentState->GameID;
	gameState["phase"] = CurrentState->Phase._to_index();
	gameState["phaseSubID"] = CurrentState->DealersGame ? CurrentState->DealersGame->GetDealingPhase() : 0;
	gameState["gameType"] = CurrentState->DealersGame ? json(CurrentState->DealersGame->GameType._to_string()) : json();
	gameState["virtual"] = CurrentState->bVirtualDealer;
	gameState["supervisorCalled"] = CurrentState->bSupervisorCalled;
	gameState["gameChangingRequested"] = CurrentState->bGameChanging;
	gameState["tableClosingRequested"] = CurrentState->bTableClosing;

	auto now = ytime::GetTimeMsec();
	if (CurrentState->Phase == EDealerAssistPhase::BetsOpen)
		gameState["timeLeft"] = CurrentState->TotalBetTimeMs - (now - CurrentState->Timestamp);
	else if (CurrentState->Phase == EDealerAssistPhase::Decision)
		gameState["timeLeft"] = CurrentState->TotalDecisionTimeMs - (now - CurrentState->Timestamp);

	gameState["totalTime"] = CurrentState->TotalBetTimeMs;
	gameState["animationTime"] = CurrentState->TotalAnimationTimeMs;
	gameState["decisionTime"] = CurrentState->TotalDecisionTimeMs;

	auto records = mGameRecords.getCopy();
	gameState["handCount"] = records.size();
	gameState["cardCount"] = records.size() * 5;    // TODO: Just temporary until we actualy count cards

	if (!CurrentState->Winners.empty())
		gameState["winner"] = CurrentState->Winners;

	auto cards = CurrentState->DealersGame ? CurrentState->DealersGame->GetCurrentStateOfCards() : json(json::value_t::array);
	gameState["cards"] = cards;
	gameState["numOfDecks"] = dto.mNumOfDecks;
	gameState["cardRule"] = CurrentState->DealersGame ? CurrentState->DealersGame->GetCardRule()._to_string() : ECardRule(ECardRule::Asian)._to_string();

	if (CurrentState->mRoundsUntilChange)
		gameState["roundsUntilChange"] = CurrentState->mRoundsUntilChange->ToJSON();

	UpdateValue<json>(json(), gameState, "gameState");
}

void TDealersHost::OnInitDealerAssist(const DealerAssistInitResponseDto& dto)
{
	Log(Info, "Dealer Assist initialized! %s", JsonSchema::PrintValueInline(dto.ToJSON()).c_str());

	SetDomain(dto.mTableId);
	ScopedLock lock(CurrentState);

	CurrentState->TotalBetTimeMs = dto.mBetTimeMs;
	CurrentState->TotalAnimationTimeMs = dto.mAnimationTimeMs;
	CurrentState->TotalDecisionTimeMs = dto.mDecisionTimeMs;
	CurrentState->Timestamp = dto.mTimestamp;
	CurrentState->Phase = dto.mTableState.mPhase;
	CurrentState->bSupervisorCalled = dto.mActionsState.bSupervisorCalled;
	CurrentState->bGameChanging = dto.mActionsState.bGameChangeRequested;
	CurrentState->bTableClosing = dto.mActionsState.bTableCloseRequested;
	if (dto.mTableState.mRoundID.has_value())
		CurrentState->GameID = *dto.mTableState.mRoundID;
	CurrentState->ChatEnabled = dto.mActionsState.bChatEnabled;
	mVideoStreamType = dto.mStreamType;
	mVideoStreamUrl = dto.mStreamUrl;
	mVideoStreamId = dto.mStreamId;
	mNumOfDecks = dto.mNumOfDecks;
	mLastStateOfCards = dto.mTableState.mCards;

	CurrentState->mRoundsUntilChange = dto.mRoundsUntilClose;

	if (dto.mGameInfo)
		CurrentState->bVirtualDealer = dto.mGameInfo->IsVirtual;

	try
	{
		EGameType type = EGameType::_from_string(dto.mGameType.c_str());
		if (!CurrentState->DealersGame || CurrentState->DealersGame->GameType != type)
			GameSwitched_AssumedWriteLock(type);
	}
	catch (const std::exception& e)
	{
		Log(Info, "Could not switch game (maybe game is not active on DAT): %s", e.what());
	}
}

void TDealersHost::OnRoundFailureResolved(Player& player) noexcept
{
	if (player.Game()->IsGameroundActive())
		return;

	uint64_t gid = 0;
	{
		SharedScopedLock lock(CurrentState);
		gid = CurrentState->GameID;
	}

	if (gid)
		player.GameRoundBegin(std::to_string(gid));
}

void TDealersHost::OnPlayerDisconnected(Player& player)
{
	Log(Info, "OnPlayerDisconnected - Player has left the game");
	TGameHost::OnPlayerDisconnected(player);

	if (!mBetTimeoutDuration)
		return;

	auto game = dynamic_cast<TDealersGame*>(player.Game().get());
	if (game->BetStatus() != EGameBetState::BetPlaced || !game->LastBetTime)
		return;

	const int64_t TimeUntilBetClear = int64_t(game->LastBetTime + mBetTimeoutDuration) - ytime::GetTimeMsec();
	if (TimeUntilBetClear <= 0)
		player.VoidGame(FGameroundError({ "BetsTooOld", "Connection lost with bets that are too old" }), false);
	else
		game->BetTimeout = Container()->Server->SetTimer(TimeUntilBetClear, [ref = player.weak_from_this(), gid = game->GameRound()](const std::error_code& ec) {
			if (ec)
				return;

			auto player = std::dynamic_pointer_cast<Player>(ref.lock());
			if (!player)
				return;

			ScopedLock lock(*player);
			if (player->Game()->GameRound() != gid)
				return;

			if (player->Game()->BetStatus() == EGameBetState::BetPlaced)
				player->VoidGame(FGameroundError({ "BetsTooOld", "Bets cleared because player has old bets and is offline" }), false);
		});
}

void TDealersHost::OnPlayerGone(Player& player) noexcept
{
	TGameHost::OnPlayerGone(player);
	Log(Info, "OnPlayerGone - Player has left the game");
}

void TDealersHost::UnregisterPlayer(Player& player, const std::optional<FGameroundError>& err)
{
	if (player.Game()->IsGameroundActive())
		player.ProcessGameResult({}, err);
	player.TryToEndAccountingRound();
}

bool TDealersHost::DisconnectService()
{
	TLiveGameHost::DisconnectService();

	ClearCurrentGame(true);

	if (YClientPtr)
	{
		YClientPtr->Stop();
	}

	return false;
}

// BETTING
const std::array<std::shared_ptr<TDealersBets>, EGameType::_size()>& TDealersHost::GetBets() const
{
	return mBets;
}

uint64_t TDealersHost::VerifyBets(const YGameClient& client, const yprotocol::Request& req, json& response) const
{
	Log(Info, "VerifyBets");
	const DealerAssistState state = CurrentState.getCopy();

	if (!state.GameID || req.GetParam("gameID").get<uint64_t>() != state.GameID)
		throw BetRequestError(req, "Invalid game ID (playing " + std::to_string(state.GameID) + ")!", FBetErrorInfo("InvalidGame"));

	if (!client.Game()->IsGameroundActive())
		throw BetRequestError(req, "Not participating in a game round. Please wait for a round to start.", FBetErrorInfo("InvalidGame"));

	if (state.Phase != EDealerAssistPhase::BetsOpen && state.Phase != EDealerAssistPhase::Decision)
		throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("BetsClosed"));

	/*{
	    SharedScopedLock lock2(mPlayers);
	    if (mGameToAccountFor)
	        throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("InvalidGame"));
	}*/

	TDealersGame* gameInst = (TDealersGame*)client.Game().get();

	auto bets = GetBets();

	try
	{
		// Will throw a BetParseError if unsuccessful
		BetAmounts betAmounts;
		try
		{
			betAmounts = BetAmounts::FromUserJSON(req.GetParam(), gameInst->CreditMultiplier);
		}
		catch (const BetParseError& err)
		{
			throw BetRequestError(req, err.what(), FBetErrorInfo("InvalidBetObject", err.Context));
		}

		FDealersStakeInfo stake = gameInst->StakeInfo;
		stake.Multiplier *= gameInst->CreditMultiplier;

		const FBetVerifyResult verifiedBets = bets[gameInst->GameType]->VerifyBets(betAmounts, stake, gameInst->GetConfirmedBets());
		if (!verifiedBets.Violation)
		{
			if (verifiedBets.TotalBet || gameInst->BetStatus() == EGameBetState::Deciding)
			{
				Log(Warning, "Total bet is greater of 0 or bet status is deciding - place bets");
				gameInst->PlacedBet = betAmounts;
				response = betAmounts.BetsAsJSON(gameInst->CreditMultiplier);
				return verifiedBets.TotalBet;
			}
			if (verifiedBets.TotalBet == 0 && gameInst->BetStatus() != EGameBetState::Deciding)
			{
				Log(Warning, "Total bet is o and bet status is NOT deciding");
				gameInst->PlacedBet.Clear();
				response = betAmounts.BetsAsJSON(gameInst->CreditMultiplier);
				return verifiedBets.TotalBet;
			}

			Log(Warning, "ELSE verify bets");
		}
		if (*verifiedBets.Violation == EBetRuleViolation::BadSetup)
		{
			throw BetRequestError(req, "Invalid stake", FBetErrorInfo("InternalError"));
		}

		FBetErrorInfo err("BetRuleViolation");
		err.Details = json(json::value_t::object);
		err.Details["x"] = verifiedBets.ErrorX;
		err.Details["violation"] = verifiedBets.Violation->_to_string();
		throw BetRequestError(req, verifiedBets.Message, err);
	}
	catch (const json::exception& err)
	{
		throw BetRequestError(req, "Problem executing commitBets: " + std::string(err.what()) + "!", FBetErrorInfo("InternalError"));
	}
}

void TDealersHost::BetPlaced(YGameClient& client, const FBetPlacedResult& betResult, const json& response)
{
	Log(Info, "BetPlaced: %s", JsonSchema::PrintValueInline(response).c_str());

	// leave previous possible bet on the table, undo this one
	TDealersGame& game = dynamic_cast<TDealersGame&>(*client.Game());

	if (!betResult.Error)
	{
		if (CurrentState->Phase == EDealerAssistPhase::Decision)
		{
			if (game.LastVerifiedBets.Empty())
			{
				Log(Info, "No previous bets, setting new bet as last verified");
				game.LastVerifiedBets = game.PlacedBet;
			}
			else
			{
				Log(Info, "Previous bets found, applying changes");
				game.LastVerifiedBets.ApplyBetChanges(game.PlacedBet.Get());
			}
		}
		else
		{
			game.LastVerifiedBets = game.PlacedBet;
		}

		game.LastBetTime = ytime::GetTimeMsec();
	}

	game.PlacedBet.Clear();
}


void TDealersHost::OnAddGameToHost(const StaticGameInformation& info)
{
	TLiveGameHost::OnAddGameToHost(info);

	// if this is unset, means that we have not yet established coms with the DA table, so it's okay to return here, since the initialization will be done
	// when the DA table is ready
	auto opt = CurrentGameType.getCopy();
	if (!opt)
		return;

	if (info.bEnabled && info.GetConfig("game-type").get<std::string>() == opt->_to_string())
	{
		ScopedLock lock(CurrentState);
		GameSwitched_AssumedWriteLock(*opt);
	}
}

GameHostGameInformation TDealersHost::RemoveGameLaunchConfig(const std::string& game)
{
	GameHostGameInformation removedGame = TLiveGameHost::RemoveGameLaunchConfig(game);

	// if this is unset, means that we have not yet established coms with the DA table, so it's okay to return here, since the initialization will be done
	// when the DA table is ready
	auto opt = CurrentGameType.getCopy();
	if (!opt)
		return removedGame;

	if (removedGame.bEnabled && removedGame.GetConfig("game-type").get<std::string>() == opt->_to_string())
	{
		ScopedLock lock(CurrentState);
		GameSwitched_AssumedWriteLock(*opt);
	}

	return removedGame;
}

// Handling phases
void TDealersHost::AddCard(const int card, const uint8_t position)
{
	ScopedLock stateLock(CurrentState);

	if (!CurrentState->DealersGame)
		return;

	if (CurrentState->Phase != EDealerAssistPhase::DealingCards)
		throw InternalError(std::string("Can not add card, game is in wrong state: ") + CurrentState->Phase._to_string());

	Log(Info, "Dealing to position %i card with id %d", position, card);
	ProcessCardAddition_AssumeLocked(card, position);
}

void TDealersHost::ProcessCardAddition_AssumeLocked(int card, uint8_t dealingPhase)
{
	CurrentState->DealersGame->AddCard(card, dealingPhase);

	std::map<std::string, PlayerPtr> players = mPlayers.getCopy();

	ParallelFor(players.begin(), players.end(), [card, dealingPhase](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		player.second->Game()->PushRandNumber(json(static_cast<int64_t>(card)));
		TDealersGame* playerGame = static_cast<TDealersGame*>(player.second->Game().get());
		playerGame->AddCard(card, dealingPhase);
	});

	if (CurrentState->DealersGame->IsLastCard())
	{
		Log(Info, "Calculating winner!");
		auto winners = CurrentState->DealersGame->Evaluate();
		SendCard(card, dealingPhase, winners);
	}
	else
	{
		SendCard(card, dealingPhase);
	}
}

void TDealersHost::SendCard(const int card, int position, std::optional<std::vector<uint8_t>> winners)
{
	json cardObj(json::value_t::object);
	cardObj["name"] = "addNewCard";
	cardObj["index"] = position;
	cardObj["card"] = card % 1000;    // Card without deck number

	if (winners)
		cardObj["winner"] = *winners;

	Log(Info, "Sending card %s to players", JsonSchema::PrintValueInline(cardObj).c_str());
	ForEachPlayer([this, &cardObj](Player& player) { player.TriggerEvent(*GameStateChangedEvent, cardObj); });
}

template <typename T>
void TDealersHost::UpdateValue(const T& oldValue, T newValue, const std::string& eventTrigger)
{
	if (newValue != oldValue)
	{
		json phaseChangeData;
		phaseChangeData["name"] = eventTrigger;
		phaseChangeData["old"] = oldValue;
		phaseChangeData["new"] = newValue;
		TriggerEvent(*GameStateChangedEvent, phaseChangeData);
	}
}

void TDealersHost::OpenNewRound(const uint64_t roundId)
{
	std::string newRound = std::to_string(roundId);

	ForEachPlayer([&newRound](yserver::Player& player) {
		ScopedLock lock(player);
		if (player.Game()->IsGameroundActive() && player.Game()->BetStatus() < EGameBetState::PendingResult)
		{
			std::optional<FGameroundError> err;
			if (player.Game()->BetStatus() == EGameBetState::BetPlaced)
				err = FGameroundError({ "NewRoundStarted", "A new game round was started!" });
			player.ProcessGameResult({}, err);
		}

		if (!newRound.empty() && !player.Game()->IsGameroundActive())
			player.GameRoundBegin(newRound);
	});
}

void TDealersHost::OnBetsClosed_AssumeLockedState()
{
	uint64_t accountForGame = 0;
	{
		accountForGame = CurrentState->GameID;
	}

	if (!CurrentState->DealersGame)
		return;

	const bool bIsFirstBet = CurrentState->DealersGame->GetNumberOfCardsOnTable() == 0;

	// Unregister players who's accounting didn't go through
	ScopedLock lock(mPlayers);
	YPlayerList playersToIterate = &mPlayers;
	mGameToAccountFor = accountForGame;
	lock.unlock();

	CurrentState->Phase = EDealerAssistPhase::DealingCards;
	CurrentState->DealingPhase = 0;

	std::atomic<size_t> numBets = 0;

	ParallelFor(playersToIterate.begin(), playersToIterate.end(),
	            [this, &numBets, bIsFirstBet, gameIDStr = std::to_string(accountForGame)](const std::pair<std::string, PlayerPtr>& player) {
		            CommitBetsResult result;
		            ScopedLock lock(*player.second);
		            auto game = std::dynamic_pointer_cast<TDealersGame>(player.second->Game());
		            if (game->GameRound() != gameIDStr)
			            return;

		            if (bIsFirstBet)
		            {
			            game->ConfirmBets(game->LastVerifiedBets);
			            game->LastVerifiedBets.Clear();
		            }

		            if (game->BetStatus() == EGameBetState::BetPlaced)
		            {
			            result = player.second->CommitBets();

			            if (game->IsOpenVersion() && bIsFirstBet && game->GetConfirmedBets().IsActiveOpenGameBet())
				            player.second->ProcessGameResult(0);
		            }
		            else if (game->BetStatus() == EGameBetState::Deciding)
		            {
			            result = player.second->CommitBets();
		            }
		            else
		            {
			            result.Message = "No bets were placed";
		            }

		            if (game->BetTimeout)
		            {
			            game->BetTimeout->cancel();
			            game->BetTimeout.reset();
		            }

		            if (result.Success() || (game->IsOpenVersion() && !bIsFirstBet && !game->GetConfirmedBets().IsActiveOpenGameBet()))
		            {
			            numBets++;
		            }
		            else
		            {
			            if (game->BetStatus() != EGameBetState::Idle)
				            UnregisterPlayer(*player.second, FGameroundError({ EVoidReason(EVoidReason::BetFailed)._to_string(), result.Message }));
			            else
				            UnregisterPlayer(*player.second, {});
		            }
	            });

	Log(Info, "Bets closed in game %lu with %lu bets", accountForGame, numBets.load());
}

void TDealersHost::OnWinner_AssumedLockedState(std::vector<uint8_t> winners)
{
	CurrentState->Winners = winners;
	UpdateValue<json>(json(), winners, "winner");

	ScopedLock lockGameRecords(mGameRecords);
	auto gameRecord = CurrentState->DealersGame->GetGameRecord();
	gameRecord.GameRoundId = CurrentState->GameID;
	mGameRecords->push_back(gameRecord);

	UpdateValue<json>(json(), gameRecord.ToJSON(), "roundEnd");

	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	const size_t numPlayersTotal = players.size();
	if (!numPlayersTotal)
	{
		Log(Info, "Got winner with index %s in game %i", CurrentState->WinnersToString().c_str(), accountForGame);
		ClearCurrentGame_AssumeLockedState(true);
		return;
	}

	const std::string accountForGameRound = std::to_string(accountForGame);

	for (auto it = players.begin(); it != players.end();)
	{
		ScopedLock lock(*it->second);
		auto playerGame = std::dynamic_pointer_cast<TDealersGame>(it->second->Game());

		if (playerGame->BetStatus() == EGameBetState::Idle || accountForGameRound != playerGame->GameRound() || it->second->Game()->IsVoid() ||
		    CurrentState->DealersGame->GameType != playerGame->GameType)    // not registered!
		{
			it = players.erase(it);
			continue;
		}

		it->second->Log(Debug, "Processing winner...");
		if (playerGame->BetStatus() != EGameBetState::PendingResult)
		{
			it->second->SetPersistence(EPlayerPersistenceMode::Game, false);
			it->second->Log(Warning, "Game %lu was not started correctly. This should not happen and indicates a problem somewhere.", accountForGame);
			UnregisterPlayer(*it->second, FGameroundError({ "GameStartFailed", "Game not started correctly" }));
			it = players.erase(it);
			continue;
		}

		// TODO: In separate task generalize game logic
		if (auto baccaratGame = std::dynamic_pointer_cast<TDealerBaccaratGame>(CurrentState->DealersGame))
		{
			auto bets = std::dynamic_pointer_cast<TDealerBaccaratBets>(GetBets()[baccaratGame->GameType]);

			playerGame->WonResult =
			  bets->CalculateWins(playerGame->LastVerifiedBets, playerGame->StakeInfo, baccaratGame->GetGameLogic(), playerGame->GetConfirmedBets());
		}
		else if (auto dragonTigerGame = std::dynamic_pointer_cast<TDragonTigerGame>(CurrentState->DealersGame))
		{
			auto bets = std::dynamic_pointer_cast<TDealerDragonTigerBets>(GetBets()[dragonTigerGame->GameType]);

			playerGame->WonResult =
			  bets->CalculateWins(playerGame->GetConfirmedBets(), playerGame->StakeInfo, dragonTigerGame->GetGameLogic(), playerGame->LastVerifiedBets);
		}
		else if (auto threeHeadedDragonGame = std::dynamic_pointer_cast<TThreeHeadedDragonGame>(CurrentState->DealersGame))
		{
			auto bets = std::dynamic_pointer_cast<TDealerThreeHeadedDragonBets>(GetBets()[threeHeadedDragonGame->GameType]);

			playerGame->WonResult =
			  bets->CalculateWins(playerGame->GetConfirmedBets(), playerGame->StakeInfo, threeHeadedDragonGame->GetGameLogic(), playerGame->LastVerifiedBets);
		}

		it++;
	}

	Log(Info, "Got win number %s in game %lu. Processing game result for %lu/%lu players...", CurrentState->WinnersToString().c_str(), accountForGame, players.size(),
	    numPlayersTotal);

	const std::string gameroundMessage = "The winning number is " + CurrentState->WinnersToString();
	std::atomic<double> wonTotalCents = 0.0;
	tbb::concurrent_unordered_map<AccountID, std::pair<double, double>, AccountIDHash> winEuroCents;
	ParallelFor(players.begin(), players.end(), [&](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);

		TDealersGame* playerGame = static_cast<TDealersGame*>(player.second->Game().get());
		const uint64_t WonCredits = playerGame->WonResult.TotalWon;

		playerGame->ResetConfirmedBets();

		Log(Info, "Player won %lu credits", WonCredits);

		if (!winners.empty())
			player.second->ProcessGameResult(WonCredits);
		else
			player.second->ProcessGameResult(WonCredits, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), gameroundMessage }));

		if (WonCredits && !player.second->Account()->IsDemo())
		{
			double rate;
			if (Container()->Server->CurrencyAPI->GetExchangeRate(ECurrency(ECurrency::EUR)._to_string(), player.second->Account()->Currency(), rate))
			{
				const double wonEuroCents = WonCredits * 1e2 * player.second->Account()->Denomination() / rate;
				wonTotalCents += wonEuroCents;
				auto emplaced = winEuroCents.emplace(player.second->Account()->ID(), std::pair(wonEuroCents, rate));
				if (!emplaced.second)
					emplaced.first->second.first += wonEuroCents;
			}
		}
		Log(Debug, "Processing win done for player %s.", player.second->Account()->ID().Username.c_str());
	});

	std::vector<std::pair<std::string, uint64_t>> winList;
	winList.reserve(winEuroCents.size());
	for (const auto& pair : winEuroCents) { winList.push_back({ pair.first.AsString(), (uint64_t)std::round(pair.second.first) }); }
	std::sort(std::execution::par, winList.begin(), winList.end(), [](const auto& l, const auto& r) -> bool { return l.second > r.second; });
	json winReportList(json::value_t::object);
	size_t n = 0;
	for (auto it = winList.begin(); it != winList.end() && n < mPlayersPerPage; it++)
	{
		winReportList[it->first] = it->second;
		n++;
	}

	json winReport(json::value_t::object);
	winReport["totalWon"] = wonTotalCents.load();
	winReport["registeredPlayers"] = players.size();
	winReport["winList"] = winReportList;
	TriggerEvent(*WinReportEvent, winReport);

	ClearCurrentGame_AssumeLockedState(true);
}

void TDealersHost::OnGameRoundBegin(YGameClient& player)
{
	TGameHost::OnGameRoundBegin(player);

	TDealersGame* gamePtr = dynamic_cast<TDealersGame*>(player.Game().get());
	gamePtr->ClearUncommittedBets();
}

void TDealersHost::OnGameRoundEnd(YGameClient& player, const GameRoundSnapshot& game)
{
	TGameHost::OnGameRoundEnd(player, game);
	TDealersGame* gamePtr = dynamic_cast<TDealersGame*>(player.Game().get());
	gamePtr->Unregister(true);
}

void TDealersHost::VoidGame(YGameClient& client, EVoidReason reason, const std::string& message)
{
	client.Log(Warning, "Voiding game for %s: %s", reason._to_string(), message.c_str());

	if (Player* player = dynamic_cast<Player*>(&client))
		UnregisterPlayer(*player, FGameroundError({ reason._to_string(), message }));
}

void TDealersHost::ClearCurrentGame(const bool clearPlayers)
{
	ScopedLock lock(CurrentState);
	ClearCurrentGame_AssumeLockedState(clearPlayers);
}

void TDealersHost::ClearCurrentGame_AssumeLockedState(const bool clearPlayers)
{
	if (CurrentState->DealersGame)
		CurrentState->DealersGame->FinishGameRound();

	UpdateValue<json>(CurrentState->Winners, json(), "winner");
	CurrentState->Winners.clear();

	if (clearPlayers)
	{
		std::map<std::string, PlayerPtr> players = mPlayers.getCopy();
		ParallelFor(players.begin(), players.end(), [](const std::pair<std::string, PlayerPtr>& player) {
			ScopedLock lock(*player.second);
			TDealersGame* playerGame = static_cast<TDealersGame*>(player.second->Game().get());
			playerGame->FinishGameRound();
		});
	}
}

void TDealersHost::VoidLiveGame(const std::string& reason)
{
	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lockPlayers(mPlayers);
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	if (accountForGame)
	{
		{
			ScopedLock lock(CurrentState);
			bCurrentRoundVoid = (accountForGame == CurrentState->GameID);
		}

		Log(Warning, "No result for game %lu (%s). Will void any bets made by the existing %lu players.", accountForGame, reason.c_str(), players.size());
	}
	else
	{
		{
			SharedScopedLock lock(CurrentState);
			accountForGame = CurrentState->GameID;
		}

		if (accountForGame)
			Log(Warning, "Not in a game currently, but voiding it anyway (%s) to clear any placed bets made by the existing %lu players.", reason.c_str(),
			    players.size());
	}

	if (!accountForGame)
		return;

	ParallelFor(players.begin(), players.end(), [this, gid = std::to_string(accountForGame), &reason](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		if (gid == player.second->Game()->GameRound())
			VoidGame(*player.second, EVoidReason::NoGameResult, reason);
	});

	ClearCurrentGame(true);
}

bool TDealersHost::IsValidStake(const uint32_t stakeIdx) const
{
	switch (mStakeAccessMode)
	{
		case EHostAccessMode::All: return true;
		case EHostAccessMode::OptIn: return mStakeAccess.contains(stakeIdx);
		case EHostAccessMode::OptOut: return !mStakeAccess.contains(stakeIdx);
	}
	return false;
}

void TDealersHost::SetStake(TDealersGame& game, uint32_t stakeID) const
{
	if (stakeID >= mBets[game.GameType]->GetAllStakes().size())
		return;

	game.StakeInfo.ID = stakeID;
	game.StakeInfo.Stake = mBets[game.GameType]->GetStake(stakeID);
	game.StakeInfo.Multiplier = 1;

	if (!game.HasBetsInGame())
	{
		/*if (game.StakeInfo.Stake)
		{
		    for (size_t idx = 0; idx < game.StakeInfo.Stake->ChipValues.size(); idx++)
		        game.SavedChipValues[idx] = game.StakeInfo.Multiplier * game.StakeInfo.Stake->ChipValues[idx];
		}*/
	}
}

void TDealersHost::GetGameDetails(GameInformation& info) const
{
	TGameHost::GetGameDetails(info);

	auto gameType = info.GetConfig("game-type").get<std::string>();
	auto type = EGameType::_from_string_nothrow(gameType.c_str());
	if (!type)
	{
		Log(Warning, "Missing game type %s!", gameType.c_str());
		return;
	}

	const auto defaultStake = mDefaultStakes[*type];

	info.RTP = mBets[*type]->GetTotalRTP(defaultStake, mNumOfDecks, EPlayboardMode::NoCommission);
	info.Volatility = mBets[*type]->GetVolatility(defaultStake, mNumOfDecks);

	for (const auto& stake : mBets[*type]->GetAllStakes())
	{
		if (info.MaxBet == 0 || stake->PlayboardLimitMax > info.MaxBet)
			info.MaxBet = stake->PlayboardLimitMax;

		if (info.MinBet == 0 || stake->PlayboardLimitMin < info.MinBet)
			info.MinBet = stake->PlayboardLimitMin;

		for (const auto& betType : stake->MultiplierOverrides) { info.MaxWin = std::max(info.MaxWin, static_cast<uint64_t>(betType.second.Items.end()->second)); }
	}

	Log(Normal, "Max bet: %i, Min bet: %i, Max win: %i, Average RTP: %f, Volatility: %f", info.MaxBet, info.MinBet, info.MaxWin, info.RTP.BaseGame.Average,
	    info.Volatility);
}

json TDealersHost::GetDescriptorInfo(const FModuleDescriptorOptions& options) const
{
	json conf(json::value_t::object);
	conf["stakeChangeAllowed"] = bAllowStakeChange;
	conf["stream"] = GetVideoStreamInfo();

	return conf;
}

json TDealersHost::GetVideoStreamInfo() const
{
	json stream(json::value_t::object);

	json impliedSource(json::value_t::object);
	impliedSource["url"] = mVideoStreamUrl;
	impliedSource["id"] = mVideoStreamId;
	stream["source"] = std::move(impliedSource);
	stream["type"] = mVideoStreamType._to_string();

	return stream;
}

bool TDealersHost::AllowJackpot() const
{
	return TGameHost::AllowJackpot();
}

std::shared_ptr<jackpot::TJackpotModule> TDealersHost::Jackpot(const std::shared_ptr<provider::TCasinoProvider>& provider, const StaticGameInformation& game,
                                                               bool bDemo) const noexcept
{
	return std::shared_ptr<jackpot::TJackpotModule>();
}

std::map<std::string, std::string> TDealersHost::Checksums(const GameHostGameInformation& info, const std::optional<std::string>& launchOption) const
{
	std::map<std::string, std::string> Ret = TGameHost::Checksums(info, launchOption);
	Ret["baccarat"] = "temporaryChecksum";

	return Ret;
}

// Restoring game rounds
void TDealersHost::TryRoundRestore(YGameClient& client, const GameRoundSnapshot& snap)
{
	TLiveGameHost::TryRoundRestore(client, snap);
	TDealersGame& game = (TDealersGame&)*client.Game();

	if (!snap.GeneratedRandoms.empty())
		throw yprotocol::InternalError("Gameround data indicates the game is completed!");

	try
	{
		auto bets = FindMember(snap.ExtraData, "bets");
		auto raise = FindMember(snap.ExtraData, "raise");

		if (bets && bets->is_object() && raise && raise->is_object())
		{
			Log(Warning, "Restoring bets and raise");
			BetAmounts betAmounts;
			BetAmounts::FromJSON(*bets, betAmounts, game.CreditMultiplier);
			game.ConfirmBets(betAmounts);

			BetAmounts raiseAmounts;
			BetAmountsDto::FromJSON(*bets, raiseAmounts, game.CreditMultiplier);
			game.LastVerifiedBets = raiseAmounts;
		}
		else if (bets && bets->is_object())
		{
			Log(Warning, "Restoring bets");
			BetAmounts betAmounts;
			BetAmountsDto::FromJSON(*bets, betAmounts, game.CreditMultiplier);
			game.ConfirmBets(betAmounts);
		}
	}
	catch (const BetParseError& e)
	{
		throw yprotocol::InternalError(e.what());
	}

	if (game.LastVerifiedBets.Empty())
		throw yprotocol::InternalError("There are no placed bets to restore!");

	if (auto stake = FindMember(snap.ExtraData, "stake"))
		if (stake && stake->is_number_integer())
			SetStake(game, stake->get<int>());
}

void TDealersHost::TryRoundResume(Player& player, const GameRoundSnapshot& snap)
{
	TGameHost::TryRoundResume(player, snap);

	if (!player.Game()->IsGameroundActive())
		return;

	TDealersGame& game = (TDealersGame&)*player.Game();
	const auto latestState = CurrentState.getCopy();

	if (!latestState.DealersGame || latestState.DealersGame->GameType != game.GameType)
		throw yprotocol::InternalError("The game cannot be resumed because the current game type is different");

	if (!game.StakeInfo.Stake || (snap.ExtraData.contains("stake") && game.StakeInfo.ID != snap.ExtraData["stake"].get<int>()))
		throw yprotocol::InternalError("The stake used in the game is not available anymore");

	const std::string latestGameID = std::to_string(latestState.GameID);
	// if not the current gameround or we already have the win number of the current gameround
	if (game.GameRound() != latestGameID || latestState.Phase == EDealerAssistPhase::RoundEnd)
	{
		std::vector<uint8_t> winners = { EBaccaratWinner::Null };

		if (game.GameRound() == latestGameID)
		{
			winners = latestState.DealersGame->GetWinners();
		}
		else
		{
			const u_int64_t GameroundID = yutils::strToInt(game.GameRound(), 0);
			auto history = mGameRecords.getCopy();
			auto it = std::find_if(history.begin(), history.end(), [GameroundID](const GameRecordDto& record) { return record.GameRoundId == GameroundID; });

			if (it != history.end())
				winners = it->Winners;
			else
				Log(Critical, "No information exists in history for gameround %s", game.GameRound().c_str());
		}

		if (game.BetStatus() == EGameBetState::BetPlaced)
		{
			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}

		if (!winners.empty())
			player.ProcessGameResult(game.WonResult.TotalWon);
		else
			player.ProcessGameResult(game.WonResult.TotalWon, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), "No win number read" }));
	}
	else
	{
		SharedScopedLock lock(mPlayers);
		const bool bWaitingForWinNumber = mGameToAccountFor;
		lock.unlock();

		// if bBetsClosed matches game.bHasAccounted bets, then do nothing, game will be finished after OnRegister happens and after the win number is read
		const bool bHasAccountedBets = game.BetStatus() > EGameBetState::PendingResult;
		if (bWaitingForWinNumber != bHasAccountedBets)
		{
			if (bHasAccountedBets)
				throw yprotocol::InternalError("Unfinished game bets were accounted but server has not accounted bets for this game yet!");

			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}
	}
}

void TDealersHost::PostChatMessage(const std::string& message, const AccountID& playerId)
{
	// Check if message contains URL
	if (MyUtils::ContainsURL(message))
		return;

	std::string username = playerId.AsString();
	auto players = mCachedPlayerList.getCopy();
	auto it = players.find(playerId);

	if (it != players.end())
		username = it->second.Nickname;

	ChatMessageDto dto = ChatMessageDto(message, username, playerId, ytime::GetSystemTimeMsec(), false);

	YClientPtr->Request(POST_CHAT_MESSAGE_EVENT_NAME, dto.ToJSON()).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		if (fut.get().Status == EMessageStatus::ResponseOk)
		{
			Log(Normal, "Received response on chat posting");
		}
	});
}

bool TDealersHost::IsAvailable(const StaticGameInformation& game) const
{
	auto gameType = CurrentGameType.getCopy();
	return gameType.has_value() && TGameHost::IsAvailable(game) && game.GetConfig("game-type").get<std::string>() == gameType.value()._to_string();
}

void TDealersHost::StopTimer(const std::weak_ptr<rtfwk_sdl2::TTimedTaskHandler>& timer)
{
	if (auto task = timer.lock())
	{
		task->Disable();
	}
}

void TDealersHost::GameSwitched_AssumedWriteLock(EGameType gameType)
{
	Log(Info, "Game type changed from %s to %s", CurrentState->DealersGame ? CurrentState->DealersGame->GameType._to_string() : "NONE", gameType._to_string());

	RemovePlayersFromServer("Switching game");

	ScopedLock lockGameRecords(mGameRecords);
	mGameRecords->clear();

	CurrentGameType = gameType;
	DoAsyncTask([this]() { GameAvailableConditionChanged(); });

	Log(Info, "Starting games as: %s", gameType._to_string());

	CurrentState->DealersGame.reset();

	auto games = LaunchInfo();
	for (const auto& [gameKey, game] : games.Games)
	{
		if (game.bEnabled && game.GetConfig("game-type").get<std::string>() == gameType._to_string())
		{
			GameInformation gameInformation(game);
			GetGameDetails(gameInformation);
			if (gameType == EGameType::Baccarat || gameType == EGameType::OpenBaccarat)
			{
				CurrentState->DealersGame = std::make_shared<TDealerBaccaratGame>(UniqueIdentifier(), gameInformation, gameType == EGameType::OpenBaccarat);
			}
			else if (gameType == EGameType::DragonTiger || gameType == EGameType::OpenDragonTiger)
			{
				CurrentState->DealersGame = std::make_shared<TDragonTigerGame>(UniqueIdentifier(), gameInformation, gameType == EGameType::OpenDragonTiger);
			}
			else if (gameType == EGameType::ThreeHeadedDragon)
			{
				CurrentState->DealersGame = std::make_shared<TThreeHeadedDragonGame>(UniqueIdentifier(), gameInformation);
			}
			break;
		}
	}

	if (CurrentState->DealersGame)
	{
		CurrentState->DealersGame->SetCurrentStateOfCards(mLastStateOfCards);
		Log(Info, "Game state for %s loaded with cards: %s", gameType._to_string(), JsonSchema::PrintValueInline(mLastStateOfCards).c_str());
	}
	else
	{
		Log(Warning, "No valid game configurations could be used to create a game state of type %s.", gameType._to_string());
	}
}

void TDealersHost::RemovePlayersFromServer(std::string reason)
{
	std::map<std::string, PlayerPtr> players = mPlayers.getCopy();
	ParallelFor(players.begin(), players.end(), [reason](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		player.second->RemoveFromServer(reason, false);
	});
}


void TDealersHost::AddGoldenCard_AssumedWriteLock()
{
	YPlayerList playersToIterate = mPlayers.getCopy();
	ParallelFor(playersToIterate.begin(), playersToIterate.end(), [this](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		auto game = std::dynamic_pointer_cast<TDealersGame>(player.second->Game());
		auto goldenCard = game->GenerateGoldenCard();

		json obj(json::value_t::object);
		obj["name"] = "goldenCard";
		obj["card"] = goldenCard;

		player.second.get()->TriggerEvent(*GameStateChangedEvent, obj);
	});
}

void TDealersHost::ParseStakes()
{
	mStakes.clear();

	const json stakesPerHost = GetConfig("per-game-stakes");

	if (!stakesPerHost.is_object())
	{
		throw ConfigError("Invalid stakesPerHost configuration: Expected an object.");
	}

	for (auto it = stakesPerHost.begin(); it != stakesPerHost.end(); ++it)
	{
		const std::string& gameType = it.key();
		const json& gameConfig = it.value();

		Log(Info, "Reading stakes for %s", gameType.c_str());

		if (!gameConfig.is_object() || !gameConfig.contains("stakes") || !gameConfig["stakes"].is_array())
		{
			Log(Warning, "Game type %s has no valid stakes array.", gameType.c_str());
			continue;
		}

		const json& stakesArray = gameConfig["stakes"];

		for (size_t stakeID = 0; stakeID < stakesArray.size(); ++stakeID)
		{
			try
			{
				auto schema = TDragonTigerStake::StakeSchema();
				if (gameType == EGameType(EGameType::OpenBaccarat)._to_string() || gameType == EGameType(EGameType::Baccarat)._to_string())
					schema = TBaccaratStake::LiveStakeSchema();
				else if (gameType == EGameType(EGameType::ThreeHeadedDragon)._to_string())
					schema = TThreeHeadedDragonStake::StakeSchema();

				TDealersStake stake;
				stake.SetSchema(schema);
				stake.LoadSubconfiguration(stakesArray[stakeID]);
				mStakes[gameType].push_back(stake);
				Log(Info, "Loaded stake %zu for game type %s", stakeID, gameType.c_str());
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Game type %s, stake %zu is invalid: %s", gameType.c_str(), stakeID, e.what());
			}
		}
	}

	if (mStakes.empty())
		throw ConfigError("No valid stakes available in the config");

	const size_t allStakesNum = mStakes.size();
	Log(Info, "Successfully read %lu stakes from config!", allStakesNum);
}

void TDealersHost::LoadStakes(const EGameType gameType)
{
	auto stakes = mStakes.find(gameType._to_string())->second;

	size_t numStakes = 0;
	for (size_t stakeIdx = 0; stakeIdx < stakes.size(); stakeIdx++)
	{
		if (!stakes[stakeIdx].IsValid())
		{
			Log(Warning, "Stake %lu is invalid and will be skipped! This means chip values are all 0.", stakeIdx);
			continue;
		}
		if (TDealersHost::IsValidStake(stakeIdx))
		{
			try
			{
				mBets[gameType]->AddStake(stakes[stakeIdx]);
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Stake %d is invalid: %s", stakeIdx, e.what());
			}
			if (mDefaultStakes[gameType] == stakeIdx && mDefaultStakes[gameType] != numStakes)
			{
				Log(Warning, "The default stake is set to %u, due because some stakes before %u are not used, it is effectively translated to %lu.",
				    mDefaultStakes[gameType], mDefaultStakes[gameType], numStakes);
				mDefaultStakes[gameType] = numStakes;
			}
			numStakes++;
		}
		else if (stakeIdx == mDefaultStakes[gameType])
		{
			Log(Warning, "The default stake is set to %u, this stake is disabled due to stake-mode and stake-access-list being configured to ignore it.",
			    mDefaultStakes[gameType]);
		}
	}

	if (!numStakes)
		throw ConfigError("No valid stakes were found that conform with the conditions defined by stake-mode and stake-access-list!");


	Log(Info, "%lu of the total %lu stakes will be used on this game host!", numStakes, stakes.size());

	if (mDefaultStakes[gameType] >= numStakes)
	{
		Log(Warning, "The default stake is set to %u, but no such stake exists on the dealers host! Will use 0 as the default stake.", mDefaultStakes[gameType]);
	}
}
