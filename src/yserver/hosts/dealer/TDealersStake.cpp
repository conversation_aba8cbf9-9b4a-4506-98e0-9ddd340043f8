//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "hosts/dealer/TDealersStake.h"

#include <format>

#include "MyUtils.h"
#include "hosts/dragontiger/TDragonTigerHostSharedTypes.h"

using namespace yserver::gamehost;
using namespace yserver::gamehost::dragontiger;

FieldLimit TDealersStake::GetFieldLimit(const std::string fieldType) const
{
	return mLimitsTable.at(fieldType);
}

std::vector<std::string> TDealersStake::GetBetTypes() const
{
	return mBetTypes;
}

void TDealersStake::SetSchema(const JsonSchema& schema)
{
	Schema() += schema;
}

bool TDealersStake::IsValid() const
{
	for (const uint64_t& chipVal : ChipValues)
	{
		if (!IsMultiplierValid(chipVal))    // If chip is not divisible by any of the multipliers represented as fraction, it's invalid
			return false;
		if (chipVal)
			return true;
	}
	// if all the chip values are 0, it's an invalid stake for sure
	return false;
}

bool TDealersStake::IsMultiplierValid(const uint64_t chipValue) const
{
	for (const auto& multiplier : MultiplierOverrides)
	{
		for (const auto& item : multiplier.second.Items)
		{
			if (item.second == 0)
				return false;
		}
	}

	return true;
}

void TDealersStake::SetMaxBet(const std::string fieldType, const uint64_t maxBet)
{
	mLimitsTable[fieldType].Max = maxBet;
}

void TDealersStake::SetMinBet(const std::string fieldType, const uint64_t minBet)
{
	mLimitsTable[fieldType].Min = minBet;
}

void TDealersStake::SetFieldLimit(const std::string fieldType, const FieldLimit& limit)
{
	mLimitsTable[fieldType] = limit;
}

void TDealersStake::SetMultiple(const std::string fieldType, const uint64_t multiple)
{
	mLimitsTable[fieldType].Multiple = multiple;
}

TDealersStake::TDealersStake() : TConfiguration(), PlayboardLimitMin(0), PlayboardLimitMax(0), BetsCountMax(0), MaxTableWinLimit(0)
{
	ChipValues.fill(0);
}

void TDealersStake::OnConfigLoaded(const std::filesystem::path& filename)
{
	BetsCountMax = GetConfig("maxNumBets").get<uint32_t>();
	MaxTableWinLimit = GetConfig("tableWinLimit").get<uint64_t>();
	PlayboardLimitMin = GetConfig("minBet").get<uint64_t>();
	PlayboardLimitMax = GetConfig("maxBet").get<uint64_t>();

	const json& chipVals = GetConfig("chipValues");
	for (uint i = 0; i < chipVals.size(); i++) ChipValues[i + 1] = chipVals[i].get<uint64_t>();

	mBetTypes.clear();
	const json& betTypes = GetConfig("betTypes");
	for (auto betType = betTypes.begin(); betType != betTypes.end(); ++betType)
	{
		uint64_t min = 0, max = 0, multiple = 0;
		if (!betType->is_object())
			throw std::runtime_error("Bet type object '" + betType.key() + "' is invalid - value needs to be an object containing 'min' and 'max' members");


		auto typ = betType.key();

		mBetTypes.push_back(typ);

		min = (*betType)["min"].get<uint64_t>();
		max = (*betType)["max"].get<uint64_t>();
		multiple = (*betType)["multiple"].get<uint64_t>();

		if (betType->contains("multiplier"))
		{
			const auto& multiplierValue = (*betType)["multiplier"];
			if (multiplierValue.is_number())
			{
				std::map<std::string, double> items;
				items[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = multiplierValue.get<double>();
				MultiplierOverrides[typ] = BetMultiplier(items);
			}
			else if (multiplierValue.is_object())
			{
				std::map<std::string, double> items;
				for (auto betMultiplierItem = multiplierValue.begin(); betMultiplierItem != multiplierValue.end(); ++betMultiplierItem)
				{
					items[betMultiplierItem.key()] = betMultiplierItem.value().get<double>();
					MultiplierOverrides[typ] = BetMultiplier(items);
				}
			}
		}

		SetMinBet(typ, min);
		SetMaxBet(typ, max);
		SetMultiple(typ, multiple);
	}
}

json TDealersStake::MultiplierOverridesToJson() const
{
	json multipliers(json::value_t::object);
	for (const auto& multiplier : MultiplierOverrides) { multipliers[multiplier.first] = multiplier.second.ToJson(); }
	return multipliers;
}
