//
// Created by <PERSON><PERSON><PERSON> on 8. 12. 24.
//

#include "hosts/dealer/TDealersStake.h"

#include <format>

#include "MyUtils.h"
#include "hosts/dragontiger/TDragonTigerHostSharedTypes.h"

using namespace yserver::gamehost;
using namespace yserver::gamehost::dragontiger;

FieldLimit TDealersStake::GetFieldLimit(const std::string fieldType) const
{
	return mLimitsTable.at(fieldType);
}

std::vector<std::string> TDealersStake::GetBetTypes() const
{
	return mBetTypes;
}

void TDealersStake::SetSchema(const JsonSchema& schema)
{
	Schema() += schema;
}

bool TDealersStake::IsValid() const
{
	for (const uint64_t& chipVal : ChipValues)
	{
		if (!IsMultiplierValid(chipVal))    // If chip is not divisible by any of the multipliers represented as fraction, it's invalid
			return false;
		if (chipVal)
			return true;
	}
	// if all the chip values are 0, it's an invalid stake for sure
	return false;
}

bool TDealersStake::IsMultiplierValid(const uint64_t chipValue) const
{
	for (const auto& cardRule : MultiplierOverrides)
	{
		for (const auto& betType : cardRule.second)
		{
			for (const auto& item : betType.second.Items)
			{
				if (item.second == 0)
					return false;
			}
		}
	}

	return true;
}

void TDealersStake::SetMaxBet(const std::string fieldType, const uint64_t maxBet)
{
	mLimitsTable[fieldType].Max = maxBet;
}

void TDealersStake::SetMinBet(const std::string fieldType, const uint64_t minBet)
{
	mLimitsTable[fieldType].Min = minBet;
}

void TDealersStake::SetFieldLimit(const std::string fieldType, const FieldLimit& limit)
{
	mLimitsTable[fieldType] = limit;
}

void TDealersStake::SetMultiple(const std::string fieldType, const uint64_t multiple)
{
	mLimitsTable[fieldType].Multiple = multiple;
}

TDealersStake::TDealersStake() : TConfiguration(), PlayboardLimitMin(0), PlayboardLimitMax(0), BetsCountMax(0), MaxTableWinLimit(0)
{
	ChipValues.fill(0);
}

void TDealersStake::OnConfigLoaded(const std::filesystem::path& filename)
{
	BetsCountMax = GetConfig("maxNumBets").get<uint32_t>();
	MaxTableWinLimit = GetConfig("tableWinLimit").get<uint64_t>();
	PlayboardLimitMin = GetConfig("minBet").get<uint64_t>();
	PlayboardLimitMax = GetConfig("maxBet").get<uint64_t>();

	const json& chipVals = GetConfig("chipValues");
	for (size_t i = 0; i < chipVals.size(); ++i) ChipValues[i + 1] = chipVals[i].get<uint64_t>();

	mBetTypes.clear();
	const json& betTypes = GetConfig("betTypes");
	for (const auto& betType : betTypes.items())
	{
		if (!betType.value().is_object())
			throw std::runtime_error("Bet type object '" + betType.key() + "' is invalid - value needs to be an object containing 'min' and 'max' members");

		mBetTypes.push_back(betType.key());

		uint64_t min = betType.value()["min"].get<uint64_t>();
		uint64_t max = betType.value()["max"].get<uint64_t>();
		uint64_t multiple = betType.value()["multiple"].get<uint64_t>();

		// Validate multiplier configuration
		ValidateMultiplierConfiguration(betType.key(), betType.value());

		// Unified multiplier processing function
		auto processUnifiedMultiplier = [&]() {
			const auto& multiplierValue = betType.value()["multiplier"];

			if (multiplierValue.is_number())
			{
				// Simple number multiplier - applies to both Asian and Western
				std::map<std::string, double> items;
				items[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = multiplierValue.get<double>();
				MultiplierOverrides[ECardRule::Asian][betType.key()] = BetMultiplier(items);
				MultiplierOverrides[ECardRule::Western][betType.key()] = BetMultiplier(items);
			}
			else if (multiplierValue.is_object())
			{
				// Check if it's the new unified format with "asian" and "western" keys
				if (multiplierValue.contains("asian") || multiplierValue.contains("western"))
				{
					// New unified format: { "asian": {...}, "western": {...} }
					if (multiplierValue.contains("asian"))
					{
						const auto& asianMultiplier = multiplierValue["asian"];
						std::map<std::string, double> asianItems;

						if (asianMultiplier.is_number())
						{
							asianItems[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = asianMultiplier.get<double>();
						}
						else if (asianMultiplier.is_object())
						{
							for (const auto& item : asianMultiplier.items())
								asianItems[item.key()] = item.value().get<double>();
						}
						MultiplierOverrides[ECardRule::Asian][betType.key()] = BetMultiplier(asianItems);
					}

					if (multiplierValue.contains("western"))
					{
						const auto& westernMultiplier = multiplierValue["western"];
						std::map<std::string, double> westernItems;

						if (westernMultiplier.is_number())
						{
							westernItems[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = westernMultiplier.get<double>();
						}
						else if (westernMultiplier.is_object())
						{
							for (const auto& item : westernMultiplier.items())
								westernItems[item.key()] = item.value().get<double>();
						}
						MultiplierOverrides[ECardRule::Western][betType.key()] = BetMultiplier(westernItems);
					}
				}
				else
				{
					// Legacy format: object with multiplier type keys (Asian-style, backward compatible)
					std::map<std::string, double> items;
					for (const auto& item : multiplierValue.items())
						items[item.key()] = item.value().get<double>();
					MultiplierOverrides[ECardRule::Asian][betType.key()] = BetMultiplier(items);
					MultiplierOverrides[ECardRule::Western][betType.key()] = BetMultiplier(items);
				}
			}
		};

		// Process unified multiplier field
		if (betType.value().contains("multiplier"))
		{
			processUnifiedMultiplier();
		}

		// Legacy support: process old "multiplier-western" field if present
		// This maintains backward compatibility for existing configurations
		if (betType.value().contains("multiplier-western"))
		{
			const auto& westernMultiplierValue = betType.value()["multiplier-western"];
			std::map<std::string, double> westernItems;

			if (westernMultiplierValue.is_number())
			{
				westernItems[EDragonTigerBetMultiplierType(EDragonTigerBetMultiplierType::Default)._to_string()] = westernMultiplierValue.get<double>();
			}
			else if (westernMultiplierValue.is_object())
			{
				for (const auto& item : westernMultiplierValue.items())
					westernItems[item.key()] = item.value().get<double>();
			}

			// Override Western multiplier if both "multiplier" and "multiplier-western" are present
			MultiplierOverrides[ECardRule::Western][betType.key()] = BetMultiplier(westernItems);
		}

		SetMinBet(betType.key(), min);
		SetMaxBet(betType.key(), max);
		SetMultiple(betType.key(), multiple);
	}
}

void TDealersStake::ValidateMultiplierConfiguration(const std::string& betTypeName, const json& betTypeConfig) const
{
	// Check for conflicting multiplier configurations
	bool hasUnifiedMultiplier = betTypeConfig.contains("multiplier");
	bool hasLegacyWesternMultiplier = betTypeConfig.contains("multiplier-western");

	if (!hasUnifiedMultiplier && !hasLegacyWesternMultiplier)
	{
		// No multiplier configuration - this is valid for simple bets
		return;
	}

	if (hasUnifiedMultiplier)
	{
		const auto& multiplierValue = betTypeConfig["multiplier"];

		// Validate unified multiplier format
		if (!multiplierValue.is_number() && !multiplierValue.is_object())
		{
			throw std::runtime_error(std::format("Invalid multiplier configuration for bet type '{}': multiplier must be a number or object", betTypeName));
		}

		if (multiplierValue.is_object())
		{
			// Check if it's the new unified format
			bool hasAsian = multiplierValue.contains("asian");
			bool hasWestern = multiplierValue.contains("western");

			if (hasAsian || hasWestern)
			{
				// New unified format validation
				if (hasAsian)
				{
					const auto& asianConfig = multiplierValue["asian"];
					if (!asianConfig.is_number() && !asianConfig.is_object())
					{
						throw std::runtime_error(std::format("Invalid Asian multiplier configuration for bet type '{}': must be a number or object", betTypeName));
					}
				}

				if (hasWestern)
				{
					const auto& westernConfig = multiplierValue["western"];
					if (!westernConfig.is_number() && !westernConfig.is_object())
					{
						throw std::runtime_error(std::format("Invalid Western multiplier configuration for bet type '{}': must be a number or object", betTypeName));
					}
				}

				// Warn if legacy multiplier-western is also present
				if (hasLegacyWesternMultiplier)
				{
					Log(Warning, "Bet type '%s' has both unified multiplier.western and legacy multiplier-western. Using unified format.", betTypeName.c_str());
				}
			}
			else
			{
				// Legacy object format - validate that all keys are valid multiplier types
				for (const auto& item : multiplierValue.items())
				{
					// This validation could be enhanced to check against known multiplier types
					if (!item.value().is_number())
					{
						throw std::runtime_error(std::format("Invalid multiplier value for bet type '{}', multiplier type '{}': must be a number", betTypeName, item.key()));
					}
				}
			}
		}
	}
}
