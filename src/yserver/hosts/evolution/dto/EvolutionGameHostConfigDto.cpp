#include "yserver/hosts/evolution/dto/EvolutionGameHostConfigDto.h"

const JsonSchema EvolutionGameHostConfigDto::EvolutionGameHostConfigSchema =
  JsonSchema({ { "troniusEvolutionServerHost", JsonSchema(json::value_t::string, "URL of the Evolution server (format: <ipAddress>[:port])") },
               { "troniusEvolutionServerSecure", JsonSchema(json::value_t::boolean, "Whether the Evolution server connection is secure (wss)") },
               { "adminKey", JsonSchema(json::value_t::string, "The admin salt key that is used in creating admin key for admin requests") } });

json EvolutionGameHostConfigDto::ToJSON() const
{
	json val;
	val["troniusEvolutionServerHost"] = TroniusEvolutionServerHost;
	val["troniusEvolutionServerSecure"] = TroniusEvolutionServerSecure;
	val["adminKey"] = AdminKey;
	return val;
}

EvolutionGameHostConfigDto EvolutionGameHostConfigDto::FromJSON(const json& val)
{
	EvolutionGameHostConfigDto dto;
	dto.TroniusEvolutionServerHost = val["troniusEvolutionServerHost"].get<std::string>();
	dto.TroniusEvolutionServerSecure = val["troniusEvolutionServerSecure"].get<bool>();
	dto.AdminKey = val["adminKey"].get<std::string>();
	return dto;
}
