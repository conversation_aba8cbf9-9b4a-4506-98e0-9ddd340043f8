#include "hosts/cards/CardGameBetStatistics.h"

#include <utility>

#include "TApplication.h"

json BetTypeHostStats::ToJSON() const
{
	return { { "totalBet", TotalBet }, { "numOfPlayers", NumOfPlayers } };
}

BetTypeHostStats BetTypeHostStats::FromJSON(const json& val)
{
	BetTypeHostStats ret;
	ret.TotalBet = val.at("totalBet").get<uint64_t>();
	ret.NumOfPlayers = val.at("numOfPlayers").get<uint64_t>();
	return ret;
}

const JsonSchema& CardGameBetsStatistic::Schema()
{
	static const JsonSchema statisticsSchema(
	  { { "interval", JsonSchema(json::value_t::number_unsigned, "Interval in milliseconds to broadcast bet statistics", 500U) },
	    { "bet-types", JsonSchema(json::value_t::array, "List of bet types to include in the statistics", json(json::value_t::array))
	                     .SetChildSchema(JsonSchema(json::value_t::string, "Bet type")) } },
	  "Schema of the bet statistics");
	__attribute__((unused)) volatile int dummy {};
	return statisticsSchema;
}

CardGameBetsStatistic::CardGameBetsStatistic(const json& config) : Lockable(), TConfiguration(Schema())
{
	LoadSubconfiguration(config);

	BroadcastTimer = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
	  [this]() {
		  PreUpdate();
		  OnUpdate(GetBetStatistics());
	  },
	  BroadcastInterval, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "Broadcast bet statistics timer");

	if (auto task = BroadcastTimer.lock())
		task->Disable();
}

void CardGameBetsStatistic::OnConfigLoaded(const std::filesystem::path& filename)
{
	BroadcastInterval = GetConfig("interval").get<uint64_t>();
	for (const auto& betType : GetConfig("bet-types")) BetTypesForBetStatistics.insert(betType.get<std::string>());
}

std::unordered_map<std::string, BetTypeHostStats> CardGameBetsStatistic::GetBetAmounts(const std::string& hostId) const
{
	SharedScopedLock lock(this);
	auto it = PerHostBetAmounts.find(hostId);
	if (it != PerHostBetAmounts.end())
		return it->second;
	return {};
}

void CardGameBetsStatistic::ClearBetAmounts(const std::string& hostId)
{
	ScopedLock lock(this);
	auto toErase = PerHostBetAmounts.find(hostId);
	if (toErase != PerHostBetAmounts.end())
	{
		for (const auto& [betType, stats] : toErase->second)
		{
			auto totalStats = TotalBetAmounts.find(betType);
			if (totalStats != TotalBetAmounts.end())
			{
				totalStats->second.TotalBet -= stats.TotalBet;
				totalStats->second.NumOfPlayers -= stats.NumOfPlayers;
			}
		}
		PerHostBetAmounts.erase(toErase);
	}
}

void CardGameBetsStatistic::SetBetAmounts(const std::string& hostId, const std::unordered_map<std::string, BetTypeHostStats>& newBets)
{
	ScopedLock lock(this);
	std::unordered_map<std::string, BetTypeHostStats>& prevHostBets = PerHostBetAmounts[hostId];
	for (const auto& [betType, stats] : prevHostBets)
	{
		auto totalStats = TotalBetAmounts.find(betType);
		if (totalStats != TotalBetAmounts.end())
		{
			totalStats->second.TotalBet -= stats.TotalBet;
			totalStats->second.NumOfPlayers -= stats.NumOfPlayers;
		}
		TotalBet -= stats.TotalBet;
	}

	for (const auto& [betType, stats] : newBets)
	{
		auto& totalStats = TotalBetAmounts[betType];

		totalStats.TotalBet += stats.TotalBet;
		totalStats.NumOfPlayers += stats.NumOfPlayers;

		TotalBet += stats.TotalBet;
	}
	prevHostBets = newBets;
}

void CardGameBetsStatistic::ClearAllBetAmounts()
{
	ScopedLock lock(this);
	PerHostBetAmounts.clear();
	TotalBetAmounts.clear();
	TotalBet = 0;
}

void CardGameBetsStatistic::StartBroadcastStatistics()
{
	if (auto task = BroadcastTimer.lock())
		task->Enable();
}
void CardGameBetsStatistic::StopBroadcastStatistics()
{
	if (auto task = BroadcastTimer.lock())
		task->Disable();
}

void CardGameBetsStatistic::SetBroadcastInterval(uint64_t interval)
{
	ScopedLock lock(this);
	BroadcastInterval = interval;
	if (auto task = BroadcastTimer.lock())
		task->SetInterval(interval);
}

dealer_assist::BetStatisticsDto CardGameBetsStatistic::GetBetStatistics()
{
	SharedScopedLock lock(this);
	dealer_assist::BetStatisticsDto ret;
	for (auto& [betType, stats] : TotalBetAmounts)
	{
		dealer_assist::BetTypeStatisticsDto val;
		val.BetType = betType;
		val.Amount = stats.TotalBet;
		val.NumOfPlayers = stats.NumOfPlayers;
		if (TotalBet > 0)
			val.Percentage = static_cast<double>(stats.TotalBet) / TotalBet * 100;
		ret.BetStatistics.push_back(std::move(val));
	}
	return ret;
}

dealer_assist::BetStatisticsDto CardGameBetsStatistic::GetBetStatistics(const std::string& hostId)
{
	SharedScopedLock lock(this);
	auto perHost = PerHostBetAmounts.find(hostId);
	if (perHost == PerHostBetAmounts.end())
		return {};

	dealer_assist::BetStatisticsDto ret;
	for (auto& [betType, stats] : perHost->second)
	{
		dealer_assist::BetTypeStatisticsDto val;
		val.BetType = betType;
		val.Amount = stats.TotalBet;
		val.NumOfPlayers = stats.NumOfPlayers;
		if (TotalBet > 0)
			val.Percentage = static_cast<double>(stats.TotalBet) / TotalBet * 100;
		ret.BetStatistics.push_back(std::move(val));
	}
	return ret;
}

void CardGameBetsStatistic::PauseUpdates()
{
	if (auto timer = BroadcastTimer.lock())
		timer->Disable();
}

void CardGameBetsStatistic::ResumeUpdates()
{
	if (auto timer = BroadcastTimer.lock())
		timer->Enable();
}

void CardGameBetsStatistic::TriggerUpdate()
{
	if (auto timer = BroadcastTimer.lock())
		timer->EnableAndExecuteImmediate();
}
