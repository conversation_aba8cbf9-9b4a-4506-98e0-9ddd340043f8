//
// Created by <PERSON><PERSON><PERSON> on 20. 8. 24.
//

#include "yserver/hosts/cards/CardGameSharedTypes.h"

using namespace yserver::gamehost;

std::optional<uint32_t> GameSide::CardAt(const uint32_t index) const
{
	if (index < mHand.size())
		return mHand[index];

	return std::nullopt;
}

uint32_t GameSide::NumberOfCards() const
{
	return mHand.size();
}

void GameSide::AddCard(const uint32_t card, const uint32_t position)
{
	if (position <= mHand.size())
		mHand.insert(mHand.begin() + position, card);
	else
		mHand.push_back(card);
}

void GameSide::AddOrReplaceCard(const uint32_t card, const uint32_t position)
{
	if (position < mHand.size())
		mHand[position] = card;
	else
		mHand.push_back(card);
}

bool GameSide::HasFaceDownCard() const
{
	if (std::ranges::find(mHand, 0) != mHand.end())
	{
		return true;
	}

	return false;
}

void GameSide::ClearCards()
{
	mHand.clear();
}
