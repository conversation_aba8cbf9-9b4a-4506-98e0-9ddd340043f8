#include "hosts/TRouletteHost.h"

#include <oneapi/tbb/concurrent_unordered_map.h>

#include <execution>

#include "TLangMessages.h"
#include "YServer.h"
#include "api/YPlayerAPI.h"
#include "common/FileUtils.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "jackpot/client/JackpotClient.h"
#include "rmclient_drv.h"

using namespace yserver;
using namespace yserver::gamehost;
using namespace dealer_assist;

DEFINE_LOG_CATEGORY(LogRoulette, "roulette")

using namespace yserver::gamehost::roulette;

const JsonSchema& GetStakeConfigSchema()
{
	const static JsonSchema StakeSchema = JsonSchema(
	  { { "default-stake", JsonSchema(json::value_t::number_unsigned, "The stake to select by default (index)", 0) },
	    { "stake-mode", JsonSchema(json::value_t::string, "Which stakes should be accessible to which providers (All, OptIn, OptOut)",
	                               EHostAccessMode(EHostAccessMode::All)._to_string())
	                      .SetToEnumType<EHostAccessMode>() },
	    { "stakes", JsonSchema(json::value_t::array, "Array of indexes to use for the stake-mode rule", json(json::value_t::array))
	                  .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "Stake index to enable/disable")) },
	    { "multiplier",
	      JsonSchema(json::value_t::number_unsigned, "value to multiply all stake values by", 1).AddConstraint(limits::IValueLimit::MakeRange(1U, {}, true, false)) } },
	  "Stake filtering rules", false);
	__attribute__((unused)) volatile int dummy {};
	return StakeSchema;
}

const JsonSchema RouletteHostSchema = JsonSchema(
  { { "address", JsonSchema(json::value_t::string, "The address of RouletteMaster, Lady or Abbiati wheel (only hostname, no URL schema (http://))")
                   .Flag(CriticalSettingFlag)
                   .Flag(PresetLockedSettingFlag)
                   .Flag(ModuleRestartRequiredSettingFlag) },
    { "secure", JsonSchema(json::value_t::boolean, "If true, will use TLS for communication with RNG (wss/https)", true)
                  .Flag(CriticalSettingFlag)
                  .Flag(PresetLockedSettingFlag)
                  .Flag(ModuleRestartRequiredSettingFlag) },
    { "delay", JsonSchema(json::value_t::number_unsigned, "Delays all RPC messages by the given number of milliseconds (BE VERY CAREFUL WITH THIS!)", 0)
                 .Flag(CriticalSettingFlag)
                 .Flag(PresetLockedSettingFlag)
                 .Flag(ModuleRestartRequiredSettingFlag) },
    { "double-zero", JsonSchema(json::value_t::boolean, "Weather this host should be double zero or not", false)
                       .Flag(CriticalSettingFlag)
                       .Flag(PresetLockedSettingFlag)
                       .Flag(ExportedSettingFlag)
                       .Flag(ModuleRestartRequiredSettingFlag) },
    { "stakes",
      JsonSchema(
        { { "default-provider", GetStakeConfigSchema() },
          { "other-providers", JsonSchema(json::value_t::object, "List of specific provider stake rules", json()).SetChildSchema(GetStakeConfigSchema()) },
          { "additional-stakes",
            JsonSchema(json::value_t::array, "List of additional hardcoded stakes on this roulette host (on top of ones retrieved from lady/master)", json::array({}))
              .SetChildSchema(::roulette::TRouletteStake::StakeSchema()) } },
        "Stake configuration", false) },
    { "allow-start-vote", JsonSchema(json::value_t::boolean, "Allow speeding up the game flow by voting for the game to start immediately", false)
                            .Flag(ExportedSettingFlag)
                            .Flag(UserSettingFlag) },
    { "allow-no-checksum",
      JsonSchema(
        json::value_t::boolean,
        "If true, cases when a checksum cannot be retrieved from the RNG are considered a Warning and not an Error. This means the host will be allowed to start.", false)
        .Flag(ModuleRestartRequiredSettingFlag) },
    { "lucky-jackpot", JsonSchema(RouletteJackpotConfig::Schema(false), false)
                         .SetDescription("Configuration relating to lucky number jackpot")
                         .Flag(ModuleRestartRequiredSettingFlag)
                         .Flag(CriticalSettingFlag)
                         .Flag(PresetLockedSettingFlag)
                         .AddEditCondition("rng-type", limits::ValueLimit(limits::ELimitType::Equal, ERNGType(ERNGType::imaxaGaming)._to_string())) },
    { "stream",
      JsonSchema({ { "type", JsonSchema(json::value_t::string, "The type of stream", EStreamType(EStreamType::imaxaPlayer)._to_string()).SetToEnumType<EStreamType>() },
                   { "source", JsonSchema({ { "url", JsonSchema(json::value_t::string, "The address of the stream") },
                                            { "localUrl", JsonSchema(json::value_t::string, "The address of the stream (if running on the same machine)", json()) },
                                            { "id", JsonSchema(json::value_t::string, "The ID of the stream (leave blank if not needed)") },
                                            { "token", JsonSchema(json::value_t::string, "A security token to access the stream", json()) },
                                            { "isCylinder", JsonSchema(json::value_t::boolean, "If true, clients will not play stream audio if platform=slot", false) } },
                                          "Information about a stream", false)
                                 .SetDefault(json()) } })
        .Flag(PresetLockedSettingFlag) },
    { "rng-type", JsonSchema(json::value_t::string, "The type of RNG this host is connecting to", ERNGType(ERNGType::imaxaGaming)._to_string())
                    .SetToEnumType<ERNGType>()
                    .Flag(CriticalSettingFlag)
                    .Flag(PresetLockedSettingFlag)
                    .Flag(ModuleRestartRequiredSettingFlag) },
    { "thunder",
      JsonSchema(
        { { "mode", JsonSchema(json::value_t::string, "Does this roulette host have thunder multipliers enabled? If so, how are they determined?",
                               EThunderMode(EThunderMode::Disabled)._to_string())
                      .SetToEnumType<EThunderMode>()
                      .Flag(CriticalSettingFlag) },
          { "local-configuration-type",
            JsonSchema(json::value_t::string, "Which variant of the thunder setup to use?", EThunderVariant(EThunderVariant::Thunder1)._to_string())
              .SetToEnumType<EThunderVariant>()
              .Flag(CriticalSettingFlag) },
          { "local-configuration",
            JsonSchema(ThunderRNGInstance1::Schema(), false)
              .SetDescription(
                "If set and the thunder mode is either LocalOnly or LiveWithLocalFallback, this is the thunder configuration used when no live input is available. (thunder1)")
              .Flag(CriticalSettingFlag)
              .SetDefault({})
              .AddEditCondition("mode", limits::ValueLimitList(
                                          limits::ELimitType::List_MatchOne,
                                          { limits::ValueLimit(limits::ELimitType::Equal, std::string(EThunderMode(EThunderMode::LocalOnly)._to_string())),
                                            limits::ValueLimit(limits::ELimitType::Equal, std::string(EThunderMode(EThunderMode::LiveWithLocalFallback)._to_string())) }))
              .AddEditCondition("local-configuration-type", limits::ValueLimit(limits::ELimitType::Equal, EThunderVariant(EThunderVariant::Thunder1)._to_string())) },
          { "local-configuration-2",
            JsonSchema(ThunderRNGInstance2::Schema(), false)
              .SetDescription(
                "If set and the thunder mode is either LocalOnly or LiveWithLocalFallback, this is the thunder configuration used when no live input is available. (thunder2)")
              .Flag(CriticalSettingFlag)
              .SetDefault({})
              .AddEditCondition("mode", limits::ValueLimitList(
                                          limits::ELimitType::List_MatchOne,
                                          { limits::ValueLimit(limits::ELimitType::Equal, std::string(EThunderMode(EThunderMode::LocalOnly)._to_string())),
                                            limits::ValueLimit(limits::ELimitType::Equal, std::string(EThunderMode(EThunderMode::LiveWithLocalFallback)._to_string())) }))
              .AddEditCondition("local-configuration-type", limits::ValueLimit(limits::ELimitType::Equal, EThunderVariant(EThunderVariant::Thunder2)._to_string())) },
          { "allow-per-game-rules",
            JsonSchema(json::value_t::boolean,
                       "True/false to allow specific game configurations on this host to define their own thunder rules (and thus have their own thunder RNG)", true) },

          { "jackpot", JsonSchema(RouletteJackpotConfig::Schema(true), false)
                         .SetDescription("Configuration relating to fireball deluxe jackpot")
                         .Flag(ModuleRestartRequiredSettingFlag)
                         .Flag(CriticalSettingFlag)
                         .Flag(PresetLockedSettingFlag)
                         .AddEditCondition("rng-type", limits::ValueLimit(limits::ELimitType::Equal, ERNGType(ERNGType::imaxaGaming)._to_string())) } },
        "Thunder roulette configuration")
        .Flag(PresetLockedSettingFlag)
        .Flag(CriticalSettingFlag)
        .Flag(ModuleRestartRequiredSettingFlag) },
    { "bet-timeout", JsonSchema(json::value_t::number_unsigned, "If > 0, bets older than this amount of seconds are cleared if the player is offline", 60U) },
    { "late-bet-threshold", JsonSchema(json::value_t::number_unsigned, "How long before Bets Closed to close bets for the players (milliseconds)", 500U)
                              .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 1000U))
                              .SetInterval(50) },
    { "abbiati",
      JsonSchema(
        { { "round-time", JsonSchema(json::value_t::number_unsigned, "The target time (in seconds) of a single game round's bets open phase", 20U)
                            .AddConstraint(limits::IValueLimit::MakeRange(10U, 100U, true, true)) },
          { "bets-open-duration-after-ball-throw",
            JsonSchema(json::value_t::number_unsigned,
                       "How long after throwing the ball should bets remain open? (milliseconds) If the roulette wheel closes bets earlier, this setting has no effect.",
                       7000U)
              .AddConstraint(limits::ValueLimit(limits::ELimitType::LessThan, 10000U))
              .Flag(CriticalSettingFlag) },
          { "wheel-properties",
            JsonSchema({ { "ball-speed-on-bets-closed", JsonSchema(json::value_t::number_unsigned, "The ball speed at which the wheel closes bets", 34) },
                         { "wheel-speed", JsonSchema(json::value_t::number_unsigned, "The speed of the cylinder when the ball is out", 12) },
                         { "lin-drag-coeff", JsonSchema(json::value_t::number_float, "The linear drag coefficient", 9.5) } },
                       "Physical properties of the wheel (to estimate bets close time)", false)
              .Flag(CriticalSettingFlag) } },
        "Abbiati configuration", false)
        .SetDefault({})
        .Flag(PresetLockedSettingFlag)
        .Flag(ModuleRestartRequiredSettingFlag)
        .AddEditCondition("rng-type", limits::ValueLimit(limits::ELimitType::Equal, ERNGType(ERNGType::Abbiati)._to_string())) },
    { "imaxa",
      JsonSchema(
        { { "enable-ws", JsonSchema(json::value_t::boolean, "If true, will use a websocket stream to receive game status updates instead of polling", true)
                           .Flag(ModuleRestartRequiredSettingFlag) },
          { "enable-rtp-control", JsonSchema(json::value_t::boolean, "If true, RTP control of the lady RNG is enabled. If false, RTP will never be controlled.", false)
                                    .Flag(PresetLockedSettingFlag)
                                    .Flag(CriticalSettingFlag) } },
        "Imaxa configuration", false)
        .Flag(PresetLockedSettingFlag)
        .AddEditCondition("rng-type", limits::ValueLimit(limits::ELimitType::Equal, ERNGType(ERNGType::imaxaGaming)._to_string())) } });



void callback_handler(std::promise<JackpotClientResult> promise, const JackpotClientResult& response)
{
	promise.set_value(response);
}

const JsonSchema RouletteGameSchema = TGameHost::BaseGameSchema() + TBaseRouletteHost::BaseRouletteGameSchema() +
                                      JsonSchema({ { "stake", JsonSchema(json::value_t::number_unsigned, "The index of the stake to launch this game with", 0U) },
                                                   { "autorepeat", JsonSchema(json::value_t::boolean, "Enable autorepeat button", false) },
                                                   { "clear-all", JsonSchema(json::value_t::boolean, "Enable Clear All button", false) },
                                                   { "autoplay-video", JsonSchema(json::value_t::boolean, "Should play video automatically on game start", true) } });


RouletteGame::RouletteGame(const std::string& host_uid, const GameInformation& info) :
    BaseRouletteGame(host_uid, info), bLuckyNumberJackpot(info.GetConfig("allow-lucky-number-jp").get<bool>()),
    bFireballDeluxeJackpot(info.GetConfig("allow-fireball-jp").get<bool>()), mFireballFee(info.GetConfig("fireball-fee").get<double>())
{
	SavedChipValues.fill(0);
}

void RouletteGame::Unregister(bool bClearWins)
{
	LastVerifiedBets.Clear();
	ConfirmedBets.Clear();
	ClearUncommittedBets();
	WinNumber.reset();
	if (bClearWins)
		WonResult = {};
}

void RouletteGame::SetLock(Player& player, ERouletteLockReasons lock, bool bActive)
{
	auto preLockState = LockState;
	LockState.SetFlag(lock, bActive);

	// lock state changed
	if (LockState != preLockState)
	{
		if (!LockState)
			player.UnlockPlay(EPlayLockLevels::LockHost);
		else
			player.LockPlay(EPlayLockLevels::LockHost, getLockReason(LockState.HighestBit()));
	}
}

::roulette::CalculateWinsResult RouletteGame::CalculateWins(const ::roulette::TRouletteBets& bets, const int winNumber, const ThunderRNGInstance* ThunderRNG,
                                                            const std::optional<::roulette::FThunderRoundHistory>& fromHistory) const
{
	// regular and fireball numbers win
	auto wonResult = bets.calculateWins(ConfirmedBets, StakeInfo, winNumber, ThunderRNG ? ThunderRNG->RNG.get() : NULL, fromHistory, GetThunderPaytableType());

	// fireball bonus multiplier win (bonus win will not be calculated in game resume in case game crashed before closed bets)
	if (ThunderRNG && FireballBonusMultiplier && FireballBonusMultiplier->Number == winNumber)
	{
		const uint64_t straightAndSplitBetAmounts = bets.amountOnBetStyles(ConfirmedBets, { ::roulette::EBetStyle::Plain, ::roulette::EBetStyle::Caval });
		if (uint64_t bonusPrizeWin = FireballBonusMultiplier->Multiplier * straightAndSplitBetAmounts)
		{
			TLOG(LogRoulette, EVerbosity::Info,
			     std::format("Won Fireball Bonus Prize ({}x {} = {})", FireballBonusMultiplier->Multiplier, straightAndSplitBetAmounts, bonusPrizeWin).c_str())

			wonResult.TotalWon += bonusPrizeWin;
		}
	}

	return wonResult;
}

bool RouletteGame::IsLuckyNumberJackpotEnabled() const
{
	return bLuckyNumberJackpot;
}

bool RouletteGame::IsFireballDeluxeJackpotEnabled() const
{
	return bFireballDeluxeJackpot;
}

bool RouletteGame::IsEnabledFireballFee() const
{
	return mFireballFee > 0.0;
}

::roulette::EThunderPaytableType RouletteGame::GetThunderPaytableType() const
{
	if (Thunder == EThunderVariant::None)
		return ::roulette::EThunderPaytableType::Standard;

	if (IsEnabledFireballFee())
		return ConfirmedBets.HasFireballFee() ? ::roulette::EThunderPaytableType::FireballPlus : ::roulette::EThunderPaytableType::Standard;

	return ::roulette::EThunderPaytableType::Fireball;
}

double RouletteGame::GetFireballFee() const
{
	return mFireballFee;
}

void RouletteGame::OnSetStake() const
{
	BaseRouletteGame::OnSetStake();
}

bool RouletteGame::CanCloseAccounting() const
{
	return GameInstance::CanCloseAccounting() && BetStatus() == EGameBetState::Idle && (Snapshot().VoidReason.has_value() || !WonResult.HasAnyWinsAffectingNextRound());
}

json RouletteGame::GetGameRoundData() const
{
	if (BetStatus() == EGameBetState::Idle)
		return {};

	::roulette::TRouletteGameExtraDataDto rouletteExtraData;

	rouletteExtraData.ConfirmedBets = ConfirmedBets.BetsAsJSON(CreditMultiplier);
	rouletteExtraData.ConfirmedBetPresets = ConfirmedBets.BetPresetsAsJSON(CreditMultiplier);
	rouletteExtraData.BetPresetFields = to_json(ConfirmedBets.presetDefinitions);

	rouletteExtraData.IsDoubleZero = bIsDoubleZero;
	rouletteExtraData.CreditMultiplier = CreditMultiplier;
	if (Thunder != EThunderVariant::None)
	{
		rouletteExtraData.BoardMultiplier = BoardMultiplier;
		rouletteExtraData.ThunderNumbers = ThunderNumbers;
		rouletteExtraData.FireballFee = mFireballFee;
		if (FireballBonusMultiplier)
			rouletteExtraData.FireballBonus = FireballBonusMultiplier->ToJSON();
	}

	// Always save jackpot data to game round data, even if it wasn't activated.
	if (const auto& jpState = JackpotState(false))
	{
		if (const auto jpModule = std::dynamic_pointer_cast<RouletteJackpot>(jpState->JackpotModule); jpModule && !jpModule->Currency().empty())
		{
			::roulette::TRouletteJackpotExtraDataDto jpData;
			jpData.Currency = jpModule->Currency();
			jpData.Levels = TGameHost::GetJackpotLevelsDataJson(jpModule);
			if (const auto fireballJpModule = std::dynamic_pointer_cast<FireballDeluxeJackpot>(jpModule))
				jpData.fireballJpCombinetMultipliers = fireballJpModule->GetJackpotCombinedMultipliers();
			if (const auto luckyJpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(jpModule))
				jpData.luckyJpLuckyLevels = luckyJpModule->GetLuckyNumbers();

			rouletteExtraData.Jackpot = jpData;
		}
	}

	rouletteExtraData.StakeInfoID = StakeInfo.ID;
	if (StakeInfo.Stake)
	{
		rouletteExtraData.chipValues = StakeInfo.Stake->ChipValues;
		rouletteExtraData.MultiplierOverrides = StakeInfo.Stake->MultiplierOverrides;
	}

	if (WinNumber.has_value())
		rouletteExtraData.WinNumber = WinNumber;

	rouletteExtraData.CalculatedWins = WonResult.ToJSON();

	return rouletteExtraData.AsJSON();
}

void RouletteGame::Restore(const GameRoundSnapshot& snap)
{
	Unregister(true);

	GameInstance::Restore(snap);

	if (snap.ExtraData.is_null())
		return;

	bIsDoubleZero = snap.ExtraData["doubleZero"].get<bool>();
	if (snap.ExtraData.contains("multiplier"))
		CreditMultiplier = snap.ExtraData["multiplier"].get<uint32_t>();
	else
		CreditMultiplier = 1;

	if (snap.ExtraData.contains("stake"))
		StakeInfo.ID = snap.ExtraData["stake"].get<int>();

	WinNumber.reset();
	if (snap.ExtraData.contains("winNum") && snap.ExtraData["winNum"].is_number_integer())
		WinNumber = snap.ExtraData["winNum"].get<int>();

	const json& chipValues = snap.ExtraData["chipValues"];
	if (chipValues.is_array())
	{
		for (size_t idx = 0; idx < std::min<size_t>(::roulette::MAX_BETTING_CHIPS_COUNT, chipValues.size()); idx++)
			SavedChipValues[idx] = chipValues[idx].get<uint64_t>();
	}
}

// err 1: cylinder too slow
// err 2: rotation of ball and cylinder is the same
// err 4: insufficient revs (ball too slow)
// err 8: ball out of cylinder
// err 10: cylinder too fast
// err 20: hardware problem
// err 40: sensor broken
const std::array<int, 6> MapFromAbbiatiToImaxa = { GAME_INIT_STATE,          PREPARING_BALL_STATE,     BALL_OUT_STATE,
	                                               WAITING_WIN_NUMBER_STATE, READING_WIN_NUMBER_STATE, GAME_PAUSED_STATE };
const JsonSchema AbbiatiGameStatusSchema = JsonSchema({ { "g", JsonSchema(json::value_t::number_unsigned, "The game ID") },
                                                        { "p", JsonSchema(json::value_t::number_integer, "game phase") },
                                                        { "w", JsonSchema(json::value_t::number_integer, "win number") },
                                                        { "v_b", JsonSchema(json::value_t::number_integer, "ball speed") },
                                                        { "v_w", JsonSchema(json::value_t::number_integer, "wheel RPM") },
                                                        { "d_b", JsonSchema(json::value_t::number_unsigned, "ball direction (0 or 1)") },
                                                        { "d_w", JsonSchema(json::value_t::number_unsigned, "wheel direction (0 or 1)") },
                                                        { "l", JsonSchema(json::value_t::number_integer, "number of laps done by the ball") },
                                                        { "e", JsonSchema(json::value_t::number_integer, "error descriptor, zero if all ok") } });

void TRouletteHost::on_abbiati_msg(const std::string& msg)
{
	json msgJson;
	try
	{
		msgJson = json::parse(msg);
	}
	catch (const std::exception& e)
	{
		Log(Error, "Could not parse abbiati wheel server message to JSON: %s", e.what());
		return;
	}

	try
	{
		if (msgJson.is_object())
			AbbiatiGameStatusSchema.TestThrow(msgJson);

		{
			ScopedLock lock(AbbiatiWheelSerialNumber);
			if (msgJson.is_array())    // init packet
			{
				mAbbiatiChecksum.first = msgJson[0].get<std::string>();
				mAbbiatiChecksum.second = msgJson[1].get<std::string>();
				return;
			}

			const uint32_t abbiatiSerial = msgJson["i"].get<int>();
			if (abbiatiSerial != &AbbiatiWheelSerialNumber)
			{
				if (&AbbiatiWheelSerialNumber)
					Log(Error, "Abbiati wheel serial number changed from %u to %d!", &AbbiatiWheelSerialNumber, abbiatiSerial);
				&AbbiatiWheelSerialNumber = abbiatiSerial;
				lock.unlock();
				AbbiatiWheelSerialCV.notify_all();
			}
		}

		int abbiatiPhase = msgJson["p"].get<int>();
		int abbiatiError = msgJson["e"].get<int>();
		if (abbiatiPhase < 0 || abbiatiPhase > (int)MapFromAbbiatiToImaxa.size())
		{
			Log(Error, "Got weird phase %d from abbiati wheel server!", abbiatiPhase);
			abbiatiPhase = 6;    // on error go to paused
			abbiatiError |= 0x20;
		}
		abbiatiPhase = std::clamp(abbiatiPhase, 1, 6);

		RNGState receivedState;
		receivedState.BallSpeed = msgJson["v_b"].get<int>();
		receivedState.RouletteGameID = msgJson["g"].get<uint64_t>();

		receivedState.Phase = MapFromAbbiatiToImaxa[abbiatiPhase - 1];

		// if ball direction & wheel direction are the same, go to error
		if (abbiatiPhase == BALL_OUT_STATE && receivedState.BallSpeed > 25 && mBetsClosedTime && yprotocol::IsTimedOut(mBetsClosedTime, 2000) &&
		    msgJson.contains("d_b") && msgJson.contains("d_w") && msgJson["d_b"].get<int>() == msgJson["d_w"].get<int>())
			abbiatiError |= 2;

		if (receivedState.Phase != GAME_PAUSED_STATE && abbiatiError)
		{
			receivedState.Phase = WAITING_FOR_FIRST_STATUS;    // fallback error

			if (abbiatiError & 1)
				receivedState.Phase = ERROR_WHEEL_SPEED;    // Log(Info, "Cylinder too slow (< WHEEL MINIMUM SPEED)");

			if (abbiatiError & 2)
				receivedState.Phase = ERROR_BALL_AND_WHEEL_SAME_DIRECTION;

			if (abbiatiError & 4)
				receivedState.Phase = ERROR_BALL_LAPS_COUNT;    // Log(Info, "Insufficient revolutions of the Ball (< MINIMUM TURNS VALID GAME)");

			if (abbiatiError & 8)
				receivedState.Phase = ERROR_BALL_LOST_ERROR;    // Log(Info, "Ball out of cylinder");

			if (abbiatiError & 0x10)
				receivedState.Phase = ERROR_WHEEL_SPEED;    // Log(Info, "Cylinder speed faster than 30 RPM");

			abbiatiError -= abbiatiError & 0x1F;
			if (abbiatiError)
			{
				receivedState.Phase = CB_COMMUNICATION_ERROR_STATE;
				Log(Info, "Unexpected abbiati error %d", std::format("{:b}", abbiatiError).c_str());
			}
		}
		receivedState.WinningNumber = msgJson["w"].get<int>();
		receivedState.Timestamp = ytime::GetTimeMsec();

		NewAbbiatiState = receivedState;

		json wheelInfo(json::value_t::object);
		wheelInfo["wheelSpeed"] = msgJson["v_w"];
		wheelInfo["ballSpeed"] = msgJson["v_b"];

		BroadcastEventToViewers(yprotocol::Event(*WheelStatusViewerEvent, wheelInfo));
	}
	catch (const SchemaError& er)
	{
		Log(Error, "Abbiati message is invalid: %s", er.what());
	}
}

int TRouletteHost::RNGState::GetPhase(const BetsCloseBehavior& behavior) const
{
	if (AreBetsOpen({}) && behavior.ReduceBetTimeByMs > TimeLeftMs)
		return WAITING_WIN_NUMBER_STATE;

	return Phase;
}

int TRouletteHost::RNGState::GetSubPhase(const BetsCloseBehavior& behavior) const
{
	if (AreBetsOpen({}) && behavior.ReduceBetTimeByMs > TimeLeftMs)
		return 0;

	return SubPhase;
}

int TRouletteHost::RNGState::GetTimeLeftMs(const BetsCloseBehavior& behavior) const
{
	if (TimeLeftMs <= 0)
		return TimeLeftMs;

	if (behavior.ReduceBetTimeByMs > TimeLeftMs)
		return 0;

	return TimeLeftMs - behavior.ReduceBetTimeByMs;
}

int TRouletteHost::RNGState::GetTotalTimeMs(const BetsCloseBehavior& behavior) const
{
	if (TotalTimeMs <= 0)
		return TotalTimeMs;

	if (behavior.ReduceBetTimeByMs > TotalTimeMs)
		return 0;

	return TotalTimeMs - behavior.ReduceBetTimeByMs;
}


bool TRouletteHost::RNGState::AreBetsOpen(const BetsCloseBehavior& behavior) const
{
	if (Phase < PREPARING_BALL_STATE)
		return false;

	if (Phase >= WAITING_WIN_NUMBER_STATE)
		return false;

	if (TimeLeftMs < behavior.ReduceBetTimeByMs)
		return false;

	return true;
}

bool TRouletteHost::RNGState::AreBetsClosedAndNotError(const BetsCloseBehavior& behavior) const
{
	// if in error, return false
	if (Phase > READING_WIN_NUMBER_STATE)
		return false;

	// if bets are open, then they can't also be closed
	if (AreBetsOpen(behavior))
		return false;

	// if the phase hos not reached ball out, bets cannot be closed
	if (Phase < BALL_OUT_STATE)
		return false;

	return true;
}

FStakeRules FStakeRules::FromJSON(const json& val)
{
	json stakeConf = GetStakeConfigSchema().GenerateConfig(val, { .bUseComments = false, .bFillEmptyOptionalsWithDefaults = true });
	FStakeRules ret;
	ret.Default = stakeConf["default-stake"].get<uint32_t>();
	ret.AccessMode = EHostAccessMode::_from_string(stakeConf["stake-mode"].get<std::string>().c_str());
	ret.Multiplier = stakeConf["multiplier"].get<uint32_t>();

	const json& list = stakeConf["stakes"];
	if (list.is_array())
	{
		for (const json& stake : list) ret.AccessList.insert(stake.get<uint32_t>());
	}
	return ret;
}

const FStakeRules& TRouletteHost::GetRulesFor(const std::string& provider) const
{
	auto find = mProviderStakeRules.find(provider);
	if (find == mProviderStakeRules.end())
		return mDefaultStakeRules;
	else
		return find->second;
}

const JsonSchema SelectStakeSchema = JsonSchema(json::value_t::number_unsigned, "The index of the stake to select");

TRouletteHost::TRouletteHost(YModuleContainerBase* container, const std::string& name) : TGameHost(container, name, HostType::Roulette)
{
	SetLogCategory(LogRoulette);
	bAllowStakeChange = false;
	bForceStakeSelect = false;
	bAllowMultiSession = false;
	bStartButtonEnabled = false;
	mDelayMs = 0;
	mHistoryJSON = json(json::value_t::array);
	bLiveGame = true;

	OnStatusChanged += [this](const FModuleStatusInfo& oldStatus, const FModuleStatusInfo& newStatus) {
		if (oldStatus.Status == EModuleStatus::Ready && newStatus.Status != EModuleStatus::Ready)
		{
			ForEachPlayer([&](Player& player) {
				ScopedLock lock(player);
				if (player.Game()->BetStatus() == EGameBetState::BetPlaced)
					player.VoidGame(FGameroundError({ EVoidReason(EVoidReason::ClientLocked)._to_string(), "Croupier left" }), false);
			});
		}
	};

	Schema() += RouletteHostSchema;

	RegisterAdminAction("Croupier Log", EModuleAdminActionTarget::Module,
	                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    const std::filesystem::path logFileLocation(StorageDir() / "croupier.log");
		                    if (!std::filesystem::exists(logFileLocation))
			                    return std::string();

		                    std::ifstream croupierLogFile(logFileLocation);
		                    if (!croupierLogFile.is_open())
			                    return std::string();

		                    std::stringstream buffer;
		                    buffer << croupierLogFile.rdbuf();

		                    return buffer.str();
	                    });

	RegisterAdminAction(
	  "Log In Croupier", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		  SetLoggedInCroupier(params.get<std::string>());
		  return {};
	  },
	  JsonSchema(json::value_t::string, "Croupier badge ID", std::string()));

	RegisterAdminAction("Log Out Croupier", EModuleAdminActionTarget::Module,
	                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
		                    if (!LogoutCroupier("Logged out by Commander - " + instigator.Public + " (" + instigator.Tag + ")", false))
			                    throw yprotocol::RequestError(request, "No croupier is logged in");

		                    ReadyStateChanged();
		                    return "Successfully logged out croupier!";
	                    });

	RegisterAdminAction(
	  "Void Game", EModuleAdminActionTarget::Player,
	  [this](const yprotocol::Request& request, const PlayerPtr& player, const json& val, const YAuthKey& instigator) -> json {
		  if (player->Game()->BetStatus() != EGameBetState::Idle)
			  VoidGame(*player, EVoidReason::NoGameResult, "Admin void: " + val.get<std::string>());

		  return {};
	  },
	  JsonSchema(json::value_t::string, "Reason", std::string()));

	RegisterAdminAction(
	  "Void Game For All Players", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, const PlayerPtr& player, const json& val, const YAuthKey& instigator) -> json {
		  VoidLiveGame("Admin void: " + val.get<std::string>());
		  return {};
	  },
	  JsonSchema(json::value_t::string, "Reason", std::string()));

	RegisterAdminAction(
	  "Set Cylinder Error", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, const PlayerPtr& player, const json& val, const YAuthKey& instigator) -> json {
		  ForcedErrorState = val.get<int>();
		  if (ForcedErrorState < LAST_REGULAR_GAME_STATE_ID || ForcedErrorState >= POWER_RESTART)
			  ForcedErrorState = 0;
		  return {};
	  },
	  JsonSchema(json::value_t::number_unsigned, "Error State ID", 0U));

#ifdef YSERVER_ENABLE_RESULT_ADJUSTMENT
	RegisterAdminAction(
	  "Set Win Number", EModuleAdminActionTarget::Module,
	  [this](const yprotocol::Request& request, const PlayerPtr& player, const json& val, const YAuthKey& instigator) -> json {
		  ForcedWinNumber = val.get<int>();
		  if (ForcedWinNumber > (bIsDoubleZero ? 37 : 36))
			  ForcedWinNumber.reset();
		  return {};
	  },
	  JsonSchema(json::value_t::number_unsigned, "Win number", 0U));
#endif

	AddPollThread("poll", 10, [this](bool bShuttingDown) { Poll(bShuttingDown); });

	// Don't show roulette master log, it's annoying
	SET_LOG_OUTPUT(LogRouletteMaster, [](const LogEntry&) {})

	GameStateChangedEvent = RegisterEvent("game-state-changed", "Sent when a variable describing the state of the game changes");
	JackpotChangedEvent = RegisterEvent("jackpot-update", "Sent when the pot value of the lucky number jackpot changes");
	ConfigChangedEvent = RegisterEvent("config-changed", "If the configuration of the host changes, this is fired");
	HistoryChangedEvent = RegisterEvent("history", "Triggered when the list of history numbers changes");
	StakeChangedEvent = RegisterEvent("stakeChanged", "Triggered when the selected stake changes");
	WinReportEvent = RegisterEvent("win-report", "Triggered when a game ends to report wins that have occurred");
	JackpotWasWonEvent = RegisterEvent("jackpot-was-won", "Triggered when some other player (not you) won the jackpot");

	WheelStatusViewerEvent = RegisterEvent("wheel-status", "Triggered when information about the wheel is updated");

	RegisterGameMethod<RouletteGame>("history", "Return the history of winning numbers",
	                                 [this](RouletteGame& game, const yprotocol::Request& req) -> json {
		                                 json ret(json::value_t::object);
		                                 {
			                                 SharedScopedLock lock(mHistory);
			                                 ret["history"] = mHistoryJSON;
		                                 }
		                                 if (auto thunderRng = GetThunderRNG(game.Info(), game.Thunder))
		                                 {
			                                 ret["thunderHistory"] = thunderRng->GetHistoryJSON();
		                                 }
		                                 return ret;
	                                 },
	                                 {},
	                                 { { "If successful", JsonSchema(json::value_t::array, "List of games played on this host")
	                                                        .SetChildSchema(JsonSchema(json::value_t::array, "Information of the played game")
	                                                                          .AppendMember(JsonSchema(json::value_t::number_unsigned, "The game round ID"))
	                                                                          .AppendMember(JsonSchema(json::value_t::number_integer, "Winning number"))) } });

	RegisterPlayerOnlyMethod("vote-start", "Vote for the early start of a game", [this](Player& player, const yprotocol::Request& req) -> json {
		if (!bStartButtonEnabled)
			throw yprotocol::RequestError(req, "Game start votes are not enabled!");

		if (RNGType != roulette::ERNGType::imaxaGaming)
			throw yprotocol::RequestError(req, "Voting for start is not supported in this configuration!");


		uint64_t gid = 0;
		{
			SharedScopedLock lock(CurrentState);
			if (!CurrentState->AreBetsOpen(mBetsClosedBehavior))
				throw yprotocol::RequestError(req, "Bets are not open - cannot vote for game start!");
			gid = CurrentState->RouletteGameID;
		}

		if (player.Game()->GameRound() != std::to_string(gid))
			throw yprotocol::RequestError(req, "Not registered in an active game!");

		int numPlayers = 0;
		int numPlayersPressedStart = 0;
		{
			SharedScopedLock lock(mPlayers);
			if (!PlayersThatVotedStart.insert(player.UniqueIdentifier()).second)
				return "Already voted to start this game!";    // Normal response (string)
			numPlayers = GetNumPlayersWithMoney();
			numPlayersPressedStart = PlayersThatVotedStart.size();
		}

		if (rmclientdrv::GameStart(Roulette, numPlayersPressedStart, numPlayers))
			throw yprotocol::RequestError(req, "Something went wrong while trying to vote for start!");

		return "Successfully voted to start the game.";    // Normal response (string)
	});

	RegisterGameMethod<RouletteGame>("config", "Return the game configuration for this host",
	                                 [this](RouletteGame& game, const yprotocol::Request& req) -> json {
		                                 try
		                                 {
			                                 return GetGameConfig(game);
		                                 }
		                                 catch (const json::exception& err)
		                                 {
			                                 throw yprotocol::RequestError(req, "Problem executing config: " + std::string(err.what()) + "!");
		                                 }
	                                 },
	                                 {}, { { "If successful", JsonSchema(json::value_t::object, "The config of this roulette game") } });

	RegisterPlayerOnlyMethod(
	  "select-stake", "Select a stake",
	  [this](Player& player, const yprotocol::Request& req) -> json {
		  if (!bAllowStakeChange)
			  throw yprotocol::RequestError(req, "Stake change during gameplay is not enabled!");

		  RouletteGame& game = dynamic_cast<RouletteGame&>(*player.Game().get());

		  if (game.BetStatus() != EGameBetState::Idle)
			  throw yprotocol::RequestError(req, "Cannot switch stake with bets on the table!");

		  const uint32_t stakeID = req.GetParam().get<uint32_t>();
		  auto allStakes = GetStakesForGame(game.Info());
		  if (allStakes.size() > stakeID)
		  {
			  throw yprotocol::RequestError(req, "A stake with ID " + std::to_string(stakeID) + " does not exist!");
		  }
		  else if (!game.StakeRules.IsValid(stakeID))
		  {
			  throw yprotocol::RequestError(req, "Stake with ID " + std::to_string(stakeID) + " is invalid!");
		  }

		  SetStake(game, stakeID);

		  return ::roulette::TRouletteBets::stakeAsJson(*game.StakeInfo.Stake, game.StakeInfo.Multiplier);
	  },
	  SelectStakeSchema, { { "If successful", ::roulette::TRouletteStake::StakeSchema() } }, EPlayLockLevels(EPlayLockLevels::LockHost));

	OnServiceStatusChanged += [this](bool bOnline) {
		if (!bOnline)
		{
			const bool bFirstTimeDropped = mIsDoubleZero.exchange(-1) >= 0;
			if (bFirstTimeDropped)
				Log(Warning, "Connection with roulette service dropped!");
			{
				SharedScopedLock lock(mPlayers);
				if (mGameToAccountFor)
					Log(Critical, "Game %lu has not been accounted for yet and will be void!", mGameToAccountFor);
			}
			if (bFirstTimeDropped)
				VoidLiveGame("Communication with game service was dropped");
		}
	};
}

TRouletteHost::~TRouletteHost()
{
	{
		ScopedLock lock(mFireballDeluxeJackpotModules);
		for (auto& jackpot : &mFireballDeluxeJackpotModules | std::views::values) { jackpot->shutdown(); }
		mFireballDeluxeJackpotModules->clear();
	}

	{
		ScopedLock lock(mLuckyNumberJackpotModules);
		for (auto& jackpot : &mLuckyNumberJackpotModules | std::views::values) { jackpot->shutdown(); }
		mLuckyNumberJackpotModules->clear();
	}
}

template <typename T>
void TRouletteHost::UpdateValue(const T& oldValue, T newValue, const std::string& eventTrigger)
{
	if (newValue != oldValue)
	{
		json phaseChangeData;
		phaseChangeData["name"] = eventTrigger;
		phaseChangeData["old"] = oldValue;
		phaseChangeData["new"] = newValue;
		TriggerEvent(*GameStateChangedEvent, phaseChangeData);
	}
}

// Explicit instantiation for the type used publicly in FireballRoulette
template void TRouletteHost::UpdateValue<std::unordered_map<std::string, uint16_t>>(const std::unordered_map<std::string, uint16_t>&,
                                                                                    std::unordered_map<std::string, uint16_t>, const std::string&);

bool TRouletteHost::LuckyJackpotEnabled() const noexcept
{
	return mLuckyNumberConfig.IsJackpotEnabled();
}

bool TRouletteHost::FireballDeluxeJackpotEnabled() const noexcept
{
	return mFireballDeluxeConfig.IsJackpotEnabled();
}

json TRouletteHost::GetGameState(const std::shared_ptr<const YGameClient>& client) const
{
	json rouletteState = TLiveGameHost::GetGameState(client);
	SharedScopedLock lock(CurrentState);
	rouletteState["ID"] = CurrentState->RouletteGameID;
	rouletteState["gameID"] = CurrentState->RouletteGameID;
	rouletteState["timeLeft"] = CurrentState->GetTimeLeftMs(mBetsClosedBehavior);
	rouletteState["totalTime"] = CurrentState->GetTotalTimeMs(mBetsClosedBehavior);
	rouletteState["phase"] = CurrentState->GetPhase(mBetsClosedBehavior);
	rouletteState["phaseSubID"] = CurrentState->GetSubPhase(mBetsClosedBehavior);
	rouletteState["winNumber"] = CurrentState->WinningNumber;
	rouletteState["ballOutTime"] = RNGType == roulette::ERNGType::Abbiati ? mAbbiatiMaxTimeAfterBallThrow : mLastBallOutTime;
	rouletteState["doubleZero"] = bConfiguredAsDoubleZero;

	bool bShouldIncludeThunderInfo = mThunderMode != EThunderMode::Disabled;
	double fireballFee = 0.0;
	const ThunderRNGInstance* thunderRNG = nullptr;
	std::optional<::roulette::NumberMultiplier> fireballBonusMultiplier;

	if (client && client->Game())
	{
		if (auto rouletteGame = dynamic_cast<RouletteGame*>(client->Game().get()))
		{
			bShouldIncludeThunderInfo = false;
			if (LuckyJackpotEnabled() && rouletteGame->IsLuckyNumberJackpotEnabled())
			{
				if (const auto jpModule = client->Jackpot())
					rouletteState["jackpot"]["luckyLevels"] = std::dynamic_pointer_cast<LuckyNumberJackpot>(jpModule)->GetLuckyNumbers();
			}

			if (rouletteGame->Thunder != EThunderVariant::None)
			{
				bShouldIncludeThunderInfo = true;
				thunderRNG = GetThunderRNG(rouletteGame->Info(), rouletteGame->Thunder);
				fireballBonusMultiplier = rouletteGame->FireballBonusMultiplier;
				fireballFee = rouletteGame->GetFireballFee();
			}
		}
	}
	else
	{
		thunderRNG = GetThunderRNG({}, {});
	}

	if (bShouldIncludeThunderInfo)
	{
		rouletteState["boardMultiplier"] = thunderRNG ? thunderRNG->RNG->CurrentRound.GetBaseMultiplier(fireballFee > 0) : 36U;
		rouletteState["thunderNumbers"] = thunderRNG ? thunderRNG->NewThunderNumbers : json(json::value_t::array);
		rouletteState["fireballFee"] = fireballFee;
		if (fireballBonusMultiplier)
			rouletteState["fireballBonus"] = fireballBonusMultiplier->ToJSON();
	}

	return rouletteState;
}

void TRouletteHost::SendJackpotUpdates(const std::string& levelId, std::weak_ptr<RouletteJackpot> weakJpModule)
{
	DoAsyncTask([this, levelId, weakJpModule]() {
		auto jpModule = weakJpModule.lock();
		if (!jpModule)
			return;

		// this is array due to legacy. Could be object in the future, but should sync with the frontend
		json levelsJson(json::value_t::array);
		auto levels = jpModule->GetLevels();
		const auto levelIt = levels.find(levelId);
		if (levelIt == levels.end())
			return;
		levelsJson.push_back(levelIt->second.ToJSON());

		ForEachPlayer([&](Player& player) {
			ScopedLock lock(player);
			if (const auto playerJp = player.Jackpot(); playerJp && playerJp == jpModule)
				player.TriggerEvent(*JackpotChangedEvent, levelsJson);
		});

		ForEachViewer([&](ViewerInstance& viewer) {
			ScopedLock lock(viewer);
			// if jpModule has no provider and viewer provider key is not empty, skip
			if (!jpModule->GetProvider() && !viewer.GetProviderKey().empty())
				return;

			// if viewer is tracking a different provider, skip
			if (jpModule->GetProvider() && jpModule->GetProvider()->UniqueIdentifier() != viewer.GetProviderKey())
				return;

			// if viewer is tracking demo and jpModule is not demo, skip
			if (viewer.IsDemo() != jpModule->IsDemo())
				return;

			// if viewer is tracking a different game, skip
			if (viewer.GetGameKey() != jpModule->GetGameKey())
				return;

			viewer.TriggerEvent(*JackpotChangedEvent, levelsJson);
		});
	});
}

void TRouletteHost::OnConfigLoaded(const std::filesystem::path& filename)
{
	TLiveGameHost::OnConfigLoaded(filename);
	TBaseRouletteHost::OnConfigLoaded(filename);

	bConfiguredAsDoubleZero = GetConfig("double-zero").get<bool>();

	RNGType = roulette::ERNGType::_from_string(GetConfig("rng-type").get<std::string>().c_str());
	Log(Important, "The RNG type is set to %s", RNGType._to_string());

	mRNGAddress = GetConfig("address").get<std::string>();
	Log(Info, "Will connect to %s at %s", (RNGType == roulette::ERNGType::imaxaGaming) ? "roulette master" : "Abbiati wheel", mRNGAddress.c_str());

	bSecureRNG = GetConfig("secure").get<bool>();
	if (RNGType == roulette::ERNGType::imaxaGaming)
	{
		bUseWebsockets = GetConfig("imaxa.enable-ws").get<bool>();
		bImaxaEnableRTPControl = GetConfig("imaxa.enable-rtp-control").get<bool>();
	}

	const uint32_t delay = GetConfig("delay").get<uint32_t>();
	mDelayMs = std::min(5000U, delay);
	if (delay > mDelayMs)
		Log(
		  Warning,
		  "The delay is hardcoded to max out at 5 seconds because you must be fucking insane to want anything more. Your value of %u was ignored and 5 seconds was chosen instead.",
		  delay);
	if (mDelayMs)
		Log(Important, "Will delay all state machine data by %ums before sending it to the client.", mDelayMs);

	mDefaultStakeRules = FStakeRules::FromJSON(GetConfig("stakes.default-provider"));

	const std::string stakeName = mDefaultStakeRules.Default ? "0" : (std::string("STAKE_") + std::to_string(mDefaultStakeRules.Default - 1));
	Log(Info, "The default stake is set to %d%s.", mDefaultStakeRules.Default, mDefaultStakeRules.Default ? ("(" + stakeName + ")").c_str() : "");

	mProviderStakeRules.clear();
	const json stakeProviderConfig = GetConfig("stakes.other-providers");
	if (stakeProviderConfig.is_object())
	{
		for (auto stake = stakeProviderConfig.begin(); stake != stakeProviderConfig.end(); stake++) mProviderStakeRules[stake.key()] = FStakeRules::FromJSON(*stake);
	}

	bAllowNoChecksum = GetConfig("allow-no-checksum").get<uint32_t>();
	if (bAllowNoChecksum)
		Log(Info, "This host is allowed to have an empty RNG checksum.");

	if (bForceStakeSelect)
	{
		if (mDefaultStakeRules.Default)
			Log(Warning, "The 'default-stake' is set to %s, but wil be ignored because 'force-stake-select' is ON", stakeName.c_str());
	}

	bStartButtonEnabled = GetConfig("allow-start-vote").get<bool>();
	Log(Info, "Voting for game start is %s", bStartButtonEnabled ? "ENABLED" : "DISABLED");

	mAdditionalStakes.clear();
	const json stakesJSON = GetConfig("stakes.additional-stakes");
	if (stakesJSON.is_array() && !stakesJSON.empty())
	{
		for (uint stakeID = 0; stakeID < stakesJSON.size(); stakeID++)
		{
			try
			{
				::roulette::TRouletteStake stake;
				stake.LoadSubconfiguration(stakesJSON[stakeID]);
				mAdditionalStakes.push_back(std::move(stake));
			}
			catch (const std::exception& e)
			{
				Log(Warning, "Additional stake %u is invalid: %s", stakeID, e.what());
			}
		}
		Log(Info, "Loaded %lu additional stakes", mAdditionalStakes.size());
	}

	mBetTimeoutDuration = GetConfig("bet-timeout").get<uint32_t>() * 1000;
	mBetsClosedBehavior.ReduceBetTimeByMs = GetConfig("late-bet-threshold").get<uint32_t>();

	if (RNGType == roulette::ERNGType::Abbiati)
	{
		if (GetConfig("abbiati").is_null())
			throw ConfigError("RNG type of roulette host is set to Abbiati, but Abbiati configuration is missing");
		mAbbiatiRoundTime = GetConfig("abbiati.round-time").get<uint32_t>() * 1000;
		mAbbiatiMaxTimeAfterBallThrow = GetConfig("abbiati.bets-open-duration-after-ball-throw").get<uint32_t>();
		if (mAbbiatiMaxTimeAfterBallThrow >= mAbbiatiRoundTime)
			throw ConfigError("Time left at ball throw cannot be larger or equal to the the total round time");

		mAbbiatiBallOutWheelSpeed = GetConfig("abbiati.wheel-properties.wheel-speed").get<uint32_t>();
		mAbbiatiBetsClosedBallSpeed = GetConfig("abbiati.wheel-properties.ball-speed-on-bets-closed").get<uint32_t>();
		mAbbiatiLinDrag = GetConfig("abbiati.wheel-properties.lin-drag-coeff").get<float>();
	}

	mThunderMode = EThunderMode::_from_string(GetConfig("thunder.mode").get<std::string>().c_str());
	bEnablePerGameThunder = GetConfig("thunder.allow-per-game-rules").get<bool>();
	if (mThunderMode == EThunderMode::LiveOnly || mThunderMode == EThunderMode::Disabled)
		bEnablePerGameThunder = false;

	mLuckyNumberConfig = RouletteJackpotConfig::FromJSON(GetConfigOptional("lucky-jackpot", json::object()), false);
	Log(Info, "Lucky number jackpot is %s.",
	    mLuckyNumberConfig.IsJackpotEnabled() ?
	      std::format("enabled on server {} with {} levels", mLuckyNumberConfig.JackpotAddress, mLuckyNumberConfig.JackpotLevels.size()).c_str() :
	      "disabled");

	mFireballDeluxeConfig = RouletteJackpotConfig::FromJSON(GetConfigOptional("thunder.jackpot", json::object()), true);
	Log(Info, "Fireball Deluxe jackpot is %s.",
	    mFireballDeluxeConfig.IsJackpotEnabled() ?
	      std::format("enabled on server {} with {} levels", mFireballDeluxeConfig.JackpotAddress, mFireballDeluxeConfig.JackpotLevels.size()).c_str() :
	      "disabled");
}

void TRouletteHost::PostLoad()
{
	TLiveGameHost::PostLoad();

	if (RNGType == roulette::ERNGType::Abbiati)
	{
		mAbbiatiClient = web::SingleWebsocketClient::New(web::websockets::uri(web::websockets::uri::websocket, bSecureRNG, mRNGAddress, {}));
		mAbbiatiClient->OnConnectionStateChanged += [this](web::websockets::session::state::value s, const std::error_code& ec) {
			if (s == web::websockets::session::state::closed)
			{
				std::string suffix;
				if (ec)
					suffix = ": " + ec.message();
				ServiceDisconnected("Abbiati wheel server connection lost" + suffix);
			}
		};
		mAbbiatiClient->OnMessage = [this](const std::string& msg) {
			on_abbiati_msg(msg);
		};
		mAbbiatiClient->Initialize();
	}

	EThunderVariant localVariant = EThunderVariant::_from_string(GetConfig("thunder.local-configuration-type").get<std::string>().c_str());
	if (!(mThunderMode == EThunderMode::LocalOnly || mThunderMode == EThunderMode::LiveWithLocalFallback))
		localVariant = EThunderVariant::None;

	try
	{
		LocalThunderRNG = TBaseRouletteHost::CreateThunderRNG("local", localVariant, GetConfig("thunder.local-configuration"), GetConfig("thunder.local-configuration-2"),
		                                                      bConfiguredAsDoubleZero, LogRoulette);

		if (LocalThunderRNG)
			LocalThunderRNG->LogComponentName = LogComponentName + " [local thunder rng]";
	}
	catch (const std::exception& e)
	{
		throw ConfigError(yutils::Format("Failed to create a local thunder RNG: %s", e.what()));
	}

	if (RNGType == ERNGType::imaxaGaming)
	{
		RegisterAdminAction("Raise Drum", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::RaiseDrum(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error raising drum: %d", res));

			                    return {};
		                    });

		RegisterAdminAction(
		  "Blow Ball", EModuleAdminActionTarget::Module,
		  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			  if (!Roulette)
				  throw yprotocol::RequestError(request, "Not connected to roulette master!");

			  const int res = rmclientdrv::Blow(Roulette, params["blowTime"].get<uint32_t>(), (short)params["blowPosition"].get<uint32_t>());
			  if (res)
				  throw yprotocol::RequestError(request, yutils::Format("Error blowing ball: %d", res));

			  return {};
		  },
		  JsonSchema({ { "blowTime", JsonSchema(json::value_t::number_unsigned, "The duration of the blow (in ms)", 2000U) },
		               { "blowPosition", JsonSchema(json::value_t::number_unsigned,
		                                            "The winning number at which to start blowing (255 for NOW, regardless of cylinder position)", 255U) } }));

		RegisterAdminAction("Pause Game", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::PauseGame(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error pausing game: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Resume Game", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::ResumeGame(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error resuming game: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Continue Game", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::ContinueGame(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error continuing game: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Reset Game", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::ResetGame(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error resetting game: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Open Cupola", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::OpenCylinderCover(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error opening cupola: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Stop Moving Cupola", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::StopCylinderCover(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error stopping cupola: %d", res));

			                    return {};
		                    });

		RegisterAdminAction("Close Cupola", EModuleAdminActionTarget::Module,
		                    [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			                    if (!Roulette)
				                    throw yprotocol::RequestError(request, "Not connected to roulette master!");

			                    const int res = rmclientdrv::CloseCylinderCover(Roulette);
			                    if (res)
				                    throw yprotocol::RequestError(request, yutils::Format("Error closing cupola: %d", res));

			                    return {};
		                    });

		RegisterAdminAction(
		  "Set Motor Speed", EModuleAdminActionTarget::Module,
		  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			  if (!Roulette)
				  throw yprotocol::RequestError(request, "Not connected to roulette master!");

			  const int res = rmclientdrv::SetMotorSpeed(Roulette, params.get<uint32_t>(), 255 /* change speed immediately */);
			  if (res)
				  throw yprotocol::RequestError(request, yutils::Format("Error setting motor speed: %d", res));

			  return {};
		  },
		  JsonSchema(json::value_t::number_unsigned, "The motor speed index (0 to 7)").AddConstraint(limits::IValueLimit::MakeRange(0U, 7U, true, true)));

		RegisterAdminAction(
		  "Set Configuration Preset", EModuleAdminActionTarget::Module,
		  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			  if (!Roulette)
				  throw yprotocol::RequestError(request, "Not connected to roulette master!");

			  const std::string res = rmclientdrv::ExecuteBashScript(Roulette, yutils::Format("/home/<USER>/presets/wheel_preset%u.sh", params.get<uint32_t>()), 0);

			  return res;
		  },
		  JsonSchema(json::value_t::number_unsigned, "The preset number (1 to 6)").AddConstraint(limits::IValueLimit::MakeRange(1U, 6U, true, true)));

		RegisterAdminAction(
		  "Set Control Board Parameter", EModuleAdminActionTarget::Module,
		  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			  if (!Roulette)
				  throw yprotocol::RequestError(request, "Not connected to roulette master!");

			  const int res = rmclientdrv::SetCBParam(Roulette, params["index"].get<uint32_t>(), params["value"].get<int>());

			  if (res)
				  return yutils::Format("Error %d setting parameter at index %u to value '%s'", params["index"].get<uint32_t>(),
				                        params["value"].get<std::string>().c_str());

			  return {};
		  },
		  JsonSchema({ { "index", JsonSchema(json::value_t::number_unsigned, "The index of the CB parameter to set") },
		               { "value", JsonSchema(json::value_t::number_integer, "Parameter value") } }));

		RegisterAdminAction(
		  "Get Control Board Parameter", EModuleAdminActionTarget::Module,
		  [this](const yprotocol::Request& request, const PlayerPtr& target, const json& params, const YAuthKey& instigator) -> json {
			  if (!Roulette)
				  throw yprotocol::RequestError(request, "Not connected to roulette master!");

			  const std::pair<int, std::string> res = rmclientdrv::GetCBParam(Roulette, params.get<uint32_t>());

			  if (res.first)
				  return yutils::Format("Error %d getting parameter at index %u", res.first, params.get<uint32_t>());

			  return res.second;
		  },
		  JsonSchema(json::value_t::number_unsigned, "The index of the CB parameter to get"));
	}
}

bool FStakeRules::IsValid(uint32_t stake) const
{
	switch (AccessMode)
	{
		case EHostAccessMode::All: return true;
		case EHostAccessMode::OptIn: return AccessList.contains(stake);
		case EHostAccessMode::OptOut: return !AccessList.contains(stake);
		default: return false;
	}
}


json TRouletteHost::GetDescriptorInfo(const FModuleDescriptorOptions& options) const
{
	json conf(json::value_t::object);
	conf["doubleZero"] = mIsDoubleZero > 0;
	conf["stakeChangeAllowed"] = bAllowStakeChange;
	conf["confirmBetsOnBetsClosed"] = mBetsClosedBehavior.ReduceBetTimeByMs > 0 ? true : false;
	conf["startVoteEnabled"] = bStartButtonEnabled;
	conf["RNGType"] = RNGType._to_string();
	conf["stream"] = GetVideoStreamInfo();
	if (auto rng = GetThunderRNG({}, {}))
		conf["thunder"] = rng->GetPublicInfo();

	if (RNGType == roulette::ERNGType::Abbiati)
	{
		conf["roundTime"] = mAbbiatiRoundTime;
	}

	if (options.bAdmin)
		conf["betsTimeOffset"] = mBetsClosedBehavior.ReduceBetTimeByMs;

	return conf;
}

bool TRouletteHost::ConnectToService(std::string& outError)
{
	{
		ScopedLock lock(CurrentState);
		CurrentState->Phase = 0;
		mBetsClosedTime = 0;
		mRNGChecksums.clear();
	}

	mBets[bConfiguredAsDoubleZero].clearStakes();
	mAdditionalVersions.clear();

	int newDoubleZero = mIsDoubleZero.exchange(-1);
	if (mRNGAddress.empty())
	{
		outError = "No RNG service address is set!";
		return false;
	}

	if (!TLiveGameHost::ConnectToService(outError))
		return false;

	if (RNGType == roulette::ERNGType::Abbiati)
	{
		const bool bSuccess = ConnectToServiceAbbiati();
		if (!bSuccess)
			outError = "Could not connect to the Abbiati wheel server.";
		return bSuccess;
	}

	if (!Roulette)
	{
		Roulette = rmclientdrv::Init(mRNGAddress, bSecureRNG, bUseWebsockets);
		if (Roulette)
		{
			rmclientdrv::SetCylinderPollTime(Roulette, 0);
			rmclientdrv::SetDelay(Roulette, mDelayMs);
			newDoubleZero = -1;
			const uint64_t startTime = yprotocol::Timestamp();
			while (!rmclientdrv::IsServerCommunicationOK(Roulette) && !yprotocol::IsTimedOut(startTime, 3000))
				std::this_thread::sleep_for(std::chrono::milliseconds(500));
		}
		else
		{
			outError = "Could not create a roulette client using the configured server address";
			return false;
		}
	}

	if (!rmclientdrv::IsServerCommunicationOK(Roulette))
	{
		outError = "Could not establish communication with the roulette server";
		return false;
	}

	if (IsShuttingDown())
	{
		outError = "Module is shutting down";
		return false;
	}

	if (newDoubleZero < 0)
	{
		std::string rouletteName;
		const int nameResult = rmclientdrv::GetParam(Roulette, "ROULETTE_NAME", rouletteName);
		if (nameResult || rouletteName.empty())
		{
			Log(Error, "Roulette name could not be fetched(%d), but it is required!", nameResult);
			outError = "Unable to get roulette name from server!";
			return false;
		}

		std::pair<std::string, std::string> checksum;
		const int filenameResult = rmclientdrv::GetParam(Roulette, "RNG_BINARY_PATH", checksum.first);
		const int checksumResult = rmclientdrv::GetParam(Roulette, "RNG_CHECKSUM", checksum.second);
		if (checksumResult || filenameResult || checksum.first.empty() || checksum.second.empty())
		{
			if (!bAllowNoChecksum)
			{
				Log(Error, "Roulette checksum could not be fetched(%d), but it is required!", checksumResult);
				outError = "Unable to get roulette checksum from server!";
				return false;
			}
			Log(Warning, "Roulette checksum could not be fetched(%d), but this is allowed in configuration - continuing without it.", checksumResult);
		}
		else
		{
			mRNGChecksums[checksum.first] = checksum.second;
		}

		try
		{
			SetDomain(rouletteName);
		}
		catch (const std::runtime_error& err)
		{
			outError = std::string("Unable to get server name: ") + err.what();
			Log(Critical, "Could not set domain: %s - shutting down! (%s)", err.what(), rouletteName.c_str());
			Shutdown("Bad configuration");
			return false;
		}

		Log(Info, "Roulette domain set to %s", rouletteName.c_str());

		const int currencyResult = rmclientdrv::GetParam(Roulette, "ACCOUNTING_CURRENCY", RouletteCurrency);
		if (currencyResult || RouletteCurrency.empty())
		{
			Log(Warning, "Could not get roulette accounting currency (err %d), will fallback to EUR!", currencyResult);
			RouletteCurrency = ECurrency(ECurrency::EUR)._to_string();
		}
		if (!Container()->Server->CurrencyAPI->Update(RouletteCurrency))
		{
			Log(Warning, "Could not get exchange rates for currency %s, will fallback to EUR!", RouletteCurrency.c_str());
			RouletteCurrency = ECurrency(ECurrency::EUR)._to_string();
		}

		Log(Info, "Roulette accounting currency set to %s", RouletteCurrency.c_str());

		if (LuckyJackpotEnabled() || FireballDeluxeJackpotEnabled())
		{
			LogScope JackpotScope;

			Container()->Server->ProviderContainer()->OnModuleRegistration += [this](std::shared_ptr<YModule> module, EModuleRegistrationEvent event,
			                                                                         bool bIsPartOfReload) {
				const auto provider = std::dynamic_pointer_cast<provider::TCasinoProvider>(module);
				if (!provider)
					return;

				if (event == EModuleRegistrationEvent::ModuleAdded)
				{
					if (LuckyJackpotEnabled())
					{
						ScopedLock lock(mLuckyNumberJackpotModules);
						for (const auto& gameKey : ListGameKeysForJackpot(ERouletteJackpotType::LuckyNumber))
							RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::LuckyNumber, &mLuckyNumberJackpotModules, mLuckyNumberConfig, gameKey, provider);
					}

					if (FireballDeluxeJackpotEnabled())
					{
						ScopedLock lock(mFireballDeluxeJackpotModules);
						for (const auto& gameKey : ListGameKeysForJackpot(ERouletteJackpotType::FireballDeluxe))
							RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::FireballDeluxe, &mFireballDeluxeJackpotModules, mFireballDeluxeConfig, gameKey,
							                                       provider);
					}
				}
				else
				{
					if (LuckyJackpotEnabled())
					{
						ScopedLock lock(mLuckyNumberJackpotModules);
						UnregisterJackpotModules_AssumeWriteLock(&mLuckyNumberJackpotModules, provider);
					}

					if (FireballDeluxeJackpotEnabled())
					{
						ScopedLock lock(mFireballDeluxeJackpotModules);
						UnregisterJackpotModules_AssumeWriteLock(&mFireballDeluxeJackpotModules, provider);
					}
				}
			};

			const auto providers = ListOnlineProviders();

			if (LuckyJackpotEnabled())
			{
				// we want to avoid the roulette jackpot destructors being called while the list of all jackpots is locked
				std::map<std::string, std::shared_ptr<roulette::RouletteJackpot>> previousModules;
				{
					ScopedLock lock(mLuckyNumberJackpotModules);
					mLuckyNumberJackpotModules->swap(previousModules);
				}
				previousModules.clear();
				ScopedLock lock(mLuckyNumberJackpotModules);

				for (const auto& gameKey : ListGameKeysForJackpot(ERouletteJackpotType::LuckyNumber))
				{
					for (const auto& provider : providers)
					{
						RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::LuckyNumber, &mLuckyNumberJackpotModules, mLuckyNumberConfig, gameKey, provider);
						// FIXME: THis sets the server currency to the last provider's currency. What if providers are in different currencies?
						//  should we maybe filter providers by the roulette currency?
						Container()->Server->CurrencyAPI->Update(provider->Currency());
					}
					RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::LuckyNumber, &mLuckyNumberJackpotModules, mLuckyNumberConfig, gameKey, nullptr);
				}
			}

			if (FireballDeluxeJackpotEnabled())
			{
				// we want to avoid the roulette jackpot destructors being called while the list of all jackpots is locked
				std::map<std::string, std::shared_ptr<roulette::RouletteJackpot>> previousModules;
				{
					ScopedLock lock(mFireballDeluxeJackpotModules);
					mFireballDeluxeJackpotModules->swap(previousModules);
				}
				previousModules.clear();
				ScopedLock lock(mFireballDeluxeJackpotModules);

				for (const auto& gameKey : ListGameKeysForJackpot(ERouletteJackpotType::FireballDeluxe))
				{
					for (const auto& provider : providers)
					{
						RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::FireballDeluxe, &mFireballDeluxeJackpotModules, mFireballDeluxeConfig, gameKey,
						                                       provider);
						// FIXME: THis sets the server currency to the last provider's currency. What if providers are in different currencies?
						//  should we maybe filter providers by the roulette currency?
						Container()->Server->CurrencyAPI->Update(provider->Currency());
					}
					RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::FireballDeluxe, &mFireballDeluxeJackpotModules, mFireballDeluxeConfig, gameKey, nullptr);
				}
			}
		}

		// check if any game has a fireball fee set, but no override stake (then we need to validate the stake chip values
		bool bAnyFireballFeeGameWithoutOverrideStake = false;
		for (const auto& [gameKey, gameInfo] : LaunchInfo().Games)
			if (gameInfo.GetConfig("fireball-fee").get<double>() > 0.0 && gameInfo.GetConfig("override-stake").is_null())
				bAnyFireballFeeGameWithoutOverrideStake = true;

		mAdditionalVersions = rmclientdrv::Versions(Roulette);

		// IsDoubleZero() returns a negative code on error (e.g. function not supported by cylinders or old masters built before 1.7.2020)
		newDoubleZero = rmclientdrv::IsDoubleZero(Roulette);
		if (newDoubleZero >= 0)
		{
			Log(Info, "Connected to roulette master server running %s zero!", newDoubleZero ? "DOUBLE" : "SINGLE");
			if (bConfiguredAsDoubleZero != (bool)newDoubleZero)
			{
				outError = "Configured 'double-zero' value does not match the one on the server!";
				Log(Error, "Double zero config of this host does not match the config on the remote roulette RNG server!");
				return false;
			}

			if (rmclientdrv::GetVideoStreamInfo(Roulette, mVideoURL, mAudioURL) == 0)
			{
				Log(Info, "Video running at '%s', with audio at '%s'", mVideoURL.c_str(), mAudioURL.c_str());
			}
			else
			{
				Log(Warning, "Couldn't get video stream information! Is the XML interface updated to the newest version?");
			}

			std::vector<::roulette::TRouletteStake> stakes;
			int stakeLoadResult = rmclientdrv::GetStakes(Roulette, stakes);
			if (stakes.empty())
			{
				if (stakeLoadResult)
					Log(Error, "Failed to read stake setup from master (%d). Is it running the new interface?", stakeLoadResult);
				else
					Log(Error, "Master seems to support GetStakes over RPC, but did not provide any stakes (empty array)!");
			}
			else
			{
				const size_t allStakesNum = stakes.size();
				Log(Info, "Successfully read %lu stakes from roulette master!", allStakesNum);
				size_t numStakes = 0;
				for (size_t stakeIdx = 0; stakeIdx < stakes.size(); stakeIdx++)
				{
					try
					{
						mBets[bConfiguredAsDoubleZero].addStake(stakes[stakeIdx], bAnyFireballFeeGameWithoutOverrideStake);
					}
					catch (const std::exception& e)
					{
						Log(Warning, "Stake %lu is invalid and will be skipped! %s", stakeIdx, e.what());
						continue;
					}

					if (mDefaultStakeRules.Default == stakeIdx && mDefaultStakeRules.Default != numStakes)
					{
						Log(Warning, "The default stake is set to %u, but because some stakes before it are not used, it is effectively translated to %lu.",
						    mDefaultStakeRules.Default, numStakes);
						mDefaultStakeRules.Default = numStakes;
					}
					numStakes++;
				}
				if (numStakes == 0)
				{
					Log(Error, "No valid stakes were found that conform with the conditions defined by stake-mode and stake-access-list!");
				}
				else
				{
					Log(Info, "%lu of the total %lu stakes will be used on this game host!", numStakes, allStakesNum);

					if (mDefaultStakeRules.Default >= numStakes)
					{
						Log(Warning, "The default stake is set to %u, but no such stake exists on the roulette host! Will use 0 as the default stake.",
						    mDefaultStakeRules.Default);
					}
				}
			}

			for (const ::roulette::TRouletteStake& s : mAdditionalStakes)
			{
				try
				{
					mBets[bConfiguredAsDoubleZero].addStake(s, bAnyFireballFeeGameWithoutOverrideStake);
				}
				catch (const std::exception& e)
				{
					Log(Warning, "Stake in additional-stakes is invalid: %s", e.what());
				}
			}

			if (mBets[bConfiguredAsDoubleZero].getAllStakes().empty())
			{
				outError = "No stakes available";
				Log(Warning, "Can not start because it could not get any stakes from roulette master or the game host config - at least 1 stake MUST be present!");
				return false;
			}

			bRequireHistoryUpdate = true;
			TriggerEvent(*ConfigChangedEvent);
		}
		else
		{
			outError = "Error in roulette server communication";
		}
	}

	mIsDoubleZero = newDoubleZero;
	return newDoubleZero >= 0;
}

void TRouletteHost::RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType jackpotType, std::map<std::string, std::shared_ptr<roulette::RouletteJackpot>>& jpModules,
                                                           const roulette::RouletteJackpotConfig& jackpotConfig, const std::string& gameKey,
                                                           const std::shared_ptr<provider::TCasinoProvider>& provider)
{
	if (provider)
		RegisterJackpotModule_AssumeWriteLock(jackpotType, jpModules, jackpotConfig, gameKey, provider, false);
	RegisterJackpotModule_AssumeWriteLock(jackpotType, jpModules, jackpotConfig, gameKey, provider, true);
}

void TRouletteHost::RegisterJackpotModule_AssumeWriteLock(ERouletteJackpotType jackpotType, std::map<std::string, std::shared_ptr<roulette::RouletteJackpot>>& jpModules,
                                                          const roulette::RouletteJackpotConfig& jackpotConfig, const std::string& gameKey,
                                                          const std::shared_ptr<provider::TCasinoProvider>& provider, const bool demo)
{
	const auto key = RouletteJackpot::GetUID(jackpotType, bConfiguredAsDoubleZero, gameKey, provider, demo);
	if (!jpModules.contains(key))
	{
		std::shared_ptr<roulette::RouletteJackpot> jackpotModule;

		switch (jackpotType)
		{
			case ERouletteJackpotType::LuckyNumber: jackpotModule = std::make_shared<LuckyNumberJackpot>(jackpotConfig, this, provider, gameKey, demo); break;
			case ERouletteJackpotType::FireballDeluxe: jackpotModule = std::make_shared<FireballDeluxeJackpot>(jackpotConfig, this, provider, gameKey, demo); break;
			default: throw std::invalid_argument("Unsupported jackpot type");
		}

		jpModules.emplace(key, jackpotModule);
	}
}

void TRouletteHost::UnregisterJackpotModules_AssumeWriteLock(std::map<std::string, std::shared_ptr<roulette::RouletteJackpot>>& jpModules,
                                                             const std::shared_ptr<provider::TCasinoProvider>& provider, const std::optional<std::string>& gameKey)
{
	for (auto it = jpModules.begin(); it != jpModules.end();)
	{
		auto& [key, jpModule] = *it;
		if ((provider && provider == jpModule->GetProvider()) || (gameKey && *gameKey == jpModule->GetGameKey()))
		{
			Log(Info, "Shutting down jackpot module %s", key.c_str());
			jpModule->shutdown();
			it = jpModules.erase(it);
		}
		else
			++it;
	}
}

std::unordered_set<std::string> TRouletteHost::ListGameKeysForJackpot(const ERouletteJackpotType jackpotType) const
{
	std::unordered_set<std::string> gameKeys;
	if (jackpotType == ERouletteJackpotType::LuckyNumber)
	{
		for (const auto& [gameKey, gameInfo] : LaunchInfo().Games)
		{
			if (gameInfo.GetConfigOptional("allow-lucky-number-jp", false).get<bool>())
				gameKeys.emplace(gameKey);
		}
	}
	else if (jackpotType == ERouletteJackpotType::FireballDeluxe)
	{
		gameKeys.insert(std::views::keys(PerGameThunderRNGs).begin(), std::views::keys(PerGameThunderRNGs).end());
		if (LocalThunderRNG)
			gameKeys.emplace(std::string());
	}

	return gameKeys;
}

std::list<std::shared_ptr<provider::TCasinoProvider>> TRouletteHost::ListOnlineProviders() const
{
	std::list<std::shared_ptr<provider::TCasinoProvider>> providers;
	Container()->Server->ProviderContainer()->ForEach([this, &providers](const std::shared_ptr<provider::TCasinoProvider>& provider) {
		if (provider->HasUniqueID() && CanAccessFrom(provider) && provider->IsOnline() && provider->Denomination() > 0)
			providers.push_back(provider);
	});
	return providers;
}

void TRouletteHost::OnPlayerDisconnected(Player& player)
{
	TGameHost::OnPlayerDisconnected(player);

	if (!mBetTimeoutDuration)
		return;

	auto game = dynamic_cast<RouletteGame*>(player.Game().get());
	if (game->BetStatus() != EGameBetState::BetPlaced || !game->LastBetTime)
		return;

	const int64_t TimeUntilBetClear = int64_t(game->LastBetTime + mBetTimeoutDuration) - ytime::GetTimeMsec();
	if (TimeUntilBetClear <= 0)
		player.VoidGame(FGameroundError({ "BetsTooOld", "Connection lost with bets that are too old" }), false);
	else
		game->BetTimeout = Container()->Server->SetTimer(TimeUntilBetClear, [ref = player.weak_from_this(), gid = game->GameRound()](const std::error_code& ec) {
			if (ec)
				return;

			auto player = std::dynamic_pointer_cast<Player>(ref.lock());
			if (!player)
				return;

			ScopedLock lock(*player);
			if (player->Game()->GameRound() != gid)
				return;

			if (player->Game()->BetStatus() == EGameBetState::BetPlaced)
				player->VoidGame(FGameroundError({ "BetsTooOld", "Bets cleared because player has old bets and is offline" }), false);
		});
}

GameInstancePtr TRouletteHost::CreateGameInstance(const GameInformation& game, const std::shared_ptr<GameEndpoint>& endp, const YPlayerAccount& account) const
{
	auto rouletteGame = std::make_shared<RouletteGame>(endp->HostUID, game);
	rouletteGame->bIsDoubleZero = mIsDoubleZero.load() == -1;

	auto provider = endp->Provider.lock();
	rouletteGame->StakeRules =
	  provider ? GetRulesFor(provider->Name()) : (endp->Params.contains("provider") ? GetRulesFor(endp->Params.at("provider")) : mDefaultStakeRules);
	if (PerGameStakes.contains(rouletteGame->Info().Key))
	{
		rouletteGame->StakeRules.Default = 0;
		rouletteGame->StakeRules.AccessList.clear();
		rouletteGame->StakeRules.AccessMode = EHostAccessMode::All;
	}

	rouletteGame->CreditMultiplier = account.CreditValueMultiplier();

	if (endp->ExtraParams.contains("stake"))
	{
		const std::string stake = endp->ExtraParams.Get("stake");
		int stakeID;
		if (yutils::strToInt2(stake, stakeID))
			SetStake(*rouletteGame, stakeID);
	}

	return rouletteGame;
}

bool TRouletteHost::AllowJackpot() const
{
	return TGameHost::AllowJackpot() && !LuckyJackpotEnabled();
}

std::shared_ptr<yserver::jackpot::TJackpotModule> TRouletteHost::Jackpot(const std::shared_ptr<provider::TCasinoProvider>& provider, const StaticGameInformation& game,
                                                                         bool bDemo) const noexcept
{
	if (LuckyJackpotEnabled() && game.GetConfigOptional("allow-lucky-number-jp", false).get<bool>())
	{
		SharedScopedLock lock(mLuckyNumberJackpotModules);
		if (auto find =
		      mLuckyNumberJackpotModules->find(LuckyNumberJackpot::GetUID(ERouletteJackpotType::LuckyNumber, bConfiguredAsDoubleZero, game.Key, provider, bDemo));
		    find != mLuckyNumberJackpotModules->end())
			return find->second;
	}

	if (FireballDeluxeJackpotEnabled() && game.GetConfigOptional("allow-fireball-jp", false).get<bool>() &&
	    game.GetConfigOptional("thunder.type", std::string()).get<std::string>() == EThunderVariant(EThunderVariant::Thunder2)._to_string())
	{
		SharedScopedLock lock(mFireballDeluxeJackpotModules);
		std::string gameKey;    // default empty string when we have host-based thunder
		if (const auto& config = game.GetConfigOptional("thunder.configuration2", nullptr); config.is_object() && !config.empty())
			gameKey = game.Key;
		else if (const auto& linkedConfigKey = game.GetConfigOptional("thunder.linked-configuration", std::string()).get<std::string>(); !linkedConfigKey.empty())
			gameKey = linkedConfigKey;

		if (auto find =
		      mFireballDeluxeJackpotModules->find(FireballDeluxeJackpot::GetUID(ERouletteJackpotType::FireballDeluxe, bConfiguredAsDoubleZero, gameKey, provider, bDemo));
		    find != mFireballDeluxeJackpotModules->end())
			return find->second;
	}

	return {};
}

const JsonSchema& TRouletteHost::GetGameSchema() const
{
	return RouletteGameSchema;
}

std::map<std::string, std::string> TRouletteHost::Checksums(const GameHostGameInformation& info, const std::optional<std::string>& launchOption) const
{
	std::map<std::string, std::string> Ret = TBaseRouletteHost::Checksums(info, launchOption);
	{
		SharedScopedLock lock(CurrentState);
		for (const auto& [filename, checksum] : mRNGChecksums) Ret[filename] = checksum;
	}

	return Ret;
}

void TRouletteHost::AddNumberToHistory(uint64_t gameID, int winNumber)
{
	ScopedLock lock(mHistory);
	if (!mHistory->empty() && mHistory->rbegin()->first == gameID)
		return;

	if (mHistory->size() >= Container()->Server->PlayerAdminAPI->MaxHistorySize)
		mHistory->erase(mHistory->begin());
	mHistory->insert_or_assign(mHistory->end(), gameID, winNumber);

	if (mHistoryJSON.size() >= Container()->Server->PlayerAdminAPI->MaxHistorySize)
		mHistoryJSON.erase(0);

	json roundHistory(json::value_t::array);
	roundHistory.push_back(gameID);
	roundHistory.push_back(winNumber);
	mHistoryJSON.push_back(std::move(roundHistory));

	if (RNGType == ERNGType::Abbiati)
	{
		DoAsyncTask([syncSave = Container()->Server->ShouldSyncDiskOperations(), json = mHistoryJSON, file = StorageDir() / "abbiati.history"]() {
			if (syncSave)
			{
				filesystem::SyncWrite(file, json.dump());
			}
			else
			{
				std::ofstream historyFile(file);
				historyFile << json;
			}
		});
	}
}

bool TRouletteHost::ConnectToServiceAbbiati()
{
	mAbbiatiClient->Stop();

	mAbbiatiClient->Start("abbiati-" + std::to_string(ID()));

	ScopedLock lock(AbbiatiWheelSerialNumber);
	&AbbiatiWheelSerialNumber = 0;
	mAbbiatiChecksum = {};
	std::error_code ec = mAbbiatiClient->Connect();
	if (ec)
	{
		Log(Critical, "Could not connect connect to service at %s: %s", mRNGAddress.c_str(), ec.message().c_str());
		return false;
	}

	{
		ScopedLock lock2(NewAbbiatiState);
		bAbbiatiNeedsFirstUpdate = true;
	}

	mLastBallOutTime = mAbbiatiMaxTimeAfterBallThrow;
	bAbbiatiHasBallSpeed = false;

	Log(Info, "Connected, reading Abbiati wheel status to determine serial number...");
	AbbiatiWheelSerialCV.wait_for(lock, std::chrono::seconds(5), [this]() -> bool { return &AbbiatiWheelSerialNumber; });

	const uint32_t serial = &AbbiatiWheelSerialNumber;
	if (!serial)
	{
		Log(Warning, "Could not read wheel status, maybe it isn't running or isn't physically connected to the abbiati wheel server");
		return false;
	}
	const auto checksum = mAbbiatiChecksum;
	lock.unlock();

	if (!checksum.first.empty() && !checksum.second.empty())
	{
		ScopedLock lock2(CurrentState);
		mRNGChecksums[checksum.first] = checksum.second;
	}

	try
	{
		SetDomain(std::to_string(serial));
	}
	catch (const std::runtime_error& err)
	{
		Log(Critical, "Could not set domain: %s - shutting down! (%04u)", err.what(), serial);
		Shutdown("Bad configuration");
		return false;
	}

	Log(Important, "Success! Connected to Abbiati wheel %04u", serial);

	{
		ScopedLock lock2(mHistory);
		mHistory->clear();
		mHistoryJSON = json(json::value_t::array);
		const std::filesystem::path historyFilepath = StorageDir() / "abbiati.history";
		if (std::filesystem::exists(historyFilepath))
		{
			std::ifstream historyFile(historyFilepath);
			if (historyFile.is_open())
			{
				try
				{
					historyFile >> mHistoryJSON;
				}
				catch (const std::exception& e)
				{
					Log(Warning, "Could not parse history file for abbiati at %s: %s", historyFilepath.c_str(), e.what());
				}
			}
		}

		for (const json& val : mHistoryJSON) mHistory->insert_or_assign(mHistory->end(), val[0U].get<uint64_t>(), val[1U].get<int>());
	}

	if (!yutils::strToUInt2(GetOrSetParam("abbiati-game-id", "1"), mAbbiatiSimulatedGameID) || !mAbbiatiSimulatedGameID)
	{
		mAbbiatiSimulatedGameID = 1;
		SetParam("abbiati-game-id", std::to_string(mAbbiatiSimulatedGameID));
	}

	mIsDoubleZero = bConfiguredAsDoubleZero ? 1 : 0;
	for (const ::roulette::TRouletteStake& s : mAdditionalStakes)
	{
		try
		{
			mBets[bConfiguredAsDoubleZero].addStake(s);
		}
		catch (const std::exception& e)
		{
			Log(Warning, "Stake in additional-stakes is invalid: %s", e.what());
		}
	}

	if (mBets[bConfiguredAsDoubleZero].getAllStakes().empty())
	{
		Log(Warning, "Can not start because there are no stakes configured - at least 1 stake MUST be present!");
		return false;
	}

	return true;
}

const JsonSchema BetSchema =
  JsonSchema({ { "bets", JsonSchema(json::value_t::array, "Array of bet placements", json(json::value_t::array))
                           .SetChildSchema(JsonSchema({ { "X", JsonSchema(json::value_t::number_unsigned, "The X coordinate in the bet table") },
                                                        { "Y", JsonSchema(json::value_t::number_unsigned, "The Y coordinate in the bet table") },
                                                        { "amount", JsonSchema(json::value_t::number_unsigned, "The amount to bet on this field") } })) },
               { "betPresets", JsonSchema(json::value_t::object, "Map of bet placements on preset fields", json(json::value_t::object))
                                 .SetChildSchema(JsonSchema(json::value_t::number_unsigned, "The amount to bet on this field")) },
               { "gameID", JsonSchema(json::value_t::number_unsigned, "The ID of the game to register in (current game)") } });

uint64_t TRouletteHost::VerifyBets(const YGameClient& player, const yprotocol::Request& req, json& response) const
{
	const RNGState state = CurrentState.getCopy();

	if (!state.RouletteGameID || req.GetParam("gameID").get<uint64_t>() != state.RouletteGameID)
		throw BetRequestError(req, "Invalid game ID (playing " + std::to_string(state.RouletteGameID) + ")!", FBetErrorInfo("InvalidGame"));

	if (!player.Game()->IsGameroundActive())
		throw BetRequestError(req, "Not participating in a game round. Please wait for a round to start.", FBetErrorInfo("InvalidGame"));

	// don't use BetsClosedBehavior here!
	if (state.AreBetsClosedAndNotError({}))
		throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("BetsClosed"));

	{
		SharedScopedLock lock2(mPlayers);
		if (mGameToAccountFor)
			throw BetRequestError(req, "Bets are closed!", FBetErrorInfo("InvalidGame"));
	}

	// Will throw a BetParseError if unsuccessful
	RouletteGame* gameInst = (RouletteGame*)player.Game().get();
	::roulette::BetAmounts Bets;
	try
	{
		Bets = ::roulette::BetAmounts::FromUserJSON(req.GetParam(), gameInst->CreditMultiplier, mBets[bConfiguredAsDoubleZero].getPresets());
	}
	catch (const BetParseError& err)
	{
		throw BetRequestError(req, err.what(), FBetErrorInfo("InvalidBetObject", err.Context));
	}


	::roulette::FStakeInfo stake = gameInst->StakeInfo;
	stake.Multiplier *= gameInst->CreditMultiplier;
	const FBetVerifyResult verifiedBets = mBets[bConfiguredAsDoubleZero].verifyBets(Bets, stake, gameInst->Mode, gameInst->WonResult, gameInst->GetFireballFee());
	if (verifiedBets.Violation)
	{
		if (*verifiedBets.Violation == EBetRuleViolation::BadSetup)
			throw BetRequestError(req, "Invalid stake", FBetErrorInfo("InternalError"));

		FBetErrorInfo err("BetRuleViolation");
		err.Details = json(json::value_t::object);
		err.Details["x"] = verifiedBets.ErrorX;
		err.Details["y"] = verifiedBets.ErrorY;
		err.Details["violation"] = verifiedBets.Violation->_to_string();
		throw BetRequestError(req, verifiedBets.Message, err);
	}

	gameInst->LastVerifiedBets = Bets;

	// only allow betting in two cases:
	// bets are open
	// wheel is in error state and bets are empty (clear bets)
	// again don-t use BetsClosedBehavior!
	if (!(state.AreBetsOpen({}) || (verifiedBets.TotalBet == 0 && state.Phase >= LAST_REGULAR_GAME_STATE_ID)))
		throw BetRequestError(req, "Cannot bet at this time! Bets are not open.", FBetErrorInfo("BetsClosed"));

	response = Bets.EntireBetAsJSON(gameInst->CreditMultiplier);
	return verifiedBets.TotalBet;
}

void TRouletteHost::GetGameDetails(GameInformation& info) const
{
	TGameHost::GetGameDetails(info);

	const uint32_t numbers = bConfiguredAsDoubleZero ? 38 : 37;

	info.RTP.BaseGame = 100. * (36. / numbers);
	info.Volatility = bConfiguredAsDoubleZero ? 5.7626 : 5.8378;    // max volatilities (bet on single number)

	auto stakes = GetStakesForGame(info);
	if (IsOnline())
	{
		info.RTP.BaseGame = 100. * (36. / (bConfiguredAsDoubleZero ? 38 : 37));
		for (const auto& stake : stakes)
		{
			if (info.MaxBet == 0 || stake->PlayboardLimitMax == 0 || stake->PlayboardLimitMax > info.MaxBet)
				info.MaxBet = stake->PlayboardLimitMax;

			if (info.MinBet == 0 || stake->PlayboardLimitMin < info.MinBet)
				info.MinBet = stake->PlayboardLimitMin;
		}

		if (!info.MinBet)
			info.MinBet = 1;
	}

	info.MaxWin = 36;

	const ::roulette::EPlayboardMode mode =
	  ::roulette::EPlayboardMode::_from_string_nothrow(info.GetConfig("playboard-mode").get<std::string>().c_str(), ::roulette::EPlayboardMode::Normal);
	const EThunderVariant thunderType = EThunderVariant::_from_string(info.GetConfig("thunder.type").get<std::string>().c_str());
	if (const ThunderRNGInstance* rng = GetThunderRNG(info, thunderType))
	{
		info.MaxWin = rng->RNG->GetEffectiveMultiplierRange().second;
		info.RTP.BaseGame = rng->RNG->GetRTPLimits();
	}

	if (mode == ::roulette::EPlayboardMode::LesFiguresPlus)
	{
		if (stakes.empty())
			stakes.push_back({});

		FRTPRange toutVaRange;
		const int coveredNumbers = 4;
		for (const auto& stake : stakes)
		{
			const uint32_t toutVaMultiplier = mBets[bConfiguredAsDoubleZero].getMultiplier(stake, ::roulette::EBetStyle(::roulette::EBetStyle::ToutVa));
			info.MaxWin = std::max<uint32_t>(toutVaMultiplier, info.MaxWin);
			toutVaRange.Expand((100.0 * (coveredNumbers * coveredNumbers) * toutVaMultiplier) / (numbers * numbers));
		}

		info.RTP.Variants["les-figures-tout-va"] = toutVaRange;
	}
	else if (mode == ::roulette::EPlayboardMode::LeFinali)
	{
		static const std::set<::roulette::EBetStyle> toutVaTypes = { ::roulette::EBetStyle::LeFinaliToutVa3, ::roulette::EBetStyle::LeFinaliToutVa4,
			                                                         ::roulette::EBetStyle::LeFinaliToutVa5 };
		if (stakes.empty())
			stakes.push_back({});

		FRTPRange toutVaRange;
		for (const auto& stake : stakes)
		{
			for (const auto& toutVaType : toutVaTypes)
			{
				if ((toutVaType == ::roulette::EBetStyle::LeFinaliToutVa5) && !bConfiguredAsDoubleZero)
					continue;    // skip this field if we are not in double-zero mode
				const int coveredNumbers = 3 + (toutVaType - ::roulette::EBetStyle::LeFinaliToutVa3);
				const uint32_t toutVaMultiplier = mBets[bConfiguredAsDoubleZero].getMultiplier(stake, toutVaType);
				info.MaxWin = std::max<uint32_t>(toutVaMultiplier, info.MaxWin);
				toutVaRange.Expand((100.0 * (coveredNumbers * coveredNumbers) * toutVaMultiplier) / (numbers * numbers));
			}
		}

		info.RTP.Variants["le-finali-tout-va"] = toutVaRange;
	}
}

const JsonSchema& TRouletteHost::GetBetSchema() const
{
	return BetSchema;
}

void TRouletteHost::BetPlaced(YGameClient& client, const FBetPlacedResult& betResult, const json& response)
{
	// leave previous possible bet on the table, undo this one
	RouletteGame& game = dynamic_cast<RouletteGame&>(*client.Game());

	if (!betResult.Error)
	{
		game.ConfirmedBets = game.LastVerifiedBets;
		game.LastBetTime = ytime::GetTimeMsec();

		// raise flag on master if bets are placed
		if (betResult.TotalAmount)
		{
			if (dynamic_cast<Player*>(&client))
			{
				bHasBetOnBoard = true;
				if (RNGType == roulette::ERNGType::imaxaGaming)
				{
					rmclientdrv::SetBetCreditStatus(Roulette, true, true);
				}
			}
		}
	}

	game.LastVerifiedBets.Clear();
}

json TRouletteHost::GetVideoStreamInfo() const
{
	json streamConf = GetConfig("stream");
	if (EStreamType::_from_string(streamConf["type"].get<std::string>().c_str()) == EStreamType::imaxaPlayer)
	{
		if ((!streamConf.contains("source") || streamConf["source"].is_null()) && !mVideoURL.empty())
		{
			json impliedSource(json::value_t::object);
			impliedSource["url"] = mVideoURL;
			impliedSource["id"] = "Meep";
			streamConf["source"] = std::move(impliedSource);
		}
	}
	return streamConf;
}

json TRouletteHost::OnRegister(const YServerClient& client)
{
	json initResult = TBaseRouletteHost::OnRegister(client);

	const YGameClient* gameClient = dynamic_cast<const YGameClient*>(&client);
	if (gameClient)
	{
		const RouletteGame& game = dynamic_cast<const RouletteGame&>(*gameClient->Game());
		const Player* player = dynamic_cast<const Player*>(gameClient);

		if (player && LuckyJackpotEnabled() && game.IsLuckyNumberJackpotEnabled())
		{
			if (std::shared_ptr<LuckyNumberJackpot> jpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(player->Jackpot()))
				initResult["jackpot"]["luckyHistory"] = jpModule->GetHistoryJSON();

			Container()->Server->CurrencyAPI->Update(player->Provider() ? player->Provider()->Currency() : Container()->Server->PlayForFunCurrency()._to_string());
		}
		if (player && FireballDeluxeJackpotEnabled() && game.IsFireballDeluxeJackpotEnabled())
		{
			if (std::shared_ptr<FireballDeluxeJackpot> jpModule = std::dynamic_pointer_cast<FireballDeluxeJackpot>(player->Jackpot()))
				initResult["jackpot"]["combinedMultipliers"] = jpModule->GetJackpotCombinedMultipliers();

			Container()->Server->CurrencyAPI->Update(player->Provider() ? player->Provider()->Currency() : Container()->Server->PlayForFunCurrency()._to_string());
		}

		initResult["config"] = GetGameConfig(game);

		if (auto thunderRng = GetThunderRNG(game.Info(), game.Thunder))
		{
			initResult["thunderHistory"] = thunderRng->GetHistoryJSON();
		}
	}
	else
	{
		StaticGameInformation gameInfo;
		EThunderVariant thunderVariant = EThunderVariant::None;

		// check if we have a viewer client with game information
		if (const ViewerInstance* viewerClient = dynamic_cast<const ViewerInstance*>(&client))
		{
			if (const auto gameKey = viewerClient->GetGameKey(); !gameKey.empty())
			{
				gameInfo = GetGame(gameKey);
				if (gameInfo.Type != HostType::Null)
					thunderVariant = EThunderVariant::_from_string(gameInfo.GetConfig("thunder.type").get<std::string>().c_str());
			}
		}

		if (auto thunderRng = GetThunderRNG(gameInfo, thunderVariant))
		{
			initResult["thunderHistory"] = thunderRng->GetHistoryJSON();
			// thunder public info added here only for the viewer client. Players get this through game config API
			initResult["thunder"] = thunderRng->GetPublicInfo();
		}
	}

	{
		SharedScopedLock lock(mHistory);
		initResult["historyNumbers"] = mHistoryJSON;
	}

	initResult["stream"] = GetVideoStreamInfo();

	return initResult;
}

bool TRouletteHost::DisconnectService()
{
	const int prevState = mIsDoubleZero.exchange(-1);
	if (Roulette)
	{
		rmclientdrv::Destroy(Roulette);
		Roulette = NULL;
		bLastCommunicationState = false;
		mAdditionalVersions.clear();
	}

	if (mAbbiatiClient)
		mAbbiatiClient->Stop();

	TLiveGameHost::DisconnectService();

	return prevState >= 0;
}

void TRouletteHost::RefreshHistory()
{
	if (RNGType != roulette::ERNGType::imaxaGaming)
		return;

	ScopedLock lock(mHistory);
	mHistoryJSON = json(json::value_t::array);

	if (rmclientdrv::WinNumberHistory(Roulette, &mHistory) != 0)
		return;

	// index 0 is the oldest, index size-1 is the newest win number
	for (const auto& history : &mHistory)
	{
		json historyRound(json::value_t::array);
		historyRound.push_back(history.first);
		historyRound.push_back(history.second);
		mHistoryJSON.push_back(std::move(historyRound));
	}
	json historyCopy(mHistoryJSON);
	lock.unlock();

	TriggerEvent(*HistoryChangedEvent, historyCopy);
	bRequireHistoryUpdate = false;
}

void TRouletteHost::Poll(bool bShuttingDown)
{
	if (bShuttingDown && !NumPlayers())
		return;

	if (!IsOnline())
		return;

	const uint64_t now = ytime::GetTimeMsec();
	uint64_t currentAbbiatiGameID = 0;
	if (RNGType == roulette::ERNGType::imaxaGaming)
	{
		const bool bConnectionOK = rmclientdrv::IsServerCommunicationOK(Roulette);
		const bool bChanged = bConnectionOK != bLastCommunicationState;
		bLastCommunicationState = bConnectionOK;

		if (bChanged && !bConnectionOK)
		{
			ServiceDisconnected("Connection lost with roulette master");
			return;
		}

		rmclientdrv::TGameStatus status;
		rmclientdrv::GameStatus(Roulette, status);

		NewState.Phase = status.GamePhase;
		if (ForcedErrorState)
			NewState.Phase = ForcedErrorState;
		NewState.SubPhase = status.GamePhaseSubId;
		NewState.RouletteGameID = status.GameID;
		NewState.TimeLeftMs = std::max(0, status.TimeLeftMs);
		NewState.WinningNumber = status.WinNumber;

		if (status.TimeLeftPercent && NewState.TimeLeftMs > 0)
			NewState.TotalTimeMs = (int)(NewState.TimeLeftMs * 1e2f / status.TimeLeftPercent);
	}
	else    // Abbiati
	{
		const bool bWasInGameOrInError = NewState.Phase >= WAITING_WIN_NUMBER_STATE;

		{
			ScopedLock lock(NewAbbiatiState);
			NewState.Phase = NewAbbiatiState->Phase;
			if (ForcedErrorState)
				NewState.Phase = ForcedErrorState;
			NewState.WinningNumber = NewAbbiatiState->WinningNumber;
			NewState.Timestamp = NewAbbiatiState->Timestamp;
			NewAbbiatiState->Timestamp = now;
			NewState.BallSpeed = NewAbbiatiState->BallSpeed;
			currentAbbiatiGameID = NewAbbiatiState->RouletteGameID;

			if (std::exchange(bAbbiatiNeedsFirstUpdate, false))
				mAbbiatiGamePhase = NewState.Phase;
		}

		if (NewState.Phase < BALL_OUT_STATE && bWasInGameOrInError)    // every time abbiati opens bets, we start a new game!
		{
			mAbbiatiActiveGameID = currentAbbiatiGameID;
			mAbbiatiSimulatedGameID++;
			SetParam("abbiati-game-id", std::to_string(mAbbiatiSimulatedGameID));
		}
		else if (!mAbbiatiActiveGameID)
		{
			mAbbiatiActiveGameID = currentAbbiatiGameID;
		}

		NewState.RouletteGameID = mAbbiatiSimulatedGameID;
		NewState.TotalTimeMs = mAbbiatiRoundTime;

		if (mAbbiatiActiveGameID != currentAbbiatiGameID)
			VoidLiveGame("Wheel reported unexpected win numbers");

		if (NewState.Phase > LAST_REGULAR_GAME_STATE_ID)    // if in error, always show it!
			mAbbiatiGamePhase = NewState.Phase;

		if (mAbbiatiGamePhase == GAME_INIT_STATE || mAbbiatiGamePhase == GAME_PAUSED_STATE)    // waiting for game start phase
		{
			if (NewState.Phase == PREPARING_BALL_STATE)
			{
				bAbbiatiHasBallSpeed = false;
				NewState.TimeLeftMs = mAbbiatiRoundTime;
				mAbbiatiGamePhase = NewState.Phase;
			}
		}
		else if (mAbbiatiGamePhase > LAST_REGULAR_GAME_STATE_ID)    // if in an error state or waiting for new game
		{
			// any starting state will get us out of an error state
			if (NewState.Phase <= PREPARING_BALL_STATE)
			{
				bAbbiatiHasBallSpeed = false;
				NewState.TimeLeftMs = NewState.AreBetsOpen({}) ? mAbbiatiRoundTime : 0;
				mAbbiatiGamePhase = NewState.Phase;
			}
		}
		else if (mAbbiatiGamePhase == READING_WIN_NUMBER_STATE)    // if waiting for new game
		{
			// only a new game gets us out of reading win number
			if (CurrentState->RouletteGameID && CurrentState->RouletteGameID != NewState.RouletteGameID && NewState.Phase <= PREPARING_BALL_STATE)
			{
				bAbbiatiHasBallSpeed = false;
				NewState.TimeLeftMs = NewState.AreBetsOpen({}) ? mAbbiatiRoundTime : 0;
				mAbbiatiGamePhase = NewState.Phase;
			}
		}
		else if (NewState.Phase > mAbbiatiGamePhase)    // only if state advances from previous state
		{
			mAbbiatiGamePhase = NewState.Phase;

			// void game if necessary
			if (mAbbiatiGamePhase > LAST_REGULAR_GAME_STATE_ID)
			{
				if (CurrentState->Phase > PREPARING_BALL_STATE && CurrentState->Phase < CLOSING_GAME_STATE)
					VoidLiveGame(rmclientdrv::DescribePhase(mAbbiatiGamePhase));
			}
			else
			{
				// ball out state skipped! error!
				if (CurrentState->Phase < BALL_OUT_STATE && mAbbiatiGamePhase >= WAITING_WIN_NUMBER_STATE)
				{
					mAbbiatiGamePhase = ERROR_BALL_ON_WHEEL_AND_CIRCUMFERENCE;
					VoidLiveGame("Ball out state did not occur!");
				}
				else
				{
					if (mAbbiatiGamePhase == PREPARING_BALL_STATE)
					{
						NewState.TimeLeftMs = mAbbiatiRoundTime;
						bAbbiatiHasBallSpeed = false;
					}
					else if (mAbbiatiGamePhase == BALL_OUT_STATE)
					{
						if (mAbbiatiMaxTimeAfterBallThrow)
						{
							NewState.TimeLeftMs = mLastBallOutTime;
						}
						else
						{
							mAbbiatiGamePhase = WAITING_WIN_NUMBER_STATE;
						}
					}
				}
			}
		}
		else if (NewState.Phase <= PREPARING_BALL_STATE)
		{
			if (mAbbiatiGamePhase > PREPARING_BALL_STATE && mAbbiatiGamePhase < READING_WIN_NUMBER_STATE)
			{
				// this happens if we somehow miss the win number result. Game will get voided this way at the end of this function under
				// "Did not get win number in game xy"
			}
		}

		NewState.Phase = mAbbiatiGamePhase;

		if (NewState.AreBetsOpen({}))
		{
			const bool bIsBallOut = mAbbiatiGamePhase == BALL_OUT_STATE;
			NewState.TimeLeftMs = std::max<int>(NewState.TimeLeftMs - int(int64_t(now) - NewState.Timestamp), bIsBallOut ? 0 : mLastBallOutTime);

			if (bIsBallOut && mAbbiatiMaxTimeAfterBallThrow && !bAbbiatiHasBallSpeed && NewState.BallSpeed > (int)mAbbiatiBetsClosedBallSpeed)
			{
				bAbbiatiHasBallSpeed = true;
				const int estimate = std::clamp<int>(std::roundf((NewState.BallSpeed - (int)mAbbiatiBetsClosedBallSpeed) * 1e3 / mAbbiatiLinDrag), 0,
				                                     mAbbiatiMaxTimeAfterBallThrow - (mLastBallOutTime - NewState.TimeLeftMs));
				UpdateValue<int>(mLastBallOutTime, estimate, "ballOutTime");
				mLastBallOutTime = estimate;
				NewState.TimeLeftMs = mLastBallOutTime;
			}

			if (!NewState.TimeLeftMs && bIsBallOut)
				NewState.Phase = mAbbiatiGamePhase = WAITING_WIN_NUMBER_STATE;
		}
		else
		{
			NewState.TimeLeftMs = 0;
		}

		if (CurrentState->Phase != NewState.Phase)
			Log(Normal, "Phase: %d", NewState.Phase);
	}

	NewState.Timestamp = now;

	ScopedLock lock(CurrentState);
	const RNGState PrevState = &CurrentState;

	if (PrevState.TotalTimeMs && PrevState.RouletteGameID == NewState.RouletteGameID)
		NewState.TotalTimeMs = PrevState.TotalTimeMs;

	if (PrevState.RouletteGameID != NewState.RouletteGameID)
		bCurrentRoundVoid = false;

	if (NewState.Phase < READING_WIN_NUMBER_STATE || NewState.Phase > CLOSING_GAME_STATE || bCurrentRoundVoid)
		NewState.WinningNumber = -1;

	std::map<const roulette::ThunderRNGInstance*, FThunderRNGChange> thunderChanges;
	if (NewState.RouletteGameID != PrevState.RouletteGameID)
		DoThunderLogic(thunderChanges, roulette::EThunderRNGEvent::ResetRound);

	const bool bWereBetsOpenBefore = PrevState.AreBetsOpen({});
	const bool bAreBetsOpenNow = NewState.AreBetsOpen({});
	if (bAreBetsOpenNow && !bWereBetsOpenBefore)
		DoThunderLogic(thunderChanges, roulette::EThunderRNGEvent::BetsOpen);

	const bool bWereBetsClosedBefore = PrevState.AreBetsClosedAndNotError({});
	const bool bAreBetsClosedNow = NewState.AreBetsClosedAndNotError({});
	if (bAreBetsClosedNow && !bWereBetsClosedBefore && PrevState.RouletteGameID == NewState.RouletteGameID)
	{
		if (NewState.WinningNumber < 0)
			mBetsClosedTime = ytime::GetTimeMsec();
		DoThunderLogic(thunderChanges, roulette::EThunderRNGEvent::BetsClosed);
	}
	else if (!bAreBetsClosedNow)
	{
		mBetsClosedTime = 0;
	}

	&CurrentState = NewState;
	lock.unlock();

	// Check if a new game was entered to refresh win number history
	if (PrevState.RouletteGameID != NewState.RouletteGameID)
	{
		if (NewState.RouletteGameID && bRequireHistoryUpdate)
			RefreshHistory();

		std::string newRound;
		if (NewState.RouletteGameID)
			newRound = std::to_string(NewState.RouletteGameID);

		ForEachPlayer([&newRound](yserver::Player& player) {
			ScopedLock lock(player);
			if (player.Game()->IsGameroundActive() && player.Game()->BetStatus() < EGameBetState::PendingResult)
			{
				std::optional<FGameroundError> err;
				if (player.Game()->BetStatus() != EGameBetState::Idle)
					err = FGameroundError({ "NewRoundStarted", "A new game round was started!" });
				player.ProcessGameResult({}, err);
			}

			if (!newRound.empty() && !player.Game()->IsGameroundActive())
				player.GameRoundBegin(newRound);
		});
	}

	mPlayers.LockShared();
	uint64_t accountForGameValue = mGameToAccountFor;
	mPlayers.UnlockShared();

	if (NewState.Phase != PrevState.Phase)
	{
		if (RNGType == roulette::ERNGType::imaxaGaming && NewState.Phase == BALL_OUT_STATE)
		{
			UpdateValue<int>(mLastBallOutTime, NewState.TimeLeftMs, "ballOutTime");
			mLastBallOutTime = NewState.TimeLeftMs;

			if (bImaxaEnableRTPControl)
			{
				std::vector<std::array<int64_t, 38>> win_values;
				{
					SharedScopedLock playersLock(mPlayers);
					win_values.reserve(mPlayers->size());
					for (const auto& player : &mPlayers)
					{
						ScopedLock playerLock(*player.second);

						double exchangeRate = 1.;
						if (!Container()->Server->CurrencyAPI->GetExchangeRate(RouletteCurrency, player.second->Account()->Currency(), exchangeRate))
						{
							Log(Warning, "Could not find currency exchange rate for %s, will fall back to 1:1!", player.second->Account()->Currency().c_str());
						}

						const RouletteGame* game = static_cast<RouletteGame*>(player.second->Game().get());
						std::array<int64_t, 38> wins;
						for (uint32_t i = 0; i < 38U; i++)
						{
							const ThunderRNGInstance* ThunderRNG = GetThunderRNG(game->Info(), game->Thunder);
							const auto winOnThisNumber = game->CalculateWins(mBets[bConfiguredAsDoubleZero], i, ThunderRNG);
							wins[i] = (int64_t)std::round(player.second->Account()->Denomination() * (winOnThisNumber.TotalWon * 100) / exchangeRate);
						}
						win_values.emplace_back(std::move(wins));
					}
				}

				rmclientdrv::RegisterMultipleWinValues(Roulette, win_values);
			}
		}

		// bets opened
		if (bAreBetsOpenNow && !bWereBetsOpenBefore)
		{
			bHasBetOnBoard = false;
			if (RNGType == roulette::ERNGType::imaxaGaming)
			{
				// Log(Debug, "Setting credit on table flag because I have players with money!");
				rmclientdrv::SetBetCreditStatus(Roulette, false, GetNumPlayersWithMoney());
			}

			Log(Info, "Bets opened for new game %ld", NewState.RouletteGameID);
		}

		// bets closed
		if (bAreBetsClosedNow && !bWereBetsClosedBefore)
		{
			if (PrevState.RouletteGameID == NewState.RouletteGameID)
			{
				OnBetsClosed();
			}
			else if (NewState.RouletteGameID && PrevState.RouletteGameID && accountForGameValue)
			{
				VoidLiveGame("Roulette host did not provide a result for this game!");
				accountForGameValue = 0;
			}
		}

		bool bErrorChangeOccurred = false;
		if (NewState.Phase > LAST_REGULAR_GAME_STATE_ID && PrevState.Phase < LAST_REGULAR_GAME_STATE_ID)
		{
			bErrorChangeOccurred = true;
			// went into error state
			if (accountForGameValue)
			{
				Log(Critical, "Voiding game %lu due to error phase %d: %s!", accountForGameValue, NewState.Phase, rmclientdrv::DescribePhase(NewState.Phase).c_str());
				VoidLiveGame("Encountered error in game!");
				accountForGameValue = 0;
			}
			else
			{
				Log(Error, "Entered error phase %d: %s!", NewState.Phase, rmclientdrv::DescribePhase(NewState.Phase).c_str());
			}
		}
		else if (NewState.Phase < LAST_REGULAR_GAME_STATE_ID && PrevState.Phase > LAST_REGULAR_GAME_STATE_ID)
		{
			bErrorChangeOccurred = true;
			// came out of error state
			Log(Important, "Exiting error phase %d into phase %d and game %lu: %s!", PrevState.Phase, NewState.Phase, NewState.RouletteGameID,
			    rmclientdrv::DescribePhase(NewState.Phase).c_str());
		}
		else if (NewState.Phase > LAST_REGULAR_GAME_STATE_ID)
		{
			bErrorChangeOccurred = true;
			Log(Error, "Error phase changed to %d: %s!", NewState.Phase, rmclientdrv::DescribePhase(NewState.Phase).c_str());
		}

		if (bErrorChangeOccurred)
		{
			if (NewState.Phase > LAST_REGULAR_GAME_STATE_ID)
			{
				// is in error
				RaiseRuntimeError("rng-status", GetRuntimeErrorForPhase(NewState.Phase));
			}
			else
			{
				// went out of error
				ClearRuntimeError("rng-status");
			}
		}
	}

	UpdateValue<int>(PrevState.GetPhase(mBetsClosedBehavior), NewState.GetPhase(mBetsClosedBehavior), "phase");
	UpdateValue<int>(PrevState.GetSubPhase(mBetsClosedBehavior), NewState.GetSubPhase(mBetsClosedBehavior), "phaseSubID");
	UpdateValue<uint64_t>(PrevState.RouletteGameID, NewState.RouletteGameID, "gameID");
	const int newTimeLeft = NewState.GetTimeLeftMs(mBetsClosedBehavior);
	const int64_t diff =
	  int64_t(mLastTimeLeftSentValue - newTimeLeft) - (int64_t(NewState.Timestamp) - mLastTimeLeftSent);    // RPC state time elapsed - actual time elapsed
	const bool bShouldPostTimeLeftUpdate = (newTimeLeft == 0 || std::abs(diff) >= 350) && mLastTimeLeftSentValue != newTimeLeft;
	if (bShouldPostTimeLeftUpdate)
	{
		mLastTimeLeftSentValue = newTimeLeft;
		mLastTimeLeftSent = NewState.Timestamp;
		// Log(Normal, "Sending timeLeftMs=%d because diff is %d", NewState.TimeLeftMs, diff);
		UpdateValue<int>(PrevState.GetTimeLeftMs(mBetsClosedBehavior), newTimeLeft, "timeLeft");
	}
	UpdateValue<int>(PrevState.GetTotalTimeMs(mBetsClosedBehavior), NewState.GetTotalTimeMs(mBetsClosedBehavior), "totalTime");
	UpdateValue<int>(PrevState.WinningNumber, NewState.WinningNumber, "winNumber");

	if (!thunderChanges.empty())
	{
		for (const auto& [gameKey, thunderRNG] : GetGame2ThunderRNG())
		{
			if (!thunderRNG)
				continue;
			auto find = thunderChanges.find(thunderRNG);
			if (find == thunderChanges.end())
				continue;

			json phaseChangeData;
			if (find->second.bHasChanged)
			{
				phaseChangeData["name"] = "thunderNumbers";
				phaseChangeData["old"] = thunderRNG->LastThunderNumbers;
				phaseChangeData["new"] = thunderRNG->NewThunderNumbers;
				BroadcastEventToViewers(yprotocol::Event(*GameStateChangedEvent, phaseChangeData), gameKey);
			}

			const auto boardMultiplier = thunderRNG->RNG->CurrentRound.GetBaseMultiplier();
			if (thunderRNG->LastBoardMultiplier != boardMultiplier)
			{
				phaseChangeData["name"] = "boardMultiplier";
				phaseChangeData["old"] = thunderRNG->LastBoardMultiplier;
				phaseChangeData["new"] = boardMultiplier;
				BroadcastEventToViewers(yprotocol::Event(*GameStateChangedEvent, phaseChangeData), gameKey);
			}
		}

		SharedScopedLock stateLock(CurrentState);
		ForEachPlayer([this, &thunderChanges](yserver::Player& player) {
			ScopedLock lock(player);
			roulette::RouletteGame* rouletteGame = dynamic_cast<roulette::RouletteGame*>(player.Game().get());
			const roulette::ThunderRNGInstance* rng = GetThunderRNG(rouletteGame->Info(), rouletteGame->Thunder);
			if (!rng)
				return;

			auto find = thunderChanges.find(rng);
			if (find == thunderChanges.end())
				return;

			for (const json& rand : find->second.GeneratedRandoms) rouletteGame->PushRandNumber(rand);

			json phaseChangeData;
			if (find->second.bHasChanged)
			{
				phaseChangeData["name"] = "thunderNumbers";
				phaseChangeData["old"] = rng->LastThunderNumbers;
				phaseChangeData["new"] = rng->NewThunderNumbers;
				player.TriggerEvent(*GameStateChangedEvent, phaseChangeData);

				rouletteGame->ThunderNumbers = rng->NewThunderNumbers;
			}

			const auto boardMultiplier = rng->RNG->CurrentRound.GetBaseMultiplier(rouletteGame->IsEnabledFireballFee());
			if (rng->LastBoardMultiplier != boardMultiplier)
			{
				phaseChangeData["name"] = "boardMultiplier";
				phaseChangeData["old"] = rng->LastBoardMultiplier;
				phaseChangeData["new"] = boardMultiplier;
				player.TriggerEvent(*GameStateChangedEvent, phaseChangeData);

				rouletteGame->BoardMultiplier = boardMultiplier;
			}
		});
	}

	if (accountForGameValue)
	{
		// Somehow, we have not accounted for a game - do that now
		if (NewState.RouletteGameID == 0)
		{
			Log(Critical, "Did not get win number in game %lu! master is not in any game right now. Voiding game!", accountForGameValue);
			VoidLiveGame("No results available from the roulette source");
		}
		else if (accountForGameValue < NewState.RouletteGameID)
		{
			Log(Critical, "Did not get win number in game %lu! master is in %lu. Voiding game!", accountForGameValue, NewState.RouletteGameID);
			VoidLiveGame("Game accounting phase was not handled properly");
		}
		else if (accountForGameValue > NewState.RouletteGameID)    // TILT condition!
		{
			Log(Critical, "Was expecting a win number for game %lu - ahead of master, which is in %lu. Voiding game!", accountForGameValue, NewState.RouletteGameID);
			VoidLiveGame("Game state has been changed to a different game!");
		}
		else
		{    // mGameToAccountFor is the current game (normal)
			if (NewState.Phase >= READING_WIN_NUMBER_STATE && NewState.Phase < LAST_REGULAR_GAME_STATE_ID && NewState.WinningNumber >= 0)
			{
				OnWinNumber(NewState.WinningNumber);
			}
		}
	}
}

void TRouletteHost::OnAddGameToHost(const StaticGameInformation& info)
{
	TBaseRouletteHost::OnAddGameToHost(info);

	const bool bAllowLuckynumber = info.GetConfigOptional("allow-lucky-number-jp", false).get<bool>();
	const bool bAllowFireballDeluxe = info.GetConfigOptional("allow-fireball-jp", false).get<bool>();

	if (bAllowLuckynumber && bAllowFireballDeluxe)
		throw HostError("game-conf", std::format("Both lucky number and fireball deluxe jackpots cannot be enabled at the same time on the same game {}", info.Key));

	// if domain is empty then this is being called before domain is set (e.g. during host init) so we can skip as jp modules will be added at
	// TRouletteHost::ConnectToService
	if (!Domain().empty() && LuckyJackpotEnabled() && bAllowLuckynumber)
	{
		ScopedLock lock(mLuckyNumberJackpotModules);
		for (const auto& provider : ListOnlineProviders())
			RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::LuckyNumber, &mLuckyNumberJackpotModules, mLuckyNumberConfig, info.Key, provider);
		RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::LuckyNumber, &mLuckyNumberJackpotModules, mLuckyNumberConfig, info.Key, nullptr);
	}

	if (!Domain().empty() && FireballDeluxeJackpotEnabled() && bAllowFireballDeluxe && PerGameThunderRNGs.contains(info.Key))
	{
		ScopedLock lock(mFireballDeluxeJackpotModules);
		for (const auto& provider : ListOnlineProviders())
			RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::FireballDeluxe, &mFireballDeluxeJackpotModules, mFireballDeluxeConfig, info.Key, provider);
		RegisterJackpotModules_AssumeWriteLock(ERouletteJackpotType::FireballDeluxe, &mFireballDeluxeJackpotModules, mFireballDeluxeConfig, info.Key, nullptr);
	}
}

GameHostGameInformation TRouletteHost::RemoveGameLaunchConfig(const std::string& game)
{
	GameHostGameInformation removedGame = TBaseRouletteHost::RemoveGameLaunchConfig(game);

	if (LuckyJackpotEnabled())
	{
		ScopedLock lock(mLuckyNumberJackpotModules);
		UnregisterJackpotModules_AssumeWriteLock(&mLuckyNumberJackpotModules, nullptr, game);
	}

	if (FireballDeluxeJackpotEnabled())
	{
		ScopedLock lock(mFireballDeluxeJackpotModules);
		UnregisterJackpotModules_AssumeWriteLock(&mFireballDeluxeJackpotModules, nullptr, game);
	}

	return removedGame;
}

json TRouletteHost::GetGameConfig(const roulette::BaseRouletteGame& game) const
{
	json conf = TBaseRouletteHost::GetGameConfig(game);

	conf["needsCroupier"] = RequiresCroupier();

	return conf;
}

void TRouletteHost::OnGameRoundBegin(YGameClient& player)
{
	TGameHost::OnGameRoundBegin(player);

	RouletteGame* gamePtr = dynamic_cast<RouletteGame*>(player.Game().get());
	gamePtr->BoardMultiplier = 36;
	gamePtr->ThunderNumbers.clear();
	gamePtr->FireballBonusMultiplier.reset();
}

void TRouletteHost::OnGameRoundEnd(YGameClient& player, const GameRoundSnapshot& game)
{
	TGameHost::OnGameRoundEnd(player, game);

	RouletteGame* gamePtr = dynamic_cast<RouletteGame*>(player.Game().get());
	gamePtr->BoardMultiplier = 36;
	gamePtr->ThunderNumbers.clear();
	gamePtr->FireballBonusMultiplier.reset();
	gamePtr->Unregister(game.VoidReason.has_value() || !gamePtr->WonResult.HasAnyWinsAffectingNextRound());
}

void TRouletteHost::OnRoundFailureResolved(Player& player) noexcept
{
	if (player.Game()->IsGameroundActive())
		return;

	const RNGState state = CurrentState.getCopy();

	bool bShouldOpenRound = state.AreBetsOpen({});
	if (!bShouldOpenRound)
	{
		// we should still open the round if the game is waiting for a result
		SharedScopedLock lock(mPlayers);
		bShouldOpenRound = (mGameToAccountFor && mGameToAccountFor == state.RouletteGameID);
	}

	if (bShouldOpenRound)
		player.GameRoundBegin(std::to_string(state.RouletteGameID));
}

void TRouletteHost::VoidGame(YGameClient& client, EVoidReason reason, const std::string& message)
{
	client.Log(Warning, "Voiding game for %s: %s", reason._to_string(), message.c_str());

	if (Player* player = dynamic_cast<Player*>(&client))
		UnregisterPlayer(*player, FGameroundError({ reason._to_string(), message }));
}

void TRouletteHost::OnPlayerAdded(Player& player, bool bReconnected) noexcept
{
	TGameHost::OnPlayerAdded(player, bReconnected);

	auto game = dynamic_cast<RouletteGame*>(player.Game().get());

	if (!bReconnected)
	{
		if (RNGType == roulette::ERNGType::imaxaGaming && !player.Account()->GetRawBalance().IsZero())
		{
			Log(Debug, "Setting credit on table flag because a new player has arrived!");
			rmclientdrv::SetBetCreditStatus(Roulette, bHasBetOnBoard, true);
		}
	}
	else
	{
		if (game->BetTimeout)
		{
			game->BetTimeout->cancel();
			game->BetTimeout.reset();
		}
	}

	OnRoundFailureResolved(player);    // to start gameround

	// If a stake is already selected, then we're ok!
	if (game->StakeInfo.ID != -1 && game->StakeInfo.Stake)
		return;

	std::vector<yprotocol::ChoiceQuestion::Choice> choices;
	std::vector<uint32_t> choiceStakeIDs;
	auto stakes = GetStakesForGame(game->Info());
	for (size_t i = 0; i < stakes.size(); i++)
	{
		if (stakes[i] && !game->StakeRules.IsValid(i))
			continue;
		yprotocol::ChoiceQuestion::Choice thisStake;
		thisStake.Title = "Stake " + std::to_string(i);
		thisStake.Data = ::roulette::TRouletteBets::stakeAsJson(*stakes[i], game->StakeRules.Multiplier);
		choices.push_back(std::move(thisStake));
		choiceStakeIDs.push_back(i);
	}

	if (choiceStakeIDs.empty())
	{
		game->SetLock(player, ERouletteLockReasons::NoStakesAvailable, true);
		return;
	}

	// If we only have one stake, or we aren't forcing a stake select, go to default stake
	if (choiceStakeIDs.size() == 1 || !bForceStakeSelect)
	{
		const uint32_t stakeID = (game->StakeRules.Default >= stakes.size()) ? 0U : game->StakeRules.Default;
		SetStake(*game, stakeID);
		json stakeChangeData(json::value_t::object);
		stakeChangeData["id"] = stakeID;
		stakeChangeData["stake"] = ::roulette::TRouletteBets::stakeAsJson(*game->StakeInfo.Stake, game->StakeRules.Multiplier);
		player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
	}
	else
	{
		game->SetLock(player, ERouletteLockReasons::StakeSelect, true);
		auto question = std::make_shared<yprotocol::ChoiceQuestion>("stake", "Select a stake", choices);
		player.Ask(question,
		           [this, choiceStakeIDs](yprotocol::YProtocolClient& client, const yprotocol::QuestionPtr& q, const json& answer,
		                                  json& finalResponse) -> yprotocol::EQuestionResult {
			           Player& player = dynamic_cast<Player&>(client);
			           auto game = dynamic_cast<RouletteGame*>(player.Game().get());
			           if (answer.size())
			           {
				           size_t stakeID = answer[0].get<uint32_t>();
				           stakeID = choiceStakeIDs[std::min(choiceStakeIDs.size() - 1, stakeID)];
				           SetStake(*game, stakeID);
				           game->SetLock(player, ERouletteLockReasons::StakeSelect, false);
				           json stakeChangeData(json::value_t::object);
				           stakeChangeData["id"] = stakeID;
				           stakeChangeData["stake"] = ::roulette::TRouletteBets::stakeAsJson(*game->StakeInfo.Stake, game->StakeInfo.Multiplier);
				           player.TriggerEvent(*StakeChangedEvent, stakeChangeData);
				           finalResponse = stakeChangeData;
				           return yprotocol::EQuestionResult::OK;
			           }
			           else
			           {
				           return yprotocol::EQuestionResult::ASK_AGAIN;
			           }
		           });
	}
}

void TRouletteHost::OnPlayerGone(Player& player) noexcept
{
	TGameHost::OnPlayerGone(player);

	// UnregisterPlayer(player, "Player left");
}

void TRouletteHost::TryRoundRestore(YGameClient& client, const GameRoundSnapshot& snap)
{
	TGameHost::TryRoundRestore(client, snap);

	RouletteGame& game = (RouletteGame&)*client.Game();

	if (game.bIsDoubleZero != mBets[bConfiguredAsDoubleZero].bCreatedForDoubleZero)
		throw yprotocol::InternalError("Game and host double zero does not match");

	try
	{
		game.ConfirmedBets = ::roulette::BetAmounts::FromJSON(snap.ExtraData, game.CreditMultiplier, mBets[bConfiguredAsDoubleZero].getPresets());
	}
	catch (const BetParseError& e)
	{
		throw yprotocol::InternalError(e.what());
	}

	if (game.ConfirmedBets.Empty())
		throw yprotocol::InternalError("There are no placed bets to restore!");

	if (snap.ExtraData.contains("stake"))
		SetStake(game, snap.ExtraData["stake"].get<int>());
}

void TRouletteHost::TryRoundResume(Player& player, const GameRoundSnapshot& snap)
{
	TGameHost::TryRoundResume(player, snap);

	if (!player.Game()->IsGameroundActive())
		return;

	RouletteGame& game = (RouletteGame&)*player.Game();

	if (game.WinNumber)    // if the win number is known, then just the accounting didn't go through, so do nothing
		return;

	if (!game.StakeInfo.Stake || (snap.ExtraData.contains("stake") && game.StakeInfo.ID != snap.ExtraData["stake"].get<int>()))
		throw yprotocol::InternalError("The stake used in the game is not available anymore");

	const RNGState latestState = CurrentState.getCopy();
	const std::string latestGameID = std::to_string(latestState.RouletteGameID);
	// if not the current gameround or we already have the win number of the current gameround
	if (game.GameRound() != latestGameID || latestState.Phase >= READING_WIN_NUMBER_STATE)
	{
		int winningNumber = -1;
		std::optional<::roulette::FThunderRoundHistory> thunderRound;
		const ThunderRNGInstance* thunderRNG = GetThunderRNG(game.Info(), game.Thunder);
		if (game.GameRound() == latestGameID)
		{
			winningNumber = latestState.WinningNumber;
		}
		else
		{
			const int64_t GameroundID = yutils::strToInt(game.GameRound(), 0);
			{
				SharedScopedLock lock(mHistory);
				auto find = mHistory->find(GameroundID);
				if (find == mHistory->end())
					throw yprotocol::InternalError("No information exists in history for gameround " + game.GameRound());
				winningNumber = find->second;
			}

			if (thunderRNG)
			{
				thunderRound = thunderRNG->GetHistory(GameroundID);
				if (!thunderRound)
					throw yprotocol::InternalError("No information exists in thunder round history for gameround " + game.GameRound());
			}
		}

		if (game.BetStatus() == EGameBetState::BetPlaced)
		{
			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}

		game.PushRandNumber(json(static_cast<int64_t>(winningNumber)));
		game.WinNumber = winningNumber;
		game.WonResult = game.CalculateWins(mBets[bConfiguredAsDoubleZero], winningNumber, thunderRNG, thunderRound);

		if (winningNumber > -1)
			player.ProcessGameResult(game.WonResult.TotalWon);
		else
			player.ProcessGameResult(game.WonResult.TotalWon, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), "No win number read" }));
	}
	else
	{
		if (RNGType == roulette::ERNGType::imaxaGaming)
			rmclientdrv::SetBetCreditStatus(Roulette, true, true);

		SharedScopedLock lock(mPlayers);
		const bool bWaitingForWinNumber = mGameToAccountFor;
		lock.unlock();

		// if bBetsClosed matches game.bHasAccounted bets, then do nothing, game will be finished after OnRegister happens and after the win number is read
		const bool bHasAccountedBets = game.BetStatus() >= EGameBetState::PendingResult;
		if (bWaitingForWinNumber != bHasAccountedBets)
		{
			if (bHasAccountedBets)
				throw yprotocol::InternalError("Unfinished game bets were accounted but server has not accounted bets for this game yet!");

			const CommitBetsResult res = player.CommitBets();
			if (!res.Success())
				throw yprotocol::InternalError("Could not perform bet: " + res.Message);
		}
	}
}

void TRouletteHost::UnregisterPlayer(Player& player, const std::optional<FGameroundError>& err)
{
	if (player.Game()->IsGameroundActive())
		player.ProcessGameResult({}, err);
	player.TryToEndAccountingRound();
}

template <class T>
struct ptr_hash : public std::__hash_base<size_t, std::shared_ptr<T>>
{
	size_t operator()(const std::shared_ptr<T>& ptr) const noexcept { return reinterpret_cast<size_t>(ptr.get()); }
};

void TRouletteHost::OnWinNumber(int winNumber)
{
#ifdef YSERVER_ENABLE_RESULT_ADJUSTMENT
	if (ForcedWinNumber)
	{
		winNumber = *ForcedWinNumber;
		ForcedWinNumber.reset();
	}
#endif

	struct WinnableLevelInfo
	{
		JackpotLevelInfoBase Info = {};
		std::list<std::string> ValidPlayers = {};
		RouletteJackpot::LevelWinInfo Win = {};
	};
	std::unordered_multimap<std::shared_ptr<RouletteJackpot>, WinnableLevelInfo> WinnableLuckyLevels;
	if (LuckyJackpotEnabled())
	{
		SharedScopedLock lock(mLuckyNumberJackpotModules);
		for (const auto& module : &mLuckyNumberJackpotModules | std::views::values)
		{
			if (const auto luckyNumberJpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(module))
			{
				const auto winnableLevelInfos = luckyNumberJpModule->NotifyExternalTrigger(winNumber);
				luckyNumberJpModule->AddHistory(LuckyNumberJackpotHistory(mGameToAccountFor, luckyNumberJpModule->GetLuckyNumbers()));

				for (const auto& winnableLevelInfo : winnableLevelInfos)
					WinnableLuckyLevels.emplace(luckyNumberJpModule, WinnableLevelInfo({ .Info = winnableLevelInfo }));
			}
		}
	}
	if (FireballDeluxeJackpotEnabled())
	{
		SharedScopedLock lock(mFireballDeluxeJackpotModules);
		for (const auto& module : &mFireballDeluxeJackpotModules | std::views::values)
		{
			const auto winnableLevelInfos = module->NotifyExternalTrigger(winNumber);
			for (const auto& winnableLevelInfo : winnableLevelInfos) WinnableLuckyLevels.emplace(module, WinnableLevelInfo({ .Info = winnableLevelInfo }));
		}
	}

	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	AddNumberToHistory(accountForGame, winNumber);

	const size_t numPlayersTotal = players.size();
	const std::string accountForGameRound = std::to_string(accountForGame);

	for (auto it = players.begin(); it != players.end();)
	{
		yutils::ScopedAction skipPlayer([&it, &players]() { it = players.erase(it); });
		ScopedLock lock(*it->second);
		RouletteGame* playerGame = static_cast<RouletteGame*>(it->second->Game().get());

		if (playerGame->BetStatus() == EGameBetState::Idle || accountForGameRound != playerGame->GameRound() || it->second->Game()->IsVoid())    // not registered!
			continue;

		it->second->Log(Debug, "Processing roulette win...");
		if (playerGame->BetStatus() != EGameBetState::PendingResult)
		{
			it->second->SetPersistence(EPlayerPersistenceMode::Game, false);
			it->second->Log(Warning, "Game %lu was not started correctly. This should not happen and indicates a problem somewhere.", accountForGame);
			UnregisterPlayer(*it->second, FGameroundError({ "GameStartFailed", "Game not started correctly" }));
			continue;
		}

		skipPlayer.reset();

		playerGame->PushRandNumber(json(static_cast<int64_t>((winNumber))));
		playerGame->WinNumber = winNumber;
		const ThunderRNGInstance* ThunderRNG = GetThunderRNG(playerGame->Info(), playerGame->Thunder);
		playerGame->WonResult = playerGame->CalculateWins(mBets[bConfiguredAsDoubleZero], winNumber, ThunderRNG);

		if (const auto& jpState = playerGame->JackpotState())
		{
			// Lucky number jackpot - check if player is eligible
			for (auto find = WinnableLuckyLevels.find(std::dynamic_pointer_cast<LuckyNumberJackpot>(jpState->JackpotModule));
			     find != WinnableLuckyLevels.end() && find->first == jpState->JackpotModule; ++find)
			{
				const double betOnStraightUp = mBets[bConfiguredAsDoubleZero].amountOnPlainNumber(playerGame->ConfirmedBets, winNumber) *
				                               it->second->Account()->Denomination() * jpState->ExchangeRate;
				const double minWinnableBet = find->second.Info.MinWinnableBet > 0 ? find->second.Info.MinWinnableBet : find->second.Info.MinBet;
				if (betOnStraightUp > 0.0 && betOnStraightUp >= minWinnableBet)
					find->second.ValidPlayers.push_back(it->first);
			}

			// Fireball deluxe jackpot - check if player is eligible:
			//  - if the player has a fireball bonus number and jackpot level and it matches the win number
			//  - if the player's sum of straight and split bets is greater or equal the min winnable bet
			for (auto find = WinnableLuckyLevels.find(std::dynamic_pointer_cast<FireballDeluxeJackpot>(jpState->JackpotModule));
			     find != WinnableLuckyLevels.end() && find->first == jpState->JackpotModule; ++find)
			{
				if (!playerGame->FireballBonusMultiplier || playerGame->FireballBonusMultiplier->JackpotLevelId != find->second.Info.ID ||
				    playerGame->FireballBonusMultiplier->Number != winNumber)
					continue;

				const double straightAndSplitBetAmounts =
				  mBets[bConfiguredAsDoubleZero].amountOnBetStyles(playerGame->ConfirmedBets, { ::roulette::EBetStyle::Plain, ::roulette::EBetStyle::Caval }) *
				  it->second->Account()->Denomination() * jpState->ExchangeRate;
				const double minWinnableBet = find->second.Info.MinWinnableBet > 0 ? find->second.Info.MinWinnableBet : find->second.Info.MinBet;
				if (straightAndSplitBetAmounts > 0.0 && straightAndSplitBetAmounts >= minWinnableBet)
					find->second.ValidPlayers.push_back(it->first);
			}
		}
		++it;
	}

	Log(Info, "Got win number %d in game %lu.%s", winNumber, accountForGame,
	    numPlayersTotal > 0 ? std::format(" Processing game result for {}/{} players...", players.size(), numPlayersTotal).c_str() : std::string().c_str());

	for (auto& [jpModule, levelInfo] : WinnableLuckyLevels)
	{
		levelInfo.Win = jpModule->ProcessPotentialWinners(levelInfo.ValidPlayers);
		if (levelInfo.Win.Winners.empty())
			continue;

		Log(Important, "Jackpot level %s won by %lu players for a total of %.02f %s", levelInfo.Info.ID.c_str(), levelInfo.Win.Winners.size(), levelInfo.Win.TotalWon,
		    jpModule->Currency().c_str());
	}

	const std::string gameroundMessage = "The winning number is " + std::to_string(winNumber);
	std::atomic<double> wonTotalCents = 0.0;
	tbb::concurrent_unordered_map<AccountID, std::pair<double, double>, AccountIDHash> winEuroCents;
	ParallelFor(players.begin(), players.end(), [&](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);

		RouletteGame* playerGame = static_cast<RouletteGame*>(player.second->Game().get());

		const uint64_t WonCredits = playerGame->WonResult.TotalWon;
		if (winNumber > -1)
			player.second->ProcessGameResult(WonCredits);
		else
			player.second->ProcessGameResult(WonCredits, FGameroundError({ EVoidReason(EVoidReason::NoGameResult)._to_string(), gameroundMessage }));

		if (WonCredits && !player.second->Account()->IsDemo())
		{
			double rate;
			if (Container()->Server->CurrencyAPI->GetExchangeRate(ECurrency(ECurrency::EUR)._to_string(), player.second->Account()->Currency(), rate))
			{
				const double wonEuroCents = WonCredits * 1e2 * player.second->Account()->Denomination() / rate;
				wonTotalCents += wonEuroCents;
				auto emplaced = winEuroCents.emplace(player.second->Account()->ID(), std::make_pair(wonEuroCents, rate));
				if (!emplaced.second)
					emplaced.first->second.first += wonEuroCents;
			}
		}
		Log(Debug, "Processing win done for player %s.", player.second->Account()->ID().Username.c_str());
	});

	SharedScopedLock lock(mLuckyNumberJackpotModules);
	for (const auto& [id, module] : &mLuckyNumberJackpotModules) module->ResetRound();
	lock.unlock();

	SharedScopedLock lock2(mFireballDeluxeJackpotModules);
	for (const auto& [id, module] : &mFireballDeluxeJackpotModules) module->ResetRound();
	lock2.unlock();

	for (const auto& [jpModule, info] : WinnableLuckyLevels)
	{
		if (info.Win.Winners.empty())
			continue;

		json playerList(json::value_t::array);
		for (const auto& winner : info.Win.Winners) playerList.push_back(winner.PlayerRef);

		ForEachPlayer(
		  [&](Player& player) {
			  for (const auto& winner : info.Win.Winners)
				  if (winner.PlayerRef == player.Account()->ID().AsString())
					  return;

			  if (const auto& jpState = player.Game()->JackpotState(); jpState && jpState->ExchangeRate > 0)
			  {
				  json jpWonCtx(json::value_t::object);
				  jpWonCtx["level"] = info.Info.ID;
				  jpWonCtx["total"] = info.Win.TotalWon / jpState->ExchangeRate;
				  jpWonCtx["winners"] = playerList;
				  player.TriggerEvent(*JackpotWasWonEvent, jpWonCtx);
			  }
		  },
		  std::dynamic_pointer_cast<TGameHost>(shared_from_this()), jpModule->GetProvider());

		// this can be an issue because we are sending to all viewers on host, not limited to a provider and game
		json jpWonViewerCtx(json::value_t::object);
		jpWonViewerCtx["level"] = info.Info.ID;
		jpWonViewerCtx["total"] = info.Win.TotalWon;    // this is in level currency because we do not know viewer's currency
		jpWonViewerCtx["winners"] = playerList;
		BroadcastEventToViewers(yprotocol::Event(*JackpotWasWonEvent, jpWonViewerCtx));
	}

	std::vector<std::pair<std::string, uint64_t>> winList;
	winList.reserve(winEuroCents.size());
	for (const auto& pair : winEuroCents) { winList.push_back({ pair.first.AsString(), (uint64_t)std::round(pair.second.first) }); }
	std::sort(std::execution::par, winList.begin(), winList.end(), [](const auto& l, const auto& r) -> bool { return l.second > r.second; });
	json winReportList(json::value_t::object);
	size_t n = 0;
	for (auto it = winList.begin(); it != winList.end() && n < mPlayersPerPage; it++)
	{
		winReportList[it->first] = it->second;
		n++;
	}

	json winReport(json::value_t::object);
	winReport["totalWon"] = wonTotalCents.load();
	winReport["registeredPlayers"] = players.size();
	winReport["winList"] = winReportList;
	TriggerEvent(*WinReportEvent, winReport);
}

void TRouletteHost::OnBetsClosed()
{
	uint64_t accountForGame = 0;
	{
		SharedScopedLock lock(CurrentState);
		accountForGame = CurrentState->RouletteGameID;
	}

	YPlayerList playersToIterate;
	// Unregister players who's accounting didn't go through
	{
		ScopedLock lock(mPlayers);
		PlayersThatVotedStart.clear();
		if (mGameToAccountFor)    // if we are already waiting for a result, don't collect any new bets!
		{
			if (mGameToAccountFor == accountForGame)
				Log(Warning, "Re-entered OnBetsClosed for game %ld!", accountForGame);
			else
				Log(Warning, "Entered OnBetsClosed for game %ld without the previous round (%ld) getting a result!", accountForGame, mGameToAccountFor);
		}
		playersToIterate = &mPlayers;
		mGameToAccountFor = accountForGame;
	}

	std::atomic<size_t> numBets = 0;
	ParallelFor(
	  playersToIterate.begin(), playersToIterate.end(), [this, &numBets, gameIDStr = std::to_string(accountForGame)](const std::pair<std::string, PlayerPtr>& player) {
		  CommitBetsResult result;
		  ScopedLock lock(*player.second);
		  auto game = std::dynamic_pointer_cast<RouletteGame>(player.second->Game());
		  if (game->GameRound() != gameIDStr)
			  return;

		  if (game->BetStatus() == EGameBetState::BetPlaced)
		  {
			  result = player.second->CommitBets();
		  }
		  else
		  {
			  result.Message = "No bets were placed";
		  }

		  if (game->BetTimeout)
		  {
			  game->BetTimeout->cancel();
			  game->BetTimeout.reset();
		  }

		  // if paytable is not standard and thunder RNG 2 is enabled, then we need to select bonus prize number and multiplier for the player
		  if (game->GetThunderPaytableType() != ::roulette::EThunderPaytableType::Standard && game->Thunder == EThunderVariant::Thunder2)
		  {
			  if (const auto thunderRNG = GetThunderRNG(game->Info(), game->Thunder); thunderRNG && thunderRNG->RNG)
			  {
				  if (const auto rng = dynamic_cast<::roulette::ThunderRNG2*>(thunderRNG->RNG.get()))
				  {
					  try
					  {
						  // First, select the bonus number and multiplier
						  game->FireballBonusMultiplier = rng->Config.GenNewRoundBonusNumberMultiplier(game->GetFireballFee());

						  // Then select the jackpot level based on the player's bet amount
						  if (const auto jpModule = std::dynamic_pointer_cast<FireballDeluxeJackpot>(player.second->Jackpot()))
						  {
							  if (const auto& jpState = game->JackpotState(false))
							  {
								  const double betAmount = mBets[bConfiguredAsDoubleZero].amountOnBetStyles(
								                             game->ConfirmedBets, { ::roulette::EBetStyle::Plain, ::roulette::EBetStyle::Caval }) *
								                           player.second->Account()->Denomination() * jpState->ExchangeRate;
								  game->FireballBonusMultiplier->JackpotLevelId = jpModule->SelectPlayedJackpotLevelId(
								    player.second->UniqueIdentifier(), *game->FireballBonusMultiplier, betAmount, bConfiguredAsDoubleZero ? 38 : 37);
							  }
						  }

						  player.second->Log(Debug, "Selected fireball bonus number %d with multiplier %d (and jackpot: %s)", game->FireballBonusMultiplier->Number,
						                     game->FireballBonusMultiplier->Multiplier,
						                     game->FireballBonusMultiplier->JackpotLevelId ? game->FireballBonusMultiplier->JackpotLevelId.value().c_str() : "/");
					  }
					  catch (const std::exception& e)
					  {
						  game->FireballBonusMultiplier = ::roulette::NumberMultiplier();
						  player.second->Log(Error, "Exception selecting bonus multiplier in round: %s ", e.what());
					  }

					  json fireballBonusCtx;
					  fireballBonusCtx["name"] = "fireballBonus";
					  fireballBonusCtx["old"] = json(json::value_t::object);
					  fireballBonusCtx["new"] = game->FireballBonusMultiplier->ToJSON();
					  player.second->TriggerEvent(*GameStateChangedEvent, fireballBonusCtx);
				  }
			  }
		  }

		  if (result.Success())
		  {
			  numBets++;
		  }
		  else
		  {
			  if (game->BetStatus() != EGameBetState::Idle)
				  UnregisterPlayer(*player.second, FGameroundError({ EVoidReason(EVoidReason::BetFailed)._to_string(), result.Message }));
			  else
				  UnregisterPlayer(*player.second, {});
		  }
	  });

	if (LuckyJackpotEnabled())
	{
		json luckyNumbersJson(json::value_t::array);
		SharedScopedLock lock(mLuckyNumberJackpotModules);
		for (const auto& module : &mLuckyNumberJackpotModules | std::views::values)
		{
			if (const auto luckyNumberJpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(module))
			{
				luckyNumberJpModule->SendOutPendingBets(accountForGame);

				json luckyNumberJson(json::value_t::object);
				if (const auto& provider = luckyNumberJpModule->GetProvider())
					luckyNumberJson["provider"] = provider->UniqueIdentifier();
				luckyNumberJson["demo"] = luckyNumberJpModule->IsDemo();
				luckyNumberJson["luckyNumbers"] = luckyNumberJpModule->GetLuckyNumbers();
				luckyNumbersJson.push_back(std::move(luckyNumberJson));
			}
		}

		// send out the new lucky numbers to the viewers
		// temporarily, we send all lucky numbers to all viewers, but add provider and demo flag to each lucky number object so that viewer can decide what to show
		// in the future, when we will have information on which provider/demo is being viewed, we can send only the relevant lucky numbers
		json luckyChangeDataViewers;
		luckyChangeDataViewers["name"] = "jackpot.luckyLevelsViewer";
		luckyChangeDataViewers["old"] = json(json::value_t::object);
		luckyChangeDataViewers["new"] = std::move(luckyNumbersJson);
		BroadcastEventToViewers(yprotocol::Event(*GameStateChangedEvent, std::move(luckyChangeDataViewers)));

		ForEachPlayer([this](Player& player) {
			if (const auto jpModule = player.Jackpot())
			{
				if (const auto luckyJpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(jpModule))
				{
					if (const auto newLuckyLevels = luckyJpModule->GetLuckyNumbers(); !newLuckyLevels.empty())
					{
						json luckyChangeData;
						luckyChangeData["name"] = "jackpot.luckyLevels";
						luckyChangeData["old"] = json(json::value_t::object);
						luckyChangeData["new"] = newLuckyLevels;
						player.TriggerEvent(*GameStateChangedEvent, luckyChangeData);
					}
				}
			}
		});
	}

	if (FireballDeluxeJackpotEnabled())
	{
		SharedScopedLock lock(mFireballDeluxeJackpotModules);
		for (const auto& module : &mFireballDeluxeJackpotModules | std::views::values) module->SendOutPendingBets(accountForGame);
	}

	Log(Info, "Bets closed in game %lu with %lu bets", accountForGame, numBets.load());
}

void TRouletteHost::VoidLiveGame(const std::string& reason)
{
	bRequireHistoryUpdate = true;
	mAbbiatiActiveGameID = 0;
	ForcedWinNumber.reset();
	uint64_t accountForGame = 0;
	std::map<std::string, PlayerPtr> players;
	{
		ScopedLock lock(mPlayers);
		PlayersThatVotedStart.clear();
		players = &mPlayers;
		std::swap(accountForGame, mGameToAccountFor);
	}

	if (accountForGame)
	{
		{
			ScopedLock lock(CurrentState);
			bCurrentRoundVoid = (accountForGame == CurrentState->RouletteGameID);
		}

		Log(Warning, "No result for game %lu (%s). Will void any bets made by the existing %lu players.", accountForGame, reason.c_str(), players.size());
	}
	else
	{
		{
			SharedScopedLock lock(CurrentState);
			accountForGame = CurrentState->RouletteGameID;
		}

		if (accountForGame)
			Log(Warning, "Not in a game currently, but voiding it anyway (%s) to clear any placed bets made by the existing %lu players.", reason.c_str(),
			    players.size());
	}

	if (!accountForGame)
		return;

	// jackpot void/loss in case of a game void
	if (LuckyJackpotEnabled())
	{
		SharedScopedLock lock(mLuckyNumberJackpotModules);
		for (const auto& module : &mLuckyNumberJackpotModules | std::views::values)
			if (const auto luckyNumberJpModule = std::dynamic_pointer_cast<LuckyNumberJackpot>(module))
			{
				luckyNumberJpModule->NotifyExternalTrigger(-1, true);
				luckyNumberJpModule->AddHistory(LuckyNumberJackpotHistory(mGameToAccountFor, luckyNumberJpModule->GetLuckyNumbers()));
			}
	}

	if (FireballDeluxeJackpotEnabled())
	{
		SharedScopedLock lock(mFireballDeluxeJackpotModules);
		for (const auto& module : &mFireballDeluxeJackpotModules | std::views::values) module->NotifyExternalTrigger(-1, true);
	}

	ParallelFor(players.begin(), players.end(), [this, gid = std::to_string(accountForGame), &reason](const std::pair<std::string, PlayerPtr>& player) {
		ScopedLock lock(*player.second);
		if (gid == player.second->Game()->GameRound())
			VoidGame(*player.second, EVoidReason::NoGameResult, reason);
	});
}

const ThunderRNGInstance* TRouletteHost::GetThunderRNG(const StaticGameInformation& game, const std::optional<roulette::EThunderVariant>& variant) const
{
	if (variant.has_value() && *variant == EThunderVariant::None)
		return {};

	auto rng = TBaseRouletteHost::GetThunderRNG(game, variant);
	if (rng)
		return rng;

	switch (mThunderMode)
	{
		case EThunderMode::LiveOnly: rng = LiveThunder.get(); break;
		case EThunderMode::LiveWithLocalFallback: rng = LiveThunder ? LiveThunder.get() : LocalThunderRNG.get(); break;
		case EThunderMode::LocalOnly: rng = LocalThunderRNG.get(); break;
		default: break;
	}

	if (rng && variant.value_or(rng->Variant) == rng->Variant)
		return rng;

	return NULL;
}

std::map<std::string, const ThunderRNGInstance*> TRouletteHost::GetGame2ThunderRNG() const
{
	std::map<std::string, const ThunderRNGInstance*> game2ThunderRNG;

	for (const auto& [gameKey, gameInfo] : LaunchInfo().Games)
	{
		const auto thunderVariant = EThunderVariant::_from_string(gameInfo.GetConfig("thunder.type").get<std::string>().c_str());
		if (const auto* thunder = GetThunderRNG(gameInfo, thunderVariant))
			game2ThunderRNG[gameKey] = thunder;
	}

	return game2ThunderRNG;
}

void TRouletteHost::DoThunderLogic(std::map<const roulette::ThunderRNGInstance*, FThunderRNGChange>& changedRNGs, roulette::EThunderRNGEvent event)
{
	if (bEnablePerGameThunder)
	{
		for (auto& thunder : PerGameThunderRNGs) thunder.second->HandleEvent(*this, NewState.RouletteGameID, event, changedRNGs[thunder.second.get()]);
	}

	if (LocalThunderRNG)
		LocalThunderRNG->HandleEvent(*this, NewState.RouletteGameID, event, changedRNGs[LocalThunderRNG.get()]);

	if (LiveThunder)
		LiveThunder->HandleEvent(*this, NewState.RouletteGameID, event, changedRNGs[LiveThunder.get()]);
}

int TRouletteHost::GetNumPlayersWithMoney() const
{
	int numPlayers = 0;
	ForEachPlayer([&numPlayers](const Player& player) {
		if (!player.Account()->GetRawBalance().IsZero())
			numPlayers++;
	});
	return numPlayers;
}

bool TRouletteHost::IsDoubleZero(const StaticGameInformation& info) const
{
	return bConfiguredAsDoubleZero;
}


enum class EActionToResolveRouletteError : uint8_t
{
	None,
	GameContinue,
	MultipleActions
};
struct RouletteErrorInfo
{
	EModuleErrorFrequency Frequency;
	ELangMessages ActionMsg;
	EActionToResolveRouletteError ResolveAction = EActionToResolveRouletteError::GameContinue;
};
const std::map<int, RouletteErrorInfo> ROULETTE_ERRORS = {
	{ ERROR_UNKNOWN_DB_ERROR_STATE, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_DBERROR, EActionToResolveRouletteError::None } },
	{ ERROR_CAN_NOT_FIND_BALL_STATE, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_CANT_FIND_BALL, EActionToResolveRouletteError::MultipleActions } },
	{ ERROR_WIN_NUMBER_KNOWN_WHILE_BO_STATE, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_WIN_NUM_KNOWN_ON_BALL_OUT } },
	{ ERROR_CAN_NOT_READ_WIN_NUMBER_STATE, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_CANT_READ_WIN_NUM } },
	{ ERROR_SAME_WIN_NUMBER_READ_TOO_MANY_TIMES, { EModuleErrorFrequency::ExtremelyUnlikely, ROULETTE_ERROR_ACTION_DESC_SAME_WIN_NUM_TOO_MANY_TIMES } },
	{ ERROR_WIN_NUMBER_CHECK_FAILED, { EModuleErrorFrequency::ExtremelyUnlikely, ROULETTE_ERROR_ACTION_DESC_WIN_NUMBER_CHECK_FAILED } },
	{ ERROR_SOFTWARE_HAS_EXPIRED, { EModuleErrorFrequency::Routine, ROULETTE_ERROR_ACTION_DESC_SOFTWARE_EXPIRED, EActionToResolveRouletteError::None } },
	{ CB_COMMUNICATION_ERROR_STATE,
	  { EModuleErrorFrequency::ExtremelyUnlikely, ROULETTE_ERROR_ACTION_DESC_CB_COMMUNICATION_ERROR, EActionToResolveRouletteError::None } },
	{ ERROR_CB_CONFIG_CRC_ERROR, { EModuleErrorFrequency::ExtremelyUnlikely, ROULETTE_ERROR_ACTION_DESC_CB_CONFIG_CRC_ERROR, EActionToResolveRouletteError::None } },
	{ ERROR_BALL_SPEED_ERROR, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_BALL_SPEED_ERROR } },
	{ DOOR_OPEN_STATE, { EModuleErrorFrequency::Routine, ROULETTE_ERROR_ACTION_DESC_DOOR_OPEN } },
	{ ERROR_BALL_OUT_TIMEOUT, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_BALL_OUT_TIMEOUT } },
	{ ERROR_BALL_LAPS_COUNT, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_BALL_LAPS_COUNT } },
	{ ERROR_BALL_IN_EJECT_AFTER_BLOW, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_BALL_IN_EJECT_AFTER_BLOW } },
	{ ERROR_BALL_IN_CYLINDER_AFTER_RAISE, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_BALL_IN_CYLINDER_AFTER_RAISE } },
	{ ERROR_BALL_ON_WHEEL_AND_CIRCUMFERENCE, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_BALL_ON_WHEEL_AND_CIRCUMFERENCE } },
	{ ERROR_TWO_BALLS_ON_WHEEL, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_TWO_BALLS_ON_WHEEL } },
	{ ERROR_BALL_LOST_ERROR, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_BALL_LOST } },
	{ ERROR_WHEEL_SPEED, { EModuleErrorFrequency::Uncommon, ROULETTE_ERROR_ACTION_DESC_WHEEL_SPEED } },
	{ ERROR_ROULETTE_WAS_IN_ERROR_BEFORE_POWER_DOWN, { EModuleErrorFrequency::Rare, ROULETTE_ERROR_ACTION_DESC_ROULETTE_IN_ERROR_ON_POWER_DOWN } },
	{ ERROR_MAIN_POWER_FAILURE_WHILE_IN_GAME, { EModuleErrorFrequency::Routine, ROULETTE_ERROR_ACTION_DESC_POWER_FAILUER_WHILE_IN_GAME } }
};

ModuleRuntimeError TRouletteHost::GetRuntimeErrorForPhase(int phase)
{
	auto found = ROULETTE_ERRORS.find(phase);
	ModuleRuntimeError ret;
	if (found == ROULETTE_ERRORS.end())
	{
		ret.Frequency = EModuleErrorFrequency::ExtremelyUnlikely;
		ret.Error = LocalizedMessage(ROULETTE_ERROR_UNKNOWN);
		ret.ActionDescription = LocalizedMessage(ROULETTE_ERROR_ACTION_UNKNOWN);
	}
	else
	{
		ret.Frequency = found->second.Frequency;
		ret.Error = LocalizedMessage(GAME_PAUSED_STRING + phase);
		ret.ActionDescription = LocalizedMessage(found->second.ActionMsg);
		if (found->second.ResolveAction == EActionToResolveRouletteError::GameContinue)
			ret.RequiredAdminActionID = "Continue Game";
		else if (found->second.ResolveAction == EActionToResolveRouletteError::MultipleActions)
			ret.RequiredAdminActionID = std::string();
	}
	return ret;
}

std::map<std::string, std::string> TRouletteHost::Versions() const
{
	std::map<std::string, std::string> vers = TGameHost::Versions();
	vers.insert(mAdditionalVersions.begin(), mAdditionalVersions.end());
	return vers;
}

::roulette::TRouletteBets TRouletteHost::GetRouletteBets() const
{
	return mBets[bConfiguredAsDoubleZero];
}
