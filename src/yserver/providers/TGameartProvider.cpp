#include "providers/TGameartProvider.h"

#include "Cryptography.h"
#include "YServer.h"

using namespace yserver::provider;
DEFINE_LOG_CATEGORY(LogHG, "hypergaming")

enum EGameartErrorCodes
{
	SESSION_EXPIRED = 104
};

const JsonSchema HG4ProviderSchema =
  JsonSchema({ { "client.timeout", JsonSchema(json::value_t::number_unsigned, "Timeout for HTTP requests on HG4", 3000U) },
               { "client.endpoint", JsonSchema(json::value_t::string, "Address of HG4").Flag(yserver::ModuleRestartRequiredSettingFlag) },
               { "client.method", JsonSchema(json::value_t::string, "The default HTTP method to use when doing requests", "GET").SetPossibleVals({ "GET", "PUT" }) },
               { "salt", JsonSchema(json::value_t::string, "The salt to use for signing requests (must match HG4 salt)") },
               { "client.auth.username", JsonSchema(json::value_t::string, "The username given to YServer by HG4 to allow access") },
               { "client.auth.password", JsonSchema(json::value_t::string, "The password given to YServer by HG4 to allow access") } });

gameart::HGAccount::HGAccount(const GameClientCreationContext& ctx, const std::string& SID, const std::string& provider_uid, const std::string& username,
                              const std::string& nickname, const std::string& locale, const std::string& currency, double denomination) :
    YPlayerAccount(ctx, SID, AccountID(provider_uid, username), nickname, locale, currency, denomination, false)
{
}

const JsonSchema ContainsBalanceSchema = JsonSchema({ { "platform", JsonSchema({ { "balance", JsonSchema(json::value_t::number_unsigned, "Balance in credits") } }) } });
const JsonSchema ContainsEventsSchema = JsonSchema({ { "events", JsonSchema(json::value_t::array, "Array of triggered events") } });
const JsonSchema ConfigSchema = JsonSchema(
  { { "events",
      JsonSchema(json::value_t::array, "Array of triggered events")
        .SetChildSchema(
          JsonSchema({ { "event", JsonSchema(json::value_t::string) }, { "context", JsonSchema() }, { "resume", JsonSchema(json::value_t::boolean, {}, false) } }), 1) },
    { "platform", JsonSchema({ { "balance", JsonSchema(json::value_t::number_unsigned, "Balance in credits") } }) } });

json gameart::HGAccount::GetAccountingRoundData() const
{
	json val(json::value_t::object);
	val["hgRound"] = HGGameround;
	return val;
}

std::string TGameartProvider::BuildAdminAPIRequestURI(const std::string& action, const web::QueryString& q) const
{
	web::QueryString query(q);
	query["args[1][usr]"] = mClientAuthID;
	query["args[1][passw]"] = mClientAuthPassword;
	query["action"] = action;

	return "/api/admin.js?" + query.ToString();
}

yserver::CreditArray TGameartProvider::Balance(const YPlayerAccount& player)
{
	CreditArray res;
	res[CREDIT_CASHABLE] = SingleBalance(CREDIT_CASHABLE, player);
	return res;
}

int64_t TGameartProvider::SingleBalance(CreditType type, const YPlayerAccount& player) const
{
	if (type != CreditType::CREDIT_CASHABLE)
		throw ProviderError("GetBalance", "Unsupported credit type (" + std::to_string(type) + ")");


	const json result = GetJSON("get-balance", GetRawEngineResponse(player, { { "config", json() } }, false), ContainsBalanceSchema);

	return result["platform"]["balance"].get<int64_t>();
}

json TGameartProvider::GetJSON(const std::string& scopeName, const web::http::response& response, const JsonSchema& validate) const
{
	if (response.get_status_code() != web::http::status::ok)
	{
		Log(Warning, "HG4 responded to %s with bad status code %d: %s!", scopeName.c_str(), (int)response.get_status_code(), response.get_status_msg().c_str());
		throw ProviderError(scopeName,
		                    "HG4 replied with bad status " + std::to_string((int)response.get_status_code()) + ". Server might be configured incorrectly or offline.");
	}

	std::string body;
	try
	{
		body = response.get_body();
	}
	catch (...)
	{
		Log(Warning, "HG4 response to %s could not be read right (content_type: %s)!", scopeName.c_str(), response.get_header(web::http::field::content_type).c_str());
		throw ProviderError(scopeName, "Could not extract HTTP body!");
	}

	json result;
	try
	{
		result = json::parse(body);
	}
	catch (const std::exception& e)
	{
		Log(Warning, "HG4 response to %s could not be parsed to json!", scopeName.c_str(), response.get_header(web::http::field::content_type).c_str());
		Log(Debug, "Malformed response: '%s' could not be parsed to json: %s!", body.c_str(), e.what());
		throw ProviderError(scopeName, "HG4 response is not a valid JSON!");
	}

	// check for errors
	if (result.is_object() && result.contains("error") && result["error"].is_string() && result.contains("errorCode") && result["errorCode"].get<int>())
	{
		if (result["errorCode"].get<int>() == EGameartErrorCodes::SESSION_EXPIRED)
		{
			Log(Warning, "HG response to %s indicated session is expired!", scopeName.c_str());
			throw SessionExpired("Session expired");
		}
		else
		{
			Log(Warning, "HG response to %s returned error %d: %s!", scopeName.c_str(), result["errorCode"].get<int>(), result["error"].get<std::string>().c_str());
			throw ProviderError(scopeName, result["error"].get<std::string>());
		}
	}

	if (validate.GetType() != json::value_t::null)
	{
		try
		{
			result = validate.GenerateConfig(result);
		}
		catch (const SchemaError& e)
		{
			Log(Warning, "HG4 response to %s could not be validated: %s", scopeName.c_str(), e.what());
			Log(Warning, "%s", JsonSchema::PrintValueInline(result).c_str());
			throw ProviderError(scopeName, "Malformed response from HG4 (" + std::string(e.what()) + ")");
		}
	}

	Log(Debug, "Response to %s: %s", scopeName.c_str(), JsonSchema::PrintValueInline(result).c_str());

	return result;
}

json MakeCritFileEntry(const std::string& filename, const std::string& fileType, const std::string& md5)
{
	json hashObj(json::value_t::object);
	hashObj["file_name"] = filename;
	hashObj["file_type"] = fileType;
	hashObj["file_hash"] = md5;
	return hashObj;
}

TGameartProvider::TGameartProvider(YModuleContainerBase* container, const std::string& name) : TCasinoProvider(container, name, ProviderType::HG4)
{
	SetLogCategory(LogHG);

	Schema() += HG4ProviderSchema;

	mQueryHandlers["GetCriticalFiles"] = [this](const imaxa_connection_ptr& con, const web::QueryString& query) {
		const uint32_t hostID = yutils::strToInt(query.Get("host"), 0);
		if (!hostID)
		{
			Reply(con, web::http::status::bad_request, "Missing 'host' query parameter");
			return;
		}

		const std::string gameKey = query.Get("game");
		if (gameKey.empty())
		{
			Reply(con, web::http::status::bad_request, "Missing 'game' query parameter");
			return;
		}

		auto host = Container()->Server->find_game_host(hostID);
		if (!host)
		{
			Reply(con, web::http::status::not_found, "Host with ID " + query.Get("host") + " not found");
			return;
		}

		const gamehost::GameHostGameInformation game(host->LaunchInfo().GetGame(gameKey));
		if (game.Type == gamehost::HostType::Null)
		{
			Reply(con, web::http::status::not_found, "Game '" + gameKey + "' on host with ID " + query.Get("host") + " not found");
			return;
		}

		json gameHashes(json::value_t::array);
		gameHashes.push_back(MakeCritFileEntry(Container()->Server->GetChecksum().first, "application/octet-stream", Container()->Server->GetChecksum().second));
		const std::string launchOption = query.Get(QUERY_PARAM_GAME_LAUNCH_OPTION_KEY);
		const auto checksums = host->Checksums(game, launchOption.empty() ? std::optional<std::string>() : launchOption);
		for (const auto& checksum : checksums) gameHashes.push_back(MakeCritFileEntry(checksum.first, "application/octet-stream", checksum.second));

		Reply(con, web::http::status::ok, gameHashes);
	};

	mQueryHandlers["GetGameConfig"] = [this](const imaxa_connection_ptr& con, const web::QueryString& query) {
		const uint32_t hostID = yutils::strToInt(query.Get("host"), 0);
		if (!hostID)
		{
			Reply(con, web::http::status::bad_request, "Missing 'host' query parameter");
			return;
		}

		const std::string gameKey = query.Get("game");
		if (gameKey.empty())
		{
			Reply(con, web::http::status::bad_request, "Missing 'game' query parameter");
			return;
		}

		auto host = Container()->Server->find_game_host(hostID);
		if (!host)
		{
			Reply(con, web::http::status::not_found, "Host with ID " + query.Get("host") + " not found");
			return;
		}

		const GameInformation game(host->GetGame(gameKey));
		if (game.Type == gamehost::HostType::Null)
		{
			Reply(con, web::http::status::not_found, "Game '" + gameKey + "' on host with ID " + query.Get("host") + " not found");
			return;
		}

		json gameInfo(json::value_t::object);
		gameInfo["name"] = game.DisplayName;
		gameInfo["rtp"] = game.RTP.GetTotalRange().Average;
		gameInfo["type"] = game.Type._to_string();
		gameInfo["maxWin"] = game.MaxWin;
		Reply(con, web::http::status::ok, gameInfo);
	};
}

struct HGEvent
{
	std::string Name;
	json Context;
};

enum EventParseResult
{
	EVENTPARSE_OK,
	EVENTPARSE_FAILED_NOT_ARRAY,
	EVENTPARSE_FAILED_BAD_EVENT_FORMAT
};

EventParseResult parseEvents(const json& eventArray, std::vector<HGEvent>& eventsOut)
{
	if (!eventArray.is_array())
		return EVENTPARSE_FAILED_NOT_ARRAY;

	int fails = 0;
	for (const json& eventObject : eventArray)
	{
		if (eventObject.is_object() && eventObject.contains("event") && eventObject["event"].is_string())
		{
			eventsOut.push_back({ eventObject["event"].get<std::string>(), eventObject.contains("context") ? eventObject["context"] : json() });
		}
		else
		{
			fails++;
		}
	}
	return fails ? EVENTPARSE_FAILED_BAD_EVENT_FORMAT : EVENTPARSE_OK;
}

yserver::TTransactionResult TGameartProvider::Bet(uint64_t bets, YGameClient& player) noexcept
{
	json betActionContext(json::value_t::object);
	betActionContext["totalBet"] = bets;
	try
	{
		auto result = GetJSON("bet", GetRawEngineResponse(*player.Account(), { { "bet", betActionContext } }), ContainsEventsSchema);

		std::vector<HGEvent> events;
		if (parseEvents(result["events"], events) != EVENTPARSE_OK)
			throw std::runtime_error("No events were found in the response");

		if (events.size() != 1 || events[0].Name != "bet")
			throw std::runtime_error("Expected a single event named 'bet'");

		if (!events[0].Context.is_object() || !events[0].Context.contains("total") || !events[0].Context["total"].is_number_unsigned())
			throw std::runtime_error("Context of 'bet' event has no 'total' member that is a uint!");

		const uint64_t betTotalFromHG = events[0].Context["total"].get<uint64_t>();
		const uint64_t prevBalance = player.Account()->GetRawBalance().Total();
		const json* balanceJson = JsonPath("platform.balance").Resolve(result);
		const uint64_t newBalance = balanceJson ? balanceJson->get<int64_t>() : (prevBalance - std::min<int64_t>(prevBalance, betTotalFromHG));
		CreditArray transacted;
		transacted[CREDIT_CASHABLE] = newBalance - prevBalance;

		TTransactionResult tr;

		const json* roundID = JsonPath("platform.gameRound.id").Resolve(result);
		if (!roundID || !roundID->is_string())
		{
			Log(Critical, "Game round not started on bet!");
			tr = TTransactionResult(ETransactionResult::AccountingError, -static_cast<int64_t>(bets), transacted, "bet");
		}
		else
		{
			gameart::HGAccount* HGplayer = dynamic_cast<gameart::HGAccount*>(player.Account().get());
			HGplayer->HGGameround = roundID->get<std::string>();
			tr = TTransactionResult(ETransactionResult::Success, -static_cast<int64_t>(bets), transacted, HGplayer->HGGameround + "-" + std::to_string(bets));
		}

		if ((newBalance + bets != prevBalance) || (betTotalFromHG != bets))
		{
			player.UpdateBalance(ETransactionSource::External, CreditArray(CREDIT_CASHABLE, newBalance));
			tr.bTriggerBalanceUpdate = false;
		}

		return tr;
	}
	catch (const SessionExpired& err)
	{
		return TTransactionResult(ETransactionResult::SessionExpired, -static_cast<int64_t>(bets), {}, "bet");
	}
	catch (const std::exception& err)
	{
		Log(Critical, "Something went wrong during bet processing: %s!", err.what());
		return TTransactionResult(ETransactionResult::AccountingError, -static_cast<int64_t>(bets), {}, "bet");
	}
	catch (...)
	{
		Log(Warning, "Unknown error in bet!");
		return TTransactionResult(ETransactionResult::FailedToSend, -static_cast<int64_t>(bets), {}, "bet");
	}
}

yserver::TTransactionResult TGameartProvider::Win(ETransactionSource source, uint64_t wins, YGameClient& player) noexcept
{
	if (source != ETransactionSource::Game)
	{
		// other sources not supported yet
		return { ETransactionResult::FailedToSend, (int64_t)wins };
	}

	json playActionContext(json::value_t::object);
	playActionContext["success"] = 1;
	json results(json::value_t::object);
	results["totalWin"] = wins;
	json rns(json::value_t::array);
	for (const json& randNum : player.Game()->GetRandomNumbers()) { rns.push_back(randNum); }

	auto account = dynamic_cast<gameart::HGAccount*>(player.Account().get());
	results["rns"] = rns;
	json data(json::value_t::object);
	data["data"] = player.Game()->GetGameRoundData();
	data["endp"] = player.Endpoint->Address;
	data["gameType"] = player.Game()->Info().Type._to_string();
	data["gid"] = player.Game()->GameRound();
	results["gameData"] = data;
	playActionContext["results"] = results;

	try
	{
		auto result = GetJSON("win", GetRawEngineResponse(*player.Account(), { { "play", playActionContext } }), ContainsEventsSchema);

		std::vector<HGEvent> events;
		if (parseEvents(result["events"], events) != EVENTPARSE_OK)
			throw std::runtime_error("No events were found in the response");

		if (events.size() != 1 || events[0].Name != "gameRoundOver")
			throw std::runtime_error("Expected a single event named 'gameRoundOver'");

		if (!events[0].Context.is_object() || !events[0].Context.contains("win") || !events[0].Context["win"].is_number_unsigned())
			throw std::runtime_error("Game round over event has no win in the context");

		const uint64_t winTotalFromHG = events[0].Context["win"].get<uint64_t>();
		const uint64_t prevBalance = player.Account()->GetRawBalance().Total();
		const json* balanceJson = JsonPath("platform.balance").Resolve(result);
		const uint64_t newBalance = balanceJson ? balanceJson->get<uint64_t>() : (prevBalance + winTotalFromHG);

		const json* roundID = JsonPath("platform.gameRound.id").Resolve(result);
		if (roundID && roundID->is_string())
		{
			if (roundID->get<std::string>() != account->HGGameround)
				Log(Warning, "Gameround ID does not match (%s != %s)!", roundID->get<std::string>().c_str(), account->HGGameround.c_str());
		}

		TTransactionResult tr { ETransactionResult::Success, static_cast<int64_t>(wins), CreditArray(CREDIT_CASHABLE, newBalance - prevBalance) };

		if ((prevBalance + wins != newBalance) || (winTotalFromHG != wins))
		{
			tr.bTriggerBalanceUpdate = false;
			player.UpdateBalance(ETransactionSource::External, CreditArray(CreditType::CREDIT_CASHABLE, newBalance));
		}

		return tr;
	}
	catch (const SessionExpired& err)
	{
		return TTransactionResult(ETransactionResult::SessionExpired, static_cast<int64_t>(wins), {}, "bet");
	}
	catch (const std::exception& err)
	{
		Log(Critical, "Something went wrong during win processing: %s!", err.what());
		return TTransactionResult(ETransactionResult::AccountingError, static_cast<int64_t>(wins), {}, "bet");
	}
	catch (...)
	{
		Log(Warning, "Unknown error in win!");
		return TTransactionResult(ETransactionResult::FailedToSend, static_cast<int64_t>(wins), {}, "bet");
	}
}

bool TGameartProvider::VoidGame(const YPlayerAccount& account, const AccountingRoundID& aid, const FGameroundError& voidReason) noexcept
{
	json playActionContext(json::value_t::object);
	playActionContext["success"] = 0;
	playActionContext["error"] = yutils::Format("Cannot finish game - return bets. %s:%s", voidReason.Code.c_str(), voidReason.Message.c_str());
	playActionContext["results"] = json();
	try
	{
		json result = GetJSON("void", GetRawEngineResponse(account, { { "play", playActionContext } }), ContainsEventsSchema);

		std::vector<HGEvent> events;
		if (parseEvents(result["events"], events) != EVENTPARSE_OK)
			throw std::runtime_error("No events were found in the response");

		if (events.size() != 1 || events[0].Name != "gameRoundOver")
			throw std::runtime_error("Expected a single event named 'gameRoundOver'");

		const json* roundID = JsonPath("platform.gameRound.id").Resolve(result);
		return roundID && roundID->is_string() && roundID->get<std::string>() == dynamic_cast<const gameart::HGAccount*>(&account)->HGGameround;
	}
	catch (const SessionExpired& err)
	{
		Log(Warning, "Cannot void game because the session is no longer valid!");
		return false;
	}
	catch (const std::exception& err)
	{
		Log(Warning, "Something went wrong during voiding the game: %s!", err.what());
		return false;
	}
	catch (...)
	{
		Log(Warning, "Unknown error in game voiding!");
		return false;
	}
}

yserver::PlayerAccountPtr TGameartProvider::GetAccount(const GameClientCreationContext& ctx, const std::string& session)
{
	if (!ctx.ConnectionInfo.Query.contains("player"))
		throw ProviderError("get-player", "Player ID ('player') not provided!");

	const std::string pid = ctx.ConnectionInfo.Query.Get("player");

	double denomination = 1.0;
	if (!yutils::strToDouble2(ctx.ConnectionInfo.Query.Get("denom", "1"), denomination) || denomination <= 0.0)
		throw ProviderError("get-player", "Bad denomination!");

	if (!ctx.ConnectionInfo.Query.contains("currency"))
		throw ProviderError("get-player", "No currency given!");

	gameart::HGAccountPtr createdAccount = std::make_shared<gameart::HGAccount>(
	  ctx, session, UniqueIdentifier(), pid, pid, ctx.ConnectionInfo.Query.Get("locale", "en_US"), ctx.ConnectionInfo.Query.Get("currency"), denomination);

	try
	{
		createdAccount->Config = GetJSON("get-player", GetRawEngineResponse(*createdAccount, { { "config", json() } }, false), ContainsBalanceSchema);

		createdAccount->SetRawBalance(CreditArray(CreditType::CREDIT_CASHABLE, createdAccount->Config["platform"]["balance"].get<uint64_t>()));

		const json* roundID = JsonPath("platform.gameRound.id").Resolve(createdAccount->Config);
		if (roundID)
			createdAccount->HGGameround = roundID->get<std::string>();

		const json* shouldResumeRound = JsonPath("events[0].resume").Resolve(createdAccount->Config);
		if (shouldResumeRound && shouldResumeRound->get<bool>())
		{
			if (createdAccount->HGGameround.empty())
				throw ProviderError("get-player", yutils::Format("Player account %s has an open gameround but no gameround ID!", pid.c_str()));
		}

		if (!createdAccount->HGGameround.empty())
		{
			Log(Debug, "Hypergaming round %s in progress for player %s: %s", createdAccount->HGGameround.c_str(), pid.c_str(),
			    JsonSchema::PrintValueInline(createdAccount->Config).c_str());

			const AccountingRoundSnapshot::RoundIDType round = FindUnfinishedRound([round = createdAccount->HGGameround](const AccountingRoundSnapshot& snap) -> bool {
				return snap.ExtraData.is_object() && snap.ExtraData.contains("hgRound") && snap.ExtraData["hgRound"].get<std::string>() == round;
			});

			if (round.empty() || !MoveUnfinishedRoundToEndpoint(round, std::dynamic_pointer_cast<PlayerEndpoint>(ctx.Endpoint)))
				throw ProviderError("get-player", yutils::Format("Player account %s has an open gameround %s(%s on HG) that cannot be continued on this server!",
				                                                 pid.c_str(), round.c_str(), createdAccount->HGGameround.c_str()));
		}

		return dynamic_pointer_cast<YPlayerAccount>(createdAccount);
	}
	catch (const SessionExpired& err)
	{
		throw err;
	}
	catch (const ProviderError& err)
	{
		throw err;
	}
	catch (const std::exception& err)
	{
		Log(Warning, "Something went wrong during player initialization: %s!", err.what());
		throw ProviderError("get-player", "Could not get player!");
	}
	catch (...)
	{
		Log(Warning, "Unknown error in creating player!");
		throw ProviderError("get-player", "Error getting player!");
	}
}

void TGameartProvider::Reply(const imaxa_connection_ptr& con, int status, const json& response) const
{
	std::error_code ec;
	json replyBody(json::value_t::object);
	replyBody["status"] = status;
	replyBody["response"] = response;
	con->set_status(web::http::status::ok, ec);
	con->set_json_body(replyBody, {}, ec);
}

void TGameartProvider::OnConfigLoaded(const std::filesystem::path& filename)
{
	TCasinoProvider::OnConfigLoaded(filename);

	if (mCasinoClient)
		mCasinoClient->TimeoutMs = GetConfig("client.timeout").get<uint64_t>();

	std::string methodDefault = GetConfig("client.method").get<std::string>();
	std::transform(methodDefault.begin(), methodDefault.end(), methodDefault.begin(), ::toupper);
	if (methodDefault == "POST")
		mDefaultMethod = web::http::verb::post;
	else
		mDefaultMethod = web::http::verb::get;
	// generic HTTP end

	mSalt = GetConfig("salt").get<std::string>();
	mClientAuthID = GetConfig("client.auth.username").get<std::string>();
	mClientAuthPassword = GetConfig("client.auth.password").get<std::string>();
}

void TGameartProvider::PostLoad()
{
	TCasinoProvider::PostLoad();

	const std::string hostname = GetConfig("client.endpoint").get<std::string>();
	web::websockets::uri uri(hostname);
	if (uri.get_type() != web::websockets::uri::http)
		throw ConfigError("Invalid URL: " + hostname);

	try
	{
		mCasinoClient = web::WebClient::New(uri);
		mCasinoClient->Mode = web::EWebClientMode::SingleInstancePersisting;
		mCasinoClient->TimeoutMs = GetConfig("client.timeout").get<uint64_t>();
	}
	catch (const std::exception& e)
	{
		throw ConfigError("Could not create HTTP client: " + std::string(e.what()));
	}

	SetDomain(uri.get_host_port());
}

const JsonSchema ServerConfigSchema = JsonSchema({ { "response", JsonSchema({ { "name", JsonSchema(json::value_t::string) },
                                                                              { "currency", JsonSchema(json::value_t::string) },
                                                                              { "config", JsonSchema({ { "denom", JsonSchema(json::value_t::number_float) } }) } }) } });
bool TGameartProvider::ConnectToService(std::string& outError)
{
	try
	{
		json result = GetJSON("config", GetRawResponse(BuildAdminAPIRequestURI("settings", { std::make_pair("args[0][render]", "json") })), ServerConfigSchema);

		if (result.contains("status") && result.contains("response"))
		{
			const int responseCode = result["status"].get<int>();
			switch (responseCode)
			{
				case 200: break;
				default:
					if (!result["response"].is_string())
					{
						Log(Warning, "HG responded to config with bad status code %d!", responseCode);
						throw ProviderError("config", "HG replied with bad status " + std::to_string(responseCode) +
						                                ". Server might be configured incorrectly or connected to an unsupported version.");
					}
					else
					{
						Log(Warning, "HG responded to config with bad status code %d: %s!", responseCode, result["response"].get<std::string>().c_str());
						throw ProviderError("config", "HG replied with bad status " + std::to_string(responseCode) + ": " + result["response"].get<std::string>() +
						                                ". Server might be configured incorrectly or connected to an unsupported version.");
					}
					break;
			}
		}
		else
		{
			Log(Warning, "HG response JSON to config in one weird son of a bitch (doesn't contain a status and response field). This is what it looks like: %s",
			    result.dump().c_str());
			throw ProviderError("config", "Weird reply from HG. Server might be configured incorrectly or connected to an unsupported version.");
		}

		mDenomination = result["response"]["config"]["denom"].get<double>();
		mCurrency = result["response"]["currency"].get<std::string>();
		Log(Important, "Connection established with HG server '%s' at %s. Currency: %s   Denomination: %.2f", result["response"]["name"].get<std::string>().c_str(),
		    mCasinoClient->HostAddress.c_str(), mCurrency.c_str(), mDenomination);
		return true;
	}
	catch (const SchemaError& err)
	{
		outError = "HyperGaming 4 server responded with a bad config: " + std::string(err.what());
		Log(Error, "Bad configuration of HG server at %s: %s", mCasinoClient->HostAddress.c_str(), err.what());
	}
	catch (const std::exception& err)
	{
		outError = "Could not connect to HyperGaming 4 server: " + std::string(err.what());
		Log(Error, "Could not connect and get configuration of HG server at %s: %s", mCasinoClient->HostAddress.c_str(), err.what());
	}
	return false;
}

web::http::response TGameartProvider::GetRawEngineResponse(const YPlayerAccount& player, const std::vector<std::pair<std::string, json>>& actions, bool bUseSeq) const
{
	json actionArray(json::value_t::array);
	for (size_t i = 0; i < actions.size(); i++)
	{
		json tempAction(json::value_t::object);
		tempAction["action"] = actions[i].first;
		if (actions[i].first != "config")
			tempAction["context"] = actions[i].second;
		actionArray.push_back(std::move(tempAction));
	}

	// start the request
	web::http::request request(web::http::verb::post);
	const std::string token = mSalt + player.Session();
	request.append_header("auth", crypto::Hash(token, EHashAlgorithm::MD5));
	request.set_json_body(actionArray);

	web::QueryString query;
	query["sid"] = player.Session();
	if (bUseSeq && !player.Snapshot().RoundID.empty())
	{
		size_t seq = 0;
		for (const YTransaction& tr : player.Snapshot().Transactions)
		{
			if (tr.Result == ETransactionResult::Success)
				seq++;
		}
		query["seq"] = std::to_string(seq);
	}

	const std::string requestURI = "/engine?" + query.ToString();
	Log(Debug, "Request to %s with auth=%s:\n%s", requestURI.c_str(), request.get_header("auth").c_str(), request.get_body().c_str());

	auto response = mCasinoClient->Request(requestURI, std::move(request)).get();
	if (response.get_error_code())
	{
		Log(Error, "Engine request failed: %s", response.get_error_code().message().c_str());
		// ServiceDisconnected("Engine request failed: " + ec.message());
		throw ProviderError("internal-error", "HTTP request error failed:" + response.get_error_code().message());
	}

	if (response.get_status_code() != web::http::status::ok)
		Log(Warning, "HG returned error code %d: %s", (int)response.get_status_code(), response.get_status_msg().c_str());
	return response;
}

web::http::response TGameartProvider::GetRawResponse(const std::string& requestURI) const
{
	return GetRawResponse(requestURI, mDefaultMethod);
}

web::http::response TGameartProvider::GetRawResponse(const std::string& requestURI, web::http::verb HTTP_Method) const
{
	auto response = mCasinoClient->Request(HTTP_Method, requestURI).get();
	if (response.get_error_code())
	{
		Log(Error, "Request %s failed: %s", requestURI.c_str(), response.get_error_code().message().c_str());
		// ServiceDisconnected("HG request failed: " + ec.message());
		throw ProviderError("internal-error", "Bad response from HG server!");
	}

	return response;
}

std::string TGameartProvider::Currency() const
{
	return mCurrency;
}

void TGameartProvider::OnAccountingRoundEnd(YGameClient& player, const AccountingRoundSnapshot& result)
{
	dynamic_cast<gameart::HGAccount*>(player.Account().get())->HGGameround.clear();
	TCasinoProvider::OnAccountingRoundEnd(player, result);
}
