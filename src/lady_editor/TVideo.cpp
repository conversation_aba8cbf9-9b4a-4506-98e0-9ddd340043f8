#include "lady_editor/TVideo.h"

#include <oneapi/tbb/parallel_for.h>

#include <algorithm>
#include <cstdio>
#include <fstream>

#if !defined(LADY_ROULETTE)
#include <numeric>
#endif

#include "Cryptography.h"
#include "MyUtils.h"
#include "common/THash.h"

#if LIBAVCODEC_VERSION_INT >= AV_VERSION_INT(59, 24, 100)
#define HAVE_FFMPEG_CH_LAYOUT
#endif

void audio_callback(void* udata, Uint8* stream, int len)
{
	/* Only play if we have data left */
	TVideo* pThis = (TVideo*)udata;
	if (!pThis)
		return;

	ScopedLock lock(pThis->mLastAudioTime);
	std::shared_ptr<AudioData> data = pThis->GetAudioData();
	if (data)
	{
		len = (len > data->len ? data->len : len);
		long audio_pts = data->pts * 1000L / (double)pThis->mAudioSpecs.freq;

		memcpy(stream, data->buffer, len);
		pThis->SetAudioTime(audio_pts);
	}
}

thread_local Uint64 timeout2;
static int decode_interrupt_cb(void* ctx)
{
	if (timeout2 == 0)
	{
		timeout2 = SDL_GetTicks64();
		return 0;
	}

	if (MyUtils::IsTime(timeout2, 1500))
		return 1;
	return 0;
}

// encrypted file helpers
int readData(void* ptr, uint8_t* buf, int buf_size)
{
	MemoryStream* stream = reinterpret_cast<MemoryStream*>(ptr);
	buf_size = std::min(buf_size, (int)(stream->Buffer.size() - stream->Position));
	std::memcpy(buf, stream->Buffer.data() + stream->Position, buf_size);
	stream->Position += buf_size;
	return buf_size;
}

int64_t seekData(void* opaque, int64_t offset, int whence)
{
	if (whence != SEEK_SET)
		return -1;

	MemoryStream* stream = reinterpret_cast<MemoryStream*>(opaque);
	stream->Position = std::min(offset, (int64_t)stream->Buffer.size());

	return offset;
}

TVideo::TVideo(Container* parent, float x, float y, float w, float h, const std::string& ComponentName) : TGuiComponent(parent, x, y, w, h, ComponentName)
{
	initVariables();

	mBufferSize = 0;

	mAudioInitialized = false;
	mDisableAudio = false;
	mQuitThreadAfterLoadFinished = false;

	mDecryptKey = HashKey(std::string(xorkey_HEX), false);

	SDL_zero(mAudioSpecs);

	const std::string hwDevice = pApp->GetParam("HwAccelMode")->AsString();
	if (hwDevice == "intel")
		HardwareDecodeDeviceType = AV_HWDEVICE_TYPE_VAAPI;
	else if (hwDevice == "nvidia")
		HardwareDecodeDeviceType = AV_HWDEVICE_TYPE_VDPAU;
	else if (hwDevice == "cuda")
		HardwareDecodeDeviceType = AV_HWDEVICE_TYPE_CUDA;

	// mGarbageCollectorThread = std::thread(&TVideo::garbageCollector, this);

	OnVisibleChanged += [this](Widget* src, bool bVisible) {
		if (!bVisible)
			SetPaused(true);
	};
}

TVideo::~TVideo()
{
	FreeVideo();

	// mGarbageCollectorSemaphore.release();
	// mGarbageCollectorThread.join();

	if (mByteConversionBuffer)
	{
		free(mByteConversionBuffer);
		mByteConversionBuffer = NULL;
		mByteConversionBufferSize = 0;
	}

	if (pTargetTexture)
	{
		GPU_FreeImage(pTargetTexture);
		pTargetTexture = NULL;
	}

	pApp->UnregisterAllActionHandlersByRelatedObject(this);
}

void TVideo::initVariables()
{
	setVisible(false);
	bVideoLoaded = false;

	mShowWaveform = false;

	mFrameRate = -1;
	mVideoFrames = -1;
	mElapsedMs = 0;
	mDurationMs = 0;
	mDisplayedFrame = -1;
	mLoadFinished = false;
	mVideoTime = 0;
	mLastAudioTime = { 0, 0 };
	mAudioSamples = 0;

	mVideoFrameDecodeTime = 0;
	mVideoDecodeTimes.clear();
	mAverageVideoFrameDecodeTime = 0.0;

	mLoopVideo = false;

	mPauseTime = -1;

	mHasAudio = false;

	mHasSeekOperationWaiting = false;
	mWasSeekingTo = -1;
	mDestTime = -1;
	mFirstSeekOutput = false;

	bQuitThread = false;

	mVFilterBufferSrcCtx = NULL;
	mVFilterBufferSinkCtx = NULL;
	mVFilterGraph = NULL;
	mVFilterInitialized = false;
	mVFilterDesc.clear();
	mAFilterBufferSrcCtx = NULL;
	mAFilterBufferSinkCtx = NULL;
	mAFilterGraph = NULL;
	mAFilterInitialized = false;
	mAFilterDesc.clear();
}

bool TVideo::decodeBinFile(const std::filesystem::path& filepath, MemoryStream& outData)
{
	std::ifstream in(filepath, std::ios_base::binary | std::ios_base::in);
	if (!in.is_open())
		return false;

	// Determine the file length
	in.seekg(0, std::ios_base::end);
	const size_t outSize = in.tellg();
	in.seekg(0, std::ios_base::beg);

	if (!outSize)
		return false;

	size_t tempBufferSize = outSize;
	// assign a divisable by 8 buffer, to prevent errors when XOR-ing the data
	const size_t leftover = outSize % 8;
	if (leftover)    // resize to 8 byte chunk
		tempBufferSize += 8 - leftover;


	std::vector<uint8_t> fileBytes(tempBufferSize);
	in.read(reinterpret_cast<char*>(fileBytes.data()), outSize);
	in.close();

	outData.Buffer.resize(tempBufferSize);

	// ACTUAL DECRYPTING PART
	tbb::parallel_for(0UL, outSize, 8UL, [&](size_t i) {
		uint64_t& xor_temp = (uint64_t&)mDecryptKey[i % KEY_SIZE];
		uint64_t& decodedChunk = *(uint64_t*)(outData.Buffer.data() + i);
		decodedChunk = ((uint64_t&)fileBytes[i]) ^ xor_temp;
	});

	outData.Buffer.erase(outData.Buffer.begin() + outSize, outData.Buffer.end());

	return true;
}

int TVideo::LoadVideo(const std::filesystem::path& Filepath, bool FreeVideo /*if strema video then FALSE else TRUE*/,
                      int BufferSize /* = 10 .. if -1 BufferSize == video length*/)
{
	if (bVideoLoaded)
		return -1;

	mFilepath = Filepath;
	mBufferSize = BufferSize;
	if (!FreeVideo)
		mBufferSize = -1;

	initVariables();
	SetPaused(true);

	if (!std::filesystem::exists(mFilepath))    // ce datoteka ne obstaja
		return -1;

	mVideoParseThread = std::thread(std::bind(&TVideo::readLocalPacketThread, this));

	return 0;
}

int TVideo::FreeVideo()
{
	if (mVideoParseThread.joinable())
	{
		bQuitThread = true;
		mVideoParseThread.join();
	}

	mFilepath.clear();
	bVideoLoaded = false;

	setVisible(false);

	mElapsedMs = 0;
	mDurationMs = 0;

	if (mHasAudio && mAudioInitialized)
		SDL_PauseAudioDevice(mAudioDevice, 1);

	pWaveformTexture.reset();

	mVideoDecodeTimes.clear();
	mLastAudioTime = { 0, 0 };

#if 0
	freeFilters(true);    // free video filters
	freeFilters(false);    // free audio filters
#endif

	FlushBuffers(NULL, NULL);

	return 0;
}
/*
void TVideo::garbageCollector()
{
    std::list<std::shared_ptr<VideoData>> videoData;
    std::list<std::shared_ptr<AudioData>> audioData;
    while (!mGarbageCollectorSemaphore.try_acquire_for(std::chrono::milliseconds(1000 + crypto::GetRandomInRange(-500, 500))))
    {
        mToFreeVideoBuffer.Lock();
        videoData.swap(&mToFreeVideoBuffer);
        mToFreeVideoBuffer.Unlock();

        mToFreeAudioBuffer.Lock();
        audioData.swap(&mToFreeAudioBuffer);
        mToFreeAudioBuffer.Unlock();

        videoData.clear();
        audioData.clear();
    }
}
*/
int hw_decoder_init(AVBufferRef** hw_devide_ctx_out, AVCodecContext* ctx, const enum AVHWDeviceType type)
{
	const int err = av_hwdevice_ctx_create(hw_devide_ctx_out, type, NULL, NULL, 0);
	if (err < 0)
	{
		char errDesc[AV_ERROR_MAX_STRING_SIZE];
		errDesc[0] = '\0';
		av_make_error_string(errDesc, AV_ERROR_MAX_STRING_SIZE, err);
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Failed to create a %s device (%d:%s).", av_hwdevice_get_type_name(type), err, errDesc);

		return err;
	}

	ctx->hw_device_ctx = av_buffer_ref(*hw_devide_ctx_out);
	if (!ctx->hw_device_ctx)
		return -1;

	pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[AVI] Successfully created %s device!", av_hwdevice_get_type_name(type));

	return err;
}

enum AVPixelFormat get_hw_format(AVCodecContext* ctx, const enum AVPixelFormat* pix_fmts)
{
	const enum AVPixelFormat* p;

	for (p = pix_fmts; *p != -1; p++)
	{
		if (*p == static_cast<AVPixelFormat>((long)ctx->opaque))
			return *p;
	}

	fprintf(stderr, "Failed to get HW surface format.\n");
	return AV_PIX_FMT_NONE;
}
void TVideo::readLocalPacketThread()
{
	AVFormatContext* pFormatCtx = avformat_alloc_context();
	const AVCodec* pVideoCodec = NULL;
	AVCodecContext* pCodecCtx = NULL;
	AVStream* pVideoStream = NULL;

	const AVCodec* pAudioCodec = NULL;
	AVCodecContext* pAudioCodecCtx = NULL;
	AVStream* pAudioStream = NULL;

	SwrContext* pSwrCtx = NULL;
	SwsContext* pSwsCtx = NULL;

	AVPacket packet;

	int VideoStreamId = -1;
	int AudioStreamId = -1;

	const std::filesystem::path filename = GetFilename();
	const std::filesystem::path& filepath = GetFilePath();

	int fmt_open_result = -1;

	MemoryStream fileBuffer;
	AVIOContext* pAvioCtx = NULL;

	AVBufferRef* hw_device_ctx = NULL;
	enum AVPixelFormat hw_pix_fmt = AV_PIX_FMT_NONE;

	enum AVPixelFormat format = AV_PIX_FMT_YUV420P;

	bool shouldSleep = false;
	bool quit_thread = false;

	// uint32_t time = SDL_GetTicks64();
	pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Loading video %s.", filename.c_str());
	if (!filepath.empty() && filepath.extension() == ".bin")
	{
		Uint64 startTime = SDL_GetTicks64();
		if (decodeBinFile(filepath, fileBuffer) && fileBuffer.Buffer.size())
		{
			// ustvarimo avio custom buffer za format context
			constexpr size_t avio_ctx_buffer_size = 32768;
			pAvioCtx = avio_alloc_context(reinterpret_cast<uint8_t*>(av_malloc(avio_ctx_buffer_size)), avio_ctx_buffer_size, 0, &fileBuffer, &readData, NULL, &seekData);

			// avio_ctx->seekable = AVIO_SEEKABLE_NORMAL;
			pFormatCtx->flags = AVFMT_FLAG_CUSTOM_IO;
			pFormatCtx->pb = pAvioCtx;
			fmt_open_result = avformat_open_input(&pFormatCtx, "", NULL, NULL);
		}
		pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Time to decode video: %d.", (SDL_GetTicks64() - startTime));
	}
	else
	{
		fmt_open_result = avformat_open_input(&pFormatCtx, filepath.c_str(), NULL, NULL);
	}

	if (fmt_open_result < 0)    // ce nismo uspeli nalozit videa
	{
		char tmp[500];
		av_strerror(fmt_open_result, tmp, 500);
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Error loading video %s: %s!", filename.c_str(), tmp);
		goto end;    // Couldn't open file
	}

	// Retrieve stream information
	if (avformat_find_stream_info(pFormatCtx, NULL) < 0)
	{
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Couldn't find stream information!");
		goto end;    // Couldn't find stream information
	}

	timeout2 = SDL_GetTicks64();
	pFormatCtx->interrupt_callback.opaque = (void*)timeout2;
	pFormatCtx->interrupt_callback.callback = decode_interrupt_cb;

	VideoStreamId = av_find_best_stream(pFormatCtx, AVMEDIA_TYPE_VIDEO, -1, -1, &pVideoCodec, 0);
	if (VideoStreamId < 0)
	{
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Cannot find a video stream in the input file!");
		goto end;
	}

	if (!(pCodecCtx = avcodec_alloc_context3(pVideoCodec)))
	{
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Cannot allocate video context from codec!");
		goto end;
	}

	if (HardwareDecodeDeviceType != AV_HWDEVICE_TYPE_NONE)    // ko imamo vklopljen HW_ACCELERATION
	{
		for (int i = 0;; i++)
		{
			const AVCodecHWConfig* config = avcodec_get_hw_config(pVideoCodec, i);
			if (!config)
			{
				pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[AVI] Decoder %s does not support device type %s.", pVideoCodec->name,
				               av_hwdevice_get_type_name(HardwareDecodeDeviceType));
				continue;
			}

			if (config->methods & AV_CODEC_HW_CONFIG_METHOD_HW_DEVICE_CTX && config->device_type == HardwareDecodeDeviceType)
			{
				hw_pix_fmt = config->pix_fmt;
				break;
			}
		}

		if (hw_pix_fmt == AV_PIX_FMT_NONE)
			pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Did not get any supported device for selected type %s!", av_hwdevice_get_type_name(HardwareDecodeDeviceType));
	}

	pVideoStream = pFormatCtx->streams[VideoStreamId];
	if (avcodec_parameters_to_context(pCodecCtx, pVideoStream->codecpar) < 0)
		goto end;

	if (HardwareDecodeDeviceType != AV_HWDEVICE_TYPE_NONE && hw_pix_fmt != AV_PIX_FMT_NONE)
	{
		if (hw_decoder_init(&hw_device_ctx, pCodecCtx, HardwareDecodeDeviceType) < 0)
			goto end;

		format = AV_PIX_FMT_NV12;
	}
	else    // fallback to no hardware decoding
	{
		hw_pix_fmt = AV_PIX_FMT_YUV420P;
	}

	pSwsCtx = sws_getContext(pCodecCtx->width, pCodecCtx->height, format, pCodecCtx->width, pCodecCtx->height, AV_PIX_FMT_RGB24, SWS_FAST_BILINEAR, NULL, NULL, NULL);

	pCodecCtx->opaque = (void*)(long)hw_pix_fmt;
	pCodecCtx->get_format = &get_hw_format;

	// Initialize codec context
	if (avcodec_open2(pCodecCtx, pVideoCodec, NULL) < 0)
	{
		pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Error opening video codec context!");
		goto end;    // Error opening codec context
	}

	if (!mDisableAudio)
	{
		AudioStreamId = av_find_best_stream(pFormatCtx, AVMEDIA_TYPE_AUDIO, -1, -1, &pAudioCodec, 0);
		if (AudioStreamId < 0)
		{
			pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Cannot find a audio stream in the input file!");
			goto end;
		}

		if (!(pAudioCodecCtx = avcodec_alloc_context3(pAudioCodec)))
		{
			pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Cannot allocate audio context from codec!");
			goto end;
		}
		pAudioCodecCtx->request_sample_fmt = AV_SAMPLE_FMT_S16;

		pAudioStream = pFormatCtx->streams[AudioStreamId];
		if (avcodec_parameters_to_context(pAudioCodecCtx, pAudioStream->codecpar) < 0)
			goto end;

		if (pAudioCodec)
		{
			if (avcodec_open2(pAudioCodecCtx, pAudioCodec, NULL) >= 0)
			{
				mHasAudio = true;
				if (!mAudioInitialized)
				{
					SDL_AudioSpec want;
					SDL_zero(want);

					int test = 1024;
					int check = 1;
					while (mAudioSpecs.size != 4096 && check <= 5)
					{
						if (mAudioInitialized)
							SDL_CloseAudioDevice(mAudioDevice);

						SDL_zero(mAudioSpecs);

						want.freq = 48000;
						want.format = AUDIO_S16LSB;
						want.channels = 2;
						want.samples = test;
						want.userdata = this;
						want.callback = audio_callback;
						mAudioDevice = SDL_OpenAudioDevice(NULL, 0, &want, &mAudioSpecs, SDL_AUDIO_ALLOW_ANY_CHANGE);
						mAudioInitialized = true;
						test = test * 2;

						check++;
					}

					pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Audio tried to open device %d times!", check);

					if (check == 5)
					{
						mHasAudio = false;
						mAudioInitialized = false;
						SDL_CloseAudioDevice(mAudioDevice);
					}

					SDL_PauseAudioDevice(mAudioDevice, 1);
				}

				// !!out format must not be planar!!
				mAudioFormat = AV_SAMPLE_FMT_NONE;
				if (mAudioSpecs.format == AUDIO_U8)
					mAudioFormat = AV_SAMPLE_FMT_U8;
				else if (mAudioSpecs.format == AUDIO_S16LSB)
					mAudioFormat = AV_SAMPLE_FMT_S16;
				else if (mAudioSpecs.format == AUDIO_S32LSB)
					mAudioFormat = AV_SAMPLE_FMT_S32;

#ifdef HAVE_FFMPEG_CH_LAYOUT
				AVChannelLayout ch_layout;
				av_channel_layout_default(&ch_layout, mAudioSpecs.channels);
				swr_alloc_set_opts2(&pSwrCtx, &ch_layout, mAudioFormat, mAudioSpecs.freq, &pAudioCodecCtx->ch_layout, pAudioCodecCtx->sample_fmt,
				                    pAudioCodecCtx->sample_rate, 0, NULL);
#else
				pSwrCtx = swr_alloc_set_opts(NULL, av_get_default_channel_layout(mAudioSpecs.channels), mAudioFormat, mAudioSpecs.freq,
				                             av_get_default_channel_layout(pAudioCodecCtx->channels), pAudioCodecCtx->sample_fmt, pAudioCodecCtx->sample_rate, 0, NULL);
#endif

				swr_init(pSwrCtx);
			}
			else
			{
				pApp->WriteLog(LT_WARNING, NO_GAME_ID, "\t[AVI] Error opening audio codec context!");
			}
		}
	}

	mVideoFrames = pFormatCtx->streams[VideoStreamId]->nb_frames;
	if (mFrameRate < 0)
	{
		if (pCodecCtx->codec_id == AV_CODEC_ID_H264)
			mFrameRate = av_q2d(pFormatCtx->streams[VideoStreamId]->avg_frame_rate);
		else
			mFrameRate = av_q2d(pFormatCtx->streams[VideoStreamId]->r_frame_rate);
	}

	mDurationMs = std::round(1e3 * pFormatCtx->streams[VideoStreamId]->duration * pFormatCtx->streams[VideoStreamId]->time_base.num /
	                         pFormatCtx->streams[VideoStreamId]->time_base.den);

	if (mBufferSize == 0)
	{
		quit_thread = true;
		mLoadFinished = true;
	}

	bVideoLoaded = true;

	while (!quit_thread)
	{
		quit_thread = bQuitThread;

		if (shouldSleep)
			SDL_Delay(1);
		shouldSleep = true;

		const bool bIsSeeking = IsSeeking();
		if (mHasSeekOperationWaiting.exchange(false))
		{
			SetPaused(true);
			FlushBuffers(pCodecCtx, pAudioCodecCtx);
			_Seek(pFormatCtx, VideoStreamId);
		}

		mVideoBuffer.Lock();
		const int currentBufSize = mVideoBuffer->size();
		mVideoBuffer.Unlock();
		if (!bIsSeeking && mBufferSize > 0 && (currentBufSize > mBufferSize))
			continue;

		shouldSleep = false;
#if 0
		if (!mVFilterInitialized && !mVFilterDesc.empty())
			initFilters(true, pFormatCtx, pCodecCtx, mVideoStreamId, mVFilterDesc);
		else if (mVFilterInitialized && mVFilterDesc.empty())
			freeFilters(true);

		if (!mAFilterInitialized && !mAFilterDesc.empty())
			initFilters(false, pFormatCtx, pAudioCodecCtx, mAudioStreamId, mAFilterDesc);
		else if (mAFilterInitialized && mAFilterDesc.empty())
			freeFilters(false);
#endif
		timeout2 = SDL_GetTicks64();
		const int res = av_read_frame(pFormatCtx, &packet);
		if (res >= 0)
		{
			if (packet.stream_index == VideoStreamId)
			{
				/*int ret = */ decodeVideoPacket(pFormatCtx, pCodecCtx, pSwsCtx, VideoStreamId, &packet);
			}
			else if (packet.stream_index == AudioStreamId)
			{
				/*int ret = */ decodeAudioPacket(pFormatCtx, pAudioCodecCtx, pSwrCtx, AudioStreamId, &packet);
			}

			av_packet_unref(&packet);
		}
		else
		{
			av_packet_unref(&packet);

			if (mLoadFinished)
			{
				shouldSleep = true;
				continue;
			}

			if (mLoopVideo)
			{
				mDestTime = 0;
				mPauseAfterSeek = false;

				_Seek(pFormatCtx, VideoStreamId);
				bSeekDueToLoop = true;

				continue;
			}

			mLoadFinished = true;
			pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Thread finished loading all packets for %s!", filename.c_str());
			if (mQuitThreadAfterLoadFinished)
			{
				bQuitThread = true;
				quit_thread = true;
			}
		}
	}    // while

	av_packet_unref(&packet);

	pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Quiting thread and clearing data of %s.", filename.c_str());

end:

	if (pSwrCtx)
	{
		swr_close(pSwrCtx);
		swr_free(&pSwrCtx);
	}

	if (pSwsCtx)
	{
		sws_freeContext(pSwsCtx);
	}

	// Close the codec
	if (pCodecCtx)
	{
		avcodec_free_context(&pCodecCtx);
	}

	if (pAudioCodecCtx)
	{
		avcodec_free_context(&pAudioCodecCtx);
	}

	if (hw_device_ctx)
	{
		av_buffer_unref(&hw_device_ctx);
	}

	if (pAvioCtx)
	{
		av_free(pAvioCtx->buffer);
		pAvioCtx->buffer = NULL;

		avio_context_free(&pAvioCtx);
	}

	// Close format context
	if (pFormatCtx)
	{
		avformat_close_input(&pFormatCtx);    // already does freeContext, and sets the pointer to null
	}
}

int TVideo::decodeVideoPacket(AVFormatContext* formatCtx, AVCodecContext* codecCtx, SwsContext* swsCtx, int streamId, const AVPacket* packet)
{
	// Decode video frame
	Uint64 time = SDL_GetTicks64();

	int ret = avcodec_send_packet(codecCtx, packet);
	if (ret < 0)
		return -1;

	while (ret >= 0)
	{
		AVFrame* frameYUV = av_frame_alloc();
		ret = avcodec_receive_frame(codecCtx, frameYUV);
		if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF || ret < 0)
		{
			// av_frame_unref(frameYUV);
			av_frame_free(&frameYUV);
			break;
		}

		if (HardwareDecodeDeviceType != AV_HWDEVICE_TYPE_NONE)    // ko imamo vklopljen HW_ACCELERATION
		{
			if (retrieveData(frameYUV) < 0)
			{
				// av_frame_unref(frameYUV);
				av_frame_free(&frameYUV);
				break;
			}
		}
#if 0
		// filters
		if (mVFilterInitialized && mVFilterBufferSrcCtx)
		{
			av_buffersrc_add_frame_flags(mVFilterBufferSrcCtx, frameYUV, AV_BUFFERSRC_FLAG_KEEP_REF);
			while (true)
			{
				int result = av_buffersink_get_frame(mVFilterBufferSinkCtx, frameYUV);

				if (result == AVERROR(EAGAIN) || result == AVERROR(AVERROR_EOF))
					break;
				if (result < 0)
					break;
			}
		}
#endif
		double v_pts_seconds = (frameYUV->pts - formatCtx->streams[streamId]->start_time) * av_q2d(formatCtx->streams[streamId]->time_base);
		int64_t v_pts_ms = std::round(v_pts_seconds * 1e3);
		int frame_idx = std::round(v_pts_seconds * mFrameRate);

		if (mDestTime >= 0 && v_pts_ms < mDestTime)    // while is seeking, we don't need to convert frameYUV to frameRGB
		{
			if (mFirstSeekOutput)
			{
				pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Seek got %ld, wanted %ld - skipping next %ldms.", v_pts_ms, mDestTime.load(),
				               mDestTime - v_pts_ms);
			}
			mFirstSeekOutput = false;

			auto video = std::make_shared<VideoData>();
			video->frame = frameYUV;
			video->pts = v_pts_ms;
			video->frame_idx = frame_idx;

			// mToFreeVideoBuffer.Lock();
			// mToFreeVideoBuffer->push_back(video);
			// mToFreeVideoBuffer.Unlock();
			continue;
		}

		// from here on, the seeking is COMPLETED!!

		AVFrame* frameRGB = av_frame_alloc();
		frameRGB->format = AV_PIX_FMT_RGB24;
		frameRGB->width = frameYUV->width;
		frameRGB->height = frameYUV->height;
		av_frame_copy_props(frameYUV, frameRGB);
		if (av_frame_get_buffer(frameRGB, 0) < 0)
		{
			// av_frame_unref(frameRGB);
			av_frame_free(&frameRGB);

			// av_frame_unref(frameYUV);
			av_frame_free(&frameYUV);

			break;
		}

		sws_scale(swsCtx, frameYUV->data, frameYUV->linesize, 0, codecCtx->height, frameRGB->data, frameRGB->linesize);

		// push video data to freeVideoBuffer, so we don't clear the frame here
		auto video4free = std::make_shared<VideoData>();
		video4free->frame = frameYUV;
		video4free->pts = v_pts_ms;
		video4free->frame_idx = frame_idx;
		// mToFreeVideoBuffer.Lock();
		// mToFreeVideoBuffer->push_back(video4free);
		// mToFreeVideoBuffer.Unlock();

		// push converted video data to videoBuffer
		auto video = std::make_shared<VideoData>();
		video->frame = frameRGB;
		video->pts = v_pts_ms;
		video->frame_idx = frame_idx;

		mVideoBuffer.Lock();
		mVideoBuffer->push_back(video);
		mVideoBuffer.Unlock();

		if (mDestTime >= 0)    // is seeking
		{
			if (!bSeekDueToLoop)
				mWasSeekingTo = mDestTime.load();
			mDestTime = -1;
		}
	}

	mVideoFrameDecodeTime = SDL_GetTicks64() - time;
	mVideoDecodeTimes.push_back(mVideoFrameDecodeTime);

	return 0;
}

int TVideo::retrieveData(AVFrame* input)
{
	AVFrame* output = av_frame_alloc();
	if (!output)
		return AVERROR(ENOMEM);

	int err;

	/* default output nv12 */
	output->format = AV_PIX_FMT_NV12;    // AV_PIX_FMT_YUV420P AV_PIX_FMT_NV12;
	if ((err = av_hwframe_transfer_data(output, input, 0)) < 0)
	{
		fprintf(stderr, "Failed to transfer data to output frame: %d.\n", err);
		av_frame_free(&output);
		return err;
	}

	if ((err = av_frame_copy_props(output, input)) < 0)
	{
		av_frame_free(&output);
		return err;
	}

	av_frame_unref(input);
	av_frame_move_ref(input, output);

	av_frame_free(&output);

	return 0;
}


int TVideo::decodeAudioPacket(AVFormatContext* formatCtx, AVCodecContext* codecCtx, SwrContext* swrCtx, int streamId, const AVPacket* packet)
{
	if (mDisableAudio)
		return -1;

	// Decode audio frame
	int ret = avcodec_send_packet(codecCtx, packet);
	if (ret < 0)
		return -1;

	// int64_t pts = (packet.dts != AV_NOPTS_VALUE) ? packet.dts : 0;

	while (ret >= 0)
	{
		AVFrame* frame = av_frame_alloc();
		ret = avcodec_receive_frame(codecCtx, frame);
		if (ret == AVERROR(EAGAIN) || ret == AVERROR_EOF || ret < 0)
		{
			// av_frame_unref(frame);
			av_frame_free(&frame);
			break;
		}
#if 0
		// filters
		if (mAFilterInitialized && mAFilterBufferSrcCtx)
		{
			av_buffersrc_add_frame_flags(mAFilterBufferSrcCtx, frame, AV_BUFFERSRC_FLAG_KEEP_REF);
			while (true)
			{
				int result = av_buffersink_get_frame(mAFilterBufferSinkCtx, frame);

				if (result == AVERROR(EAGAIN) || result == AVERROR(AVERROR_EOF))
					break;
				if (result < 0)
					break;
			}
		}
#endif
		int outChannels = av_sample_fmt_is_planar(mAudioFormat) ? mAudioSpecs.channels : 1;
		if (outChannels > 1)
		{
			// av_frame_unref(frame);
			av_frame_free(&frame);
			throw std::runtime_error("Unsupported!!!");
		}

		int outSampleSize = av_samples_get_buffer_size(NULL, mAudioSpecs.channels, mAudioSpecs.samples, mAudioFormat, 1) / outChannels;

		uint8_t* sampleBuffer = (uint8_t*)av_malloc((size_t)outSampleSize);
		bool bChannelsMatch;
#ifdef HAVE_FFMPEG_CH_LAYOUT
		bChannelsMatch = codecCtx->ch_layout.nb_channels == outChannels;
#else
		bChannelsMatch = codecCtx->channels == outChannels;
#endif
		if (!bChannelsMatch || mAudioFormat != codecCtx->sample_fmt || mAudioSpecs.freq != codecCtx->sample_rate)
			ret = swr_convert(swrCtx, &sampleBuffer, codecCtx->frame_size, (const uint8_t**)frame->extended_data, frame->nb_samples);
		else
			ret = av_samples_fill_arrays(&sampleBuffer, frame->linesize, frame->extended_data[0], outChannels, frame->nb_samples, mAudioFormat, 1);

		if (ret < 0)
		{
			av_freep(&sampleBuffer);
			// av_frame_unref(frame);
			av_frame_free(&frame);
			break;
		}

		double a_pts = frame->best_effort_timestamp;
		if (a_pts == AV_NOPTS_VALUE)
			a_pts = 0;

		a_pts *= av_q2d(formatCtx->streams[streamId]->time_base);

		if (mDestTime >= 0)    // while is seeking update mAudioSamples
		{
			mAudioSamples = a_pts * mAudioSpecs.freq;

			if ((a_pts * 1e3) < mDestTime)
			{
				av_freep(&sampleBuffer);
				// av_frame_unref(frame);
				av_frame_free(&frame);
				continue;
			}
		}

		auto audio = std::make_shared<AudioData>();
		audio->buffer = sampleBuffer;
		audio->len = outSampleSize;
		audio->pts = mAudioSamples;    // a_pts;

		mAudioBuffer.Lock();
		mAudioBuffer->push_back(audio);
		mAudioBuffer.Unlock();

		mAudioSamples += frame->nb_samples;

		// av_frame_unref(frame);
		av_frame_free(&frame);
	}

	return 0;
}

#if 0
int TVideo::initFilters(bool isVideoFilter, AVFormatContext* formatCtx, AVCodecContext* codecCtx, int streamId, std::string filters_descr)
{
	freeFilters(isVideoFilter);

	char args[512];
	int ret;
	AVFilterInOut* outputs = NULL;
	AVFilterInOut* inputs = NULL;
	if (isVideoFilter)
	{
		const AVFilter* buffersrc = avfilter_get_by_name("buffer");
		const AVFilter* buffersink = avfilter_get_by_name("buffersink");

		AVRational time_base = formatCtx->streams[streamId]->time_base;

		enum AVPixelFormat pix_fmts[] = { AV_PIX_FMT_GRAY8, AV_PIX_FMT_NONE };
		mVFilterGraph = avfilter_graph_alloc();

		/* buffer video source: the decoded frames from the decoder will be inserted here. */
		snprintf(args, sizeof(args), "video_size=%dx%d:pix_fmt=%d:time_base=%d/%d:pixel_aspect=%d/%d", codecCtx->width, codecCtx->height, codecCtx->pix_fmt,
		         time_base.num, time_base.den, codecCtx->sample_aspect_ratio.num, codecCtx->sample_aspect_ratio.den);

		ret = avfilter_graph_create_filter(&mVFilterBufferSrcCtx, buffersrc, "in", args, NULL, mVFilterGraph);
		if (ret < 0)
		{
			avfilter_graph_free(&mVFilterGraph);
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer source\n");
			return ret;
		}

		/* buffer video sink: to terminate the filter chain. */
		AVBufferSinkParams* buffersink_params = av_buffersink_params_alloc();
		buffersink_params->pixel_fmts = pix_fmts;

		ret = avfilter_graph_create_filter(&mVFilterBufferSinkCtx, buffersink, "out", NULL, buffersink_params, mVFilterGraph);
		av_free(buffersink_params);
		if (ret < 0)
		{
			avfilter_graph_free(&mVFilterGraph);
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer sink\n");
			return ret;
		}

		/* Endpoints for the filter graph. */
		outputs = avfilter_inout_alloc();
		inputs = avfilter_inout_alloc();
		outputs->name = av_strdup("in");
		outputs->filter_ctx = mVFilterBufferSrcCtx;
		outputs->pad_idx = 0;
		outputs->next = NULL;
		inputs->name = av_strdup("out");
		inputs->filter_ctx = mVFilterBufferSinkCtx;
		inputs->pad_idx = 0;
		inputs->next = NULL;
		if ((ret = avfilter_graph_parse_ptr(mVFilterGraph, filters_descr.c_str(), &inputs, &outputs, NULL)) < 0)
			goto end;
		if ((ret = avfilter_graph_config(mVFilterGraph, NULL)) < 0)
			goto end;

		mVFilterInitialized = true;
	}
	else
	{
		const AVFilter* buffersrc = avfilter_get_by_name("abuffer");
		const AVFilter* buffersink = avfilter_get_by_name("abuffersink");

		AVRational time_base = formatCtx->streams[streamId]->time_base;
		mAFilterGraph = avfilter_graph_alloc();

		/* buffer audio source: the decoded frames from the decoder will be inserted here. */
		if (!codecCtx->channel_layout)
			codecCtx->channel_layout = av_get_default_channel_layout(codecCtx->channels);

		snprintf(args, sizeof(args), "time_base=%d/%d:sample_rate=%d:sample_fmt=%s:channel_layout=0x%" PRIx64, time_base.num, time_base.den, codecCtx->sample_rate,
		         av_get_sample_fmt_name(codecCtx->sample_fmt), codecCtx->channel_layout);
		ret = avfilter_graph_create_filter(&mAFilterBufferSrcCtx, buffersrc, "in", args, NULL, mAFilterGraph);
		if (ret < 0)
		{
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer source\n");
			return ret;
		}

		ret = avfilter_graph_create_filter(&mAFilterBufferSinkCtx, buffersink, "out", NULL, NULL, mAFilterGraph);
		if (ret < 0)
		{
			av_log(NULL, AV_LOG_ERROR, "Cannot create buffer sink\n");
			return ret;
		}

#if 0
		std::string filterName = filters_descr.substr(0, filters_descr.find("="));
		const AVFilter* filter = avfilter_get_by_name(filterName.c_str());
		if (!filter)
		{
			return AVERROR_FILTER_NOT_FOUND;
		}

		std::string args = filters_descr.substr(filters_descr.find("=") + 1);
		AVFilterContext* filter_ctx;
		ret = avfilter_graph_create_filter(&filter_ctx, filter, filterName.c_str(), args.c_str(), NULL, mAFilterGraph);
		if (ret < 0)
		{
			av_log(NULL, AV_LOG_ERROR, "Cannot create audio filter\n");
			return ret;
		}

		/* Connect the filters; */
		ret = avfilter_link(filter_ctx, 0, mAFilterBufferSinkCtx, 0);
		if (ret < 0)
		{
			av_log(NULL, AV_LOG_ERROR, "Error connecting filters\n");
			return ret;
		}

		/* Configure the graph. */
		ret = avfilter_graph_config(mAFilterGraph, NULL);
		if (ret < 0)
		{
			av_log(NULL, AV_LOG_ERROR, "Error while configuring graph\n");
			return ret;
		}
#else
		/* Endpoints for the filter graph. */
		outputs = avfilter_inout_alloc();
		inputs = avfilter_inout_alloc();
		outputs->name = av_strdup("in");
		outputs->filter_ctx = mAFilterBufferSrcCtx;
		outputs->pad_idx = 0;
		outputs->next = NULL;
		inputs->name = av_strdup("out");
		inputs->filter_ctx = mAFilterBufferSinkCtx;
		inputs->pad_idx = 0;
		inputs->next = NULL;

		if ((ret = avfilter_graph_parse_ptr(mAFilterGraph, filters_descr.c_str(), &inputs, &outputs, NULL)) < 0)
			goto end;
		if ((ret = avfilter_graph_config(mAFilterGraph, NULL)) < 0)
			goto end;
#endif

		mAFilterInitialized = true;
	}

end:
	avfilter_inout_free(&inputs);
	avfilter_inout_free(&outputs);

	return 0;
}

void TVideo::freeFilters(bool isVideoFilter)
{
	if (isVideoFilter)
	{
		if (mVFilterInitialized)
		{
			mVFilterInitialized = false;

			if (mVFilterBufferSrcCtx)
			{
				avfilter_free(mVFilterBufferSrcCtx);
				mVFilterBufferSrcCtx = NULL;
			}
			if (mVFilterBufferSinkCtx)
			{
				avfilter_free(mVFilterBufferSinkCtx);
				mVFilterBufferSinkCtx = NULL;
			}
			if (mVFilterGraph)
			{
				avfilter_graph_free(&mVFilterGraph);
				mVFilterGraph = NULL;
			}
		}
	}
	else
	{
		if (mAFilterInitialized)
		{
			mAFilterInitialized = false;

			if (mAFilterBufferSrcCtx)
			{
				avfilter_free(mAFilterBufferSrcCtx);
				mAFilterBufferSrcCtx = NULL;
			}
			if (mAFilterBufferSinkCtx)
			{
				avfilter_free(mAFilterBufferSinkCtx);
				mAFilterBufferSinkCtx = NULL;
			}
			if (mAFilterGraph)
			{
				avfilter_graph_free(&mAFilterGraph);
				mAFilterGraph = NULL;
			}
		}
	}
}
#endif

void TVideo::SetVideoFilter(const std::string& filterDesc)
{
	if (filterDesc == mVFilterDesc)
		return;

	// if (mVFilterInitialized)
	//	freeFilters(true);

	mVFilterDesc = filterDesc;
}

void TVideo::SetAudioFilter(const std::string& filterDesc)
{
	if (filterDesc == mAFilterDesc)
		return;

	if (!mHasAudio || !mAudioInitialized)
		return;

	// if (mAFilterInitialized)
	//	freeFilters(false);

	mAFilterDesc = filterDesc;
}

void TVideo::FlushBuffers(AVCodecContext* videoCtx, AVCodecContext* audioCtx)
{
	if (videoCtx)
		avcodec_flush_buffers(videoCtx);
	if (audioCtx)
		avcodec_flush_buffers(audioCtx);

	// mToFreeVideoBuffer.Lock();
	mVideoBuffer.Lock();
	// mToFreeVideoBuffer->insert(mToFreeVideoBuffer->end(), mVideoBuffer->begin(), mVideoBuffer->end());
	size_t videoDataGarbage = mVideoBuffer->size();
	mVideoBuffer->clear();
	mVideoBuffer.Unlock();
	// size_t videoDataGarbage = mToFreeVideoBuffer->size();
	// mToFreeVideoBuffer.Unlock();

	// mToFreeAudioBuffer.Lock();
	mAudioBuffer.Lock();
	// mToFreeAudioBuffer->insert(mToFreeAudioBuffer->end(), mAudioBuffer->begin(), mAudioBuffer->end());
	size_t audioDataGarbage = mAudioBuffer->size();
	mAudioBuffer->clear();
	mAudioBuffer.Unlock();
	// size_t audioDataGarbage = mToFreeAudioBuffer->size();
	// mToFreeAudioBuffer.Unlock();

	if (!GetFilename().empty())
		pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Cleared all data [video %d and audio %d] of %s", videoDataGarbage, audioDataGarbage,
		               GetFilename().c_str());
}

void TVideo::UpdateGraphics(Uint32 time)
{
	bool bPlaysNormally = !IsPaused();
	bool bPauseAtEnd = IsPaused();
	if (mWasSeekingTo >= 0)
	{
		mElapsedMs = mWasSeekingTo.exchange(-1);
		bPauseAtEnd = mPauseAfterSeek;
		bPlaysNormally = true;
		SetAudioTime(-1);
	}

	size_t elapsedMs = mElapsedMs + (time - mPrevUpdateTime);    // koliko casa je preteklo od prejsnjega kroga
	mPrevUpdateTime = time;

	if (!((IsLoading() && mDisplayedFrame == -1) || bPlaysNormally))
		return;

	if (bPlaysNormally)
	{
		bool playing = (mHasAudio && mAudioInitialized && SDL_GetAudioDeviceStatus(mAudioDevice) == SDL_AUDIO_PLAYING);
		mLastAudioTime.Lock();
		if (playing && mLastAudioTime->LastUpdate > 0 && mLastAudioTime->Timestamp >= 0)    // ko imamo audio, upostevamo njegov cas
		{
			elapsedMs = mLastAudioTime->Timestamp + (time - mLastAudioTime->LastUpdate);
			mElapsedMs = elapsedMs;
		}
		mLastAudioTime.Unlock();
	}

	std::shared_ptr<VideoData> LatestFrame;

	mVideoBuffer.Lock();
	// mToFreeVideoBuffer.Lock();
	while (!mVideoBuffer->empty())
	{
		std::shared_ptr<VideoData> video = mVideoBuffer->front();
		if (video)
		{
			if (mElapsedMs < video->pts)
				break;

			LatestFrame = video;
			if (LatestFrame->frame_idx == (mVideoFrames - 1) && mLoopVideo)
			{
				mElapsedMs -= std::min(mElapsedMs, (size_t)std::round(std::max(0., mDurationMs - (1000. / mFrameRate))));
				SetAudioTime(-1);
				bPlaysNormally = false;
			}
			// mToFreeVideoBuffer->push_back(video);
		}

		mVideoBuffer->pop_front();
	}
	// mToFreeVideoBuffer.Unlock();

	const bool bIsEmpty = mVideoBuffer->empty();
	mVideoBuffer.Unlock();

	SetPaused(bPauseAtEnd);

	if (bPlaysNormally && !IsPaused())
		mElapsedMs = elapsedMs;

	if (!LatestFrame)
		return;

	if (LatestFrame->frame)
	{
		size_t newDimension = 1 << (int)std::ceil(std::log2(std::max(LatestFrame->frame->width, LatestFrame->frame->height)));
		if (!pTargetTexture || pTargetTexture->w != newDimension)
		{
			if (pTargetTexture)
				GPU_FreeImage(pTargetTexture);

			setSrcRect(Rectangle(0, 0, LatestFrame->frame->width, LatestFrame->frame->height));

			pTargetTexture = GPU_CreateImage(newDimension, newDimension, GPU_FormatEnum::GPU_FORMAT_RGB);
			GPU_SetBlendMode(pTargetTexture, GPU_BlendPresetEnum::GPU_BLEND_NORMAL);
		}

		GPU_Rect rect = { 0.f, 0.f, (float)LatestFrame->frame->width, (float)LatestFrame->frame->height };
		GPU_UpdateImageBytes(pTargetTexture, &rect, LatestFrame->frame->data[0], LatestFrame->frame->linesize[0]);
	}

	mDisplayedFrame = LatestFrame->frame_idx;
	mVideoTime = LatestFrame->pts;

	if (mLoadFinished && bIsEmpty)    // nitka se je koncala, in nimamo vec tekstur () -> konec videa
		SetPaused(true);
}

void TVideo::createWaveformGraph()
{
	int width = getWidth();
	int height = 201;

	std::string cmd = "rm /tmp/wavform.png; ffmpeg -y -hide_banner -loglevel panic -i \"" + mFilepath.string() +
	                  "\" -filter_complex \"aformat=channel_layouts=mono,showwavespic=s=" + std::to_string(width) + "x" + std::to_string(height) +
	                  ":colors=cyan\" -frames:v 1 /tmp/wavform.png";    //: split_channels=1
	std::system(cmd.c_str());

	pWaveformTexture = Image::GetImage("/tmp/wavform.png");
	if (!pWaveformTexture)
	{
		// odpremo bin video file
		MemoryStream data;
		if (decodeBinFile(mFilepath, data) && data.Buffer.size())
		{
			std::system("rm /tmp/video.mp4");

			FILE* decodedFile = fopen("/tmp/video.mp4", "wb");
			if (decodedFile)
			{
				// fseek(decodedFile, 0, SEEK_SET);
				fwrite(data.Buffer.data(), 1, data.Buffer.size(), decodedFile);
				fclose(decodedFile);

				cmd = "rm /tmp/wavform.png; ffmpeg -y -hide_banner -loglevel panic -i \"/tmp/video.mp4\" -filter_complex \"aformat=channel_layouts=mono,showwavespic=s=" +
				      std::to_string(width) + "x" + std::to_string(height) + ":colors=cyan\" -frames:v 1 /tmp/wavform.png";    //: split_channels=1
				std::system(cmd.c_str());

				pWaveformTexture = Image::GetImage("/tmp/wavform.png");
				mWaveformRect = Rectangle(0, getHeight() - height, width, height);
			}
		}
	}
	else
		mWaveformRect = Rectangle(0, getHeight() - height, width, height);
}

void TVideo::drawLogic(Graphics* graphics, float deltaTime)
{
	const Uint64 now = SDL_GetTicks64();

	if (mShowWaveform && !pWaveformTexture)
		createWaveformGraph();

	if (IsPaused() && mPauseTime > 0 && MyUtils::CompareTimes(now, mPauseTime))
	{
		mPauseTime = -1;
		Play();
	}

	UpdateGraphics(now);

#if !defined(LADY_ROULETTE)
	if (mDisplayedFrame >= 0 && mVideoDecodeTimes.size() > 0)
	{
		mAverageVideoFrameDecodeTime = float(std::accumulate(mVideoDecodeTimes.begin(), mVideoDecodeTimes.end(), 0)) / mVideoDecodeTimes.size();
	}
#endif

	TGuiComponent::drawLogic(graphics, deltaTime);
}

void TVideo::draw(Graphics* graphics)
{
	if (pTargetTexture)
		graphics->drawTexture(pTargetTexture, getSrcRect(), Rectangle(0, getSize()));

#if !defined(LADY_ROULETTE)
	drawWaveform(graphics);
#endif

	TGuiComponent::draw(graphics);
}

void TVideo::drawWaveform(Graphics* graphics)
{
	if (mShowWaveform)
	{
		graphics->drawImage2(pWaveformTexture, Rectangle(), mWaveformRect);
		// draw middle line
		graphics->drawLine(Cyan, mWaveformRect.x(), mWaveformRect.y() + mWaveformRect.height() / 2, mWaveformRect.width(),
		                   mWaveformRect.y() + mWaveformRect.height() / 2);

		// draw current time line
		float dx = std::max(1.f, (GetVideoTime() / (float)mDurationMs) * mWaveformRect.width());
		float dh = 100;
		float ty = mWaveformRect.y() + (mWaveformRect.height() - dh) / 2;
		graphics->drawLine(Red, mWaveformRect.x() + dx, ty, mWaveformRect.x() + dx, ty + dh, 2);

		// draw mouse line
		if (mMouseX >= 0)
		{
			dh = mWaveformRect.height();
			ty = mWaveformRect.y();

			graphics->drawLine(Green, mWaveformRect.x() + mMouseX, ty, mWaveformRect.x() + mMouseX, ty + dh);
		}
	}
}

std::shared_ptr<AudioData> TVideo::GetAudioData()
{
	mAudioBuffer.Lock();
	if (mAudioBuffer->empty())
	{
		mAudioBuffer.Unlock();
		return NULL;
	}

	auto queuedItem = mAudioBuffer->front();
	mAudioBuffer->pop_front();

	// mToFreeAudioBuffer.Lock();
	// mToFreeAudioBuffer->push_back(queuedItem);
	// mToFreeAudioBuffer.Unlock();

	mAudioBuffer.Unlock();
	return queuedItem;
}

void TVideo::SetAudioTime(int time)
{
	mLastAudioTime.Lock();
	mLastAudioTime->Timestamp = time;
	mLastAudioTime->LastUpdate = SDL_GetTicks64();
	mLastAudioTime.Unlock();
}

void TVideo::SeekTo(size_t time_ms, bool pause_after_seek)
{
	mDestTime = time_ms;
	mPauseAfterSeek = pause_after_seek;
	mHasSeekOperationWaiting = true;
}

bool TVideo::IsLoading() const
{
	return mVideoParseThread.joinable();
}

void TVideo::_Seek(AVFormatContext* formatCtx, int streamId)
{
	// zaokrozimo na celi frame, zaradi ffmepg seek-a
	if (mDestTime > 0)
	{
		float frame_ms = (1000. / mFrameRate);
		int selected_frame = std::round(mDestTime / frame_ms);
		mDestTime = selected_frame * frame_ms;

		pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t\t[AVI] Seeking video to %ldms.", mDestTime.load());
	}

	int seek_flag = (/*AVSEEK_FLAG_FRAME |*/ AVSEEK_FLAG_BACKWARD);
	int64_t seek_target = std::round(double(1e-3 * std::min(mDestTime.load(), (int64_t)mDurationMs) * formatCtx->streams[streamId]->time_base.den) /
	                                 formatCtx->streams[streamId]->time_base.num);

	int ret = av_seek_frame(formatCtx, streamId, seek_target, seek_flag);
	if (ret < 0)
		printf("%s: error while seeking\n", formatCtx->url);

	mFirstSeekOutput = true;
	mWasSeekingTo = -1;
	bSeekDueToLoop = false;
}

AudioData::~AudioData()
{
	if (buffer)
	{
		av_freep(&buffer);
	}
}

VideoData::~VideoData()
{
	if (frame)
	{
		// av_frame_unref(frame);
		av_frame_free(&frame);

		frame = NULL;
	}
}
