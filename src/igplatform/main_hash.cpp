#include <math.h>

#include <cstdio>
#include <cstring>
#include <ctime>
#include <fstream>
#include <string>
#include <vector>

#include "THash.h"

#undef main

void UnMix(void* bytes, size_t len)
{
	const int* remapTable = (len == 1U) ? singleRemaps : remaps;

	uint32_t origBytes = (len == 1U) ? (uint32_t)(*(unsigned char*)bytes) : *(uint32_t*)bytes;
	uint32_t workingBytes = 0U;
	uint8_t* changeIdx = new uint8_t[len];
	const size_t len8 = len * 8;
	for (int i = HASH_NUM_MIXERS - 1; i >= 0; i--)
	{    // Reverse order
		int changeO = mixerShiftOffsets[i] % len8;
		uint8_t changedByte;

		// Ushift the bytes using the far side of the shift vector
		for (size_t byteN = 0; byteN < len; byteN++)
		{
			changeIdx[byteN] = i * HASH_BLOCK_SIZE_BYTES + byteN + len;
			changeIdx[byteN] = (changeO < 0) ? (255 - changeIdx[byteN] + changeO) : (changeIdx[byteN] + changeO);
			changedByte = ((uint8_t)((origBytes >> (byteN * 8)) & 0xFF));
			changedByte = (changedByte % 2) ? (changedByte - 0x14) : (changedByte + 0xC8);
			changedByte = changedByte ^ randBytes[255 - changeIdx[byteN]];
			workingBytes = workingBytes | (((uint32_t)changedByte) << (byteN * 8));
		}
		origBytes = workingBytes;
		workingBytes = 0U;

		// Rearrange the bits
		int remapped;
		int mapO = mixerMapOffsets[i] % len8;
		for (size_t bit = 0; bit < len8; bit++)
		{
			remapped = (mapO < 0) ? (len8 - 1 + mapO - bit) : (mapO + bit);
			remapped = (remapped + len8) % len8;
			if ((origBytes & (1U << remapTable[remapped])) == 0)
				continue;    // skip this bit, it's zero
			workingBytes = workingBytes | (1U << bit);
		}
		origBytes = workingBytes;
		workingBytes = 0U;

		// Unshift the bytes a second time
		for (size_t byteN = 0; byteN < len; byteN++)
		{
			changedByte = (uint8_t)((origBytes >> (byteN * 8)) & 0xFF);
			changedByte = (changedByte % 2) ? (changedByte - 0x3A) : (changedByte + 0x62);
			changedByte = changedByte ^ randBytes[changeIdx[byteN]];
			workingBytes = workingBytes | (((uint32_t)changedByte) << (byteN * 8));
		}
		origBytes = workingBytes;
		workingBytes = 0U;
	}
	delete[] changeIdx;
	if (len == 1U)
	{
		*(unsigned char*)bytes = (unsigned char)origBytes;
	}
	else
	{
		*(uint32_t*)bytes = origBytes;
	}
}

#ifdef PARALLEL
#include <execution>
#endif

void UnhashKey(void* keyData, size_t len)
{
	size_t leftOver = len % HASH_BLOCK_SIZE_BYTES;
	if (leftOver >= 2U)
		UnMix((char*)keyData + len - 1, 1);
	if (leftOver == 3U)
		UnMix((char*)keyData + len - 2, 1);
#ifdef PARALLEL
	uint32_t* blocks = (uint32_t*)keyData;
	std::for_each(std::execution::par_unseq, blocks, blocks + (len >> 2), [](uint32_t& block) { UnMix(&block, HASH_BLOCK_SIZE_BYTES); });
#else
	for (size_t blocks = 0; blocks < len / HASH_BLOCK_SIZE_BYTES; blocks++) UnMix((char*)keyData + blocks * HASH_BLOCK_SIZE_BYTES, HASH_BLOCK_SIZE_BYTES);
#endif

	if (leftOver)
		UnMix(keyData, 1);

	// Premix is disabled for length 1
	if (len == 1)
		return;

	const int power = (int)ceil(log2((double)len) / 8);
	// Reorganize the bytes
	unsigned char* byteArray = (unsigned char*)keyData;
	// Do the inverse of HashKey
	for (int moveIters = HASH_NUM_PREMIX_ITERATIONS - 1; moveIters >= 0; moveIters--)
	{
		// Un-swap positions
		for (int current = len - 1; current >= 0; current--)
		{
			uint8_t swapBase = (current / 5) + 2 + (current / 3) - (current % 3) - (current / 4) * ((current % 3) / 2) + len + moveIters - 1;
			uint64_t swap = 1U;
			for (int p = 0; p < power; p++)
			{
				swap *= randBytes[(randBytes[swapBase] + randBytes[255 - swapBase]) / 2];
				swapBase += 1 + p;
			}
			swap = swap % len;
			std::swap(byteArray[current], byteArray[swap]);
		}


		if (len < 512)
		{
			// Create order and diff tables
			unsigned char diffTable[len];
			for (size_t iterBytes = 1; iterBytes < len; iterBytes++) diffTable[iterBytes] = byteArray[iterBytes] - byteArray[iterBytes - 1];
			diffTable[0] = 255 - byteArray[len - 1] + 1 + byteArray[0];
			unsigned char diffRanges[2] = { 255U, 0U };
			for (size_t byt = 0; byt < len; byt++)
			{
				if (diffRanges[0] > diffTable[byt])
					diffRanges[0] = diffTable[byt];
				if (diffRanges[1] < diffTable[byt])
					diffRanges[1] = diffTable[byt];
			}

			// Un-shift
			uint8_t commonShift = randBytes[(diffRanges[1] - diffRanges[0] + (diffRanges[1] - diffRanges[0]) / 4) & 0xff];
			for (size_t current = 0; current < len; current++) byteArray[current] = byteArray[current] - commonShift;
		}


		// XOR
		for (size_t current = 0; current < len; current++)
		{
			size_t xorIdx = 2 + (current / 3) - (current % 3) - (current / 4) * ((current % 3) / 2) + len + moveIters - 1;
			xorIdx = ((xorIdx << (current % 3)) ^ (len + current + moveIters)) % HASH_BLOCK_SIZE_BITS;
			xorIdx = (1U << moveIters) + remaps[HASH_BLOCK_SIZE_BITS - 1 - remaps[xorIdx]];
			byteArray[current] = byteArray[current] ^ randBytes[xorIdx];
		}
	}
}

void printData(const void* data, size_t size)
{
	for (size_t i = 0; i < size; i++)
	{
		if (i)
			printf(" ");
		printf("%02X", *((const unsigned char*)data + i));
	}
}

char* readFile(const std::string& location, size_t& outLen)
{
	// open file
	std::ifstream infile(location);

	if (!infile.is_open())
		return NULL;

	// get length of file
	infile.seekg(0, infile.end);
	outLen = infile.tellg();
	infile.seekg(0, infile.beg);

	char* buffer = new char[outLen];

	// read file
	infile.read(buffer, outLen);

	// Close file
	infile.close();

	return buffer;
}

int writeFile(const std::string& location, char* data, size_t length)
{
	std::ofstream outFile(location);

	if (!outFile.is_open())
		return -1;

	outFile.clear();

	outFile.write(data, length);

	outFile.close();
	return 0;
}

#include <chrono>

int main(int const argc, const char** const argv)
{
	if (argc > 3)
	{
		string command(argv[1]);
		std::vector<string> options;
		std::vector<string> args;
		bool isFile = false;
		for (int i = 2; i < argc; i++)
		{
			string argValue(argv[i]);
			if (argValue.length() == 2 && argValue[0] == '-' && argValue[1] != '-')
			{
				switch (argValue[1])
				{
					case 'f': options.push_back("file"); break;
					default: printf("Unknown option specifier %c\n", argValue[1]); return 0;
				}
			}
			else if (argValue.length() > 2 && argValue.substr(0, 2) == "--")
			{
				options.push_back(argValue.substr(2, argValue.length() - 2));
			}
			else
			{
				args.push_back(argValue);
			}
		}
		for (size_t opt = 0; opt < options.size(); opt++)
		{
			if (options[opt] == "file")
			{
				isFile = true;
			}
			else
			{
				printf("Unknown option \"%s\"\n", options[opt].c_str());
				return 0;
			}
		}
		if (isFile && args.size() < 3 && (args.size() % 2 != 1))
		{
			printf("Invalis number of arguments.\n\tFile option requires 1 + 2n arguments.\n\t<cycles> <sourceFile_n> <outputFile_n>\n");
			return 0;
		}
		else if (!isFile && args.size() < 2)
		{
			printf("Invalis number of arguments.\n\tHash requires at least 2 arguments.\n\t<cycles> <string to (un)hash>\n");
			return 0;
		}
		int repeat = atoi(args[0].c_str());
		for (size_t i = 1; i < args.size(); i++)
		{
			auto begin = std::chrono::steady_clock::now();
			size_t len = 0;
			char* data;
			if (isFile)
			{
				data = readFile(args[i], len);
				if (data == NULL)
				{
					printf("Could not open file %s\n", args[i].c_str());
					return 0;
				}
			}
			else
			{
				len = args[i].length();
				data = new char[len + 1];
				strcpy(data, args[i].c_str());
				data[len] = '\0';
			}
			printf("%d: Running %s %i %s on data of size %lu\n", (int)i, command.c_str(), repeat, repeat == 1 ? "time" : "times", len);
			if (command == "hash" || command == "test")
			{
				for (int n = 0; n < repeat; n++) HashKey(data, len);
			}
			else if (command == "unhash")
			{
				for (int n = 0; n < repeat; n++) UnhashKey(data, len);
			}
			else
			{
				printf("Procedure %s not recognized!\n", command.c_str());
				printf("Procedures: %s\t%s\t%s\n", "hash", "unhash", "test");
				delete[] data;
				return 0;
			}
			if (!isFile && command != "test")
			{
				printf("Source string:\t%s\nSource bytes:\t", args[i].c_str());
				printData(args[i].c_str(), len);
				printf("\nBytes after %s:\t", command.c_str());
				printData(data, len);
				printf("\nHex string after %s:\t%s\nString after %s:\t%s\n", command.c_str(), bytesToHex((const unsigned char*)data, len).c_str(), command.c_str(),
				       (const char*)data);
			}
			else if (!isFile && command == "test")
			{
				printf("Source string:\t%s\nSource bytes:\t", args[i].c_str());
				printData(args[i].c_str(), len);
				printf("\nBytes after hash:\t");
				printData(data, len);
				printf("\nHex string after hash:\t%s\nString after hash:\t%s\n", bytesToHex((const unsigned char*)data, len).c_str(), (const char*)data);
				for (int n = 0; n < repeat; n++) UnhashKey(data, len);
				printf("Bytes after unhash:\t");
				printData(data, len);
				printf("\nHex string after unhash:\t%s\nString after unhash:\t%s\n", bytesToHex((const unsigned char*)data, len).c_str(), (const char*)data);
			}
			else
			{
				if (command == "test")
				{
					for (int n = 0; n < repeat; n++) HashKey(data, len);
				}
				if (writeFile(args[++i], data, len))
				{
					delete[] data;
					printf("Could not write to file %s\n", args[i].c_str());
					return 0;
				}
			}
			delete[] data;
			auto end = std::chrono::steady_clock::now();
			double elapsed_secs = std::chrono::duration_cast<std::chrono::microseconds>(end - begin).count() * 1e-6;
			printf("===============TOOK===============\n");
			printf("%fms\n", elapsed_secs * 1e3);
			printf("===============DONE===============\n");
		}
	}
	else
	{
		printf("Hash needs at least 3 arguments!\n");
		printf("Usage: hash_unhash [hash/unhash/test] (options) [number of cycles] {hash arguments}\n");
		printf("Options:\n\t--file or -f\tArguments are [source filename] [dest filename]\n");
		printf("\tno options\tArguments are [string1] [string2] [string3] [...]\n");
	}
	return 0;
}
