#include "InfoPage.h"

#include "TotallyTemporaryDesignTokens.h"
#include "YUtils.h"

DEFINE_LOG_CATEGORY(InfoPageMenuLog, "InfoPageMenu")

InfoPage::InfoPage() : mDefaultPageIdCounter(0), mCurrentLanguage(pApp->Language())
{
	setId("user-info-page-menu");

	pCommander = dynamic_cast<TPlatformCommander*>(pApp->GetModuleByName("Commander"));

	auto* generalLayout = setLayout<GridLayout>();
	generalLayout->AddColumns({ { .Size = 130.f, .bAbsoluteInPixels = true }, {} });

	setFocusMode(EFocusMode::Accept);

	mGeneralInfoNavigationContainer = new LayoutContainer();
	mGeneralInfoNavigationContainer->setId("general-info-navigation-container");

	mGeneralInfoNavigationLayout = mGeneralInfoNavigationContainer->setLayout<StackLayout>();
	mGeneralInfoNavigationLayout->Direction = EStackLayoutDirection::VERTICAL;
	mGeneralInfoNavigationLayout->Alignment = EChildAlignment::Stretch;
	mGeneralInfoNavigationLayout->Padding.X() = 10.f;

	add(mGeneralInfoNavigationContainer);
	generalLayout->Set(mGeneralInfoNavigationContainer, GridLayout::GridPosition(sVector2D(0, 0), sVector2D(1), EChildAlignment::Stretch));

	mGeneralInfoContentContainer = new LayoutContainer();
	mGeneralInfoContentContainer->setId("general-info-content-container");
	mGeneralInfoContentContainer->bResizeToFitLayout.Y() = true;

	mGeneralInfoContentLayout = mGeneralInfoContentContainer->setLayout<StackLayout>();
	mGeneralInfoContentLayout->Direction = EStackLayoutDirection::VERTICAL;
	mGeneralInfoContentLayout->bUsePaddingBeforeFirstAndAfterLast = true;
	mGeneralInfoContentLayout->Alignment = EChildAlignment::Stretch;

	add(mGeneralInfoContentContainer);

	mGeneralRadioButtonManageContainer = new LayoutContainer();
	mGeneralRadioButtonManageContainer->bResizeToFitLayout = { true, false };
	mGeneralRadioButtonManageContainer->setHeight(64);
	mGeneralRadioButtonManageContainer->setId("general-radio-button-group");
	mGeneralRadioButtonManageContainer->Background = tempDesignTokens::LobbyControlsBackground;
	mGeneralRadioButtonManageContainer->OutlineStyle = Outline(Transparent, 0_px, 1_rem, 1_px);
	mGeneralRadioButtonManageContainer->setVisible(false);

	auto* generalRadioButtonManageLayout = mGeneralRadioButtonManageContainer->setLayout<StackLayout>();
	generalRadioButtonManageLayout->Padding = { 8, 0 };
	generalRadioButtonManageLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	generalRadioButtonManageLayout->Alignment = EChildAlignment::Center;
	generalRadioButtonManageLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	mGeneralInfoContentLayout->setPaddingFor(mGeneralRadioButtonManageContainer, { 0, 80, 0, 0 });
	mGeneralInfoContentLayout->setAlignmentFor(mGeneralRadioButtonManageContainer, EChildAlignment::Center);

	mGeneralInfoContentContainer->add(mGeneralRadioButtonManageContainer);

	auto* generalRulesLabel = new Label();
	generalRulesLabel->setId("info-label");
	generalRulesLabel->setCaption(LocalizedMessage(INFO_STRING));
	generalRulesLabel->mTypography = tempDesignTokens::MainFont_600_24;
	generalRulesLabel->TextColor = tempDesignTokens::GoldTextColor;
	generalRulesLabel->setAlignment(align::LEFT_CENTER);
	generalRulesLabel->setResizeMode(Label::AUTOSIZE);
	generalRulesLabel->setPadding(Vector2D(20, 20));

	mGeneralInfoContentContainer->add(generalRulesLabel);

	generalLayout->Set(mGeneralInfoContentContainer, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(1), EChildAlignment::Stretch));

	std::vector<TModernButton*> radioButtons;
}

void InfoPage::LoadInfo(const size_t slotID)
{
	// This code is not used right now because a game's info is contained in the game itself. It was kept only for future use if the design for info pages is changed.

	// if (slotID == GENERAL_SETTINGS)
	// {
	// 	CreateInfoContent(slotID, LocalizedMessage(GENERAL_STRING).Get());
	// }
	// else
	// {
	// 	pCommander->GetGameState(
	// 	  slotID,
	// 	  [this, slotID](const GameRunContext& ctx) {
	// 		  if (!ctx.RunningGame)
	// 			  return;
	//
	// 		  mGeneralRadioButtonManageContainer->setVisible(true);
	// 		  CreateInfoContent(slotID, ctx.RunningGame->Info().Config.GameConfig.DisplayName);
	// 	  },
	// 	  false);
	// }

	CreateInfoContent(slotID, LocalizedMessage(GENERAL_STRING).Get());
	SelectInfo(slotID);
}

void InfoPage::SelectInfo(const size_t slotID)
{
	auto it = InfoMenuContentMap.find(slotID);

	if (it != InfoMenuContentMap.end())
	{
		if (slotID != GENERAL_SETTINGS)
			mActiveGameIndex = it->first;

		it->second.radioButton->SetSelected(true);
		for (const auto& content : InfoMenuContentMap | std::views::values)
		{
			if (content.radioButton != it->second.radioButton)
			{
				content.radioButton->SetSelected(false);
			}
		}
	}
}

void InfoPage::RemoveInfo(const size_t slotID)
{
	const auto itInfoPage = InfoMenuContentMap.find(slotID);
	if (itInfoPage != InfoMenuContentMap.end())
	{
		mGeneralRadioButtonManageContainer->remove(itInfoPage->second.radioButton);
		mGeneralInfoContentContainer->remove(itInfoPage->second.contentScrollArea);
		mGeneralInfoNavigationContainer->remove(itInfoPage->second.navigationContainer);
		InfoMenuContentMap.erase(itInfoPage);
	}

	if (InfoMenuContentMap.size() == 1)
	{
		mGeneralRadioButtonManageContainer->setVisible(false);
	}
}

void InfoPage::onLanguageChanged(const ELanguage lang)
{
	mCurrentLanguage = lang;

	auto infoMenuContentMapCopy = InfoMenuContentMap;

	// Reload all info pages with the new language
	for (const auto& slotID : infoMenuContentMapCopy | std::views::keys)
	{
		RemoveInfo(slotID);
		LoadInfo(slotID);
	}

	LayoutContainer::onLanguageChanged(lang);
}

void InfoPage::CreateInfoContent(const size_t slotID, const std::string& infoType)
{
	auto* infoPageNavigationContainer = new LayoutContainer();
	infoPageNavigationContainer->setId("info-navigation-container-" + infoType);
	infoPageNavigationContainer->bResizeToFitLayout.Y() = true;
	infoPageNavigationContainer->setVisible(false);

	auto* infoPageNavigationLayout = infoPageNavigationContainer->setLayout<StackLayout>();
	infoPageNavigationLayout->Direction = EStackLayoutDirection::VERTICAL;
	infoPageNavigationLayout->Alignment = EChildAlignment::Stretch;

	mGeneralInfoNavigationContainer->add(infoPageNavigationContainer);
	InfoMenuContentMap[slotID].navigationContainer = infoPageNavigationContainer;

	const auto infoPageContentScrollArea = new TScrollArea(mGeneralInfoContentContainer, 0, 0, 0, 0);
	infoPageContentScrollArea->setId("info-content-area-" + infoType);
	infoPageContentScrollArea->setScrollBarVisibility(TScrollArea::EScrollBarVisibility::Auto);
	infoPageContentScrollArea->setVisible(false);

	mGeneralInfoContentLayout->setSizeModeFor(infoPageContentScrollArea, EWidgetSizeMode::FILL_AVAILABLE_SPACE);
	mGeneralInfoContentLayout->setPaddingFor(infoPageContentScrollArea, { 0, 0, 24, 0 });

	InfoMenuContentMap[slotID].contentScrollArea = infoPageContentScrollArea;

	// TODO: Will be changed in future. For now, it's just a placeholder
	ParseInfoPages(slotID, infoType);

	auto* radioButton = new TModernButton(mGeneralRadioButtonManageContainer);
	SetRadioButton(slotID, radioButton, LocalizedMessage(infoType), infoType);

	InfoMenuContentMap[slotID].radioButton = radioButton;
}

void InfoPage::drawLogic(Graphics* graphics, float deltaTime)
{
	LayoutContainer::drawLogic(graphics, deltaTime);

	const auto it = InfoMenuContentMap.find(mActiveInfoIndex);
	if (it == InfoMenuContentMap.end())
		return;

	bool isGoldTextSet = false;
	const float scrollOffset = it->second.contentScrollArea->getScrollOffset();

	for (const auto& [infoPageContentWidget, infoPageNavigationBtn] : it->second.infoPageContentElements)
	{
		const bool isInfoPageVisible = infoPageContentWidget->getPosition().Y() - scrollOffset + infoPageContentWidget->getHeight() >= 0;
		if (isInfoPageVisible && !isGoldTextSet)
		{
			infoPageNavigationBtn->Text->TextColor = tempDesignTokens::GoldTextColor;
			isGoldTextSet = true;
		}
		else
		{
			infoPageNavigationBtn->Text->TextColor = White;
		}
	}
}

void InfoPage::SetRadioButton(const size_t slotID, TModernButton* radioButton, const LocalizedMessage& caption, const std::string& infoType)
{
	radioButton->Type = EModernButtonType::Radio;
	radioButton->Background = Transparent;
	radioButton->SelectedBrush = tempDesignTokens::LobbyControlsSelectedFilter;
	radioButton->bResizeToFitLayout = bVector2D(true, false);
	radioButton->setHeight(48);
	radioButton->RadioButtonGroup = "info-categories-radio-group";
	radioButton->Text->setCaption(caption);
	radioButton->Text->setPadding(Rectangle(40, 0, 40, 0));
	radioButton->Text->setResizeMode(Label::AUTOSIZE);
	radioButton->Text->TextColor = tempDesignTokens::GoldTextColor;
	radioButton->Text->setHeight(48);
	radioButton->Text->TextShadow = Shadow();
	radioButton->setId("category-" + infoType);
	radioButton->ClickSound = "button_press_soft.wav";
	radioButton->OutlineStyle = Outline(Transparent, 0_px, tempDesignTokens::RoundedCorner_1rem, 1_px);

	radioButton->OnSelectedChanged += [this, radioButton, slotID](const bool selected) {
		if (selected)
		{
			HandleRadioButtonSelected(radioButton, slotID);
		}
		else
		{
			HandleRadioButtonDeselected(radioButton, slotID);
		}
	};
}

void InfoPage::HandleRadioButtonSelected(TModernButton* radioButton, const size_t slotID)
{
	mActiveInfoIndex = slotID;

	radioButton->Text->TextColor = Black;
	radioButton->OutlineStyle = Outline(tempDesignTokens::GoldTextColor, 0_px, tempDesignTokens::RoundedCorner_1rem, 1_px);

	const auto it = InfoMenuContentMap.find(mActiveInfoIndex);
	if (it == InfoMenuContentMap.end())
		return;

	it->second.contentScrollArea->setVisible(true);
	it->second.radioButton->setVisible(true);

	if (!it->second.infoPageContentElements.empty())
	{
		it->second.navigationContainer->setVisible(true);
		it->second.contentScrollArea->scrollIntoViewTop(it->second.infoPageContentElements.front().first);
	}

	HideInactiveRadioButtons();
}

void InfoPage::HandleRadioButtonDeselected(TModernButton* radioButton, const size_t slotID)
{
	radioButton->Text->TextColor = tempDesignTokens::GoldTextColor;
	radioButton->OutlineStyle = Outline(Transparent, 0_px, tempDesignTokens::RoundedCorner_1rem, 1_px);
	InfoMenuContentMap[slotID].contentScrollArea->setVisible(false);
	InfoMenuContentMap[slotID].navigationContainer->setVisible(false);
}

void InfoPage::HideInactiveRadioButtons()
{
	for (auto& [id, content] : InfoMenuContentMap)
	{
		if (id != GENERAL_SETTINGS && id != mActiveGameIndex)
		{
			content.radioButton->setVisible(false);
		}
	}
}

void InfoPage::SetUpFillingEmptySpaceWidget(const size_t slotID, const std::string& infoType)
{
	if (InfoMenuContentMap.empty())
	{
		return;
	}

	auto* emptySpaceFillUpWidget = new Widget();
	emptySpaceFillUpWidget->setId("fill-up-space-container-" + infoType);
	InfoMenuContentMap[slotID].contentScrollArea->AddContent(emptySpaceFillUpWidget);

	InfoMenuContentMap[slotID].infoPageContentElements.back().first->OnDimensionChanged +=
	  [this, slotID, emptySpaceFillUpWidget](const Widget* w, [[maybe_unused]] const Rectangle& oldDim, [[maybe_unused]] const Rectangle& targetDim) {
		  const float heightDiff = InfoMenuContentMap[slotID].contentScrollArea->getHeight() - w->getHeight() -
		                           InfoMenuContentMap[slotID].contentScrollArea->ContentContainer()->layout<StackLayout>()->Padding.Y();

		  if (heightDiff > 0)
		  {
			  emptySpaceFillUpWidget->setHeight(heightDiff);
		  }
	  };
}

void InfoPage::ParseInfoPages(const size_t slotID, const std::string& infoType)
{
	// Right now there is implementation only for general info, but it can be easily extended for other info types in the future.
	std::filesystem::path infoPagesFilePath = pApp->GetContentDir() / "lang" / (std::string("General-Info-") + mCurrentLanguage._to_string() + ".json");

	if (!exists(infoPagesFilePath))
	{
		ELanguage defaultLanguage = ELanguage::English;
		infoPagesFilePath = pApp->GetContentDir() / "lang" / (std::string("General-Info-") + defaultLanguage._to_string() + ".json");
	}

	std::ifstream infoPagesFile(infoPagesFilePath);
	json infoPagesDescr;
	try
	{
		infoPagesFile >> infoPagesDescr;
	}
	catch (const std::exception& e)
	{
		TLOG(InfoPageMenuLog, Error, "Info pages file could not be parsed: %s", e.what());
		return;
	}

	for (auto& infoPageDescr : infoPagesDescr["pages"]) { CreateNewInfoPage(slotID, infoPageDescr, infoType); }

	SetUpFillingEmptySpaceWidget(slotID, infoType);
}

void InfoPage::CreateNewInfoPage(const size_t slotID, const json& infoPageDescr, const std::string& infoType)
{
	const std::string pageId = CreatePageId(infoPageDescr);

	auto* pageContainer = new LayoutContainer();
	pageContainer->setId(pageId + "-info-page-container-" + infoType);
	pageContainer->Background = Black.Fade(0.5f);
	pageContainer->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	pageContainer->bResizeToFitLayout.Y() = true;

	const std::string titleStr = infoPageDescr.contains("title") ? infoPageDescr["title"].get<std::string>() : "default_title";

	auto* infoPageNavigationBtn = CreateInfoPageNavigationBtn(slotID, pageId, pageContainer, titleStr, infoType);

	auto* pageLayout = pageContainer->setLayout<StackLayout>();
	pageLayout->Direction = EStackLayoutDirection::VERTICAL;
	pageLayout->Padding = Vector2D(10, 12);
	pageLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	auto* titleLabel = new Label();
	titleLabel->setId(pageId + "-info-title-label-" + infoType);
	titleLabel->setCaption(LocalizedMessage(titleStr));
	titleLabel->mTypography = tempDesignTokens::MainFont_600_20;
	titleLabel->TextColor = tempDesignTokens::GoldTextColor;
	titleLabel->setPadding(Vector2D(28, 10));
	titleLabel->setAlignment(align::LEFT_CENTER);
	titleLabel->setResizeMode(Label::AUTOSIZE);
	pageContainer->add(titleLabel);

	std::string subtitleStr;
	if (infoPageDescr.contains("subtitle"))
		subtitleStr = infoPageDescr["subtitle"].get<std::string>();

	if (!subtitleStr.empty())
	{
		auto* subtitleLabel = new Label();
		subtitleLabel->setId(pageId + "-info-subtitle-label-" + infoType);
		subtitleLabel->setCaption(LocalizedMessage(subtitleStr));
		subtitleLabel->mTypography = tempDesignTokens::MainFont_600_20;
		subtitleLabel->TextColor = White;
		subtitleLabel->setPadding(Vector2D(10, 10));
		subtitleLabel->setAlignment(align::LEFT_CENTER);
		subtitleLabel->setResizeMode(Label::AUTOSIZE);
		pageContainer->add(subtitleLabel);
	}

	if (infoPageDescr.contains("content"))
	{
		ParseContentEntities(pageId, pageContainer, infoPageDescr["content"]);
	}
	else
	{
		TLOG(InfoPageMenuLog, Debug, "Trying to process info page without content entities");
	}

	InfoMenuContentMap[slotID].contentScrollArea->AddContent(pageContainer);
	InfoMenuContentMap[slotID].contentScrollArea->ContentContainer()->layout<StackLayout>()->Padding = Vector2D(10.f, 20.f);
	InfoMenuContentMap[slotID].contentScrollArea->ContentContainer()->layout<StackLayout>()->Alignment = EChildAlignment::Stretch;

	InfoMenuContentMap[slotID].infoPageContentElements.emplace_back(pageContainer, infoPageNavigationBtn);
}

std::string InfoPage::CreatePageId(const json& infoPageDescr)
{
	std::string pageId;
	if (infoPageDescr.contains("id"))
	{
		pageId = infoPageDescr["id"].get<std::string>();
	}
	else
	{
		pageId = yutils::Format("default_page_id_%d", mDefaultPageIdCounter);
		mDefaultPageIdCounter++;
	}
	return pageId;
}

TStyleButton2* InfoPage::CreateInfoPageNavigationBtn(const size_t slotID, const std::string& pageId, LayoutContainer* parentContainer, const std::string& title,
                                                     const std::string& infoType)
{
	auto* infoPageNavigationBtn = new TStyleButton2(InfoMenuContentMap[slotID].navigationContainer, LocalizedMessage(title));
	infoPageNavigationBtn->setId(pageId + "-btn-" + infoType);
	infoPageNavigationBtn->setHeight(70.f);
	infoPageNavigationBtn->Text->setMaxNumberOfLines(2);
	infoPageNavigationBtn->Text->setAlignment(align::CENTER_CENTER);
	infoPageNavigationBtn->Text->setOrigin(0.5);
	infoPageNavigationBtn->Text->setWrap(true);
	infoPageNavigationBtn->Text->mTypography = tempDesignTokens::MainFont_400_16;
	infoPageNavigationBtn->OnPressed += [this, parentContainer, slotID]() {
		InfoMenuContentMap[slotID].contentScrollArea->scrollIntoViewTop(parentContainer, 0.1f);
	};

	InfoMenuContentMap[slotID].navigationContainer->layout<StackLayout>()->setPaddingFor(infoPageNavigationBtn, { 0, 24, 0, 24 });
	return infoPageNavigationBtn;
}

void InfoPage::ParseContentEntities(const std::string& pageId, LayoutContainer* parentContainer, const json& contentFieldDescr, const size_t rowPosition,
                                    const size_t colPosition)
{
	if (contentFieldDescr.is_array())
	{
		for (const auto& contentEntityDescr : contentFieldDescr) { CreateContentEntity(pageId, parentContainer, contentEntityDescr, rowPosition, colPosition); }
	}
	else
	{
		CreateContentEntity(pageId, parentContainer, contentFieldDescr, rowPosition, colPosition);
	}
}

void InfoPage::CreateContentEntity(const std::string& pageId, LayoutContainer* parentContainer, const json& contentEntityDescr, const size_t rowPosition,
                                   const size_t colPosition)
{
	if (!contentEntityDescr.contains("type"))
	{
		TLOG(InfoPageMenuLog, Debug, "Trying to draw untyped content");
		return;
	}

	const auto type = contentEntityDescr["type"].get<std::string>();
	Widget* contentEntity = nullptr;
	if (type == "grid")
		contentEntity = CreateGridContentEntity(pageId, contentEntityDescr);
	else if (type == "text")
		contentEntity = CreateTextContentEntity(pageId, contentEntityDescr);
	else if (type == "image")
		contentEntity = CreateImageContentEntity(pageId, contentEntityDescr, rowPosition, colPosition);

	if (contentEntity)
	{
		parentContainer->add(contentEntity);

		if (type == "grid" && contentEntityDescr.contains("width"))
		{
			SetContentEntityWidth(contentEntityDescr, contentEntity, rowPosition, colPosition);
		}

		SetContentEntityPosition(contentEntityDescr, contentEntity, rowPosition, colPosition);
	}
}

LayoutContainer* InfoPage::CreateGridContentEntity(const std::string& pageId, const json& contentEntityDescr)
{
	auto* contentEntityContainer = new LayoutContainer();
	contentEntityContainer->setId(pageId + "-grid-container");
	auto* contentEntityLayout = contentEntityContainer->setLayout<GridLayout>();
	contentEntityContainer->bResizeToFitLayout.Y() = true;

	const size_t rowSize = contentEntityDescr.contains("rows") ? contentEntityDescr["rows"].size() : 0;
	for (size_t rowIndex = 0; rowIndex < rowSize; rowIndex++)
	{
		const auto& row = contentEntityDescr["rows"][rowIndex];
		contentEntityLayout->AddRow({});

		const size_t columnSize = row.contains("columns") ? row["columns"].size() : 0;
		if (contentEntityLayout->Columns().size() < columnSize)
		{
			std::vector<GridLayout::FieldSize> deficientColumns(columnSize - contentEntityLayout->Columns().size());
			contentEntityLayout->AddColumns(deficientColumns);
		}

		float currentTotalWidthWeight = 0.f;
		std::set<size_t> autoColumns;

		for (size_t colIndex = 0; colIndex < columnSize; colIndex++)
		{
			const auto& column = row["columns"][colIndex];

			GatherGridContentEntityColumnsWeightsData(column, contentEntityLayout, colIndex, currentTotalWidthWeight, autoColumns);
			if (column.contains("content"))
			{
				ParseContentEntities(pageId, contentEntityContainer, column["content"], rowIndex, colIndex);
			}
			else
			{
				TLOG(InfoPageMenuLog, Debug, "Grid column content without any content");
			}
		}

		// Calculate width for auto-width columns
		if (!autoColumns.empty())
		{
			const float widthForAutoColumns = (1 - currentTotalWidthWeight) / static_cast<float>(autoColumns.size());
			for (auto& col : autoColumns) { contentEntityLayout->Columns()[col].Size = widthForAutoColumns; }
		}
	}

	return contentEntityContainer;
}

Icon* InfoPage::CreateImageContentEntity(const std::string& pageId, const json& contentEntityDescr, const size_t rowPosition, const size_t colPosition)
{
	const std::string imageSrcStr = contentEntityDescr.contains("src") ? contentEntityDescr["src"].get<std::string>() : "";
	const ImagePtr imgFromSrc = Image::GetImage(imageSrcStr);

	auto* imageBanner = new Icon();
	imageBanner->setId(pageId + "-img");

	if (rowPosition != std::string::npos && colPosition != std::string::npos)
	{
		imageBanner->ScaleMode = EScaleMode::ZOOM_TO_CONTAIN_ONLY_DOWN;
		imageBanner->Autosize = bVector2D(false, true);
	}
	else
	{
		imageBanner->Autosize = bVector2D(true);
	}
	imageBanner->setImage(imgFromSrc);

	return imageBanner;
}

Label* InfoPage::CreateTextContentEntity(const std::string& pageId, const json& contentEntityDescr)
{
	const std::string TextValueStr = contentEntityDescr.contains("value") ? contentEntityDescr["value"].get<std::string>() : "";

	Vector2D paddingVector(16, 10);
	if (contentEntityDescr.contains("padding-x"))
		paddingVector.X() = std::stof(contentEntityDescr["padding-x"].get<std::string>());
	if (contentEntityDescr.contains("padding-y"))
		paddingVector.Y() = std::stof(contentEntityDescr["padding-y"].get<std::string>());

	auto* textContentLabel = new Label();
	textContentLabel->setId(pageId + "-text-content");
	textContentLabel->setCaption(LocalizedMessage(TextValueStr));
	textContentLabel->TextColor = White;
	textContentLabel->setWrap(true);
	textContentLabel->setPadding(paddingVector);
	textContentLabel->setAlignment({ PositionToAlignment<EAlignment>(contentEntityDescr), EAlignment::Min });
	textContentLabel->setResizeMode(Label::NONE, Label::AUTOSIZE);
	SetFontDesignToken(contentEntityDescr, textContentLabel);

	return textContentLabel;
}

void InfoPage::GatherGridContentEntityColumnsWeightsData(const json& column, GridLayout* contentEntityLayout, size_t colPosition, float& currentTotalWidthWeight,
                                                         std::set<size_t>& autoColumns)
{
	if (!column.contains("width"))
	{
		autoColumns.insert(colPosition);
		return;
	}

	if (const auto colWidth = column["width"].get<std::string>(); colWidth != "auto")
	{
		contentEntityLayout->Columns()[colPosition].Size = std::stof(colWidth) / 100;
		currentTotalWidthWeight += contentEntityLayout->Columns()[colPosition].Size;
	}
	else
	{
		autoColumns.insert(colPosition);
	}
}

void InfoPage::SetContentEntityPosition(const json& contentEntityDescr, Widget* contentEntityWidget, const size_t rowPosition, const size_t colPosition)
{
	auto alignment = PositionToAlignment<EChildAlignment>(contentEntityDescr);
	auto* parentContainer = dynamic_cast<LayoutContainer*>(contentEntityWidget->getParent());

	if (!parentContainer)
	{
		TLOG(InfoPageMenuLog, Error, "Cannot set position for widget without parent");
		return;
	}


	const bool isContentEntityInGrid = rowPosition != std::string::npos && colPosition != std::string::npos;
	if (isContentEntityInGrid || dynamic_cast<Label*>(contentEntityWidget))
	{
		alignment = EChildAlignment::Stretch;
	}

	if (isContentEntityInGrid)
	{
		auto* parentGridLayout = dynamic_cast<GridLayout*>(parentContainer->layout());
		const auto contentAlignment = ChildAlignment2D({ alignment, EChildAlignment::Min });
		parentGridLayout->Set(contentEntityWidget, GridLayout::GridPosition(colPosition, rowPosition, 1, 1, contentAlignment));
	}
	else
	{
		auto* parentStackLayout = dynamic_cast<StackLayout*>(parentContainer->layout());
		parentStackLayout->setAlignmentFor(contentEntityWidget, alignment);
	}
}

void InfoPage::SetContentEntityWidth(const json& contentEntityDescr, Widget* contentEntityWidget, const size_t rowPosition, const size_t colPosition) const
{
	auto widthStr = contentEntityDescr["width"].get<std::string>();
	auto* parentContainer = dynamic_cast<LayoutContainer*>(contentEntityWidget->getParent());

	if (widthStr != "auto")
	{
		if (widthStr.back() == '%')    // Percentage width
		{
			widthStr.pop_back();
			float contentWidthPercentage = std::stof(widthStr) / 100.0f;
			parentContainer->OnDimensionChanged +=
			  [rowPosition, colPosition, contentEntityWidget, contentWidthPercentage](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
				  const float setUpWidth = (rowPosition != std::string::npos && colPosition != std::string::npos) ?
				                             dynamic_cast<LayoutContainer*>(w)->layout<GridLayout>()->GetFieldDimension(sVector2D(colPosition, rowPosition), 1).width() :
				                             w->getWidth();
				  contentEntityWidget->setWidth(setUpWidth * contentWidthPercentage);
			  };
		}
		else    // Px width
		{
			contentEntityWidget->setWidth(std::stof(widthStr));
		}
	}
	else    // Auto width
	{
		parentContainer->OnDimensionChanged += [rowPosition, colPosition, contentEntityWidget](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
			float setUpWidth = (rowPosition != std::string::npos && colPosition != std::string::npos) ?
			                     dynamic_cast<LayoutContainer*>(w)->layout<GridLayout>()->GetFieldDimension(sVector2D(colPosition, rowPosition), 1).width() :
			                     w->getWidth();
			contentEntityWidget->setWidth(setUpWidth);
		};
	}
}

void InfoPage::SetFontDesignToken(const json& contentEntityDescr, Label* textContentLabel)
{
	auto fontStyle = tempDesignTokens::MainFont_400_16;

	if (contentEntityDescr.contains("fontStyle"))
	{
		fontStyle = "global.usedFonts." + contentEntityDescr["fontStyle"].get<std::string>();
	}

	textContentLabel->mTypography = fontStyle;
}

template <typename AlignmentType>
AlignmentType InfoPage::PositionToAlignment(const json& contentEntityDescr)
{
	AlignmentType alignment = AlignmentType::Min;
	if (contentEntityDescr.contains("position"))
	{
		const std::string positionStr = contentEntityDescr["position"].get<std::string>();
		if (positionStr == "center")
		{
			alignment = AlignmentType::Center;
		}
		else if (positionStr == "right")
		{
			alignment = AlignmentType::Max;
		}
	}

	return alignment;
}