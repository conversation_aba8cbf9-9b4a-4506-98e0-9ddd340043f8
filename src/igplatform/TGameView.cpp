#include "TGameView.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"
#include "gui/cef/browser_window.h"

TGameView::TGameView()
{
	setId("TGameView");
	mLayout = setLayout<GridLayout>();

	mLayout->AddRow({ .Margin = { 0, 8 } });
	mLayout->AddRow({ .Margin = { 0, 8 } });
	mLayout->AddColumn({ .Margin = { 0, 8 } });
	mLayout->AddColumn({ .Margin = { 0, 8 } });

	// This should be set. In another case, CEF will crash when trying to draw the browser window.
	// TODO: Remove this once the bug is fixed.
	setSize(64);

	pCommander = dynamic_cast<TPlatformCommander*>(pApp->GetModuleByName("Commander"));

	for (size_t i = 0; i < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); ++i) { initializeGameView(i); }

	BrowserBorderedStyle = Outline(tempDesignTokens::Golden_Outline_Gradient, 2_px, tempDesignTokens::RoundedCorner_12, 1_px);

	BorderWidgetStyle = Outline(tempDesignTokens::GameViewOutlineColor, 2_px, tempDesignTokens::RoundedCorner_12, 1_px);

	Transition transition;
	transition.duration = 0.25s;
	transition.timingFunction = CubicBezier::EaseIn;
	mLayout->DimensionChangeTransition = transition;
}

void TGameView::initializeGameView(const size_t gameSlotID)
{
	TGameWindow gameWindow;

	gameWindow.gameWindowContainer = new LayoutContainer();
	gameWindow.gameWindowLayout = gameWindow.gameWindowContainer->setLayout<BoxLayout>();
	gameWindow.gameWindowLayout->DefaultBehavior.Alignment = EChildAlignment::Stretch;
	gameWindow.gameWindowContainer->setId("game-container-" + std::to_string(gameSlotID));
	gameWindow.gameWindowContainer->setVisible(false);

	gameWindow.addGameButton = new Button();
	auto brush = ImageBrush(Image::GetImage("bgTextureDark.png"));
	brush.tiling = true;
	gameWindow.addGameButton->Background = brush;
	gameWindow.addGameButton->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	auto* addLayout = gameWindow.addGameButton->setLayout<GridLayout>();
	addLayout->AddRows({ { .5f }, { .5f } });

	const auto icon = new Icon("addGame.png");
	icon->setId("icon");
	icon->ScaleFactor = 2.0f;
	icon->setSize(77);
	gameWindow.addGameButton->add(icon);
	addLayout->Set(icon, { 0, 0, 1, 1, ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Max) });

	auto* label = new Label(LocalizedMessage(ADD_GAME_STRING));
	label->setResizeMode(Label::AUTOSIZE);
	label->mTypography = tempDesignTokens::Inter_400_45;
	gameWindow.addGameButton->add(label);
	addLayout->Set(label, { 0, 1, 1, 1, ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Min) });

	gameWindow.addGameButton->OnPressed += [this]() {
		OnAddGamePressed();
	};
	gameWindow.gameWindowContainer->add(gameWindow.addGameButton);

	gameWindow.borderWidget = new Widget();
	gameWindow.borderWidget->OutlineStyle = BorderWidgetStyle.Value();
	// This container will be moved to the top of the game window container on game addition. It shouldn't receive any mouse and key inputs.
	gameWindow.borderWidget->setMouseInput(EventPropagateMode::NONE);
	gameWindow.borderWidget->setKeyInput(EventPropagateMode::NONE);

	gameWindow.gameWindowContainer->add(gameWindow.borderWidget);
	gameWindow.gameWindowLayout->PerWidgetBehaviors[gameWindow.borderWidget].Alignment = ChildAlignment2D(EChildAlignment::Stretch);

	InitializeTopBarGameView(gameSlotID, gameWindow);

	add(gameWindow.gameWindowContainer);

	Games[gameSlotID] = gameWindow;
}

void TGameView::AddGame(BrowserWindow* gameWindow, const size_t gameSlotID, const bool focusNewGame)
{
	if (!gameWindow)
		return;

	Games[gameSlotID].addGameButton->moveToBottom();
	Games[gameSlotID].browser = gameWindow;
	Games[gameSlotID].gameWindowContainer->add(gameWindow);
	Games[gameSlotID].gameWindowLayout->PerWidgetBehaviors[gameWindow].Padding = { 0, 56, 0, 0 };

	Games[gameSlotID].SlotPosition = getFirstFreePosition();

	Games[gameSlotID].borderWidget->moveToTop();

	AddTopBarGameTags(gameSlotID);

	Games[gameSlotID].borderWidget->OutlineStyle.AddModifier([this, gameSlotID](Outline& outline) {
		if (NumOfActiveGames > 1 && CurrentlyFocusedGame.value_or(std::string::npos) == gameSlotID)
			outline = BrowserBorderedStyle.Value();
		else
			outline = BorderWidgetStyle.Value();
	});

	if (focusNewGame)
	{
		CurrentlyFocusedGame = gameSlotID;
		dynamic_cast<TIGPlatformApp*>(pApp)->GameFocusChange(static_cast<int>(gameSlotID));
	}

	gameWindow->OnFocusedChanged += [this, gameSlotID](Widget* oldWidget, const bool focused) {
		if (focused && (!CurrentlyFocusedGame.has_value() || (CurrentlyFocusedGame.has_value() && gameSlotID != CurrentlyFocusedGame)))
		{
			CurrentlyFocusedGame = gameSlotID;
			dynamic_cast<TIGPlatformApp*>(pApp)->GameFocusChange(static_cast<int>(gameSlotID));
		}
	};

	++NumOfActiveGames;

	gameWindow->setVisible(false);
	gameWindow->bPerformLayoutIfInvisible = true;

	OnGameAdded();

	UpdateDrawLayout();

	OnGameViewModeChanged(NumOfActiveGames);
}

void TGameView::RemoveGame(const size_t slotID)
{
	if (slotID >= dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames())    // Max number of games slot reserved for game restore
		return;

	--NumOfActiveGames;

	Games[slotID].gameWindowContainer->remove(Games[slotID].browser);
	Games[slotID].browser = nullptr;
	Games[slotID].SlotPosition = -1;

	if (CurrentlyFocusedGame.value_or(std::string::npos) == slotID)    // removed one was selected
	{
		CurrentlyFocusedGame.reset();
		int newSelectedSlot = getFirstInitializedGame();
		if (newSelectedSlot >= 0)
		{
			CurrentlyFocusedGame = newSelectedSlot;
			dynamic_cast<TIGPlatformApp*>(pApp)->GameFocusChange(newSelectedSlot);
		}
	}

	if (NumOfActiveGames <= static_cast<uint8_t>(EGameViewLayout::DualView))    // reposition, so the next open game will be in the next slot position
	{
		int pos = 0;
		for (size_t i = 0; i < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); i++)
		{
			if (Games[i].SlotPosition >= 0)
			{
				Games[i].SlotPosition = pos;
				pos++;
			}
		}
	}

	OnGameRemoved(slotID);

	if (gamesEmpty())
	{
		OnAllGamesClosed();
	}

	UpdateDrawLayout();

	OnGameViewModeChanged(NumOfActiveGames);
}

bool TGameView::gamesEmpty() const
{
	for (size_t i = 0; i < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); i++)
	{
		if (Games[i].SlotPosition >= 0)
			return false;
	}
	return true;
}

int TGameView::getFirstInitializedGame() const
{
	for (size_t i = 0; i < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); i++)
	{
		if (Games[i].SlotPosition >= 0)
			return static_cast<int>(i);
	}

	return -1;
}

int TGameView::getFirstFreePosition() const
{
	const size_t games = dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames();
	bool usedPositions[MAX_NUM_GAMES_MULTIPLAY] = { false };

	// Mark all used positions
	for (size_t i = 0; i < games; i++)
	{
		if (Games[i].SlotPosition >= 0 && Games[i].SlotPosition < (int)games)
			usedPositions[Games[i].SlotPosition] = true;
	}

	// Find the first unused position
	for (size_t i = 0; i < games; i++)
	{
		if (!usedPositions[i])
			return static_cast<int>(i);
	}

	// If all positions are used, return the next available one
	// TLOG(LogApp, Critical, "All game positions are used!");
	return static_cast<int>(games);
}

void TGameView::SetSelectedActiveGame(const size_t slotID)
{
	CurrentlyFocusedGame = slotID;

	UpdateDrawLayout();
}

void TGameView::SetLobbyVisible(bool lobbyDrawn)
{
	if (bLobbyDrawn != lobbyDrawn)
	{
		bLobbyDrawn = lobbyDrawn;
		UpdateDrawLayout();
	}
}

void TGameView::SetChangeGameMode(bool changeGameModeEnabled)
{
	mChangeGameMode = changeGameModeEnabled;
}

const std::optional<size_t>& TGameView::GetActiveGame() const
{
	return CurrentlyFocusedGame;
}

void TGameView::UpdateDrawLayout()
{
	if (NumOfActiveGames == 0)
		return;

	const size_t games = dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames();
	std::array<sVector2D, MAX_NUM_GAMES_MULTIPLAY> usedPositions;
	std::array<sVector2D, MAX_NUM_GAMES_MULTIPLAY> spans;


	GetGamePositions(NumOfActiveGames, bLobbyDrawn, usedPositions, spans);


	int pos = getFirstFreePosition();
	for (size_t i = 0; i < games; i++)
	{
		TGameWindow& gameWindow = Games[i];
		if (gameWindow.SlotPosition >= 0)    // game is set and show it
		{
			gameWindow.gameWindowContainer->setVisible(true);
			gameWindow.addGameButton->setVisible(false);
			gameWindow.gameTopBar.gameTopBarContainer->setVisible(true);
			mLayout->Set(gameWindow.gameWindowContainer,
			             GridLayout::GridPosition(usedPositions.at(gameWindow.SlotPosition),
			                                      { spans.at(gameWindow.SlotPosition).X(), spans.at(gameWindow.SlotPosition).Y() }, EChildAlignment::Stretch));
		}
		else    // game is not set
		{
			if (NumOfActiveGames > static_cast<uint8_t>(EGameViewLayout::DualView))    // we have more than 2 games - show add game button
			{
				gameWindow.gameWindowContainer->setVisible(true);    // we need this so add game will be visible
				gameWindow.addGameButton->setVisible(true);
				gameWindow.gameTopBar.gameTopBarContainer->setVisible(false);
				mLayout->Set(gameWindow.gameWindowContainer,
				             GridLayout::GridPosition(usedPositions.at(pos), { spans.at(pos).X(), spans.at(pos).Y() }, EChildAlignment::Stretch));
				pos++;
			}
			else    // hide container
			{
				gameWindow.gameWindowContainer->setVisible(false);
			}
		}
	}

	markLayoutDirty();
}

void TGameView::GetGamePositions(const uint8_t numOfGames, const bool lobbyDrawn, std::array<sVector2D, MAX_NUM_GAMES_MULTIPLAY>& outPositions,
                                 std::array<sVector2D, MAX_NUM_GAMES_MULTIPLAY>& outSpan)
{
	outSpan = { { { 1, 1 }, { 1, 1 }, { 1, 1 }, { 1, 1 } } };
	outPositions = { { { 1, 0 }, { 1, 1 }, { 0, 0 }, { 0, 1 } } };

	// according to figma;
	// 1st game is top right or right in dual view when lobby isnt open, and on top when lobby is open while in dual view
	// 2nd game is on bottom right, left in dual view when lobby isnt open and bottom right when lobby is open while in Dual view
	// 3rd game slot is top left
	// 4th game slot is bottom left

	if (!lobbyDrawn)
	{
		if (numOfGames == static_cast<uint8_t>(EGameViewLayout::SingleView))
		{
			outSpan[0] = { 2, 2 };
			outPositions[0] = { 0, 0 };
		}
		else if (numOfGames == static_cast<uint8_t>(EGameViewLayout::DualView))
		{
			outSpan[0] = { 1, 2 };
			outSpan[1] = { 1, 2 };
			outPositions[0] = { 1, 0 };
			outPositions[1] = { 0, 0 };
		}
	}
	else
	{
		outPositions = { { { 1, 0 }, { 1, 1 }, { 0, 0 }, { 0, 1 } } };
		if (numOfGames == static_cast<uint8_t>(EGameViewLayout::SingleView))
		{
			outSpan[0] = { 2, 2 };
			outPositions[0] = { 0, 0 };
		}
		else if (numOfGames == static_cast<uint8_t>(EGameViewLayout::DualView))
		{
			outSpan[0] = { 2, 1 };
			outSpan[1] = { 2, 1 };
			outPositions[0] = { 0, 0 };
			outPositions[1] = { 0, 1 };
		}
	}
}

Label* TGameView::CreateTopBarTagLabel(const std::string& id, const Color& bg, const Outline& outline, const std::string& typography)
{
	auto* label = new Label();
	label->setId(id);
	label->mTypography = typography;
	label->setResizeMode(Label::AUTOSIZE);
	label->Background = bg;
	label->OutlineStyle = outline;
	label->setClippingMode(EClippingMode::ClipToDimensions);
	label->setPadding(Rectangle({ 8, 0 }, { 8, 0 }));
	return label;
}

void TGameView::InitializeTopBarGameView(size_t gameSlotID, TGameWindow& gameWindow)
{
	auto* topBarContainer = new LayoutContainer();
	topBarContainer->setId("game-top-bar-container-" + std::to_string(gameSlotID));
	topBarContainer->setHeight(56);
	topBarContainer->OutlineStyle = Outline(tempDesignTokens::GameViewOutlineColor, 2_px, {}, 1_px);
	topBarContainer->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_0,
	                                              tempDesignTokens::RoundedCorner_0);
	topBarContainer->OutlineStyle.BoxShadow = Shadow { .color = Black, .blur = 4_px };
	topBarContainer->Background = tempDesignTokens::GameViewTopBarColor;
	topBarContainer->setFocusMode(EFocusMode::Accept);

	topBarContainer->OnFocusedChanged += [this, gameSlotID](Widget*, const bool focused) {
		if (focused && (!CurrentlyFocusedGame.has_value() || gameSlotID != CurrentlyFocusedGame))
		{
			CurrentlyFocusedGame = static_cast<int>(gameSlotID);
			dynamic_cast<TIGPlatformApp*>(pApp)->GameFocusChange(static_cast<int>(gameSlotID));
		}
	};

	auto* topBarLayout = topBarContainer->setLayout<BoxLayout>();
	topBarLayout->DefaultBehavior.Alignment = EChildAlignment::Min;

	auto* tagsContainer = new LayoutContainer();
	tagsContainer->setId("tags-container-" + std::to_string(gameSlotID));
	tagsContainer->bResizeToFitLayout = true;

	auto* tagsLayout = tagsContainer->setLayout<StackLayout>();
	tagsLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	tagsLayout->Alignment = EChildAlignment::Center;
	tagsLayout->Padding = { 8, 0 };

	topBarContainer->add(tagsContainer);
	topBarLayout->PerWidgetBehaviors[tagsContainer].Alignment = ChildAlignment2D { EChildAlignment::Min, EChildAlignment::Center };
	topBarLayout->PerWidgetBehaviors[tagsContainer].Padding = { 16, 0, 0, 0 };

	auto* rngNameTag =
	  CreateTopBarTagLabel("game-top-bar-rng-name", tempDesignTokens::GameTag_RNGName,
	                       Outline(tempDesignTokens::GameTag_RNGNameBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px), tempDesignTokens::TitleFont_16);
	rngNameTag->setX(16.f);
	tagsContainer->add(rngNameTag);

	auto* rngTypeTag =
	  CreateTopBarTagLabel("rng-type", tempDesignTokens::GameTag_RNGType, Outline(tempDesignTokens::GameTag_RNGTypeBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px),
	                       tempDesignTokens::TitleFont_16);
	tagsContainer->add(rngTypeTag);

	auto* gameNameTag =
	  CreateTopBarTagLabel("game-name-label", tempDesignTokens::GameTag_GameName,
	                       Outline(tempDesignTokens::GameTag_GameNameBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px), tempDesignTokens::TitleFont_16);
	gameNameTag->TextScrollSpeed = { 20.f, 5.f };
	tagsContainer->add(gameNameTag);

	gameWindow.gameWindowContainer->add(topBarContainer);
	gameWindow.gameWindowLayout->PerWidgetBehaviors[topBarContainer].Alignment = ChildAlignment2D { EChildAlignment::Stretch, EChildAlignment::Min };

	auto* closeButton = new Button();
	closeButton->setId("game-close-btn-" + std::to_string(gameSlotID));
	closeButton->setBackgroundImage(Image::GetImage("gameThumbnailClose.png"));
	closeButton->Background = tempDesignTokens::GameViewTopBarCloseButtonColor;
	closeButton->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_0,
	                                          tempDesignTokens::RoundedCorner_0);
	closeButton->setSize(52, 48);
	closeButton->OnPressed += [this, gameSlotID]() {
		if (mChangeGameMode)
			dynamic_cast<TIGPlatformApp*>(pApp)->GameFocusChange(static_cast<int>(gameSlotID));
		else
			dynamic_cast<TIGPlatformApp*>(pApp)->CloseGame(gameSlotID, EClosingState::USER_CLOSE);
	};
	topBarContainer->add(closeButton);

	topBarLayout->PerWidgetBehaviors[closeButton].Alignment = ChildAlignment2D { EChildAlignment::Max, EChildAlignment::Min };
	topBarLayout->PerWidgetBehaviors[closeButton].Padding = { 0, 1, 0, 0 };

	gameWindow.gameTopBar = { topBarContainer, topBarLayout, tagsContainer, tagsLayout, gameNameTag, rngTypeTag, rngNameTag, closeButton };
}

void TGameView::AddTopBarGameTags(const size_t gameSlotID)
{
	pCommander->GetGameState(gameSlotID, [this, gameSlotID](const GameRunContext& ctx) {
		if (!ctx.RunningGame)
			return;

		const auto* runningGame = ctx.RunningGame.get();
		const auto* runningPackage = ctx.RunningPackage.get();

		// Set the game name tag
		Games[gameSlotID].gameTopBar.gameNameTag->setCaption(runningGame->Info().Config.GameConfig.DisplayName);

		if (!runningGame->Host)
			return;

		// Set the RNG name tag
		if (!runningGame->Host->Name.empty())
			Games[gameSlotID].gameTopBar.RNGNameTag->setCaption(LocalizedMessage(runningGame->Host->Name));

		// Set the RNG type tag
		const uint32_t hostID = runningGame->Host->ID;
		const HostType gameHostType = runningPackage->Package.Game.Type;
		if (const auto hostData = pCommander->GetTrackedHost(hostID))
		{
			switch (gameHostType)
			{
				case HostType::Roulette: {
					Games[gameSlotID].gameTopBar.RNGTypeTag->setCaption(LocalizedMessage(AUTO_STRING));
					break;
				}
				case HostType::DealersGame: {
					ScopedLock lock(hostData->State);
					const json* virtualJson = FindMember((&hostData->State), "virtual");
					if (virtualJson && !virtualJson->is_null())
					{
						const bool isVirtual = virtualJson->get<bool>();
						Games[gameSlotID].gameTopBar.RNGTypeTag->setCaption(isVirtual ? LocalizedMessage(VIRTUAL_STRING) : LocalizedMessage(GAME_CATEGORY_LIVE));
					}
					break;
				}
				default: {
					Games[gameSlotID].gameTopBar.RNGTypeTag->setCaption(LocalizedMessage(VIRTUAL_STRING));
					break;
				}
			}
		}

		OnGameStateChangedHandler = pCommander->OnGameStateChanged.bind([this, gameSlotID, hostID, gameHostType](const uint32_t id, const json& data) {
			if (hostID == id)
			{
				OnGameStateChanged(data, gameHostType, gameSlotID);
			}
		});
	});
}

void TGameView::OnGameStateChanged(const json& data, const HostType gameHostType, const size_t gameSlotID) const
{
	const std::string eventName = data["name"].get<std::string>();

	if (eventName == "gameState" && gameHostType == HostType::DealersGame)
	{
		const json* virtualJson = FindMember(data["new"], "virtual");
		if (virtualJson && !virtualJson->is_null())
		{
			const bool isVirtual = virtualJson->get<bool>();
			Games[gameSlotID].gameTopBar.RNGTypeTag->setCaption(isVirtual ? LocalizedMessage(VIRTUAL_STRING) : LocalizedMessage(GAME_CATEGORY_LIVE));
		}
	}
}