#include "TGameHistory.h"

#include <chrono>

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"
#include "roulette/TRouletteBets.h"
#include "roulette/TRouletteTypes.h"
#include "yserver/TRouletteGamesExtraData.h"
#include "yserver/hosts/roulette/RouletteHostSharedTypes.h"


static std::pair<LinearGradient, Outline> LastNumGradients(int number)
{
	if (number == 0 || number == 37)
		return { tempDesignTokens::GameIconRouletteLastResultGreenBackground, tempDesignTokens::GameIconRouletteLastResultGreenOutline };
	else if (BlackColors.contains(number))
		return { tempDesignTokens::GameIconRouletteLastResultBlackBackground, tempDesignTokens::GameIconRouletteLastResultBlackOutline };
	else
		return { tempDesignTokens::GameIconRouletteLastResultRedBackground, tempDesignTokens::GameIconRouletteLastResultRedOutline };
}

static inline std::string CardValueToString(const uint32_t value)
{
	switch (value)
	{
		case 1: return "A";
		case 11: return "J";
		case 12: return "Q";
		case 13: return "K";
		default: return std::to_string(value);
	}
}

THistoryCard::THistoryCard(const int faceValue, const int suiteValue)
{
	auto* layout = setLayout<BoxLayout>();

	setSize(30, 40);
	Background = White;
	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_3);
	OutlineStyle.Feather = 1_px;

	plCardNumber = new Label();
	plCardNumber->setId("card-number");

	plCardNumber->setCaption(CardValueToString(faceValue));
	plCardNumber->mTypography = tempDesignTokens::TitleFont_18;
	plCardNumber->setAlignment(align::LEFT_TOP);
	plCardNumber->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
	plCardNumber->setSize(20, 20);
	plCardNumber->setPadding({ 2, 0, 0, 0 });
	add(plCardNumber);
	layout->PerWidgetBehaviors[plCardNumber].Alignment = EChildAlignment::Min;

	switch (suiteValue)
	{
		case 1:
			piSuite = new Icon("gameHistoryClubs.png");
			plCardNumber->TextColor = Black;
			break;
		case 2:
			piSuite = new Icon("gameHistoryDiamonds.png");
			plCardNumber->TextColor = Red;
			break;
		case 3:
			piSuite = new Icon("gameHistoryHearts.png");
			plCardNumber->TextColor = Red;
			break;
		case 4:
			piSuite = new Icon("gameHistorySpade.png");
			plCardNumber->TextColor = Black;
			break;
		default: break;
	}

	piSuite->setId("game-screenshot");
	piSuite->setSize(16, 16);
	piSuite->ScaleMode = EScaleMode::ZOOM_TO_FIT_ONLY_DOWN;
	add(piSuite);
	layout->PerWidgetBehaviors[piSuite].Alignment = EChildAlignment::Max;
}

TGameHistoryEntry::TGameHistoryEntry(const yserver::TGameRoundSnapshotDto& snapshot, const std::string& gameName) : ExtraData(snapshot.ExtraData)
{
	setId("game-history-entry");

	bResizeToFitLayout = { false, true };

	pScreenshotModule = (TScreenshots*)pApp->GetModuleByName("Screenshots1");
	arID = snapshot.AccountingRound;

	StackLayout* layout = setLayout<StackLayout>();
	layout->Alignment = EChildAlignment::Stretch;

	LayoutContainer* pGrid = new LayoutContainer();
	pGrid->bResizeToFitLayout = { false, true };
	pGrid->setId("grid-container");
	add(pGrid);

	GridLayout* pGridLayout = pGrid->setLayout<GridLayout>();
	pGridLayout->AddColumns({ { .Size = 628 }, { .Size = 54, .bAbsoluteInPixels = true } });


	pContent = new LayoutContainer();
	pContent->bResizeToFitLayout = { false, true };
	pContent->setId("content-container");
	pGrid->add(pContent);
	pGridLayout->Set(pContent, GridLayout::GridPosition(sVector2D(0, 0), 1, { EChildAlignment::Stretch, EChildAlignment::Center }));
	pContent->OnDimensionChanged += [this, pGrid](Widget* source, const Rectangle& oldDim, const Rectangle& newDim) {
		pGrid->setHeight(pContent->getHeight());
	};

	StackLayout* pContentLayout = pContent->setLayout<StackLayout>();
	pContentLayout->Direction = EStackLayoutDirection::VERTICAL;
	pContentLayout->Alignment = EChildAlignment::Stretch;
	pContentLayout->Padding = { 24, 24 };
	pContentLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	CreateGeneralInfo(snapshot, gameName);

	pResultContainer = new LayoutContainer();
	pResultContainer->bResizeToFitLayout = { false, true };
	pResultContainer->setId("result-container");
	StackLayout* pResultLayout = pResultContainer->setLayout<StackLayout>();
	pResultLayout->Direction = EStackLayoutDirection::VERTICAL;
	pResultLayout->Alignment = EChildAlignment::Stretch;
	pResultLayout->Padding = { 6, 6 };
	pResultContainer->setVisible(false);
	pContent->add(pResultContainer);

	pBetInfoContainer = new LayoutContainer();
	pBetInfoContainer->bResizeToFitLayout = { false, true };
	pBetInfoContainer->setId("bet-info-container");
	pBetInfoContainer->setWidth(682);
	pBetInfoContainer->setVisible(false);
	StackLayout* pBetInfoLayout = pBetInfoContainer->setLayout<StackLayout>();
	pBetInfoLayout->Direction = EStackLayoutDirection::VERTICAL;
	pBetInfoLayout->Alignment = EChildAlignment::Stretch;
	pBetInfoLayout->Padding = { 12, 24 };
	pBetInfoLayout->bUsePaddingBeforeFirstAndAfterLast = true;
	pContent->add(pBetInfoContainer);

	Button* expandCollapse = new Button();
	expandCollapse->setId("expand");
	expandCollapse->setBackgroundImage(Image::GetImage("expand.png"));
	expandCollapse->setSize(52, 52);
	expandCollapse->Background = Color::FromRGBInteger(0x16191A);
	expandCollapse->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_12,
	                                             tempDesignTokens::RoundedCorner_0);
	expandCollapse->setOrigin(1.f, 1.f);
	pGrid->add(expandCollapse);
	pGridLayout->Set(expandCollapse, GridLayout::GridPosition(sVector2D(1, 0), 1, ChildAlignment2D(EChildAlignment::Min, EChildAlignment::Max)));

	expandCollapse->OnPressed += [this, expandCollapse]() {
		pGuiApp->DeferToDrawSimple(
		  [this, expandCollapse]() {
			  bExpanded = !bExpanded;
			  pResultContainer->setVisible(bExpanded);
			  pBetInfoContainer->setVisible(bExpanded);

			  if (bExpanded)
			  {
				  expandCollapse->setBackgroundImage(Image::GetImage("collapse.png"));
				  LoadResultAndInfoDataIfNonexistent();
			  }
			  else
			  {
				  expandCollapse->setBackgroundImage(Image::GetImage("expand.png"));
			  }
		  },
		  "expandCollapse-history");
	};

	auto brush = ImageBrush(Image::GetImage("bgTextureDark.png"));
	brush.tiling = true;
	Background = brush;
	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
}


void TGameHistoryEntry::CreateGeneralInfo(const yserver::TGameRoundSnapshotDto& snapshot, const std::string& gameName)
{
	using namespace std::chrono;
	pGeneralInfo = new LayoutContainer();
	pGeneralInfo->bResizeToFitLayout = { false, true };
	pGeneralInfo->setId("top-row-container");
	pGeneralInfo->setWidth(682);
	pContent->add(pGeneralInfo);
	GridLayout* pTopRowLayout = pGeneralInfo->setLayout<GridLayout>();
	pTopRowLayout->AddRows({ {}, {}, { .Padding = 12 } });

	Label* pGameNameLabel = new Label();
	pGameNameLabel->setId("game-name-label");
	pGameNameLabel->setCaption(LocalizedMessage(gameName));
	pGameNameLabel->mTypography = tempDesignTokens::TitleFont_18;
	pGameNameLabel->TextColor = tempDesignTokens::GoldTextColor;
	pGameNameLabel->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pGameNameLabel->setAlignment(align::LEFT_CENTER);
	pGeneralInfo->add(pGameNameLabel);
	pTopRowLayout->Set(pGameNameLabel, GridLayout::GridPosition({ 0, 0 }, sVector2D(1, 1), { EChildAlignment::Min, EChildAlignment::Center }));

	LayoutContainer* pBetContainer = new LayoutContainer();
	pBetContainer->setId("bet-container");
	pBetContainer->bResizeToFitLayout = { true, false };
	pBetContainer->setHeight(24);
	StackLayout* pBetInfoLayout = pBetContainer->setLayout<StackLayout>();
	pBetInfoLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	pBetInfoLayout->Alignment = EChildAlignment::Stretch;
	pGeneralInfo->add(pBetContainer);
	pTopRowLayout->Set(pBetContainer, GridLayout::GridPosition({ 0, 1 }, sVector2D(1, 1), { EChildAlignment::Min, EChildAlignment::Min }));

	Label* pBetLabel = new Label();
	pBetLabel->setId("bet-label");
	pBetLabel->setCaption(LocalizedMessage(BET_STRING) + ":");
	pBetLabel->mTypography = tempDesignTokens::MainFont_400_16;
	pBetLabel->TextColor = tempDesignTokens::GreyTextColor;
	pBetLabel->setAlignment(align::LEFT_CENTER);
	pBetLabel->setResizeMode(Label::AUTOSIZE);
	pBetInfoLayout->setPaddingFor(pBetLabel, Rectangle(0, 0, 8, 0));
	pBetContainer->add(pBetLabel);

	Label* pBetAmount = new Label();
	pBetAmount->setId("bet-amount");
	pBetAmount->setCaption(MenuGUI()->FormatCreditsAsCurrency(snapshot.TotalBet.Total()));
	pBetAmount->mTypography = tempDesignTokens::MainFont_600_16;
	pBetAmount->setAlignment(align::LEFT_CENTER);
	pBetAmount->setResizeMode(Label::AUTOSIZE);
	pBetInfoLayout->setPaddingFor(pBetAmount, Rectangle(0, 0, 16, 0));
	pBetContainer->add(pBetAmount);

	Label* pWinLabel = new Label();
	pWinLabel->setId("win-label");
	pWinLabel->setCaption(LocalizedMessage(WIN_STRING) + ":");
	pWinLabel->mTypography = tempDesignTokens::MainFont_400_16;
	pWinLabel->TextColor = tempDesignTokens::GreyTextColor;
	pWinLabel->setAlignment(align::LEFT_CENTER);
	pWinLabel->setResizeMode(Label::AUTOSIZE);
	pBetInfoLayout->setPaddingFor(pWinLabel, Rectangle(0, 0, 8, 0));
	pBetContainer->add(pWinLabel);

	Label* pWinAmount = new Label();
	pWinAmount->setId("win-amount");
	pWinAmount->setCaption(MenuGUI()->FormatCreditsAsCurrency(snapshot.TotalWin.Total()));
	pWinAmount->mTypography = tempDesignTokens::MainFont_600_16;
	pWinAmount->setAlignment(align::LEFT_CENTER);
	pWinAmount->setResizeMode(Label::AUTOSIZE);
	pBetContainer->add(pWinAmount);

	Label* pRoundID = new Label();
	pRoundID->setId("round-id");
	pRoundID->setCaption("ID: " + snapshot.RoundID);
	pRoundID->mTypography = tempDesignTokens::MainFont_400_16;
	pRoundID->TextColor = tempDesignTokens::GreyTextColor;
	pRoundID->setAlignment(align::LEFT_CENTER);
	pRoundID->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pGeneralInfo->add(pRoundID);
	pTopRowLayout->Set(pRoundID, GridLayout::GridPosition({ 0, 2 }, sVector2D(1, 1), { EChildAlignment::Min, EChildAlignment::Min }));

	Label* pDateLabel = new Label();
	pDateLabel->setId("date-label");
	system_clock::time_point tp = time_point<system_clock, milliseconds>(milliseconds(snapshot.EndedTimestamp));
	time_t time = system_clock::to_time_t(tp);
	pDateLabel->setCaption(yutils::FormatTime("%Y-%m-%d %H:%M:%S", time));
	pDateLabel->mTypography = tempDesignTokens::MainFont_400_16;
	pDateLabel->setAlignment(align::RIGHT_CENTER);
	pDateLabel->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pDateLabel->setOrigin(1.f, 0.f);
	pGeneralInfo->add(pDateLabel);
	pTopRowLayout->Set(pDateLabel, GridLayout::GridPosition({ 0, 2 }, sVector2D(1, 1), { EChildAlignment::Max, EChildAlignment::Center }));
}

TRouletteHistoryEntry::TRouletteHistoryEntry(const yserver::TGameRoundSnapshotDto& snapshot, const std::string& gameName) : TGameHistoryEntry(snapshot, gameName) {}

void TGameHistoryEntry::LoadResultAndInfoDataIfNonexistent()
{
	if (bInitialized)
		return;

	bInitialized = true;

	const auto& screenshots = pScreenshotModule->myScreenshots();

	std::string iconPath = "repeat.png";

	auto it = screenshots.find(std::stoi(arID));
	if (it != screenshots.end())
	{
		iconPath = it->second.ScreenshotFile;
	}

	piGameScreenshot = new Icon(iconPath);
	piGameScreenshot->setId("game-screenshot");
	piGameScreenshot->setSize(450, 230);
	piGameScreenshot->ScaleMode = EScaleMode::ZOOM_TO_FIT;
	pBetInfoContainer->add(piGameScreenshot);

	pWinPositions = new LayoutContainer();
	pWinPositions->setVisible(false);
	pWinPositions->setId("win-positions");
	pWinPositions->bResizeToFitLayout = { false, true };
	pBetInfoContainer->add(pWinPositions);
	winPositionsLayout = pWinPositions->setLayout<GridLayout>();
	winPositionsLayout->AddRow({ .Size = 32.f, .bAbsoluteInPixels = true, .Margin = 0.f, .bCollapsed = false });
	winPositionsLayout->AddColumns({ { .Size = 300, .Padding = 12 }, { .Size = 134, .Padding = { 12, 12 } }, { .Size = 134, .Padding = 12 } });    // figma sizes

	Label* plWinPositions = new Label();
	plWinPositions->setId("win-positions-label");
	plWinPositions->setCaption(LocalizedMessage(WIN_POSITIONS_STRING));
	plWinPositions->mTypography = tempDesignTokens::MainFont_600_16;
	plWinPositions->TextColor = tempDesignTokens::GoldTextColor;
	plWinPositions->setAlignment(align::LEFT_CENTER);
	plWinPositions->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pWinPositions->add(plWinPositions);
	winPositionsLayout->Set(plWinPositions, GridLayout::GridPosition(sVector2D(0), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Min, EChildAlignment::Center)));

	Label* plBet = new Label();
	plBet->setId("bet");
	plBet->setCaption(LocalizedMessage(BET_STRING) + ":");
	plBet->mTypography = tempDesignTokens::MainFont_600_16;
	plBet->TextColor = tempDesignTokens::GoldTextColor;
	plBet->setAlignment(align::CENTER_CENTER);
	plBet->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pWinPositions->add(plBet);
	winPositionsLayout->Set(plBet, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Center)));

	Label* plWin = new Label();
	plWin->setId("win");
	plWin->setCaption(LocalizedMessage(WIN_STRING) + ":");
	plWin->mTypography = tempDesignTokens::MainFont_600_16;
	plWin->TextColor = tempDesignTokens::GoldTextColor;
	plWin->setAlignment(align::CENTER_CENTER);
	plWin->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pWinPositions->add(plWin);
	winPositionsLayout->Set(plWin, GridLayout::GridPosition(sVector2D(2, 0), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Center)));
}

LocalizedMessage TRouletteHistoryEntry::GetPositions(const int betType, const std::string& positions)
{
	using namespace roulette;

	int id = NONE_STRING;
	switch (betType)
	{
		case EBetStyle::Plain: id = ROULETTE_BET_PLAIN_STRING; break;
		case EBetStyle::Caval: id = ROULETTE_BET_CAVAL_STRING; break;
		case EBetStyle::Trasversales: id = ROULETTE_BET_TRASVERSALES_STRING; break;
		case EBetStyle::Carre: id = ROULETTE_BET_CARRE_STRING; break;
		case EBetStyle::FiveNumbers: id = ROULETTE_BET_FIVE_NUMBERS_STRING; break;
		case EBetStyle::SixNumbers: id = ROULETTE_BET_SIX_NUMBERS_STRING; break;
		case EBetStyle::HorizontalLine: id = ROULETTE_BET_HORIZONTAL_LINE_STRING; break;
		case EBetStyle::Third: id = ROULETTE_BET_THIRD_STRING; break;
		case EBetStyle::Color: id = ROULETTE_BET_COLOR_STRING; break;
		case EBetStyle::OddEven: id = ROULETTE_BET_ODD_EVEN_STRING; break;
		case EBetStyle::Half: id = ROULETTE_BET_HALF_STRING; break;
		case EBetStyle::LesFigures: id = ROULETTE_BET_LES_FIGURES_STRING; break;
		case EBetStyle::ToutVa: id = ROULETTE_BET_TOUT_VA_STRING; break;
		case EBetStyle::LeFinali3: id = ROULETTE_BET_LE_FINALI_3_STRING; break;
		case EBetStyle::LeFinali4: id = ROULETTE_BET_LE_FINALI_4_STRING; break;
		case EBetStyle::LeFinali5: id = ROULETTE_BET_LE_FINALI_5_STRING; break;
		case EBetStyle::LeFinaliToutVa3: id = ROULETTE_BET_LE_FINALI_TOUT_VA_3_STRING; break;
		case EBetStyle::LeFinaliToutVa4: id = ROULETTE_BET_LE_FINALI_TOUT_VA_4_STRING; break;
		case EBetStyle::LeFinaliToutVa5: id = ROULETTE_BET_LE_FINALI_TOUT_VA_5_STRING; break;
		case EBetStyle::None:
		default: id = NONE_STRING; break;
	}

	return LocalizedMessage(id) + " " + positions;
}

void TRouletteHistoryEntry::LoadResultAndInfoDataIfNonexistent()
{
	using namespace roulette;
	if (bInitialized)
		return;

	TGameHistoryEntry::LoadResultAndInfoDataIfNonexistent();

	TRouletteGameExtraDataDto rouletteDto;
	TRouletteGameExtraDataDto::FromJSON(ExtraData, rouletteDto);

	Container* pTopDivider = new Container();
	pTopDivider->setId("top-divider");
	pTopDivider->setHeight(1);
	pTopDivider->Background = Color::FromRGBInteger(0x212628);
	pResultContainer->add(pTopDivider);

	LayoutContainer* pContainer = new LayoutContainer();
	pContainer->setId("result-container");
	pContainer->bResizeToFitLayout = true;
	pResultContainer->add(pContainer);
	auto layout = pContainer->setLayout<StackLayout>();
	layout->Direction = EStackLayoutDirection::HORIZONTAL;
	layout->Padding = Vector2D(0, 0);
	layout->Alignment = EChildAlignment::Center;

	Label* pResult = new Label();
	pResult->setId("result");
	pResult->setCaption(LocalizedMessage(RESULT_STRING) + ":");
	pResult->mTypography = tempDesignTokens::MainFont_400_16;
	pResult->TextColor = tempDesignTokens::GreyTextColor;
	pResult->setAlignment(align::LEFT_CENTER);
	pResult->setOrigin(0.f, 0.5f);
	pResult->setResizeMode(Label::AUTOSIZE);
	pResult->setPadding(Rectangle(0, 0, 16.f, 0));
	pContainer->add(pResult);

	Label* pWinNumber = new Label();
	pWinNumber->setId("round-result");
	pWinNumber->mTypography = tempDesignTokens::MainFont_600_16;
	pWinNumber->setAlignment(align::CENTER_CENTER);
	pWinNumber->setOrigin(0.f, 0.5f);
	if (rouletteDto.WinNumber.has_value())
	{
		if (rouletteDto.WinNumber.value() == 37)
			pWinNumber->setCaption("00");
		else
			pWinNumber->setCaption(LocalizedMessage(MyUtils::int2str(rouletteDto.WinNumber.value())));

		pWinNumber->setSize(40, 40);

		auto gradients = LastNumGradients(rouletteDto.WinNumber.value());

		pWinNumber->Background = gradients.first;
		pWinNumber->OutlineStyle = gradients.second;
	}
	else
	{
		pWinNumber->setResizeMode(Label::AUTOSIZE);
		pWinNumber->setCaption(LocalizedMessage(GAME_VOIDED_STRING));
	}


	pContainer->add(pWinNumber);

	Container* pBottomDivider = new Container();
	pBottomDivider->setId("bottom-divider");
	pBottomDivider->setHeight(1);
	pBottomDivider->Background = Color::FromRGBInteger(0x212628);
	pResultContainer->add(pBottomDivider);

	CalculateWinsResultDto wins;
	CalculateWinsResultDto::FromJson(rouletteDto.CalculatedWins, wins);

	std::map<EBetPresetType, BetPresetField> presetDefinitions;
	roulette::from_json(rouletteDto.BetPresetFields, presetDefinitions);
	const BetAmounts betAmounts = BetAmounts::FromJSON(ExtraData, rouletteDto.CreditMultiplier, presetDefinitions);

	if (!wins.WinningFields.empty())
		pWinPositions->setVisible(true);

	int position = 1;
	for (auto it = wins.WinningFields.begin(); it != wins.WinningFields.end(); ++it)
	{
		winPositionsLayout->AddRow({ .Size = 32.f, .bAbsoluteInPixels = true, .Margin = 0.f, .bCollapsed = false });

		Container* pDivider = new Container();
		pDivider->setId("divider");
		pDivider->setHeight(1);
		pDivider->Background = Color::FromRGBInteger(0x212628);
		pDivider->setVisible(true);
		pWinPositions->add(pDivider);
		winPositionsLayout->Set(pDivider,
		                        GridLayout::GridPosition(sVector2D(0, position), sVector2D(3, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Min)));

		Label* plPosition = new Label();
		plPosition->setId("position");
		plPosition->setCaption(GetPositions(it->Position.Type, it->Position.ParseNumbers()));
		plPosition->mTypography = tempDesignTokens::MainFont_400_16;
		plPosition->TextColor = tempDesignTokens::GreyTextColor;
		plPosition->setAlignment(align::LEFT_CENTER);
		plPosition->setSize(300, 24);
		plPosition->setResizeMode(Label::SCROLLABLE, Label::AUTOSIZE);
		pWinPositions->add(plPosition);
		winPositionsLayout->Set(plPosition,
		                        GridLayout::GridPosition(sVector2D(0, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

		Label* plBetOnPos = new Label();
		plBetOnPos->setId("bet-amount");
		plBetOnPos->setCaption(MenuGUI()->FormatCreditsAsCurrency(betAmounts.Get(it->X, it->Y)));
		plBetOnPos->mTypography = tempDesignTokens::MainFont_400_16;
		plBetOnPos->TextColor = tempDesignTokens::GreyTextColor;
		plBetOnPos->setAlignment(align::CENTER_CENTER);
		plBetOnPos->setSize(100, 24);
		plBetOnPos->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
		pWinPositions->add(plBetOnPos);
		winPositionsLayout->Set(plBetOnPos,
		                        GridLayout::GridPosition(sVector2D(1, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

		Label* plWinOnPos = new Label();
		plWinOnPos->setId("win-amount");
		plWinOnPos->setCaption(MenuGUI()->FormatCreditsAsCurrency(it->Won));
		plWinOnPos->mTypography = tempDesignTokens::MainFont_600_16;
		plWinOnPos->setAlignment(align::CENTER_CENTER);
		plWinOnPos->setSize(100, 24);
		plWinOnPos->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
		pWinPositions->add(plWinOnPos);
		winPositionsLayout->Set(plWinOnPos,
		                        GridLayout::GridPosition(sVector2D(2, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

		position++;
	}
}

void TCardGamesHistoryEntry::LoadResultAndInfoDataIfNonexistent()
{
	if (bInitialized)
		return;

	TGameHistoryEntry::LoadResultAndInfoDataIfNonexistent();

	yserver::gamehost::TDealerCardGameExtraDataDto extraData;
	yserver::gamehost::TDealerCardGameExtraDataDto::FromJSON(ExtraData, extraData);

	Container* pTopDivider = new Container();
	pTopDivider->setId("top-divider");
	pTopDivider->setHeight(1);
	pTopDivider->Background = Color::FromRGBInteger(0x212628);
	pResultContainer->add(pTopDivider);

	LayoutContainer* pContainer = new LayoutContainer();
	pContainer->setId("result-container");
	pContainer->bResizeToFitLayout = true;
	pResultContainer->add(pContainer);
	auto layout = pContainer->setLayout<StackLayout>();
	layout->Direction = EStackLayoutDirection::HORIZONTAL;
	layout->Padding = Vector2D(12, 12);
	layout->Alignment = EChildAlignment::Center;

	Label* pResult = new Label();
	pResult->setId("result");
	pResult->setCaption(LocalizedMessage(RESULT_STRING) + ":");
	pResult->mTypography = tempDesignTokens::MainFont_400_16;
	pResult->TextColor = tempDesignTokens::GreyTextColor;
	pResult->setAlignment(align::LEFT_CENTER);
	pResult->setOrigin(0.f, 0.5f);
	pResult->setDimension(0, pContainer->getHeight() / 2, 60, 24);
	pContainer->add(pResult);


	if (gameType == GameType::DealersGame)
	{
		AddDealerGameBrackets(pContainer, extraData);
	}

	Container* pBottomDivider = new Container();
	pBottomDivider->setId("bottom-divider");
	pBottomDivider->setHeight(1);
	pBottomDivider->Background = Color::FromRGBInteger(0x212628);
	pResultContainer->add(pBottomDivider);

	if (!extraData.CalculateGameWins.WinningFields.empty())
		pWinPositions->setVisible(true);

	int position = 1;
	for (auto it = extraData.CalculateGameWins.WinningFields.begin(); it != extraData.CalculateGameWins.WinningFields.end(); ++it)
	{
		winPositionsLayout->AddRow({ .Size = 32.f, .bAbsoluteInPixels = true, .Margin = 0.f, .bCollapsed = false });

		Container* pDivider = new Container();
		pDivider->setId("divider");
		pDivider->setHeight(1);
		pDivider->Background = Color::FromRGBInteger(0x212628);
		pDivider->setVisible(true);
		pWinPositions->add(pDivider);
		winPositionsLayout->Set(pDivider,
		                        GridLayout::GridPosition(sVector2D(0, position), sVector2D(3, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Min)));

		Label* plPosition = new Label();
		plPosition->setId("position");
		plPosition->setCaption(LocalizedMessage(it->type));
		plPosition->mTypography = tempDesignTokens::MainFont_400_16;
		plPosition->TextColor = tempDesignTokens::GreyTextColor;
		plPosition->setAlignment(align::LEFT_CENTER);
		plPosition->setSize(100, 24);
		plPosition->setResizeMode(Label::SCROLLABLE);
		pWinPositions->add(plPosition);
		winPositionsLayout->Set(plPosition,
		                        GridLayout::GridPosition(sVector2D(0, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));


		Label* plBetOnPos = new Label();
		plBetOnPos->setId("bet-amount");
		plBetOnPos->setCaption(MenuGUI()->FormatCreditsAsCurrency(it->credits));
		plBetOnPos->mTypography = tempDesignTokens::MainFont_400_16;
		plBetOnPos->TextColor = tempDesignTokens::GreyTextColor;
		plBetOnPos->setAlignment(align::CENTER_CENTER);
		plBetOnPos->setSize(100, 24);
		plBetOnPos->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
		pWinPositions->add(plBetOnPos);
		winPositionsLayout->Set(plBetOnPos,
		                        GridLayout::GridPosition(sVector2D(1, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

		Label* plWinOnPos = new Label();
		plWinOnPos->setId("win-amount");
		plWinOnPos->setCaption(MenuGUI()->FormatCreditsAsCurrency(it->won));
		plWinOnPos->mTypography = tempDesignTokens::MainFont_600_16;
		plWinOnPos->setAlignment(align::CENTER_CENTER);
		plWinOnPos->setSize(100, 24);
		plWinOnPos->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
		pWinPositions->add(plWinOnPos);
		winPositionsLayout->Set(plWinOnPos,
		                        GridLayout::GridPosition(sVector2D(2, position), sVector2D(1, 1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

		position++;
	}
}
void TCardGamesHistoryEntry::AddDealerGameBrackets(LayoutContainer* pContainer, yserver::gamehost::TDealerCardGameExtraDataDto& extraData)
{
	if (extraData.gameInfo.GameType == dealer_assist::EGameType::Baccarat || extraData.gameInfo.GameType == dealer_assist::EGameType::OpenBaccarat)
	{
		yserver::gamehost::CardPositionDto<yserver::gamehost::baccarat::EBaccaratDealingPhase> cardsArray;
		yserver::gamehost::CardPositionDto<yserver::gamehost::baccarat::EBaccaratDealingPhase>::FromJSON(ExtraData, cardsArray);

		const std::vector<yserver::gamehost::baccarat::EBaccaratDealingPhase> playerIndices = { yserver::gamehost::baccarat::EBaccaratDealingPhase::PlayerFirstCard,
			                                                                                    yserver::gamehost::baccarat::EBaccaratDealingPhase::PlayerSecondCard,
			                                                                                    yserver::gamehost::baccarat::EBaccaratDealingPhase::PlayerThirdCard };
		const std::vector<yserver::gamehost::baccarat::EBaccaratDealingPhase> bankerIndices = { yserver::gamehost::baccarat::EBaccaratDealingPhase::BankerFirstCard,
			                                                                                    yserver::gamehost::baccarat::EBaccaratDealingPhase::BankerSecondCard,
			                                                                                    yserver::gamehost::baccarat::EBaccaratDealingPhase::BankerThirdCard };

		const yserver::gamehost::baccarat::EBaccaratWinner winner = yserver::gamehost::baccarat::EBaccaratWinner::_from_integral(extraData.gameRecord.Winners[0]);
		const yserver::gamehost::baccarat::EBaccaratSide player = yserver::gamehost::baccarat::EBaccaratSide::Player;
		const yserver::gamehost::baccarat::EBaccaratSide banker = yserver::gamehost::baccarat::EBaccaratSide::Banker;

		AddCardBracket(pContainer, player._to_string(), playerIndices, cardsArray, extraData.gameRecord.HandValues[player._to_string()],
		               player._to_integral() == winner._to_integral());
		AddCardBracket(pContainer, banker._to_string(), bankerIndices, cardsArray, extraData.gameRecord.HandValues[banker._to_string()],
		               banker._to_integral() == winner._to_integral());
	}
	else if (extraData.gameInfo.GameType == dealer_assist::EGameType::DragonTiger || extraData.gameInfo.GameType == dealer_assist::EGameType::OpenDragonTiger)
	{
		yserver::gamehost::CardPositionDto<yserver::gamehost::dragontiger::EDragonTigerDealingPhase> cardsArray;
		yserver::gamehost::CardPositionDto<yserver::gamehost::dragontiger::EDragonTigerDealingPhase>::FromJSON(ExtraData, cardsArray);

		const std::vector<yserver::gamehost::dragontiger::EDragonTigerDealingPhase> dragonIndices = {
			yserver::gamehost::dragontiger::EDragonTigerDealingPhase::DragonCard
		};
		const std::vector<yserver::gamehost::dragontiger::EDragonTigerDealingPhase> tigerIndices = {
			yserver::gamehost::dragontiger::EDragonTigerDealingPhase::TigerCard
		};

		const yserver::gamehost::dragontiger::EDragonTigerWinner winner =
		  yserver::gamehost::dragontiger::EDragonTigerWinner::_from_integral(extraData.gameRecord.Winners[0]);
		const yserver::gamehost::dragontiger::EDragonTigerSide dragon = yserver::gamehost::dragontiger::EDragonTigerSide::Dragon;
		const yserver::gamehost::dragontiger::EDragonTigerSide tiger = yserver::gamehost::dragontiger::EDragonTigerSide::Tiger;

		AddCardBracket(pContainer, dragon._to_string(), dragonIndices, cardsArray, extraData.gameRecord.HandValues[dragon._to_string()],
		               dragon._to_integral() == winner._to_integral());
		AddCardBracket(pContainer, tiger._to_string(), tigerIndices, cardsArray, extraData.gameRecord.HandValues[tiger._to_string()],
		               tiger._to_integral() == winner._to_integral());
	}
	else if (extraData.gameInfo.GameType == dealer_assist::EGameType::ThreeHeadedDragon)
	{
		yserver::gamehost::CardPositionDto<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase> cardsArray;
		yserver::gamehost::CardPositionDto<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase>::FromJSON(ExtraData, cardsArray);

		const std::vector<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase> goldenDragonIndices = {
			yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase::GoldenDragonCard,
		};
		const std::vector<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase> blackDragonIndices = {
			yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase::BlackDragonCard,
		};
		const std::vector<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase> redDragonIndices = {
			yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase::RedDragonCard,
		};
		const std::vector<yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase> tigerIndices = {
			yserver::gamehost::threeheadeddragon::EThreeHeadedDragonDealingPhase::TigerCard
		};

		const auto& winners = extraData.gameRecord.Winners;
		std::array<bool, 3> dragonWinners = { false, false, false };
		bool tigerWinner = true;
		for (size_t i = 0; i < 3 && i < winners.size(); ++i)
		{
			dragonWinners[i] = (winners[i] == static_cast<uint8_t>(yserver::gamehost::threeheadeddragon::EThreeHeadedDragonWinner::DragonWin));
			if (dragonWinners[i] || winners[i] == static_cast<uint8_t>(yserver::gamehost::threeheadeddragon::EThreeHeadedDragonWinner::Tie))
				tigerWinner = false;
		}

		AddCardBracket(pContainer, "Golden", goldenDragonIndices, cardsArray, extraData.gameRecord.HandValues["GoldenDragon"], dragonWinners[0]);
		AddCardBracket(pContainer, "Black", blackDragonIndices, cardsArray, extraData.gameRecord.HandValues["BlackDragon"], dragonWinners[1]);
		AddCardBracket(pContainer, "Red", redDragonIndices, cardsArray, extraData.gameRecord.HandValues["RedDragon"], dragonWinners[2]);
		AddCardBracket(pContainer, "Tiger", tigerIndices, cardsArray, extraData.gameRecord.HandValues["Tiger"], tigerWinner);
	}
}

template <typename PhaseEnum>
void TCardGamesHistoryEntry::AddCardBracket(LayoutContainer* pContainer, const std::string& bracketName, const std::vector<PhaseEnum>& cardIndices,
                                            yserver::gamehost::CardPositionDto<PhaseEnum>& cardsArray, const int score, const bool isWinner)
{
	LayoutContainer* pResultBracket = new LayoutContainer();
	pResultBracket->bResizeToFitLayout = { false, false };
	pResultBracket->setSize(65, 150);
	pResultBracket->setId("result-bracket");
	GridLayout* grid = pResultBracket->setLayout<GridLayout>();
	grid->AddRows({ {}, {}, {} });

	Label* pResultBracketName = new Label();
	pResultBracketName->setId("round-id");
	pResultBracketName->setCaption(bracketName);
	pResultBracketName->mTypography = isWinner ? tempDesignTokens::MainFont_600_16 : tempDesignTokens::MainFont_400_16;
	pResultBracketName->TextColor = tempDesignTokens::GreyTextColor;
	pResultBracketName->setAlignment(align::CENTER_CENTER);
	pResultBracketName->setSize(65, 24);
	pResultBracket->add(pResultBracketName);
	grid->Set(pResultBracketName, GridLayout::GridPosition({ 0, 0 }, sVector2D(1, 1), { EChildAlignment::Center, EChildAlignment::Center }));

	LayoutContainer* cardContainer = new LayoutContainer();
	cardContainer->bResizeToFitLayout = { false, false };
	cardContainer->setSize(65, 80);
	cardContainer->setId("result-bracket");
	pResultBracket->add(cardContainer);
	grid->Set(cardContainer, GridLayout::GridPosition({ 0, 1 }, sVector2D(1, 1), { EChildAlignment::Center, EChildAlignment::Center }));

	GridLayout* cardGridLayout = cardContainer->setLayout<GridLayout>();
	cardGridLayout->AddRows({ {}, {} });
	cardGridLayout->AddColumns({ {}, {} });

	for (size_t j = 0; j < cardIndices.size(); ++j)
	{
		auto it = cardsArray.Cards.find(cardIndices[j]);
		if (it == cardsArray.Cards.end())
			continue;

		int suiteValue = static_cast<int>(it->second) / 100;
		int faceValue = static_cast<int>(it->second) % 100;
		auto* pCard = new THistoryCard(faceValue, suiteValue);
		pCard->setId("card-" + std::to_string(cardIndices[j]._to_integral()));
		cardContainer->add(pCard);
		pCard->setOrigin(0.5f, 0.5f);

		if (j < 2)
		{
			cardGridLayout->Set(pCard, GridLayout::GridPosition({ j, 0 }, sVector2D(cardIndices.size() == 1 ? 2 : 1, cardsArray.Cards.contains(cardIndices[2]) ? 1 : 2),
			                                                    { EChildAlignment::Center, EChildAlignment::Center }));
		}
		else if (j == 2)
		{
			pCard->Angle = 90;
			cardGridLayout->Set(pCard, GridLayout::GridPosition({ 0, 1 }, sVector2D(2, 1), { EChildAlignment::Center, EChildAlignment::Center }));
		}
	}

	Label* pResultBracketScore = new Label();
	pResultBracketScore->setId("round-id");
	pResultBracketScore->setCaption(cardIndices.size() == 1 ? CardValueToString(score) : std::to_string(score));
	pResultBracketScore->mTypography = tempDesignTokens::MainFont_400_16;
	pResultBracketScore->TextColor = tempDesignTokens::GreyTextColor;
	pResultBracketScore->setAlignment(align::CENTER_CENTER);
	pResultBracketScore->setSize(65, 24);
	pResultBracket->add(pResultBracketScore);
	grid->Set(pResultBracketScore, GridLayout::GridPosition({ 0, 2 }, sVector2D(1, 1), { EChildAlignment::Center, EChildAlignment::Center }));

	pContainer->add(pResultBracket);
}

void TExternalGamesHistoryEntry::LoadResultAndInfoDataIfNonexistent()
{
	if (bInitialized)
		return;
	TGameHistoryEntry::LoadResultAndInfoDataIfNonexistent();
}

TGameHistory::TGameHistory(const size_t slotID) : gameType(GameType::Null)
{
	setId("game-history-page");
	pCommander = (TPlatformCommander*)pApp->GetModuleByName("Commander");

	mLayout = setLayout<StackLayout>();
	mLayout->Direction = EStackLayoutDirection::VERTICAL;
	mLayout->Alignment = EChildAlignment::Stretch;
	mLayout->Padding = { 0, 12 };
	bResizeToFitLayout = { false, true };

	setFocusMode(EFocusMode::Accept);

	pTitleLabel = new Label();
	pTitleLabel->setId("current-category-label");
	pTitleLabel->setCaption(LocalizedMessage(HISTORY_STRING));
	pTitleLabel->mTypography = tempDesignTokens::TitleFont_24;
	pTitleLabel->TextColor = tempDesignTokens::GoldTextColor;
	pTitleLabel->setSize(300, 68);
	pTitleLabel->setAlignment(align::LEFT_CENTER);
	pTitleLabel->setPadding(Vector2D(124, 32));
	add(pTitleLabel);

	pHistoryContent = new TScrollArea(this, 0, 0, 500, 500);
	pHistoryContent->setId("game-history-content");
	mLayout->setSizeModeFor(pHistoryContent, EWidgetSizeMode::FILL_AVAILABLE_SPACE);
	OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pHistoryContent->markLayoutDirty();
	};

	pHistoryContentLayout = pHistoryContent->getContentLayout();
	pHistoryContentLayout->Alignment = EChildAlignment::Stretch;
	pHistoryContentLayout->Padding = { 12, 12 };
	pHistoryContentLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	pCommander->GetGameState(
	  slotID,
	  [this](GameRunContext& ctx) {
		  for (auto& snapshot : ctx.GameRounds) CreateAndAddGameHistoryEntry(ctx, snapshot);

		  gameType = ctx.RunningGame->Host->Type;

		  OnNewGameRoundSnapshot =
		    ctx.OnNewGameRoundAdded.bind(std::bind(&TGameHistory::CreateAndAddGameHistoryEntry, this, std::placeholders::_1, std::placeholders::_2));
	  },
	  false);
}

void TGameHistory::SetCompactView(bool compact)
{
	if (compact)
	{
		pHistoryContentLayout->Padding = Vector2D(32, 12);
		pTitleLabel->setPadding(Vector2D(50, 32));
	}
	else
	{
		pHistoryContentLayout->Padding = Vector2D(99, 12);
		pTitleLabel->setPadding(Vector2D(124, 32));
	}
	markLayoutDirty();
	pHistoryContent->markLayoutDirty();
}

TGameHistoryEntry* TGameHistory::CreateGameHistoryEntry(const yserver::TGameRoundSnapshotDto& snapshot, const std::string& gameName)
{
	if (gameType == GameType::Roulette || gameType == GameType::VirtualRoulette)
	{
		return new TRouletteHistoryEntry(snapshot, gameName);
	}
	else if (gameType == GameType::Baccarat || gameType == GameType::VirtualBaccarat || (gameType <= GameType::DealersGame && gameType >= GameType::VirtualDragonTiger))
	{
		return new TCardGamesHistoryEntry(gameType, snapshot, gameName);
	}
	else
	{
		return new TGameHistoryEntry(snapshot, gameName);
	}
}

void TGameHistory::CreateAndAddGameHistoryEntry(GameRunContext& ctx, const yserver::TGameRoundSnapshotDto& snapshot)
{
	pGuiApp->DeferToDrawSimple(
	  [this, snapshot, gameName = ctx.RunningGame->Info().Config.GameConfig.DisplayName]() {
		  TGameHistoryEntry* gameEntry = CreateGameHistoryEntry(snapshot, gameName);
		  pHistoryContent->AddContent(gameEntry);
		  gameEntry->moveToBottom();
		  pHistoryContentLayout->setAlignmentFor(gameEntry, EChildAlignment::Stretch);
	  },
	  "TGameHistory::CreateAndAddGameHistoryEntry");
}
