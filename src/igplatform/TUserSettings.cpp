#include "TUserSettings.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"

TUserSettingSectionHolder::TUserSettingSectionHolder(Container* pParent, ELangMessages settingSection, const size_t slotID) : mSlotID(slotID)
{
	if (pParent)
		pParent->add(this);

	setId("user-settings-section-holder");

	mLayout = setLayout<StackLayout>();
	mLayout->Direction = EStackLayoutDirection::VERTICAL;
	mLayout->Alignment = EChildAlignment::Stretch;
	mLayout->Padding = { 32, 12 };
	mLayout->bUsePaddingBeforeFirstAndAfterLast = true;
	bResizeToFitLayout = { false, true };

	plTitle = new Label();
	plTitle->setId("title-label");
	plTitle->setCaption(LocalizedMessage(settingSection));
	plTitle->mTypography = tempDesignTokens::MainFont_600_16;
	plTitle->setSize({ 150, 24 });
	plTitle->setAlignment(align::LEFT_CENTER);
	add(plTitle);

	pContent = new LayoutContainer();
	pContent->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	pContent->bResizeToFitLayout = { false, true };
	pContent->setId("content-container");
	StackLayout* pContentLayout = pContent->setLayout<StackLayout>();
	pContentLayout->Direction = EStackLayoutDirection::VERTICAL;
	pContentLayout->Alignment = EChildAlignment::Stretch;
	pContentLayout->Padding = { 12, 12 };
	pContentLayout->bUsePaddingBeforeFirstAndAfterLast = true;
	add(pContent);

	auto brush = ImageBrush(Image::GetImage("bgTextureDark.png"));
	brush.tiling = true;
	pContent->Background = brush;
	pContent->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
}


LayoutContainer* TUserSettingSectionHolder::CreateSettingLayout(const ELangMessages titleID)
{
	LayoutContainer* pSettingContainer = new LayoutContainer();
	pSettingContainer->setId("setting-container");
	pSettingContainer->setSize(658, 40);
	GridLayout* layout = pSettingContainer->setLayout<GridLayout>();
	layout->AddColumns({ { 35 }, { 65 } });
	layout->AddRow({});

	Label* pSettingName = new Label();
	pSettingName->setId("setting-label");
	pSettingName->setCaption(titleID);
	pSettingName->mTypography = tempDesignTokens::MainFont_400_16;
	pSettingName->setAlignment(align::LEFT_CENTER);
	pSettingName->setPadding(Vector2D(24, 0));
	pSettingContainer->add(pSettingName);
	layout->Set(pSettingName, GridLayout::GridPosition(0, 1, EChildAlignment::Stretch));
	return pSettingContainer;
}

Widget* TUserSettingSectionHolder::CreateSetting(const FGameSetting& setting, const std::function<void(const std::string&, const json&)>& work)
{
	LayoutContainer* pSettingContainer = CreateSettingLayout(setting.GameSettingTitleID);
	pContent->add(pSettingContainer);
	pContent->OnDimensionChanged += [this, pSettingContainer](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pSettingContainer->setWidth(pContent->getWidth());
	};

	GridLayout* layout = pSettingContainer->layout<GridLayout>();

	if (setting.GameSettingType == EGameSettingType::Toggle)
	{
		TToggleButton* toggleButton = CreateToggleButton(pSettingContainer, layout);
		mSettingWidget.insert({ setting.key, toggleButton });
		toggleButton->setChecked((setting.DefaultValue > 0));

		if (work)
			toggleButton->OnCheckedChanged += [work, setting](const bool bChecked) {
				work(setting.key, json(bChecked));
			};

		return toggleButton;
	}
	else if (setting.GameSettingType == EGameSettingType::Slider)
	{
		TStyleSlider* pSliderVolume = CreateSlider(pSettingContainer, layout, setting);
		mSettingWidget.insert({ setting.key, pSliderVolume });
		pSliderVolume->setValue(ESliderInputType::SetByCode, static_cast<int>(setting.DefaultValue));
		pContent->OnDimensionChanged += [this, pSliderVolume](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
			pSliderVolume->setWidth(pContent->getWidth() * 0.6f);
			pSliderVolume->markLayoutDirty();
		};

		if (work)
			pSliderVolume->mpOnChangeCallback = [this, pSliderVolume, setting, work](ESliderInputType const type, int const idx) {
				if (type == ESliderInputType::SetByCode || type == ESliderInputType::Direct)
				{
					work(setting.key, json(idx));

					if (auto task = pSliderTimedTask.lock())
						task->Remove();
					pSliderTimedTask.reset();
				}
				else
				{
					if (!pSliderTimedTask.lock())
					{
						pSliderTimedTask = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthLambda>(
						  [pSliderVolume, setting, work]() { work(setting.key, pSliderVolume->getIndex()); }, 200, rtfwk_sdl2::TTimedTaskHandler::EXECUTE_ONCE,
						  "Send setting to game once per 200ms while dragging.");
					}
				}
			};

		return pSliderVolume;
	}
	// else if (setting.GameSettingType == EGameSettingType::RadioGroup)
	// {
	// 	pSettingName->setVisible(false);
	//
	// 	// TODO implement when we actually have some settings that could be radio :)
	//
	// 	unsigned long counter = 0;
	// 	for (auto option : setting.GameSettingsMetaData.options)
	// 	{
	// 		if (counter >= 2)
	// 			layout->AddColumn({});
	// 		TModernButton* radioButton = new TModernButton(pSettingContainer);
	// 		layout->Set(radioButton, GridLayout::GridPosition({ counter, 0 }, Vector2D(1), { EChildAlignment::Center, EChildAlignment::Center }));
	// 		counter++;
	// 		radioButton->Type = EModernButtonType::Radio;
	// 		radioButton->RadioButtonGroup = "game-categories-" + option.option;
	// 		radioButton->Text->setCaption(option.optionTitle);
	// 		// radioButton->SetPadding(Vector2D(12.f, 6.f), 10.f);
	// 		radioButton->setId("category-" + option.optionTitle);
	// 		radioButton->ClickSound = "button_press_soft.wav";
	// 		radioButton->OnPressed += [this, option]() {
	// 			// set value of option
	// 			std::string setOption = option.optionValue;
	// 		};
	// 	}
	// }

	return pSettingContainer;
}

void TUserSettingSectionHolder::SetSettingValue(const std::string& settingKey, const json& value)
{
	auto it = mSettingWidget.find(settingKey);
	if (it != mSettingWidget.end())
	{
		if (auto toggleButton = dynamic_cast<TToggleButton*>(it->second))
		{
			toggleButton->setChecked(value.get<bool>());
		}
		else if (auto slider = dynamic_cast<TStyleSlider*>(it->second))
		{
			slider->setValue(ESliderInputType::SetByCode, value.get<int>(), false);
		}
	}
}

void TUserSettingSectionHolder::SetCompactView(const bool compact)
{
	if (compact)
		mLayout->Padding = { 32, 12 };
	else
		mLayout->Padding = { 100, 12 };

	markLayoutDirty();
}

TToggleButton* TUserSettingSectionHolder::CreateToggleButton(LayoutContainer* parent, GridLayout* layout)
{
	TToggleButton* toggleButton = new TToggleButton();
	toggleButton->setId("toggle-button");
	parent->add(toggleButton);
	layout->Set(toggleButton, GridLayout::GridPosition(sVector2D(1, 0), 1, { EChildAlignment::Max, EChildAlignment::Center }));
	toggleButton->setSize({ 70, 38 });
	toggleButton->ColorChecked = tempDesignTokens::GameMenuSettingsToggleButtonColor;
	toggleButton->BorderColor = tempDesignTokens::GameMenuSettingsToggleButtonColor;
	toggleButton->OnCheckedChanged += [toggleButton](const bool bChecked) {
		if (bChecked)
		{
			toggleButton->BorderColor = tempDesignTokens::GameMenuSettingsToggleButtonColor;
		}
		else
		{
			toggleButton->BorderColor = Color();
		}
	};
	return toggleButton;
}

TStyleSlider* TUserSettingSectionHolder::CreateSlider(LayoutContainer* parent, GridLayout* layout, const FGameSetting& setting)
{
	TStyleSlider* pSlider =
	  new TStyleSlider(parent, "btnVolume", 0, 0, 420, 32,
	                   setting.GameSettingsMetaData.maxValue - setting.GameSettingsMetaData.minValue + 1);    // +1  ->  (0-100 are 101 different values)
	pSlider->setSliderButtonImages(Image::GetImage("sliderDot.png"), Image::GetImage("sliderDot.png"), Image::GetImage("sliderDot.png"));
	pSlider->showArrows(true);
	pSlider->setId("slider");
	pSlider->showValuesOnSlide(true);
	pSlider->valueLabel()->TextShadow = Shadow();
	pSlider->setFocusMode(EFocusMode::Accept);
	pSlider->setLoopAround(false);
	pSlider->valueLabel()->mTypography = tempDesignTokens::MainFont_500_16;
	layout->Set(pSlider, GridLayout::GridPosition(sVector2D(1, 0), 1, { EChildAlignment::Center, EChildAlignment::Center }));

	auto downImage = Image::GetImage("menuSettingsVolumeDown.png");
	auto upImage = Image::GetImage("menuSettingsVolumeUp.png");
	std::array<TSliderArrowImages, 2> sliderArrowImages = { TSliderArrowImages({ downImage, downImage, downImage, downImage }),
		                                                    TSliderArrowImages({ upImage, upImage, upImage, upImage }) };
	pSlider->SetFlipRightArrowImage(false);
	pSlider->SetSliderButtonBackgroundColors(Transparent, Transparent, Transparent);
	pSlider->setSliderArrowsImages(sliderArrowImages);
	pSlider->SetWriteValueOutsideSlider(true);
	pSlider->TrackBrush = Transparent;
	pSlider->SetShowProgressBar(true);
	return pSlider;
}

void TUserSettings::SendSettingParamValueToGame(const std::string& paramName, const json& value)
{
	json params(json::value_t::object);
	params["name"] = paramName;
	params["value"] = value;
	pCommander->NanoAction(ENanoPlayerAction::SetParam, params);
}

TUserSettings::TUserSettings(Container* pParent, const size_t slotID)
{
	if (pParent)
		pParent->add(this);

	setId("user-settings-page-" + std::to_string(slotID));

	StackLayout* layout = setLayout<StackLayout>();
	layout->Direction = EStackLayoutDirection::VERTICAL;
	layout->Alignment = EChildAlignment::Stretch;
	layout->Padding = { 0, 12 };
	bResizeToFitLayout = { false, true };

	setFocusMode(EFocusMode::Accept);

	pTitleLabel = new Label();
	pTitleLabel->setId("current-category-label");
	pTitleLabel->setCaption(LocalizedMessage(SETTINGS_STRING));
	pTitleLabel->mTypography = tempDesignTokens::TitleFont_24;
	pTitleLabel->TextColor = tempDesignTokens::GoldTextColor;
	pTitleLabel->setSize(300, 68);
	pTitleLabel->setAlignment(align::LEFT_CENTER);
	pTitleLabel->setPadding(Vector2D(124, 32));
	add(pTitleLabel);

	pSettingsContent = new TScrollArea(this, 0, 0, 0, 0);
	pSettingsContent->setId("settings-content");
	layout->setSizeModeFor(pSettingsContent, EWidgetSizeMode::FILL_AVAILABLE_SPACE);

	pCommander = pApp->GetModuleByName<TPlatformCommander>("Commander");

	const std::string genericSettings = "Generic";
	CreateSections({ genericSettings, GENERAL_SETTINGS_STRING }, slotID);

	// SW-3644 -> disabled since we dont know how to properly address unlocking.
	// const FGameSetting lockStation = { "", EGameSettingType::Toggle, LOCK_STATION_STRING, genericSettings, 0, {} };
	// auto lockOnChanged = [](const std::string& paramName, const json& idx) {
	// 	if (MenuGUI()->pClient->Locks->IsLocked())
	// 		return;
	//
	// 	if (idx.get<bool>())
	// 		MenuGUI()->pClient->CLIENT_LOCK_USER->Lock();
	// };
	//
	// auto lockButton = dynamic_cast<TToggleButton*>(mSectionLayouts[genericSettings]->CreateSetting(lockStation, lockOnChanged));
	// MenuGUI()->pClient->CLIENT_LOCK_USER.get()->OnLockChanged += [lockButton](const bool bRaised) {
	// 	if (!bRaised)
	// 		lockButton->setChecked(false);
	// };

	const FGameSetting masterVolumeSlider = { "", EGameSettingType::Slider, AUDIO_VOLUME_STRING, genericSettings, 80, { .minValue = 0, .maxValue = 100, .options = {} } };

	auto generalVolume = dynamic_cast<TStyleSlider*>(mSectionLayouts[genericSettings]->CreateSetting(masterVolumeSlider, {}));
	generalVolume->SetParam(pApp->GetParam("USER_PLAYBACK_VOLUME"));

	const FGameSetting gameVolumeSlider = { "", EGameSettingType::Slider, GAME_VOLUME_STRING, genericSettings, 80, { .minValue = 0, .maxValue = 100, .options = {} } };

	auto gameVolume = dynamic_cast<TStyleSlider*>(mSectionLayouts[genericSettings]->CreateSetting(gameVolumeSlider, {}));
	gameVolume->SetParam(pApp->GetParam("GAME_PLAYBACK_VOLUME"));


	auto commonSetting = [this](const std::string& paramName, const json& idx) {
		SendSettingParamValueToGame(paramName, idx);
	};

	if (slotID != std::numeric_limits<size_t>::max())
		pCommander->GetGameState(
		  slotID,
		  [this, commonSetting, slotID](const GameRunContext& ctx) {
			  const auto& [Sections, Settings] = ctx.RunningGame->IntegrationData().GameSettingsInfo;

			  for (auto& setting : Sections) CreateSections(setting, slotID);

			  for (auto& setting : Settings) mSectionLayouts[setting.GameSettingsSection]->CreateSetting(setting, commonSetting);
		  },
		  true);
}

void TUserSettings::UpdateSettings(const std::string& setting, const json& value)
{
	for (auto& [section, holder] : mSectionLayouts) { holder->SetSettingValue(setting, value); }
}

void TUserSettings::SetCompactView(const bool compact) const
{
	for (auto& [section, holder] : mSectionLayouts) { holder->SetCompactView(compact); }

	pTitleLabel->setPadding(compact ? Vector2D(50, 32) : Vector2D(124, 32));
}

void TUserSettings::CreateSections(const std::pair<std::string, ELangMessages>& settingSection, const size_t slotID)
{
	TUserSettingSectionHolder* pSettingHolder = new TUserSettingSectionHolder(nullptr, settingSection.second, slotID);
	pSettingsContent->AddContent(pSettingHolder);
	pSettingsContent->OnDimensionChanged += [this, pSettingHolder](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pSettingHolder->setWidth(pSettingsContent->getWidth());
	};

	mSectionLayouts.insert({ settingSection.first, pSettingHolder });
}
