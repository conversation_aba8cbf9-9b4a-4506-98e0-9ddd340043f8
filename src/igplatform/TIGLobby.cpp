#include "TIGLobby.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"

TIGLobby::TIGLobby()
{
	layout = setLayout<GridLayout>();
	layout->AddColumns({ { 24 }, { 112 }, { 880 }, { 8 }, { 872 }, { 24 } });    // figma sizes
	layout->bUsePaddingBeforeFirstAndAfterLast = true;
	Transition transition;
	transition.duration = 0.25s;
	transition.timingFunction = CubicBezier::EaseOut;
	layout->DimensionChangeTransition = transition;

	pBackground = new Widget();
	pBackground->setId("background");
	auto menuTabBrush = ImageBrush(Image::GetImage("bgTextureDark.png"));
	menuTabBrush.tiling = true;
	pBackground->Background = menuTabBrush;
	pBackground->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	add(pBackground);
	layout->Set(pBackground, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(2, 1), EChildAlignment::Stretch));

	pMenuTabHolder = new TMenuTabHolder();
	add(pMenuTabHolder);

	layout->Set(pMenuTabHolder, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(1), EChildAlignment::Stretch));
	pMenuTabHolder->OnGameCategorySelected += [this](ELobbyGameCategory category) {
		SetSelectedCategory(category);
	};

	pMenuTabHolder->OnLobbyButtonPressed += [this](ELobbyButtons button) {
		HandleLobbyIconClick(button);
	};

	pLobbyPages = new TLobbyPages();
	add(pLobbyPages);
	layout->Set(pLobbyPages, GridLayout::GridPosition(sVector2D(LEFT_CONTENT_COLUMN_ID, 0), sVector2D(1), EChildAlignment::Stretch));

	pGameView = new TGameView();
	add(pGameView);
	pGameView->setId("game-view");
	layout->Set(pGameView, { { RIGHT_CONTENT_COLUMN_ID, 0 }, 1, EChildAlignment::Stretch });
	pGameView->OnDeath += [this](Widget* src) {
		pGameView = NULL;
	};
	pGameView->OnGameRemoved += [this](size_t id) {
		ActiveGameCounter--;
		pGameMenu->RemoveGame(id);
		SetLayouts();
		RecalculateContentPositions();
	};
	pGameView->OnAllGamesClosed += [this]() {
		ActiveGameCounter = 0;
		pMenuTabHolder->SetSelectedButton(ELobbyButtons::AddGame);
	};
	pLobbyPages->mCloseButton->OnPressed += [this]() {
		LobbyVisible = false;
		SetLayouts();
	};
	pGameView->OnAddGamePressed += [this]() {
		AddGamePressed();
	};

	pGameMenu = new TGameMenu();
	pGameMenu->setVisible(false);
	pGameMenu->mCloseButton->OnPressed += [this]() {
		SideMenuVisible = false;
		SetLayouts();
	};

	add(pGameMenu);
	layout->Set(pGameMenu, GridLayout::GridPosition(sVector2D(LEFT_CONTENT_COLUMN_ID, 0), sVector2D(1), EChildAlignment::Stretch));


	SetLayouts();
}

void TIGLobby::SetSelectedActiveGame(const size_t slotID) const
{
	pGameView->SetSelectedActiveGame(slotID);
}

void TIGLobby::ChangeGameSelected(bool selected) const
{
	pLobbyPages->SetChangeGameMode(selected);
}

void TIGLobby::SetChangeGameMode(bool changeGameMode)
{
	pGameView->SetChangeGameMode(changeGameMode);
	pLobbyPages->SetChangeGameMode(false);
}

void TIGLobby::AddGameThumbnail(const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf, const igp::FPlatformEnvironment& env) const
{
	if (pLobbyPages)
		pLobbyPages->AddAndGetCategoryButton(game, conf, env);
}

void TIGLobby::RecalculateContentPositions() const
{
	pLobbyPages->RedrawContent();


	const std::set<ELobbyGameCategory> VisibleCategories = pLobbyPages->GetAndSetVisibleFiltersAndCategories();

	for (auto value : ELobbyGameCategory::_values()) pMenuTabHolder->SetCategoryVisible(value, VisibleCategories.contains(value));
}

void TIGLobby::AddGame(BrowserWindow* gameWindow, const size_t gameSlotID, const bool focusNewGame)
{
	if (pGameView)
		pGameView->AddGame(gameWindow, gameSlotID, focusNewGame);
	ActiveGameCounter++;
	LobbyVisible = false;
	SideMenuVisible = false;
	SetLayouts();
}

void TIGLobby::SetLayouts()
{
	if (ActiveGameCounter == 0)
	{
		if (SideMenuVisible)
		{
			layout->Columns()[LEFT_CONTENT_COLUMN_ID].Size = 880;
			layout->Columns()[RIGHT_CONTENT_COLUMN_ID].Size = 872;
			layout->Columns()[MIDDLE_COLUMN_DIVIDER_ID].Size = 8;

			layout->Set(pLobbyPages, GridLayout::GridPosition(sVector2D(RIGHT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
			layout->Set(pBackground, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(2, 1), EChildAlignment::Stretch));
		}
		else
		{
			layout->Columns()[LEFT_CONTENT_COLUMN_ID].Size = 0;
			layout->Columns()[RIGHT_CONTENT_COLUMN_ID].Size = 1760;
			layout->Columns()[MIDDLE_COLUMN_DIVIDER_ID].Size = 0;

			layout->Set(pLobbyPages, GridLayout::GridPosition(sVector2D(RIGHT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
			layout->Set(pBackground, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(4, 1), EChildAlignment::Stretch));
			pMenuTabHolder->SetSelectedButton(ELobbyButtons::AddGame, false);
		}

		pGameView->fadeOut(0.1s);
		pLobbyPages->mCloseButton->fadeOut(0.1s);
		pLobbyPages->GoToPage(0);
		LobbyVisible = true;
	}
	else if (LobbyVisible || SideMenuVisible)
	{
		pLobbyPages->mCloseButton->fadeIn(0.1s);
		if (ActiveGameCounter <= 2)
		{
			layout->Columns()[LEFT_CONTENT_COLUMN_ID].Size = 880;
			layout->Columns()[RIGHT_CONTENT_COLUMN_ID].Size = 872;
		}
		else
		{
			layout->Columns()[LEFT_CONTENT_COLUMN_ID].Size = 538;
			layout->Columns()[RIGHT_CONTENT_COLUMN_ID].Size = 1214;
		}
		layout->Columns()[MIDDLE_COLUMN_DIVIDER_ID].Size = 8;

		layout->Set(pLobbyPages, GridLayout::GridPosition(sVector2D(LEFT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
		layout->Set(pGameMenu, GridLayout::GridPosition(sVector2D(LEFT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
		layout->Set(pBackground, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(2, 1), EChildAlignment::Stretch));
		pGameView->fadeIn(0.3s);
		layout->Set(pGameView, GridLayout::GridPosition(sVector2D(RIGHT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
		pLobbyPages->GoToPage(0);
	}
	else
	{
		layout->Columns()[LEFT_CONTENT_COLUMN_ID].Size = 0;
		layout->Columns()[RIGHT_CONTENT_COLUMN_ID].Size = 1752;
		layout->Columns()[MIDDLE_COLUMN_DIVIDER_ID].Size = 8;
		layout->Set(pGameView, GridLayout::GridPosition(sVector2D(RIGHT_CONTENT_COLUMN_ID, 0), sVector2D(1, 1), EChildAlignment::Stretch));
		pGameView->fadeIn(0.1s);
		pMenuTabHolder->DeselectAllLobbyButtons();
		layout->Set(pBackground, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(1, 1), EChildAlignment::Stretch));
	}

	pLobbyPages->setVisible(LobbyVisible);

	pLobbyPages->SetCompactView(ActiveGameCounter > 2 && LobbyVisible);
	pGameMenu->SetCompactView(ActiveGameCounter > 2 && SideMenuVisible);

	pGameMenu->setVisible(SideMenuVisible);

	dynamic_cast<TIGPlatformApp*>(pApp)->LobbyOpened(LobbyVisible);

	if (ActiveGameCounter == dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames() && LobbyVisible)
		dynamic_cast<TIGPlatformApp*>(pApp)->SetChangeGameMode(true);
	else
		dynamic_cast<TIGPlatformApp*>(pApp)->SetChangeGameMode(false);

	pGameView->SetLobbyVisible(LobbyVisible || SideMenuVisible);
	pMenuTabHolder->SetNumberOfActiveGames(ActiveGameCounter);
	pMenuTabHolder->SetLobbyVisible(LobbyVisible && !SideMenuVisible);
	markLayoutDirty();
}

void TIGLobby::SetSelectedCategory(ELobbyGameCategory category) const
{
	pLobbyPages->SetSelectedCategory(category);
}

void TIGLobby::HandleLobbyIconClick(ELobbyButtons category)
{
	switch (category)
	{
		case ELobbyButtons::CloseAllGames: {
			if (ActiveGameCounter > 0)
				dynamic_cast<TIGPlatformApp*>(pApp)->OnUserCloseAllGames();
			break;
		}
		case ELobbyButtons::ChangeGame:
		case ELobbyButtons::AddGame: {
			pGameMenu->SetSelectedCategory(ELobbyButtons::AddGame);
			AddGamePressed();
			break;
		}
		default: OpenSideMenu(category);
	}
}

void TIGLobby::AddGamePressed()
{
	LobbyVisible = true;
	SideMenuVisible = false;
	SetLayouts();
}

void TIGLobby::OpenSideMenu(ELobbyButtons button)
{
	SideMenuVisible = true;
	LobbyVisible = false;
	pGameMenu->SetSelectedCategory(button);
	SetLayouts();
}
