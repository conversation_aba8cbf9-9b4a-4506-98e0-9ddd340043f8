
#include "TIGLobbyView.h"

#include "Cryptography.h"
#include "MagicNumbers.h"
#include "MyUtils.h"
#include "PlatformCommander.h"
#include "TGameInfoPanel.h"
#include "TIGPlatformApp.h"
#include "TOtherStationsPanel.h"
#include "TPlatformClient.h"
#include "TotallyTemporaryDesignTokens.h"
#include "drv/system/SAS/TSASModule.h"
#include "gui/panels/TScreenshotViewer.h"

using namespace magic;

#define BALANCE (static_cast<TMachineBalanceManager*>(MenuGUI()->pClient->BalanceManager()))

constexpr float ANIMATION_FRAME_RATE = 30.f;

TIGLobbyView::TIGLobbyView(Container* pParent, float x, float y, float w, float h) : TPanelBase(pParent, x, y, w, h)
{
	bCreateFocusableChildren = false;
	bAlwaysShowAllCategories = pApp->GetParamFromCache("AlwaysShowCategories")->AsBoolean();

	pCommander = pApp->GetModuleByName<TPlatformCommander>("Commander");
	pCommander->OnNanoEvent += [this](const std::string& playerID, const std::string& event, const json& eventBody) {
		if (event == "player-authenticated")
		{
			HideQRCode(eventBody["session"].get<std::string>());
		}
	};
	pCommander->OnPlayerSettingsChanged += [this](const std::string& param, const json& value) {
		if (pLobby->pGameMenu)
			pLobby->pGameMenu->UpdateGameSettingValues(param, value);
	};

	Packman = pApp->GetModuleByName<TPackmanClient>("Packman");

	const float screenWidth = pApp->GetParam("ScreenW")->AsFloat();
	const float screenHeight = pApp->GetParam("ScreenH")->AsFloat();

	GameLayer = new LayoutContainer();
	GameLayer->setId("game-layer");
	GameLayer->bResizeToFitLayout = false;
	GameLayer->setDimension({ 0, 0, screenWidth, screenHeight });
	GameLayerLayout = GameLayer->setLayout<GridLayout>();

	add(GameLayer);

	pPanelMenu = ((TMenuGUI*)pGuiApp->GUI())->PanelMenu;

	// these are figma px sizes
	GridLayout::FieldSize TopRow = { .Size = 112 };
	GridLayout::FieldSize BottomRow = { .Size = 928, .Margin = 0 };
	GridLayout::FieldSize LegalRow = { .Size = 40 };    // legals

	GameLayer->Background = Black;

	GameLayerLayout->AddRow(TopRow);
	GameLayerLayout->AddRow(BottomRow);
	GameLayerLayout->AddRow(LegalRow);

	pDemoOverlay = new Container();
	pDemoOverlay->setSize(getSize());
	pDemoOverlay->setBackgroundImage(Image::GetImage("ClientDemo.png"));
	pDemoOverlay->setVisible(MenuGUI()->pClient->IsInDemoMode());
	pDemoOverlay->setMouseInput(EventPropagateMode::NONE);
	pDemoOverlay->setId("demo-overlay");
	add(pDemoOverlay);

	pTopInfoBar = new TTopInfoBar(GameLayer);
	GameLayerLayout->Set(pTopInfoBar, { { 0, 0 }, 1, EChildAlignment::Stretch });
	pTopInfoBar->OnQRPayoutBtnPressed += [this]() {
		if (pPlayAwayDialog)
		{
			pPlayAwayDialog->setY((getHeight() - pPlayAwayDialog->getHeight()) / 2, 0.5s);
			pPlayAwayDialog->fadeIn();
			pPlayAwayDialog->moveToTop();
			pGuiApp->GUI()->requestModalFocus(pPlayAwayDialog, false);
		}
	};
	pTopInfoBar->OnPayoutPressed += std::bind(&TIGLobbyView::OnPayout, this);
	pTopInfoBar->OnCallAttendantPressed += std::bind(&TIGLobbyView::OnCallAttendantPressed, this);
	pTopInfoBar->OnDeath += [this](Widget* src) {
		pTopInfoBar = NULL;
	};

	pLobby = new TIGLobby();
	pLobby->setId("lobby");
	GameLayer->add(pLobby);
	GameLayerLayout->Set(pLobby, { { 0, 1 }, 1, EChildAlignment::Stretch });
	pLobby->OnDeath += [this](Widget* src) {
		pLobby = NULL;
	};

	pLobby->pGameView->OnAllGamesClosed += std::bind(&TIGLobbyView::OnAllGamesClosed, this);

	// TODO wtf do i do with this :)
	//  PopUpLayer = new LayoutContainer();
	//  PopUpLayer->setId("popup-layer");
	//  PopUpLayer->bResizeToFitLayout = false;
	//  PopUpLayer->setDimension({ 0, 0, screenWidth, screenHeight });
	//  add(PopUpLayer);

	const int LEGAL_TEXT_MARGIN = 40;
	pLegalBar = new Container();
	pLegalBar->setId("legal-bar");
	pLegalBar->setOrigin(0.f, 1.f);
	pLegalBar->setPosition({ 0, screenHeight });
	GameLayerLayout->Set(pLegalBar, { { 0, 2 }, { 1, 1 }, EChildAlignment::Stretch });
	GameLayer->add(pLegalBar);

	pLegalBar->OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pStationID->setPosition(LEGAL_TEXT_MARGIN, pLegalBar->getHeight() / 2);
		pMalfunctionVoids->setPosition(pLegalBar->getWidth() / 2, pLegalBar->getHeight() / 2);

		if (pAssetNum)
			pAssetNum->setPosition(pLegalBar->getWidth() - LEGAL_TEXT_MARGIN, pLegalBar->getHeight() / 2);
	};

	pStationID = new Label(LocalizedMessage(STATION_ID_STRING) + ": " + std::to_string(dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Number));
	pStationID->setId("station-id");
	pStationID->setOrigin(0.f, 0.5f);
	pStationID->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pStationID->mTypography = tempDesignTokens::MainFont_400_16;
	pStationID->TextColor = tempDesignTokens::GreyTextColor;
	pStationID->setAlignment(align::LEFT_CENTER);
	pStationID->setPosition(LEGAL_TEXT_MARGIN, pLegalBar->getHeight() / 2);
	pLegalBar->add(pStationID);

	pMalfunctionVoids = new Label(LocalizedMessage(MSG_MALFUNCTION_VOIDS_ALL_PLAYS_AND_PAYS));
	pMalfunctionVoids->setId("malfunction-voids");
	pMalfunctionVoids->setOrigin(0.5f, 0.5f);
	pMalfunctionVoids->setResizeMode(Label::ResizeMode::AUTOSIZE);
	pMalfunctionVoids->mTypography = tempDesignTokens::MainFont_400_16;
	pMalfunctionVoids->TextColor = tempDesignTokens::GreyTextColor;
	pMalfunctionVoids->setAlignment(align::CENTER_CENTER);
	pMalfunctionVoids->setPosition(pLegalBar->getWidth() / 2, pLegalBar->getHeight() / 2);
	pLegalBar->add(pMalfunctionVoids);

	auto sas = pApp->GetModuleByName<TSASModule>("SAS1");
	if (sas && sas->IsEnabled())
	{
		pAssetNum = new Label(LocalizedMessage(SAS_ASSET_NUMBER_STRING) + ": " + pApp->GetParam("AFT_ASSET_NUMBER")->AsString());
		pAssetNum->setId("asset-number");
		pAssetNum->setOrigin(1.f, 0.5f);
		pAssetNum->setResizeMode(Label::ResizeMode::AUTOSIZE);
		pAssetNum->mTypography = tempDesignTokens::MainFont_400_16;
		pAssetNum->TextColor = tempDesignTokens::GreyTextColor;
		pAssetNum->setAlignment(align::RIGHT_CENTER);
		pAssetNum->setPosition(pLegalBar->getWidth() - LEGAL_TEXT_MARGIN, pLegalBar->getHeight() / 2);
		pLegalBar->add(pAssetNum);
	}

	std::shared_ptr<TAppParam> allowZeroCashout = pApp->GetParam("ALLOW_ZERO_CASHOUT", { ParameterDomain::CACHE });
	allowZeroCashout->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
			bAllowZeroCashout = param->AsBoolean();
	};
	allowZeroCashout->OnParamChange(allowZeroCashout.get(), { ParameterProperty::OLD_VALUE }, ParameterChangeSource::Application);

	// NOTE GLOBAL FONT IS SET TO AppFont.ttf 15pix
	RibbonTextTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 24_px });    // system buttons
	RibbonTextTypography.OnChanged += [this](Typography oldVal, Typography newVal) {
		Resources.RibbonFont = FontLibrary.Get(newVal);
		RefreshGameData(false);
	};

	Resources.RibbonFont = FontLibrary.Get(RibbonTextTypography.Value());
	Resources.RibbonBlue = Image::GetImage("ribbon.png");
	if (Resources.RibbonBlue)
		GPU_SetBlendMode(Resources.RibbonBlue->getTexture(), GPU_BLEND_NORMAL_FACTOR_ALPHA);
	Resources.RibbonRed = Image::GetImage("ribbon_red.png");
	if (Resources.RibbonRed)
		GPU_SetBlendMode(Resources.RibbonRed->getTexture(), GPU_BLEND_NORMAL_FACTOR_ALPHA);
	Resources.GraphicsPtr = pGuiApp->GUI()->getGraphics();

	const size_t allConfigs = Packman->NumConfigurations();
	if (allConfigs)
	{
		const LocalizedMessage& prevLoadingStage = pApp->CurrentLoadingStage();
		pApp->StartLoadingStage(LocalizedMessage(LOADING_GAME_ASSETS_STRING));
		pApp->AddLoadingWorkItem("Games", allConfigs);

		size_t numCompleted = 0;
		TLOG(LogLoading, Normal, "Loading game package textures...");

		pGuiApp->GUI()->drawFrame();
		const igp::FPlatformEnvironment env(dynamic_cast<TIGPlatformApp*>(pApp)->GetPlatformEnvironment(true));
		Packman->ForAllGameConfigurations([this, &numCompleted, &env, &allConfigs](const std::shared_ptr<igp::PlatformGamePackage>& game,
		                                                                           const std::shared_ptr<igp::PlatformGameConfiguration>& conf) -> bool {
			// always first update the game icon before adding it to the lobby!!!
			conf->UpdateButtonIcon(Resources, game->Package.Game.Type);

			pLobby->AddGameThumbnail(game, *conf, env);

			conf->OnGameConfigurationUpdated(*conf);
			numCompleted++;
			pApp->SetLoadingWorkItemProgress("Games", numCompleted);
			if (numCompleted < allConfigs)
				pGuiApp->GUI()->drawFrame();

			return true;
		});
		pLobby->RecalculateContentPositions();

		pApp->StartLoadingStage(prevLoadingStage);
		TLOG(LogLoading, Info, "Loading game menu graphics...");
		pGuiApp->GUI()->drawFrame();
	}

	/* izplačila -- podvojena instanca za izplačilo - tale je za igralca */
	pPnlPlayerPayout = new TUserPayoutPanel(pParent, (w - 480) / 2, (h - 800) / 2, 480, 800);
	pPnlPlayerPayout->setVisible(false);
	pPnlPlayerPayout->setId("payout-panel");

	// /* DISPLAY MODE */
	// mScreenColorMode = 0;
	// for (int i = 0; i < 3; i++)
	// {
	// 	pBrightnessIcons[2 * i] = Image::GetImage("displayMode_" + std::to_string(i + 1) + ".png");
	// 	pBrightnessIcons[2 * i + 1] = Image::GetImage("displayMode_" + std::to_string(i + 1) + "_H.png");
	// }
	//
	// pDisplayMode = AddButton("btnDisplayMode", {}, pBtnLockPlayboard->getX(), pBtnLockPlayboard->getDimension().bottom() + 15, buttonSize1, buttonSize1);
	// pDisplayMode->setOrigin(1.f, 0.f);
	// pDisplayMode->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_FIT;
	// pDisplayMode->setNormalImage(pBrightnessIcons[0]);
	// pDisplayMode->setHighImage(pBrightnessIcons[1]);
	// pDisplayMode->setDownImage(pBrightnessIcons[1]);
	// pDisplayMode->OnPressed += [this]() {
	// 	SelectBrightness((mScreenColorMode + 1) % 3);
	// };
	// MyUtils::SystemAsync("redshift -l manual -x -m randr > /dev/null 1>&2;");


	// this should be called when order and filter buttons are already created!
	bGameRefreshBlocked = false;
	RefreshGameData(true);


	/* MUSIC ICON */
	if (pApp->GetModuleByName<TPackmanClient>("Packman")->Features().contains(igp::MultiplayerFeatureName))
	{
		pGuiApp->pMusicVolume->SetValue(0);
		pGuiApp->pMusicVolume->Save();
	}

	pGuiApp->pMusicVolume->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::VALUE) && param->AsInteger())
			mLastMusicVolume = param->AsInteger();
	};
	mLastMusicVolume = pGuiApp->pMusicVolume->AsInteger();
	if (!mLastMusicVolume)
		mLastMusicVolume = 40;


	std::shared_ptr<TAppParam> payoutButton = pApp->GetParam("PAYOUT_BUTTON_DISABLED", { ParameterDomain::CACHE });
	payoutButton->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
			PayoutButtonDisabled = param->AsInteger();
	};
	payoutButton->OnParamChange(payoutButton.get(), { ParameterProperty::OLD_VALUE }, ParameterChangeSource::Application);

	std::list<std::pair<TSpriteAnimation*, std::shared_future<void>>> animationLoadTasks;
	TLOG(LogLoading, Info, "Loading animations...");
	const int TOTAL_ANIMS = 4;
	int numAnimsLoaded = 0;
	int numAminSpritesLoaded = 0;
	pGuiApp->AddLoadingWorkItem("anims", TOTAL_ANIMS);
	pGuiApp->GUI()->drawFrame();

	// JACKPOT DIALOG
	pJackpotDialogBG = new Container();
	pJackpotDialogBG->setId("jackpot-dialog");
	pJackpotDialogBG->setSize(getSize());
	pJackpotDialogBG->Background = Black.Fade(0.5f);
	pJackpotDialogBG->setConsumeAllMouseEvents(true);
	pJackpotDialogBG->setVisible(false);
	add(pJackpotDialogBG);

	Rectangle bgSize(396, 165, 1128, 750);
	const Vector2D resizeF = bgSize.Size / TGameButton::ButtonSize;

	TSpriteAnimation* jackpotBorderAnimation = new TSpriteAnimation(pJackpotDialogBG, 0, 0, TGameButton::ButtonSize.X(), TGameButton::ButtonSize.Y(),
	                                                                "Animation/GameSelect", EAnimationMode::LOOP, "pJackpotBorderAnimation");
	jackpotBorderAnimation->setId("jackpot-border-anim");
	jackpotBorderAnimation->SetRefreshRate(ANIMATION_FRAME_RATE);
	jackpotBorderAnimation->SetOffset(bgSize.x() - 3, bgSize.y() - 3);
	jackpotBorderAnimation->SetResizeFactor(resizeF.X(), resizeF.Y());

	Icon* jackpotImg = new Icon("Celebration.jpg");
	jackpotImg->setDimension(bgSize);
	jackpotImg->ScaleMode = EScaleMode::STRETCH;
	pJackpotDialogBG->add(jackpotImg);

	pJackpotFireworksAnimation = new TSpriteAnimation(pJackpotDialogBG, 0, 0, TGameButton::ButtonSize.X(), TGameButton::ButtonSize.Y(), "Animation/Fireworks",
	                                                  EAnimationMode::ONCE, "pJackpotFireworksAnimation");
	pJackpotFireworksAnimation->setId("jackpot-fireworks-anim");
	pJackpotFireworksAnimation->SetRefreshRate(ANIMATION_FRAME_RATE);
	pJackpotFireworksAnimation->SetOffset(bgSize.x(), bgSize.y());
	pJackpotFireworksAnimation->SetResizeFactor(resizeF.X(), resizeF.Y());

	animationLoadTasks.push_back({ jackpotBorderAnimation, jackpotBorderAnimation->GetLoadingTask() });
	animationLoadTasks.push_back({ pJackpotFireworksAnimation, pJackpotFireworksAnimation->GetLoadingTask() });


	auto lastTick = std::chrono::steady_clock::now();
	while (!animationLoadTasks.empty())
	{
		auto now = std::chrono::steady_clock::now();
		const float budgetSeconds = (1e3f / pGuiApp->GUI()->getFPS() - float(std::chrono::duration_cast<std::chrono::milliseconds>(now - lastTick).count())) * 1e-3f;
		if (budgetSeconds >= 1e-3f)
		{
			if (animationLoadTasks.front().second.valid())
			{
				if (animationLoadTasks.front().second.wait_for(std::chrono::milliseconds((int64_t)std::floor(budgetSeconds * 1e3f))) == std::future_status::ready)
				{
					numAnimsLoaded++;
					animationLoadTasks.front().second = {};
				}
			}
			else if (animationLoadTasks.front().first->EnsureLoaded(budgetSeconds))
			{
				numAminSpritesLoaded++;
				animationLoadTasks.pop_front();
			}
		}
		else
		{
			pGuiApp->SetLoadingWorkItemProgress("anims", numAnimsLoaded * 0.8f + numAminSpritesLoaded * 0.2f);
			pGuiApp->GUI()->drawFrame();
			lastTick = std::chrono::steady_clock::now();
		}
	}

	pGuiApp->SetLoadingWorkItemProgress("anims", numAnimsLoaded);
	pGuiApp->GUI()->drawFrame();

	TGlyphLabel* jackpotHeader = new TGlyphLabel(pJackpotDialogBG, pJackpotDialogBG->getWidth() / 2, pJackpotDialogBG->getHeight() / 2 - 100, 0, 0,
	                                             LocalizedMessage(JACKPOT_WON_STRING), "Fonts/Captions", "jackpot-header-label");
	jackpotHeader->SetAutosize(true, true);
	jackpotHeader->SetUppercase(true);
	jackpotHeader->setOrigin(0.5f, 0.5f);
	jackpotHeader->SetResizeFactor(2.f, 2.f);

	pJackpotAmount = new TGlyphLabel(pJackpotDialogBG, pJackpotDialogBG->getWidth() / 2, pJackpotDialogBG->getHeight() / 2 + 100, 0, 0, LocalizedMessage(),
	                                 "Fonts/Captions", "jackpot-amount-label");
	pJackpotAmount->SetAutosize(true, true);
	pJackpotAmount->SetUppercase(true);
	pJackpotAmount->setOrigin(0.5f, 0.5f);
	pJackpotAmount->SetResizeFactor(2.f, 2.f);

	pJackpotDialogBG->OnClicked += [this](Widget* clicked, const Vector2D& pos) {
		pJackpotDialogBG->fadeOut();
	};

	TLOG(LogLoading, Info, "Loading PlayAway...");
	pGuiApp->GUI()->drawFrame();

	pQRPanelBackground = new Container(this, 0, 0, w, h);
	pQRPanelBackground->setId("qr-panel-bg");
	pQRPanelBackground->BackgroundImageScaleMode = EScaleMode::STRETCH;
	pQRPanelBackground->setVisible(false);
	pQRPanelBackground->Background = Black.Fade(0.6f);
	pQRPanelBackground->setConsumeAllMouseEvents(true);
	pQRPanelBackground->setConsumeAllKeyEvents(true);
	pQRPanelBackground->OnClicked += [this](Widget* clicked, const Vector2D& pos) {
		HideQRCode({});
	};

	pQRCode = new TQRCode(pQRPanelBackground, w / 2.f, h / 2.f, 360, 360);
	pQRCode->setOrigin(0.5f);
	pQRCode->Background = White;
	pQRCode->ForegroundColor = Black;

	Label* QRInfoLabel = new Label(LocalizedMessage(SCAN_ME_STRING));
	QRInfoLabel->setResizeMode(Label::AUTOSIZE);
	QRInfoLabel->setOrigin(0.5f, 1.f);
	QRInfoLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 72_px });
	QRInfoLabel->setFontStyle(TTF_STYLE_BOLD);
	pQRPanelBackground->add(QRInfoLabel, w / 2, pQRCode->getDimension().top() - 50);

	pQRCountdownLabel = new Label();
	pQRCountdownLabel->setResizeMode(Label::AUTOSIZE);
	pQRCountdownLabel->setOrigin(0.5f, 0.f);
	pQRCountdownLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 62_px });
	pQRCountdownLabel->setFontStyle(TTF_STYLE_BOLD);
	pQRPanelBackground->add(pQRCountdownLabel, w / 2, pQRCode->getDimension().bottom() + 50);

	Label* TouchAnywhereToCloseLabel = new Label(LocalizedMessage(TOUCH_ANYWHERE_TO_CLOSE_STRING));
	TouchAnywhereToCloseLabel->setOrigin(0.5f, 1.f);
	TouchAnywhereToCloseLabel->setResizeMode(Label::AUTOSIZE);
	TouchAnywhereToCloseLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 24_px });
	TouchAnywhereToCloseLabel->setFontStyle(TTF_STYLE_BOLD);
	pQRPanelBackground->add(TouchAnywhereToCloseLabel, w / 2, h - 150);


	ImagePtr playAwayBg = Image::GetImage("playaway_bg.png");
	pPlayAwayDialog = new Container(this, (getWidth() - playAwayBg->getWidth()) / 2, getHeight(), playAwayBg->getWidth(), playAwayBg->getHeight());
	pPlayAwayDialog->setBackgroundImage(playAwayBg);
	pPlayAwayDialog->setVisible(false);

	auto lbl = new Label(LocalizedMessage(HOW_THIS_WORKS_STRING));
	lbl->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 36_px });
	lbl->setOrigin(0.5f, 0.f);
	lbl->setResizeMode(Label::AUTOSIZE);
	pPlayAwayDialog->add(lbl, pPlayAwayDialog->getWidth() / 2, 410);

	int instructionsY = 480;
	std::vector<LocalizedMessage> instructions = { LocalizedMessage(PLAYAWAY_INSTRUCTION1_STRING), LocalizedMessage(PLAYAWAY_INSTRUCTION2_STRING),
		                                           LocalizedMessage(PLAYAWAY_INSTRUCTION3_STRING), LocalizedMessage(PLAYAWAY_INSTRUCTION4_STRING) };

	for (const LocalizedMessage& instruction : instructions)
	{
		lbl = new Label("• " + instruction);
		lbl->setWrap(true);
		lbl->setAlignment(align::LEFT_CENTER);
		lbl->IndentAfterFirstLine = 10.f;
		lbl->setResizeMode(Label::NONE, Label::AUTOSIZE);
		lbl->setWidth(380);
		pPlayAwayDialog->add(lbl, 270, instructionsY);
		instructionsY += 40;
	}

	pCancelPlayAwayBtn = new Label(LocalizedMessage(CANCEL_STRING));
	pCancelPlayAwayBtn->setForceUpperCase(true);
	pCancelPlayAwayBtn->setId("pa-cancel-button");
	pCancelPlayAwayBtn->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 28_px });
	pCancelPlayAwayBtn->setPadding(Rectangle(20, 15, 20, 15));
	pCancelPlayAwayBtn->OutlineStyle = BorderStyle({ .color = White, .width = 4_px });
	pCancelPlayAwayBtn->setMouseInput(EventPropagateMode::NORMAL);
	pCancelPlayAwayBtn->setResizeMode(Label::AUTOSIZE);
	pCancelPlayAwayBtn->setOrigin(0, 1);
	pPlayAwayDialog->add(pCancelPlayAwayBtn, 270, 810);

	pCancelPlayAwayBtn->OnClicked += [this](Widget* src, const Vector2D& pos) {
		pGuiApp->PlaySound("button_press.wav");
		HidePlayAway();
	};

	pConfirmPlayAwayBtn = new Label(LocalizedMessage(CONFIRM_STRING));
	pConfirmPlayAwayBtn->setForceUpperCase(true);
	pConfirmPlayAwayBtn->setId("pa-confirm-button");
	pConfirmPlayAwayBtn->mTypography = pCancelPlayAwayBtn->mTypography;
	pConfirmPlayAwayBtn->setPadding(Rectangle(20, 15, 20, 15));
	pConfirmPlayAwayBtn->OutlineStyle = BorderStyle({ .color = White, .width = 4_px });
	pConfirmPlayAwayBtn->setMouseInput(EventPropagateMode::NORMAL);
	pConfirmPlayAwayBtn->setResizeMode(Label::AUTOSIZE);
	pConfirmPlayAwayBtn->setOrigin(1, 1);
	pPlayAwayDialog->add(pConfirmPlayAwayBtn, 650, 810);

	pConfirmPlayAwayBtn->OnClicked += [](Widget* src, const Vector2D& pos) {
		pGuiApp->PlaySound("button_press.wav");
		dynamic_cast<TIGPlatformApp*>(pApp)->PrintPlayAwayTicket();
	};

	pGameInfoPanelBG = new Widget();
	pGameInfoPanelBG->setId("game-info-bg");
	pGameInfoPanelBG->setConsumeAllMouseEvents(true);
	pGameInfoPanelBG->setSize(getSize());
	pGameInfoPanelBG->setVisible(false);
	pGameInfoPanelBG->Background = Black.Fade(0.5f);
	add(pGameInfoPanelBG);

	pGameInfoPanelBG->OnClicked += std::bind(&TIGLobbyView::HideGameInfo, this);

	pGameInfoPanel = new TGameInfoPanel(this, Vector2D(900, 900));
	pGameInfoPanel->setVisible(false);
	pGameInfoPanel->setMouseInput(EventPropagateMode::NONE);

	// if (getenv("FORCE_MULTIPLAYER") || Packman->Features().contains(igp::MultiplayerFeatureName))
	// {
	// 	auto terminalNumberLabel = AddButton({}, std::to_string(dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Number),
	// 	                                     getWidth() - MenuGUI()->ScreenEdgePadding.width(), pBtnDenomi->getY() - 12.f, buttonSize1, buttonSize1);
	// 	terminalNumberLabel->setEnabled(false);
	// 	terminalNumberLabel->setOrigin(1.f, 1.f);
	// 	terminalNumberLabel->OverrideTextDisabledColor = White;
	// 	terminalNumberLabel->setBorderRadius(0.5f, true);
	// 	terminalNumberLabel->Border = BorderStyle({ .color = BoxBorderColor, .width = { BoxBorderSize, EDimensionUnit::px } });
	// 	terminalNumberLabel->setId("station-number");
	// 	terminalNumberLabel->Text->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 30_px });
	// }

	// pApp->AddActionListener("EnableMenu", new TahMember<TIGLobbyView, void>(this, &TIGLobbyView::EnableMenuActions)); //da ne prestrezamo eventov
	// ko se igra nalaga in ko je pognana

	OnLockedHandle = MenuGUI()->pClient->Locks->OnLocked.bind(std::bind(&TIGLobbyView::OnClientLock, this));
	OnLockChangedHandle = MenuGUI()->pClient->Locks->OnLockChanged.bind(std::bind(&TIGLobbyView::OnClientLock, this));
	OnUnlockedHandle = MenuGUI()->pClient->Locks->OnUnlocked.bind(std::bind(&TIGLobbyView::OnClientUnlock, this));


	MenuGUI()->pClient->OnVirtualDenomChanged += [this](int coeff) {
		pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::OnVirtualDenomChanged, this, coeff), "TIGLobbyView::OnVirtualDenomChanged", false, this);
		if (!bGameRefreshBlocked)
			pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::RefreshGameData, this, false), "TIGLobbyView::RefreshGameData", false, this);
	};

	// AFT Message Dialog
	pApp->NewActionListener<TahMemberLarge<TIGLobbyView>, ACTION_DRAW>("ServiceMessage", this, &TIGLobbyView::ShowMessageToPlayer, nullptr);
	pApp->NewActionListener<TahMemberLarge<TIGLobbyView>, ACTION_DRAW>("ShowMessage", this, &TIGLobbyView::ShowMessageToPlayer, nullptr);
	pApp->NewActionListener<TahMemberLarge<TIGLobbyView>, ACTION_DRAW>("MessageCannotPayoutZero", this, &TIGLobbyView::ShowMessageToPlayer, nullptr);
	pApp->NewActionListener<TahMemberLarge<TIGLobbyView>, ACTION_DRAW>("MessageCannotHandpayRestricted", this, &TIGLobbyView::ShowMessageToPlayer, nullptr);

	pApp->NewActionListener<TahMember<TIGLobbyView>>("OnBoardCommunicationReset", this, &TIGLobbyView::OnBoardCommunicationReset);
	pApp->NewActionListener<TahMember<TIGLobbyView>, ACTION_DRAW>("OnDemoMode", this, &TIGLobbyView::OnDemoMode);

	OnDemoMode();

	TLOG(LogLoading, Info, "Loading sound effects...");
	pGuiApp->GUI()->drawFrame();

	pGuiApp->LoadSoundEffect("button_press.wav");
	pGuiApp->LoadSoundEffect("button_press_soft.wav");
	ThumpSound = pGuiApp->LoadSoundEffect("thump.wav");
	PixieDustSound = pGuiApp->LoadSoundEffect("pixie_dust.wav");
	ShutdownSound = pGuiApp->LoadSoundEffect("shutdown.wav");
	EpicPresentSound = pGuiApp->LoadSoundEffect("epic_present.wav");

	std::shared_ptr<TAppParam> audioCheck = pApp->GetOrSetParam("PLAYBACK_CHECK_TIME", "10", "Audio check time [min]", "Audio check time in minutes ( 0 = OFF )",
	                                                            PARAM_READ_MINE_OR_GROUP_OR_GLOBAL_WRITE_GLOBAL);
	audioCheck->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			int audioCheckTime = param->AsInteger();

			if (auto task = pAudioTask.lock())
				task->Remove();
			pAudioTask.reset();


			if (audioCheckTime > 0)
			{
				audioCheckTime = audioCheckTime * 60000;
				pAudioTask = pApp->AddTimedTask<TtthMember<TIGLobbyView, void>>(this, &TIGLobbyView::OnAudioCheck, audioCheckTime, TTimedTaskHandler::ENDLESS,
				                                                                "TIGLobbyView::OnAudioCheck, 10min");
			}
		}
	};
	audioCheck->OnParamChange(audioCheck.get(), { ParameterProperty::OLD_VALUE }, ParameterChangeSource::Application);

	bLastPayoutEnabled = false;

	pCommander->OnGameLeaveState[yserver::EGameClientState::Init] +=
	  [this](GameRunContext& ctx, const yserver::GameClientState& oldState, const yserver::GameClientState& newState) {
		  for (const auto& queuedSound : SoundTasks)
			  if (auto task = queuedSound.lock())
				  task->Remove();
		  SoundTasks.clear();

		  if (ctx.RunningGame->LoopSound)
			  pGuiApp->StopSound(ctx.RunningGame->LoopSound, 1000);
	  };

	dynamic_cast<TPlatformClient*>(MenuGUI()->pClient)->OnStartService += std::bind(&TIGLobbyView::OnStartService, this);
	dynamic_cast<TPlatformClient*>(MenuGUI()->pClient)->OnStopService += std::bind(&TIGLobbyView::OnStopService, this);
}

TIGLobbyView::~TIGLobbyView()
{
	Packman->ForAllGameConfigurations([](const std::shared_ptr<igp::PlatformGamePackage>& game, const std::shared_ptr<igp::PlatformGameConfiguration>& cfg) -> bool {
		cfg->DestroyGraphics();
		return true;
	});
	clear();

	pApp->UnregisterAllActionHandlersByRelatedObject(this);
}

void TIGLobbyView::UpdateGameTranslations(const igp::PlatformGameConfiguration& gameConf, ELanguage lang)
{
	if (lang == ELanguage::Current)
		lang = pApp->Language();

	// const bool bLangVisible = lang == ELanguage::English || gameConf.StaticGame().SupportedLanguages.contains(lang);
	// if (bLangVisible)
	// 	gameConf.GameButton->pLanguageIcon->fadeIn();
	// else
	// 	gameConf.GameButton->pLanguageIcon->fadeOut();
}

void TIGLobbyView::SetQRCode(const std::string& address, const web::QueryString& query)
{
	pQRCode->setData(address + "?" + query.ToString());
	mPendingQRSession = query.Get("session");
}

void TIGLobbyView::ShowQRCode()
{
	pQRPanelBackground->fadeIn();
	HidePlayAway();

	pQRHideTask = pApp->Delay(
	  [this]() {
		  mPendingQRSession.clear();
		  pQRPanelBackground->fadeOut();
	  },
	  20000, "QRPanel hide", TASK_DRAW);
}

void TIGLobbyView::HideQRCode(const std::string& pendingSession)
{
	pApp->Defer(
	  [this, pendingSession]() {
		  if (pendingSession.empty() || pendingSession == mPendingQRSession)
		  {
			  if (auto task = pQRHideTask.lock())
				  task->Remove();
			  pQRHideTask.reset();
			  pQRPanelBackground->fadeOut();
			  mPendingQRSession.clear();
		  }
	  },
	  __FUNCTION__);
}

void TIGLobbyView::setAmbient(const std::string& audio)
{
	// not playing ambient music
	return;

	if (!audio.empty() && pAmbientMusic && pAmbientMusic->FileName.filename() == audio)
		return;

	if (pAmbientMusic)
	{
		pGuiApp->StopSound(pAmbientMusic);
		pAmbientMusic.reset();
	}

	if (!audio.empty())
	{
		pAmbientMusic = pGuiApp->LoadSoundEffect(audio, -1, false);
		// pGuiApp->SetSoundVolume(pAmbientMusic, pSliderMusicVolume->GetSelected().IntValue * 1e-2f * mVolumeAnimation.from());
		pGuiApp->Play(pAmbientMusic);
		mLastVolumeChange = SDL_GetTicks64();
	}
}

bool TIGLobbyView::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	if (pJackpotDialogBG->isVisible())
	{
		pJackpotDialogBG->fadeOut();
		return true;
	}

	if (MenuGUI()->pClient->Locks->IsLocked())
		return false;

	if (ev.bPressed && ev.Action != EHardwareButtonAction::Info)
		HideGameInfo();

	switch (ev.Action)
	{
		case EHardwareButtonAction::Payout:
			if (ev.bPressed && pTopInfoBar->QRPayoutIsEnabled())
				OnPayout();
			return true;
		case EHardwareButtonAction::Service:
			if (ev.bPressed)
				dynamic_cast<TPlatformClient*>(MenuGUI()->pClient)->StartService();
			return true;
		default: break;
	}

	return false;
}

void TIGLobbyView::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	// if (dynamic_cast<TIGPlatformApp*>(pApp)->IsGameRunning())
	if (false)
	{
		outAvailableActions.SetAvailable(EHardwareButtonAction::GameSelect, RETURN_TO_MENU_STRING);
		outAvailableActions.SetAvailable(EHardwareButtonAction::Payout, PAYOUT_BUTTON_STRING);
	}
	else
	{
		if (pJackpotDialogBG->isVisible())
		{
			outAvailableActions.SetAvailable(EHardwareButtonAction::Autoplay, RETURN_TO_MENU_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::GameSelect, RETURN_TO_MENU_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::Payout, PAYOUT_BUTTON_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::StartGame, START_GAME_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::Info, SHOW_GAME_INFO_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::BetMinus, PREV_PAGE_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::BetPlus, NEXT_PAGE_STRING);
			outAvailableActions.SetAvailable(EHardwareButtonAction::BetMax, NEXT_PAGE_STRING);
		}
		else if (!MenuGUI()->pClient->Locks->IsLocked())
		{
			if (pTopInfoBar->PayoutIsEnabled())
				outAvailableActions.SetAvailable(EHardwareButtonAction::Payout, PAYOUT_BUTTON_STRING);
		}
	}

	outAvailableActions.SetAvailable(EHardwareButtonAction::Service, CALL_ATTENDANT_STRING);
}

void TIGLobbyView::onLanguageChanged(ELanguage lang)
{
	if (pTopInfoBar)
		pTopInfoBar->onLanguageChanged(lang);
	if (pLobby)
		pLobby->onLanguageChanged(lang);
}

void TIGLobbyView::HidePlayAway()
{
	if (pPlayAwayDialog->isVisible())
	{
		pPlayAwayDialog->setY(getHeight(), 0.5s);
		pPlayAwayDialog->fadeOut();
	}
}

void TIGLobbyView::HideGameInfo()
{
	if (!pGameInfoPanel)
		return;

	if (pGameInfoPanel->isVisible())
	{
		pGameInfoPanel->setY(getHeight() + pGameInfoPanel->getHeight() / 2, 0.5s);
		pGameInfoPanel->fadeOut();
	}
	pGameInfoPanelBG->fadeOut();
}

void TIGLobbyView::ShowGameInfo(GameIdentifier id)
{
	std::shared_ptr<igp::PlatformGamePackage> GamePack;
	std::shared_ptr<igp::PlatformGameConfiguration> GameConf = Packman->GetGame(id, GamePack);

	if (!GamePack || !GameConf)
		return;

	if (!pGameInfoPanel)
		return;

	pGameInfoPanel->setY(getHeight() / 2, 0.5s);
	pGameInfoPanel->fadeIn();
	pGameInfoPanel->moveToTop();

	pGameInfoPanelBG->fadeIn();

	pGameInfoPanel->SetDisplayedGame(*GamePack, *GameConf);
}

void TIGLobbyView::OnStartGame(const std::shared_ptr<igp::PlatformGameConfiguration>& game)
{
	// play game start sounds
	PlayGameStartSound(game->ID);
}

void TIGLobbyView::PlayGameStartSound(const GameIdentifier& game)
{
	std::shared_ptr<TSoundEffect> announceSound, loopSound;
	FSoundInfo AnnounceInfo, LoopInfo;

	std::shared_ptr<igp::PlatformGamePackage> gamePack;
	auto conf = Packman->GetGame(game, gamePack);
	if (conf && gamePack && gamePack->Package.CanBeExecuted())
	{
		announceSound = conf->AnnounceSound;
		loopSound = conf->LoopSound;
		AnnounceInfo = conf->IntegrationData().AnnounceSound;
		LoopInfo = conf->IntegrationData().LoopSound;
	}

	if (announceSound)    // we have the announce sound, play that!
	{
		LoopInfo.Delay += AnnounceInfo.Delay;    // delay the loop sound by the announce sound delay
		if (AnnounceInfo.Delay > 0.f)
			SoundTasks.insert(
			  pGuiApp->Delay(std::bind(&TGuiApplication::Play, pGuiApp, announceSound, 0, int(AnnounceInfo.FadeIn * 1000)), AnnounceInfo.Delay * 1000, "announceGame"));
		else
			pGuiApp->Play(announceSound);
	}

	if (loopSound)
	{
		if (LoopInfo.Delay > 0.f)
			SoundTasks.insert(
			  pGuiApp->Delay(std::bind(&TGuiApplication::Play, pGuiApp, loopSound, -1, int(LoopInfo.FadeIn * 1000)), LoopInfo.Delay * 1000, "loadingLoop"));
		else
			pGuiApp->Play(loopSound);
	}

	if (!announceSound && !loopSound)    // no special sound configured for this game, fall back to default
	{
		// predvajamo zvok, samo takrat ko je uporabnik izbral .. v primeru crash-a ali replay-a ga ne predvajamo
		pGuiApp->Play(ThumpSound);
		SoundTasks.insert(pGuiApp->Delay(std::bind(&TGuiApplication::Play, pGuiApp, PixieDustSound, 0, 0), 1000, "launchSoundSpice"));
	}
}

bool TIGLobbyView::SortGames(const FGameEntry& a, const FGameEntry& b) const
{
	switch (mOrderMode)
	{
		case EGameOrderMode::Volatility:
			if (!a.Configuration->LiveGame())
				return false;
			if (!b.Configuration->LiveGame())
				return true;
			if (a.Configuration->LiveGame()->Volatility != b.Configuration->LiveGame()->Volatility)
				return a.Configuration->LiveGame()->Volatility > b.Configuration->LiveGame()->Volatility;
			__attribute__((fallthrough));
		case EGameOrderMode::Newest:
			if (a.Configuration->StaticGame().ReleaseTimestampSeconds != b.Configuration->StaticGame().ReleaseTimestampSeconds)
				return a.Configuration->StaticGame().ReleaseTimestampSeconds > b.Configuration->StaticGame().ReleaseTimestampSeconds;
			__attribute__((fallthrough));
		case EGameOrderMode::Default:
			if (!a.Configuration->LiveGame())
				return false;
			if (!b.Configuration->LiveGame())
				return true;
			if (a.Configuration->LiveGame()->Order != b.Configuration->LiveGame()->Order)
				return a.Configuration->LiveGame()->Order < b.Configuration->LiveGame()->Order;
			__attribute__((fallthrough));
		case EGameOrderMode::MostPlayed:
			if (a.Configuration->PopularityRank && b.Configuration->PopularityRank)    // both games have ranks
			{
				if (a.Configuration->PopularityRank != b.Configuration->PopularityRank)    // their ranks differ
					return a.Configuration->PopularityRank < b.Configuration->PopularityRank;    // A is ranked ahead of B
			}
			else if (a.Configuration->PopularityRank)    // only A has a rank, it should come first
				return true;
			else if (b.Configuration->PopularityRank)    // only B has a rank, A should not come first
				return false;
			__attribute__((fallthrough));
		case EGameOrderMode::Alphabetic:
			if (a.Package->Package.Game.Name != b.Package->Package.Game.Name)
				return a.Package->Package.Game.Name < b.Package->Package.Game.Name;
			__attribute__((fallthrough));
		default: break;
	}

	return a.Configuration->ID < b.Configuration->ID;    // if all else fails
}

void TIGLobbyView::RefreshGameData(bool bMoveToFirstPage)
{
	if (bGameRefreshBlocked)
		return;

	const igp::FPlatformEnvironment env(dynamic_cast<TIGPlatformApp*>(pApp)->GetPlatformEnvironment(true));
	Packman->ForAllGameConfigurations(
	  [this, &env](const std::shared_ptr<igp::PlatformGamePackage>& game, const std::shared_ptr<igp::PlatformGameConfiguration>& conf) -> bool {
		  // always first update the game icon before adding it to the lobby!!!
		  conf->UpdateButtonIcon(Resources, game->Package.Game.Type);

		  pLobby->AddGameThumbnail(game, *conf, env);

		  conf->OnGameConfigurationUpdated(*conf);
		  return true;
	  });

	pLobby->RecalculateContentPositions();
}

void TIGLobbyView::OnOrderSelectionChanged(EGameOrderMode mode, bool bSelected)
{
	if (!bSelected)    // ignore deselected events
		return;

	mOrderMode = mode;

	if (bGameRefreshBlocked)
		return;

	pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::RefreshGameData, this, true), "TIGLobbyView::OnOrderSelectionChanged", false, this);
}

void TIGLobbyView::OnVirtualDenomChanged(int virtCoeff)
{
	LocalizedMessage sCurrCre = "1 " + LocalizedMessage(CREDIT_STRING) + (" = " + MenuGUI()->pClient->CreditsAsCurrencyString(virtCoeff));

	if (MenuGUI()->CurrencySymbol != "   " &&
	    !MenuGUI()->CurrencySymbol.empty())    // to sem naredil za AIK bosno, ko skrivajo oznako valute.., ce oznake ni, niti ne prikazemo sporocila
	{
		int64_t leftover_credits = MenuGUI()->pClient->BalanceManager()->GetBettableCredit().Total() % MenuGUI()->pClient->GetVirtualCreditCoefficient();
		if (leftover_credits)
			AddNotification({ sCurrCre, LocalizedMessage(INDIVISIBLE_DENOMI_WARNING_STRING),
			                  LocalizedMessage(INDIVISIBLE_REMAINDER_STRING) + (": " + MenuGUI()->pClient->CreditsAsCurrencyString(leftover_credits)) },
			                4, EVerbosity::Warning);
		else
			AddNotification({ sCurrCre }, 2);
	}

	const credit::CreditArray balance = MenuGUI()->pClient->BalanceManager()->GetBalance();
	pTopInfoBar->RefreshCreditMeter(balance, balance);
}

void TIGLobbyView::SetJackpotWon(uint64_t wonJackpotCredits)
{
	mJackpotAmount += wonJackpotCredits;

	TryShowJackpot();
}

void TIGLobbyView::TryShowJackpot()
{
	if (mJackpotAmount)
	{
		pApp->Delay(
		  [this, toShowAmount = mJackpotAmount]() {
			  pJackpotFireworksAnimation->ResetAnimationSkipResizeFactor();

			  pJackpotAmount->SetText(MenuGUI()->FormatCreditsAsCurrency(toShowAmount),
			                          MenuGUI()->FormatCreditsAsCurrency(toShowAmount, ECurrencyDisplayMode::WithCurrencyCode));

			  pJackpotDialogBG->fadeIn();
			  pGuiApp->Play(EpicPresentSound);
		  },
		  500, "jackpot-dialog-show", ETimedTaskType::TASK_DRAW);

		mJackpotAmount = 0;
	}
}

void TIGLobbyView::draw(Graphics* graphics)
{
	// Izris glavnih komponent
	TPanelBase::draw(graphics);
}

void TIGLobbyView::drawLogic(Graphics* graphics, float deltaTime)
{
	Uint64 now = SDL_GetTicks64();
	long CurrentCredit = MenuGUI()->pClient->BalanceManager()->GetBettableCredit().Total();

	if (pQRPanelBackground->isVisible())
	{
		if (auto task = pQRHideTask.lock())
			pQRCountdownLabel->setCaption(std::to_string((int)std::ceil(task->GetTimeRemainingToExecute() / 1000.f)));
	}

	if (pAmbientMusic && pAmbientMusic->IsPlaying())
	{
		if (MyUtils::IsTime(mLastVolumeChange, 10000))
		{
			mLastVolumeChange = now;
		}
	}

	const bool bHasCredits = (CurrentCredit > 0);
	// const bool bShouldHaveLockButton = !bLockButtonDisabled && !MenuGUI()->pClient->IsInDemoMode() && (bHasCredits || (MenuGUI()->pClient->GetBetCredit() > 0));
	// if (bShouldHaveLockButton != pBtnLockPlayboard->isEnabled())
	// {
	// 	pBtnLockPlayboard->setEnabled(bShouldHaveLockButton);
	// 	if (bShouldHaveLockButton)
	// 		pBtnLockPlayboard->fadeIn();
	// 	else
	// 		pBtnLockPlayboard->fadeOut();
	// }

	const bool bCashoutInProgress = pPnlPlayerPayout->getEffectiveVisiblity();

	// CREDITS, BET, WIN LABELS
	pTopInfoBar->QRPayoutSetVisible(dynamic_cast<TIGPlatformApp*>(pApp)->IsLinkedToServerHub() &&
	                                (dynamic_cast<TIGPlatformApp*>(pApp)->PlayAwayEnabled && dynamic_cast<TIGPlatformApp*>(pApp)->PlayAwayEnabled->AsBoolean()) &&
	                                MenuGUI()->pMoneyAcceptor && MenuGUI()->pMoneyAcceptor->IsTicketPrinterReady() &&
	                                (!dynamic_cast<TIGPlatformApp*>(pApp)->bPlayAwayRequireBill || MenuGUI()->pMoneyAcceptor->IsBillAcceptorCommunicationOK()));
	pTopInfoBar->QRPayoutSetEnabled(!bCashoutInProgress && bHasCredits);

	// HARDWARE KEY - LIGHTS AND INSTRUCTIONS
	const bool isAttendantMenuVisible = IsAttendantMenuOpened();
	// const bool isVirtualCreditVisible = pVirtualCreditSelectPanel->getEffectiveVisiblity();
	const bool isVirtualCreditVisible = false;

	const bool isPayoutVisible = pPnlPlayerPayout->getEffectiveVisiblity();
	const bool isPayinVisible = isAttendantMenuVisible ? pPanelMenu->pPnlCredit->getEffectiveVisiblity() : false;

	const bool isAttendantPayoutVisible = isAttendantMenuVisible ? pPanelMenu->pPnlPayout->getEffectiveVisiblity() : false;
	const bool isPlayAwayDialogVisible = pPlayAwayDialog->getEffectiveVisiblity();

	const bool isLocked = MenuGUI()->pClient->Locks->IsLocked();
	bool isPayoutEnabled = bAllowZeroCashout || (BALANCE->GetBalance().Total() > 0 && BALANCE->IsPayoutPossible());
	if (1 == PayoutButtonDisabled)    // "PAYOUT_BUTTON_DISABLED" parameter has priority over "ALLOW_ZERO_CASHOUT" parameter
		isPayoutEnabled = false;

	if (BALANCE->GetBalance().IsZero() && bUserHiddenCreditValue)
	{
		bUserHiddenCreditValue = false;

		pTopInfoBar->RefreshCreditMeter(credit::CreditArray(), MenuGUI()->pClient->BalanceManager()->GetBalance());

		pTopInfoBar->SetCreditValueCurrencyContentVisible(true);
	}
	pTopInfoBar->PayoutSetEnabled(!isLocked && isPayoutEnabled && !isPayoutVisible && !isVirtualCreditVisible && !isPayinVisible && !isAttendantPayoutVisible &&
	                              !isPlayAwayDialogVisible);

	TPanelBase::drawLogic(graphics, deltaTime);
}

bool TIGLobbyView::IsAttendantMenuOpened()
{
	return pPanelMenu && pPanelMenu->IsAttendantMenuOpened();
}

bool TIGLobbyView::WasPayoutPressed()
{
	return pPnlPlayerPayout->getEffectiveVisiblity() || pPnlPlayerPayout->pPayoutEnd->getEffectiveVisiblity();
}

void TIGLobbyView::ReturnToDisplayingMenu()
{
	TMenuGUI* MenuGUI = (TMenuGUI*)(pGuiApp->GUI());
	dynamic_cast<TScreenshotViewer*>(dynamic_cast<TOtherStationsPanel*>(MenuGUI->PanelMenu->pOtherStationsPanel)->mpPnlLastGames)->OnCameBackFromGamePlay();

	pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::OnLoadCancelled, this), "TIGLobbyView::OnLoadCancelled", false, this);

	pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::RefreshGameData, this, true), "TIGLobbyView::ReturnToDisplayingMenu", false, this);
	if (pAmbientMusic)
	{
		pGuiApp->SetSoundVolume(pAmbientMusic, 0.f);
		pGuiApp->Play(pAmbientMusic);
		mLastVolumeChange = 0;
	}
}

void TIGLobbyView::OnLoadCancelled()
{
	rgbanimation::TRGBEngine& engine = dynamic_cast<TStandaloneApplication*>(pApp)->RGBEngine;
	engine.SetIlluminationPreset(engine.DefaultIllumination);

	pGuiApp->StopSound(ThumpSound);
	pGuiApp->StopSound(PixieDustSound);

	for (const auto& queuedSound : SoundTasks)
		if (auto task = queuedSound.lock())
			task->Remove();
	SoundTasks.clear();

	TryShowJackpot();
}

void TIGLobbyView::SelectBrightness(int mode)
{
	if (mScreenColorMode == mode || mode >= 3)
		return;

	mScreenColorMode = mode;

	// pDisplayMode->setNormalImage(pBrightnessIcons[2 * mScreenColorMode]);
	// pDisplayMode->setDownImage(pBrightnessIcons[2 * mScreenColorMode + 1]);
	// pDisplayMode->setHighImage(pBrightnessIcons[2 * mScreenColorMode + 1]);

	std::string command = "redshift -l manual -x -m randr > /dev/null 1>&2;";
	int tmpIlluminationMax = pApp->GetParam("IGPV6_ILLUMINATION_MAX_PWM")->AsInteger();
	int tmpSemaphoreMax = pApp->GetParam("IGPV6_SEMAPHORE_MAX_PWM")->AsInteger();
	int tmpButtonMax = pApp->GetParam("IGPV6_BUTTON_LIGHTS_MAX_PWM")->AsInteger();
	switch (mScreenColorMode)
	{
		case 1: {
			command = "redshift -l manual -O 6500 -b 0.6 -m randr > /dev/null 1>&2;";
			tmpIlluminationMax = tmpIlluminationMax * 0.6;
			// tmpSemaphoreMax = tmpSemaphoreMax * 0.6;
			tmpButtonMax = tmpButtonMax * 0.6;
			break;
		}
		case 2: {
			command = "redshift -l manual -O 3700 -b 0.8 -m randr > /dev/null 1>&2;";
			tmpIlluminationMax = tmpIlluminationMax * 0.45;
			// tmpSemaphoreMax = tmpSemaphoreMax * 0.45;
			tmpButtonMax = tmpButtonMax * 0.45;
			break;
		}
	}

	MyUtils::SystemAsync(command);

	MenuGUI()->pMoneyAcceptor->SetPWMMax(tmpIlluminationMax, tmpSemaphoreMax, tmpButtonMax);
}

void TIGLobbyView::ResetUserChoices()
{
	SelectBrightness(0);

	bGameRefreshBlocked = true;
	// the other because one already returned true
	bool bDenomChanged = false;
	// TODO this crashes currently, check for SetValidDenomi(0);
	// if (pVirtualCreditSelectPanel)
	// bDenomChanged = pVirtualCreditSelectPanel->SetValidDenomi(0);
	bGameRefreshBlocked = false;
	if (bDenomChanged)
		pGuiApp->DeferToDrawSimple(std::bind(&TIGLobbyView::RefreshGameData, this, false), "TIGLobbyView::ResetUserChoices", false, this);
}

void TIGLobbyView::OnClientLock()
{
	if (MenuGUI()->pClient->Locks->IsLockedWithoutIDs({ MenuGUI()->pClient->CLIENT_UNFINISHED_GAME_LOCK }))
		setEnabled(false);    // tukaj se izkjlucijo vsi gumbi

	if (MenuGUI()->pClient->Locks->IsLockedWithoutIDs(
	      { dynamic_cast<TPlatformClient*>(MenuGUI()->pClient)->PLAYAWAY_LOCK, MenuGUI()->pClient->CLIENT_TICKET_PRINTER_ERROR_LOCK }))
		HidePlayAway();

	HideGameInfo();

	// StopIdleAnimation();

	// Če smo v SAS transakciji ne skrivamo kreditov, da je vidno kaj se dogaja
	std::shared_ptr<LockID> sasLock;
	TSASModule* pSAS = pApp->GetModuleByName<TSASModule>("SAS1");
	if (pSAS && pSAS->IsEnabled())
		sasLock = pSAS->SAS_AFT_LOCK;
}

void TIGLobbyView::OnClientUnlock()
{
	setEnabled(true);    // tukaj se vkljucijo gumbi

	if (pTopInfoBar)
	{
		pTopInfoBar->SetCreditValueCurrencyContentVisible(true);
	}

	// StartIdleAnimation();
}

void TIGLobbyView::OnAudioCheck()
{
	// int AudioVolumeLevel = pSliderVolume->getIndex();
	// nastavimo na izbrano glasnost

	// pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "########## OnAudioCheck on %d ##########", AudioVolumeLevel);

	if (isVisible() && !IsAttendantMenuOpened() && !pLobby->IsGameRunning())
	{
		// std::string cmd = "./set_audio_volume_igp.sh "+std::to_string(AudioVolumeLevel)+" &";
		// system("pulseaudio --system");
		pGuiApp->PlaySound("intro.wav");    // ko ni nobene prijavljene igrice in ni odprt attendant menu, zaigramo intro.wav
	}
}

void TIGLobbyView::OnStartService()
{
	if (pTopInfoBar)
		pTopInfoBar->InService(true);
}
void TIGLobbyView::OnStopService()
{
	if (pTopInfoBar)
		pTopInfoBar->InService(false);
}

void TIGLobbyView::OnReturnFromGame()
{
	if (!mJackpotAmount)
		pGuiApp->Play(ShutdownSound);
}

void TIGLobbyView::OnAllGamesClosed()
{
	if (pTopInfoBar)
		pTopInfoBar->SetSelectedActiveGame(-1);

	auto& rgbEngine = (dynamic_cast<TIGPlatformApp*>(pApp))->RGBEngine;
	rgbEngine.SetIlluminationPreset(rgbEngine.DefaultIllumination);
}

void TIGLobbyView::OnGameFocusChanged(const size_t gameSlotID)
{
	pGuiApp->DeferToDrawSimple(
	  [this, gameSlotID]() {
		  if (pTopInfoBar)
			  pTopInfoBar->SetSelectedActiveGame(gameSlotID);

		  if (pLobby->pGameMenu)
			  pLobby->pGameMenu->SetSelectedActiveGame(gameSlotID);

		  if (pLobby)
			  pLobby->SetSelectedActiveGame(gameSlotID);
	  },
	  "TIGLobbyView::OnGameFocusChanged", false);
}

void TIGLobbyView::AddGame(BrowserWindow* w, const size_t gameSlotID, const bool focusNewGame) const
{
	pLobby->AddGame(w, gameSlotID, focusNewGame);
	pLobby->pGameMenu->LoadGameSettings(gameSlotID);
	pLobby->pGameMenu->LoadGameHistory(gameSlotID);
}

void TIGLobbyView::OnDemoMode()    // called in draw thread because it is a draw action
{
	pDemoOverlay->setVisible(MenuGUI()->pClient->IsInDemoMode());
	RefreshGameData(true);
}

void TIGLobbyView::OnPayout()
{
	if (TLocks::IsLockedInAnyOfIDs({ MenuGUI()->pClient->CLIENT_HANDPAY_LOCK, MenuGUI()->pClient->CLIENT_UNFINISHED_GAME_LOCK }))
		return;

	if (pPlayAwayDialog->getEffectiveVisiblity())
		return;

	if (pPnlPlayerPayout->getEffectiveVisiblity())
		return;

	if (1 == PayoutButtonDisabled)
		return;

	if (2 == PayoutButtonDisabled || (bAllowZeroCashout && !BALANCE->GetBalance().Total()))    // 2-without player confirmation
	{
		BALANCE->StartUnattendedPayOUT();    // sprozi postopek izplacila, najprej preko online sistema, nato še ostale metode, če online ne uspe
	}
	else
	{
		pApp->GenerateAction("CashOutButtonPressed");    // je izklopljeno tukaj, signal gre preko PayoutPanel-a
		pPnlPlayerPayout->bDirect = false;
		pPnlPlayerPayout->Show();
	}

	pGuiApp->PlaySound("take_win.wav");
}

void TIGLobbyView::OnCallAttendantPressed()
{
	// also probably call stop service somewhere
	if (MenuGUI()->pClient && dynamic_cast<TPlatformClient*>(MenuGUI()->pClient))
		dynamic_cast<TPlatformClient*>(MenuGUI()->pClient)->StartService(false);

	pCommander->DisplayMessage(LocalizedMessage(CALLING_ATTENDANT_STRING), 5000);
}

void TIGLobbyView::OnBoardCommunicationReset()
{
	pApp->WriteLog(LT_WARNING, 0, "!!!!!!!!!!! BOARD COMMUNICATION RESET !!!!!!!!!!!");
}

void TIGLobbyView::OnGameViewVolumeSet(const size_t gameSlotID, float volume)
{
	pCommander->GetGameState(gameSlotID, [volume](GameRunContext& ctx) {
		ctx.Volume = volume;
		if (ctx.GameWindow)
			ctx.GameWindow->SetVolume(ctx.Volume * ctx.RunningGame->IntegrationData().AudioGain);
	});
}

void TIGLobbyView::ShowMessageToPlayer(std::string ActionName, void* hActionData, void* hUserData)
{
	if ("ServiceMessage" == ActionName)
	{
		AddNotification({ LocalizedMessage(CALLING_ATTENDANT_STRING) }, 3);
	}
	else if ("MessageCannotHandpayRestricted" == ActionName)
	{
		AddNotification({ LocalizedMessage(CANNOT_PAYOUT_ZERO_CREDITS_STRING), LocalizedMessage(MSG_CANNOT_PAYOUT_RESTRICTED) }, 3, EVerbosity::Warning);
	}
	else if ("MessageCannotPayoutZero" == ActionName)
	{
		AddNotification({ LocalizedMessage(CANNOT_PAYOUT_ZERO_CREDITS_STRING) }, 3, EVerbosity::Warning);
	}
}
