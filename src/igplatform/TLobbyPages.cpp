#include "TLobbyPages.h"

#include "MyUtils.h"
#include "TotallyTemporaryDesignTokens.h"
#include "gui/widgets/label.hpp"
constexpr size_t X_PADDING = 24;
constexpr size_t Y_PADDING = 16;

TLobbyIconPositioner::TLobbyIconPositioner(Container* pParent) : mGameFilters(EGameFilters::AllGames), mMenuCategory(ELobbyGameCategory::AllGames)
{
	setId("lobby-icon-positioner");
	if (pParent)
		pParent->add(this);


	bResizeToFitLayout = true;
	setSize(500, 528);
}

size_t TLobbyIconPositioner::getIconPositions(const Vector2D& inParentSize, std::vector<TGameButton*> buttons, std::vector<Vector2D>& positions)
{
	if (buttons.empty())
		return 0;

	auto parentSize = inParentSize;
	int iconsPerRow = std::min(3.f, (parentSize.X() + X_PADDING) / (buttons[0]->getWidth() + X_PADDING));
	int maxIconsPerPage = iconsPerRow * 2;

	if (maxIconsPerPage == 0)
		return 0;

	int totalPages = (buttons.size() + maxIconsPerPage - 1) / maxIconsPerPage;

	size_t iconIndex = 0;
	for (int page = 0; page < totalPages; ++page)
	{
		int numIconsOnPage = std::min<int>(maxIconsPerPage, buttons.size() - page * maxIconsPerPage);
		int iconsInFirstRow = numIconsOnPage <= iconsPerRow ? numIconsOnPage : (numIconsOnPage + 1) / 2;
		for (int i = 0; i < numIconsOnPage; ++i, ++iconIndex)
		{
			int row = i / iconsInFirstRow;
			int col = i % iconsInFirstRow;

			int iconsInCurrentRow = row ? (numIconsOnPage - iconsInFirstRow) : iconsInFirstRow;
			float rowWidth = iconsInCurrentRow * (buttons[0]->getWidth() + X_PADDING) - X_PADDING;
			float xOffset = (parentSize.X() - rowWidth) / 2 + page * inParentSize.X();

			Vector2D position;
			position.X() = col * (buttons[0]->getWidth() + X_PADDING) + xOffset;

			// Center vertically if only one row
			if (iconsInFirstRow == numIconsOnPage)
			{
				position.Y() = (parentSize.Y() - buttons[0]->getHeight()) / 2;
			}
			else
			{
				position.Y() = (parentSize.Y() / 2 - buttons[0]->getHeight() - Y_PADDING / 2) + row * (buttons[0]->getHeight() + Y_PADDING);
			}

			positions.push_back(position);
		}
	}

	return totalPages;
}

std::vector<TGameButton*> TLobbyIconPositioner::GetFilteredVectorOfButtons()
{
	std::vector<TGameButton*> gameTypeButtons;
	if (mMenuCategory == ELobbyGameCategory::AllGames || mMenuCategory == ELobbyGameCategory::ActiveGames)
	{
		// this orders ALL games in a SPECIFIC order
		gameTypeButtons = GetPredeterminedOrderOfButtons();
	}
	else
	{
		// handle categories
		for (auto& button : mButtons)
		{
			if (button->isVisible())
				button->setVisible(false);

			if (button->ShouldGameBeDisplayed())
			{
				if (mMenuCategory == ELobbyGameCategory::RouletteGames && (button->HostType == GameType::Roulette || button->HostType == GameType::VirtualRoulette))
					gameTypeButtons.push_back(button);
				else if (mMenuCategory == ELobbyGameCategory::CardGames &&
				         (button->HostType == GameType::Baccarat || button->HostType == GameType::VirtualBaccarat ||
				          (button->HostType <= GameType::DealersGame && button->HostType >= GameType::VirtualDragonTiger)))
					gameTypeButtons.push_back(button);
				else if (mMenuCategory == ELobbyGameCategory::iGames &&
				         (button->HostType == GameType::Evolution ||
				          button->HostType == GameType::GameartSlotGame))    // Slots are here just for show, should be removed probably
					gameTypeButtons.push_back(button);
			}
		}
	}


	std::vector<TGameButton*> filteredButtons;
	if (mGameFilters == EGameFilters::AllGames)
	{
		filteredButtons = gameTypeButtons;
	}
	else
	{
		// filter by RNG type
		EGameRNGType currentFilter = EGameRNGType::Automated;
		switch (mGameFilters)
		{
			case EGameFilters::Live: {
				currentFilter = EGameRNGType::Live;
				break;
			}
			case EGameFilters::Automated: {
				currentFilter = EGameRNGType::Automated;
				break;
			}
			case EGameFilters::Virtual: {
				currentFilter = EGameRNGType::Virtual;
				break;
			}

			default: currentFilter = EGameRNGType::Automated;
		}

		// push correct filters to final vector
		for (auto& button : gameTypeButtons)
		{
			if (button->RNGType == currentFilter)
				filteredButtons.push_back(button);
		}
	}

	// puts unavailable games at the end
	std::ranges::partition(filteredButtons, [](TGameButton* button) { return !button->IsGameUnavailable(); });

	// display ALL games that the filters deem usable
	for (auto& button : filteredButtons) { button->setVisible(true); }

	return filteredButtons;
}

std::vector<TGameButton*> TLobbyIconPositioner::GetPredeterminedOrderOfButtons()
{
	// this helps us sort games in THIS order
	const static std::vector<GameType> Order { GameType::Roulette,           GameType::VirtualRoulette,        GameType::Baccarat,
		                                       GameType::DealersGame,        GameType::VirtualBaccarat,        GameType::VirtualOpenBaccarat,
		                                       GameType::VirtualDragonTiger, GameType::VirtualOpenDragonTiger, GameType::VirtualThreeHeadedDragon,
		                                       GameType::Evolution,          GameType::GameartSlotGame };

	std::vector<TGameButton*> allShownGames;
	for (auto& button : mButtons)
	{
		// hide all icons as we don't know which ones we could show even
		button->setVisible(false);

		if (!button->ShouldGameBeDisplayed())
			continue;

		if (mMenuCategory == ELobbyGameCategory::ActiveGames && button->IsGameUnavailable())    // only return available games
			continue;

		allShownGames.push_back(button);
	}

	std::ranges::sort(allShownGames, [](TGameButton* a, TGameButton* b) -> bool {
		size_t aOrder = std::ranges::find(Order, a->HostType) - Order.begin();
		size_t bOrder = std::ranges::find(Order, b->HostType) - Order.begin();

		if (a->IsGameUnavailable())
			aOrder += Order.size() + 1;

		if (b->IsGameUnavailable())
			bOrder += Order.size() + 1;

		return aOrder < bOrder;
	});

	return allShownGames;
}

void TLobbyIconPositioner::AddCategoryButton(const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf,
                                             const igp::FPlatformEnvironment& env)
{
	if ((!conf.IsConfigurable() || !game->Package.CanBeExecuted()) && !igp::PlatformGameConfiguration::bAlwaysShowGameConfigurations)
	{
		conf.DestroyGraphics();
		return;
	}

	TGameButton* GameButton = findGameButton(conf);
	if (!GameButton)
	{
		GameButton = new TGameButton(this, game, conf, env);
		// TODO swipe lobby?
		// GameButton->addMouseListener(this, true);    // to catch mouse events before they reach the button, in case input is a swipe and not a button press

		GameButton->OnDeath += [this](Widget* w) {
			mButtons.erase(std::find(mButtons.begin(), mButtons.end(), w));
		};

		mButtons.push_back(GameButton);
	}

	RepositionLobbyIcons();
}

void TLobbyIconPositioner::RepositionLobbyIcons()
{
	const Vector2D parentSize = getParent()->getSize();
	std::vector<Vector2D> positions;

	std::vector<TGameButton*> filteredButtons = GetFilteredVectorOfButtons();
	size_t numPages = getIconPositions(parentSize, filteredButtons, positions);

	if (positions.empty())
		return;

	if (filteredButtons.size() != positions.size())
	{
		TLOG(LogGUI, Critical, "getIconPositions returned a different number of positions than buttons! No buono.");
		return;
	}

	float maxX = 0.f;
	for (size_t i = 0; i < positions.size(); ++i)
	{
		filteredButtons[i]->setPosition(positions[i]);
		maxX = std::max(maxX, positions[i].X() + filteredButtons[i]->getWidth());
	}
	setSize(maxX, parentSize.Y());
	OnPagesCalculated(1, numPages);
}

void TLobbyIconPositioner::SetGameFilter(EGameFilters filter)
{
	mGameFilters = filter;
	RepositionLobbyIcons();
}

void TLobbyIconPositioner::SetGameLobbyCategory(const ELobbyGameCategory category)
{
	mMenuCategory = category;
	RepositionLobbyIcons();
}

void TLobbyIconPositioner::GetVisibleGameFilters(std::set<EGameRNGType>& rngType, std::set<ELobbyGameCategory>& gameType) const
{
	gameType.insert(ELobbyGameCategory::AllGames);
	for (auto& button : mButtons)
	{
		if (!button->ShouldGameBeDisplayed())
			continue;

		rngType.insert(button->RNGType);

		if (!button->IsGameUnavailable())
			gameType.insert(ELobbyGameCategory::ActiveGames);

		if (button->HostType == GameType::Roulette || button->HostType == GameType::VirtualRoulette)
		{
			gameType.insert(ELobbyGameCategory::RouletteGames);
		}
		else if (button->HostType == GameType::Baccarat || button->HostType == GameType::VirtualBaccarat ||
		         (button->HostType <= GameType::DealersGame && button->HostType >= GameType::VirtualDragonTiger))
		{
			gameType.insert(ELobbyGameCategory::CardGames);
		}
		else if (button->HostType == GameType::Evolution || button->HostType == GameType::GameartSlotGame)
		{
			gameType.insert(ELobbyGameCategory::iGames);
		}
	}
}

TGameButton* TLobbyIconPositioner::findGameButton(const igp::PlatformGameConfiguration& conf) const
{
	for (auto& Button : mButtons)
	{
		if (Button->ID == conf.ID)
			return Button;
	}
	return NULL;
}


TLobbyPages::TLobbyPages() : mGameFilters(EGameFilters::AllGames)
{
	auto brush = ImageBrush(Image::GetImage("bgTextureSemiDark.png"));
	brush.tiling = true;
	Background = brush;

	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);

	setId("lobby-pages");
	mLayout = setLayout<GridLayout>();
	mLayout->AddRows({ { .Size = 80 }, { .Size = 64 }, { .Size = 56 }, { .Size = 528 }, { .Size = 56 }, { .Size = 64 }, { .Size = 80 } });


	bResizeToFitLayout = { false, false };

	mFilters = new LayoutContainer();
	mFilters->bResizeToFitLayout = { true, false };
	mFilters->setHeight(64);
	mFilters->setId("filters-group");
	filterStack = mFilters->setLayout<StackLayout>();
	filterStack->Padding = { -8, 0 };
	filterStack->Direction = EStackLayoutDirection::HORIZONTAL;
	filterStack->Alignment = EChildAlignment::Center;
	filterStack->bUsePaddingBeforeFirstAndAfterLast = true;
	mFilters->Background = tempDesignTokens::LobbyControlsBackground;
	mFilters->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, { 1.f, EDimensionUnit::rem }, { 1.f, EDimensionUnit::px });
	add(mFilters);
	mLayout->Set(mFilters, GridLayout::GridPosition(sVector2D(0, 1), sVector2D(1, 1), EChildAlignment::Center));


	constexpr std::array filters = { ALL_STRING, GAME_CATEGORY_LIVE, AUTO_STRING, VIRTUAL_STRING };

	for (auto option : EGameFilters::_values())
	{
		TModernButton* radioButton = new TModernButton(mFilters);
		radioButton->Type = EModernButtonType::Radio;
		radioButton->Background = Transparent;
		radioButton->SelectedBrush = tempDesignTokens::LobbyControlsSelectedFilter;
		radioButton->bResizeToFitLayout = bVector2D(true, false);
		radioButton->setHeight(48);
		radioButton->RadioButtonGroup = "game-categories-radio-group";
		radioButton->Text->setCaption(
		  LocalizedMessage(filters[option], [](const std::string& msg) -> std::string { return yutils::Format("%s", MyUtils::ToLowercase(msg, true).c_str()); }));
		radioButton->Text->setPadding(Rectangle(40, 0, 40, 0));
		radioButton->Text->setResizeMode(Label::AUTOSIZE);
		radioButton->Text->mTypography = tempDesignTokens::MainFont_400_16;
		radioButton->Text->TextColor = tempDesignTokens::GoldTextColor;
		radioButton->Text->setHeight(48);
		radioButton->Text->TextShadow = Shadow();
		radioButton->setId(std::string("category-") + option._to_string());
		radioButton->ClickSound = "button_press_soft.wav";
		radioButton->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });

		mFilterButtons.insert({ option, radioButton });

		radioButton->OnSelectedChanged += [radioButton](const bool selected) {
			if (selected)
			{
				radioButton->Text->TextColor = Black;
				radioButton->OutlineStyle =
				  Outline(tempDesignTokens::GoldTextColor, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
			}
			else
			{
				radioButton->Text->TextColor = tempDesignTokens::GoldTextColor;
				radioButton->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
			}
		};

		if (option == EGameFilters::AllGames)
			radioButton->SetSelected(true);

		if (option == EGameFilters::AllGames)
			filterStack->setPaddingFor(radioButton, { 8, 0, -8, 0 });
		if (option == EGameFilters::Virtual)
			filterStack->setPaddingFor(radioButton, { -8, 0, 8, 0 });

		radioButton->OnPressed += [this, option]() {
			UpdateFilters(option);
		};
		radioButton->OnSelectedChanged += [this, option](bool selected) {
			if (selected)
				UpdateFilters(option);
		};
	}

	mPageContent = new Container();
	mPageContent->setHeight(528);
	mLayout->Set(mPageContent, GridLayout::GridPosition(sVector2D(0, 3), sVector2D(1, 1), EChildAlignment::Stretch));
	mPageContent->setId("page-content");
	add(mPageContent);

	mIconPositioner = new TLobbyIconPositioner(mPageContent);

	mIconPositioner->OnPagesCalculated += [this](int min, const int max) {
		if (MaxPages == static_cast<size_t>(max))
			return;

		MaxPages = max;

		lPageCounterMax->setCaption(std::to_string(MaxPages));
		GoToPage(CurrentPageIdx);
	};

	mChangeGameContainer = new LayoutContainer();
	mChangeGameContainer->setId("change-game-container");
	auto box = mChangeGameContainer->setLayout<BoxLayout>();
	mChangeGameContainer->setVisible(false);

	auto changeGameBrush = ImageBrush(Image::GetImage("bgTextureBlack.png"));
	changeGameBrush.tiling = true;
	mChangeGameContainer->Background = changeGameBrush;
	mPageContent->add(mChangeGameContainer);

	Label* lCloseToAdd = new Label(LocalizedMessage(CLOSE_ONE_GAME_TO_ADD_SELECTED_STRING));
	lCloseToAdd->setId("page-counter");
	lCloseToAdd->mTypography.addToken(GlobalDesignTokens, tempDesignTokens::GlobalFont_16);
	lCloseToAdd->setVisible(true);
	lCloseToAdd->setAlignment(align::CENTER_CENTER);
	lCloseToAdd->setSize(50, 24);
	lCloseToAdd->setResizeMode(Label::AUTOSIZE);
	lCloseToAdd->setMaxNumberOfLines(2);
	box->PerWidgetBehaviors[lCloseToAdd].Alignment = EChildAlignment::Center;
	mChangeGameContainer->add(lCloseToAdd);

	mPageContent->OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		mIconPositioner->RepositionLobbyIcons();
		PositionChangeGameContainer();
	};

	mPageCounter = new LayoutContainer();
	mPageCounter->setId("page-counter-layout");
	mPageCounter->setSize(228, 64);
	mPageCounter->Background = tempDesignTokens::LobbyControlsBackground;
	mPageCounter->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, { 1.f, EDimensionUnit::rem }, { 1.f, EDimensionUnit::px });
	add(mPageCounter);
	mLayout->Set(mPageCounter, GridLayout::GridPosition(sVector2D(0, 5), sVector2D(1, 1), EChildAlignment::Center));
	GridLayout* countersStack = mPageCounter->setLayout<GridLayout>();
	countersStack->bUseMarginBeforeFirstAndAfterLast = true;
	countersStack->AddColumns({ { .Size = 8 }, { .Size = 48 }, { .Size = 42 }, { .Size = 32 }, { .Size = 42 }, { .Size = 48 }, { .Size = 8 } });

	arrowLeft = new Icon("arrowLeft.png");
	arrowLeft->setId("arrow-left");
	arrowLeft->setSize(48, 48);
	mPageCounter->add(arrowLeft);
	arrowLeft->OnClicked += [this](Widget* w, const Vector2D& pos) {
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		if (CurrentPageIdx)
			GoToPage(CurrentPageIdx - 1);
	};
	countersStack->Set(arrowLeft, GridLayout::GridPosition(sVector2D(1, 0), sVector2D(1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

	lPageCounter = new Label();
	lPageCounter->setId("page-counter");
	lPageCounter->mTypography = tempDesignTokens::MainFont_600_18;
	lPageCounter->setAlignment(align::RIGHT_CENTER);
	lPageCounter->setSize(42, 24);
	lPageCounter->setResizeMode(Label::NONE);
	lPageCounter->TextColor = tempDesignTokens::GoldTextColor;
	mPageCounter->add(lPageCounter);
	countersStack->Set(lPageCounter, GridLayout::GridPosition(sVector2D(2, 0), sVector2D(1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

	Label* lPageCounterSlash = new Label();
	lPageCounterSlash->setId("page-counter-max");
	lPageCounterSlash->setCaption("/");
	lPageCounterSlash->mTypography = tempDesignTokens::MainFont_600_20;
	lPageCounterSlash->setResizeMode(Label::AUTOSIZE);
	lPageCounterSlash->setAlignment(align::CENTER_CENTER);
	mPageCounter->add(lPageCounterSlash);
	countersStack->Set(lPageCounterSlash, GridLayout::GridPosition(sVector2D(3, 0), sVector2D(1), ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Center)));

	lPageCounterMax = new Label();
	lPageCounterMax->setId("page-counter-max");
	lPageCounterMax->mTypography = tempDesignTokens::MainFont_600_16;
	lPageCounterMax->setAlignment(align::LEFT_CENTER);
	lPageCounterMax->setSize(42, 24);
	lPageCounterMax->setResizeMode(Label::NONE);
	mPageCounter->add(lPageCounterMax);
	countersStack->Set(lPageCounterMax, GridLayout::GridPosition(sVector2D(4, 0), sVector2D(1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

	arrowRight = new Icon("arrowRight.png");
	arrowRight->setId("arrow-right");
	arrowRight->setSize(48, 48);
	mPageCounter->add(arrowRight);
	arrowRight->OnClicked += [this](Widget* w, const Vector2D& pos) {
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		GoToPage(CurrentPageIdx + 1);
	};
	countersStack->Set(arrowRight, GridLayout::GridPosition(sVector2D(5, 0), sVector2D(1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center)));

	mCloseButton = new Button();
	mCloseButton->setId("close-btn");
	mCloseButton->setBackgroundImage(Image::GetImage("gameThumbnailClose.png"));
	mCloseButton->Background = Black;
	mCloseButton->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_0,
	                                           tempDesignTokens::RoundedCorner_12);
	mCloseButton->setSize(56, 56);
	mCloseButton->setOrigin(1.f, 0.f);
	mCloseButton->setFloating(true);
	add(mCloseButton);

	OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		mCloseButton->setPosition(getWidth() - 2, 2);
	};
}

void TLobbyPages::AddAndGetCategoryButton(const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf,
                                          const igp::FPlatformEnvironment& env) const
{
	if (mIconPositioner)
		mIconPositioner->AddCategoryButton(game, conf, env);
}


void TLobbyPages::RedrawContent()
{
	if (!mFilterButtons[EGameFilters::AllGames]->isVisible())
		UpdateFilters(EGameFilters::AllGames);
	mIconPositioner->RepositionLobbyIcons();
}

void TLobbyPages::SetSelectedCategory(const ELobbyGameCategory gameCategory)
{
	mIconPositioner->SetGameLobbyCategory(gameCategory);
	mFilterButtons[EGameFilters::AllGames]->SetSelected(true);
	GetAndSetVisibleFiltersAndCategories();
}

void TLobbyPages::UpdateFilters(const EGameFilters filter)
{
	mGameFilters = filter;
	mIconPositioner->SetGameFilter(mGameFilters);
}

void TLobbyPages::GoToPage(size_t pageIdx)
{
	if (pageIdx >= MaxPages - 1)
		pageIdx = MaxPages - 1;

	CurrentPageIdx = pageIdx;
	mIconPositioner->setX(-(static_cast<float>(pageIdx) * mPageContent->getWidth()), 0.3s);

	lPageCounter->setCaption(std::to_string(CurrentPageIdx + 1));

	if (MaxPages > 0)
	{
		arrowLeft->setVisible(CurrentPageIdx > 0);
		arrowRight->setVisible(CurrentPageIdx != MaxPages - 1);
	}
	else
	{
		arrowLeft->setVisible(false);
		arrowRight->setVisible(false);
		lPageCounterMax->setCaption("1");
	}
}

const std::set<ELobbyGameCategory> TLobbyPages::GetAndSetVisibleFiltersAndCategories()
{
	std::set<EGameRNGType> visibleFilters;
	std::set<ELobbyGameCategory> visibleCategories;
	mIconPositioner->GetVisibleGameFilters(visibleFilters, visibleCategories);

	visibleFilters.contains(EGameRNGType::Live) ? mFilterButtons[EGameFilters::Live]->setVisible(true) : mFilterButtons[EGameFilters::Live]->setVisible(false);
	visibleFilters.contains(EGameRNGType::Automated) ? mFilterButtons[EGameFilters::Automated]->setVisible(true) :
	                                                   mFilterButtons[EGameFilters::Automated]->setVisible(false);
	visibleFilters.contains(EGameRNGType::Virtual) ? mFilterButtons[EGameFilters::Virtual]->setVisible(true) : mFilterButtons[EGameFilters::Virtual]->setVisible(false);

	float padding = -8.f;
	if (visibleFilters.size() == 1)
	{
		mFilterButtons[EGameFilters::AllGames]->setVisible(false);

		switch (*visibleFilters.begin())
		{
			case EGameRNGType::Live: mFilterButtons[EGameFilters::Live]->SetSelected(true); break;
			case EGameRNGType::Automated: mFilterButtons[EGameFilters::Automated]->SetSelected(true); break;
			case EGameRNGType::Virtual: mFilterButtons[EGameFilters::Virtual]->SetSelected(true); break;
			default: break;
		}
		padding = 8.f;
	}
	else
	{
		if (visibleFilters.size() == 0)
			padding = 8.f;
		mFilterButtons[EGameFilters::AllGames]->setVisible(true);
		mFilterButtons[EGameFilters::AllGames]->SetSelected(true);
	}

	if (!mFilterButtons.empty())
	{
		for (int size = EGameFilters::_size() - 1; size >= 0; size--)

			if (mFilterButtons[EGameFilters::_from_index(size)]->isVisible())
			{
				filterStack->setPaddingFor(mFilterButtons[EGameFilters::_from_index(size)], { padding, 0, 8, 0 });
				break;
			}
	}
	mFilters->markLayoutDirty();

	return visibleCategories;
}
void TLobbyPages::SetChangeGameMode(bool changeGameMode) const
{
	mChangeGameContainer->setVisible(changeGameMode);
}

void TLobbyPages::SetCompactView(bool compactMode) const
{
	Rectangle textPadding;
	if (compactMode)
		textPadding = { 32, 0, 32, 0 };
	else
		textPadding = { 40, 0, 40, 0 };

	constexpr std::array filters = { ALL_STRING, GAME_CATEGORY_LIVE, AUTO_STRING, VIRTUAL_STRING };
	int i = 0;
	for (auto& button : mFilterButtons)
	{
		button.second->Text->setPadding(textPadding);
		button.second->Text->forceRedraw();
		button.second->Text->setCaption(
		  LocalizedMessage(filters[i], [](const std::string& msg) -> std::string { return yutils::Format("%s", MyUtils::ToLowercase(msg, true).c_str()); }));
		button.second->markLayoutDirty();
		i++;
	}

	mFilters->markLayoutDirty();
}

void TLobbyPages::PositionChangeGameContainer() const
{
	const int PADDING = 72;
	mChangeGameContainer->setSize(mPageContent->getWidth() - 2 * PADDING, mPageContent->getHeight());
	mChangeGameContainer->setX(PADDING);
}
