#include "TGameButton.h"

#include "MyUtils.h"
#include "TGameHistoryDisplay.h"
#include "TIGPlatformApp.h"
#include "TLangMessages.h"
#include "TotallyTemporaryDesignTokens.h"
#include "dealer-assist/DealerAssistSharedTypes.h"
#include "gui/widgets/label.hpp"
#include "yserver/TDealerGamesExtraData.h"

const Vector2D TGameButton::ButtonSize = Vector2D(386, 256);

bool TGameButton::IsGameUnavailable() const
{
	return bGameUnavailable;
}

TGameButton::TGameButton(Container* parent, const std::shared_ptr<igp::PlatformGamePackage>& game, igp::PlatformGameConfiguration& conf,
                         const igp::FPlatformEnvironment& env, bool bVisibleWhenUnavailable) :
    bShowWhenUnavailable(bVisibleWhenUnavailable), ID(conf.ID), HostType(game->Package.Game.Type), RNGType(EGameRNGType::Automated)
{
	Text->setVisible(false);

	if (parent)
		parent->add(this);

	setSize(ButtonSize);

	pCommander = pApp->GetModuleByName<TPlatformCommander>("Commander");
	auto layout = setLayout<BoxLayout>();

	setId("btnGame" + std::string(conf.ID));

	pButtonContent = new LayoutContainer();
	pButtonContent->setSize(getSize());
	pButtonContent->setId("btn-content");
	pButtonContent->OutlineStyle = tempDesignTokens::GameIconBorderOutline;
	add(pButtonContent);
	layout->PerWidgetBehaviors[pButtonContent].Alignment = { EChildAlignment::Stretch, EChildAlignment::Stretch };
	// setClippingMode(EClippingMode::NoClipping); // temp disable clipping

	GridLayout* grid = pButtonContent->setLayout<GridLayout>();
	grid->AddColumn({});
	grid->AddRow({ .Size = tempDesignTokens::GameIconLogoHeight });
	grid->AddRow({ .Size = tempDesignTokens::GameIconHistoryHeight });
	grid->AddRow({ .Size = tempDesignTokens::GameIconInfoHeight });

	if (conf.LogoTexture)
	{
		LayoutContainer* logoPanel = new LayoutContainer();
		BoxLayout* logoLayout = logoPanel->setLayout<BoxLayout>();
		logoLayout->DefaultBehavior.Alignment = EChildAlignment::Center;
		logoLayout->DefaultBehavior.Padding = Rectangle(8.f, 0.f, 8.f, 0.f);
		logoPanel->setId("logoPanel");
		pButtonContent->add(logoPanel);
		grid->Set(logoPanel, { 0, 0, 2, 1, ChildAlignment2D(EChildAlignment::Stretch) });

		Icon* logo = new Icon(conf.LogoTexture);
		logo->setId("logo");
		logoPanel->add(logo);
	}

	pHistoryPanel = new LayoutContainer();
	BoxLayout* historyLayout = pHistoryPanel->setLayout<BoxLayout>();
	historyLayout->DefaultBehavior.Alignment = EChildAlignment::Min;
	historyLayout->DefaultBehavior.Padding = Rectangle(16.f, 0.f, 16.f, 0.f);
	pHistoryPanel->setId("HistoryPanel");
	pButtonContent->add(pHistoryPanel);
	pHistoryPanel->setVisible(false);
	grid->Set(pHistoryPanel, { 0, 1, 2, 1, ChildAlignment2D(EChildAlignment::Stretch) });

	if (conf.Host)
	{
		uint32_t hostID = conf.Host->ID;
		const auto hostData = pCommander->GetTrackedHost(hostID);
		if (!hostData)
		{
			TLOG(LogApp, Warning, "Host data for host ID %u is not available", hostID);
			return;
		}

		if (HostType == yserver::gamehost::HostType::Roulette || HostType == yserver::gamehost::HostType::VirtualRoulette)
		{
			auto RouletteHistory = new TRouletteHistoryDisplay();
			RouletteHistory->setHeight(32);
			pHistoryPanel->add(RouletteHistory);
			historyLayout->PerWidgetBehaviors[RouletteHistory].Alignment = ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Center);
			mHistoryWidgets.push_back(RouletteHistory);
			OnInitParseRouletteResult(hostData->History);
			pHistoryPanel->setVisible(true);

			OnGameResultHandler = pCommander->OnGameResult.bind([this, hostID](const uint32_t id, const uint32_t gameID, const json& roundHistory) {
				if (hostID == id)
				{
					addRouletteResult(roundHistory);
				}
			});
		}
		else if ((HostType == yserver::gamehost::HostType::VirtualBaccarat) || (HostType == yserver::gamehost::HostType::Baccarat) ||
		         (HostType >= yserver::gamehost::HostType::VirtualDragonTiger && HostType <= yserver::gamehost::HostType::DealersGame))
		{
			if (HostType == yserver::gamehost::HostType::DealersGame)
				DealerGameInfo = conf.StaticGame().GetConfig("dat-game").get<dealer_assist::DealerGameInfoDto>();
			else
			{
				switch (HostType)
				{
					case yserver::gamehost::HostType::Baccarat: DealerGameInfo = { dealer_assist::EGameType::Baccarat, false }; break;
					case yserver::gamehost::HostType::VirtualBaccarat: DealerGameInfo = { dealer_assist::EGameType::Baccarat, true }; break;
					case yserver::gamehost::HostType::VirtualOpenBaccarat: DealerGameInfo = { dealer_assist::EGameType::OpenBaccarat, true }; break;
					case yserver::gamehost::HostType::VirtualDragonTiger: DealerGameInfo = { dealer_assist::EGameType::DragonTiger, true }; break;
					case yserver::gamehost::HostType::VirtualOpenDragonTiger: DealerGameInfo = { dealer_assist::EGameType::OpenDragonTiger, true }; break;
					case yserver::gamehost::HostType::VirtualThreeHeadedDragon: DealerGameInfo = { dealer_assist::EGameType::ThreeHeadedDragon, true }; break;
					default: DealerGameInfo = {};
				}
			}

			if (DealerGameInfo.GameType == dealer_assist::EGameType::Baccarat || DealerGameInfo.GameType == dealer_assist::EGameType::OpenBaccarat)
			{
				auto MainRoad = new TMainRoadDisplay(5, 22);
				pHistoryPanel->add(MainRoad);
				historyLayout->PerWidgetBehaviors[MainRoad].Alignment = ChildAlignment2D(EChildAlignment::Center);
				mHistoryWidgets.push_back(MainRoad);
				MainRoad->leftSideColor = tempDesignTokens::BaccaratLeftSideResultColor;
				MainRoad->rightSideColor = tempDesignTokens::BaccaratRightSideResultColor;
			}
			else if (DealerGameInfo.GameType == dealer_assist::EGameType::DragonTiger || DealerGameInfo.GameType == dealer_assist::EGameType::OpenDragonTiger)
			{
				auto MainRoad = new TMainRoadDisplay(5, 22);
				pHistoryPanel->add(MainRoad);
				historyLayout->PerWidgetBehaviors[MainRoad].Alignment = ChildAlignment2D(EChildAlignment::Center);
				mHistoryWidgets.push_back(MainRoad);
				MainRoad->leftSideColor = tempDesignTokens::TigerDragonLeftSideResultColor;
				MainRoad->rightSideColor = tempDesignTokens::TigerDragonRightSideResultColor;
			}
			else if (DealerGameInfo.GameType == dealer_assist::EGameType::ThreeHeadedDragon)
			{
				auto MainRoadGold = new TMainRoadDisplay(5, 7);
				pHistoryPanel->add(MainRoadGold);
				historyLayout->PerWidgetBehaviors[MainRoadGold].Alignment = ChildAlignment2D(EChildAlignment::Min, EChildAlignment::Stretch);
				mHistoryWidgets.push_back(MainRoadGold);
				MainRoadGold->Background = tempDesignTokens::GameIcon3HeadedDragonGold;
				MainRoadGold->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_4, tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_0,
				                                           tempDesignTokens::RoundedCorner_4);

				MainRoadGold->leftSideColor = tempDesignTokens::ThreeHeadedDragonLeftSideResultColor;
				MainRoadGold->rightSideColor = tempDesignTokens::ThreeHeadedDragonRightSideResultColor;

				auto MainRoadBlack = new TMainRoadDisplay(5, 7);
				pHistoryPanel->add(MainRoadBlack);
				historyLayout->PerWidgetBehaviors[MainRoadBlack].Alignment = ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Stretch);
				mHistoryWidgets.push_back(MainRoadBlack);
				MainRoadBlack->Background = tempDesignTokens::GameIcon3HeadedDragonBlack;
				MainRoadBlack->leftSideColor = tempDesignTokens::ThreeHeadedDragonLeftSideResultColor;
				MainRoadBlack->rightSideColor = tempDesignTokens::ThreeHeadedDragonRightSideResultColor;

				auto MainRoadRed = new TMainRoadDisplay(5, 7);
				pHistoryPanel->add(MainRoadRed);
				historyLayout->PerWidgetBehaviors[MainRoadRed].Alignment = ChildAlignment2D(EChildAlignment::Max, EChildAlignment::Stretch);
				mHistoryWidgets.push_back(MainRoadRed);
				MainRoadRed->Background = tempDesignTokens::GameIcon3HeadedDragonRed;
				MainRoadRed->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_4, tempDesignTokens::RoundedCorner_4,
				                                          tempDesignTokens::RoundedCorner_0);
				MainRoadRed->leftSideColor = tempDesignTokens::ThreeHeadedDragonLeftSideResultColor;
				MainRoadRed->rightSideColor = tempDesignTokens::ThreeHeadedDragonRightSideResultColor;
			}

			std::optional<dealer_assist::DealerGameInfoDto> activeGame;
			ScopedLock lock(hostData->State);
			const json* activeGameJson = FindMember((&hostData->State), "activeGame");
			if (activeGameJson && !activeGameJson->is_null())
				activeGame = activeGameJson->get<dealer_assist::DealerGameInfoDto>();
			if (activeGame->Id == DealerGameInfo.Id)
			{
				// only init data from same game
				OnInitParseCardResult(hostData->History);
				pHistoryPanel->setVisible(true);
			}
			else
			{
				pHistoryPanel->setVisible(false);
			}

			OnGameResultHandler = pCommander->OnGameResult.bind([this, hostID](const uint32_t id, const uint32_t gameID, const json& roundHistory) {
				if (hostID != id)
					return;

				if (const auto stateDataCpy = pCommander->GetTrackedHost(hostID))
				{
					ScopedLock lock(stateDataCpy->State);
					const json* activeGame = FindMember((&stateDataCpy->State), "activeGame");

					if (activeGame && !activeGame->is_null())
					{
						// only init data from same game
						dealer_assist::DealerGameInfoDto dealerGame = activeGame->get<dealer_assist::DealerGameInfoDto>();
						if (dealerGame.Id == DealerGameInfo.Id)
						{
							addCardResult(roundHistory);
							pHistoryPanel->setVisible(true);
							return;
						}
					}
				}

				pHistoryPanel->setVisible(false);
			});

			OnGameStateChangedHandler = pCommander->OnGameStateChanged.bind([this, hostID](const uint32_t id, const json& data) {
				if (hostID == id)
				{
					OnGameStateChanged(data);
				}
			});
		}
	}

	LayoutContainer* infoPanel = new LayoutContainer();
	BoxLayout* infoLayout = infoPanel->setLayout<BoxLayout>();
	infoLayout->DefaultBehavior.Alignment = EChildAlignment::Min;
	infoLayout->DefaultBehavior.Padding = Rectangle(16.f, 0.f, 16.f, 16.f);
	infoPanel->setId("InfoPanel");
	pButtonContent->add(infoPanel);
	grid->Set(infoPanel, { 0, 2, 2, 1, ChildAlignment2D(EChildAlignment::Stretch) });


	Label* pGameMinBet = new Label();
	pGameMinBet->setId("min-bet-txt");
	pGameMinBet->setCaption(LocalizedMessage(MIN_BET_STRING) + ":");
	pGameMinBet->mTypography = tempDesignTokens::MainFont_400_16;
	pGameMinBet->TextColor = tempDesignTokens::GreyTextColor;
	pGameMinBet->setResizeMode(Label::AUTOSIZE);
	pGameMinBet->setPadding(Rectangle(0.f, 4.f, 0.f, 2.f));
	pGameMinBet->OnDimensionChanged += [this, pGameMinBet, infoLayout](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		Rectangle padding = infoLayout->DefaultBehavior.Padding;
		padding.x() = pGameMinBet->getPosition().X() + pGameMinBet->getWidth() + 12;
		infoLayout->PerWidgetBehaviors[pGameMinBetValue].Padding = padding;
	};
	infoPanel->add(pGameMinBet);

	pGameMinBetValue = new Label();
	pGameMinBetValue->setId("min-bet-value");
	pGameMinBetValue->setCaption(MenuGUI()->FormatCreditsAsCurrency(conf.LiveGame()->MinBet == 0 ? 1 : conf.LiveGame()->MinBet));
	pGameMinBetValue->mTypography = tempDesignTokens::MainFont_600_16;
	pGameMinBetValue->setResizeMode(Label::AUTOSIZE);
	pGameMinBetValue->setPadding(Rectangle(0.f, 4.f, 0.f, 2.f));
	infoPanel->add(pGameMinBetValue);

	pIconTags = new LayoutContainer();
	pIconTags->setId("tags");
	pIconTags->bResizeToFitLayout = { false, false };
	pIconTags->setHeight(30.f);
	pIconTags->OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		updateGameInfoTextPositions = true;
	};
	pIconTags->setClippingMode(EClippingMode::ClipToDimensions);
	pIconTags->setOrigin(0.f, 1.0f);
	infoPanel->add(pIconTags);
	infoLayout->PerWidgetBehaviors[pIconTags].Alignment = { EChildAlignment::Stretch, EChildAlignment::Max };
	infoLayout->PerWidgetBehaviors[pIconTags].Padding = Rectangle(0.f, 0.f, 0.f, 14.f);

	lRNGName = new Label();
	lRNGName->setId("rng-name");
	if (conf.Host && conf.Host.get() && conf.Host.get()->Name.length() > 0)
		lRNGName->setCaption(LocalizedMessage(conf.Host.get()->Name));

	lRNGName->mTypography = tempDesignTokens::TitleFont_16;
	lRNGName->setX(16.f);
	lRNGName->setResizeMode(Label::AUTOSIZE);
	lRNGName->Background = tempDesignTokens::GameTag_RNGName;
	lRNGName->OutlineStyle = Outline(tempDesignTokens::GameTag_RNGNameBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);
	lRNGName->setPadding(Rectangle({ 8, 0 }, { 8, 0 }));
	pIconTags->add(lRNGName);


	lRNGType = new Label();
	lRNGType->setId("rng-type");
	if (conf.Host)
	{
		switch (game->Package.Game.Type)
		{
			case yserver::gamehost::HostType::Roulette: {
				lRNGType->setCaption(LocalizedMessage(AUTO_STRING));
				RNGType = EGameRNGType::Automated;
				break;
			}
			case yserver::gamehost::HostType::DealersGame: {
				if (DealerGameInfo.IsVirtual)
				{
					lRNGType->setCaption(LocalizedMessage(VIRTUAL_STRING));
					RNGType = EGameRNGType::Virtual;
				}
				else
				{
					lRNGType->setCaption(LocalizedMessage(GAME_CATEGORY_LIVE));
					RNGType = EGameRNGType::Live;
				}
				break;
			}
			case yserver::gamehost::HostType::Baccarat: {
				lRNGType->setCaption(LocalizedMessage(GAME_CATEGORY_LIVE));
				RNGType = EGameRNGType::Live;
				break;
			}
			default: {
				lRNGType->setCaption(LocalizedMessage(VIRTUAL_STRING));
				RNGType = EGameRNGType::Virtual;
				break;
			}
		}
	}
	lRNGType->mTypography = tempDesignTokens::TitleFont_16;
	lRNGType->setResizeMode(Label::AUTOSIZE);
	lRNGType->Background = tempDesignTokens::GameTag_RNGType;
	lRNGType->OutlineStyle = Outline(tempDesignTokens::GameTag_RNGTypeBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);
	lRNGType->setPadding(Rectangle({ 8, 0 }, { 8, 0 }));
	lRNGType->setClippingMode(EClippingMode::ClipToDimensions);
	pIconTags->add(lRNGType);

	gameNameLabel = new Label();
	gameNameLabel->setId("game-name-label");
	gameNameLabel->setCaption(conf.Info().Config.GameConfig.DisplayName);
	gameNameLabel->mTypography = tempDesignTokens::TitleFont_16;
	gameNameLabel->setResizeMode(Label::AUTOSIZE);
	gameNameLabel->Background = tempDesignTokens::GameTag_GameName;
	gameNameLabel->TextScrollSpeed = { 20.f, 5.f };
	gameNameLabel->OutlineStyle = Outline(tempDesignTokens::GameTag_GameNameBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);
	gameNameLabel->setPadding(Rectangle({ 8, 0 }, { 8, 0 }));
	gameNameLabel->setClippingMode(EClippingMode::ClipToDimensions);
	pIconTags->add(gameNameLabel);

	OnPressed += std::bind(&TGameButton::OnButtonPressed, this);

	pUnavailablePanel = new LayoutContainer();
	pUnavailablePanel->setSize(getSize());
	pUnavailablePanel->setVisible(true);
	pUnavailablePanel->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	GridLayout* unavailableGrid = pUnavailablePanel->setLayout<GridLayout>();
	unavailableGrid->AddColumn({});
	unavailableGrid->AddRow({ .Size = tempDesignTokens::GameIconLogoHeight });
	unavailableGrid->AddRow({ .Size = tempDesignTokens::GameIconHistoryHeight });
	unavailableGrid->AddRow({ .Size = tempDesignTokens::GameIconInfoHeight });
	pUnavailablePanel->setId("unavailablePanel");
	add(pUnavailablePanel);

	plUnavailable = new Label();
	plUnavailable->setId("game-unavailable");
	plUnavailable->setSize(354, 48);
	plUnavailable->Background = Red;
	plUnavailable->OutlineStyle = Outline(tempDesignTokens::GameIcon_UnavailableBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);
	plUnavailable->setCaption(LocalizedMessage());
	plUnavailable->mTypography = tempDesignTokens::TitleFont_24;
	plUnavailable->TextColor = White;
	plUnavailable->setAlignment(EAlignment::Center);
	plUnavailable->setOrigin(0.5f, 0.5f);
	pUnavailablePanel->add(plUnavailable);
	unavailableGrid->Set(plUnavailable, { 0, 1, 1, 1, ChildAlignment2D(EChildAlignment::Center) });

	OnGameConfigurationChange =
	  conf.OnGameConfigurationUpdated.bind([this, game, env](const igp::PlatformGameConfiguration& conf) { UpdateButtonFromGameConfiguration(game, conf, env); });

	UpdateButtonFromGameConfiguration(game, conf, env);
	updateGameInfoTextPositions = true;
}

bool TGameButton::ShouldGameBeDisplayed() const
{
	return mShouldBeDisplayed && (bShowWhenUnavailable || !bGameUnavailable);
}

void TGameButton::drawLogic(Graphics* graphics, float deltaTime)
{
	Button::drawLogic(graphics, deltaTime);
	if (updateGameInfoTextPositions)
	{
		constexpr float initialPadding = 16.f;
		constexpr float padding = 8.f;
		const auto tagContainerSize = pIconTags->getWidth();
		lRNGType->setX(initialPadding + lRNGName->getWidth() + padding);

		const float RNGNameAndRNGTypeWidth = lRNGType->getX() + lRNGType->getWidth() + padding;
		gameNameLabel->setX(RNGNameAndRNGTypeWidth);

		if ((gameNameLabel->getWidth() + RNGNameAndRNGTypeWidth) >= (tagContainerSize + initialPadding))
		{
			gameNameLabel->setWidth(tagContainerSize - RNGNameAndRNGTypeWidth - initialPadding);
			gameNameLabel->setResizeMode(Label::SCROLLABLE, Label::AUTOSIZE);
		}
		else
		{
			gameNameLabel->setResizeMode(Label::AUTOSIZE);
		}
		updateGameInfoTextPositions = false;
	}
}

void TGameButton::onLanguageChanged(ELanguage lang)
{
	Button::onLanguageChanged(lang);
	updateGameInfoTextPositions = true;
}

void TGameButton::OnButtonPressed()
{
	dynamic_cast<TIGPlatformApp*>(pApp)->PlayerStartGame(ID);
}

void TGameButton::addRouletteResult(const json& roundData)
{
	if (mHistoryWidgets.size() > 0 && mHistoryWidgets[0] && roundData.size() > 0 && roundData[1].is_number())
		mHistoryWidgets[0]->AddEntry(roundData[1].get<int>());
}

void TGameButton::addCardResult(const json& roundData) const
{
	using namespace dealer_assist;
	using namespace yserver::gamehost;
	GameRecordDto gameRecordDto = GameRecordDto::FromJSON(roundData);

	if (DealerGameInfo.GameType == EGameType::Baccarat || DealerGameInfo.GameType == EGameType::OpenBaccarat)
	{
		RoundData gameRoundData;
		switch (baccarat::EBaccaratWinner result = baccarat::EBaccaratWinner::_from_integral(gameRecordDto.Winners[0]))
		{
			case baccarat::EBaccaratWinner::PlayerWin: gameRoundData.winner = Winner::Left; break;
			case baccarat::EBaccaratWinner::BankerWin: gameRoundData.winner = Winner::Right; break;
			default: gameRoundData.winner = Winner::Tie; break;
		}
		dealer_assist::BaccaratGameRecordExtraDataDto extraDataDto;
		from_json(gameRecordDto.ExtraData, extraDataDto);
		gameRoundData.bankerPair = extraDataDto.BankerPair;
		gameRoundData.playerPair = extraDataDto.PlayerPair;
		gameRoundData.natural = extraDataDto.Naturals;

		if (!mHistoryWidgets.empty())
			mHistoryWidgets[0]->AddEntry(gameRoundData);
	}
	else if (DealerGameInfo.GameType == dealer_assist::EGameType::DragonTiger || DealerGameInfo.GameType == dealer_assist::EGameType::OpenDragonTiger)
	{
		RoundData gameRoundData;
		switch (dragontiger::EDragonTigerWinner result = dragontiger::EDragonTigerWinner::_from_integral(gameRecordDto.Winners[0]))
		{
			case dragontiger::EDragonTigerWinner::DragonWin: gameRoundData.winner = Winner::Left; break;
			case dragontiger::EDragonTigerWinner::TigerWin: gameRoundData.winner = Winner::Right; break;
			default: gameRoundData.winner = Winner::Tie; break;
		}
		if (!mHistoryWidgets.empty())
			mHistoryWidgets[0]->AddEntry(gameRoundData);
	}
	else
	{
		for (size_t i = 0; i < gameRecordDto.Winners.size(); i++)
		{
			RoundData gameRoundData;
			switch (threeheadeddragon::EThreeHeadedDragonWinner result = threeheadeddragon::EThreeHeadedDragonWinner::_from_integral(gameRecordDto.Winners[i]))
			{
				case threeheadeddragon::EThreeHeadedDragonWinner::DragonWin: gameRoundData.winner = Winner::Left; break;
				case threeheadeddragon::EThreeHeadedDragonWinner::TigerWin: gameRoundData.winner = Winner::Right; break;
				default: gameRoundData.winner = Winner::Tie; break;
			}
			if (!mHistoryWidgets.empty())
				mHistoryWidgets[i]->AddEntry(gameRoundData);
		}
	}
}

void TGameButton::OnInitParseRouletteResult(const json& data)
{
	size_t historySize = data.size();
	if (historySize > 0)
		for (size_t i = 0; i < historySize; i++) addRouletteResult(data[i]);
}

void TGameButton::OnInitParseCardResult(const json& data)
{
	size_t historySize = data.size();
	if (historySize > 0)
		for (size_t i = 0; i < historySize; i++) addCardResult(data[i]);
}

void TGameButton::OnGameStateChanged(const json& data)
{
	const std::string eventName = data["name"].get<std::string>();


	if (eventName == "phase")
	{
		if (data["new"].get<int>() == dealer_assist::EDealerAssistPhase::CardBurn)
			for (const auto& road : mHistoryWidgets) { road->ResetHistoryData(); }
	}
	else if (eventName == "gameState")
	{
		if (HostType == yserver::gamehost::HostType::DealersGame)
		{
			std::optional<dealer_assist::DealerGameInfoDto> type;
			const json* typeJSON = FindMember(data["new"], "activeGame");
			if (typeJSON && !typeJSON->is_null())
				type = typeJSON->get<dealer_assist::DealerGameInfoDto>();

			pHistoryPanel->setVisible(type && type.value() == DealerGameInfo);
		}
	}
}

bool TGameButton::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	switch (ev.Action)
	{
		case EHardwareButtonAction::StartGame:
			if (ev.bPressed)
				OnPressed();
			return true;
		case EHardwareButtonAction::Info:
			// if (ev.bPressed) ShowGameInfo(ID);
			return true;
		default: return false;
	}
}

void TGameButton::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	outAvailableActions.SetAvailable(EHardwareButtonAction::StartGame, START_GAME_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::Info, INFO_STRING);
}

void TGameButton::UpdateButtonFromGameConfiguration(const std::shared_ptr<igp::PlatformGamePackage>& game, const igp::PlatformGameConfiguration& conf,
                                                    const igp::FPlatformEnvironment& env)
{
	mShouldBeDisplayed = game->ShouldBeDisplayed(conf, env);

	bGameUnavailable = conf.bIsRunning ? true : game->IsGrayedOut(conf, env);
	setVisible(ShouldGameBeDisplayed());
	setEnabled(!bGameUnavailable);

	pGuiApp->DeferToDrawSimple([this, img = conf.GameImage]() { pButtonContent->Background = ImageBrush(img); }, "UpdateButtonFromGameConfiguration");

	LocalizedMessage message;
	if (bGameUnavailable && conf.Host)
	{
		if (conf.bIsRunning)
		{
			plUnavailable->Background = tempDesignTokens::GameTag_RNGName;
			plUnavailable->OutlineStyle = Outline(tempDesignTokens::GameTag_RNGNameBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);

			message = LocalizedMessage(ALREADY_RUNNING_STRING);
		}
		else
		{
			plUnavailable->Background = Red;
			plUnavailable->OutlineStyle = Outline(tempDesignTokens::GameIcon_UnavailableBorder, 1_px, tempDesignTokens::RoundedCorner_4, 1_px);

			message = !conf.Host->RuntimeErrors.empty() ? LocalizedMessage(HOST_ERROR_STRING) : LocalizedMessage(TABLE_CLOSED_STRING);
		}
	}

	plUnavailable->setCaption(message);
	pUnavailablePanel->setVisible(bGameUnavailable);

	pButtonContent->clearPostProcessPasses();
	if (bGameUnavailable)
		pButtonContent->addPostProcessPass(Grayscale());
}
