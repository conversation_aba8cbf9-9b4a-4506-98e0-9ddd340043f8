set(HID_SRC
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/SerialCommunication.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/1WireOnSerial/1wire_on_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/1WireOnSerial/dallas_ds2401_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/1WireOnSerial/dallas_ds2401_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/AcornRFID/rfid_drv_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/AcornRFID/rfid_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/Elo/elo_touch_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/Elo/SerialConnection.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/KeyboardElbet/keyboard_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/KeyboardElbet/keyboard_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/KeyboardTKP1/easy_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/KeyboardTKP1/keyboard_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/KeyboardTKP1/keyboard_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/Microtouch/micro_touch_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/Microtouch/SerialConnection.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/QuboRFID/qubo_rfid_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/QuboRFID/qubo_rfid_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/QuboRFID/qubo_rfid_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/eGalaxTouch/galax_touch_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/eGalaxTouch/SerialConnection.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/THumanInputDevices.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/HID/TMouseCalibration.cpp)

set(MONEY_SRC
        ${PROJECT_SOURCE_DIR}/src/common/TMachineBalanceManager.cpp
        ${PROJECT_SOURCE_DIR}/src/common/TStandaloneApplication.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ICTProto/ict_drv_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ICTProto/ict_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ICTProto/ict_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ID003/eba_serial_drv_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ID003/eba_serial_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ID003/easy_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/MyCoins/coins_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/MyCoins/coins_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/MyCoins/easy_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/gen2/gen2_drv_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/gen2/gen2_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ithaca950/ithaca950_drv_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ithaca950/ithaca950_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/ithaca950/IthacaSerial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/mei/driver.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/mei/mei_control.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/mei/mei_transport.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/SSP/driver.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/SSP/ssp_control.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/SSP/ssp_transport.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/bscp/driver.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/bscp/bscp_control.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/bscp/bscp_transport.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/money/TMoneyAcceptor.cpp)

set(SYS_SRC
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/CntSwBat/cntswbat_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/CntSwBat/cntswbat_drv.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/CntSwBat/cntsw_serial.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASAft.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASChannel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASExceptions.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASLink.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASModule.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASPoll.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASTicketing.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/SAS/TSASTimer.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/TSystemDevices.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/semaphore/TDefaultSemaphoreController.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/semaphore/TFranceSemaphoreController.cpp)

set(MENU_SRC
        ${PROJECT_SOURCE_DIR}/src/common/TMenuGUI.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TKeyboard.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPinDialog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleConfirmButton2.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TTextBox.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TDBGrid.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TToggleButton.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TKeyboardButton.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TScrollArea.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPanelMenu.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPayoutPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPayoutEnd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TAttendantPayInPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TCountersPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPowerMenuPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TSetupPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/THoperControl.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/THopperRetryPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TOtherStationsPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPlayBoardConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TMachineConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TSASConfigPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TKeyProgramm.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TKeyProperties.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TExpiredPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPeripheralConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TCoinsConfig.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TTotalsAllLog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TEventLogPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TCreditLogPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TJackpotHistoryLog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TTicketsLog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TOperatorLog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TSASLog.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TParamLogPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TMoneyLogPanel.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TSetupBase.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TPanelBase.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TMenuPanelBase.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TLogBase.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TMenuTabHolder.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TActiveGamesView.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TGameMenu.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TTopInfoBar.cpp


        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TDBDataSet.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TConsole.cpp
)

set(SDL_COMPONENTS_SRC
        ${PROJECT_SOURCE_DIR}/src/components/TImageList.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TQRCode.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TGlyphLabel.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TChecksumDisplay.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TSpriteAnimation.cpp
        ${PROJECT_SOURCE_DIR}/src/components/TScrollingComponent.cpp)

set(GUI_COMP_SRC
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TActionElement.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleStateButton.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleImageList.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleButton2.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TDropdown.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleConfirmButton2.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleListBox.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleRadioButton.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TMessageBox.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleSlider.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TStyleSlider2.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TPopupMessageBox.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TLobbyMenuButton.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/TDBParameterControl.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/DropdownItemContainer.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/comp/Dropdown.cpp
)

set(IGP_SOURCES
        ${PROJECT_SOURCE_DIR}/src/common/qrcodegen.cpp
        ${PROJECT_SOURCE_DIR}/src/common/Games.cpp

        ${PROJECT_SOURCE_DIR}/src/packman/PackmanSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/packman/PackmanAPI.cpp

        ${PROJECT_SOURCE_DIR}/src/jackpot/client/JackpotAdminClient.cpp
        ${PROJECT_SOURCE_DIR}/src/jackpot/client/JackpotClientConfig.cpp

        ${PROJECT_SOURCE_DIR}/src/common/TRGBAnimations.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/TAdminKeySystem.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/TCounterSystem.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/TNanoCounterSystem.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/TCounterNames.cpp
        ${PROJECT_SOURCE_DIR}/src/common/TNanoBalanceManager.cpp

        ${PROJECT_SOURCE_DIR}/src/imaxanano/TNanoTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/gui/TConsole.cpp

        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TVirtualCreditSelect.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TScreenshotViewer.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TJsonEditor.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TGamesConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TRNGConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TButtonConfig.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TPlayAwayConfigPanel.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/gui/panels/TUpdatePanel.cpp

        ${CMAKE_CURRENT_SOURCE_DIR}/PlatformGamePackage.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/PackmanClient.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TPlatformClient.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TPlatformLoader.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TIGPlatformApp.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TIGPlatformScreen.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TGameInfoPanel.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TIGLobbyView.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TIGLobby.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TUserSettings.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TGameHistory.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/InfoPage.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/IGPlatformV6/IGPboard_cmd.cpp
        ${PROJECT_SOURCE_DIR}/src/terminal/drv/system/IGPlatformV6/IGPboard_drv.cpp

        ${CMAKE_CURRENT_SOURCE_DIR}/SAS/TPlatformSAS.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TScreenshots.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/PlatformGamePackage.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/RunnableGame.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TGameButton.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TGameView.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/GameRunContext.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/PlatformCommander.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TButtonConfiguration.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TJackpotBaseConfigPanel.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TJackpotConfigPanel.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TroniusJackpotAdminClientModule.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TStageProgressDisplay.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TLobbyPages.cpp
        ${CMAKE_CURRENT_SOURCE_DIR}/TGameHistoryDisplay.cpp

        ${CMAKE_CURRENT_SOURCE_DIR}/components/ActiveGameStatusWidget.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/YServerAdminAPIClient.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TRouletteGamesExtraData.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GameRecordDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistSharedTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/yserver/TDealerGamesExtraData.cpp
        ${PROJECT_SOURCE_DIR}/src/common/TRouletteTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/serverhub/NodeSharedTypes.cpp

        ${PROJECT_SOURCE_DIR}/src/common/Shield.cpp
        ${PROJECT_SOURCE_DIR}/src/common/ShieldSerial.cpp)

add_executable(igplatform
        ${JACKPOT_SOURCES}
        ${DB_COMMON_SRC}
        ${GAME_CLIENT_SRC}
        ${IGP_SOURCES}
        ${HID_SRC}
        ${MONEY_SRC}
        ${SYS_SRC}
        ${MENU_SRC}
        ${GUI_COMP_SRC}
        ${SDL_COMPONENTS_SRC})

find_library(ROCKS_DB_LIB rocksdb)
find_library(JPEG_LIB jpeg)
find_library(ICU_UC icuuc)
find_library(ICU_I18N icui18n)

add_dependencies(igplatform serial)
target_link_libraries(igplatform serial)
target_include_directories(igplatform PRIVATE ${CMAKE_SOURCE_DIR}/modules/serial/include)

target_include_directories(igplatform PRIVATE ${CMAKE_SOURCE_DIR}/include/igplatform ${CMAKE_SOURCE_DIR}/include/imaxanano ${CMAKE_SOURCE_DIR}/include/terminal ${CMAKE_SOURCE_DIR}/include/terminal/gui ${CMAKE_SOURCE_DIR}/include/terminal/gui/panels ${CMAKE_SOURCE_DIR}/modules/rocksdb/include)

target_compile_definitions(igplatform PRIVATE IGPLATFORM STANDALONE)
target_compile_options(igplatform PRIVATE -fPIE)

target_link_libraries(igplatform rtfwk-cef jackpot-core yprotocol-client ${DB_LIB} ${OPENGL_LIBRARIES} ${JPEG_LIB} ${ROCKS_DB_LIB} ${BOOST_FILES} ${ICU_UC} ${ICU_I18N})

add_version_define(igplatform)
