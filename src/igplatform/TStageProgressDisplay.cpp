#include "TStageProgressDisplay.h"

#include "PackmanClient.h"
#include "gui/widgets/label.hpp"
#include "gui/widgets/progressbar.hpp"

TStageProgressDisplay::TStageProgressDisplay(float width, bool bIncludeAppLogs, int fontSizeLarge, int fontSizeSmall, bool bWithShadows, float relativeProgressBarSize)
{
	setWidth(width);
	bResizeToFitLayout.Y() = true;

	StackLayout* labelLayout = setLayout<StackLayout>();
	labelLayout->Direction = EStackLayoutDirection::VERTICAL;
	labelLayout->Alignment = EChildAlignment::Center;
	labelLayout->Padding.Y() = 10.f;

	mStageLabel = new Label();
	mStageLabel->setWidth(width);
	mStageLabel->setResizeMode(Label::NONE, Label::AUTOSIZE);
	mStageLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = { (float)fontSizeLarge, EDimensionUnit::px } });
	mStageLabel->setId("stageLabel");
	if (bWithShadows)
		mStageLabel->TextShadow = Shadow::MakeSoftShadow(2_px, 0_px, Black);
	add(mStageLabel);

	mProgressBar = new ProgressBar();
	mProgressBar->Background = Color::Gray(48);
	mProgressBar->ForegroundColor = White;
	mProgressBar->SetSmoothing(0.2f);
	mProgressBar->setSize(width * relativeProgressBarSize, 10.f);
	mProgressBar->setId("progressBar");
	add(mProgressBar);

	mProgressBarTotal = new ProgressBar();
	mProgressBarTotal->Background = Color::Gray(48);
	mProgressBarTotal->ForegroundColor = White;
	mProgressBarTotal->SetSmoothing(0.2f);
	mProgressBarTotal->setSize(width * relativeProgressBarSize, 10.f);
	mProgressBarTotal->setId("progressBarTotal");
	add(mProgressBarTotal);

	mStatusLabel = new Label();
	mStatusLabel->setWidth(width);
	mStatusLabel->setResizeMode(Label::NONE, Label::AUTOSIZE);
	mStatusLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = { (float)fontSizeSmall, EDimensionUnit::px } });
	mStatusLabel->setId("statusLabel");
	mStatusLabel->setWrap(true);
	if (bWithShadows)
		mStatusLabel->TextShadow = Shadow::MakeSoftShadow(2_px, 0_px, Black);
	add(mStatusLabel);

	if (bIncludeAppLogs)
		OnLogHandle = LogLoading.OnLog.bind([this](const LogEntry& entry) { mLastLog = entry.Message; });

	pPackman = rtfwk_sdl2::pApp->GetModuleByName<TPackmanClient>("Packman");
	OnPackmanLogHandle = pPackman->OnEvent.bind([this](const std::string& event, const json& ctx) {
		if (event == igp::UpdateLogEventName)
		{
			mLastLog = ctx["msg"].get<std::string>();
		}
	});
}

void TStageProgressDisplay::drawLogic(Graphics* graphics, float deltaTime)
{
	const std::optional<igp::FUpdateStatus> state = pPackman->UpdateInProgress();

	if (mStageLabel->getCaption() != pApp->CurrentLoadingStage())
	{
		mStageLabel->setCaption(pApp->CurrentLoadingStage());
		mProgressBar->ResetSmoothing();
		mProgressBarTotal->ResetSmoothing();
		mNumWorkItemsCompleted = 0;
	}
	size_t numWorkItemsCompleted = 0, numWorkItemsTotal = 0;
	float progressTotalNew, progressNew;
	progressTotalNew = pApp->CurrentLoadingStageProgress(progressNew, numWorkItemsCompleted, numWorkItemsTotal);
	if (numWorkItemsCompleted != mNumWorkItemsCompleted)
	{
		mProgressBar->ResetSmoothing();
		mNumWorkItemsCompleted = numWorkItemsCompleted;
	}

	mProgressBar->SetProgress(progressNew);
	mProgressBarTotal->SetProgress(progressTotalNew);

	mProgressBar->setVisible(numWorkItemsTotal > 1 && progressNew >= 0.f);
	mProgressBarTotal->setVisible(progressTotalNew >= 0.f);

	if (bEnableLiveLogs)
	{
		if (state && !state->CurrentlyUpdating.Package.Name.empty())
		{
			mStatusLabel->setCaption(std::format("{} '{}': {}", state->CurrentlyUpdating.Package.Type, state->CurrentlyUpdating.Package.Name, mLastLog.getCopy()));
		}
		else
			mStatusLabel->setCaption(mLastLog.getCopy());
	}

	LayoutContainer::drawLogic(graphics, deltaTime);
}

void TStageProgressDisplay::setDisplayMessage(const LocalizedMessage& msg)
{
	mStatusLabel->setCaption(msg);
}

void TStageProgressDisplay::setLiveLogEnabled(bool bEnabled)
{
	bEnableLiveLogs = bEnabled;
}
