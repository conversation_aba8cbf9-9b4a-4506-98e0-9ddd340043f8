#include "PlatformCommander.h"

#include "Cryptography.h"
#include "PackmanClient.h"
#include "TApplication.h"
#include "TGameView.h"
#include "TIGPlatformApp.h"
#include "TParamMan.h"
#include "TTaskMan.h"
#include "YUtils.h"
#include "common/THash.h"
#include "common/TMachineBalanceManager.h"
#include "common/TStandaloneApplication.h"

DEFINE_LOG_CATEGORY(Log<PERSON><PERSON><PERSON><PERSON>, "commander");
DEFINE_LOG_CATEGORY(LogYPlayer, "player");

using rtfwk_sdl2::pApp;

TPlatformCommander::TPlatformCommander(const std::string& yserverHostAddress, const std::string& nanoHostAddress)
{
	bSecureYServer = pApp->GetParamFromCache("SecureY")->AsInteger();
	bSecureNano = pApp->GetParamFromCache("SecureNano")->AsInteger();
	NanoAddress = nanoHostAddress;

	TrackingHosts.clear();

	YClientCommanderPtr = std::make_shared<YClient>(yserverHostAddress, bSecureYServer, pApp->ClientID() + " Commander");
	YClientViewerPtr = std::make_shared<YClient>(yserverHostAddress, bSecureYServer, pApp->ClientID() + " Viewer");

	if (pApp->Args().HasSwitch("show-yclient-coms"))
		YClientCommanderPtr->SetPrintTraffic(Debug);

	YComs = std::make_unique<YServerAdminAPIClient>(YClientCommanderPtr);
	YComsViewer = std::make_unique<YServerAdminAPIClient>(YClientViewerPtr);

	YClientCommanderPtr->OnEvent += [this](const std::string& event, const json& context) {
		if (event == yserver::events::SpectatedClientEvent)
		{
			pApp->Defer(
			  [this, event, context]() {
				  auto target = mTargets.find(context["uid"].get<std::string>());
				  if (target == mTargets.end())
					  return;

				  const std::string eventName = context["event"]["name"].get<std::string>();
				  const json& ctx = context["event"]["data"];
				  if (target->second->Type == yserver::EClientType::Player)
					  HandlePlayerEvent(*target->second, eventName, ctx);
				  else if (target->second->Type == yserver::EClientType::StaticViewer)
					  HandleGameClientEvent(*target->second, eventName, ctx);
			  },
			  "CommanderSpectatedClientEvent");
		}
		else if (event == yserver::events::CommanderTargetConnected)
		{
			pApp->Defer([this, event, context]() { HandleTargetConnected(context, false); }, "CommanderTargetConnectedEvent");
		}
		else if (event == yserver::events::CommanderTargetDisconnected)
		{
			pApp->Defer([this, event, context]() { HandleTargetDisconnected(context); }, "CommanderTargetDisconnectedEvent");
		}
		else if (event == yserver::events::CommanderPlayerRequestedAction)
		{
			const std::string type = context["type"].get<std::string>();

			if (!type.empty())
			{
				Log(Info, "Player requested action: %s", type.c_str());
				OnPlayerRequestedAction(type, context);
			}
		}
		else if (event == yserver::events::PlayerReservedFundsChanged)
		{
			uint64_t funds = context["reserved-credits"].get<uint64_t>();
			Log(Info, "Setting player reserved funds: %lu", funds);
			TStandaloneApplication* standalone = dynamic_cast<TStandaloneApplication*>(pApp);
			if (!standalone->Client() || !standalone->Client()->IsLoaded())
				return;

			{
				ScopedLock lock(mCurrentBalance);
				mCurrentReservedFunds = funds / standalone->Client()->BalanceManager()->Denomination() * yserver::BASE_DENOM_FOR_RESERVED_FUNDS;
			}

			standalone->Client()->PollCredits(false);
		}
		else
		{
			if (event == yserver::events::ModuleStatusChanged)
			{
				if (context["type"].get<std::string>() == yserver::provider::TYPE_NAME && context["uid"].get<std::string>() == mProviderUID)
				{
					mProviderID = context["id"].get<uint32_t>();
					NanoProviderStatus = yserver::EModuleStatus::_from_string(context["status"].get<std::string>().c_str());
					NanoProviderStatusCV.notify_all();
				}
			}
			OnCommanderEvent(event, context);
		}
	};

	YClientViewerPtr->OnEvent += [this](const std::string& event, const json& context) {
		HandleGameHostEvent(event, context);
	};

	YClientCommanderPtr->OnDisconnected += [this](bool bIsReconnecting) {
		auto lostTargets = mTargets;
		pApp->Defer(
		  [this, lostTargets]() {
			  for (const auto& val : lostTargets | std::views::values)
			  {
				  OnTargetDisconnected(*val);
				  GetGameState(val->Endpoint, std::bind(&TPlatformCommander::OnPlayerDisconnectedFromServer, this, std::placeholders::_1), false);
			  }
			  mTargets.clear();
		  },
		  "CommanderOnDisconnected");
	};

	YComs->SetAPIKeys(HashKey(pApp->GetParamFromCache("YServerKey")->AsString(), false), HashKey(pApp->GetParamFromCache("YServerSecret")->AsString(), false));
	YComsViewer->SetAPIKeys(HashKey(pApp->GetParamFromCache("YServerKey")->AsString(), false), HashKey(pApp->GetParamFromCache("YServerSecret")->AsString(), false));

	NanoSalt = HashKey(pApp->GetParamFromCache("NanoSalt")->AsString(), false);

	// PLATFORM_0 does no accounting!
	bHasAccounting = dynamic_cast<TDBInterface*>(pApp)->RegisteredInstance().Number > 0 && dynamic_cast<TDBInterface*>(pApp)->RegisteredInstance().Type != "UNKNOWN";

	if (bHasAccounting)
	{
		NanoGeneralClient = web::SingleWebsocketClient::New(NanoAddress, bSecureNano);
		NanoPlayerClient = web::SingleWebsocketClient::New(NanoAddress, bSecureNano);

		NanoPlayerClient->OnConnectionStateChanged += [this](web::websockets::session::state::value state, const std::error_code& ec) {
			if (state == web::websockets::session::state::open)
				Log(Debug, "Connected to nano player stream of %s.", pApp->ClientID().c_str());
			else if (state == web::websockets::session::state::closed)
				Log(Debug, "Nano player stream of %s ended.", pApp->ClientID().c_str());
		};

		NanoGeneralClient->OnMessage = std::bind(&TPlatformCommander::on_nano_message, this, std::string(), std::placeholders::_1);
		NanoPlayerClient->OnMessage = std::bind(&TPlatformCommander::on_nano_message, this, pApp->ClientID(), std::placeholders::_1);

		NanoGeneralClient->OnConnectionStateChanged += [this](web::websockets::session::state::value state, const std::error_code& ec) {
			if (state == web::websockets::session::state::closed && YClientCommanderPtr->Connected())
				pApp->Defer(std::bind(&TPlatformCommander::Disconnect, this), "CommanderDisconnect");
		};

		NanoGeneralClient->Initialize();
		NanoPlayerClient->Initialize();
	}

	for (size_t idx = 0; idx < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); idx++) GameSlots[idx]->Slot = idx;


	pClient = pApp->GetModuleByName<TPlatformClient>("GameClient1");
}

TPlatformCommander::~TPlatformCommander()
{
	Disconnect();

	if (bHasAccounting)
	{
		NanoPlayerClient->Stop();
		NanoGeneralClient->Stop();
	}
}

int TPlatformCommander::Load(void* pInitData)
{
	TAppModule::Load(pInitData);

	if (bHasAccounting)
	{
		NanoPlayerClient->Start("igp-nano-player");
		NanoGeneralClient->Start("igp-nano-all");
	}

	return 0;
}

void TPlatformCommander::DoBalanceUpdate()
{
	if (!bHasAccounting)
	{
		mCurrentBalance = credit::CreditArray();
		return;
	}

	json RequestBody(json::value_t::object);
	RequestBody["username"] = pApp->ClientID();

	auto response = NanoRequest("/info", RequestBody).get();
	if (response.get_status_code() != web::http::status::ok)
		return;

	json responseJson;
	try
	{
		responseJson = json::parse(response.get_body());
	}
	catch (const std::exception& e)
	{
		return;
	}

	bool bOK = false;
	const credit::CreditArray balance = yserver::CreditFromJSON(responseJson["balance"], &bOK);
	if (bOK)
		mCurrentBalance = balance;

	// we have to wait for the app to be ready due to module initialization not happening when this balance update first occurs
	const std::string status = responseJson["status"].get<std::string>();
	if (pApp->status() == EApplicationState::Running)
	{
		pApp->Defer(std::bind(&TPlatformCommander::OnStatusChange, this, status), "CommanderOnStatusChange1");
	}
	else
	{
		pApp->DoAsyncTask([this, status]() {
			pApp->waitForStatus(EApplicationState::Running);

			pApp->Defer(std::bind(&TPlatformCommander::OnStatusChange, this, status), "CommanderOnStatusChange2");
		});
	}
}

void TPlatformCommander::DisplayMessage(const LocalizedMessage& msg, uint64_t displayTime)
{
	if (msg.empty())
		return;
	json msgCtx(json::value_t::object);
	msgCtx["message"] = msg.Get(pApp->Language());
	msgCtx["displayTime"] = displayTime;
	BroadcastEventToTargets("message", msgCtx);
}

boost::future<web::http::response> TPlatformCommander::NanoAction(ENanoPlayerAction action, const json& body, std::optional<size_t> gameSessionIdx) const
{
	return NanoAction(action, body, gameSessionIdx ? GameSession(*gameSessionIdx) : Session());
}

boost::future<web::http::response> TPlatformCommander::NanoRequest(const std::string& endpoint, const json& body) const
{
	if (!bHasAccounting)
	{
		boost::promise<web::http::response> promise;
		promise.set_value(web::websockets::error::make_error_code(web::websockets::error::invalid_state));
		return promise.get_future();
	}

	// start the request
	const uint64_t startTS = ytime::GetTimeMsec();

	return NanoGeneralClient->Request(endpoint, PrepareNanoRequest(body))
	  .then(boost::launch::sync, [this, startTS, endpoint](boost::future<web::http::response> fut) -> web::http::response {
		  web::http::response response = fut.get();
		  if (!LogCommander.IsSupressed(VeryVerbose))
			  Log(VeryVerbose, "Nano request '%s' took %lums", endpoint.c_str(), ytime::GetTimeMsec() - startTS);

		  if (response.get_error_code())
			  Log(Error, "Request %s failed: %s", endpoint.c_str(), response.get_error_code().message().c_str());
		  else if (response.get_status_code() != web::http::status::ok)
			  Log(Debug, "Request %s returned error code %d: %s", endpoint.c_str(), (int)response.get_status_code(), response.get_status_msg().c_str());

		  return response;
	  });
}

credit::CreditArray TPlatformCommander::Balance() const
{
	ScopedLock lock(mCurrentBalance);
	return &mCurrentBalance - mCurrentBalance->MakeBet(mCurrentReservedFunds);
}

uint32_t TPlatformCommander::ProviderID() const
{
	return mProviderID;
}

const std::string& TPlatformCommander::ProviderUID() const
{
	return mProviderUID;
}

void TPlatformCommander::SetBalance(const credit::CreditArray& balance, uint64_t changeID)
{
	{
		ScopedLock lock(mCurrentBalance);
		if (mBalanceChangeID >= changeID)
			return;
		&mCurrentBalance = balance;
		mBalanceChangeID = changeID;
	}

	TStandaloneApplication* standalone = dynamic_cast<TStandaloneApplication*>(pApp);
	if (standalone->Client() && standalone->Client()->IsLoaded())
		standalone->Client()->PollCredits(false);
}

void TPlatformCommander::HandleTargetConnected(const json& data, bool bReconnected)
{
	const yserver::EClientType type = yserver::EClientType::_from_string(data["client"]["type"].get<std::string>().c_str());
	const std::string uid = data["uid"].get<std::string>();

	std::shared_ptr<CommanderConnectedTarget>& target = mTargets[uid];
	if (!target)
		target = std::make_shared<CommanderConnectedTarget>();

	if (!bReconnected)
	{
		target->Type = type;
		target->UID = uid;
		target->Endpoint = data["client"]["endp"].get<std::string>();
		target->CreditScale = data["client"]["credit-scale"].get<uint32_t>();
		target->Actions.clear();
	}

	yserver::PersistenceFlags flags;
	if (type == yserver::EClientType::Player)
		flags.Set(data["persistence"].get<uint32_t>());
	else
		flags.SetFlag(yserver::EPlayerPersistenceMode::Connection, true);

	yserver::GameClientState state;
	if (type == yserver::EClientType::Player || type == yserver::EClientType::StaticViewer)
		state = ClientStateFromJSON(data["state"]);

	OnTargetConnected(*target, bReconnected, flags, state);

	if (type == yserver::EClientType::Player && bReconnected && data["actions"].is_object())
		LoadSupportedActions(*target, data["actions"]);
}

void TPlatformCommander::HandleTargetDisconnected(const json& data)
{
	const std::string uid = data["uid"].get<std::string>();
	auto disconnectedTarget = mTargets.find(uid);
	if (disconnectedTarget == mTargets.end())
		return;

	OnTargetDisconnected(*disconnectedTarget->second);
	GetGameState(disconnectedTarget->second->Endpoint, std::bind(&TPlatformCommander::OnPlayerDisconnectedFromServer, this, std::placeholders::_1), false);


	mTargets.erase(disconnectedTarget);
}

std::string TPlatformCommander::Connect(const std::string& machineID)
{
	// disconnect any previous session (does nothing if no session exists)
	Disconnect();

	mProviderUID = std::string(yserver::provider::ProviderType(yserver::provider::ProviderType::nano)._to_string()) + "|" + machineID;
	mProviderUID = crypto::Hash(mProviderUID, EHashAlgorithm::MD5);

	Log(Debug, "Using Nano %s(%s) at %s and yserver at %s", machineID.c_str(), mProviderUID.c_str(), NanoAddress.c_str(),
	    YClientCommanderPtr->Client->HostAddress.c_str());

	YServerConnect();

	if (bHasAccounting)
	{
		{
			std::string errorStr;
			ScopedLock lock(mSession);
			&mSession = CreateSessionAndCreatePlayer(false, errorStr);

			while (mSession->empty())
			{
				Log(Error, "Error creating Nano session(%s), retrying in 5 seconds...", errorStr.c_str());
				std::this_thread::sleep_for(std::chrono::seconds(5));
				auto response = CreateSession(false).get();
				if (response.get_status_code() == web::http::status::ok)
					&mSession = response.get_body();
			}

			Log(Info, "Session successfully created: %s", mSession->c_str());
		}

		while (!NanoPlayerClient->IsConnected())
		{
			const std::error_code ec = NanoPlayerClient->Connect("/player/" + pApp->ClientID());

			if (ec)
			{
				Log(Warning, "Could not connect to platform stream for %s: %s! Retrying in 5 seconds...", pApp->ClientID().c_str(), ec.message().c_str());
				std::this_thread::sleep_for(std::chrono::seconds(5));
			}
		}

		while (!NanoGeneralClient->IsConnected())
		{
			const std::error_code ec = NanoGeneralClient->Connect("/all");
			if (!ec)
				break;

			Log(Error, "Failed to connect to nano player stream: %s", ec.message().c_str());
			Log(Warning, "Will retry connection to nano in 5 seconds...");
			std::this_thread::sleep_for(std::chrono::seconds(5));
		}

		pApp->SetRestriction(EParameterRestrictions::RamClear, !dynamic_cast<TDBInterface*>(pApp)->IsRamCleared());

		Log(Info, "Successfully connected to nano, querying for player balance...");
		DoBalanceUpdate();
	}

	ScopedLock lock(mCurrentBalance);
	Log(Info, "Player balance total: %ld credits", mCurrentBalance->Total());

	return mSession;
}

std::string TPlatformCommander::Session() const
{
	return mSession;
}

void TPlatformCommander::Disconnect()
{
	if (bHasAccounting)
	{
		{
			ScopedLock lock(mSession);
			if (!mSession->empty())
				NanoAction(ENanoPlayerAction::Logout, json());
		}
		NanoPlayerClient->Disconnect(websocketpp::close::status::normal, "Logging off");
		NanoGeneralClient->Disconnect(websocketpp::close::status::normal, "Logging off");
	}

	YClientCommanderPtr->Stop();
	YClientViewerPtr->Stop();
}

const LogCategory& TPlatformCommander::GetLogCategory() const
{
	return bLoading ? (const LogCategory&)LogLoading : (const LogCategory&)LogCommander;
}

const std::string pass("7777");
boost::future<web::http::response> TPlatformCommander::CreateSession(bool bAllowUnfinished) const
{
	json RequestBody(json::value_t::object);
	RequestBody["username"] = pApp->ClientID();
	RequestBody["pass"] = crypto::Hash(pass, EHashAlgorithm::SHA256);
	RequestBody["allow-unfinished"] = bAllowUnfinished;
	RequestBody["timeout"] = false;

	return NanoRequest("/session", RequestBody);
}

std::string TPlatformCommander::CreateSessionAndCreatePlayer(bool bAllowUnfinished, std::string& outError) const
{
	auto response = CreateSession(bAllowUnfinished).get();
	if (response.get_status_code() == web::http::status::ok)
		return response.get_body();

	if (response.get_status_code() == web::http::status::not_found)    // no such player, create one!
	{
		json RequestBody(json::value_t::object);
		RequestBody["type"] = ENanoPlayerType(ENanoPlayerType::Kiosk)._to_string();
		RequestBody["pass"] = crypto::Hash(pass, EHashAlgorithm::SHA256);
		const bool bIsMultiplayer = pApp->GetModuleByName<TPackmanClient>("Packman")->Features().contains(igp::MultiplayerFeatureName);
		RequestBody["nickname"] =
		  bIsMultiplayer ? ("Player " + std::to_string(dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Number)) : std::string("Slot Player");
		RequestBody["username"] = pApp->ClientID();

		bool bSuccess = true;
		do {
			if (!bSuccess)
			{
				Log(Error, "Error creating Nano player, retrying in 5 seconds...");
				std::this_thread::sleep_for(std::chrono::seconds(5));
			}
			bSuccess = NanoRequest("/register", RequestBody).get().get_status_code() == web::http::status::ok;
		} while (!bSuccess);

		// now try again
		response = CreateSession(bAllowUnfinished).get();
		if (response.get_status_code() == web::http::status::ok)
			return response.get_body();
	}

	outError = yutils::Format("status %d/%s", response.get_status_code(), response.get_status_msg().c_str());
	return {};
}

std::string TPlatformCommander::CreateGameSession(size_t gameIdx)
{
	if (!bHasAccounting)
		return {};

	ScopedLock lock(mGameSessions[gameIdx]);
	if (!mGameSessions[gameIdx]->Session.empty())
		return mGameSessions[gameIdx]->Session;

	do {
		Log(Info, "Creating game session...");
		auto response = CreateSession(true).get();
		if (response.get_status_code() == web::http::status::ok)
			mGameSessions[gameIdx]->Session = response.get_body();
		if (mGameSessions[gameIdx]->Session.empty())
		{
			Log(Error, "Could not create game session, retrying in 5 seconds...");
			std::this_thread::sleep_for(std::chrono::seconds(5));
		}
	} while (mGameSessions[gameIdx]->Session.empty());

	AddTargetSession(mGameSessions[gameIdx]->Session);

	mGameSessions[gameIdx]->GameRoundData.clear();
	auto response = NanoAction(ENanoPlayerAction::GameStatus, json(), mGameSessions[gameIdx]->Session).get();
	if (response.get_status_code() == web::http::status::ok)
	{
		try
		{
			mGameSessions[gameIdx]->GameRoundData = json::parse(response.get_body());
		}
		catch (const std::exception&)
		{
		}
	}
	mGameSessions[gameIdx]->ActiveRound = mGameSessions[gameIdx]->GameRoundData.is_object() && mGameSessions[gameIdx]->GameRoundData["ended"].get<uint64_t>() == 0 ?
	                                        mGameSessions[gameIdx]->GameRoundData["gid"].get<uint64_t>() :
	                                        0;

	return mGameSessions[gameIdx]->Session;
}

std::string TPlatformCommander::GameSession(size_t gameIdx) const
{
	SharedScopedLock lock(mGameSessions[gameIdx]);
	return mGameSessions[gameIdx]->Session;
}

void TPlatformCommander::InvokeAction(CommanderConnectedTarget& target, yserver::EPlayerAction action, const std::string& subaction, const json& context)
{
	if (!target.Actions.contains(action))
	{
		Log(Warning, "Cannot execute action '%s' because it isn't supported!", action._to_string());
		return;
	}

	if (!target.Actions[action].bEnabled)
	{
		Log(Warning, "Cannot execute any action '%s' because it isn't enabled right now!", action._to_string());
		return;
	}

	if (!subaction.empty() && !target.Actions[action].SubActions.contains(subaction))
	{
		Log(Warning, "Cannot execute action '%s:%s' because it isn't supported!", action._to_string(), subaction.c_str());
		return;
	}

	if (!subaction.empty() && !target.Actions[action].SubActions[subaction].bEnabled)
	{
		Log(Warning, "Cannot execute action '%s:%s' because it isn't enabled right now!", action._to_string(), subaction.c_str());
		return;
	}

	json actionReqObj(json::value_t::object);
	actionReqObj["type"] = action._to_string();
	if (!subaction.empty())
		actionReqObj["action"] = subaction;
	actionReqObj["context"] = context;
	actionReqObj["target"] = target.UID;

	YClientCommanderPtr->Request("action", actionReqObj).then(boost::launch::sync, [this, action, subaction](boost::future<YResponse> response) {
		if (response.get().Status == EMessageStatus::ResponseOk)
		{
			Log(Debug, "Executed action '%s%s' successfully!", action._to_string(), (subaction.empty() ? std::string() : (":" + subaction)).c_str());
		}
	});
}

void TPlatformCommander::ExecuteModuleAction(const std::string& moduleType, uint32_t moduleId, const std::string& action, const json& params,
                                             const std::function<void(const YResponse&)>& callback)
{
	if (!moduleId || action.empty())
		return;

	json actionReqObj(json::value_t::object);
	actionReqObj["target-type"] = moduleType;
	actionReqObj["target"] = moduleId;
	actionReqObj["action"] = action;
	actionReqObj["params"] = params;

	auto req = YClientCommanderPtr->Request("module-action", actionReqObj);
	if (callback)
		req.then(boost::launch::sync, [callback](boost::future<YResponse> fut) { callback(fut.get()); });
}

CommanderConnectedTarget::EBindActionResult CommanderConnectedTarget::BindActionEnabled(const std::function<void(bool)>& actionEnabledChangedHandler,
                                                                                        yserver::EPlayerAction action, const std::string& subaction)
{
	auto find = Actions.find(action);
	if (find == Actions.end())
		return EBindActionResult::NotFound;

	if (subaction.empty())
	{
		find->second.OnEnabledChanged.push_back(actionEnabledChangedHandler);
		actionEnabledChangedHandler(find->second.bEnabled);
		return find->second.bEnabled ? EBindActionResult::Enabled : EBindActionResult::Disabled;
	}
	else
	{
		auto find2 = find->second.SubActions.find(subaction);
		if (find2 == find->second.SubActions.end())
			return EBindActionResult::NotFound;

		find2->second.OnEnabledChanged.push_back(actionEnabledChangedHandler);
		return find2->second.bEnabled ? EBindActionResult::Enabled : EBindActionResult::Disabled;
	}
}

void TPlatformCommander::LockClient(ELockLevels level, const LocalizedMessage& message)
{
	auto emplaced = mLordOfTheLockFlags.try_emplace(level, message);
	if (emplaced.second)
	{
		OnLockUpdated();
	}
	else if (emplaced.first->second != message)
	{
		emplaced.first->second = message;
		OnLockUpdated();
	}
}

void TPlatformCommander::UnlockClient(ELockLevels level)
{
	if (mLordOfTheLockFlags.erase(level))
		OnLockUpdated();
}

void TPlatformCommander::AddTargetSession(const std::string& session)
{
	if (session.empty())
		return;

	json addTargetCtx(json::value_t::object);
	addTargetCtx["provider"] = ProviderID();
	addTargetCtx["session"] = session;
	addTargetCtx["latch"] = true;
	addTargetCtx["remove-when-client-disconnects"] = false;

	YClientCommanderPtr->Request("add-target-session", addTargetCtx).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		YResponse response = fut.get();
		if (response.Status != EMessageStatus::ResponseOk)
			return;

		if (response.Message.Body().is_object())
			pApp->Defer(std::bind(&TPlatformCommander::HandleTargetConnected, this, response.Message.Body(), false), "CommanderAddTargetSession");
	});
}

void TPlatformCommander::AddTarget(yserver::EClientType type, const std::string& uid)
{
	json addTargetCtx(json::value_t::object);
	addTargetCtx["type"] = type._to_string();
	addTargetCtx["uid"] = uid;
	addTargetCtx["latch"] = true;
	addTargetCtx["remove-when-client-disconnects"] = true;

	YClientCommanderPtr->Request("add-target", addTargetCtx).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
		YResponse response = fut.get();
		if (response.Status != EMessageStatus::ResponseOk)
			return;

		if (response.Message.Body().is_object())
			pApp->Defer(std::bind(&TPlatformCommander::HandleTargetConnected, this, response.Message.Body(), false), "CommanderHandleTargetConnected");
	});
}

size_t TPlatformCommander::NumActiveGames() const
{
	SharedScopedLock lock(ActiveGames);
	return ActiveGames->size();
}

void TPlatformCommander::TrackHost(uint32_t id)
{
	auto found = std::find_if(TrackingHosts.begin(), TrackingHosts.end(), [&id](const std::pair<uint32_t, FHostData> host) { return host.first == id; });
	if (found == TrackingHosts.end())
	{
		json requestObj(json::value_t::object);
		requestObj["host"] = id;
		YClientViewerPtr->Request("track-gamehost", requestObj).then(boost::launch::sync, [this, id](boost::future<YResponse> fut) {
			YResponse response = fut.get();
			if (response.Status != EMessageStatus::ResponseOk)
				return;

			const json* history = FindMember(response.Message.Body(), "history");
			if (!history || history->is_null() || history->empty())
				history = FindMember(response.Message.Body(), "historyNumbers");

			if (history && !history->is_null() && !history->empty())
				TrackingHosts.insert({ id, { *history, response.Message.Body()["state"] } });
			else
				TrackingHosts.insert({ id, { {}, response.Message.Body()["state"] } });

			OnHostTracked(id, response.Message.Body());
		});
	}
}

void TPlatformCommander::UntrackHost(uint32_t id)
{
	auto found = std::find_if(TrackingHosts.begin(), TrackingHosts.end(), [&id](const std::pair<uint32_t, FHostData> host) { return host.first == id; });
	if (found != TrackingHosts.end())
	{
		YClientViewerPtr->Request("untrack-gamehost", id).then(boost::launch::sync, [this, id, found](boost::future<YResponse> fut) {
			YResponse response = fut.get();
			if (response.Status != EMessageStatus::ResponseOk)
				return;

			TrackingHosts.erase(found);

			OnHostUntracked(id);
		});
	}
}

TPlatformCommander::FHostData TPlatformCommander::GetTrackedHost(uint32_t id) const
{
	auto found = std::find_if(TrackingHosts.begin(), TrackingHosts.end(), [&id](const std::pair<uint32_t, FHostData> host) { return host.first == id; });
	if (found != TrackingHosts.end())
		return found->second;

	return {};
}

size_t TPlatformCommander::NumActiveRounds() const
{
	size_t num = 0;
	for (size_t idx = 0; idx < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); idx++)
	{
		SharedScopedLock lock(mGameSessions[idx]);
		if (mGameSessions[idx]->ActiveRound)
			num++;
	}
	return num;
}

uint64_t TPlatformCommander::GetActiveGameRound(size_t gameIdx) const
{
	SharedScopedLock lock(mGameSessions[gameIdx]);
	return mGameSessions[gameIdx]->ActiveRound;
}

void TPlatformCommander::FlushCounterChangeQueue()
{
	std::list<std::pair<std::string, std::list<FNanoCounterUpdate>>> changes;
	{
		ScopedLock lock(QueuedChanges);
		while (!QueuedChanges->empty())
		{
			if (changes.empty() || changes.front().first != QueuedChanges->front().Source)
			{
				changes.push_back({ QueuedChanges->front().Source, {} });
			}

			changes.back().second.push_back({ QueuedChanges->front().CounterId, QueuedChanges->front().Value, QueuedChanges->front().CountingFromTS });
			QueuedChanges->pop();
		}
	}

	for (const auto& changeBlock : changes) OnCountersChanged(changeBlock.first, changeBlock.second);
}

yserver::GameClientState TPlatformCommander::ClientStateFromJSON(const json& val)
{
	yserver::GameClientState clientState;
	clientState.State = yserver::EGameClientState::Error;

	std::optional<yserver::EGameClientState> state = yserver::EGameClientState::_from_string_nocase_nothrow(val["state"].get<std::string>().c_str());

	if (state)
	{
		clientState.State = *state;
		clientState.SubState = val["subState"].get<uint32_t>();
		clientState.Flags.clear();
		for (const json& flag : val["flags"]) clientState.Flags.insert(flag.get<std::string>());
	}

	return clientState;
}

void TPlatformCommander::OnLockUpdated()
{
	for (const auto& [targetUid, target] : mTargets)
	{
		if (target->Type != yserver::EClientType::Player)
			continue;

		if (mLordOfTheLockFlags.empty())
		{
			YClientCommanderPtr->Request("unlock", targetUid).then(boost::launch::sync, [this](boost::future<YResponse> fut) {
				if (fut.get().Status == EMessageStatus::ResponseOk)
				{
					Log(Normal, "Unlocked client successfully!");
				}
			});
		}
		else
		{
			json requestObj(json::value_t::object);
			requestObj["target"] = targetUid;
			requestObj["message"] = mLordOfTheLockFlags.rbegin()->second.Get();
			YClientCommanderPtr->Request("lock", requestObj).then(boost::launch::sync, [this, msg = mLordOfTheLockFlags.rbegin()->second](boost::future<YResponse> fut) {
				if (fut.get().Status == EMessageStatus::ResponseOk)
				{
					Log(Normal, "Locked client: %s", msg.Get(ELanguage::English).c_str());
				}
			});
		}
	}
}

void TPlatformCommander::HandleNanoGameSessionEvent(FNanoSessionData& session, const std::string& event, const json& context)
{
	ytime::ScopedPerformanceCounter perfDelegate("TPlatformCommander::HandleNanoEvent", LogCommander, 500);
	if (event == "game-start")
	{
		session.GameRoundData = context;
		session.ActiveRound = context["gid"].get<uint64_t>();
	}
	else if (event == "game-end")
	{
		session.ActiveRound = 0;
		session.GameRoundData = context;
	}
	else if (event == "session-end")
	{
		session.Session.clear();
	}
	else if (event == "transaction")
	{
		auto typ = ENanoTransactionType::_from_string_nocase_nothrow(context["type"].get<std::string>().c_str());
		if (!typ)
			return;

		const credit::CreditArray amount = yserver::CreditFromJSON(context["amount"]);

		const int64_t trxTotal = amount.Total();
		OnNanoTransaction(*typ, trxTotal, context["gid"].get<uint64_t>());

		if (context.contains("instigator") && context["instigator"].get<std::string>() == pApp->ClientID())
			return;    // ignore transaction updates coming from actions that we did (feedback loop)

		// Log(Info, "Got nano transaction event: %s", context.toStyledString().c_str());
		if (*typ == ENanoTransactionType::Deposit || *typ == ENanoTransactionType::Withdraw)
		{
			auto standalone = dynamic_cast<TStandaloneApplication*>(pApp);
			if (standalone->Client() && standalone->Client()->BalanceManager())
			{
				if (context["from"] == pApp->ClientID())
					dynamic_cast<TMachineBalanceManager*>(standalone->Client()->BalanceManager())->OnPayoutFinished(trxTotal);
				else if (context["to"] == pApp->ClientID())
					dynamic_cast<TMachineBalanceManager*>(standalone->Client()->BalanceManager())->OnPayinFinished(trxTotal);
			}
		}
	}
}

void TPlatformCommander::HandleNanoSessionlessEvent(const std::string& event, const json& context)
{
	Log(Debug, "Nano event '%s'", event.c_str());
	ytime::ScopedPerformanceCounter perfDelegate("TPlatformCommander::HandleNanoEvent::" + event, LogCommander, 200);
	if (event == "counter-change" || event == "counter-reset")
	{
		const json& counterList = context["counters"];
		const std::string source = context["source"].get<std::string>();

		std::list<FNanoCounterUpdate> changes;
		for (auto change = counterList.begin(); change != counterList.end(); change++)
		{
			const int id = yutils::strToInt(change.key(), 0);

			if (id <= 0)
				continue;

			if (OnCountersChanged.isBound())
			{
				changes.push_back({ id, (*change)["v"].get<int64_t>(), (*change)["t"].get<uint64_t>() });
			}
			else
			{
				ScopedLock lock(QueuedChanges);
				QueuedChanges->push({ source, id, (*change)["v"].get<int64_t>(), (*change)["t"].get<uint64_t>() });
			}
		}

		if (!changes.empty())
			OnCountersChanged(source, changes);
	}
	else if (event == "status")
	{
		OnStatusChange(context.get<std::string>());
	}
	else if (event == "handpay-cancelled")
	{
		TSASModule* pSAS = pApp->GetModuleByName<TSASModule>("SAS1");
		if (pSAS && pSAS->IsEnabled())
			pSAS->HandpayAborted();
	}
	else if (event == "handpay-pending")
	{
		FPendingHandpayInfo info;
		info.Load(context);

		dynamic_cast<TStandaloneApplication*>(pApp)->OnNewHandpayPending(info);
	}
	else if (event == "handpay-confirmed")
	{
		const uint64_t credits = context["handpay"]["amount"].get<uint64_t>();
		const EHandpayTriggerSource typ = EHandpayTriggerSource::_from_string_nothrow(context["handpay"]["src"].get<std::string>().c_str(), EHandpayTriggerSource::User);
		Log(Important, "Handpay of %lu credits (type '%s') confirmed (by '%s')!", credits, typ._to_string(), context["confirmed-by"].get<std::string>().c_str());
		TSASModule* pSAS = pApp->GetModuleByName<TSASModule>("SAS1");
		if (pSAS && pSAS->IsEnabled())
			pSAS->HandpayCompleted(credits, typ);
	}
	else if (event == "playerSettingsChanged")
	{
		const std::string parameter = context["name"].get<std::string>();
		OnPlayerSettingsChanged(parameter, context["value"]);
	}
	else
	{
		OnNanoEvent(pApp->ClientID(), event, context);
	}
}

void TPlatformCommander::HandleEveryNanoEvent(const std::string& event, const json& context)
{
	if (event == "balance-update")
	{
		bool bOK = false;
		const credit::CreditArray balance = yserver::CreditFromJSON(context["balance"], &bOK);
		if (bOK)
		{
			SetBalance(balance, context["changeID"].get<uint64_t>());
		}
	}
}

void TPlatformCommander::on_nano_message(const std::string& playerID, const std::string& msg)
{
	json payload;
	try
	{
		payload = json::parse(msg);
	}
	catch (const std::exception& e)
	{
		Log(Debug, "Nano socket update '%s' could not be parsed to json: %s!", msg.c_str(), e.what());
		return;
	}

	if (!payload.is_object() || !payload.contains("event") || !payload.contains("context") || !payload["event"].is_string())
	{
		Log(Debug, "Nano socket update '%s' is not a valid event!", msg.c_str());
		return;
	}

	const std::string eventName = payload["event"].get<std::string>();
	const json& eventCtx = payload["context"];
	if (playerID.empty())
	{
		if (eventName == yserver::events::PlayerWelcome)
		{
			NanoRequest("/auth-listener/" + eventCtx.get<std::string>(), crypto::Hash(NanoSalt + eventCtx.get<std::string>(), EHashAlgorithm::SHA256));
			return;
		}

		if (payload.contains("player"))
		{
			const std::string player = payload["player"].get<std::string>();
			if (player != pApp->ClientID())
			{
				pApp->Defer([this, player, eventName, eventCtx]() { OnNanoEvent(player, eventName, eventCtx); }, "CommanderOnNanoEvent1");
			}
		}
		else
		{
			pApp->Defer([this, eventName, eventCtx]() { OnNanoEvent({}, eventName, eventCtx); }, "CommanderOnNanoEvent2");
		}
	}
	else
	{
		const json* sessionJson = FindMember(payload, "session");
		pApp->Defer(
		  [this, eventName, eventCtx, session = sessionJson ? sessionJson->get<std::string>() : std::string()]() {
			  HandleEveryNanoEvent(eventName, eventCtx);

			  if (session.empty() || session == Session())
			  {
				  HandleNanoSessionlessEvent(eventName, eventCtx);
			  }
			  else
			  {
				  for (size_t idx = 0; idx < mGameSessions.size(); idx++)
				  {
					  ScopedLock lock(mGameSessions[idx]);
					  if (mGameSessions[idx]->Session == session)
					  {
						  Log(Debug, "Nano event '%s' for game %lu", eventName.c_str(), idx);
						  HandleNanoGameSessionEvent(&mGameSessions[idx], eventName, eventCtx);
						  lock.unlock();

						  ytime::ScopedPerformanceCounter perfDelegate("CommanderOnNanoGameSessionEvent", LogCommander, 500);
						  OnNanoGameSessionEvent(idx, eventName, eventCtx);
						  return;
					  }
				  }
			  }
		  },
		  "CommanderOnNanoMessage");
	}
}

web::http::request TPlatformCommander::PrepareNanoRequest(const json& body) const
{
	web::http::request req;
	if (!body.is_null())
	{
		req.set_json_body(body);
		req.set_method(web::http::method(web::http::verb::post));
	}
	else
	{
		req.set_method(web::http::method(web::http::verb::get));
	}
	req.append_header(web::http::field::user_agent, "IGPlatform commander " + pApp->ClientID());
	const uint64_t time = ytime::GetSystemTimeMsec() + 2000;
	req.append_header(web::http::field::expires, std::to_string(time));
	req.append_header(web::http::field::authorization, crypto::Hash(NanoSalt + std::to_string(time) + req.get_body(), EHashAlgorithm::SHA256));
	return req;
}

void TPlatformCommander::OnStatusChange(const std::string& status)
{
	const bool bIsInHandpay = (status == HANDPAY_STATUS);
	if (TPlatformClient* Client = dynamic_cast<TStandaloneApplication*>(pApp)->Client())
	{
		Client->CLIENT_HANDPAY_LOCK->SetLockState(bIsInHandpay);
		if (bIsInHandpay)
			Client->StartService(false, 0);
	}
}

boost::future<web::http::response> TPlatformCommander::NanoAction(ENanoPlayerAction action, const json& body, const std::string& session) const
{
	return NanoRequest(yutils::Format("/action/%s/%s", session.c_str(), action._to_string()), body);
}

bool TPlatformCommander::HandleGameClientEvent(CommanderConnectedTarget& target, const std::string& event, const json& context)
{
	ytime::ScopedPerformanceCounter perfDelegate("CommanderHandleGameClientEvent", LogApp, 200);
	if (event == yserver::events::GameClientStateChanged)
	{
		if (OnStateChanged)
			OnStateChanged(target, ClientStateFromJSON(context));
	}
	else
	{
		return false;
	}

	return true;
}

bool TPlatformCommander::HandlePlayerEvent(CommanderConnectedTarget& target, const std::string& event, const json& context)
{
	if (HandleGameClientEvent(target, event, context))
		return true;

	if (event == yserver::events::PlayerSupportedActionsLoaded)
	{
		LoadSupportedActions(target, context);
	}
	else if (event == yserver::events::PlayerActionEnabledChanged)
	{
		ytime::ScopedPerformanceCounter perfDelegate("CommanderPlayerActionEnabledChanged", LogCommander, 200);
		std::optional<yserver::EPlayerAction> typ = yserver::EPlayerAction::_from_string_nocase_nothrow(context["type"].get<std::string>().c_str());

		// Log(Info, "Got event action-enabled for %s and is %s", context["action"].get<std::string>().c_str(), context["enabled"].get<bool>() ? "enabled" : "disabled");

		if (!typ)
			return true;

		auto find = target.Actions.find(*typ);
		if (find == target.Actions.end())
			return true;

		const bool bEnabled = context["enabled"].get<bool>();
		const std::string subaction = context["action"].get<std::string>();
		if (subaction.empty())
		{
			if (find->second.bEnabled != bEnabled)
			{
				find->second.bEnabled = bEnabled;
				for (const auto& handler : find->second.OnEnabledChanged) handler(bEnabled);
			}
		}
		else
		{
			auto subactionFind = find->second.SubActions.find(subaction);
			if (subactionFind != find->second.SubActions.end() && subactionFind->second.bEnabled != bEnabled)
			{
				subactionFind->second.bEnabled = bEnabled;
				for (const auto& handler : subactionFind->second.OnEnabledChanged) handler(bEnabled);
			}
		}
	}
	else if (event == yserver::events::PlayerReconnected)
	{
		HandleTargetConnected(context, true);
	}
	else
	{
		ytime::ScopedPerformanceCounter perfDelegate("CommanderOnPlayerEventDelegate", LogCommander, 500);
		OnPlayerEvent(target, event, context);
	}

	return true;
}

bool TPlatformCommander::HandleGameHostEvent(const std::string& event, const json& context)
{
	// TLOG(LogCommander, Critical, "Viewer event: %s - %s", event.c_str(), yutils::jsonToStr(context).c_str());
	if (event == yserver::events::ModuleEvent)
	{
		const std::string eventName = context["event"]["name"].get<std::string>();
		const uint32_t id = context["id"].get<uint32_t>();
		if (eventName == "game-state-changed")
		{
			auto found = std::find_if(TrackingHosts.begin(), TrackingHosts.end(), [&id](const std::pair<uint32_t, FHostData> host) { return host.first == id; });
			if (found != TrackingHosts.end())
			{
				const std::string name = context["event"]["data"]["name"].get<std::string>();
				json value = context["event"]["data"]["new"];
				bool updateState = true;

				if (name == "winNumber")
				{
					int newWin = context["event"]["data"]["new"].get<int>();
					int oldWin = context["event"]["data"]["old"].get<int>();
					// roulette logic - winNumber
					if (newWin >= 0 && oldWin < 0)
					{
						uint32_t gameID = found->second.State["gameID"].get<uint32_t>();

						json roundHistory(json::value_t::array);
						roundHistory.push_back(gameID);
						roundHistory.push_back(newWin);
						found->second.History.push_back(roundHistory);

						OnGameResult(id, gameID, roundHistory);
					}
				}
				else if (name == "roundEnd")
				{
					// card logic - skipping roundEnd
					found->second.History.push_back(value);
					uint32_t gameID = value["gameNumber"].get<uint32_t>();

					updateState = false;

					OnGameResult(id, gameID, value);
				}
				else if (name == "gameState")
				{
					// card logic - gameState override everything
					found->second.State = value;
					updateState = false;
				}

				if (updateState)
					found->second.State[name] = value;
			}
			OnGameStateChanged(id, context["event"]["data"]);
		}
	}

	return true;
}

void TPlatformCommander::LoadSupportedActions(CommanderConnectedTarget& target, const json& list)
{
	for (auto action = list.begin(); action != list.end(); action++)
	{
		std::optional<yserver::EPlayerAction> opt = yserver::EPlayerAction::_from_string_nocase_nothrow(action.key().c_str());
		if (!opt)
		{
			Log(Warning, "Unknown supported action type %s!", action.key().c_str());
			continue;
		}
		YActionType& Type = target.Actions[*opt];
		Type.bEnabled = (*action)["enabled"].get<bool>();
		Type.bRootActionAllowed = (*action)["allowRootAction"].get<bool>();
		const json& subactions = (*action)["subactions"];
		for (auto subaction = subactions.begin(); subaction != subactions.end(); subaction++)
		{
			Type.SubActions[subaction.key()].bEnabled = (*subaction)["enabled"].get<bool>();
		}
		Log(Info, "Action type '%s' supports %s%lu subactions", opt->_to_string(), Type.bRootActionAllowed ? "the root action and " : "", subactions.size());
	}
	Log(Info, "A total of %lu action types are supported.", target.Actions.size());

	if (OnSupportedActionsLoaded)
		OnSupportedActionsLoaded(target.Endpoint, target.Actions);
}

void TPlatformCommander::YServerConnect()
{
	mProviderID = 0;
	NanoProviderStatus = yserver::EModuleStatus::Disabled;

	while (!pApp->shuttingDown())
	{
		std::string error;
		const bool bSuccess = YComs->ConnectToProtectedEndpoint(yserver::EClientType::Commander, {}, &error);
		if (bSuccess)
			break;

		Log(Error, "Failed to create a commander endpoint on yserver (%s), retrying in 5 seconds", error.c_str());
		std::this_thread::sleep_for(std::chrono::seconds(5));
	}

	if (YComs->Client->Connected())
		Log(Info, "Successfully connected to server as a commander!");

	while (!pApp->shuttingDown())
	{
		auto response = YComs->Client->Request("status").get();
		if (response.Status <= EMessageStatus::TimedOut)
		{
			Log(Error, "Y server status request timed out, retrying in 5 seconds...");
			std::this_thread::sleep_for(std::chrono::seconds(5));
			continue;
		}

		if (response.Status != EMessageStatus::ResponseOk)
		{
			Log(Error, "Could not get Y server status (%s), retrying in 5 seconds...", response.ErrorMessage().c_str());
			std::this_thread::sleep_for(std::chrono::seconds(5));
			continue;
		}

		for (const json& provider : response.Message.Body()["providers"])
		{
			if (provider["uid"].get<std::string>() == mProviderUID)
			{
				mProviderID = provider["id"].get<uint32_t>();
				NanoProviderStatus = yserver::EModuleStatus::_from_string(provider["status"].get<std::string>().c_str());
				break;
			}
		}

		break;
	}

	while (!pApp->shuttingDown())
	{
		std::string error;
		const bool bSuccess = YComsViewer->ConnectToProtectedEndpoint(yserver::EClientType::Viewer, {}, &error);
		if (bSuccess)
			break;

		Log(Error, "Failed to create a viewer endpoint on yserver (%s), retrying in 5 seconds", error.c_str());
		std::this_thread::sleep_for(std::chrono::seconds(5));
	}

	if (YComsViewer->Client->Connected())
		Log(Info, "Successfully connected to server as a viewer!");

	if (pApp->shuttingDown())
		return;

	{
		SharedScopedLock lock(NanoProviderStatus);
		if (&NanoProviderStatus != yserver::EModuleStatus::Ready)
			Log(Info, "Waiting for nano provider to come online with UID %s...", mProviderUID.c_str());
		NanoProviderStatusCV.wait(lock, [this]() -> bool { return &NanoProviderStatus == yserver::EModuleStatus::Ready || pApp->shuttingDown(); });
	}
}

bool TPlatformCommander::HasTarget() const
{
	return !mTargets.empty();
}

void TPlatformCommander::BroadcastEventToTargets(const std::string& event, const json& ctx)
{
	json msg(json::value_t::object);
	msg["name"] = event;
	msg["data"] = ctx;
	YClientCommanderPtr->Request("target-trigger", msg);
}

void TPlatformCommander::GetGameState(const std::string& endpoint, const std::function<void(GameRunContext&)>& work, bool bReadOnly)
{
	const std::optional<size_t> position = GetGameIndexFromEndpoint(endpoint);
	if (position)
		GetGameState(*position, work, bReadOnly);
}

void TPlatformCommander::GetGameState(size_t gameSlot, const std::function<void(GameRunContext&)>& work, bool bReadOnly)
{
	if (gameSlot > dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames())
		return;

	auto& slot = GameSlots[gameSlot];
	if (bReadOnly)
		slot.LockShared();
	else
		slot.Lock();

	work(&slot);

	if (bReadOnly)
		slot.UnlockShared();
	else
		slot.Unlock();
}

void TPlatformCommander::ForAllGames(const std::function<void(GameRunContext&)>& work, bool bReadOnly)
{
	for (size_t idx = 0; idx < dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames(); idx++) GetGameState(idx, work, bReadOnly);
}

void TPlatformCommander::requestGameExit(GameRunContext& ctx, EClosingState state, const std::string& message)
{
	if (ctx.State == EGameInstanceState::Inactive || ctx.State == EGameInstanceState::ClosingGame || !ctx.RunningGame)
		return;

	// if in replay mode or if we have no connected target, safe to kill game at any time!
	if (!ctx.ConnectedTarget || ctx.Mode == EPlayMode::REPLAY)
	{
		OnCloseGameCtx(ctx, EGameCloseType::Kill);
		OnGameContextCleanup(ctx, EGameCloseType::Kill);
		ctx.SetState(EGameInstanceState::Inactive);
		return;
	}

	if (state == EClosingState::NOT_CLOSING)
	{
		if (ctx.PendingClose.State != EClosingState::CRITICAL_CLOSE)
		{
			if (ctx.PendingClose.State != EClosingState::NOT_CLOSING)
			{
				Log(Debug, "Game %s close cancelled!", ctx.RunningGame->ID.string().c_str());
				InvokeAction(*ctx.ConnectedTarget, yserver::EPlayerAction::Exit, yserver::PlayerExitSubaction_Cancel);
				ctx.PendingClose.State = state;
				ctx.PendingClose.Type = EGameCloseType::NormalExit;
			}
			if (auto task = ctx.GameClosingTask.lock())
				task->Remove();
			ctx.GameClosingTask.reset();
		}
		return;
	}

	// if we are still loading, just cancel that
	if (ctx.State != EGameInstanceState::Inactive && ctx.State <= EGameInstanceState::WaitingForGameReady)
	{
		OnCloseGameCtx(ctx, EGameCloseType::Kill);
		OnGameContextCleanup(ctx, EGameCloseType::Kill);
		return;
	}

	if (state < ctx.PendingClose.State)    // block user close when there is a system close pending
		return;

	ctx.PendingClose.State = state;
	ctx.PendingClose.Type = EGameCloseType::NormalExit;

	json exitObj(json::value_t::object);
	exitObj["message"] = message;

	if (state == EClosingState::USER_CLOSE)
	{
		Log(Debug, "Game close request received by USER: %s", message.c_str());
		exitObj["user-initiated"] = true;
	}
	else
	{
		Log(Debug, "Game close request received by SYSTEM: %s", message.c_str());
	}
	TLOG(LogApp, Important, "Requesting yserver to close game!");
	InvokeAction(*ctx.ConnectedTarget, yserver::EPlayerAction::Exit, std::string(), exitObj);
}


void TPlatformCommander::OnPlayerDisconnectedFromServer(GameRunContext& ctx)
{
	ctx.ConnectedTarget.reset();
	if (!ctx.RunningGame || ctx.DebugMode)
		return;

	if (ctx.PendingClose.State == EClosingState::NOT_CLOSING)    // unexpected close!
	{
		if (ctx.Mode == EPlayMode::REPLAY)
		{
			ctx.PendingClose.Type = EGameCloseType::NormalExit;
		}
		else
		{
			if (ctx.RunningGame)
				TLOG(LogGames, Warning, "Game %s on slot %lu closed without notifying the server (maybe crashed)!", ctx.RunningGame->ID.string().c_str(), ctx.Slot);
			ctx.PendingClose.Type = EGameCloseType::CrashOrTimedOut;
		}
	}

	// if crashed, try to move log file, because the generic one will get overwritten on the next run
	const bool bKilledWhenOperating = ctx.PendingClose.Type == EGameCloseType::Kill && ctx.State == EGameInstanceState::PlayingGame;
	if (ctx.PendingClose.Type == EGameCloseType::CrashOrTimedOut || ctx.LastGameStart.bPlayerPresenceRequired || bKilledWhenOperating)
	{
		std::string filename;
		if (ctx.PendingClose.Type == EGameCloseType::CrashOrTimedOut)
			filename = "crash_";
		if (ctx.LastGameStart.bPlayerPresenceRequired)
			filename += "unfinished_";
		if (bKilledWhenOperating)
			filename += "killed_";

		filename += yutils::FormatTime("%d-%m-%Y %H-%M-%S", std::chrono::system_clock::now()) + ".txt";

		std::error_code ec;
		std::filesystem::rename(ctx.RunningPackage->WritableDirectory / RunnableGame::LOGFILE_NAME, ctx.RunningPackage->WritableDirectory / filename, ec);
	}
	OnCloseGameCtx(ctx, ctx.PendingClose.Type);
	OnGameContextCleanup(ctx, ctx.PendingClose.Type);
	ctx.SetState(EGameInstanceState::Inactive);
}

void TPlatformCommander::CloseGame(const std::string& endpoint, EClosingState closingState, const std::string& message)
{
	const std::optional<size_t> slot = GetGameIndexFromEndpoint(endpoint);
	if (slot)
		CloseGame(*slot, closingState, message);
}

void TPlatformCommander::CloseGame(size_t slotID, EClosingState closingState, std::string message)
{
	if (slotID == std::string::npos)
	{
		ForAllGames([this, message, closingState](GameRunContext& ctx) { InternalCloseGame(ctx, closingState, message); });
	}
	else
		GetGameState(slotID, [&](GameRunContext& ctx) { InternalCloseGame(ctx, closingState, message); }, false);
}

void TPlatformCommander::InternalCloseGame(GameRunContext& ctx, EClosingState closingState, std::string message)
{
	if (ctx.State <= EGameInstanceState::WaitingForYPlayer)
	{
		// delete endpoint
		ctx.SetState(EGameInstanceState::Inactive);
	}
	else if (ctx.State == EGameInstanceState::WaitingForGameReady)
	{
		if (ctx.ConnectedTarget)
			YClientCommanderPtr->Request("kick", ctx.ConnectedTarget->UID);
	}
	else
	{
		requestGameExit(ctx, closingState, message);
	}
}

void TPlatformCommander::OnCloseGameCtx(GameRunContext& ctx, EGameCloseType type)
{
	using namespace rtfwk_sdl2;
	if (ctx.PendingClose.State == EClosingState::NOT_CLOSING)    // if we weren't closing before, this must be a system close
		ctx.PendingClose.State = EClosingState::CRITICAL_CLOSE;
	ctx.PendingClose.Type = type;

	if (ctx.RunningGame)    // ce igra tece, pol kill igrice, nadaljujemo game close ko se igra zapre!
	{
		if (!ctx.SetState(EGameInstanceState::ClosingGame))    // if already waiting for game close, we can return from this function
			return;
		TLOG(LogGamePackage, Info, "Stopping running game %s on slot %lu (%s)...", ctx.RunningGame->ID.string().c_str(), ctx.Slot, type._to_string());
	}
	bool bTryToRestart = false;

	if (ctx.LastGameStart.ID.valid())    // log this close, check if crashed
	{
		switch (type)
		{
			case EGameCloseType::CrashOrTimedOut:
				TLOG(LogGamePackage, Warning, "Game %s on slot %lu has timed out or crashed!", ctx.LastGameStart.ID.string().c_str(), ctx.Slot);

				bTryToRestart = true;

				// ce se je zaklenilo v restart required (ne more naredit screenshotov) ne poizkusamo startat ponovno
				if (pClient->CLIENT_RESTART_REQUIRED_LOCK->IsLocked())
					bTryToRestart = false;

				if (ctx.LastGameStart.StartAttempts >= MAX_GAME_CRASHES)
					bTryToRestart = false;    // too many crashes, stop starting this game!
				break;
			case EGameCloseType::Inactive: TLOG(LogGamePackage, Info, "Game %s was closed due to inactivity.", ctx.LastGameStart.ID.string().c_str()); break;
			case EGameCloseType::Kill:
				ctx.LastGameStart.StartAttempts = 0;
				TLOG(LogGamePackage, Info, "Game %s was successfully closed - killed!", ctx.LastGameStart.ID.string().c_str());
				break;
			case EGameCloseType::NormalExit: TLOG(LogGamePackage, Info, "Game %s was successfully closed!", ctx.LastGameStart.ID.string().c_str()); break;
			default: TLOG(LogGamePackage, Warning, "Unhandled game close case %s!", type._to_string()); break;
		}
	}

	// ce nismo bili sredi igre (se ni usulo), potem resetiramo vrednost parametra nazadnje igrane igre
	if (!ctx.LastGameStart.bPlayerPresenceRequired && !bTryToRestart)
	{
		ScopedLock lock(ActiveGames);
		ActiveGames->erase(ctx.LastGameStart.Endpoint);
		ctx.ResetLastPlayedGame();
	}
}

void TPlatformCommander::OnGameContextCleanup(GameRunContext& ctx, EGameCloseType type)
{
	using namespace rtfwk_sdl2;

	// remove timed tasks
	if (auto task = ctx.GameClosingTask.lock())
		task->Remove();
	ctx.GameClosingTask.reset();
	if (auto task = ctx.InactiveGameTask.lock())
		task->Remove();
	ctx.InactiveGameTask.reset();
	if (auto task = ctx.GameLoadTimeoutTask.lock())
		task->Remove();
	ctx.GameLoadTimeoutTask.reset();

	ctx.PendingClose.State = EClosingState::NOT_CLOSING;

	ctx.Mode = EPlayMode::NORMAL;
	ctx.ConnectedTarget.reset();
	pClient->SetIsBigWin(false);
	ctx.GameRounds.clear();
	ctx.AccountingRounds.clear();

	if (ctx.LastGameStart.bPlayerPresenceRequired)    // check for unfinished games
	{
		TLOG(LogGamePackage, Warning, "Game %s has an unfinished state on the server!", ctx.LastGameStart.ID.string().c_str());

		ctx.LastPlayedGame->SetValue(ctx.LastGameStart.ID.string());
		pApp->AddToStoreQueue(ctx.LastPlayedGame);
	}
	pClient->CLIENT_UNFINISHED_GAMEX_LOCK[ctx.Slot]->SetLockState(ctx.LastGameStart.bPlayerPresenceRequired);    // lock if unfinished game

	{
		ScopedLock lock(ActiveGames);
		ActiveGames->erase(ctx.LastGameStart.Endpoint);
	}
	ctx.ResetLastPlayedGame();
}

std::optional<size_t> TPlatformCommander::GetGameIndexFromEndpoint(const std::string& endpoint) const
{
	SharedScopedLock lock(ActiveGames);
	auto find = ActiveGames->find(endpoint);
	if (find != ActiveGames->end())
		return find->second;
	return std::nullopt;    // invalid index
}
