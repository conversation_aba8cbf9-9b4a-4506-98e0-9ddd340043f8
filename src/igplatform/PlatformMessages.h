#pragma once

#include "TLangMessages.h"

void LoadPlatformMessages()
{
	LocalizedMessage::Preload(MSG_OUT_OF_ORDER_LOCK, "Gaming machine is out of order.");    // 300
	LocalizedMessage::Preload(MSG_COPY_PROTECTION_LOCK, "Could not obtain a valid license for this copy of the software! Check cables and fuses!");    // 301
	LocalizedMessage::Preload(MSG_SAS_GENERAL_LOCK, "Online system requested a lock.");    // 302
	LocalizedMessage::Preload(MSG_GENERAL_LOCK, "This play station is locked. Please call attendant.");    // 303
	LocalizedMessage::Preload(MSG_HANDPAY_LOCK, "Handpay is pending. Please wait for attendant.");    // 304
	LocalizedMessage::Preload(MSG_EXTERNAL_MEMORY_LOCK, "There was a fault reading external memory.");    // 305
	LocalizedMessage::Preload(MSG_MECHANICAL_COUNTERS_LOCK, "Error advancing mechanical counters.");    // 306
	LocalizedMessage::Preload(MSG_HOPPER_ERROR_LOCK, "There was an error with the hopper.");    // 307
	LocalizedMessage::Preload(MSG_BILL_ERROR_STACKER_OPEN, "Bill acceptor stacker is open");    // 309
	LocalizedMessage::Preload(MSG_BILL_ERROR_STACKER_JAM, "Bill acceptor stacker is jammed");    // 310
	LocalizedMessage::Preload(MSG_BILL_ERROR_STACKER_FULL, "Bill acceptor not ready");    // 311
	LocalizedMessage::Preload(MSG_BILL_ERROR_ACCEPTOR_JAM, "Bill acceptor is jammed");    // 312
	LocalizedMessage::Preload(MSG_BILL_ERROR_POWER_UP_BILL_ACCEPTOR, "Powered up with a bill in the acceptor");    // 313
	LocalizedMessage::Preload(MSG_BILL_ERROR_POWER_UP_BILL_STACKER, "Powered up with a bill in the stacker");    // 314
	LocalizedMessage::Preload(MSG_BILL_ERROR_RECYCLER_COMMUNICATION_ERROR, "There was an error communicating with the bill recycler.");    // 315
	LocalizedMessage::Preload(MSG_BILL_ERROR_COMMUNICATION_FAILED, "Communication link with bill acceptor could not be established.");    // 316
	LocalizedMessage::Preload(MSG_COIN_ERROR_ACCEPTED_NOT_PAID, "Cannot payin already accepted coin!");    // 317
	LocalizedMessage::Preload(MSG_COIN_ERROR_COMMUNICATION_ERROR, "Coin acceptor communication error");    // 318
	LocalizedMessage::Preload(MSG_COIN_ERROR_COMMUNICATION_FAILED, "Coin acceptor communication failed");    // 319
	LocalizedMessage::Preload(MSG_TICKET_ERROR_HEAD_UP, "Ticket printer head is upside down");    // 320
	LocalizedMessage::Preload(MSG_TICKET_ERROR_CHASIS_OPEN, "Ticket printer chassis is open");    // 321
	LocalizedMessage::Preload(MSG_TICKET_ERROR_PAPER_NOT_LOADED, "Paper isn't loaded in the ticket printer");    // 322
	LocalizedMessage::Preload(MSG_TICKET_ERROR_PAPER_JAM, "Ticket printer is jammed");    // 323
	LocalizedMessage::Preload(MSG_TICKET_ERROR_NOT_READY, "Ticket printer is not ready");    // 324
	LocalizedMessage::Preload(MSG_TICKET_ERROR_COMMUNICATION_ERROR, "Error communicating with the ticket printer");    // 325
	LocalizedMessage::Preload(MSG_CARD_READER_ERROR_LOCK, "There was an error with the card reader.");    // 327
	LocalizedMessage::Preload(MSG_BIG_DOOR_OPEN, "Big door is opened!");    // 332
	LocalizedMessage::Preload(MSG_DEPOSIT_BOX_DOOR_OPEN, "Deposit box door is opened!");    // 333
	LocalizedMessage::Preload(MSG_LOGIC_DOOR_OPEN, "Logic door is opened!");    // 334
	LocalizedMessage::Preload(MSG_SMALL_DOOR_OPEN, "Small door is opened!");    // 335
	LocalizedMessage::Preload(MSG_USER_LOCK, "This play station was locked by the player. Please call attendant.");    // 336
	LocalizedMessage::Preload(MSG_INTEGRITY_LOCK, "Integrity check failed! Invalid binary version");    // 337
	LocalizedMessage::Preload(MSG_SAS_DOWN_LOCK, "Could not establish communication with online system");    // 338
	LocalizedMessage::Preload(MSG_TILT_LOCK, "Gaming machine is in TILT.");    // 339
	LocalizedMessage::Preload(MSG_ENCHANCED_VALIDATION_LOCK, "Enhanced validation not configured.");    // 340
	LocalizedMessage::Preload(MSG_SAS_AFT_LOCK, "Online system is in the process of validating a transaction");    // 341
	LocalizedMessage::Preload(MSG_RESTART_REQUIRED_LOCK, "Restart of the gaming machine is required.");    // 342
	LocalizedMessage::Preload(MSG_UNFINISHED_GAME_LOCK, "Gaming machine could not resume unfinished game. Please call attendant.");    // 349
	LocalizedMessage::Preload(MSG_TICKET_IS_PRINTING, "Ticket is printing. Please wait...");    // 350
	LocalizedMessage::Preload(MSG_YSERVER_CONNECTION_LOST, "Connection with yserver was lost. Reconnect is in progress...");    // 351
	LocalizedMessage::Preload(MSG_CANNOT_PAYOUT_RESTRICTED, "Cannot payout restricted credits via handpay.");    // 352
	LocalizedMessage::Preload(MSG_GAME_REPLAY_FAILED, "Failed replaying game round");    // 362
	LocalizedMessage::Preload(MSG_GAME_START_FAILED, "Game could not start");    // 363
	LocalizedMessage::Preload(MSG_UPDATE_LOCK, "Update in progress. Please wait...");    // 364
	LocalizedMessage::Preload(MSG_PLAYAWAY_CREATING_TICKET, "Creating PlayAway ticket...");    // 365
	LocalizedMessage::Preload(CONFIRM_QUESTION_STRING, "Confirm?");    // 366
	LocalizedMessage::Preload(
	  MSG_PRESS_START_TO_CONFIRM,
	  "Drag buttons into the grid such that they are positioned like on this slot machine. Press the 'Start Game' button to confirm layout.");    // 367
	LocalizedMessage::Preload(UNUSED_BUTTON_STRING, "Unused");    // 368
	LocalizedMessage::Preload(MANUFACTURER_STRING, "Manufacturer");    // 369
	LocalizedMessage::Preload(RELEASE_DATE_STRING, "Release Date");    // 370
	LocalizedMessage::Preload(RNG_NAME_STRING, "RNG Name");    // 371
	LocalizedMessage::Preload(NO_DATA_STRING, "/");    // 372
	LocalizedMessage::Preload(DESCRIPTION_STRING, "Description");    // 373
	LocalizedMessage::Preload(LOW_VOLATILITY_STRING, "Low");    // 374
	LocalizedMessage::Preload(MEDIUM_VOLATILITY_STRING, "Medium");    // 375
	LocalizedMessage::Preload(HIGH_VOLATILITY_STRING, "High");    // 376
	LocalizedMessage::Preload(VERY_HIGH_VOLATILITY_STRING, "Very High");    // 377
	LocalizedMessage::Preload(RTP_STRING, "RTP");    // 378
	LocalizedMessage::Preload(MSG_PERFORMANCE_WARNING, "The application is running slowly - hardware too weak");    // 379
	LocalizedMessage::Preload(EXPERIMENTAL_FEATURES_STRING, "Experimental Features");    // 380
	LocalizedMessage::Preload(ACTIVE_TIME_STRING, "Active Time");    // 381
	LocalizedMessage::Preload(DISPLAYED_POT_VALUE_STRING, "Displayed Pot Value");    // 382
	LocalizedMessage::Preload(TIMEZONE_STRING, "Timezone");    // 383
	LocalizedMessage::Preload(BET_STRING, "Bet");    // 384
	LocalizedMessage::Preload(WIN_STRING, "Win");    // 385
	LocalizedMessage::Preload(MSG_NOTE_SCREENSHOT_VALUES_INACCURATE,
	                          "Values on screenshots may be inaccurate.\nRefer to game logs and counters for accurate values.");    // 386
	LocalizedMessage::Preload(EDIT_SETTINGS_STRING, "Edit settings");    // 387
	LocalizedMessage::Preload(GAME_ACTIONS_STRING, "Selected Game Actions");    // 388
	LocalizedMessage::Preload(WARNING_EXPIRE_SOON, "Software license will expire soon");    // 389
	LocalizedMessage::Preload(HOST_ACTIONS_STRING, "Host actions");    // 390
	LocalizedMessage::Preload(SUCCESS_STRING, "Success");    // 391
	LocalizedMessage::Preload(ADVANCED_SETTINGS_STRING, "Advanced Settings");    // 392
	LocalizedMessage::Preload(MSG_WAIT_FOR_UPDATE_LOCK, "Waiting for update to start. Please wait...");    // 393
	LocalizedMessage::Preload(GAME_CATEGORY_SLOT_GAMES, "Slot Games");    // 400
	LocalizedMessage::Preload(GAME_CATEGORY_FRUITS, "Fruits");    // 401
	LocalizedMessage::Preload(GAME_CATEGORY_ROULETTE, "Roulette");    // 402
	LocalizedMessage::Preload(GAME_CATEGORY_LIVE, "Live");    // 403
	LocalizedMessage::Preload(GAME_CATEGORY_SPECIAL_BONUS, "Special Bonus");    // 404
	LocalizedMessage::Preload(GAME_CATEGORY_TABLE_GAMES, "Table Games");    // 405
	LocalizedMessage::Preload(GAME_CATEGORY_ANIMALS, "Animals");    // 406
	LocalizedMessage::Preload(GAME_CATEGORY_ASIAN, "Asian");    // 407
	LocalizedMessage::Preload(GAME_CATEGORY_WINTER, "Winter");    // 408
	LocalizedMessage::Preload(GAME_CATEGORY_SUMMER, "Summer");    // 409
	LocalizedMessage::Preload(GAME_CATEGORY_CARDS, "Cards");
	LocalizedMessage::Preload(CONFIGURE_BUTTONS_STRING, "Configure Buttons");    // 503
	LocalizedMessage::Preload(MSG_ONLINE_SYSTEM_NEEDS_TRANSACTION, "Online system needs to execute a transaction.");    // 510
	LocalizedMessage::Preload(MSG_ONLINE_SYSTEM_NEEDS_TRANSACTION_PLEASE_FINISH_GAME,
	                          "Online system needs to execute a transaction, please finish the current game.");    // 511
	LocalizedMessage::Preload(CLOSE_STRING, "Close");    // 600
	LocalizedMessage::Preload(CONTINUE_STRING, "Continue");    // 607
	LocalizedMessage::Preload(EXIT_STRING, "Exit");    // 608
	LocalizedMessage::Preload(OK_STRING, "OK");    // 609
	LocalizedMessage::Preload(CLEAR_STRING, "Clear");    // 610
	LocalizedMessage::Preload(REFRESH_STRING, "Refresh");    // 611
	LocalizedMessage::Preload(CANCEL_STRING, "Cancel");    // 612
	LocalizedMessage::Preload(BACKSPACE_SIGN_STRING, "<-");    // 613
	LocalizedMessage::Preload(SHOW_COUNTERS_STRING, "Show counters");    // 614
	LocalizedMessage::Preload(SHOW_LOGS_STRING, "Show logs");    // 615
	LocalizedMessage::Preload(CREDITS_STRING, "Credits");    // 616
	LocalizedMessage::Preload(MONEY_STRING, "Money");    // 617
	LocalizedMessage::Preload(GAMES_SETUP_STRING, "Games Setup");    // 622
	LocalizedMessage::Preload(KEY_PROGRAM_STRING, "Key program");    // 626
	LocalizedMessage::Preload(EXPIRED_STRING, "Expired");    // 627
	LocalizedMessage::Preload(DELETE_KEY_STRING, "Delete key");    // 632
	LocalizedMessage::Preload(SAVE_STRING, "Save");    // 635
	LocalizedMessage::Preload(SETUP_STRING, "Setup");    // 636
	LocalizedMessage::Preload(PAYIN_STRING, "PayIN");    // 637
	LocalizedMessage::Preload(GAME_HISTORY_STRING, "Game History");    // 638
	LocalizedMessage::Preload(PAYOUT_STRING, "PayOUT");    // 639
	LocalizedMessage::Preload(POWER_MENU_STRING, "Power");    // 645
	LocalizedMessage::Preload(HIDE_COUNTERS_STRING, "Hide counters");    // 646
	LocalizedMessage::Preload(HIDE_LOGS_STRING, "Hide logs");    // 647
	LocalizedMessage::Preload(REPLAY_STRING, "Replay");    // 648
	LocalizedMessage::Preload(INSERT_KEY_STRING, "Insert key");    // 649
	LocalizedMessage::Preload(PERIPHERAL_SETUP_STRING, "Peripheral Setup");    // 662
	LocalizedMessage::Preload(NETWORK_RESTART_STRING, "NET REST.");    // 665
	LocalizedMessage::Preload(NETWORK_INFO_STRING, "NET INFO");    // 667
	LocalizedMessage::Preload(PING_STRING, "PING");    // 668
	LocalizedMessage::Preload(MONEY_COLLECT_STRING, "Money Collect");    // 669
	LocalizedMessage::Preload(SAS_SETUP_STRING, "SAS Setup");    // 670
	LocalizedMessage::Preload(ASSIST_STRING, "Assist.");    // 671
	LocalizedMessage::Preload(TOUCH_CALIBRATION_STRING, "Touch Calibration");    // 672
	LocalizedMessage::Preload(JACKPOT_SETUP_STRING, "Jackpot Setup");    // 674
	LocalizedMessage::Preload(JACKPOT_RESET_STRING, "Reset Jackpot");    // 675
	LocalizedMessage::Preload(LOCK_STRING, "Lock");    // 676
	LocalizedMessage::Preload(TICKETS_AND_BILL_STRING, "Ticket & Bill");    // 679
	LocalizedMessage::Preload(ATTENDANT_STRING, "Attendant");    // 680
	LocalizedMessage::Preload(SAS_STRING, "SAS");    // 681
	LocalizedMessage::Preload(GENERAL_STRING, "General");    // 682
	LocalizedMessage::Preload(PARAMS_STRING, "Params");    // 683
	LocalizedMessage::Preload(PAY_IN_ALARM_STRING, "Pay in notification");    // 703
	LocalizedMessage::Preload(PAY_OUT_ALARM_STRING, "Pay out notification");    // 704
	LocalizedMessage::Preload(YOU_MUST_RESTART_ALL_PLAYBOARDS_STRING, "You must RESTART ALL GAMING MACHINES");    // 706
	LocalizedMessage::Preload(FOR_CHANGES_TO_TAKE_EFFECT_STRING, "for the changes to take effect.");    // 707
	LocalizedMessage::Preload(CREDIT_STRING, "Credit");    // 709
	LocalizedMessage::Preload(ALL_STRING, "ALL");    // 710
	LocalizedMessage::Preload(RESET_STRING, "Reset");    // 714
	LocalizedMessage::Preload(CASH_OUT_STRING, "Cash Out");    // 717
	LocalizedMessage::Preload(COUNTER_LAST_RESET_STRING, "Last reset at:");    // 719
	LocalizedMessage::Preload(CONFIRM_PAYOUT_STRING, "Confirm PayOUT");    // 721
	LocalizedMessage::Preload(CASHABLE_CREDIT_STRING, "Cashable credit");    // 722
	LocalizedMessage::Preload(CASHABLE_MONEY_STRING, "Cashable money");    // 723
	LocalizedMessage::Preload(PROMOTIONAL_CREDIT_STRING, "Restricted credit");    // 724
	LocalizedMessage::Preload(SOFTWARE_LICENCE_VALID_UNTIL_STRING, "Software license valid until:");    // 726
	LocalizedMessage::Preload(MSG_YOU_CAN_REQUEST_UNLOCK_BY_REPORTING_CHALLANGE,
	                          "You can request the unlock code by reporting the CHALLENGE CODE to your vendor");    // 735
	LocalizedMessage::Preload(PLAEASE_ENTER_SOFTWARE_UNLOCK_CODE_STRING, "Please enter software unlock code");    // 736
	LocalizedMessage::Preload(CHALLENGE_CODE_STRING, "CHALLENGE CODE");    // 737
	LocalizedMessage::Preload(PARAMETERS_SAVED_STRING, "PARAMETERS SAVED!");    // 738
	LocalizedMessage::Preload(NEVER_STRING, "Never");    // 743
	LocalizedMessage::Preload(KEY_STRING, "Key");    // 746
	LocalizedMessage::Preload(TEXT_LANGUAGE_STRING, "Text lang");    // 751
	LocalizedMessage::Preload(AUDIO_VOLUME_STRING, "Audio volume");    // 756
	LocalizedMessage::Preload(CONVERSION_STRING, "Accounting denomination");    // 757
	LocalizedMessage::Preload(UNLOCK_STRING, "Unlock");    // 759
	LocalizedMessage::Preload(YOU_PAID_IN_X_STRING, "You paid in");    // 763
	LocalizedMessage::Preload(YOU_PAID_OUT_X_STRING, "You paid out");    // 764
	LocalizedMessage::Preload(MSG_MALFUNCTION_VOIDS_ALL_PLAYS_AND_PAYS, "Malfunction voids all pays and plays.");    // 765
	LocalizedMessage::Preload(UPTIME_STRING, "UPTIME");    // 771
	LocalizedMessage::Preload(NO_KEY_PROGRAMMED_STRING, "WARNING: NO KEYS PROGRAMMED — DEFAULT FULL ACCESS!!!");    // 772
	LocalizedMessage::Preload(JURISDICTION_STRING, "Jurisdiction");    // 773
	LocalizedMessage::Preload(NOT_VALID_STRING, "Not valid");    // 774
	LocalizedMessage::Preload(ERROR_STRING, "ERROR");    // 775
	LocalizedMessage::Preload(STATUS_OK_STRING, "Status OK");    // 776
	LocalizedMessage::Preload(NO_KEY_PRESENT_STRING, "-No key present-");    // 777
	LocalizedMessage::Preload(PLEASE_WAIT_STRING, "Please wait...");    // 787
	LocalizedMessage::Preload(PLEASE_CALL_ATTENDANT_STRING, "Please call attendant");    // 788
	LocalizedMessage::Preload(ATTENDANT_PAYIN_MAXIMUM_STRING, "Key Payin Max");    // 795
	LocalizedMessage::Preload(ALLOW_DENOMI_CHANGE_WHILE_GAME_STRING, "Allow denomination change in game");    // 796
	LocalizedMessage::Preload(DENOMINATION_STRING, "Denomination");    // 797
	LocalizedMessage::Preload(SERIAL_NUMBER_STRING, "Serial Number");    // 799
	LocalizedMessage::Preload(TIME_STRING, "Time");    // 801
	LocalizedMessage::Preload(TRANSACTION_BY_STRING, "Tr. made by");    // 803
	LocalizedMessage::Preload(TRANSACTION_TYPE_STRING, "Transaction type");    // 804
	LocalizedMessage::Preload(VALUE_STRING, "Value");    // 806
	LocalizedMessage::Preload(CLIENT_STRING, "Client");    // 807
	LocalizedMessage::Preload(MESSAGE_STRING, "Message");    // 808
	LocalizedMessage::Preload(MONEY_VALUE_STRING, "Money value");    // 814
	LocalizedMessage::Preload(CURRENCY_STRING, "Currency");    // 815
	LocalizedMessage::Preload(ASSIGNED_CREDIT_STRING, "Assigned credit");    // 816
	LocalizedMessage::Preload(PROFIT_STRING, "Profit");    // 821
	LocalizedMessage::Preload(PARAM_NOT_SAVED_STRING, "Parameters were not saved");    // 829
	LocalizedMessage::Preload(PROMOTIONAL_NONRESTRICTED_CREDIT_STRING, "Non-restricted credit");    // 830
	LocalizedMessage::Preload(PROMOTIONAL_NONRESTRICTED_MONEY_STRING, "Non-restricted money");    // 831
	LocalizedMessage::Preload(ON_STRING, "ON");    // 832
	LocalizedMessage::Preload(ALWAYS_STRING, "always");    // 833
	LocalizedMessage::Preload(FALLS_EVERY_DAY_STRING, "falls every day\nfrom {0} to {1}");    // 834
	LocalizedMessage::Preload(INPUT_CALIBRATION_PERMISSION, "Input Calibration");    // 838
	LocalizedMessage::Preload(CURRENCY_SYMBOL_STRING, "Currency Symbol");    // 839
	LocalizedMessage::Preload(PLAYAWAY_ENABLED_STRING, "PlayAway Enabled");    // 840
	LocalizedMessage::Preload(PLAYAWAY_INFO_STRING, "PlayAway Info");    // 841
	LocalizedMessage::Preload(PLYAWAY_INFO_ACTIONS_STRING, "PlayAway Information and Operator Actions");    // 842
	LocalizedMessage::Preload(UNFINISHED_GAME_STRING, "Unfinished Game");    // 843
	LocalizedMessage::Preload(MSG_UNFINISHED_GAME_INFO,
	                          "A game round was started and could not be completed.\nYou can void the game round and return the bet to the player.");    // 844
	LocalizedMessage::Preload(SHOW_OPTIONS_STRING, "Show Options");    // 845
	LocalizedMessage::Preload(TOGGLE_ENABLED_STRING, "Toggle Enabled");    // 846
	LocalizedMessage::Preload(SELECT_THIS_BUTTON_STRING, "Select This Button");    // 847
	LocalizedMessage::Preload(START_GAME_STRING, "Start Game");    // 848
	LocalizedMessage::Preload(CALL_ATTENDANT_STRING, "Call Attendant");    // 849
	LocalizedMessage::Preload(PAYOUT_BUTTON_STRING, "Payout");    // 850
	LocalizedMessage::Preload(SELECT_NEXT_GAME_STRING, "Select Next Game");    // 851
	LocalizedMessage::Preload(BACKSPACE_STRING, "Backspace");    // 853
	LocalizedMessage::Preload(PRESS_SELECTED_KEY_STRING, "Press Selected Key");    // 854
	LocalizedMessage::Preload(RETURN_TO_MENU_STRING, "Back to Menu");    // 855
	LocalizedMessage::Preload(JACKPOT_TOTAL_RTP_STRING, "Total jackpot RTP");    // 856
	LocalizedMessage::Preload(TABLE_CLOSED_STRING, "Live table is closed");    // 857
	LocalizedMessage::Preload(SHOW_GAME_INFO_STRING, "Show Game Info");    // 858
	LocalizedMessage::Preload(ATTENTION_STRING, "Attention");    // 859
	LocalizedMessage::Preload(HANDPAY_TRIGGERED_BY_STRING, "This handpay way triggered by a");    // 860
	LocalizedMessage::Preload(MSG_EXCEEDED_MAX_BALANCE, "exceeded the maximum allowed balance. The amount over the limit must be paid out immediately.");    // 861
	LocalizedMessage::Preload(MSG_EXCEEDED_MAX_WIN, "exceeded the max win amount. The win must be paid out immediately.");    // 862
	LocalizedMessage::Preload(MSG_JACKPOT_REQUIRE_HANDPAY, "requires that the won amount is immediately paid out.");    // 863
	LocalizedMessage::Preload(STATION_COMMANDS_STRING, "Station Commands");    // 864
	LocalizedMessage::Preload(THIS_KEY_IS_MISSING_PERMISSIONS_STRING, "Not permitted for the current admin key");    // 865
	LocalizedMessage::Preload(GAMEROUND_ID_STRING, "Game round ID");    // 866
	LocalizedMessage::Preload(RNG_SETUP_STRING, "RNG Setup");    // 867
	LocalizedMessage::Preload(ID_STRING, "ID");    // 868
	LocalizedMessage::Preload(NAME_STRING, "Name");    // 870
	LocalizedMessage::Preload(CHECKSUM_STRING, "Checksum");    // 871
	LocalizedMessage::Preload(INTEGRITY_CHECK_DISABLED_STRING, "Integrity check disabled!");    // 872
	LocalizedMessage::Preload(FAILED_STRING, "FAILED");    // 873
	LocalizedMessage::Preload(SHOWING_RESULTS_FOR_STRING, "Showing results for");    // 874
	LocalizedMessage::Preload(ONLY_STRING, "Only");    // 875
	LocalizedMessage::Preload(NO_FILTERS_STRING, "All (no filter)");    // 876
	LocalizedMessage::Preload(THIS_STRING, "this");    // 877
	LocalizedMessage::Preload(JACKPOT_ID_STRING, "Jackpot ID");    // 878
	LocalizedMessage::Preload(WIN_TIMESTAMP_STRING, "Win Timestamp");    // 879
	LocalizedMessage::Preload(WINNING_CLIENT_STRING, "Winning Clients");    // 880
	LocalizedMessage::Preload(PULSE_STRING, "pulse");    // 881
	LocalizedMessage::Preload(SINGLE_STRING, "SINGLE");    // 882
	LocalizedMessage::Preload(MULTI_STRING, "MULTI");    // 883
	LocalizedMessage::Preload(OFF_STRING, "OFF");    // 884
	LocalizedMessage::Preload(BASE_STRING, "BASE");    // 885
	LocalizedMessage::Preload(SYSTEM_STRING, "SYSTEM");    // 886
	LocalizedMessage::Preload(DISABLED_STRING, "DISABLED");    // 887
	LocalizedMessage::Preload(DIRECT_PAYOUT_STRING, "DIRECT PAYOUT");    // 888
	LocalizedMessage::Preload(CHANGABLE_STRING, "changeable");    // 889
	LocalizedMessage::Preload(NONCHANGEABLE_STRING, "non changeable");    // 890
	LocalizedMessage::Preload(RAM_CLEAR_STRING, "RAM CLEAR");    // 891
	LocalizedMessage::Preload(NO_LIMIT_STRING, "NO_LIMIT");    // 892
	LocalizedMessage::Preload(MUTE_STRING, "MUTE");    // 893
	LocalizedMessage::Preload(BILL_CHANNEL_MUST_BE_DIVIDABLE_STRING, "Bill Channel {0} must be dividable by");    // 897
	LocalizedMessage::Preload(MAX_RTP_STRING, "Max RTP");    // 898
	LocalizedMessage::Preload(MSG_RTP_INFO1, "All games with final RTP above 'Max RTP' will not be available to the player.");    // 899
	LocalizedMessage::Preload(MSG_RTP_INFO2, "Final RTP is calculated as 'Game RTP' + 'Jackpot RTP'!");    // 900
	LocalizedMessage::Preload(LIFETIME_COUNTERS_STRING, "Lifetime Counters");    // 901
	LocalizedMessage::Preload(PERIODIC_COUNTERS_STRING, "Periodic Counters");    // 902
	LocalizedMessage::Preload(CREDIT_LOG_STRING, "Credit log");    // 903
	LocalizedMessage::Preload(PLAYBOARD_MONEY_LOG_STRING, "Money log");    // 904
	LocalizedMessage::Preload(PARAMETER_CHANGES_LOG_STRING, "Parameter changes log");    // 905
	LocalizedMessage::Preload(EVENT_LOG_STRING, "Event log");    // 908
	LocalizedMessage::Preload(SELECT_SETUP_MENU_STRING, "Select a setup category");    // 909
	LocalizedMessage::Preload(EXPIRE_MANAGER_STRING, "Expired code manipulation");    // 917
	LocalizedMessage::Preload(GAME_CONFIG_MANAGER_STRING, "Games configuration");    // 918
	LocalizedMessage::Preload(KEY_PROGRAM_UTILITY_STRING, "Key programing utility");    // 919
	LocalizedMessage::Preload(GAMING_MACHINE_SETUP_STRING, "Gaming Machine Setup");    // 921
	LocalizedMessage::Preload(PLAYBOARD_SETUP_STRING, "Playboard Setup");    // 922
	LocalizedMessage::Preload(PAYIN_MANAGER_STRING, "PayIN manager");    // 925
	LocalizedMessage::Preload(PAYOUT_MANAGER_STRING, "PayOUT Manager");    // 926
	LocalizedMessage::Preload(ROULETTE_POWER_MENU_STRING, "Power menu");    // 927
	LocalizedMessage::Preload(SAS_CONFIG_STRING, "SAS Configuration");    // 929
	LocalizedMessage::Preload(SAS_LOG_STRING, "SAS log");    // 930
	LocalizedMessage::Preload(TICKET_LOG_STRING, "Ticket log");    // 931
	LocalizedMessage::Preload(OPERATOR_LOG_STRING, "Operator activity log");    // 932
	LocalizedMessage::Preload(LOGS_STRING, "Logs");    // 933
	LocalizedMessage::Preload(COINS_SETUP_STRING, "Coins Setup");    // 934
	LocalizedMessage::Preload(HOPER_CONTROL_PANEL_STRING, "Hopper Control Panel");    // 936
	LocalizedMessage::Preload(JACKPOT_HISTORY_LOG_STRING, "Jackpot History Log");    // 942
	LocalizedMessage::Preload(TOTALS_DETAIL_LOG_STRING, "Totals log");    // 943
	LocalizedMessage::Preload(TICKET_PRINTER_STRING, "Ticket Printer");    // 945
	LocalizedMessage::Preload(OTHER_STATIONS_PANEL_STRING, "Available platforms");    // 946
	LocalizedMessage::Preload(BUTTON_CONFIG_PANEL_STRING, "Hardware button configuration");    // 947
	LocalizedMessage::Preload(LIMITS_STRING, "Limits");    // 948
	LocalizedMessage::Preload(UNLIMITED_STRING, "Unlimited");    // 949
	LocalizedMessage::Preload(INVALID_PLAYAWAY_RECEIPT_STRING, "Invalid web transfer receipt");    // 950
	LocalizedMessage::Preload(NOTHING_WAS_PAID_IN_STRING, "Nothing was paid in");    // 951
	LocalizedMessage::Preload(MSG_PLAYER_HAS_NO_CREDITS_LEFT, "The player on this ticket had no credits left");    // 952
	LocalizedMessage::Preload(RNG_CONFIG_MANAGER_STRING, "RNG configuration");    // 953
	LocalizedMessage::Preload(USED_BY_GAMES_STRING, "Used by Games");    // 955
	LocalizedMessage::Preload(PLAYAWAY_CASHIN_OK_STRING, "Transfer token successfully cashed-in");    // 957
	LocalizedMessage::Preload(INVALID_CODE_STRING, "Invalid code!");    // 958
	LocalizedMessage::Preload(GAME_NAME_STRING, "Game Name");    // 1020
	LocalizedMessage::Preload(CONFIGURATION_STRING, "Configuration");    // 1021
	LocalizedMessage::Preload(DISPLAY_TYPE_STRING, "Display Type");    // 1024
	LocalizedMessage::Preload(PLAYAWAY_TOKEN_SOURCE_COLUMN_STRING, "Token Source");    // 1026
	LocalizedMessage::Preload(PLAYAWAY_CREATED_PLAYER_COLUMN_STRING, "Created Player");    // 1027
	LocalizedMessage::Preload(PLAYAWAY_INITIAL_CREDITS_COLUMN_STRING, "Initial Balance");    // 1028
	LocalizedMessage::Preload(PLAYAWAY_CURRENT_CREDITS_COLUMN_STRING, "Current Balance");    // 1029
	LocalizedMessage::Preload(PLAYAWAY_CREATION_TIME_COLUMN_STRING, "Creation Time");    // 1030
	LocalizedMessage::Preload(PLAYAWAY_CASH_IN_COLUMN_STRING, "Cash-In");    // 1031
	LocalizedMessage::Preload(PLAYAWAY_MANUAL_TOKEN_REDEMPTION_STRING, "Manual token redemption");    // 1032
	LocalizedMessage::Preload(MAX_POSSIBLE_WIN_STRING, "Max Possible Win");    // 1034
	LocalizedMessage::Preload(GAME_STRING, "Game");    // 1100
	LocalizedMessage::Preload(TOTAL_IN_STRING, "Total Drop");    // 1101
	LocalizedMessage::Preload(TOTAL_OUT_STRING, "Total Out");    // 1102
	LocalizedMessage::Preload(TOTAL_BETS_STRING, "Total Bets");    // 1103
	LocalizedMessage::Preload(TOTAL_WINS_STRING, "Total Wins");    // 1104
	LocalizedMessage::Preload(TOTAL_PROFIT_STRING, "Total Profit");    // 1105
	LocalizedMessage::Preload(PAYBACK_STRING, "Payback");    // 1106
	LocalizedMessage::Preload(ATTENDANT_IN_STRING, "Attendant In");    // 1107
	LocalizedMessage::Preload(ATTENDANT_OUT_STRING, "Attendant Out");    // 1108
	LocalizedMessage::Preload(HANDPAY_STRING, "Handpay");    // 1109
	LocalizedMessage::Preload(PLAYER_INITIATED_HANDPAY_STRING, "Player Initiated Handpay");    // 1110
	LocalizedMessage::Preload(TOTAL_RESTRICTED_BETS_STRING, "Total Restricted Bets");    // 1111
	LocalizedMessage::Preload(TOTAL_NONRESTRICTED_BETS_STRING, "Total NonRestricted Bets");    // 1112
	LocalizedMessage::Preload(GAMES_STRING, "Games");    // 1113
	LocalizedMessage::Preload(PLAYED_STRING, "Played");    // 1114
	LocalizedMessage::Preload(GAMES_WON_STRING, "Games Won");    // 1115
	LocalizedMessage::Preload(GAMES_LOST_STRING, "Games Lost");    // 1116
	LocalizedMessage::Preload(GAMES_SINCE_STRING, "Games Since");    // 1117
	LocalizedMessage::Preload(POWER_UP_STRING, "Power Up");    // 1118
	LocalizedMessage::Preload(DOOR_CLOSURE_STRING, "Door Closure");    // 1119
	LocalizedMessage::Preload(GAMBLE_TOTAL_STRING, "Gamble Total");    // 1120
	LocalizedMessage::Preload(GAMBLE_GAMES_STRING, "Gamble Games");    // 1121
	LocalizedMessage::Preload(COUNTER_STRING, "Counter");    // 1122
	LocalizedMessage::Preload(OPEN_COUNTER_STRING, "Open Counter");    // 1123
	LocalizedMessage::Preload(STACKER_REMOVE_STRING, "Stacker Remove");    // 1124
	LocalizedMessage::Preload(MACHINE_PAID_STRING, "Machine Paid");    // 1125
	LocalizedMessage::Preload(ATTENDANT_PAID_STRING, "Attendant Paid");    // 1126
	LocalizedMessage::Preload(PAYTABLE_WIN_STRING, "Paytable Win");    // 1127
	LocalizedMessage::Preload(PROGRESSIVE_WIN_STRING, "Progressive Win");    // 1128
	LocalizedMessage::Preload(EXTERNAL_BONUS_WIN_STRING, "External Bonus Win");    // 1129
	LocalizedMessage::Preload(COIN_IN_STRING, "Coin In");    // 1130
	LocalizedMessage::Preload(COIN_OUT_STRING, "Coin Out");    // 1131
	LocalizedMessage::Preload(BILL_IN_STRING, "Bill In");    // 1132
	LocalizedMessage::Preload(BILL_OUT_STRING, "Bill Out");    // 1133
	LocalizedMessage::Preload(STACKER_TOTAL_STRING, "Stacker Total");    // 1134
	LocalizedMessage::Preload(STACKER_QUANTITY_STRING, "Stacker Quantity");    // 1135
	LocalizedMessage::Preload(ELECTRONIC_STRING, "Electronic");    // 1136
	LocalizedMessage::Preload(ELECTRONIC_IN_STRING, "Electronic In");    // 1137
	LocalizedMessage::Preload(ELECTRONIC_OUT_STRING, "Electronic Out");    // 1138
	LocalizedMessage::Preload(CASHABLE_IN_STRING, "Cashable In");    // 1139
	LocalizedMessage::Preload(CASHABLE_OUT_STRING, "Cashable Out");    // 1140
	LocalizedMessage::Preload(RESTRICTED_IN_STRING, "Restricted In");    // 1141
	LocalizedMessage::Preload(RESTRICTED_OUT_STRING, "Restricted Out");    // 1142
	LocalizedMessage::Preload(NONRESTRICTED_IN_STRING, "NonRestricted In");    // 1143
	LocalizedMessage::Preload(NONRESTRICTED_OUT_STRING, "NonRestricted Out");    // 1144
	LocalizedMessage::Preload(TICKET_STRING, "Ticket");    // 1145
	LocalizedMessage::Preload(TICKET_IN_STRING, "Ticket In");    // 1146
	LocalizedMessage::Preload(TICKET_OUT_STRING, "Ticket Out");    // 1147
	LocalizedMessage::Preload(QUANTITY_STRING, "Quantity");    // 1148
	LocalizedMessage::Preload(TOTALS_STRING, "TOTALS");    // 1149
	LocalizedMessage::Preload(NO_BUTTON_STRING, "I DON'T HAVE THIS BUTTON");    // 1150
	LocalizedMessage::Preload(NO_EXECUTABLE_WERE_STARTED_STRING, "No executables were started");    // 1151
	LocalizedMessage::Preload(GAME_COULD_NOT_BE_STARTED_STRING, "Game could not be started on the server");    // 1152
	LocalizedMessage::Preload(UNKNOWN_PROBLEM_STRING, "Unknown problem occurred");    // 1153
	LocalizedMessage::Preload(JACKPOT_WON_STRING, "Jackpot won!");    // 1154
	LocalizedMessage::Preload(EUROPEAN_STRING, "European");    // 1155
	LocalizedMessage::Preload(BILL_ACCEPTOR_MESSAGE_STRING, "Bill acceptor message:");    // 1156
	LocalizedMessage::Preload(TICKET_PRINTER_MESSAGE_STRING, "Ticket printer message:");    // 1157
	LocalizedMessage::Preload(COIN_ACCEPTOR_MESSAGE_STRING, "Coin acceptor message:");    // 1158
	LocalizedMessage::Preload(CANNOT_PAYOUT_ZERO_CREDITS_STRING, "Cannot payout zero credits!");    // 1159
	LocalizedMessage::Preload(MSG_UNFINSHED_GAME_REMOVE_KEY, "Please remove the key and close the admin menu to resume unfinished game");    // 1160
	LocalizedMessage::Preload(UNKNOWN_AMOUNT_STRING, "Unknown Amount");    // 1161
	LocalizedMessage::Preload(YES_STRING, "YES");    // 1164
	LocalizedMessage::Preload(NO_STRING, "NO");    // 1165
	LocalizedMessage::Preload(NO_LEVEL_SELECTED_STRING, "No level selected!");    // 1166
	LocalizedMessage::Preload(POT_VALUES_INVALID_NUMBER_STRING, "Pot values are not set to valid numbers!");    // 1167
	LocalizedMessage::Preload(POT_VALUES_MAX_BET_ERROR_STRING, "Pot value must be less or equal than Max Pot!");    // 1168
	LocalizedMessage::Preload(MAX_POT_UNLIMITED_ERROR_STRING, "Max pot cannot be unlimited!");    // 1169
	LocalizedMessage::Preload(MAX_POT_HALF_MIN_POT_ERROR_STRING, "Max pot must be at least 50% larger than the min pot!");    // 1170
	LocalizedMessage::Preload(NO_CHANGES_SAVED_STRING, "No changes to be saved!");    // 1171
	LocalizedMessage::Preload(LEVEL_NOT_SET_STRING, "Jackpot level value was not set");    // 1172
	LocalizedMessage::Preload(LEVEL_NOT_ENABLED_STRING, "Jackpot level could not be enabled/disabled");    // 1173
	LocalizedMessage::Preload(ONLINE_SYSTEM_MESSAGE_STRING, "Online system message:");    // 1174
	LocalizedMessage::Preload(BUTTONS_CONFIG_CLEARED_STRING, "Button configuration cleared");    // 1175
	LocalizedMessage::Preload(BUTTONS_RESET_STRING, "All hardware buttons were reset.");    // 1176
	LocalizedMessage::Preload(ERROR_RESETING_COUNTERS_STRING, "Error resetting counters");    // 1177
	LocalizedMessage::Preload(THANK_YOU_STRING, "Thank you.");    // 1178
	LocalizedMessage::Preload(GAME_SYNC_INFO_STRING, "Remember to trigger a game sync if required!");    // 1179
	LocalizedMessage::Preload(LINK_OK_STRING, "Link ok.");    // 1180
	LocalizedMessage::Preload(LINK_ERROR_STRING, "No Link. Check cable!");    // 1181
	LocalizedMessage::Preload(HOST_OK_STRING, "Host {0} response ok.");    // 1182
	LocalizedMessage::Preload(HOST_NOT_REACHABLE_STRING, "Host {0} is not reachable.");    // 1183
	LocalizedMessage::Preload(KEY_PROGRAMMED_STRING, "PROGRAMMED!");    // 1184
	LocalizedMessage::Preload(KEY_ERROR_PROGRAMMING_STRING, "ERROR PROGRAMMING!!!");    // 1185
	LocalizedMessage::Preload(FORBIDDEN_STRING, "FORBIDDEN");    // 1186
	LocalizedMessage::Preload(MASTER_KEY_PERMISSION_ONE_INFO_STRING, "You need to have MASTER KEY permission to delete this key.");    // 1187
	LocalizedMessage::Preload(MASTER_KEY_PERMISSION_ALL_INFO_STRING, "You need to have MASTER KEY permission to delete all keys.");    // 1188
	LocalizedMessage::Preload(DELETED_STRING, "Deleted");    // 1189
	LocalizedMessage::Preload(ALL_KEYS_DELETED_STRING, "All keys deleted!");    // 1190
	LocalizedMessage::Preload(INFORMATION_STRING, "INFORMATION");    // 1191
	LocalizedMessage::Preload(WARNING_STRING, "WARNING");    // 1192
	LocalizedMessage::Preload(INSERT_ONE_KEY_INFO_STRING, "Please insert at least one key, to exit from this menu.");    // 1193
	LocalizedMessage::Preload(INSERT_ONE_KEY_INFO2_STRING, "You cannot leave KeyProgram menu until at least one key has permissions for");    // 1194
	LocalizedMessage::Preload(KEY_SAVED_STRING, "SAVED!");    // 1195
	LocalizedMessage::Preload(KEY_ERROR_SAVING_STRING, "ERROR SAVING!");    // 1196
	LocalizedMessage::Preload(STATION_SELECT_STRING, "Please select Station with upper buttons.");    // 1197
	LocalizedMessage::Preload(COMMAND_NOT_EXECUTED_STRING, "Command NOT executed.");    // 1198
	LocalizedMessage::Preload(USB_LOAD_STRING, "USB Load");    // 1199
	LocalizedMessage::Preload(LOAD_QUESTION_STRING, "Load?");    // 1200
	LocalizedMessage::Preload(LOADING_STRING, "Loading...");    // 1201
	LocalizedMessage::Preload(PRINTER_READY_STRING, "Printer ready.");    // 1202
	LocalizedMessage::Preload(PRINTER_NOT_READY_STRING, "Printer is not ready!");    // 1203
	LocalizedMessage::Preload(NO_LAST_TICKET_STRING, "No last ticket to print available!");    // 1204
	LocalizedMessage::Preload(PRINTING_LAST_TICKET_STRING, "Printing LAST ticket");    // 1205
	LocalizedMessage::Preload(PRINTING_TEST_TICKET_STRING, "Printing TEST ticket");    // 1206
	LocalizedMessage::Preload(PRINTER_CONNECTED_PAPPER_LOADED_STRING, "Is printer connected? Is paper loaded?");    // 1207
	LocalizedMessage::Preload(KEY_NOT_HAVE_PERMISSION_STRING, "Inserted key does not have permission for {0}.");    // 1208
	LocalizedMessage::Preload(TAKE_TICKET_STRING, "TAKE TICKET");    // 1209
	LocalizedMessage::Preload(CUPOLA_CONTINUE_UNLOCKED_STRING, "Cupola Continue Unlock");    // 1210
	LocalizedMessage::Preload(POWER_CONTROL_STRING, "Power Control");    // 1211
	LocalizedMessage::Preload(MD5_RECALCULATING_STRING, "Please wait while recalculating MD5!");    // 1212
	LocalizedMessage::Preload(RESTART_AFTER_RAM_CLEAR_STRING, "The machine will restart after RAM CLEAR is done!");    // 1213
	LocalizedMessage::Preload(SAS_DISABLED_STRING, "SAS Disabled");    // 1214
	LocalizedMessage::Preload(SHOW_CONSOLE_STRING, "Show Console");    // 1215
	LocalizedMessage::Preload(HIDE_CONSOLE_STRING, "Hide Console");    // 1216
	LocalizedMessage::Preload(PRINTER_DRIVER_RUNNING_STRING, "Is the driver running?");    // 1217
	LocalizedMessage::Preload(PRINT_QUEUE_CLEARED_STRING, "Print QUEUE cleared.");    // 1218
	LocalizedMessage::Preload(TITO_PARAMETERS_CLEARED_STRING, "TITO Enhanced validation parameters were cleared.");    // 1219
	LocalizedMessage::Preload(PARAMS_FETCH_AUTOMAITCALLY_STRING, "New parameters will be fetched automatically from host.");    // 1220
	LocalizedMessage::Preload(TERMINAL_STRING, "Terminal");    // 1221
	LocalizedMessage::Preload(LATEST_GAME_STRING, "Latest game");    // 1222
	LocalizedMessage::Preload(PREVIOUS_GAME_STRING, "Previous game");    // 1223
	LocalizedMessage::Preload(GAMES_AGO_STRING, "games ago");    // 1224
	LocalizedMessage::Preload(MISSING_GAME_INFO_STRING, "Missing required game information");    // 1225
	LocalizedMessage::Preload(NO_LOGS_IN_DB_STRING, "No logs in DB for game round");    // 1226
	LocalizedMessage::Preload(NO_GAMEROUND_DATA_STRING, "No game round ID in round info data!");    // 1227
	LocalizedMessage::Preload(COUNTERS_FILE_INCOMPATIBLE_STRING, "Counter savefile version incompatible with current software version!");    // 1228
	LocalizedMessage::Preload(YEAR_STRING, "Year");    // 1229
	LocalizedMessage::Preload(PRINT_LAST_STRING, "Print Last");    // 1230
	LocalizedMessage::Preload(PRINT_TEST_STRING, "Print TEST");    // 1231
	LocalizedMessage::Preload(PAYLINES_STRING, "paylines");    // 1232
	LocalizedMessage::Preload(GAMEROUND_INVALIDATED_STRING, "Game round was successfully invalidated!");    // 1233
	LocalizedMessage::Preload(GAMEROUND_NOT_INVALIDATED_STRING, "Game round could not be invalidated!");    // 1234
	LocalizedMessage::Preload(PRESS_THE_BUTTON_STRING, "Press the {0} button");    // 1235
	LocalizedMessage::Preload(GAME_MENU_STRING, "Game Menu");    // 1236
	LocalizedMessage::Preload(SERVICE_STRING, "Service");    // 1237
	LocalizedMessage::Preload(INFO_STRING, "Info");    // 1238
	LocalizedMessage::Preload(AUTOPLAY_STRING, "Autoplay");    // 1239
	LocalizedMessage::Preload(MSG_START_GAME_BUTTON_INFO, "This button will be used to start game rounds (e.g. spin), confirm choices in dialogs, etc.");    // 1240
	LocalizedMessage::Preload(
	  MSG_GAME_MENU_BUTTON_INFO,
	  "This button will be used to go back to the game selection page, cycle between games in the menu, or cancel choices in dialogs.");    // 1241
	LocalizedMessage::Preload(MSG_SERVICE_BUTTON_INFO, "This button will be used to call the attendant and will engage the semaphore.");    // 1242
	LocalizedMessage::Preload(MSG_PAYOUT_BUTTON_INFO, "This button will be used to trigger a payout on the machine.");    // 1243
	LocalizedMessage::Preload(MSG_INFO_BUTTON_INFO,
	                          "This button will open and cycle through info pages in games, and change the denomination while in the game menu.");    // 1244
	LocalizedMessage::Preload(
	  MSG_BET_MINUS_BUTTON_INFO,
	  "This button will be used to decrement the chosen bet amount, or switch to a smaller betting chip. While in the game menu, it will be used to go to the next page of games or navigate the controls when a dialog is open.");    // 1245
	LocalizedMessage::Preload(
	  MSG_BET_PLUS_BUTTON_INFO,
	  "This button will be used to increment the chosen bet amount, or switch to a bigger betting chip. While in the game menu, it will be used to go to the next page of games or navigate the controls when a dialog is open.");    // 1246
	LocalizedMessage::Preload(MSG_BET_MAX_BUTTON_INFO, "This button will select the maximum possible bet or betting chip.");    // 1247
	LocalizedMessage::Preload(MSG_AUTOPLAY_BUTTON_INFO, "This button will try to engage an autoplay feature, or repeat the last bet if that isn't possible.");    // 1248
	LocalizedMessage::Preload(START_CALIBRATION_STRING, "Starting Calibration... (please connect your touchscreen device on {0} or wait 10 seconds to abort)");    // 1249
	LocalizedMessage::Preload(POINT_CALIBRATED_STRING, "Point {0} calibrated. Please stop pressing the point to continue.");    // 1250
	LocalizedMessage::Preload(POINT_STRING, "Point");    // 1251
	LocalizedMessage::Preload(TOUCH_CALIB_POINT_STRING, "Please {0} {1} (or wait 10 seconds to abort).");    // 1252
	LocalizedMessage::Preload(TOUCH_AND_HOLD_STRING, "touch and hold");    // 1253
	LocalizedMessage::Preload(CLICK_AND_HOLD_STRING, "click and hold");    // 1254
	LocalizedMessage::Preload(
	  CALIBRATION_OK_STRING,
	  "Calibration OK. Test your touchscreen and save or discard changes.\nNew settings will be discarded automatically after 10 seconds of inactivity...");    // 1255
	LocalizedMessage::Preload(CALIBRATION_ERROR_STRING, "Calibration ERROR!");    // 1256
	LocalizedMessage::Preload(CALIBRATION_DCLICK_ERROR_STRING, "Calibration ERROR - double click detected!");    // 1257
	LocalizedMessage::Preload(CALIBRATION_MISSCLICK_ERROR_STRING, "Calibration ERROR - missclick detected!");    // 1258
	LocalizedMessage::Preload(CALIBRATION_COMMUNICATION_ERROR_STRING, "Touchscreen Communication error");    // 1259
	LocalizedMessage::Preload(SLOT_EMPTY_STRING, "SLOT {0} EMPTY");    // 1260
	LocalizedMessage::Preload(BILLS_AVAILABLE_TO_PAYOUT_STRING, "Bills available to payout");    // 1261
	LocalizedMessage::Preload(BILLS_PAYOUT_IN_PAYMENT_STRING, "Bills paid out in last payment");    // 1262
	LocalizedMessage::Preload(TICKET_HEAD_UP_STRING, "Ticket Head is UP");    // 1263
	LocalizedMessage::Preload(TICKET_CHASIS_OPEN_STRING, "Ticket Chassis is OPEN");    // 1264
	LocalizedMessage::Preload(TICKET_PAPER_JAM_STRING, "Ticket Paper JAM!");    // 1265
	LocalizedMessage::Preload(TICKET_PAPER_OUT_STRING, "Ticket Paper OUT!");    // 1266
	LocalizedMessage::Preload(TICKET_PAPER_LOW_STRING, "Ticket paper LOW!");    // 1267
	LocalizedMessage::Preload(TICKET_PAPER_NOT_LOADED_STRING, "Ticket Paper not loaded!");    // 1268
	LocalizedMessage::Preload(BILLACCEPTOR_INHIBIT_STRING, "BillAcceptor INHIBIT!");    // 1269
	LocalizedMessage::Preload(BILL_STRING, "Bill");    // 1270
	LocalizedMessage::Preload(AUTOCONFIRM_IN_STRING, "Auto confirm in {0} sec.");    // 1271
	LocalizedMessage::Preload(AUTOCONFIRM_DISABLED_STRING, "Auto confirm disabled because a game is running!");    // 1272
	LocalizedMessage::Preload(INVALID_PIN_STRING, "Invalid PIN, Please try again.");    // 1273
	LocalizedMessage::Preload(PLAYAWAY_COULD_NOT_PRINT_TICKET_STRING, "Could not print PlayAway ticket");    // 1274
	LocalizedMessage::Preload(PLAYAWAY_RETURNING_MONEY_STRING, "Returning money to the machine...");    // 1275
	LocalizedMessage::Preload(PLAYAWAY_RETURN_OK_STRING, "Successfully returned money to machine.");    // 1276
	LocalizedMessage::Preload(PLAYAWAY_RETURN_FAILED_STRING, "Failed returning money to the machine!");    // 1277
	LocalizedMessage::Preload(COULD_NOT_CREATE_TOKEN_STRING, "Could not create transfer token");    // 1278
	LocalizedMessage::Preload(SCAN_ME_STRING, "SCAN ME!");    // 1279
	LocalizedMessage::Preload(HOW_THIS_WORKS_STRING, "HOW THIS WORKS?");    // 1280
	LocalizedMessage::Preload(PLAYAWAY_INSTRUCTION1_STRING, "A new player account is created for you, and funds are transferred to it");    // 1281
	LocalizedMessage::Preload(PLAYAWAY_INSTRUCTION2_STRING, "A receipt is printed which is your proof of account ownership");    // 1282
	LocalizedMessage::Preload(PLAYAWAY_INSTRUCTION3_STRING, "A QR code appears which you scan to enter the online casino");    // 1283
	LocalizedMessage::Preload(PLAYAWAY_INSTRUCTION4_STRING, "You can cash-in your player account at any time by inserting your receipt into this machine");    // 1284
	LocalizedMessage::Preload(PLAYAWAY_INVALID_TOKEN_STRING, "Invalid token");    // 1285
	LocalizedMessage::Preload(TRANSFERED_STRING, "Transferred");    // 1286
	LocalizedMessage::Preload(INSERT_ONE_KEY_INFO3_STRING, "\'{0}\' and \'{1}\'");    // 1287
	LocalizedMessage::Preload(MSG_DISPLAYS_ORIENTED_BADLY,
	                          "Machine displays are not oriented correctly.");    // 1288
	LocalizedMessage::Preload(ENTER_PIN_STRING, "Please Enter PIN");    // 1289
	LocalizedMessage::Preload(ELECTRONIC_MONEY_TRANSFER_STRING, "Electronic money transfer. Please wait.");    // 1290
	LocalizedMessage::Preload(ELECTRONIC_CARD_OUT_STRING, "Electronic Card out");    // 1291
	LocalizedMessage::Preload(PAYOUT_RESTRICTED_CREDITS_DISABLED_STRING, "PAYOUT for Restricted Credit(promotional) is disabled.");    // 1292
	LocalizedMessage::Preload(GAME_DISABLED_BY_ONLINE_SYSTEM_STRING, "Game is disabled by the online system");    // 1293
	LocalizedMessage::Preload(PREVENTED_BY_MAX_BET_STRING, "Prevented by max bet");    // 1294
	LocalizedMessage::Preload(PREVENTED_BY_MAX_WIN_STRING, "Prevented by max win");    // 1295
	LocalizedMessage::Preload(MISSING_ICON_STRING, "Missing icon");    // 1296
	LocalizedMessage::Preload(UNKNOWN_ISSUE_STRING, "Unknown issue");    // 1297
	LocalizedMessage::Preload(BET_TOO_HIGH_STRING, "The minimum bet for this game is higher than the maximum allowed bet on this machine");    // 1298
	LocalizedMessage::Preload(
	  POSSIBLE_WIN_TOO_HIGH_STRING,
	  "The maximum possible win in this game (even with the lowest bet selected) is higher than the maximum allowed win on this machine");    // 1299
	LocalizedMessage::Preload(NO_HOST_CREATED_STRING, "No host was created");    // 1300
	LocalizedMessage::Preload(NO_ICON_IMAGE_FOUND_STRING, "No game image found in configuration with type 'Icon'");    // 1301
	LocalizedMessage::Preload(CANNOT_LOAD_ICON_AT_STRING, "Cannot load icon at {0}");    // 1302
	LocalizedMessage::Preload(MODULE_NOT_READY_STRING, "Module is not Ready");    // 1303
	LocalizedMessage::Preload(GAME_NOT_ADDED_TO_SERVER_STRING, "Game was not added to server");    // 1304
	LocalizedMessage::Preload(GAME_UNKNOWN_RTP_STRING, "Game has unknown RTP");    // 1305
	LocalizedMessage::Preload(GAME_RTP_TOO_HIGH_STRING, "Game RTP too high");    // 1306
	LocalizedMessage::Preload(AVAILABLE_TO_PLAYER_STRING, "Available to the player");    // 1307
	LocalizedMessage::Preload(INTEGRITY_CHECK_FAILED_STRING, "Integrity check failed.");    // 1308
	LocalizedMessage::Preload(GAME_NOT_LICENSED_FOR_MACHINE_STRING, "Game is not licensed for this machine");    // 1309
	LocalizedMessage::Preload(GAME_NOT_AVAILABLE_FOR_JURISDICTION_STRING, "Game is not available for this jurisdiction");    // 1310
	LocalizedMessage::Preload(BED_GAME_CONFIG_STRING, "Bad game configuration");    // 1311
	LocalizedMessage::Preload(GAME_DISABLED_FROM_MENU_STRING, "Configuration is disabled from menu");    // 1312
	LocalizedMessage::Preload(GAME_PACKAGE_ERROR_STRING, "Something is wrong with the package");    // 1313
	LocalizedMessage::Preload(GAME_CONFIG_CORRUPT_STRING, "Configuration is corrupt.");    // 1314
	LocalizedMessage::Preload(INVALID_GAME_STRING, "Invalid game!");    // 1315
	LocalizedMessage::Preload(CONSOLE_PREVENTING_STARTUP_STRING, "Open console preventing startup");    // 1316
	LocalizedMessage::Preload(LOCK_PREVENTING_STARTUP_STRING, "Locked state preventing startup");    // 1317
	LocalizedMessage::Preload(KEY_PREVENTING_STARTUP_STRING, "Cannot start game with key inserted!");    // 1318
	LocalizedMessage::Preload(NOT_CONNECTED_TO_YSERVER_STRING, "Not connected to YServer!");    // 1319
	LocalizedMessage::Preload(ATTENDANT_MENU_IS_OPEN_STRING, "Attendant menu is open");    // 1320
	LocalizedMessage::Preload(WAIT_FOR_PREVIOUS_GAME_CLOSE_STRING, "Wait a moment for the previous game to close");    // 1321
	LocalizedMessage::Preload(PACKAGE_NOT_LOADED_STRING, "Package not loaded");    // 1322
	LocalizedMessage::Preload(INVALID_GAME_CONFIG_STRING, "Invalid game configuration '{0}'");    // 1323
	LocalizedMessage::Preload(NO_GAME_HOST_AVAILABLE_STRING, "No game host available");    // 1324
	LocalizedMessage::Preload(GAME_HOST_INVALID_STRING, "Game host is invalid");    // 1325
	LocalizedMessage::Preload(GAME_HOST_NOT_READY_STRING, "Game host is not ready");    // 1326
	LocalizedMessage::Preload(MISSING_GAMEROUND_INFO_STRING, "Missing game round information!");    // 1327
	LocalizedMessage::Preload(GAME_NOT_FOUND_STRING, "Game not found on machine");    // 1328
	LocalizedMessage::Preload(GAME_NOT_CERTIFIED_FOR_STRING, "This game is not certified for");    // 1329
	LocalizedMessage::Preload(ENABLE_GAME_TO_MAKE_VISIBLE_STRING, "Enable the game to make it visible to players");    // 1330
	LocalizedMessage::Preload(GET_LICENSE_FOR_GAME_STRING, "Get a license for this game by contacting us");    // 1331
	LocalizedMessage::Preload(GAME_ONLY_AVAILABLE_IN_DEMO_STRING, "Game is only available in demo mode");    // 1332
	LocalizedMessage::Preload(CHECKSUM_NOT_MATCH_STRING, "Checksum does not match");    // 1333
	LocalizedMessage::Preload(TRY_DOWNLAD_AND_RESTART_STRING, "Try to download it again and restart");    // 1334
	LocalizedMessage::Preload(GAME_CONFIGURED_INCORRECTLY_STRING, "Game is likely configured incorrectly, try to update game");    // 1335
	LocalizedMessage::Preload(GAME_AND_JACKPOT_RTP_OVER_LIMIT_STRING, "The total jackpot RTP + game RTP is over the configured limit");    // 1336
	LocalizedMessage::Preload(ELECTRONIC_CASH_OUT_STRING, "Electronic Cash out");    // 1337
	LocalizedMessage::Preload(VALUES_ARE_IN_X_WHERE_APPLICABLE_STRING, "values are in {0} where applicable");    // 1338
	LocalizedMessage::Preload(PAGE_STRING, "Page");    // 1339
	LocalizedMessage::Preload(COUNTING_FROM_STRING, "Counting from");    // 1340
	LocalizedMessage::Preload(MISSING_GAME_CONTENT_STRING, "Missing game content folder");    // 1341
	LocalizedMessage::Preload(UPDATING_STRING, "Updating...");    // 1342
	LocalizedMessage::Preload(DOWNLOADING_STRING, "Downloading...");    // 1343
	LocalizedMessage::Preload(DELETING_STRING, "Deleting...");    // 1344
	LocalizedMessage::Preload(MOUNTING_GAME_PACKAGE_STRING, "Mounting game package...");    // 1345
	LocalizedMessage::Preload(LOADING_GAME_CONFIGURATION_STRING, "Loading game configurations...");    // 1346
	LocalizedMessage::Preload(VERIFYING_GAME_INSTALLATION_STRING, "Verifying game installation...");    // 1347
	LocalizedMessage::Preload(SAVING_CHANGEGS_STRING, "Saving changes...");    // 1348
	LocalizedMessage::Preload(VERIFYING_GAME_PACKAGES_STRING, "Verifying game packages...");    // 1349
	LocalizedMessage::Preload(CREATING_GAME_SERVICES_STRING, "Creating required game services...");    // 1350
	LocalizedMessage::Preload(SYNCHRONIZING_GAME_CONFIGURATIONS_STRING, "Synchronizing {0} game configurations with server...");    // 1351
	LocalizedMessage::Preload(UPDATE_COMPLETED_STRING, "Update completed!");    // 1352
	LocalizedMessage::Preload(LOADING_GRAPHICS_STRING, "Loading graphics");    // 1353
	LocalizedMessage::Preload(VERIFYING_INSTALLATION_STRING, "Verifying installation");    // 1354
	LocalizedMessage::Preload(LOADING_GAMES_STRING, "Loading games");    // 1355
	LocalizedMessage::Preload(UPDATING_GAMSES_STRING, "Updating packages");    // 1356
	LocalizedMessage::Preload(INITIALIZING_GAMING_STATION_STRING, "Initializing gaming station");    // 1357
	LocalizedMessage::Preload(CREATING_GAMES_STRING, "Creating games");    // 1358
	LocalizedMessage::Preload(CONNECTING_TO_YSERVER_STRING, "Connecting to YServer");    // 1359
	LocalizedMessage::Preload(BOOTING_GAMING_STATION_STRING, "Booting gaming station");    // 1360
	LocalizedMessage::Preload(LOADING_GAME_ASSETS_STRING, "Loading game assets");    // 1361
	LocalizedMessage::Preload(RESTART_TERMINAL_STRING, "Restart Station");    // 1362
	LocalizedMessage::Preload(RESTART_ALL_TERMINALS_STRING, "Restart All Terminals");    // 1363
	LocalizedMessage::Preload(MSG_HOST_ACTION_EXECUTED_SUCCESSFULLY, "Host action executed successfully");    // 1481
	LocalizedMessage::Preload(PACKAGE_NOT_FOUND_STRING, "Package file not found on the disk");    // 1482
	LocalizedMessage::Preload(PACKAGE_INFO_NOT_FOUND_AT_STRING, "Can't find pack info file at");    // 1483
	LocalizedMessage::Preload(PACKAGE_INVALID_INFO_STRING, "Invalid pack info");    // 1484
	LocalizedMessage::Preload(PACKAGE_UNMOUNT_OUTER_ERROR_STRING, "Could not unmount outer package (return code {0}: {1})");    // 1485
	LocalizedMessage::Preload(PACKAGE_OUTER_NOT_OPENED_STRING, "Outer package could not be opened");    // 1486
	LocalizedMessage::Preload(PACKAGE_CREATE_LOOP_DEVICE_ERROR_STRING, "Could not create loop device (return code {0}: {1})");    // 1487
	LocalizedMessage::Preload(PACKAGE_CONTROL_LOOP_DEVICE_ERROR_STRING, "Could not control loop devices (no permissions?)");    // 1488
	LocalizedMessage::Preload(PACKAGE_MOUNT_ERROR_STRING, "Could not mount outer package (return code {0}: {1})");    // 1489
	LocalizedMessage::Preload(PACKAGE_OUTER_NOT_FOUND_STRING, "Outer package could not be found");    // 1490
	LocalizedMessage::Preload(PACKAGE_CORE_UPDATING_STRING, "Updating core package...");    // 1491
	LocalizedMessage::Preload(PACKAGE_STARTING_UPDATE_STRING, "Starting update...");    // 1492
	LocalizedMessage::Preload(PACKAGE_BAD_INFO_STRING, "Bad pack info");    // 1493
	LocalizedMessage::Preload(PACKAGE_NOT_EXISTS_STRING, "Package does not exist on disk");    // 1494
	LocalizedMessage::Preload(PACKAGE_NOT_MOUNTED_STRING, "Package is not mounted");    // 1495
	LocalizedMessage::Preload(PACKAGE_READY_STRING, "Package ready");    // 1496
	LocalizedMessage::Preload(PACKAGE_INFO_NOT_LOADED_STRING, "Pack info could not be loaded");    // 1497
	LocalizedMessage::Preload(PACKAGE_OUTER_INVALID_STRING, "Outer package is not a valid pack");    // 1498
	LocalizedMessage::Preload(PACKAGE_UNKNOWN_MOUNT_STATUS_STRING, "Unknown package mount status");    // 1499
	LocalizedMessage::Preload(MSG_ERROR_EXECUTING_ACTION, "Error executing game host action");    // 1500
	LocalizedMessage::Preload(SAS_ENABLE_STRING, "SAS Enabled");    // 1503
	LocalizedMessage::Preload(AFT_ENABLE_STRING, "AFT Enabled");    // 1504
	LocalizedMessage::Preload(AFT_IN_HOUSE_ENABLE_STRING, "AFT InHouseEnabled");    // 1505
	LocalizedMessage::Preload(AFT_BONUS_ENABLE_STRING, "AFT BonusEnabled");    // 1506
	LocalizedMessage::Preload(AFT_RESTRICTED_ENABLE_STRING, "AFT Enable Restricted");    // 1507
	LocalizedMessage::Preload(AFT_AUTO_CASHOUT_STRING, "AFT Auto Cashout");    // 1508
	LocalizedMessage::Preload(AFT_PAYIN_LIMIT_STRING, "AFTPayin Limit");    // 1510
	LocalizedMessage::Preload(AFT_PAYOUT_LIMIT_STRING, "AFTPayout Limit");    // 1511
	LocalizedMessage::Preload(MSG_HOPER_PAYING_PLEASE_WAIT, "Hopper payout in progress. Please wait.");    // 1514
	LocalizedMessage::Preload(DENOMINATION_MODE_STRING, "Denomination Mode");    // 1516
	LocalizedMessage::Preload(ATTENDANT_ALLOW_PAYIN_STRING, "Allow attendant Pay in");    // 1517
	LocalizedMessage::Preload(ATTENDANT_PARTIAL_PAYOUT_STRING, "Allow partial Pay out");    // 1518
	LocalizedMessage::Preload(LOCK_BUTTON_DISABLED_STRING, "Disable lock button");    // 1519
	LocalizedMessage::Preload(PAYOUT_BUTTON_DISABLED_STRING, "Player payout button");    // 1520
	LocalizedMessage::Preload(ALLOW_INDIVISIBLE_DENOMINATIONS_STRING, "Allow indivisible denominations");    // 1522
	LocalizedMessage::Preload(BILL_WAITING_USER_ESCROW_CONFIRM, "Please confirm bill escrow");    // 1524
	LocalizedMessage::Preload(BALANCE_LIMIT_STRING, "Balance Limit");    // 1525
	LocalizedMessage::Preload(MIN_BET_STRING, "Min Bet");    // 1526
	LocalizedMessage::Preload(MAX_BET_STRING, "Max Bet");    // 1527
	LocalizedMessage::Preload(MONEY_COLLECT_MODE_STRING, "Money Collect Mode");    // 1528
	LocalizedMessage::Preload(POWER_OFF_STRING, "Power OFF");    // 1529
	LocalizedMessage::Preload(RESTART_STRING, "Machine Restart");    // 1530
	LocalizedMessage::Preload(ONLINE_SYSTEM_COMMUNICATION_ERROR_STRING, "Online System Communication Error !!!");    // 1532
	LocalizedMessage::Preload(YOU_MUST_RESTART_WHOLE_MACHINE_STRING, "You must RESTART THE GAMING MACHINE");    // 1534
	LocalizedMessage::Preload(PLEASE_REMOVE_ATTENDANT_KEY_STRING, "Please Remove Attendant Key.");    // 1536
	LocalizedMessage::Preload(AFT_NONRESTRICTED_ENABLE_STRING, "AFT Enable Non-Restricted");    // 1538
	LocalizedMessage::Preload(TRANSACTION_ERROR_STRING, "TRANSACTION ERROR !!!");    // 1539
	LocalizedMessage::Preload(FAILED_FOR_AMOUNT_STRING, "Failed for amount");    // 1540
	LocalizedMessage::Preload(NETWORK_STATUS_STRING, "Network Status");    // 1542
	LocalizedMessage::Preload(DOORS_STATUS_STRING, "Doors Status");    // 1543
	LocalizedMessage::Preload(BIG_DOOR_STRING, "Big Door");    // 1544
	LocalizedMessage::Preload(SMALL_DOOR_STRING, "Small Door");    // 1545
	LocalizedMessage::Preload(LOGIC_DOOR_STRING, "Logic Door");    // 1546
	LocalizedMessage::Preload(DEPOSIT_DOOR_STRING, "Deposit Box Door");    // 1547
	LocalizedMessage::Preload(TOUCH_ANYWHERE_TO_CLOSE_STRING, "Touch anywhere to close");    // 1564
	LocalizedMessage::Preload(RESULTS_STRING, "results");    // 1574
	LocalizedMessage::Preload(ROULETTE_NAME_STRING, "Machine name");    // 1578
	LocalizedMessage::Preload(OPERATING_WITH_KEY_STRING, "operating with key");    // 1581
	LocalizedMessage::Preload(KEY_CODE_STRING, "Key Code");    // 1582
	LocalizedMessage::Preload(KEY_NAME_STRING, "Key Name");    // 1583
	LocalizedMessage::Preload(DELETE_ALL_KEYS_STRING, "Delete ALL keys");    // 1584
	LocalizedMessage::Preload(TERMINAL_PROFIT_STRING, "Terminal Profit");    // 1585
	LocalizedMessage::Preload(ROULETTE_PROFIT_STRING, "Machine Profit");    // 1586
	LocalizedMessage::Preload(MISCELLANEOUS_STRING, "Miscellaneous");    // 1589
	LocalizedMessage::Preload(PAYMENT_STRING, "Payment");    // 1591
	LocalizedMessage::Preload(LANGUAGE_AND_AUDIO_STRING, "Language & Audio");    // 1592
	LocalizedMessage::Preload(BILL_ACCEPTOR_STRING, "Bill Acceptor");    // 1593
	LocalizedMessage::Preload(BILL_CHANNEL_STRING, "Bill Channel");    // 1594
	LocalizedMessage::Preload(RECYCLE_CHANNEL_STRING, "Recycle Channel");    // 1595
	LocalizedMessage::Preload(BILL_RECYCLER_STATUS_STRING, "Bill Recycler status");    // 1596
	LocalizedMessage::Preload(BILL_ACCEPTOR_STATUS_STRING, "Bill Acceptor status");    // 1597
	LocalizedMessage::Preload(TO_STRING, "To");    // 1604
	LocalizedMessage::Preload(PRESET_STRING, "Preset");    // 1612
	LocalizedMessage::Preload(AUTO_RESET_STRING, "Auto Reset");    // 1616
	LocalizedMessage::Preload(PAST_PLAYERS_STRING, "Past Players");    // 1616
	LocalizedMessage::Preload(SET_POT_VALUE_STRING, "Set pot value");    // 1618
	LocalizedMessage::Preload(POT_INCREMENT_STRING, "Pot Increment");    // 1621
	LocalizedMessage::Preload(MINIMUM_POT_STRING, "Minimum Pot");    // 1627
	LocalizedMessage::Preload(PLAY_JACKPOT_FROM_STRING, "Play Jackpot FROM");    // 1628
	LocalizedMessage::Preload(MAXIMUM_POT_STRING, "Maximum Pot");    // 1629
	LocalizedMessage::Preload(JACKPOT_MIN_BET, "Jackpot Min Bet");    // 1631
	LocalizedMessage::Preload(POT_VALUE_STRING, "Pot Value");    // 1638
	LocalizedMessage::Preload(BACKPOT_VALUE_STRING, "Hidden Pot Value");    // 1639
	LocalizedMessage::Preload(JACKPOT_STATE_STRING, "Jackpot State");    // 1641
	LocalizedMessage::Preload(FIRST_DENOMINATION_STRING, "First denomination");    // 1648
	LocalizedMessage::Preload(SECOND_DENOMINATION_STRING, "Second denomination");    // 1649
	LocalizedMessage::Preload(THIRD_DENOMINATION_STRING, "Third denomination");    // 1650
	LocalizedMessage::Preload(EDIT_STRING, "EDIT");    // 1651
	LocalizedMessage::Preload(MAX_WIN_STRING, "Max Win");    // 1678
	LocalizedMessage::Preload(SAS_NET_ADDRESS_STRING, "SAS Net Address");    // 1679
	LocalizedMessage::Preload(SAS_ASSET_NUMBER_STRING, "Asset Number");    // 1680
	LocalizedMessage::Preload(SAS_LOCK_ON_LINK_DOWN_STRING, "Lock On Link Down");    // 1681
	LocalizedMessage::Preload(SAS_CASHOUT_PRIORITY_STRING, "Cashout priority");    // 1682
	LocalizedMessage::Preload(SAS_TICKET_ACCEPT_STRING, "Ticket Accept");    // 1683
	LocalizedMessage::Preload(SAS_TICKET_PRINT_STRING, "Ticket Print");    // 1684
	LocalizedMessage::Preload(SAS_TICKET_PRINT_TYPE_STRING, "Validation Type");    // 1685
	LocalizedMessage::Preload(SAS_PROMO_ACCEPT_STRING, "Promo Accept");    // 1686
	LocalizedMessage::Preload(SAS_PROMO_PRINT_STRING, "Promo Print");    // 1687
	LocalizedMessage::Preload(SAS_TICKET_EXPIRATION_STRING, "Ticket Expiration");    // 1688
	LocalizedMessage::Preload(SAS_PRINT_HANDPAY_RECEIPT_STRING, "Print Handpay Receipt");    // 1689
	LocalizedMessage::Preload(SAS_TICKET_TITLE_STRING, "Ticket Title");    // 1690
	LocalizedMessage::Preload(SAS_TICKET_ADDRESS_STRING, "Ticket Address");    // 1691
	LocalizedMessage::Preload(SAS_TICKET_PAYIN_LIMIT_STRING, "Ticket Payin Limit");    // 1692
	LocalizedMessage::Preload(SAS_TICKET_PAYOUT_LIMIT_STRING, "Ticket Payout Limit");    // 1693
	LocalizedMessage::Preload(FORCE_UPDATE_STRING, "Force update");
	LocalizedMessage::Preload(SAS_CLEAR_QUEUE_STRING, "Clear Queue");    // 1695
	LocalizedMessage::Preload(SAS_REFRESH_TITO_ENHANCED_STRING, "Clear Secure Enhanced Validation");    // 1696
	LocalizedMessage::Preload(PROGRAM_STRING, "PROGRAM");    // 1698
	LocalizedMessage::Preload(PAYOUT_ERROR_STRING, "Payout Error");    // 1707
	LocalizedMessage::Preload(RETRY_PAYOUT_STRING, "Retry Payout");    // 1708
	LocalizedMessage::Preload(PAYOUT_ERROR_DO_HANDPAY_STRING, "Pay via Handpay");    // 1709
	LocalizedMessage::Preload(DETAILS_STRING, "Details...");    // 1710
	LocalizedMessage::Preload(PLATFORMS_STRING, "Platforms");    // 1712
	LocalizedMessage::Preload(OUT_OUT_ORDER_LOCK_STRING, "Out of Order Lock");    // 1713
	LocalizedMessage::Preload(CLEAR_ALL_STRING, "Clear All");    // 1718
	LocalizedMessage::Preload(JACKPOTS_STRING, "Jackpots");    // 1735
	LocalizedMessage::Preload(PREV_PAGE_STRING, "Prev Page");    // 1750
	LocalizedMessage::Preload(NEXT_PAGE_STRING, "Next Page");    // 1751
	LocalizedMessage::Preload(VIEW_GAMES_HISTORY_STRING, "View games history");    // 1752
	LocalizedMessage::Preload(RESET_PERIODIC_COUNTERS_STRING, "Reset Periodic Counter");    // 1753
	LocalizedMessage::Preload(POWER_OPTIONS_STRING, "Power options");    // 1755
	LocalizedMessage::Preload(WIN_JACKPOT_RESET_STRING, "Win Jackpot reset");    // 1756
	LocalizedMessage::Preload(COUNT_JACKPOT_RESET_STRING, "Count Jackpot reset");    // 1757
	LocalizedMessage::Preload(GAME_CONTINUE_STRING, "Game Continue");    // 1758
	LocalizedMessage::Preload(PARAM_CHANGES_LOG_STRING, "Param Changes log");    // 1759
	LocalizedMessage::Preload(PB_TOTALS_LOG_STRING, "PB Totals log");    // 1760
	LocalizedMessage::Preload(BILLS_STRING, "Bills");    // 1761
	LocalizedMessage::Preload(COINS_STRING, "Coins");    // 1762
	LocalizedMessage::Preload(HOPER_RECYCLER_CONTOL_STRING, "Hopper/Recycler control");    // 1763
	LocalizedMessage::Preload(HOPER_EMPTY_ACTION_STRING, "Hopper empty action");    // 1764
	LocalizedMessage::Preload(PROGRAM_MASTERKEY_STRING, "Program MasterKey");    // 1767
	LocalizedMessage::Preload(CHANGE_EXPIRED_STRING, "Change Expired");    // 1768
	LocalizedMessage::Preload(REQUEST_REMOTE_ASSISTANCE_STRING, "Request remote Assistance");    // 1769
	LocalizedMessage::Preload(SET_ALL_STRING, "Set All");    // 1772
	LocalizedMessage::Preload(COPY_STRING, "Copy");    // 1773
	LocalizedMessage::Preload(PASTE_STRING, "Paste");    // 1774
	LocalizedMessage::Preload(PERMANENT_LOCK_STRING, "Permanent lock");    // 1777
	LocalizedMessage::Preload(HOPPER_STRING, "Hopper");    // 1778
	LocalizedMessage::Preload(TICKETS_STRING, "Tickets");    // 1779
	LocalizedMessage::Preload(USER_ID_STRING, "User ID");    // 1780
	LocalizedMessage::Preload(KEYBOARD_STRING, "Keyboard");    // 1781
	LocalizedMessage::Preload(TOUCH_STRING, "Touch");    // 1782
	LocalizedMessage::Preload(SWITCHES_STRING, "Switches");    // 1783
	LocalizedMessage::Preload(COUNTERS_STRING, "Counters");    // 1784
	LocalizedMessage::Preload(ONLINE_SYSTEM_STRING, "Online System");    // 1785
	LocalizedMessage::Preload(TYPE_STRING, "Type");    // 1786
	LocalizedMessage::Preload(PORT_STRING, "Port");    // 1787
	LocalizedMessage::Preload(STATUS_STRING, "Status");    // 1788
	LocalizedMessage::Preload(VERSION_TEXT_STRING, "Version");    // 1789
	LocalizedMessage::Preload(PRINT_LAST_TICKET_STRING, "Print Last Ticket");    // 1790
	LocalizedMessage::Preload(HOPERS_STRING, "Hoppers");    // 1791
	LocalizedMessage::Preload(TOUCH_TO_MAKE_SELECTION_STRING, "Touch the screen to make your selection");    // 1794
	LocalizedMessage::Preload(CABINET_TEMPERATURE_STRING, "Cabinet Temperature");    // 1795
	LocalizedMessage::Preload(VOLTAGE_STRING, "Voltage");    // 1796
	LocalizedMessage::Preload(INSERT_ADMIN_KEY_STRING, "Please insert admin key");    // 1799
	LocalizedMessage::Preload(OPERATION_MODE_STRING, "Operation Mode");    // 1801
	LocalizedMessage::Preload(DEMONSTRATION_MODE_WARNING_STRING, "Demo mode is enabled.");    // 1802
	LocalizedMessage::Preload(CPU_TEMPERATURE_STRING, "CPU Temperature");    // 1804
	LocalizedMessage::Preload(REJECT_STRING, "REJECT");    // 1807
	LocalizedMessage::Preload(ENABLED_STRING, "Enabled");    // 1808
	LocalizedMessage::Preload(COMMA_STRING, "Comma");    // 1809
	LocalizedMessage::Preload(DOT_STRING, "Dot");    // 1810
	LocalizedMessage::Preload(PRESENTATION_STRING, "PRESENTATION");    // 1811
	LocalizedMessage::Preload(NORMAL_STRING, "NORMAL");    // 1812
	LocalizedMessage::Preload(COMING_SOON_STRING, "COMING SOON");    // 1813
	LocalizedMessage::Preload(JACKPOT_LEVEL_STRING, "Level");    // 1814
	LocalizedMessage::Preload(BACK_STRING, "Back");    // 1816
	LocalizedMessage::Preload(ILLUMINATION_STRING, "Illumination");    // 1817
	LocalizedMessage::Preload(MAX_ILLUMINATION_PWM_STRING, "Max Illumination PWM");    // 1818
	LocalizedMessage::Preload(MAX_SEMAPHORE_PWM_STRING, "Max Semaphore PWM");    // 1819
	LocalizedMessage::Preload(MAX_BUTTON_LIGHTS_PWM_STRING, "Max Buttons Light PWM");    // 1820
	LocalizedMessage::Preload(MANDATORY_HANDPAY_WIN_LIMIT_STRING, "Require Handpay For Win");    // 1821
	LocalizedMessage::Preload(INSTALL_DATE_STRING, "Install Date");    // 1822
	LocalizedMessage::Preload(DOUBLE_ZERO_STRING, "Double Zero");    // 1823
	LocalizedMessage::Preload(MASTER_KEY_STRING, "Master Key");    // 1824
	LocalizedMessage::Preload(INTEGRITY_CHECK_STRING, "Integrity check");    // 1826
	LocalizedMessage::Preload(DOWNLOAD_STRING, "Download");    // 1827
	LocalizedMessage::Preload(UPDATE_STRING, "Update");    // 1828
	LocalizedMessage::Preload(RESET_SETTINGS_STRING, "Reset settings");    // 1829
	LocalizedMessage::Preload(MSG_RESTRICTION_RAM_CLEAR, "Only editable in RAM CLEAR");    // 1830
	LocalizedMessage::Preload(MSG_RESTRICTION_LOGIC_DOOR, "Logic door is not opened");    // 1831
	LocalizedMessage::Preload(RESTRICTION_UNEDITABLE_STRING, "Uneditable in");    // 1832
	LocalizedMessage::Preload(UPDATE_ALL_GAMES_STRING, "Update all games");    // 1833
	LocalizedMessage::Preload(MSG_GAME_UPDATE_NO_SUCH_GAME, "No such package available");    // 1836
	LocalizedMessage::Preload(MSG_GAME_UPDATE_OK_MEDIA, "Package updated from media");    // 1837
	LocalizedMessage::Preload(MSG_GAME_UPDATE_OK_WEB, "Package updated from WEB");    // 1838
	LocalizedMessage::Preload(MSG_GAME_UPDATE_ERR_MEDIA, "Error copying package from media");    // 1839
	LocalizedMessage::Preload(MSG_GAME_UPDATE_ERR_WEB, "Error getting package WEB");    // 1840
	LocalizedMessage::Preload(MSG_GAME_UPDATE_ERR_WEB_TRANSFER, "Error transferring package from WEB");    // 1841
	LocalizedMessage::Preload(MSG_GAME_UPDATE_DID_NOTHING, "No actions were taken");    // 1842
	LocalizedMessage::Preload(DELETE_SELECTED_STRING, "Delete selected");    // 1843
	LocalizedMessage::Preload(UPDATE_CORE_STRING, "Update Core");    // 1844
	LocalizedMessage::Preload(MSG_GAME_UPDATE_NO_SPACE, "Not enough space for package update");    // 1847
	LocalizedMessage::Preload(BILL_ACCEPTOR_TYPE_STRING, "Bill Device Type");    // 1848
	LocalizedMessage::Preload(TICKET_DEVICE_TYPE_STRING, "Ticket Device Type");    // 1849
	LocalizedMessage::Preload(TOTAL_FOR_PAYOUT_STRING, "For payout");    // 1853
	LocalizedMessage::Preload(PLAYER_BALANCE_AFTER_PAYOUT_STRING, "Player balance after payout");    // 1854
	LocalizedMessage::Preload(CLOSE_MENU_OK_KEYOUT_STRING, "Close menu on key out");    // 1855
	LocalizedMessage::Preload(BACKUP_TO_USB_STRING, "Backup All To USB");    // 1856
	LocalizedMessage::Preload(TOUCH_TO_CLOSE_STRING, "Touch to close!");    // 1858
	LocalizedMessage::Preload(CONFIRM_STRING, "Confirm");    // 1859
	LocalizedMessage::Preload(PREV_ELEMENT_STRING, "Prev Element");    // 1860
	LocalizedMessage::Preload(NEXT_ELEMENT_STRING, "Next Element");    // 1861
	LocalizedMessage::Preload(PREV_VALUE_STRING, "Prev Value");    // 1862
	LocalizedMessage::Preload(NEXT_VALUE_STRING, "Next Value");    // 1863
	LocalizedMessage::Preload(GAMES_ORDER_STRING, "Games order");    // 1865
	LocalizedMessage::Preload(WON_AMOUNT_STRING, "Won Amount");    // 1866
	LocalizedMessage::Preload(WON_STRING, "Won");    // 1868
	LocalizedMessage::Preload(MSG_GAME_UPDATE_SAME_FROM_MEDIA, "Already same version as on media");    // 1872
	LocalizedMessage::Preload(MSG_GAME_UPDATE_SAME_FROM_WEB, "Already same version as on WEB");    // 1873
	LocalizedMessage::Preload(MSG_GAME_UPDATE_SAME_ON_DISK, "Update is already downloaded. Please restart.");    // 1874
	LocalizedMessage::Preload(LOCK_PLATFORM_STRING, "Lock Platform");    // 1875
	LocalizedMessage::Preload(LOCK_IF_BIG_DOOR_OPEN_STRING, "If Big Door Open");    // 1876
	LocalizedMessage::Preload(LOCK_IF_SMALL_DOOR_OPEN_STRING, "If Small Door Open");    // 1877
	LocalizedMessage::Preload(LOCK_IF_LOGIC_DOOR_OPEN_STRING, "If Logic Door Open");    // 1878
	LocalizedMessage::Preload(LOCK_IF_DEPOSIT_DOOR_OPEN_STRING, "If Deposit Box Door Open");    // 1879
	LocalizedMessage::Preload(LOCK_IF_COUNTERS_ERROR_STRING, "If Mechanical Counters Error");    // 1880
	LocalizedMessage::Preload(GAME_UPDATE_JURISDICTION_STRING, "NOTE: All packages will be updated for jurisdiction");    // 1881
	LocalizedMessage::Preload(NEW_STRING, "NEW");    // 1882
	LocalizedMessage::Preload(TOP_STRING, "TOP");    // 1883
	LocalizedMessage::Preload(SOON_STRING, "SOON");    // 1884
	LocalizedMessage::Preload(FIXED_ORDER_STRING, "Fixed order");    // 1885
	LocalizedMessage::Preload(MOST_PLAYED_STRING, "Most played");    // 1886
	LocalizedMessage::Preload(NEWEST_FIRST_STRING, "Newest first");    // 1887
	LocalizedMessage::Preload(ALPHABETICAL_ORDER_STRING, "A to Z");    // 1888
	LocalizedMessage::Preload(VOLATILITY_STRING, "Volatility");    // 1889
	LocalizedMessage::Preload(APP_LOAD_COMPLETED_STRING, "Done. Starting application...");    // 1890
	LocalizedMessage::Preload(RESUMING_UNFINISHED_GAME_STRING, "Resuming unfinished game");    // 1891
	LocalizedMessage::Preload(BACKGROUND_SETTING_STRING, "Game Menu Background");    // 1893
	LocalizedMessage::Preload(TOP_BACKGROUND_SETTING_STRING, "Top Screen Background");    // 1894
	LocalizedMessage::Preload(BUTTON_CONFIG_CONFIGURE_STRING, "Configure");    // 1895
	LocalizedMessage::Preload(BUTTON_CONFIG_RESET_ALL_STRING, "Reset ALL buttons");    // 1896
	LocalizedMessage::Preload(SOFTWARE_LICENSE_EXPIRED, "Software license has expired");    // 1897
	LocalizedMessage::Preload(SHADER_SYNC_SETTING_STRING, "Sync Menu & Top Screen animations");    // 1898
	LocalizedMessage::Preload(QUICK_RESTART_STRING, "Application Restart");    // 1899
	LocalizedMessage::Preload(MENU_LANGUAGE_STRING, "Menu lang");    // 1904
	LocalizedMessage::Preload(MECHANICAL_COUNTERS_DIVISOR_STRING, "Mechanical Counters Divisor");    // 1924
	LocalizedMessage::Preload(AUDIO_CHECK_TIME_STRING, "Attract Mode Time");    // 1925
	LocalizedMessage::Preload(INDIVISIBLE_DENOMI_WARNING_STRING, "Your balance is not evenly divisible by this denomination!");    // 1927
	LocalizedMessage::Preload(INDIVISIBLE_REMAINDER_STRING, "Indivisible remainder");    // 1928
	LocalizedMessage::Preload(SEMAPHORE_TYPE_STRING, "Semaphore Type");    // 1929
	LocalizedMessage::Preload(VOID_GAMEROUND_STRING, "Void Game round");    // 1930
	LocalizedMessage::Preload(RETRY_STRING, "Retry");    // 1931
	LocalizedMessage::Preload(RETRY_AFTER_RESTART_STRING, "Restart And Retry");    // 1932
	LocalizedMessage::Preload(DEMO_STRING, "DEMO");    // 1933
	LocalizedMessage::Preload(DECIMAL_SEPARATOR_STRING, "Decimal Separator");    // 1934
	LocalizedMessage::Preload(THOUSANDS_SEPARATOR_STRING, "Thousands Separator");    // 1935
	LocalizedMessage::Preload(CURRENCY_DECIMAL_PLACES_STRING, "Currency Decimal Places");    // 1936
	LocalizedMessage::Preload(SEPARATORS_INCORRECT_SETTINGS_STRING, "Incorrect setting for system separators.");    // 1937
	LocalizedMessage::Preload(COUNTERS_SCALE_STRING, "Counters Scale");    // 1938
	LocalizedMessage::Preload(AUTO_COUNTERS_RESET_HOUR_STRING, "Auto counters reset hour");    // 1939
	LocalizedMessage::Preload(DISK_USAGE_STRING, "Disk usage");    // 1940
	LocalizedMessage::Preload(CORE_ERROR_UPDATING_STRING, "Error updating core!");    // 1942
	LocalizedMessage::Preload(CONFIG_SAVE_OK_STRING, "Successfully saved configuration");    // 1943
	LocalizedMessage::Preload(CONFIG_SAVE_ERROR_STRING, "There was a problem saving this configuration");    // 1944
	LocalizedMessage::Preload(AFT_STRING, "AFT");    // 1945
	LocalizedMessage::Preload(NONE_STRING, "NONE");    // 1948
	LocalizedMessage::Preload(ENHANCED_STRING, "ENHANCED");    // 1949
	LocalizedMessage::Preload(USB_BACKUP_ERROR, "Error backing up to USB!");    // 1953
	LocalizedMessage::Preload(NOT_PRESENT_STRING, "NOT PRESENT");    // 1954
	LocalizedMessage::Preload(ONLINE_STRING, "ONLINE");    // 1955
	LocalizedMessage::Preload(OFFLINE_STRING, "OFFLINE");    // 1956
	LocalizedMessage::Preload(OPENED_STRING, "OPENED");    // 1957
	LocalizedMessage::Preload(CLOSED_STRING, "CLOSED");    // 1958
	LocalizedMessage::Preload(NO_PLAYAWAY_TOKENS_STRING, "There are no active PlayAway tokens.");    // 1959
	LocalizedMessage::Preload(CANNOT_START_GAME_STRING, "Cannot start game!");    // 1960
	LocalizedMessage::Preload(CANNOT_START_UNFINISHED_GAME_STRING, "Cannot start unfinished game!");    // 1961
	LocalizedMessage::Preload(TRAFFIC_LIGHT_STRING, "Traffic Light");    // 1962
	LocalizedMessage::Preload(RGB_LIGHT_STRING, "RGB");    // 1963
	LocalizedMessage::Preload(CALLING_ATTENDANT_STRING, "Calling attendant!");    // 1964
	LocalizedMessage::Preload(SYSTEM_MESSAGE_STRING, "System message:");    // 1965
	LocalizedMessage::Preload(CORE_SUCCESSFULLY_UPDATED_STRING, "Core successfully updated!");    // 1966
	LocalizedMessage::Preload(REQUEST_FAILED_STRING, "Request failed");    // 1971
	LocalizedMessage::Preload(CORE_UPDATE_FAILED_STRING, "Core update failed");    // 1972
	LocalizedMessage::Preload(COUNTING_STRING, "Counting");    // 1973
	LocalizedMessage::Preload(PENDING_WIN_STRING, "Pending Win");    // 1974
	LocalizedMessage::Preload(UPDATE_SKIPPED_STRING, "Update skipped");    // 1975
	LocalizedMessage::Preload(ID_ASSIGNMENT_REQUIRED, "Station must be assigned");
	LocalizedMessage::Preload(STATION_ASSIGNMENT_STRING, "Station assignment");
	LocalizedMessage::Preload(STATION_ASSIGN_ACTION, "Assign Station Type And Number");
	LocalizedMessage::Preload(STATION_UNASSIGN_ACTION, "Un-assign Station");
	LocalizedMessage::Preload(STATION_UNASSIGN_ACTION_WARNING, "This will un-assign {0} and restart it.");
	LocalizedMessage::Preload(ARE_YOU_SURE_WARNING, "Are you sure you want to do this?");
	LocalizedMessage::Preload(STATION_TYPE_STRING, "Station Type");
	LocalizedMessage::Preload(STATION_NUMBER_STRING, "Station Number");
	LocalizedMessage::Preload(STATION_ALREADY_TARGET_TYPE_AND_NUMBER_MSG, "The selected station is already assigned to {0}. Nothing was done.");
	LocalizedMessage::Preload(ERROR_ASSIGNING_STATION_MSG, "Error assigning station to {0}.");
	LocalizedMessage::Preload(STATION_ASSIGNED_TO_MSG, "Station was assigned to {0}.");
	LocalizedMessage::Preload(STATION_ASSIGN_ACTION_WARNING, "This action will assign {0} to {1}.");
	LocalizedMessage::Preload(UNASSIGNED_STATION_STRING, "Unassigned Station");
	LocalizedMessage::Preload(WHICH_STRING, "which");
	LocalizedMessage::Preload(RESTART_OF_THE_STATION_IS_REQUIRED, "Restart of the station is required.");
	LocalizedMessage::Preload(AUTOSTART_GAME_TIME_STRING, "Favorite game start delay");
	LocalizedMessage::Preload(SET_AUTOSTART_GAME_STRING, "Set as Favorite");
	LocalizedMessage::Preload(UNSET_AUTOSTART_GAME_STRING, "Unset as Favorite");
	LocalizedMessage::Preload(RESOLVE_ERROR_STRING, "Resolve error");
	LocalizedMessage::Preload(UNKNOWN_ERROR_SOURCE, "Unknown error source");
	LocalizedMessage::Preload(PERFORMANCE_STRING, "Performance");
	LocalizedMessage::Preload(PERFORMANCE_RESOLVE_ACTION_MSG, "Set backgrounds of the game menu and top screen to Version6Deluxe.");
	LocalizedMessage::Preload(LICENSING_STRING, "License");
	LocalizedMessage::Preload(LICENSE_ERROR_RESOLVE_MSG, "Connect the machine to internet, the license will extend automatically. "
	                                                     "If this is not the case report the CHALLENGE CODE to your lesser or vendor. "
	                                                     "He will reply to you with the new expiry code.");
	LocalizedMessage::Preload(GO_TO_EXPIRE_CONFIG_STRING, "Go to expire config panel");
	LocalizedMessage::Preload(RNG_STRING, "RNG");
	LocalizedMessage::Preload(MSG_DISPLAYS_ORIENTED_BADLY_ACTION_INSTRUCTIONS,
	                          "Align them in a horizontal line, in order from top to bottom. Set the bottom display as the primary.");
	LocalizedMessage::Preload(SAVE_FOR_ALL_STRING, "Save for All");
	LocalizedMessage::Preload(DEFAULT_CATEGORY_STRING, "Default selected game category");
	LocalizedMessage::Preload(PENDING_UPDATE_STRING, "Pending Update");
	LocalizedMessage::Preload(MACHINE_SOFTWARE_STRING, "Machine software");
	LocalizedMessage::Preload(GAME_CONFIGURATIONS_STRING, "Game configurations");
	LocalizedMessage::Preload(LOCK_STATION_UNTIL_UPDATED_STRING, "Lock station until update is applied");
	LocalizedMessage::Preload(STATION_STRING, "Station");
	LocalizedMessage::Preload(PLAYER_PRESENT_STRING, "Player is present");
	LocalizedMessage::Preload(GAME_IS_RUNNING_STRING, "A game is being played");
	LocalizedMessage::Preload(BLOCKED_BY_TERMINALS_STRING, "Blocked by terminals");
	LocalizedMessage::Preload(AFFECTED_COMPONENTS_STRING, "Affected components");
	LocalizedMessage::Preload(ALLOW_ZERO_PAYOUT_STRING, "Allow zero payout");
	LocalizedMessage::Preload(HOST_ERROR_STRING, "Host Error");
	LocalizedMessage::Preload(DELETE_JACKPOT_LEVEL_STRING, "Delete level");
	LocalizedMessage::Preload(PARTICIPATE_IN_LEVEL, "Participate");
	LocalizedMessage::Preload(DISABLE_LEVEL, "Disable level");
	LocalizedMessage::Preload(ENABLE_LEVEL, "Enable level");
	LocalizedMessage::Preload(THIS_MACHINE_WILL_PARTICIPATE_IN_LEVEL, "Machine WILL participate in level {0}.");
	LocalizedMessage::Preload(THIS_MACHINE_WILL_NOT_PARTICIPATE_IN_LEVEL, "Machine WILL NOT participate in level {0}.");
	LocalizedMessage::Preload(COULD_NOT_DELETE_LEVEL, "Could not delete jackpot level");
	LocalizedMessage::Preload(COULD_NOT_RESET_LEVEL, "Could not reset jackpot level");
	LocalizedMessage::Preload(BET_SIZE_RANGE_STRING, "Bet size range");
	LocalizedMessage::Preload(CAN_BE_WON_ANYTIME_STRING, "Can be won anytime");
	LocalizedMessage::Preload(CONFIGURE_JACKPOT_SERVER_STRING, "Configure jackpot server");
	LocalizedMessage::Preload(LINK_JACKPOT_STRING, "Link jackpot server");
	LocalizedMessage::Preload(LOCAL_ONLY_STRING, "Local only");
	LocalizedMessage::Preload(UPDATED_STRING, "Updated");
	LocalizedMessage::Preload(DOWNLOADED_STRING, "Downloaded");
	LocalizedMessage::Preload(PACKAGES_STRING, "Packages");
	LocalizedMessage::Preload(WITH_ERRORS_STRING, "with {0} errors");
	LocalizedMessage::Preload(AUTOMATIC_ACTION_ON_WIN, "Automatic action on win");
	LocalizedMessage::Preload(NOTHING_STRING, "Nothing");
	LocalizedMessage::Preload(RESET_AND_DISABLE_STRING, "Reset and Disable");
	LocalizedMessage::Preload(NUMBER_OF_LAST_GAMES_STRING, "Number of last games");
	LocalizedMessage::Preload(SAVE_ALL_GAMES_STRING, "Save all games");
	LocalizedMessage::Preload(NO_CHECKSUM_STRING, "No Hash");
	LocalizedMessage::Preload(AUTO_UNLOCK_DOOR_ERRORS_STRING, "Auto unlock door errors");
	LocalizedMessage::Preload(AUTO_UNLOCK_MONEY_ERRORS_STRING, "Auto unlock money peripheral errors");
	LocalizedMessage::Preload(ENABLE_SAS_TIME_CONTROL, "Enable SAS control of system time");
	LocalizedMessage::Preload(MACHINE_RESTARTING_STRING, "Machine restarting ...");
	LocalizedMessage::Preload(MACHINE_SHUTTING_DOWN_STRING, "Machine shutting down ...");
	LocalizedMessage::Preload(CURRENCY_NOT_MATCHING_BA_STRING, "Currency reported from Bill Acceptor does not match chosen currency");
	LocalizedMessage::Preload(BILL_MAPPING_NOT_MACHING_BA_STRING, "Bill mapping on Bill Acceptor does not match configured bill mapping in menu");
	LocalizedMessage::Preload(BILL_MAPPINGS_STRING, "Bill channel mapping");
	LocalizedMessage::Preload(BILL_MAPPINGS_NOTE_STRING,
	                          "NOTE: These settings will apply for all stations. Incorrect configurations may lock other stations and render them unusable.");
	LocalizedMessage::Preload(CHOSEN_SETTING_NOT_MATCHING_BA_STRING, "Bill mapping on channel is not matching Bill Acceptors settings.");
	LocalizedMessage::Preload(TIMEOUT_BILL_RETURNED_STRING, "TICKET RETURNED");
	LocalizedMessage::Preload(BILL_ACCEPTED_STRING, "GOT TICKET");
	LocalizedMessage::Preload(BILL_STACKED_STRING, "BILL STACKED");
	LocalizedMessage::Preload(BILL_STACKER_CLOSED_STRING, "STACKER CLOSED");
	LocalizedMessage::Preload(TICKET_INSTEAD_OF_BILL_STRING, "TICKET INSTEAD OF BILL");
	LocalizedMessage::Preload(READY_STRING, "Ready");
	LocalizedMessage::Preload(COIN_ACCEPTED_STRING, "Got Coin");
	LocalizedMessage::Preload(HOPPER_DONE_STRING, "Payment done");
	LocalizedMessage::Preload(HOPPER_EMPTY_STRING, "Hopper empty");
	LocalizedMessage::Preload(HOPPER_PAY_ACK_STRING, "Paying..");
	LocalizedMessage::Preload(EEPROM_DISCONNECT_STRING, "EEPROM disconnected");
	LocalizedMessage::Preload(POWER_RESET_STRING, "Power was reset");
	LocalizedMessage::Preload(EEPROM_DATA_READ_ERROR_STRING, "EEPROM data read error");
	LocalizedMessage::Preload(COMMUNICATION_ERROR_STRING, "Communication error.");
	LocalizedMessage::Preload(INTERNAL_TICKET_PRINTER_ERROR_STRING, "Internal ticket printer error.");
	LocalizedMessage::Preload(OUT_OF_SERVICE_STRING, "Out of service.");
	LocalizedMessage::Preload(BILLBOARD_FOLLOW_HOST_STRING, "Billboard Follow Host");
	LocalizedMessage::Preload(USE_BILL_ACCEPTOR_VALUES_STRING, "Use bill acceptor values");
	LocalizedMessage::Preload(GO_TO_PERIPHERAL_CONFIG_STRING, "Go to peripheral config panel");
	LocalizedMessage::Preload(JACKPOT_TEMPLATES_SETUP_STRING, "Jackpot Templates Setup");
	LocalizedMessage::Preload(TEMPLATE_ID_STRING, "Template ID");
	LocalizedMessage::Preload(TEMPLATE_STRING, "Template");
	LocalizedMessage::Preload(MYSTERY_STRING, "Mystery");
	LocalizedMessage::Preload(LUCKY_NUMBER_STRING, "Lucky Number");
	LocalizedMessage::Preload(HOT_SEAT_STRING, "Hot Seat");
	LocalizedMessage::Preload(AVERAGE_PAYOUT_STRING, "Average Payout");
	LocalizedMessage::Preload(DELETE_TEMPLATE_STRING, "Delete Template");
	LocalizedMessage::Preload(NO_TEMPLATE_SELECTED_STRING, "No template selected!");
	LocalizedMessage::Preload(MIN_WINNABLE_BET_STRING, "Min Winnable Bet");
	LocalizedMessage::Preload(MAX_WINNABLE_BET_STRING, "Max Winnable Bet");
	LocalizedMessage::Preload(USE_FIXED_PAID_IN_STRING, "Use Fixed Paid In");
	LocalizedMessage::Preload(KEEP_POT_ABOVE_MIN_POT_STRING, "Keep Pot Above Min Pot");
	LocalizedMessage::Preload(LUCKY_NUMBER_FREQUENCY_STRING, "Lucky Number Frequency");
	LocalizedMessage::Preload(NEW_LEVEL_STRING, "New Level");
	LocalizedMessage::Preload(FROM_TEMPLATE_STRING, "From Template");
	LocalizedMessage::Preload(BILLBOARD_FOLLOW_GAME_STRING, "Billboard Follow Game");
	LocalizedMessage::Preload(SELECTED_CURRENCY_STRING, "SELECTED CURRENCY");
	LocalizedMessage::Preload(BILL_ACCEPTOR_CURRENCY_STRING, "BILL ACCEPTOR CURRENCY");
	LocalizedMessage::Preload(NUM_ROULETTES_STRING, "Number of RNGs");
	LocalizedMessage::Preload(SELECT_HOST_STRING, "Select Host");
	LocalizedMessage::Preload(NO_LUCKY_NUMBER_ENABLED_STRING, "This host doesn't have Lucky Number jackpots enabled!");
	LocalizedMessage::Preload(HOST_LUCKY_NUBER_JP_NOT_SUPPORTED_STRING, "Host does not support Lucky Number jackpots!");
	LocalizedMessage::Preload(HOST_ALREADY_CONTAINS_TEMPLATE_STRING, "Host already contains this template!");
	LocalizedMessage::Preload(BILL_ACCEPTOR_INTERNAL_ERROR_STRING, "Bill Acceptor internal error!");
	LocalizedMessage::Preload(EDIT_GAME_SETTINGS_STRING, "Edit game settings");
	LocalizedMessage::Preload(EDIT_GAME_SETTINGS_ADMIN_STRING, "Edit game settings (admin)");
	LocalizedMessage::Preload(STOP_GAME_IF_BIG_DOOR_OPEN_STRING, "Stop game if big door open");
	LocalizedMessage::Preload(STOP_GAME_IF_SMALL_DOOR_OPEN_STRING, "Stop game if small door open");
	LocalizedMessage::Preload(STOP_GAME_IF_LOGIC_DOOR_OPEN_STRING, "Stop game if logic door open");
	LocalizedMessage::Preload(STOP_GAME_IF_DEPOSIT_DOOR_OPEN_STRING, "Stop game if deposit door open");
	LocalizedMessage::Preload(STOP_GAME_IF_COUPOLA_DOOR_OPEN_STRING, "Stop game if cupola door open");
	LocalizedMessage::Preload(MAX_NUM_GAMES_ALREADY_STARTED, "Max number of games already started");
	LocalizedMessage::Preload(PAYOUT_IN_PROGRESS, "Payout in progress");
	LocalizedMessage::Preload(BETS_OPEN_STRING, "BETS ARE OPEN");
	LocalizedMessage::Preload(BETS_CLOSED_STRING, "BETS ARE CLOSED");
	LocalizedMessage::Preload(WAITING_FOR_RESULT_STRING, "WAITING FOR RESULT");
	LocalizedMessage::Preload(STATION_ID_STRING, "Station ID");
	LocalizedMessage::Preload(GAMES_IN_MULTIPLAY_STRING, "Games in multiplay");
	LocalizedMessage::Preload(ALL_GAMES_STRING, "All\nGames");
	LocalizedMessage::Preload(ROULETTE_STRING, "Roulette Games");
	LocalizedMessage::Preload(CARD_GAMES_STRING, "Card Games");
	LocalizedMessage::Preload(ACTIVE_GAMES_STRING, "Active Games");
	LocalizedMessage::Preload(SETTINGS_STRING, "Settings");
	LocalizedMessage::Preload(PLAY_THIS_GAME_STRING, "Play this game");
	LocalizedMessage::Preload(ADD_GAME_STRING, "Add\nGame");
	LocalizedMessage::Preload(LAST_WIN_STRING, "Last Win");
	LocalizedMessage::Preload(MULTIPLAY_STRING, "Multiplay");
	LocalizedMessage::Preload(BALANCE_STRING, "Balance");
	LocalizedMessage::Preload(PLAY_AWAY_STRING, "Play Away");
	LocalizedMessage::Preload(I_GAMES_STRING, "iGames");
	LocalizedMessage::Preload(VIRTUAL_STRING, "Virtual");
	LocalizedMessage::Preload(AUTO_STRING, "Auto");
	LocalizedMessage::Preload(UNAVAILABLE_STRING, "Unavailable");

	LocalizedMessage::Preload(SOUND_STRING, "Sound");
	LocalizedMessage::Preload(GAME_VOLUME_STRING, "Game Volume");
	LocalizedMessage::Preload(GAME_MUSIC_VOLUME_STRING, "Game Music Volume");
	LocalizedMessage::Preload(CHIP_PAINTING_STRING, "Chip Painting");
	LocalizedMessage::Preload(LOCK_STATION_STRING, "Lock Station");
	LocalizedMessage::Preload(VIDEO_STRING, "Video");
	LocalizedMessage::Preload(VIDEO_RESOLUTION_STRING, "Video Resolution");
	LocalizedMessage::Preload(GENERAL_SETTINGS_STRING, "General");
	LocalizedMessage::Preload(ACCESSIBILITY_STRING, "Accessibility");
	LocalizedMessage::Preload(STREAM_ENABLED_STRING, "Stream enabled");
	LocalizedMessage::Preload(GAME_EFFECTS_SOUND_STRING, "Game Effects Sound");
	LocalizedMessage::Preload(JACKPOT_SERVER_STRING, "Jackpot server");
	LocalizedMessage::Preload(SERVER_STATUS_STRING, "Server Status");
	LocalizedMessage::Preload(WIN_POSITIONS_STRING, "Win positions");
	LocalizedMessage::Preload(RESULT_STRING, "Result");
	LocalizedMessage::Preload(PLAYER_STRING, "Player");
	LocalizedMessage::Preload(BANKER_STRING, "Banker");
	LocalizedMessage::Preload(TIGER_STRING, "Tiger");
	LocalizedMessage::Preload(DRAGON_STRING, "Dragon");

	LocalizedMessage::Preload(CLOSE_ONE_GAME_TO_ADD_SELECTED_STRING, "Close one game to add selected one.");
	LocalizedMessage::Preload(HISTORY_STRING, "History");
	LocalizedMessage::Preload(CLOSE_ALL_GAMES_STRING, "Close All Games");
	LocalizedMessage::Preload(DENOMINATION_TIP_STRING, "Select value for 1 credit");
	LocalizedMessage::Preload(CHANGE_GAME_STRING, "Change game");

	LocalizedMessage::Preload(CLOSING_ALL_GAMES_STRING, "Closing All Games");
	LocalizedMessage::Preload(HINT_STRING, "Hint");
	LocalizedMessage::Preload(NOTIFICATION_STRING, "Notification");
	LocalizedMessage::Preload(SYSTEM_VOLUE_STRING, "System Volume");
	LocalizedMessage::Preload(MAX_NUMBER_OF_CONCURRENT_GAMES_STRING, "Max Number Of Concurrent Games");
	LocalizedMessage::Preload(DISPLAY_CURRENCY_ON_LEFT_STRING, "Display currency symbol on the left");
	LocalizedMessage::Preload(CLOSE_GAME_STRING, "Close Game");
	LocalizedMessage::Preload(ALREADY_RUNNING_STRING, "Already running");
	LocalizedMessage::Preload(GAME_VOIDED_STRING, "Game voided");
	LocalizedMessage::Preload(PROGRESSIVE_WIN_OCCURRENCE_STRING, "Progressive Win Occurrence");

	LocalizedMessage::Preload(ROULETTE_BET_PLAIN_STRING, "Straight Up");
	LocalizedMessage::Preload(ROULETTE_BET_CAVAL_STRING, "Split");
	LocalizedMessage::Preload(ROULETTE_BET_TRASVERSALES_STRING, "Trio");
	LocalizedMessage::Preload(ROULETTE_BET_CARRE_STRING, "Corner");
	LocalizedMessage::Preload(ROULETTE_BET_FIVE_NUMBERS_STRING, "Five numbers");
	LocalizedMessage::Preload(ROULETTE_BET_SIX_NUMBERS_STRING, "Six numbers");
	LocalizedMessage::Preload(ROULETTE_BET_HORIZONTAL_LINE_STRING, "Horizontal line");
	LocalizedMessage::Preload(ROULETTE_BET_THIRD_STRING, "Third");
	LocalizedMessage::Preload(ROULETTE_BET_COLOR_STRING, "Red / Black");
	LocalizedMessage::Preload(ROULETTE_BET_ODD_EVEN_STRING, "Odd / Even");
	LocalizedMessage::Preload(ROULETTE_BET_HALF_STRING, "1-18 / 19-36");
	LocalizedMessage::Preload(ROULETTE_BET_LES_FIGURES_STRING, "Les Figures");
	LocalizedMessage::Preload(ROULETTE_BET_TOUT_VA_STRING, "Tout Va");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_3_STRING, "Le Finali 3");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_4_STRING, "Le Finali 4");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_5_STRING, "Le Finali 5");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_TOUT_VA_3_STRING, "Le Finali Tout Va 3");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_TOUT_VA_4_STRING, "Le Finali Tout Va 4");
	LocalizedMessage::Preload(ROULETTE_BET_LE_FINALI_TOUT_VA_5_STRING, "Le Finali Tout Va 5");
	LocalizedMessage::Preload(TRANSACTION_ID_STRING, "Transaction ID");
}