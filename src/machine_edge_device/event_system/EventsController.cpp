//
// Created by sviatoslavs on 27.08.24.
//

#include "event_system/EventsController.h"

#include "TMachineEdgeDeviceApp.h"
#include "enums/Exceptions.h"
#include "enums/LongPolls.h"
#include "session/SessionController.h"
#include "web/yprotocol/YClient.h"

// Definition of the static method getInstance
EventsController& EventsController::GetInstance()
{
	static EventsController instance;
	return instance;
}
void EventsController::RegisterEvent(EMedEvent event, const std::string& comment /* = ""*/, uint64_t eventTime /* = 0*/,
                                     double_t value /*= std::numeric_limits<double_t>::max()*/, uint64_t sendingDelay /* = 0*/,
                                     const std::function<void()>& OnEventSent /*= nullptr*/)
{
	// CHecking if the event can influence the session
	SessionController::GetInstance().RegisterEvent(event);
	// if CMS is not interested in this event, we can ignore it
	if (CMSIgnored(event))
		return;
	// check if the event must have a meter with it
	std::string machineId = CMSConnection->GetSlotMachine()->mCMSMachineInfo.mMachineProfile.mId;
	const auto res = SASMetersController::GetInstance().RequestMetersForCMSEvent(
	  event, IsInPriorityList(event),
	  [this, event, eventTime, comment, machineId, OnEventSent, sendingDelay, value](const std::unordered_map<ESASMeters, MeterToSend>& metersBefore,
	                                                                                 const std::unordered_map<ESASMeters, MeterToSend>& metersAfter) {
		  // maybe need to add here check if meters before were doesnt exist
		  uint64_t beforeMeterCollection = 0;
		  if (!metersBefore.empty())
		  {
			  beforeMeterCollection = std::ranges::max_element(metersBefore, {}, [](const auto& pair) { return pair.second.valueAt; })->second.valueAt;
		  }
		  uint64_t afterMeterCollection = 0;
		  if (!metersAfter.empty())
		  {
			  afterMeterCollection = std::ranges::max_element(metersAfter, {}, [](const auto& pair) { return pair.second.valueAt; })->second.valueAt;
		  }
		  Event eventToSend {
			  static_cast<uint16_t>(mEventsForCMS->size()),    // id
			  event,    // event
			  eventTime == 0 ? ytime::GetSystemTimeMsec() : eventTime,    // timestamp
			  beforeMeterCollection,    // beforeMeterCollection
			  afterMeterCollection,    // afterMeterCollection
			  sendingDelay,    // sendingDelay
			  value,    // value
			  comment.empty() ? event._to_string() : comment,    // comment
			  machineId,    // machineId
			  metersBefore,    // metersBefore
			  metersAfter,    // metersAfter
			  OnEventSent    // OnEventSent
		  };

		  ScopedLock lock(mEventsForCMS);
		  mEventsForCMS->emplace_back(eventToSend);
		  ProcessEvents();
	  });
	if (!res)
	{
		// in case event dont require meters
		ScopedLock lock(mEventsForCMS);
		mEventsForCMS->emplace_back(mEventsForCMS->size(), event, eventTime == 0 ? ytime::GetSystemTimeMsec() : eventTime, 0, 0, sendingDelay, value,
		                            comment.empty() ? event._to_string() : comment, machineId, std::unordered_map<ESASMeters, MeterToSend>(),
		                            std::unordered_map<ESASMeters, MeterToSend>(), OnEventSent);
		ProcessEvents();
	}
	SASMetersController::GetInstance().CollectMeters();
	CMSConnection->GetSlotMachine()->OnStatusUpdated();
}

void EventsController::RegisterException(const EException exception, const uint64_t mExceptionTime, double_t value /* = std::numeric_limits<double_t>::max() */)
{
	uint64_t validityTime;
	uint64_t lastRead = 0;

	auto excReadTime = mLastTimeExceptionProcessed.find(exception);
	if (excReadTime != mLastTimeExceptionProcessed.end())
	{
		lastRead = excReadTime->second;
	}
	auto it = EXCEPTIONS_LIFETIME_MAP.find(exception);
	if (it != EXCEPTIONS_LIFETIME_MAP.end())
		validityTime = it->second;
	else
		validityTime = DEFAULT_EXCEPTION_LIFETIME;

	if (lastRead + validityTime > ytime::GetSystemTimeMsec())
	{
		return;    // ignoring if get spammed with exceptions;
	}

	mLastTimeExceptionProcessed[exception] = ytime::GetSystemTimeMsec();

	mHaveExceptions = true;
	auto medSettings = CMSConnection->GetSlotMachine()->mCMSMachineInfo.mCasinoSettings.mMedSettings;
	switch (exception)
	{
		case EException::OPERATOR_CHANGED_OPTIONS: RegisterEvent(EMedEvent::MachineConfigurationChange, "", mExceptionTime, value); break;
		case EException::PRINTER_COMMUNICATION_ERROR: RegisterEvent(EMedEvent::TicketPrinterCommunicationError, "", mExceptionTime, value); break;
		case EException::PRINTER_PAPER_OUT_ERROR: RegisterEvent(EMedEvent::TicketPrinterOutOfPaper, "Ticket printer out of paper", mExceptionTime, value); break;
		case EException::GAME_LOCKED:
			RegisterEvent(EMedEvent::AFTLockConfirmed, "", mExceptionTime, value);    // need to do AFT epoc check
			RegisterEvent(EMedEvent::MachineLockedUsingMachineMenu, "", mExceptionTime, value);
			break;
		case EException::EXCEPTION_BUFFER_OVERFLOW: RegisterEvent(EMedEvent::MachineExceptionBufferOverflow, "", mExceptionTime, value); break;
		case EException::PRINTER_PAPER_LOW: RegisterEvent(EMedEvent::TicketPrinterPaperLow, "Ticket printer paper low", mExceptionTime, value); break;
		case EException::PRINTER_POWER_OFF: RegisterEvent(EMedEvent::TicketPrinterPowerOff, "", mExceptionTime, value); break;
		case EException::PRINTER_POWER_ON: RegisterEvent(EMedEvent::TicketPrinterPowerOn, "", mExceptionTime, value); break;
		case EException::GAME_STARTED:
			if (medSettings.mGameRoundEventsEnabled)
			{
				RegisterEvent(EMedEvent::GameRoundStartReceived, "", mExceptionTime, value);
			}
			break;
		case EException::GAME_ENDED:
			if (medSettings.mGameRoundEventsEnabled)
			{
				RegisterEvent(EMedEvent::GameRoundEndReceived, "", mExceptionTime, value);
			}
			break;
		case EException::GAME_SELECTED: RegisterEvent(EMedEvent::GameSelected, "", mExceptionTime, value); break;
		case EException::SLOT_DOOR_OPENED: RegisterEvent(EMedEvent::MainDoorOpened, "", mExceptionTime, value); break;
		case EException::SLOT_DOOR_CLOSED: RegisterEvent(EMedEvent::MainDoorClosed, "", mExceptionTime, value); break;
		case EException::CASHBOX_DOOR_OPENED: RegisterEvent(EMedEvent::StackerDoorOpened, "", mExceptionTime, value); break;
		case EException::CASHBOX_DOOR_CLOSED: RegisterEvent(EMedEvent::StackerDoorClosed, "", mExceptionTime, value); break;
		case EException::BELLY_DOOR_OPENED: RegisterEvent(EMedEvent::BellyDoorOpened, "", mExceptionTime, value); break;
		case EException::BELLY_DOOR_CLOSED: RegisterEvent(EMedEvent::BellyDoorClosed, "", mExceptionTime, value); break;
		// case EException::DROP_DOOR_OPENED: RegisterEvent(EMedEvent::LogicDoorOpened, "", mExceptionTime); break;
		// case EException::DROP_DOOR_CLOSED: RegisterEvent(EMedEvent::LogicDoorClosed, "", mExceptionTime); break;
		case EException::CARD_CAGE_OPENED: RegisterEvent(EMedEvent::LogicDoorOpened, "", mExceptionTime, value); break;
		case EException::CARD_CAGE_CLOSED: RegisterEvent(EMedEvent::LogicDoorClosed, "", mExceptionTime, value); break;
		case EException::CASHBOX_REMOVED:
			// mGotStackerRemoved = true;
			RegisterEvent(EMedEvent::BillStackerRemoved, "", mExceptionTime, value);
			break;
		case EException::CASHBOX_INSTALLED:
			// if(!mGotStackerRemoved)
			RegisterEvent(EMedEvent::BillStackerInstalled, "", mExceptionTime, value);
			break;
		case EException::CASH_OUT_TICKET_PRINTED: {
			//			std::shared_ptr<FTITOTransactionData> lastPrinted = CMSConnection->GetSlotMachine()->TicketingModule->GetLastTicketPrinted();
			//			RegisterEvent(EMedEvent::TicketPrintedAtMachine, "", mExceptionTime, lastPrinted ? lastPrinted->mTicketAmount * 0.01 : 0.0);
		}
		break;
		case EException::HANDPAY_VALIDATED: RegisterEvent(EMedEvent::HandpayValidatedForHandpayReceipt, "", mExceptionTime, value); break;
		case EException::BILL_ACCEPTED_1_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 1); break;
		case EException::BILL_ACCEPTED_2_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 2); break;
		case EException::BILL_ACCEPTED_5_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 5); break;
		case EException::BILL_ACCEPTED_10_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 10); break;
		case EException::BILL_ACCEPTED_20_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 20); break;
		case EException::BILL_ACCEPTED_50_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 50); break;
		case EException::BILL_ACCEPTED_100_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 100); break;
		case EException::BILL_ACCEPTED_200_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 200); break;
		case EException::BILL_ACCEPTED_500_00: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, 500); break;
		case EException::BILL_ACCEPTED: RegisterEvent(EMedEvent::BillIn, "", mExceptionTime, value == std::numeric_limits<double_t>::max() ? 1000 : value); break;
		case EException::HANDPAY_RESET: RegisterEvent(EMedEvent::HandpayResetManulCompleted, "", mExceptionTime, value); break;
		case EException::TICKET_TRANSFER_COMPLETE: {
			std::shared_ptr<FTITOTransactionData> lastRedeemed = CMSConnection->GetSlotMachine()->TicketingModule->GetLastTicketRedeemed();
			RegisterEvent(EMedEvent::TicketRedeemedAtMachine, "", mExceptionTime, lastRedeemed ? lastRedeemed->mTicketAmount * 0.01 : 0.0);
		}
		break;
		case EException::BILL_VALIDATOR_TOTALS_RESET: RegisterEvent(EMedEvent::StackerMetersReset, "", mExceptionTime, value); break;
		case EException::GAMING_MACHINE_SOFT_METERS_RESET: RegisterEvent(EMedEvent::AllMetersResetToZeroReported, "", mExceptionTime, value); break;
		case EException::TICKET_HAS_BEEN_INSERTED: RegisterEvent(EMedEvent::TicketHasBeenInserted, "", mExceptionTime, value); break;
		case EException::CASH_OUT_BUTTON_PRESSED: RegisterEvent(EMedEvent::CollectButtonPressed); break;
		case EException::AFT_REQUEST_FOR_HOST_CASHOUT: RegisterEvent(EMedEvent::AFTOutRequested); break;

		default:;
	}
}
void EventsController::RegisterCMSEvent(const std::string& event, const json& body)
{
	if (CMSConnection->GetSlotMachine() == nullptr)
		return;

	if (event == ECMSEvent::HANDPAY_RESET)
	{
		RegisterEvent(EMedEvent::HandpayResetCMSRequest);
		CMSConnection->GetSlotMachine()->HandpayModule->ResetHandpay();
	}
	else if (event == ECMSEvent::MACHINE_FUNDS_TRANSFER)
	{
		/*
		 * First we do an AFT lock request.
		 * After we get exception 0x6F, meaning that the lock was successful, we do AFT fund transfer.
		 */
		CMSConnection->GetSlotMachine()->AFTModule->BeginCMSAFTTransaction(body);
	}
	else if (event == ECMSEvent::REQUEST_METERS)
	{
		CMSConnection->GetSlotMachine()->CMSMeterRequest(body);
	}
	else if (event == ECMSEvent::LOCK_STATE_CHANGE)
	{
		CMSConnection->GetSlotMachine()->CMSLockRequest(body);
	}
	else if (event == ECMSEvent::OPERATIONAL_STATUS_CHANGE)
	{
		CMSConnection->GetSlotMachine()->CMSOperationalStatusChangeRequest(body);
	}
	else if (event == ECMSEvent::PROPERTY_SETTINGS_CHANGE)
	{
		if (body.contains("settings"))
		{
			TLOG(LogMEDApp, Info, "Propetry settings changed");
			CMSConnection->GetSlotMachine()->mCMSMachineInfo.ParsePropertySettings(body["settings"]);
			SASMetersController::GetInstance().UpdateMetersForEventsBasedOnSettings();
			CMSConnection->GetSlotMachine()->CheckConfiguration();
		}
	}
	else if (event == ECMSEvent::MED_CONFIGURATION_CHANGE)
	{
		if (body.contains("configuration"))
		{
			TLOG(LogMEDApp, Info, "MED settings changed");
			CMSConnection->GetSlotMachine()->mCMSMachineInfo.ParseMEDSettings(body["configuration"]);
			SASMetersController::GetInstance().UpdateMetersForEventsBasedOnSettings();
			CMSConnection->GetSlotMachine()->CheckConfiguration();
		}
	}
	else if (event == ECMSEvent::REBOOT_MED)
	{
		CMSConnection->StopMEDWithCode(EMEDExitCode::ERebootMED);
	}
	else if (event == ECMSEvent::RESTART_MED_APPLICATION)
	{
		CMSConnection->StopMEDWithCode(EMEDExitCode::ERestartApplication);
	}
	else
		TLOG(LogMEDApp, Warning, "Unknown CMS event '%s'", event.c_str());
}
void EventsController::ForceProceddingEvents()
{
	ProcessEvents();
}
void EventsController::ExceptionsReadigFinished()
{
	if (mHaveExceptions)
	{
		mHaveExceptions = false;
		SASMetersController::GetInstance().CollectMeters();
	}
}

void EventsController::ProcessEvents()
{
	if (mtthProcessEvents != nullptr)
	{
		mtthProcessEvents->Disable();
	}
	auto medSettings = CMSConnection->GetSlotMachine()->mCMSMachineInfo.mCasinoSettings.mMedSettings;
	if (!HaveHighPriorityEvents() && mLastEventProcessing + medSettings.mLowPriorityEventFrequencyInSeconds > ytime::GetSystemTimeMsec())
	{
		// if there are no high priority events and the delay time has not passed yet, we will wait for the delay time to pass
		mtthProcessEvents = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthMember<EventsController, void>>(
		  this, &EventsController::ProcessEvents, (mLastEventProcessing + medSettings.mLowPriorityEventFrequencyInSeconds - ytime::GetSystemTimeMsec()),
		  rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "EventsController::ProcessEvents");

		return;
	}
	uint64_t timeToProcessNextEvent = 0;
	if (!mEventsForCMS->empty() && CMSConnection->GetSlotMachine() /* && CMSConnection->GetSlotMachine()->SasIsReady()*/)
	{
		ScopedLock lock(mEventsForCMS);
		std::vector<Event> tmpEvents;
		// filtering here what to send
		for (auto event = mEventsForCMS->begin(); event != mEventsForCMS->end();)    // or some another rule by priority or limited amount of events
		{
			// if we didn't finish discovery process sending only MED events
			if (!CMSConnection->mConnectedToCMS && !MED_EVENTS.contains(event->mEvent))
			{
				++event;
				continue;
			}
			if (event->sendingDelay > 0 && event->mTimestamp + event->sendingDelay > ytime::GetSystemTimeMsec())
			{
				TLOG(LogCMSComm, VeryVerbose, "Too soon for sending the event %s", event->mComment.c_str());
				if (timeToProcessNextEvent == 0 || event->mTimestamp + event->sendingDelay < timeToProcessNextEvent)
				{
					timeToProcessNextEvent = event->mTimestamp + event->sendingDelay;
				}
				++event;
				continue;
			}
			tmpEvents.push_back(*event);
			event = mEventsForCMS->erase(event);
		}
		lock.unlock_checked();
		if (timeToProcessNextEvent != 0)
		{
			mtthProcessEvents = rtfwk_sdl2::pApp->AddTimedTask<rtfwk_sdl2::TtthMember<EventsController, void>>(
			  this, &EventsController::ProcessEvents, timeToProcessNextEvent, rtfwk_sdl2::TTimedTaskHandler::ENDLESS, "TSlotMachineSAS::CMSUpdate, 15000");
		}
		if (!tmpEvents.empty())
		{
			mLastEventProcessing = ytime::GetSystemTimeMsec();
			CMSConnection->SendEventsToCMS(tmpEvents).then(boost::launch::async, [this, tmpEvents](boost::future<YResponse> fut) {
				const YResponse response = fut.get();
				// if server received message out part is done if not we need to resend
				if (response.Status != EMessageStatus::ResponseOk && response.Status != EMessageStatus::ResponseError)
				{
					ScopedLock lock(mEventsForCMS);
					mLastEventProcessing = 0;    // reset timer to be sent again
					for (auto it = tmpEvents.cbegin(); it != tmpEvents.cend(); ++it) { mEventsForCMS->push_front(tmpEvents.back()); }

					TLOG(LogCMSComm, Debug, "Resending events: %s", response.ErrorMessage().c_str());
				}
				else
				{
					// calling OnEventSent callbacks if necessary
					for (const auto& event : tmpEvents)
					{
						if (event.OnEventSent)
						{
							event.OnEventSent();
						}
					}
				}
			});
		}
	}
}

// Private constructor
EventsController::EventsController()
{
	mLastEventProcessing = 0;
}

// Private destructor
EventsController::~EventsController() {}
bool EventsController::HaveHighPriorityEvents() const
{
	ScopedLock lock(mEventsForCMS);
	const auto& events = *mEventsForCMS.operator->();
	for (const auto& event : events)
	{
		if (IsInPriorityList(event.mEvent))
		{
			return true;
		}
	}
	return false;
}
bool EventsController::IsInPriorityList(const EMedEvent event) const
{
	if (std::ranges::find(PRIORITY_EVENTS, event) != PRIORITY_EVENTS.end())
	{
		return true;
	}
	return false;
}
bool EventsController::CMSIgnored(const EMedEvent event) const
{
	if (std::ranges::find(CMS_IGNORING_EVENTS, event) != CMS_IGNORING_EVENTS.end())
	{
		return true;
	}
	return false;
}
