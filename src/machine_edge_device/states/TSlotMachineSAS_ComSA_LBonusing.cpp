//---------------------------------------------------------------------------
// SEND IN RECEIVE MORATA BITI LO�ENI FUNKCIJI, KER GRE ZA ENO NITKO, IN PO NEPOTREBNEM �AKAMO RESPONSE SLOT-A!!!!!!!!!

#include "TSlotMachineSAS_ComSA_LBonusing.h"

#include "TSlotMachineSAS_ComSA_Common.h" /*_COMMON mora biti v .cpp*/
#include "enums/LongPolls.h"


//---------------------------------------------------------------------------
OnExecuteStatus TSA_LB_InitiateLegacyBonusCommand1::OnExecute()
{
	AppendChar(ELongPoll::InitiateLegacyBonusPay); /* Initiate Legacy Bonus Command */
	uint64_t amount_in_cents = 0;
	amount_in_cents = mAmount;

	uint64_t amount_in_base_units = amount_in_cents / SLOT->getDenomination();
	amount_in_base_units = amount_in_base_units / 100;    // zaradi centov
	AppendBCD(amount_in_base_units, 4);
	AppendChar(static_cast<uint8_t>(TaxStatus));    // v TransferFlags je TAX Status - 0-Deductible, 1-NonDeductible, 2-WagerMatch

	// char ASCIICommand[(2 * SAS_MAX_PCT_LEN) + 1];
	// unsigned tmpLength = m_Len;
	// MyUtils::BinHexToASCIIHex((unsigned char*)m_Buf, ASCIICommand, &tmpLength);
	TLOG(LogSASComm, Debug, "%s: InitiateLegacyBonus: SENDRECIVE", SlotName.c_str());
	SLOT->SASSendReceiveBinHEX(m_Buf, true, true);


	return OnExecuteStatus::HANDLED;
}
OnEventStatus TSA_LB_InitiateLegacyBonusCommand1::OnEvent(const std::shared_ptr<TSASMessage> message)
{
	/*
	    if (0 == result)    // communication succeded
	    {
	        SLOT->setAFTEpochInProgress();    // tole bi morala klicati ze  SetAFTTransactionPending() !!!
	        tmpLength = SAS_MAX_PCT_LEN;
	        MyUtils::ASCIIHexToBinHex(AsciiResponse, (unsigned char*)m_Buf, &tmpLength);
	        TLOG(LogSASComm, Debug, "%s: InitiateLegacyBonus: (TransactionID=%s) status %0.2X", SlotName.c_str(), AFTTransactionData.TransactionID.c_str(),
	            (unsigned char)m_Buf[4]);
	        if (tmpLength > 1 && 0x00 == (unsigned char)m_Buf[1])
	        {
	            // Gaming Machine Busy response
	            // to javi slot, ce je v door open
	            TLOG(LogSASComm, Debug,
	                "%s: InitiateLegacyBonus: Slot is currently unable to process the bonus award(Malfunction, door open or maintenance). Retrying later...",
	                SlotName.c_str());
	            SLOT->SetAFTTransactionRetry(AFTTransactionData.TransactionID,
	                                         "Slot is currently unable to process the bonus award(Malfunction, door open or maintenance). Retrying later...");
	        }
	        else if (1 == tmpLength)
	        {    // ack or nack
	            if (SASAddress == (unsigned char)m_Buf[0])    // ACK - SAS 6.02 table 7.4B, page 7-5
	            {    // ACK - najbrz bo slot poslal sedaj se potrditev!
	                TLOG(
	                  LogSASComm, Debug,
	                  "%d: InitiateLegacyBonus: Transfer ACK-ed, waiting confirmation exception. *TR_ID=%s, Amount=%d(cents), CorrectedAmount=%.2f(cents)
	   Amount=%d(credits)", Address, AFTTransactionData.TransactionID.c_str(), amount_in_cents, MyUtils::round(amount_in_base_units * (double)(SLOT->getDenomination() *
	   (double)10), 3), amount_in_base_units); SLOT->SetAFTTransactionPending(AFTTransactionData.TransactionID,
	                                               ((std::string) "Transfer ACK-ed, waiting confirmation exception. *TR_ID=") + AFTTransactionData.TransactionID.c_str());
	            }
	            else
	            {    // unknown response from slot!!
	                TLOG(LogSASComm, Debug, "%d: InitiateLegacyBonus: Unknown response(1) from Slot. Is LegacyBonusing enabled on slot???", Address);
	                SLOT->SetAFTTransactionAborted(AFTTransactionData.TransactionID, "Unknown response(1) from Slot. Is LegacyBonusing enabled on slot???");
	            }
	        }
	        else
	        {    // unknown response type
	            TLOG(LogSASComm, Debug, "%d: InitiateLegacyBonus: Slot response not recognised. Aborting...", Address);
	            SLOT->SetAFTTransactionAborted(AFTTransactionData.TransactionID, "Slot response  not recognised. Aborting...");
	        }
	        SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	        return OnExecuteStatus::FAILED;
	    }    // NEVER REACHING BECAUSE OF RETURN
	    else if (128 == result)    // communication succeded, but negative ACK  //NACK - SAS 6.02 table 7.4B, page 7-5
	    {    // SAS NACK
	        TLOG(LogSASComm, Debug, "%d: InitiateLegacyBonus: Slot is refused the bonus award. Aborting...", Address);
	        SLOT->SetAFTTransactionAborted(AFTTransactionData.TransactionID, "Slot is refused the bonus award. Aborting...");
	        SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	        return OnExecuteStatus::FAILED;
	    }
	    else
	    {
	        // unknown response
	        TLOG(LogSASComm, Debug, "%d: InitiateLegacyBonus: Unknown response(2) from Slot. Is LegacyBonusing enabled on slot???", Address);
	        SLOT->SetAFTTransactionAborted(AFTTransactionData.TransactionID, "Unknown response(2) from Slot. Is LegacyBonusing enabled on slot???");
	        SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	        return OnExecuteStatus::FAILED;
	    }

	    if (OnExecuteCounter > 1)
	    {    // timeout
	        TLOG(LogSASComm, Debug, "%d: InitiateLegacyBonus: Slot is ignoring LegacyBonusing long poll. Is LegacyBonusing enabled on slot???", Address);
	        SLOT->SetAFTTransactionAborted(AFTTransactionData.TransactionID, "Slot is ignoring LegacyBonusing long poll. Is LegacyBonusing enabled on slot???");
	        SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	        return OnExecuteStatus::IGNORED;
	    }
	*/
	TLOG(LogSASComm, Debug, "%s received from slot machine.", message->mHexResponse[0] > 0x80 ? "NACK" : "ACK");
	if (message->mHexResponse[0] <= 0x80)
		return OnEventStatus::HANDLED;

	return OnEventStatus::IGNORED;
}

OnExecuteStatus TSA_LB_LegacyBonusAwardedException1::OnExecute()
{
	// samo potrdimo transackijo

	// potrdimo vplacilo s prejetjem exception-a 0x7C
	/*if (!SLOT->isAFTEpoch(true))
	{
	    TLOG(LogSASComm, Debug, "%d: LegacyBonusAwardedException: Transfer is not pending(Epoch closed!) *TR_ID=%s", Address, AFTTransactionData.TransactionID.c_str());
	    SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	    return OnExecuteStatus::FAILED;
	}*/

	// unsigned int amount_in_cents = AFTTransactionData.CashableCents;
	// unsigned int amount_in_base_units = amount_in_cents / SLOT->getDenomination();
	// amount_in_base_units = amount_in_base_units / 100;    // zaradi centov

	// TLOG(LogSASComm, Debug,
	//     "%d: LegacyBonusAwardedException: Transfer completed successfuly with LegacyBonusing. *TR_ID=%s, Amount=%d(cents), CorrectedAmount=%d(cents)
	//     Amount=%d(credits)", SLOT->GetAddress(), AFTTransactionData.TransactionID.c_str(), amount_in_cents, amount_in_base_units * SLOT->getDenomination() * 100,
	//     amount_in_base_units);


	///	SetNext(std::make_shared<TSA_IdleState>(GetSA()));
	return OnExecuteStatus::HANDLED;
}
