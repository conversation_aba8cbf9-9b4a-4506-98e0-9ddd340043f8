//---------------------------------------------------------------------------
#include "TSlotMachineSAS_ComSA_CNT_EXT.h"

#include "TSlotMachineSAS_ComSA_Common.h" /*_COMMON mora biti v .cpp*/
#include "enums/LongPolls.h"
#include "sas_meters/SASMetersController.h"

//---------------------------------------------------------------------------
int TSA_GetCountersExtended_SEND::OnEnter()    // tukaj rutamo v Normal Counters, ce je prislo do napake, da smo prisli sem
{
	if (SLOT->AFTModule->isAFTEpoch(true))
	{
		//	SetNext(std::make_shared<TSA_IdleState>(GetSA()));    // gremo poservisirat AFT, ker ima prednost
		return 0;
	}

	SLOT->SetName("TSA_GetCountersExtended_SEND Slot " + std::to_string(SLOT->GetAddress()));
	SetStateID(1, "####%d:GetCountersExtended SEND", SLOT->GetAddress());
	return 0;
}
OnExecuteStatus TSA_GetCountersExtended_SEND::OnExecute()
{
	/*
	 * If we are fetching counters in more than just a single poll, we need to change
	 * the command between 0x6F and 0xAF, otherwise the Slot machine will consider this
	 * as "an implied negative acknowledgment (NACK) telling the gaming machine to re-send the requested information."
	 * Copied from SAS documentation, paragraph 3.2.
	 */
	// if SPLIT mode, we use only 6F - amatic board does not recognise AF, only 6F
	if ((SLOT->_ExtenderMetersSequenceId++) % 2 ||
	    EMedCountersMode::SPLIT == SLOT->FetchCountersMode)    // to allow consecutive meter polls - ker sicer misli, da gre za ponovljen paket
		AppendChar(ELongPoll::SendExtendedMetersForGameN); /* Command - Send Extended Meters For Game N */
	else
		AppendChar(ELongPoll::SendExtendedMetersForGameN_AF); /* Command - Send Extended Meters For Game N */

	AppendChar(2 + 2 * mCountersInfo.size());    // 2 bytes for game number + number of counter to poll * 2
	AppendBCD("0", 2);    // Game 0 - SLOT TOTAL
	// Add counters
	for (const auto counterId : mCountersInfo) AppendInt(counterId, 2);

	SLOT->SASSendReceiveBinHEX(m_Buf, true, true, 1);

	return OnExecuteStatus::HANDLED;
}

OnEventStatus TSA_GetCountersExtended_SEND::OnEvent(const std::shared_ptr<TSASMessage> message)
{
	switch (CheckExpectingData(ELongPoll::SendExtendedMetersForGameN, ELongPoll::SendExtendedMetersForGameN_AF, message))
	{
		case StateCheckResult::FAILED: return OnEventStatus::UNRECOGNIZED;
		case StateCheckResult::EMPTY_IGNORED: return OnEventStatus::SKIPPED_EMPTY;
		case StateCheckResult::PASSED: break;
		default:;
	}

	// Setting initial length to predefined constant
	// m_Len = SAS_MAX_PCT_LEN;

	unsigned short MeterIndex = 5;
	unsigned short SASPacketLength = message->mHexResponse[2];
	int MoreCountersAvailable = SASPacketLength - 2;    // Length is bytes following lenbyte, -2 because of gameid
	std::vector<uint16_t> gotMeters;
	while (MoreCountersAvailable > 0)
	{
		uint64_t temp64_1 = 0;
		bool meterRead = false;
		auto MeterCode = message->mHexResponse[MeterIndex];
		auto MeterSize = message->mHexResponse[MeterIndex + 2];

		if (MeterSize > 0)
		{    // Meter size can be zero

			// Read the meter value from HexResponse, converting from BCD
			temp64_1 = ConvertBCDToUInt64(message->mHexResponse, MeterIndex + 3, MeterSize);
			meterRead = true;
		}
		else
		{
			TLOG(LogSASComm, Error, "No responce on Meter 0x%02X", MeterCode);
		}


		MoreCountersAvailable -= MeterSize;
		MoreCountersAvailable -= 3;    // Subtract MeterCode and MeterSize
		MeterIndex += 3;    // Advance for MeterCode and MeterSize
		MeterIndex += MeterSize;    // Advance for meter data
		gotMeters.push_back(static_cast<uint16_t>(MeterCode));
		// Set the counter value to the controller
		SASMetersController::GetInstance().SetCounterValue(ESASMeters::_from_integral_nothrow(MeterCode).value(), temp64_1, meterRead);
	}

	for (const auto counterId : mCountersInfo)
	{
		if (std::find(gotMeters.begin(), gotMeters.end(), counterId) == gotMeters.end())
		{
			// If we didn't get the counter we requested, we need to request it via long poll 0x2F
			SASMetersController::GetInstance().SetCounterValue(ESASMeters::_from_integral_nothrow(counterId).value(), 0, false);
			TLOG(LogSASComm, Error, "No responce on Meter 0x%02X", counterId);
		}
	}

	/*if (OnEventResult)
	    OnEventResult(OnEventStatus::HANDLED);*/

	return OnEventStatus::HANDLED;
}



//-------------------------------------------     TSA_GetCounters_2F_SEND	 -------------------------------------------

int TSA_GetCounters_2F_SEND::OnEnter()    // tukaj rutamo v Extended Counters
{
	SLOT->SetName("TSA_GetCounters_SEND Slot " + std::to_string(SLOT->GetAddress()));
	SetStateID(1, "####%d:GetCounters SEND", SLOT->GetAddress());
	return 0;
}

OnExecuteStatus TSA_GetCounters_2F_SEND::OnExecute()
{
	// memset(m_Buf, 0, SAS_MAX_PCT_LEN);
	// m_Len = 0;

	AppendChar(ELongPoll::SendSelectedMetersForGameN); /* Command - Send Selected Meters For Game N */
	AppendChar(2 + mCountersInfo.size());    // 2 bytes for game number + number of counter to poll
	AppendBCD("0", 2);    // Game 0 - SLOT TOTAL
	// Add counters
	for (const auto counterId : mCountersInfo) AppendChar(counterId);

	SLOT->SASSendReceiveBinHEX(m_Buf, true, true);
	return OnExecuteStatus::HANDLED;
}

OnEventStatus TSA_GetCounters_2F_SEND::OnEvent(const std::shared_ptr<TSASMessage> message)
{
	switch (CheckExpectingData(ELongPoll::SendSelectedMetersForGameN, message))
	{
		case StateCheckResult::FAILED: return OnEventStatus::UNRECOGNIZED;
		case StateCheckResult::EMPTY_IGNORED: return OnEventStatus::SKIPPED_EMPTY;
		case StateCheckResult::PASSED: break;
		default:;
	}

	uint64_t temp64_1 = 0;
	std::vector<uint16_t> gotMeters;
	// m_Len = SAS_MAX_PCT_LEN;
	unsigned char MeterCode = 0;
	unsigned short MeterIndex = 5;
	// MyUtils::ASCIIHexToBinHex(AsciiResponse, (unsigned char*)m_Buf, (unsigned int*)&m_Len);
	unsigned short SASPacketLength = message->mHexResponse[2];
	int MoreCountersAvailable = SASPacketLength - 2;    // Length je bytes folowing lenbyte, 2 zaradi gameid
	// bool TotalDropMeterRecived = false;
	while (MoreCountersAvailable > 0)
	{
		temp64_1 = 0;
		bool meterRead = false;
		MeterCode = message->mHexResponse[MeterIndex];
		MeterIndex++;    // pove�amo �e za MeterCode byte
		MoreCountersAvailable -= SAS_METER_LENGTH[MeterCode];    // zmanj�amo za ustrezno dol�ino
		MoreCountersAvailable--;    // zmanjsamo za MeterCode byte


		// temp64_1 = std::stoll(((std::string)AsciiResponse).substr((i * 10) + 2, SAS_METER_LENGTH[MeterCode] * 2).c_str());
		temp64_1 = ConvertBCDToUInt64(message->mHexResponse, MeterIndex, SAS_METER_LENGTH[MeterCode]);
		meterRead = true;
		gotMeters.push_back(static_cast<uint16_t>(MeterCode));
		MeterIndex += SAS_METER_LENGTH[MeterCode];    // pove�amo za ustrezno dol�ino
		SASMetersController::GetInstance().SetCounterValue(ESASMeters::_from_integral_nothrow(MeterCode).value(), temp64_1, meterRead);
	}

	for (const auto counterId : mCountersInfo)
	{
		if (std::find(gotMeters.begin(), gotMeters.end(), counterId) == gotMeters.end())
		{
			// If we didn't get the counter we requested, we need to request it via long poll 0x2F
			SASMetersController::GetInstance().SetCounterValue(ESASMeters::_from_integral_nothrow(counterId).value(), 0, false);
			TLOG(LogSASComm, Error, "No responce on Meter 0x%02X", counterId);
		}
	}

	return OnEventStatus::HANDLED;
}
