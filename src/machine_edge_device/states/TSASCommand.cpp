//
// Created by sviatoslavs on 27.11.24.
//

#include "TSASCommand.h"

int TSASCommand::AppendChar(const uint8_t c, uint8_t nCount /* = 1*/)
{
	int nResult = true;

	while (nCount > 0)
	{
		// m_Buf[m_Len++] = c;
		m_Buf.push_back(c);
		nCount--;
	}
	return nResult;
}
/* Appends buffer to packet       */
/* if not sucsesfull return false */
int TSASCommand::AppendBuf(const uint8_t* pBuf, const uint8_t nLen)
{
	for (int i = 0; i < nLen; i++) AppendChar(*(pBuf + i));
	return true;
}

/* Converts nValue to nBufLen length BCD MSB first */
/* if not sucsesful returns false */
int TSASCommand::ToBCD(uint64_t nValue, uint8_t* pBuf, const uint8_t nBufLen)
{
	int i, nDigit;
	// clear buffer
	for (i = 0; i < nBufLen; i++) *(pBuf + i) = 0;

	for (i = nBufLen * 2 - 1; i >= 0; i--)
	{
		nDigit = nValue % 10;
		nValue = nValue / 10;
		// shift right every second digit
		if (i % 2 == 0)
			nDigit = nDigit << 4;
		// put the digit in buffer
		*(pBuf + (i / 2)) |= nDigit;
		// the whole number was converted
		if (nValue == 0)
			return true;
	}
	return true;
}

/* Appends string value as BCD, nBCDLen is number of appended bytes */
/* Missing leading 0 are appended                                   */
/* 0 is appendded if invalid data in string                         */
int TSASCommand::AppendBCD(TString sStr, const uint8_t nBCDLen)
{
	unsigned char locBuf[32];
	TString s2Digits;
	uint64_t uValue, i;
	int nResult = true;
	unsigned int nLen;

	if (nBCDLen <= 0)
		return true;

	// trim sStr if to long
	if ((nLen = sStr.length()) > nBCDLen * 2)
		sStr = sStr.Mid(nLen - nBCDLen * 2);

	nLen = sStr.length();
	// Prepend zeros if sStr to short
	sStr = std::string(nBCDLen * 2 - nLen, '0') + sStr;

	for (i = 0; i < nBCDLen; i++)
	{
		s2Digits = sStr.Mid(i * 2, 2);
		if (yutils::strToUInt2(s2Digits.c_str(), uValue) && ToBCD(uValue, locBuf, 1))
		{
			AppendBuf(locBuf, 1);
		}
		else
		{
			AppendChar(0, 1);
			nResult = false;
		}
	}
	return nResult;
}
/*inline int AppendBCD(int value, unsigned int nBCDLen)
{
    unsigned char locBuf[32];
    TString sStr = std::to_string(value);
    uint32_t uValue;
    int nResult = true;
    unsigned int nLen;

    if (nBCDLen <= 0)
        return true;

    // trim sStr if too long
    if ((nLen = sStr.length()) > nBCDLen * 2)
        sStr = sStr.substr(nLen - nBCDLen * 2);

    nLen = sStr.length();
    // Prepend zeros if sStr is too short
    sStr = std::string(nBCDLen * 2 - nLen, '0') + sStr;

    for (unsigned int i = 0; i < nBCDLen; i++)
    {
        TString s2Digits = sStr.substr(i * 2, 2);
        if (yutils::strToUInt2(s2Digits.c_str(), uValue) && ToBCD(uValue, locBuf, 1))
        {
            AppendBuf(locBuf, 1);
        }
        else
        {
            AppendChar(0, 1);
            nResult = false;
        }
    }
    return nResult;
}*/

int TSASCommand::AppendBCD(const uint64_t value, const uint8_t nBCDLen)
{
	int nResult = true;

	if (nBCDLen <= 0)
		return true;

	for (unsigned int i = 0; i < nBCDLen; i++)
	{
		// Extract two digits from the value
		uint32_t uValue = (value / static_cast<uint64_t>(pow(10, (nBCDLen - 1 - i) * 2))) % 100;
		unsigned char bcdValue = ((uValue / 10) << 4) | (uValue % 10);

		if (!AppendChar(bcdValue))
		{
			AppendChar(0, 1);
			nResult = false;
		}
	}
	return nResult;
}

/* Append unsigned int binary LSB */
int TSASCommand::AppendInt(uint32_t n, const uint8_t nLen)
{
	for (int i = 0; i < nLen; i++)
	{
		m_Buf.push_back(n % 256);
		// m_Buf[m_Len++] = n % 256;
		n = n / 256;
	}
	return true;
}
