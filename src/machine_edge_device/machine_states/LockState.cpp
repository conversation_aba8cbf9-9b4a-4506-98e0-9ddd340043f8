//
// Created by sviatoslavs on 22.08.24.
//
#include "machine_states/LockState.h"

#include <format>

#include "TSlotMachineSAS.h"

void LockState::ActivationLogic(const uint8_t stateToActivate, uint8_t howManyTry, bool force)
{
	if (stateToActivate == ELockState::LOCKED)
	{
		mSlotMachine->RequestLockSlot(std::format("lock requested from cms"), [this](const bool success) {
			if (success)
			{
				Success();
			}
			else
			{
				Fail();
			}
		});
	}
	else
	{
		mSlotMachine->RequestUnlockSlot([this](const bool success) {
			if (success)
			{
				Success();
			}
			else
			{
				Fail();
			}
		});
	}
}

ELockState LockState::GetState() const
{
	return ELockState::_from_integral(mCurrentState.load());
}
bool LockState::SetState(const ELockState stateToActivate, const uint8_t howManyTry /* = 1*/, const bool force /* = false*/)
{
	return BaseMachineState::SetState(stateToActivate, howManyTry, force);
}
