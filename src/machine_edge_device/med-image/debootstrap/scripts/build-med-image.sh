#!/bin/sh -e

RED="\e[31m"
GREEN="\e[32m"
YELLOW="\e[33m"
BLUE="\e[34m"
RESET="\e[0m"

# Check if we are running as root
if [ $(id -u) -ne 0 ]
then
	echo "This script should be run as ${BLUE}root${RESET} user."
	return 1
fi

script_dir=$(dirname "$(readlink -f "$0")")
package_dir=$(readlink -f "${script_dir}/../packages")
mount_dir=$(readlink -f "${script_dir}/../image-root")


# Get configuration values
config_file=${script_dir}/config.sh
if [ ! -f ${config_file} ]
then
	echo "Configuration file ${config_file} ${RED}does not exists${RESET}, aborting."
	return 1
fi
. ${config_file}

create_image_file() {
	image_file=$1
	if [ -z "${image_file}" ]
	then
		return 1
	fi

	# Create 3GB image file
	dd if=/dev/zero of=${image_file} bs=1M count=3072

	# Set MSDOD partition table and create two partitions, boot and rootfs
	parted ${image_file} mktable msdos 
	parted ${image_file} mkpart primary fat32 2048s 512MB
	parted ${image_file} mkpart primary ext4 512MB 100%
	parted ${image_file} set 1 boot on

	# Mount those partitions so we can set up the system
	if [ -d "${mount_dir}" ]
	then
		mkdir "${mount_dir}"
	fi
		
	loop_dev=$(losetup -Pf --show ${image_file})
	mount_source=$(basename ${loop_dev})
	kpartx -av "${loop_dev}"
	mkfs.vfat -F 32 -n "system_boot" /dev/mapper/${mount_source}p1
	mkfs.ext4 -L "writable" /dev/mapper/${mount_source}p2
	mount /dev/mapper/${mount_source}p2 "${mount_dir}"
	mkdir -p "${mount_dir}/boot/firmware"
	mount /dev/mapper/${mount_source}p1 "${mount_dir}/boot/firmware"
}


install_base_system() {
	arch=arm64
	echo -n "Installing base system for ${BLUE}${arch}${RESET} architecture...."
	debuutstrap --arch ${arch} ${UBUNTU_RELEASE} "${mount_dir}"
	echo "${GREEN} Done${RESET}"
}

install_custom_files() {
	# Copy custom configuration and startup files
	cp "${script_dir}/target/files/sudoers" "${mount_dir}/etc/."
	cp "${script_dir}/target/files/rsyslog.logrotate" "${mount_dir}/etc/logrotate.d/rsyslog"
	cp "${script_dir}/target/files/med-app.logrotate" "${mount_dir}/etc/logrotate.d/med-app"
	cp "${script_dir}/target/files/50-eth0-dhcp.yaml.netplan" "${mount_dir}/etc/netplan/50-eth0-dhcp.yaml"
	cp "${script_dir}/target/files/syslog.conf.journald" "${mount_dir}/usr/lib/systemd/journald.conf.d/syslog.conf"
	cp "${script_dir}/target/files/size.conf.journald" "${mount_dir}/usr/lib/systemd/journald.conf.d/size.conf"
	cp "${script_dir}/target/files/timesyncd.conf.systemd" "${mount_dir}/etc/systemd/timesyncd.conf"
	cp "${script_dir}/target/files/tronius-launcher.service.systemd" "${mount_dir}/etc/systemd/system/tronius-launcher.service"
	cp "${script_dir}/target/files/fstab" "${mount_dir}/etc/fstab"

	# Add additional pools to DEB sources
	sed -i 's/$/ universe restricted multiverse/' "${mount_dir}/etc/apt/sources.list"
}

install_docker_files() {
	# Make sure that you are logged in into Tronius docker registry
	container_id=$(docker create ${TRONIUS_DOCKER_URL})

	# Install libraries build with Docker platform build
	# NOTE: We cannot use wildcards like * when copying files from container
	#       but we need to specify exact name. When could use a docker exec
	#       command and list all the files and then in a for loop copy those
	#       files. For now we will use exact versions of files.
	docker cp ${container_id}:"/usr/local/lib/libmodbus.so.5.1.0" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libgpiod.so.2.2.3" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libgpiodcxx.so.1.1.2" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libSDL2-2.0.so.0.3000.7" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libtbb.so.12.11" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libboost_filesystem.so.1.83.0" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libboost_thread.so.1.83.0" "${mount_dir}/usr/local/lib/."
	docker cp ${container_id}:"/usr/local/lib/libboost_chrono.so.1.83.0" "${mount_dir}/usr/local/lib/."

	# Create needed symbolic links
	cd "${mount_dir}/usr/local/lib/" && \
		ln -s libmodbus.so.5.* libmodbus.so.5 && \
		ln -s libgpiod.so.2.* libgpiod.so.2 && \
		ln -s libgpiodcxx.so.1.* libgpiodcxx.so.1 && \
		ln -s libSDL2-2.0.so.0.3000.* libSDL2-2.0.so.0 && \
		ln -s libtbb.so.12.* libtbb.so.12

	# Remove Docker container, we do not need it an more
	docker rm ${container_id}
}

setup_target_system() {
	cmd="$1"
	if [ -z "${cmd}" ]
	then
		return 1
	fi

	# Copy the script into newly created root file system that will execute
	# command inside new root file system
	if [ ! -f "${mount_dir}/root/setup_target_system.sh" ]
	then
		cp "${script_dir}/target/setup_target_system.sh" "${mount_dir}/root/."
	fi

	# Change to new system and execute the script
	# NOTE: We are using systemd-nspawn instead of chroot since it is more powerful
	#       and does not have problems as chroot on systemd enabled systems
	systemd-nspawn -D ${mount_dir} /root/setup_target_system.sh
}

package_helper() {
	# If called without arguments, just unmount the mounted pack
	# Otherwise we need two parameters, package to install and remote path
	# from the depo name forward

	if [ $# -eq 0 ]
	then
		umount "${package_dir}/mount"
		return 0
	fi

	if [ $# -ne 2 ]
	then
		echo "Please specify the package name and remote URL"
		return 1
	fi

	pack_name=$1
	pack_remote_url=$2

	if [ ! "-d ${package_dir}" ]
	then
		mkdir -p "${package_dir}"
	fi
	if [ ! -d "${package_dir}/download" ]
	then
		mkdir -p "${package_dir}/download"
	fi
	if [ ! -d "${package_dir}/mount" ]
	then
		mkdir "${package_dir}/mount"
	fi

	# If the package file exists, we assume it is the same one
	# and we do not download the file again
	if [ ! -f "${package_dir}/download/${pack_name}.pack" ]
        then
                curl -o "${package_dir}/download/${pack_name}.pack" "${PACKAGES_URL}/${PACKAGES_DEPO}/${pack_remote_url}/${pack_name}.pack"
        fi

	mount "${package_dir}/download/${pack_name}.pack" "${package_dir}/mount"
}

install_package_core() {
	package_helper "${PACKAGES_CORE_VERSION}" "core/core"

	# From core package we only need Python script for launching
	# software and its dependencies
	cp "${package_dir}/mount/launchSoftware.py" "${mount_dir}/home/<USER>/."
	cp "${package_dir}/mount/pack_utils.py" "${mount_dir}/home/<USER>/."

	package_helper
}

install_package_launcher() {
	package_helper "${PACKAGES_LAUNCHER_VERSION}" "core/launcher"
	
	# We copy everything from the Launcher package
	# NOTE: We cannot use "" around source directory, since bash will expand
	#       * to the actual files! If we use it, cp command will fail!
	cp -a ${package_dir}/mount/* "${mount_dir}/opt/tronius/launcher/."
	
	package_helper
}

copy_boot_files() {
	cp -a ${mount_dir}/lib/firmware/6.8.0-*/device-tree/overlays "${mount_dir}/boot/firmware/."
	cp -a ${mount_dir}/lib/firmware/6.8.0-*/device-tree/broadcom/* "${mount_dir}/boot/firmware/."
	cp ${mount_dir}/boot/vmlinuz "${mount_dir}/boot/firmware/."
	cp "${script_dir}/target/files/boot/config.txt" "${mount_dir}/boot/firmware/." 
	cp "${script_dir}/target/files/boot/cmdline.txt" "${mount_dir}/boot/firmware/." 
}

create_image_file "./test.img"
#install_base_system
#install_custom_files
#install_docker_files
setup_target_system user
#install_package_core
#install_package_launcher
setup_target_system finalize


