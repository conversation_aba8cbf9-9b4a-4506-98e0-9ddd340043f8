#include "Internet.h"

#include <boost/asio.hpp>
#include <boost/bind/bind.hpp>
#include <iostream>
#include <istream>
#include <ostream>

using boost::asio::deadline_timer;
using boost::asio::ip::icmp;
namespace posix_time = boost::posix_time;

class pinger
{
   public:
	pinger(boost::asio::io_context& io_context, const char* destination) : resolver_(io_context), socket_(io_context, icmp::v4()), timer_(io_context), sequence_number_(0)
	{
		icmp::resolver::query query(icmp::v4(), destination, "");
		destination_ = *resolver_.resolve(query);

		start_send();
		start_receive();
	}

	bool ok() const { return bGotReply; }

   private:
	void start_send()
	{
		std::string body("Meep map");

		// Create an ICMP header for an echo request.
		icmp_header echo_request;
		echo_request.type(icmp_header::echo_request);
		echo_request.code(0);
		echo_request.identifier(get_identifier());
		echo_request.sequence_number(++sequence_number_);
		compute_checksum(echo_request, body.begin(), body.end());

		// Encode the request packet.
		boost::asio::streambuf request_buffer;
		std::ostream os(&request_buffer);
		os << echo_request << body;

		// Send the request.
		time_sent_ = posix_time::microsec_clock::universal_time();
		socket_.send_to(request_buffer.data(), destination_);

		// Wait up to five seconds for a reply.
		bGotReply = false;
		timer_.expires_at(time_sent_ + posix_time::seconds(5));
		timer_.async_wait(std::bind(&pinger::handle_timeout, this));
	}

	void handle_timeout() { socket_.close(); }

	void start_receive()
	{
		// Discard any data already in the buffer.
		reply_buffer_.consume(reply_buffer_.size());

		// Wait for a reply. We prepare the buffer to receive up to 64KB.
		socket_.async_receive(reply_buffer_.prepare(65536), std::bind(&pinger::handle_receive, this, std::placeholders::_2));
	}

	void handle_receive(std::size_t length)
	{
		// The actual number of bytes received is committed to the buffer so that we
		// can extract it using a std::istream object.
		reply_buffer_.commit(length);

		// Decode the reply packet.
		std::istream is(&reply_buffer_);
		ipv4_header ipv4_hdr;
		icmp_header icmp_hdr;
		is >> ipv4_hdr >> icmp_hdr;

		// We can receive all ICMP packets received by the host, so we need to
		// filter out only the echo replies that match the our identifier and
		// expected sequence number.
		if (is && icmp_hdr.type() == icmp_header::echo_reply && icmp_hdr.identifier() == get_identifier() && icmp_hdr.sequence_number() == sequence_number_)
		{
			bGotReply = true;
			timer_.cancel();
			socket_.close();
		}

		start_receive();
	}

	static unsigned short get_identifier()
	{
#if defined(BOOST_WINDOWS)
		return static_cast<unsigned short>(::GetCurrentProcessId());
#else
		return static_cast<unsigned short>(::getpid());
#endif
	}

	icmp::resolver resolver_;
	icmp::endpoint destination_;
	icmp::socket socket_;
	deadline_timer timer_;
	unsigned short sequence_number_;
	posix_time::ptime time_sent_;
	boost::asio::streambuf reply_buffer_;
	bool bGotReply = false;
};

bool TInternetUtils::CheckConnection(const std::string& target, std::string& outError)
{
	try
	{
		boost::asio::io_context io_context;
		pinger p(io_context, target.c_str());
		io_context.run_one();
		if (p.ok())
			return true;

		outError = "Request timed out";
	}
	catch (std::exception& e)
	{
		outError = e.what();
	}

	return false;    // no internet
}