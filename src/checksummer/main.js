const path = require('path');

var workingDir = path.dirname(process.argv[1]);
console.log("Working in directory: " + workingDir);

var myArgs = process.argv.slice(2);
var defaultPackageDir = "/var/IGP/packages/**.pack"
if(myArgs.length == 0)
	console.log("No package directory provided, using default: " + defaultPackageDir);
else
{
	defaultPackageDir = myArgs[0];
	console.log("Using package directory: " + defaultPackageDir);
}

const Express = require('express');

const objHash = require('object-hash');
const fb = require('node-firebird');
const semaphore = require('semaphore');

const querystring = require('querystring');

var options = {};
 
options.host = '127.0.0.1';
options.port = 3050;
options.database = 'PC_DATA.GDB';
options.user = 'READONLY';
options.password = 'passmein';
options.lowercase_keys = false;
options.role = null;            // default
options.pageSize = 4096;        // default when creating database

const md5File = require('md5-file');
const app = new Express();

const checksumFile = "/tmp/checksums.csv";

app.use(Express.static(path.join(workingDir,'public')));

app.get('/', function(req, res) {
	res.sendFile('public/welcome.html', { root: workingDir });
});

const fs = require('fs');

const getChecksumsIn = function (folder, startswith="", endsWith="", propagate=true, exclude=[])
{
	if(!fs.existsSync(folder))
		return [];
	var out = [];
	fs.readdirSync(folder).forEach(function (file) {
		// Make one pass and make the file complete
		var filepath = path.join(folder, file);

		if(!fs.existsSync(filepath))
			return;
	
		stat = fs.statSync(filepath);

		const hasExt = (startswith.length > 0) || (endsWith.length > 0);

		if (stat.isFile())
		{
			let fileN = path.parse(filepath).base;
			if( !hasExt || (startswith.length && fileN.startsWith(startswith)) || (endsWith.length && fileN.endsWith(endsWith)))
			{
				console.log('    Doing MD5 on: ' + filepath);
				out.push({name:filepath, md5:md5File.sync(filepath)});
			}
		}else if(stat.isDirectory() && propagate)
		{
			var ok = true;
			exclude.forEach(element => {
				ok = ok && !filepath.match(element);
			});
			if(ok)
			{
				console.log('    Diving into dir: ' + filepath);
				out = out.concat(getChecksumsIn(filepath, startswith, endsWith, propagate, exclude));
			}
		}
	});
	return out;
}

app.get('/download', function(req, res) {
	var packName = req.query.p;
	var sql = req.query.db;
	if(packName != undefined){
		console.log("Got download request for " + packName);
		var stat = fs.statSync(packName);
		if(stat.isFile())
		{
			res.download(packName);
			return;
		}else{
			res.writeHead(404);
		}
		res.end();
	}else if(sql != undefined)
	{
		console.log("Getting from db: " + sql);
			
		dbquery = querystring.parse(sql);
		if(dbquery.length == 0)
		{
			console.log("Empty DB checks!");
			res.writeHead(404);
			res.end();
			return;
		}
		var sem = semaphore(1);

		var results = [];

		let sql = 'SELECT * FROM ' + dbquery.table;
		if(dbquery.where)
			sql += ' WHERE ' + dbquery.where;
		sql += ";"
		console.log("Opening DB for querry " + sql);
		sem.take(function(){
		fb.attach(options, function(sql, tablename, err, db) {
			if (err)
			{
				this.resultArray.push({name:tablename, md5:"DB ERROR"});
				return;
			}

			db.on('result', function(sql, tablename, result) {
				console.log("SQL '" + sql + "' OK!");
				this.resultArray.push(result);
				db.detach();
				this.ondone.leave();
			}.bind(this, sql, tablename));

			console.log("Running querry " + sql);
			db.query(sql, [], function(tablename, err, result) {
				if(err)
				{
					console.log("SQL ERROR!");
					this.resultArray.push({name:tablename, md5:"ERROR"});
				}else{
					console.log("SQL OK! " + JSON.stringify(result));
					this.resultArray.push(result);
				}
				try{
					this.ondone.leave();
				}catch(d)
				{}
				db.detach();
			}.bind(this, tablename));
		}.bind({ondone:sem, query:sql, response:res, resultArray:results}, sql, dbquery.table));
		}.bind(this));
		sem.take(function(){
			console.log("REPLYING!");
			res.json(results[0]);
			res.end();
		}.bind(this));
	}else{
		console.log("Bad download request");
		res.writeHead(400);
		res.end();
	}
});

app.get('/checksums', function(req, res) {

	console.log("Got download request for checksums");
	var stat = fs.statSync(checksumFile);
	if(stat.isFile())
	{
		res.download(checksumFile);
		return;
	}else{
		res.writeHead(404);
	}
	res.end();
});

reply = function(response, results) {
	console.log(`Got ${results.length} checksums.`);

	var fileContent = "File,MD5 Checksum\n";
	results.forEach(element => {
		fileContent += element.name + "," + element.md5 + "\n";
	});

	fs.writeFileSync(checksumFile, fileContent, 
	{ 
		encoding: "utf8", 
		flag: "w", 
		mode: 0o666 
	});


	fs.readFile('public/results.html', "utf8", function(err, page) {
		response.writeHead(200, {'Content-Type': 'text/html'});
		var lines = ""
		if(results.length)
		{
			results.forEach(element => {
				var downloadStyle;
				if(element.sqlQuery != undefined)
					downloadStyle = "db=" + querystring.escape(element.sqlQuery);
				else
					downloadStyle = "p=" + element.name;
				lines += `<tr><td>${element.name}</td><td>${element.md5}</td><td><a class="buttonstyle button-small" href="download?${downloadStyle}" rel="nofollow noopener" download><span>Download</span></a></td></tr>`;
			});
		}else
			lines = "<tr><td>no data</td><td>NaN</td><td></td></tr>"
		response.write(page.replace("${tablelines}", lines));
		response.end();
	});
};

app.get('/calculate', function(req, res) {
	var packNamesAndDirectories = req.query.p;
	if(packNamesAndDirectories == undefined)
	{
		packNamesAndDirectories = [defaultPackageDir];
	}else{
		packNamesAndDirectories = req.query.p.split(';');
	}

	var exclude = req.query.e;
	if(exclude == undefined)
	{
		exclude = [];
	}else{
		exclude = req.query.e.split(';');
	}

	//joining path of directory 
	var results = [];
	packNamesAndDirectories.forEach(pathName => {
		console.log("!! Checking path " + pathName);
		
		var filename = path.parse(pathName).base;
		var startsWith = "";
		var endsWith = "";
		var propagate = false;
		if(filename.startsWith("*"))
		{
			propagate = filename.startsWith("**");
			endsWith = filename.substr(propagate ? 2 : 1);
		}
		else if(filename.endsWith("*"))
		{
			propagate = filename.endsWith("**");
			startsWith = filename.substr(0, filename.length - propagate ? 2 : 1);
		}
		else if(!fs.existsSync(pathName))
			return;
		
		if(startsWith.length || endsWith.length)
		{
			const dir = pathName.substr(0, pathName.length - filename.length);
			console.log(`!! Going through directory  ${dir} with filters '${startsWith}', '${endsWith}'.`);
			results = results.concat(getChecksumsIn(dir, startsWith, endsWith, propagate, exclude));
		}else{
			stat = fs.statSync(pathName);
			if(stat.isDirectory(pathName))
			{
				var ok = true;
				exclude.forEach(element => {
					ok = ok && !pathName.match(element);
				});
				if(ok)
				{
					console.log("!! Going through directory " + pathName);
					results = results.concat(getChecksumsIn(pathName, "", "", true, exclude));
				}
			}
			else if(stat.isFile(pathName))
			{
				console.log('!! Doing MD5 on: ' + pathName);
				let hash = md5File.sync(pathName);
				results.push({name:pathName, md5:hash});
			}
		}
	});
	
	if(req.query.db)
	{
		console.log("db start");
			
		dbchecks = JSON.parse('{"array":' + req.query.db + '}').array;
		if(dbchecks.length == 0)
		{
			console.log("Empty DB checks!");
			reply(res, results);
			return;
		}
		var sem = semaphore(1);

		dbchecks.forEach(checkObj =>
		{
			let sql = 'SELECT * FROM ' + checkObj.table;
			if(checkObj.where)
				sql += ' WHERE ' + checkObj.where;
			sql += ";"
			console.log("Opening DB for querry " + sql);
			sem.take(function(){
			fb.attach(options, function(sql, tablename, err, db) {
				if (err)
				{
					this.resultArray.push({name:tablename, md5:"DB ERROR", sqlQuery:sql});
					return;
				}

				db.on('result', function(sql, tablename, result) {
					console.log("SQL '" + sql + "' OK!");
					let hash = objHash(result, {algorithm: 'md5', unorderedArrays:true});
					console.log("done! hash: " + hash);
					this.resultArray.push({name:tablename, md5:hash, sqlQuery:sql});
					db.detach();
					this.ondone.leave();
				}.bind(this, sql, tablename));

				console.log("Running querry " + sql);
				db.query(sql, [], function(tablename, err, result) {
					if(err)
					{
						console.log("SQL ERROR!");
						this.resultArray.push({name:tablename, md5:"ERROR", sqlQuery:sql});
					}else{
						console.log("SQL OK! " + JSON.stringify(result));
						let hash = objHash(result, {algorithm: 'md5', unorderedArrays:true});
						console.log("done! hash: " + hash);
						this.resultArray.push({name:tablename, md5:hash, sqlQuery:sql});
					}
					try{
						this.ondone.leave();
					}catch(d)
					{}
					db.detach();
				}.bind(this, tablename));
			}.bind({ondone:sem, query:req.query.db, response:res, resultArray:results}, sql, checkObj.table));
			}.bind(this));
		});
		sem.take(function(){
			console.log("REPLYING!");
			reply(res, results);
		});
	}else{
		console.log("No DB checks!");
		reply(res, results);
	}
});

function addResult(response, results, newname, newhash)
{
	results.contains(newname);
}

var port = 5555;
app.listen(port, () => console.log(`IGP checksummer started on http://localhost:${port}`))