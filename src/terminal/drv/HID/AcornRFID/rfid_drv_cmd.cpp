/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/


#include "drv/HID/AcornRFID/rfid.h"
#include "drv/HID/AcornRFID/rfid_l.h"


namespace RFID_drv
{
/*---------------------------------------------------------------------------------------------------
FUNCTION: GetKeyCode()

DESC:	The function fills the buffer with the currently insert key code. If there is no key the function
            returns -2;

PARAMS: hInst - instance handle
    KeyNumber - KeyNumber if key code matches one of key in configuration; if 0 the key code does not
            match
    pBuffer - pointer to buffer where the keycode will be written - must be at least E_KEY_DATA_LENGTH bytes long

RESULT: 0 success; 0< no key present; 0> failure
---------------------------------------------------------------------------------------------------*/
int GetKeyCode(void* hInst, short* KeyNumber, std::vector<uint8_t>& pBuffer)
{
	if (NULL == hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pKeyMutex);
	short tmpKeyIndex = pMyData->KeyIndex;
	SDL_UnlockMutex(pMyData->pKeyMutex);

	/* check if the key is present */
	if (tmpKeyIndex < 0)
		return 1;    // key not present


	/* return the key number */
	if (NULL != KeyNumber)
		*KeyNumber = tmpKeyIndex;

	/* return the key value */
	SDL_LockMutex(pMyData->pKeyMutex);
	for (int i = 0; i < RFID_KEYS_CODE_LENGTH; i++) pBuffer.push_back(pMyData->SerialNumber[i]);
	SDL_UnlockMutex(pMyData->pKeyMutex);
	return 0;
}

}    // namespace RFID_drv
