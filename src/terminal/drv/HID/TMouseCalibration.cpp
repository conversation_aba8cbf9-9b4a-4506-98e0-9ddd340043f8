/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include "drv/HID/TMouseCalibration.h"

#include <SDL2/SDL.h>
#include <math.h>

#include "Calibrator.h"
#include "MyUtils.h"
#include "TGuiApplication.h"

const std::string XINPUT_CONF_FILE = "99-IGP-touch.conf";

TMouseCalibration::TMouseCalibration(const TInitData& initInfo) : Calibrator(true)
{
	Calibrator::Load();

	Config = initInfo;

	pApp->OnComponentStatusChanged("touch.status", "OK");
	pApp->OnComponentStatusChanged("touch.version", "USB HID v1.0");
}

void TMouseCalibration::HandleRawInput(const MouseInputData& input_data)
{
	// const Uint32 timestamp = SDL_MOUSEMOTION == event.type ? event.motion.timestamp : event.button.timestamp;
	const Uint32 timestamp = input_data.timestamp;

	/*printf("Mouse event at: %d, %d\n", event.button.x, event.button.y);
	return 1;*/

	if (CalibrationStatus > 0 && CalibrationStatus < 10)
	{
		if (CalibrationStatus <= 4)
		{
			if (SDL_MOUSEBUTTONDOWN != input_data.eventType)    // don't accept mouse up/move as the first event!
			{
				if (!get_numclicks())
					return;

				const ClickedPoint& c = getClickPoint(get_numclicks() - 1);
				if (!c.Calibrated() && !c.NumDataPoints())
					return;
			}
		}

		bool bIsFinalPoint = false;
		if ((int)get_numclicks() == CalibrationStatus)
		{
			const ClickedPoint& c = getClickPoint(CalibrationStatus - 1);
			bIsFinalPoint = c.NumDataPoints() && SDL_MOUSEBUTTONUP == input_data.eventType;
		}

		if (SDL_MOUSEBUTTONDOWN == input_data.eventType || SDL_MOUSEBUTTONUP == input_data.eventType ||
		    (SDL_MOUSEMOTION == input_data.eventType && (input_data.buttonState & SDL_BUTTON_LMASK)))
		{
			if (!mLastClickTime && SDL_MOUSEBUTTONDOWN == input_data.eventType)
				mLastClickTime = timestamp;

			const ECalibrationClickResult res = add_click(input_data.mousePosition);
			if (res != ECalibrationClickResult::OK)
			{
				MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationError, (void*)(int)res, NULL);
				CalibrationStatus = 0;    // končamo
			}

			MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationTouchProgress, NULL, NULL);
		}

		// finish calibration for this point if we have at least one point mouse was released
		if (bIsFinalPoint)
		{
			finish_click();

			if (CalibrationStatus < 4)
			{
				if (SDL_MOUSEBUTTONUP == input_data.eventType)
				{
					MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationTouchPoint[CalibrationStatus], NULL, NULL);
					CalibrationStatus = (CalibrationStatus + 1) * 10;    // gremo v next point
					mLastClickTime = 0;
				}
			}
			else if (finishCalibration())
			{
				save_calibration();
				// sporocimo
				MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationSuccess, NULL, NULL);
				CalibrationStatus = 0;    // konec
			}
			else
			{
				MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationError, (void*)3, NULL);
				CalibrationStatus = 0;    // končamo
			}
		}
		else
		{
			if ((mLastClickTime && MyUtils::IsTime(mLastClickTime, 500)))
				MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationTouchPoint[CalibrationStatus - 1], (void*)1, NULL);
		}
	}
	else if (CalibrationStatus >= 10)
	{
		mLastClickTime = 0;
		CalibrationStatus /= 10;
	}
}

void TMouseCalibration::onCompletedCalibration()
{
	output_xorgconfd(XINPUT_CONF_FILE);
	CalibrationStatus = 0;
	pApp->OnComponentStatusChanged("touch.status", "OK");
}

uint32_t TMouseCalibration::set_calibration(const Mat9& coeff)
{
	const uint32_t res = Calibrator::set_calibration(coeff);

	bFallbackToResolutionScale = (res == 0);

	return res;
}

void TMouseCalibration::ResetCalibration()
{
	mLastClickTime = 0;
	reset();
}

void TMouseCalibration::CalibrateDevice()
{
	CalibrationStatus = 1;
	mLastClickTime = 0;
	reset(true);
	save_calibration();

	MyUtils::PostSDLEvent(Config.SDLEventCode_OnCalibrationTouchPoint[0], NULL, NULL);

	pApp->OnComponentStatusChanged("touch.status", "CALIBRATING");
}