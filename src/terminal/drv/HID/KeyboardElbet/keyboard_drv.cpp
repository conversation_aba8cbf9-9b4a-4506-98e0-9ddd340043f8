/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/

#include "drv/HID/KeyboardElBet/keyboard_drv.h"

#include <MyUtils.h>
#include <SDL2/SDL.h>
#include <TApplication.h>
#include <math.h>

#include "drv/HID/KeyboardElBet/keyboard_drv_l.h"


#define TIME_OUT                5000
#define MAX_SEND_RECIVE_RETRIES 3

namespace key_elbet_drv
{
int ReciveMessage(TMyData* pMyData, TMyMessage* pMessage);

//---------------------------------------------------------------------------------------------------
//------------------ SUPPORT FUNCTIONS --------------------------------------------------------------
void SetNextState(TMyData* pMyData, TpSAFunction pNextStateFunction)
{
	if (NULL == pMyData || NULL == pNextStateFunction)
		return;
	pMyData->PreviusState = pMyData->NextState;
	pMyData->NextState = pNextStateFunction;
}


int SetPreviousState(TMyData* pMyData)
{
	if (NULL == pMyData || pMyData->NextState == pMyData->PreviusState)
		return -1;

	pMyData->NextState = pMyData->PreviusState;

	return 0;
}

//---------------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------
//---- M A I N    S T A T E S -----------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------


/*---------------------------------------------------------------------------------------------------
FUNCTION: stateStatusRequest()

DESC:	SA funkcija, sproži posluša za evente iz tipkovnice

PARAMS: pMyData - handle na instanco komunikacijskega driverja

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int stateStatusRequest(TMyData* pMyData)
{
	typedef struct TStateData
	{
		Uint64 WatchDog;
		TMyMessage* pMessage;
	} TStateData;
	TStateData* pStateData;
	if (NULL == pMyData)
		return -1;
	if (NULL == pMyData->pStateData)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		((TStateData*)pMyData->pStateData)->WatchDog = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->pMessage = NULL;

		rtfwk_sdl2::pApp->OnComponentStatusChanged("keyboard.status", "OK");
		// TODO read version?
		rtfwk_sdl2::pApp->OnComponentStatusChanged("keyboard.version", "0.9");
	}
	pStateData = (TStateData*)pMyData->pStateData;
	/* ALI MORAMO POSLATI UKAZ */
	SDL_LockMutex(pMyData->pCommandQueueMutex);
	bool QueueEmpty = pMyData->CommandQueue.empty();
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	if (!QueueEmpty)
	{
		SDL_LockMutex(pMyData->pCommandQueueMutex);
		pStateData->pMessage = pMyData->CommandQueue.front();
		pMyData->CommandQueue.pop();    // brišemo, ker gre za nepomembne komande... //driver je hecal, zato sem to naredil
		SDL_UnlockMutex(pMyData->pCommandQueueMutex);
		if (SendMessage(pMyData, pStateData->pMessage)) {}
		if (pStateData->pMessage)
			delete (pStateData->pMessage);
		pStateData->WatchDog = SDL_GetTicks64();
	}
	else
	{
		if (ReciveMessage(pMyData, pMyData->pMessage) > 0)
		{
			//         printf("KEY %d\n", pMyData->pMessage->pData[0]);
			MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnKeyDown, (void*)KeyboardKeyCodes[pMyData->pMessage->pData[0]][0],
			                      (void*)KeyboardKeyCodes[pMyData->pMessage->pData[0]][1]);
			/* TODO - kaj delamo z lučkami na tipkovnici
			         switch ( pMyData->pMessage->pData[0] )
			         {
			            case 197: case 155: case 198: SetLed(pMyData, 1, 0,1,0); break;
			            case 131: SetLed(pMyData, 2, 0,1,0); break;
			            ....
			         }
			         LedsUpdate(pMyData);
			*/
		}
	}

	/* preverimo ali mogoče že preveč časa nismo prejeli veljavnega sporočila */
	if (pStateData->WatchDog + TIME_OUT < SDL_GetTicks64())
	{
		return 0;
	}

	return 0;
}



/*---------------------------------------------------------------------------------------------------
FUNCTION: MainInThread()

DESC:	Funkcija while true; ki teče v nitki in pogoanja StateAvtomat

PARAMS: hInst - handle na instanco komunikacijskega driverja
Device - pot do datoteke porta(/dev/ttyS0,...)

RESULT: 0 success; 0<> failurepKeyMutex
---------------------------------------------------------------------------------------------------*/
int MainInThread(ThInst hInst)
{
	if (NULL == hInst)
		return -1;

	TMyData* pMyData = (TMyData*)hInst;
	try
	{    // izvajamo se v nitki
		while (true)
		{
			/* kličemo next state */
			try
			{
				/* ali je aplikacija zahtevala zaključek nitke - ali lahko nadaljujemo z nitko */
				SDL_LockMutex(pMyData->pQuitThreadMutex);
				if (pMyData->QuitThread)
				{
					SDL_UnlockMutex(pMyData->pQuitThreadMutex);
					break;
				}
				SDL_UnlockMutex(pMyData->pQuitThreadMutex);

				pMyData->NextState(hInst);
				usleep(1000);
			}
			catch (...)
			{
			};
		}
	}
	catch (...)
	{
		return -1;
	}
	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: SendMessage()

DESC:	Funkcija zgradi sporocilo za prenos proti napravi in ga poÅ¡lje na serijski port
---------------------------------------------------------------------------------------------------*/
int SendMessage(TMyData* pMyData, TMyMessage* pMessage)
{
	if (pMyData->hSerial < 0)
		return -1;

	unsigned short BytesWritten = 0;
	unsigned short MessageLength = 0;
	/*
	    for ( int i=0; i<pMessage->DataLength; i++ )
	       pMessage->pData[pMessage->DataLength] += pMessage->pData[i];
	   pMessage->pData[pMessage->DataLength] = (pMessage->pData[pMessage->DataLength]%64)|0x40;
	    MessageLength = pMessage->DataLength+1;//CRC;	*/
	MessageLength = pMessage->DataLength;

	/* print the message - debug only */
	// for ( int i=0 ; i<MessageLength; i++ )
	//	printf("Byte%d = %X\n", i, (unsigned char)pMyData->buff[i]);

	int SendingWatchdog = 0;
	BytesWritten = 0;
	while (BytesWritten < MessageLength)
	{
		int tmp = write(pMyData->hSerial, (void*)&(pMessage->pData[BytesWritten]), MessageLength - BytesWritten);
		BytesWritten += tmp;
		if (SendingWatchdog++ > 5 || tmp < 1)
			return -2;
	}
	return BytesWritten;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ReciveMessage()

DESC:	Funkcija sprejme en byte iz serijskega porta, če je byte na voljo - čaka 1sec na podatek
---------------------------------------------------------------------------------------------------*/
int ReciveMessage(TMyData* pMyData, TMyMessage* pMessage)
{
	// UNUSED unsigned short BytesRead;

	if (NULL == pMyData || NULL == pMessage || pMyData->hSerial < 0)
		return -1;

	fd_set rfds;
	struct timeval tv;
	FD_ZERO(&rfds);
	FD_SET(pMyData->hSerial, &rfds);
	tv.tv_sec = 0;
	tv.tv_usec = 1;    // wait 100 ms

	if (select(pMyData->hSerial + 1, &rfds, NULL, NULL, &tv))
		read(pMyData->hSerial, (void*)&(pMessage->pData[0]), 1);
	else
		return -2;    // timeout*/

	pMessage->DataLength = 1;
	return 1;
}

int ComDriverInit(TMyData* pMyData)
{
	struct termios SerialPortOptions;
	bzero(&SerialPortOptions, sizeof(struct termios));
	SerialPortOptions.c_cflag = B9600 | CS8 | CLOCAL | CREAD;
	SerialPortOptions.c_oflag = 0;
	// SerialPortOptions.c_cc[VTIME]=10;/* 1 sec timeout za read */
	// SerialPortOptions.c_cc[VMIN]=8;

	pMyData->hSerial = open(pMyData->pSerialFileName, O_RDWR | O_NOCTTY | O_NONBLOCK);
	if (pMyData->hSerial < 0)
	{
		perror(pMyData->pSerialFileName);
		return -1;
	}
	/* nastavim nove nastavitve porta */
	tcflush(pMyData->hSerial, TCIOFLUSH);
	tcsetattr(pMyData->hSerial, TCSANOW, &SerialPortOptions);
	return 0;
}

void ComDriverDestroy(TMyData* pMyData)
{
	/* zaprem port */
	close(pMyData->hSerial);
	pMyData->hSerial = 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()

DESC:	Funkcija ustvari instanco KeyboardElbet in vrne handle - konstruktor

PARAMS: pSerialFileName - path to device file
                SDLEventCode_OnKeyDown - SDL message code for key down
                SDLEventCode_OnKeyUp - SDL message code for key up

RESULT: InstanceHandle or NULL if failure
---------------------------------------------------------------------------------------------------*/
ThInst Init(char* pSerialFileName, int SDLEventCode_OnKeyDown, int SDLEventCode_OnKeyUp, int SDLEventCode_OnCommunicationError)
{
	if (NULL == pSerialFileName)
		return NULL;
	TMyData* pMyData = new TMyData;
	try
	{
		/* pMyData init */
		pMyData->NextState = (TpSAFunction)stateStatusRequest;
		pMyData->PreviusState = NULL;

		while (!pMyData->CommandQueue.empty()) pMyData->CommandQueue.pop();
		pMyData->pCommandQueueMutex = SDL_CreateMutex();

		pMyData->SDLEventCode_OnKeyDown = SDLEventCode_OnKeyDown;
		pMyData->SDLEventCode_OnKeyUp = SDLEventCode_OnKeyUp;
		pMyData->SDLEventCode_OnCommunicationError = SDLEventCode_OnCommunicationError;

		pMyData->pMessage = new TMyMessage;
		memset(pMyData->pMessage->pData, 0, 36);
		pMyData->pMessage->DataLength = 0;

		memset(pMyData->LedBuffer, 0, 32);
		LedsUpdate(pMyData);    // ugasnemo vse ledice kot prvo stvar po zagonu nitke
		ProgressBarUpdate(pMyData, 100);

		rtfwk_sdl2::pApp->OnComponentStatusChanged("keyboard.version", "Connecting..");

		strncpy(pMyData->pSerialFileName, pSerialFileName, 100);

		pMyData->pStateData = NULL;

		ComDriverInit(pMyData);


		pMyData->QuitThread = false;
		pMyData->pQuitThreadMutex = SDL_CreateMutex();
		pMyData->pSAThread = SDL_CreateThread(MainInThread, "keyboard_drv", pMyData);
		//		printf("keyboard_drv: Thread started.\n");
	}
	catch (...)
	{
		if (!pMyData)
			delete (pMyData);
		return NULL;
	}
	return pMyData;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: Destroy()

DESC:	Funkcija izprazni resurse instance ebadrv - destruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja
TMess age - kazalec na strkturo s podatki iz sporočila

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
void Destroy(ThInst hInst)
{
	if (NULL == hInst)
		return;
	try
	{    // delete all mydata poiters

		//		printf("keyboard_drv: Waiting for thread to finish.\n");
		SDL_LockMutex(((TMyData*)hInst)->pQuitThreadMutex);
		((TMyData*)hInst)->QuitThread = true;
		SDL_UnlockMutex(((TMyData*)hInst)->pQuitThreadMutex);
		SDL_WaitThread(((TMyData*)hInst)->pSAThread, NULL);
		SDL_DestroyMutex(((TMyData*)hInst)->pQuitThreadMutex);
		//		printf("keyboard_drv: Thread terminated.\n");
		ComDriverDestroy((TMyData*)hInst);

		SDL_DestroyMutex(((TMyData*)hInst)->pCommandQueueMutex);

		delete ((TMyData*)hInst);
	}
	catch (...)
	{
	}
}

}    // namespace key_elbet_drv
