/***************************************************************************
 *   Copyright (C) 2006 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include <SDL2/SDL.h>
#include <TApplication.h>

#include "YUtils.h"
#include "common/TRGBAnimations.h"
#include "drv/system/IGPlatformV6/IGPboard_drv.h"
#include "drv/system/IGPlatformV6/IGPboard_drv_l.h"

namespace IGPboard
{
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetButtonLights()

DESC:	Funkcija nastavi luci na gumbih

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         LightActionID - light action ID is same as button id-s SW_1_OPEN, SW_1_CLOSE,...
         ActionsCount, LightActionID je lahko seznam uint32t, dolzino seznama doloca ActionsCount,
         ce je vec kot 1, se bodo poslali vsi v isti komandi proti kontrollerju

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetButtonLights(ThIGPboard hInst, const std::vector<ELightState>& LightCommands)
{
	if (!hInst)
		return -1;

	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock statuslock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -1;
	statuslock.unlock();

	std::vector<uint8_t> tmpbuf;
	tmpbuf.push_back(1);    // skupni pwm value, if 1, pwm will not change
	// TODO ! tmpbuf[0] = (LightActionID&0xFF000000)>>24; //PWM parameter

	for (uint8_t btnID = 0; btnID < magic::MAX_HARDWARE_BUTTONS; btnID++)
	{
		if (pMyData->LastButtonLights.empty() || pMyData->LastButtonLights[btnID] != LightCommands[btnID])
		{
			tmpbuf.push_back(btnID);
			tmpbuf.push_back((uint8_t)LightCommands[btnID]);
		}
	}

	ScopedLock lock(pMyData->CommandQueue);
	if (pMyData->CommandQueue->size() > 1000)    // imamo že preveč polno vrsto z ukazi, naj zgornji layer počaka.
	{
		rtfwk_sdl2::logman::WriteLog(LT_WARNING, NO_GAME_ID, "IGPV6: SetButtonLights - Command queue full (%lu)", pMyData->CommandQueue->size());
		return -3;
	}

	auto it = pMyData->CommandQueue->begin();
	if (pMyData->bExecutingCommand && it != pMyData->CommandQueue->end())
		it++;

	for (; it != pMyData->CommandQueue->end(); it++)
	{
		if ((*it)->Command == EIGPCommand::CMD_LIGHTS_SET)
			break;
	}

	if (tmpbuf.size() < 2)
	{
		if (it != pMyData->CommandQueue->end())
		{
			delete *it;
			pMyData->CommandQueue->erase(it);
		}
	}
	else
	{
		if (it != pMyData->CommandQueue->end())
		{
			coinsserial::AlocateDataSpaceInMessage(*it, tmpbuf.size());
			std::memcpy((*it)->pData, tmpbuf.data(), tmpbuf.size());
			(*it)->DataLength = tmpbuf.size();
		}
		else
		{
			pMyData->CommandQueue->push_back(coinsserial::CreateMessage(EIGPCommand::CMD_LIGHTS_SET, pMyData->GenNewMessageID(), tmpbuf.data(), tmpbuf.size()));
		}
	}

	return 0;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: SetIlluminationSlideshow()
FUNCTION: SetSemaphoreSlideshow()
FUNCTION: SetButtonsPWMSlideshow()

DESC: Funkcija nastavi barvo RGB ledic, mozno je nastaviti prehode in trajanje posamezne barve.
      Glej rgbanimation::TRGBSlide strukturo. Zadnji slide mora biti oznacen tako da je DurationDS==0(loop) ali pa z DurationDS==255(forever)
      ce je zadnji slide loop(DurationDS==0), potem se vrednosti RGB ignorira.., uposteva pa se vrednost v SpeedDS za index zacetnega slide-a za loop
      prvi slide za loop ima index 0, torej ce zelis loop med vsemi slide-i je pravilna nastavitev zadnjega slaide-a -> DurationDS==0 SpeedDS=0

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        slides - kazalec na tabelo rgbanimation::TRGBSlide, teh slide-ov je lahko najvec 20(trenutno toliko podprto na napravi),
                 ce jih je manj kot 20, mora zadnji slide imeti DurationDS==0, tako je oznacen zadnji!

RESULT: 0 success; -2 communication error; -3 out buffer full
---------------------------------------------------------------------------------------------------*/
int SetIlluminationSlideshow(ThIGPboard hInst, const std::vector<rgbanimation::TRGBSlide>& slides)
{
	if (!hInst || slides.empty())
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock statuslock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	statuslock.unlock();

	std::vector<unsigned char> tmp;
	tmp.reserve(6 * slides.size());
	// std::string msg = "IGPV6: Posting RGB slideshow:\n";
	// size_t idx = 0;
	for (const rgbanimation::TRGBSlide& slide : slides)
	{
		tmp.push_back(slide.Red);
		tmp.push_back(slide.Green);
		tmp.push_back(slide.Blue);
		tmp.push_back(slide.DurationDS);    // naprava se ustavi pri DurationDS==0, mi pa imamo 255 za to funkcijo
		tmp.push_back(slide.SpeedDS);
		tmp.push_back(slide.StroboSpecial);
		/*msg += yutils::Format("%lu) RGB(%d,%d,%d) dur %d, speed %d, strobo %d\n", idx++, slide.Red, slide.Green, slide.Blue, slide.DurationDS, slide.SpeedDS,
		                      slide.StroboSpecial);*/
		if (slide.DurationDS == rgbanimation::dFOREVER || slide.DurationDS == rgbanimation::dLOOP)
			break;    // if last slide has dForever, it is also considered as a termination slide
	}

	// LOG(LogIGPBoard, Debug, "%s", msg.c_str());

	return pMyData->PushCommand(EIGPCommand::CMD_ILLUMINATION_SLIDE_SET, tmp.data(), tmp.size());
}

int SetSemaphoreSlideshow(ThIGPboard hInst, const std::vector<rgbanimation::TRGBSlide>& slides)
{
	if (!hInst || slides.empty())
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	// stanje semaforja damo na sklad in ko bo komunikacija bomo poslali
	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	std::vector<unsigned char> tmp;
	tmp.reserve(6 * slides.size());
	for (const rgbanimation::TRGBSlide& slide : slides)
	{
		tmp.push_back(slide.Red);
		tmp.push_back(slide.Green);
		tmp.push_back(slide.Blue);
		tmp.push_back(slide.DurationDS);    // naprava se ustavi pri DurationDS==0, mi pa imamo 255 za to funkcijo
		tmp.push_back(slide.SpeedDS);
		tmp.push_back(slide.StroboSpecial);
		if (slide.DurationDS == rgbanimation::dFOREVER || slide.DurationDS == rgbanimation::dLOOP)
			break;    // if last slide has dForever, it is also considered as a termination slide
	}

	return pMyData->PushCommand(EIGPCommand::CMD_SEMAPHORE_SLIDE_SET, tmp.data(), tmp.size());
}

int SetButtonsPWMSlideshow(ThIGPboard hInst, const std::vector<rgbanimation::TRGBSlide>& slides)
{
	if (!hInst || slides.empty())
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();
	std::vector<unsigned char> tmp;
	tmp.reserve(6 * slides.size());
	for (const rgbanimation::TRGBSlide& slide : slides)
	{
		tmp.push_back(slide.Red);
		tmp.push_back(slide.Green);
		tmp.push_back(slide.Blue);
		tmp.push_back(slide.DurationDS);    // naprava se ustavi pri DurationDS==0, mi pa imamo 255 za to funkcijo
		tmp.push_back(slide.SpeedDS);
		tmp.push_back(slide.StroboSpecial);
		if (slide.DurationDS == rgbanimation::dFOREVER || slide.DurationDS == rgbanimation::dLOOP)
			break;    // if last slide has dForever, it is also considered as a termination slide
	}

	return pMyData->PushCommand(EIGPCommand::CMD_BTN_PWM_SLIDE_SET, tmp.data(), tmp.size());
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: SetMaxPWM()

DESC: Funkcija nastavi najvisjo vrednost za PWM. Vse vrednosti se bodo normirale na ta maximum.

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        IlluminationMax - vrednost v obmocju od 0-255. Vrednost 0 ohranja prejsnjo vrednost
        SemaphoreMax - vrednost v obmocju od 0-255. Vrednost 0 ohranja prejsnjo vrednost
        ButtonMax - vrednost v obmocju od 0-255. Vrednost 0 ohranja prejsnjo vrednost

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetMaxPWM(ThIGPboard hInst, uint8_t IlluminationMax, uint8_t SemaphoreMax, uint8_t ButtonMax)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char tmp[3];
	tmp[0] = (unsigned char)IlluminationMax;
	tmp[1] = (unsigned char)SemaphoreMax;
	tmp[2] = (unsigned char)ButtonMax;

	return pMyData->PushCommand(EIGPCommand::CMD_MAX_PWM_SET, tmp, 3);
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDoorStatus()

DESC: Returns the status of 4 door switches. Result will contain info only on lower 4 bits. LSB bit DOOR1,...

PARAMS: hInst - instance handle

RESULT: 0 - all keys are released; >1 - key is pressed; bits(0-10) define which key is pressed 1-10 repectively
---------------------------------------------------------------------------------------------------*/
unsigned char GetDoorStatus(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;
	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.DoorSwitchesStatus & 0x0F;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetMotionSensorStatus()

DESC: Returns the status of motion sensor input(PIR Sensor). TRUE- high, FALSE- low

PARAMS: hInst - instance handle

RESULT: 0 - motion low, 1 - motion high
---------------------------------------------------------------------------------------------------*/
bool GetMotionSensorStatus(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;
	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.DoorSwitchesStatus & 0x10;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: UpdateDeviceConfig()

DESC:	Funkcija popravi nastavitve naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        DevConfig - kazalec na strukturo TIGPConfig

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int UpdateDeviceConfig(ThIGPboard hInst, TIGPConfig* DevConfig)
{
	if (!hInst || !DevConfig)
		return -1;

	ScopedLock lock(((TMyData*)hInst)->DeviceStatus);
	((TMyData*)hInst)->DeviceStatus.CoinsConfig = *DevConfig;
	((TMyData*)hInst)->DeviceStatus.ConfigurationUpdatePending = true;
	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDeviceConfig()

DESC:	Funkcija vrne trenutne nastavitve naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        DevConfig - kazalec na strukturo TIGPConfig, kamor se zapisejo trenutne nastavitve

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetDeviceConfig(ThIGPboard hInst, TIGPConfig* DevConfig)
{
	if (!hInst || !DevConfig)
		return -1;

	ScopedLock lock(((TMyData*)hInst)->DeviceStatus);
	*DevConfig = ((TMyData*)hInst)->DeviceStatus.CoinsConfig;
	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: EnableMoneyAcceptor()

DESC:	Funkcija omogoči Coinacceptor

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int EnableMoneyAcceptor(ThIGPboard hInst)
{
	return 0;    // SW-2616 TODO THIS WAS TEMPORARILY DISABLED FOR LAOS, FIX WHEN WE NEED COIN ACCEPTOR

	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_COIN_ACC_ENABLE);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: DisableMoneyAcceptor()

DESC:	Funkcija onemogoči Coinacceptor

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int DisableMoneyAcceptor(ThIGPboard hInst)
{
	return 0;    // SW-2616 TODO THIS WAS TEMPORARILY DISABLED FOR LAOS, FIX WHEN WE NEED COIN ACCEPTOR

	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_COIN_ACC_DISABLE);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ResetAcceptedCoinsBuffer()

DESC:	Funkcija izprazni vrsto sprejetih kovancev, ki niso še bili poslani iz naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int ResetAcceptedCoinsBuffer(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_COIN_ACC_RESET);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillConfirmEscrowWithStack()

DESC:	Funkcija sprejme bankovec ki je trenutno v escrow poziciji

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillConfirmEscrowWithStack(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	if (!pMyData->DeviceStatus.CoinsConfig.EscrowEnable)
	{    // ni potrebno, ker je ze na napavi AutoEscrow
		return 0;
	}
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_STACK);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillConfirmEscrowWithReturn()

DESC:	Funkcija zavrne bankovec ki je trenutno v escrow poziciji

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillConfirmEscrowWithReturn(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	if (!pMyData->DeviceStatus.CoinsConfig.EscrowEnable)
	{    // ni potrebno, ker je ze na napavi AutoEscrow
		return 0;
	}
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_RETURN);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillReadBarcodeData()

DESC:	Funkcija prebere podatke barcod-e zadnje sprejetega ticketa iz naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillReadBarcodeData(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_READ_BARCODE_DATA);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsBillRecyclerFull()

DESC:	Pove ali je recycler na bill-u poln bankovcev

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
bool IsBillRecyclerFull(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerFull;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsBillRecyclerEmpty()

DESC:	Pove ali je recycler na bill-u prazen

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
bool IsBillRecyclerEmpty(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerEmpty;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsBillRecyclerPaying()

DESC: Pove ali je recycler trenutno aktiven

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
bool IsBillRecyclerPaying(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerIsPaying;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsAvailableToPayout()

DESC: Koliko bankovcev je mozno izplacati - zaloga

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsAvailableToPayout(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsAvailableToPayout;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsPaidOut()

DESC: Koliko bankovcev je bilo izplacanih v zadnjem izplacilu

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsPaidOut(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsPaidOut;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsPayoutRemain()

DESC: Koliko bankovcev se ni bilo izplacanih v zadnjem izplacilu in izplacilo se vedno v teku

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsPayoutRemain(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsPayoutRemain;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsUnableToPay()

DESC: Koliko bankovcev ni bilo izplacanih v zadnjem izplacilu / oz jih ni bilo mogoče izplacati, in prislo do napake

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsUnableToPay(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsUnableToPay;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsFromStoreToStacker()

DESC: Koliko bankovcev je bilo prenesenih iz store v stacker

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsFromStoreToStacker(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsFromStoreToStacker;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetBillRecyclerBillsToStackRemain()

DESC: Koliko bankovcev ni se bilo prenesenih iz store v stacker v zadnji operaciji / oz jih ni bilo mogoče prenesti, ce pride do napake

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int GetBillRecyclerBillsToStackRemain(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BillRecyclerBillsToStackRemain;
}



/*---------------------------------------------------------------------------------------------------
FUNCTION: BillRecyclerPayout()

DESC:	Funkcija sprozi izplacilo bankovcev iz billacceptorja

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        BillsCount - stevilo bankovcev za izplacilo - ce klicemo to funkcijo z BillCount==0, bo vezje
        samo resetiralo BillsPaid in BillToPay na 0 - uporabno za reset pri prikazu vrednosti ki preostane za
        izplacilo na ekranu

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillRecyclerPayout(ThIGPboard hInst, uint8_t BillsCount)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;

	pMyData->DeviceStatus.BillRecyclerBillsPaidOut = 0;
	pMyData->DeviceStatus.BillRecyclerBillsPayoutRemain = 0;
	pMyData->DeviceStatus.BillRecyclerBillsUnableToPay = 0;
	pMyData->DeviceStatus.H1CTG = 0;
	pMyData->DeviceStatus.H2CTG = 0;
	pMyData->DeviceStatus.H1CP = -2;    // dva poll-a se ne bo uposteval
	pMyData->DeviceStatus.H2CP = -2;    // dva poll-a se ne bo uposteval
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_DISPENSE_BILLS, &BillsCount, 1);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillRecyclerDispenseEmergencyStop()

DESC:	Funkcija takoj zakljuci z izplacilom bankovcev

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillRecyclerDispenseEmergencyStop(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_DISPENSE_EMERGENCY_STOP);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillRecyclerEmptyStoreToCashbox()

DESC:	Funkcija sprozi praznjenje bankovcev iz store v stacker

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int BillRecyclerEmptyStoreToCashbox(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_BILL_EMPTY_STORE_TO_CASHBOX);
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDeviceWorkingMode() - trenutno se uporablja samo za AT4Wireless

DESC: Vraca nacin delovanja naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
uint8_t GetDeviceWorkingMode(ThIGPboard hInst)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.DeviceWorkingMode;
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetDeviceWorkingMode() - trenutno se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi statusni byte, ki se na napravi shrani v EEPROM v primeru reset-a in se tako obnovi,
      uporabljamo pa ga za to, da ugotovimo, ali so krediti v CTG pravi ali za TEST - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetDeviceWorkingMode(ThIGPboard hInst, uint8_t Mode)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;

	// shranimo stanje v naprej, da se ne napolne vrsta iz Poll(), kjer klicemo to funkcijo
	pMyData->DeviceStatus.DeviceWorkingMode = Mode;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SET_WORKING_MODE, &Mode, 1);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetSelectorOutput() - se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi izhod selectorja  glede na Value - spodnji trije biti - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetSelectorOutput(ThIGPboard hInst, uint8_t Value)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SELECTOR_OUTPUT_SET, &Value, 1);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetStakeTypeLone() - se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi izhod Clear tipke glede na LoneValue - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetStakeTypeLone(ThIGPboard hInst, uint8_t LoneValue)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SET_STAKE_TYPE_LONE, &LoneValue, 1);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetAccumulatedValueBanco() - se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi izhod Repeat tipke glede na IsValueInBanco - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetAccumulatedValueBanco(ThIGPboard hInst, uint8_t IsValueInBanco)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SET_ACCUMULATED_VALUE_BANCO, &IsValueInBanco, 1);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetGameOverSignal() - se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi izhod Undo tipke glede na IsGameOver - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetGameOverSignal(ThIGPboard hInst, uint8_t IsGameOver)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SET_GAME_OVER_SIGNAL, &IsGameOver, 1);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: SetUpperGameActive() - se uporablja samo za AT4Wireless

DESC:	Funkcija nastavi izhod Undo tipke glede na IsUpperGameActive - AT4Wireless

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetUpperGameActive(ThIGPboard hInst, uint8_t IsUpperGameActive)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_SET_UPPER_GAME_ACTIVE, &IsUpperGameActive, 1);
}



/*---------------------------------------------------------------------------------------------------
FUNCTION: ResetHoper()

DESC:	Funkcija ustavi morebitno delovanje hoperja in resetira števec kovancev za izplacilo.

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2
        CoinsToCancel - ce je razlicen od nic, bo hoper-ju samo zmanjšal CTG za to vrednost, ce je 0
        se brisejo vsi CTG.

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int ResetHoper(ThIGPboard hInst, uint8_t HoperId, uint16_t CoinsToCancel)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	EIGPCommand cmd = EIGPCommand::NONE;
	uint8_t data[2];
	size_t dataSize = 0;
	if (0 == CoinsToCancel)
	{
		cmd = (1 == HoperId) ? EIGPCommand::CMD_H1_RESET : EIGPCommand::CMD_H2_RESET;
	}
	else
	{
		cmd = (1 == HoperId) ? EIGPCommand::CMD_H1_UnPAY : EIGPCommand::CMD_H2_UnPAY;

		data[0] = (unsigned char)CoinsToCancel;
		data[1] = (unsigned char)(CoinsToCancel >> 8);
		dataSize = 2;
	}

	return pMyData->PushCommand(cmd, data, dataSize);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ResumeHoper()

DESC:	Funkcija ponovno poskusi z izplacilom na hoper-ju. V primeru da prejšnje izplačilo ni bilo dokončano,
      sicer se ne zgodi nic.

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int ResumeHoper(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(1 == HoperId ? EIGPCommand::CMD_H1_RESUME : EIGPCommand::CMD_H2_RESUME);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: RefillHoper()

DESC:	Funkcija doda k stanju števila kovancev v hoperju zahtevano število kovancev. Pokliče naj se
ob polnjenju hoperja

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2
        CoinsCount - število kovancev ki so bili dodani v hoper

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int RefillHoper(ThIGPboard hInst, uint8_t HoperId, uint16_t CoinsCount)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char tmp[2];
	// tmp[0]=(unsigned char)CounterIndex;
	tmp[0] = (unsigned char)CoinsCount;
	tmp[1] = (unsigned char)(CoinsCount >> 8);

	return pMyData->PushCommand(1 == HoperId ? EIGPCommand::CMD_H1_REFIL : EIGPCommand::CMD_H2_REFIL, tmp, 2);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: SetHoperStock()

DESC:	Funkcija nastavi trenutno število kovancev v hoper-ju

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2
        CoinsCount - število kovancev ki so trenutno v hoper-ju

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int SetHoperStock(ThIGPboard hInst, uint8_t HoperId, uint16_t CoinsCount)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char tmp[2];
	// tmp[0]=(unsigned char)CounterIndex;
	tmp[0] = (unsigned char)CoinsCount;
	tmp[1] = (unsigned char)(CoinsCount >> 8);

	return pMyData->PushCommand(1 == HoperId ? EIGPCommand::CMD_H1_SET_STOCK : EIGPCommand::CMD_H2_SET_STOCK, tmp, 2);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: HopperPayOut()

DESC:	Funkcija sporži izplačilo na hoperju

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         HoperID - stevilka hoperja  -  trenutno podprto 1, 2
         CoinsCount - koliko žetonov naj izplačamo - ce klicemo to funkcijo z CoinsCount==0, bo vezje
         samo resetiralo CoinsPaid in CoinsToPay na 0 - uporabno za reset pri prikazu vrednosti ki preostane za
         izplacilo na ekranu

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int HopperPayOut(ThIGPboard hInst, uint8_t HoperId, uint16_t CoinsCount)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;

	pMyData->DeviceStatus.BillRecyclerBillsPaidOut = 0;
	pMyData->DeviceStatus.BillRecyclerBillsPayoutRemain = 0;    // zato da takoj popravimo status, do naslednjega poll-a
	pMyData->DeviceStatus.BillRecyclerBillsUnableToPay = 0;
	pMyData->DeviceStatus.H1CTG = 0;
	pMyData->DeviceStatus.H2CTG = 0;
	pMyData->DeviceStatus.H1CP = -2;    // dva poll-a se ne bo uposteval
	pMyData->DeviceStatus.H2CP = -2;    // dva poll-a se ne bo uposteval
	lock.unlock();

	unsigned char tmp[2];
	// tmp[0]=(unsigned char)CounterIndex;
	tmp[0] = (unsigned char)CoinsCount;
	tmp[1] = (unsigned char)(CoinsCount >> 8);

	return pMyData->PushCommand(1 == HoperId ? EIGPCommand::CMD_H1_PAY : EIGPCommand::CMD_H2_PAY, tmp, 2);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: HopperPayOutTestMode()

DESC:	Funkcija sporži izplačilo na hoperju za oznaceno stevilo kovancev, pri cemer se ta vrednost ne
      shrani v EEPROM niti ne vztraja ko zmanjka kovancev. Ce pride do napake pri izplacilu, se
      vrednost postavi na 0 in ukaz zakjluci. Funkcija je namenjena preskusanju delovanja hopper-ja.

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         HoperID - stevilka hoperja  -  trenutno podprto 1, 2
         CoinsCount - koliko žetonov naj poskusno izplačamo


RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int HopperPayOutTestMode(ThIGPboard hInst, uint8_t HoperId, uint16_t CoinsCount)
{
	if (!hInst || HoperId > HOPERS_COUNT || HoperId < 1)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;

	pMyData->DeviceStatus.BillRecyclerBillsPaidOut = 0;
	pMyData->DeviceStatus.BillRecyclerBillsPayoutRemain = 0;    // zato da takoj popravimo status, do naslednjega poll-a
	pMyData->DeviceStatus.BillRecyclerBillsUnableToPay = 0;
	pMyData->DeviceStatus.H1CTG = 0;
	pMyData->DeviceStatus.H2CTG = 0;
	pMyData->DeviceStatus.H1CP = -2;    // dva poll-a se ne bo uposteval
	pMyData->DeviceStatus.H2CP = -2;    // dva poll-a se ne bo uposteval
	lock.unlock();

	unsigned char tmp[2];
	// tmp[0]=(unsigned char)CounterIndex;
	tmp[0] = (unsigned char)CoinsCount;
	tmp[1] = (unsigned char)(CoinsCount >> 8);

	return pMyData->PushCommand(1 == HoperId ? EIGPCommand::CMD_H1_TEST_PAY : EIGPCommand::CMD_H2_TEST_PAY, tmp, 2);
}



/*---------------------------------------------------------------------------------------------------
FUNCTION: ExternalEEPROMWrite()

DESC:	Funkcija zapise v zunanji EEPROM priklopljen na napravi

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         Address - naslov v pomnilniskem prostoru zunanje naprave kamor bomo zapisali prvi byte
         pBuffer - podatki
         bufferLength - dolzina podatkov za pisanja - max 246 bytes per message

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int ExternalEEPROMWrite(ThIGPboard hInst, uint16_t Address, uint8_t* pBuffer, uint8_t bufferLength)
{
	if (!hInst || bufferLength > 246)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	std::vector<uint8_t> data;
	data.push_back((unsigned char)Address);
	data.push_back((unsigned char)(Address >> 8));
	data.push_back((unsigned char)bufferLength);
	if (bufferLength)
		data.insert(data.end(), pBuffer, pBuffer + bufferLength);
	return pMyData->PushCommand(EIGPCommand::CMD_EXT_EEPROM_WRITE, data.data(), data.size(), 250);
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: ExternalEEPROMIsConnected()

DESC:	Funkcija prebere podatke iz zunanjega eeprom-a na napravi

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool ExternalEEPROMIsConnected(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.CommunicationOK && pMyData->DeviceStatus.ExternalEEPROMConnected;
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: ExternalEEPROMFetchData()

DESC:	Funkcija prebere podatke iz zunanjega eeprom-a na napravi

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         Address - naslov v pomnilniskem prostoru zunanje naprave kamor bomo zapisali prvi byte
         Length - dolzina podatkov za pisanja - max 246 bytes per message

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int ExternalEEPROMFetchData(ThIGPboard hInst, uint16_t Address, uint8_t Length)
{
	if (!hInst || Length > 246)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char tmp[3];
	tmp[0] = (unsigned char)Address;
	tmp[1] = (unsigned char)(Address >> 8);
	tmp[2] = (unsigned char)Length;
	return pMyData->PushCommand(EIGPCommand::CMD_EXT_EEPROM_WRITE, tmp, 3, 150);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ExternalEEPROMRead()

DESC:	Funkcija vraca podatke ki so bili predhodno prebrani iz zunanjega eeproma-a na napravi
      s klicem na ExternalEEPROMFetchData

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         pDestination - naslov v pomnilniskem prostoru zunanje naprave kamor bomo zapisali prvi byte
         MaxLength - najvec koliko podatkov beremo oz. dolžina spomina kamor kaže pDestination

RESULT: bytes copied or -1 on error
---------------------------------------------------------------------------------------------------*/
int ExternalEEPROMRead(ThIGPboard hInst, uint8_t* pDestination, uint8_t MaxLength)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	const uint8_t bytes_to_copy = std::min(MaxLength, (uint8_t)pMyData->DeviceStatus.EEPROM_DATA_CACHE.size());
	memcpy(pDestination, pMyData->DeviceStatus.EEPROM_DATA_CACHE.data(), bytes_to_copy);

	return bytes_to_copy;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ExternalEEPROMSetAutopollInterval()

DESC:	Funkcija nastavi preverjanje za prisotnost EEPROM-a na napravi na izbrano periodo v sekundah.
      Ce je vrednost PollEverySeconds==0, potem naprava polanja ne izvaja

PARAMS:  hInst - pointer na handle instance komunikacijskega driverja
         PollEverySeconds - vsake koliko sekund naj naprava pola za prisotnost eerpoma

RESULT: bytes copied or -1 on error
---------------------------------------------------------------------------------------------------*/
int ExternalEEPROMSetAutopollInterval(ThIGPboard hInst, uint8_t PollEverySeconds)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_EXT_EEPROM_POLL, &PollEverySeconds, 1);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: FetchBoardVersion()

DESC:	Sprozi preverjanje verzije na napravi

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 communication error; -1 error
---------------------------------------------------------------------------------------------------*/
int FetchBoardVersion(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_GET_VERSION);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsCommunicationOK()

DESC:	Ali je komunikacija z napravo vzpostavljena

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool IsCommunicationOK(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.CommunicationOK;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsCoinCommunicationOK()

DESC:	Ali je komunikacija z napravo vzpostavljena, oznacuje link med coins vezjem in coinacceptorjem preko cctalk

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool IsCoinCommunicationOK(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.COIN1_CommunicationOk;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsBillCommunicationOK()

DESC:	Ali je komunikacija z bill-om vzpostavljena, oznacuje link med coins vezjem in billom preko cctalk

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool IsBillCommunicationOK(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BILL1_CommunicationOk;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsBillRecyclerCommunicationOK()

DESC:	Ali je komunikacija z bill recycler-jem vzpostavljena, oznacuje link med coins vezjem in billom preko cctalk

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool IsBillRecyclerCommunicationOK(ThIGPboard hInst)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BILL_RECYCLER1_CommunicationOk;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsHopperCommunicationOK()

DESC:	Ali je komunikacija s hopperjem vzpostavljena, oznacuje link med coins vezjem in hopperjem preko cctalk

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: true/false
---------------------------------------------------------------------------------------------------*/
bool IsHopperCommunicationOK(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.HOPPER1_CommunicationOk;
	else
		return pMyData->DeviceStatus.HOPPER2_CommunicationOk;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: IsHoperFull()

DESC:	Funkcija vraca stanje hoperja, glede na senzor hoperja ali števično, če senzorja ni

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: 1 - hoper is full, 0 - not full
---------------------------------------------------------------------------------------------------*/
bool IsHoperFull(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1Full;
	else
		return pMyData->DeviceStatus.H2Full;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsHoperLow()

DESC:	Funkcija vraca stanje hoperja, glede na senzor hoperja ali števično, če senzorja ni

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: 1 - hoper is low, 0 - not low
---------------------------------------------------------------------------------------------------*/
bool IsHoperLow(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1Low;
	else
		return pMyData->DeviceStatus.H2Low;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsHoperErrorAndCoinsPending()

DESC:	Funkcija vraca stanje hoperja

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: 1 - is in error, and coins still in CTG, 0 - ready
---------------------------------------------------------------------------------------------------*/
bool IsHoperErrorAndCoinsPending(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return false;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1ErrorCoinsPending;
	else
		return pMyData->DeviceStatus.H2ErrorCoinsPending;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetHoperCTG()

DESC:	Funkcija vraca stevilo kovancev, ki se cakajo na izplacilo na hoper-ju

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: <0 - napaka, stevilo kovancev
---------------------------------------------------------------------------------------------------*/
int GetHoperCTG(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1CTG;
	else if (2 == HoperId)
		return pMyData->DeviceStatus.H2CTG;
	else
		return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetHoperCP()

DESC:	Funkcija vraca stevilo kovancev, ki so bili izplacani v zadnjem izplacilu. V primeru, da trenutno
      izplacilo se traja in poklicemo ponovno PayOut(), se ta stevilka ne bo resetirala ampak bo vsebovala
      obe izplacili..

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: <0 - napaka, stevilo kovancev
---------------------------------------------------------------------------------------------------*/
int GetHoperCP(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1CP < 0 ? 0 : pMyData->DeviceStatus.H1CP;
	else
		return pMyData->DeviceStatus.H2CP < 0 ? 0 : pMyData->DeviceStatus.H2CP;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetHoperStock()

DESC:	Funkcija vraca stevilo kovancev, trenutno v hoper-ju

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        HoperID - stevilka hoperja  -  trenutno podprto 1, 2

RESULT: <0 - napaka, stevilo kovancev
---------------------------------------------------------------------------------------------------*/
int GetHoperStock(ThIGPboard hInst, uint8_t HoperId)
{
	if (!hInst)
		return 0;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (1 == HoperId)
		return pMyData->DeviceStatus.H1CurrentStock;
	else
		return pMyData->DeviceStatus.H2CurrentStock;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: IsMoneyAcceptanceDisabled()

DESC:	Funkcija vraca stanje acceptor-ja

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 1 - acceptor is disabled, 0 - Enabled
---------------------------------------------------------------------------------------------------*/
bool IsMoneyAcceptanceDisabled(ThIGPboard hInst)
{
	if (!hInst)
		return true;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.MoneyAcceptanceDisabled;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: CounterIncrement()

DESC: Increments the counter for the specified values

PARAMS: hInst - instance handle

CounterIndex - index of counter(0-7)

Counts - number of to increment

RESULT: 0 = OK; 0<> failure;
---------------------------------------------------------------------------------------------------*/
int CounterIncrement(ThIGPboard hInst, unsigned short CounterIndex, unsigned short Counts)
{
	if (!hInst || CounterIndex >= MAX_MECHANICAL_COUNTERS || Counts == 0)
		return -1;
	// if ( 0==Counts )
	//   printf("*******************************DEBUG_WARNING: Counts = = 0\n");
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char tmp[3];
	tmp[0] = (unsigned char)CounterIndex;
	tmp[1] = (unsigned char)Counts;
	tmp[2] = (unsigned char)(Counts >> 8);
	return pMyData->PushCommand(EIGPCommand::CMD_INC_CNT, tmp, 3);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: CounterClear()

DESC: Clears the counter ToCount value to 0 - mechanical counter will stop

PARAMS: hInst - instance handle

CounterIndex - index of counter(0-7)

RESULT: 0 = OK; 0<> failure;
---------------------------------------------------------------------------------------------------*/
int CounterClear(ThIGPboard hInst, unsigned char CounterIndex)
{
	if (!hInst || CounterIndex >= MAX_MECHANICAL_COUNTERS)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	return pMyData->PushCommand(EIGPCommand::CMD_CLR_CNT, &CounterIndex, 1);
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ForceReset()

DESC: Resets the MCU on the board

PARAMS: hInst - instance handle

RESULT: 0 = OK; 0<> failure;
---------------------------------------------------------------------------------------------------*/
int ForceReset(ThIGPboard hInst)
{
	if (!hInst)
		return -1;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	unsigned char rebootMode = 0;    // flag to reboot into ISP mode (0 means NO)
	return pMyData->PushCommand(EIGPCommand::CMD_REBOOT, &rebootMode, 1);
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetEnvironmentTemperature()

DESC: Vraca vrednost temperature okolja, ki jo izmeri temperaturni sensor na board-u

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: <-100 - error, other values - temperature in Celsius, 2 decimal places
---------------------------------------------------------------------------------------------------*/
double GetEnvironmentTemperature(ThIGPboard hInst)
{
	if (!hInst)
		return -111;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BoardEnvironmentTemperatureCents /
	       (double)100;    // vrednost je v int spremenljivki vpisana v centih, zato delimo, da dobimo pravo vrednost
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: GetSupplyVoltage12V()

DESC: Vraca vrednost temperature okolja, ki jo izmeri temperaturni sensor na board-u

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: <-100 - error, other values - voltage in volts, 1 decimal place
---------------------------------------------------------------------------------------------------*/
double GetSupplyVoltage12V(ThIGPboard hInst)
{
	if (!hInst)
		return -111;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BoardVoltage12VDeciVolts / (double)10;    // vrednost je v int spremenljivki vpisana v deci, zato delimo, da dobimo pravo vrednost
}
/*---------------------------------------------------------------------------------------------------
FUNCTION: GetSupplyVoltage5V()

DESC: Vraca vrednost temperature okolja, ki jo izmeri temperaturni sensor na board-u

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: <-100 - error, other values - voltage in volts, 1 decimal place
---------------------------------------------------------------------------------------------------*/
double GetSupplyVoltage5V(ThIGPboard hInst)
{
	if (!hInst)
		return -111;
	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	return pMyData->DeviceStatus.BoardVoltage5VDeciVolts / (double)10;    // vrednost je v int spremenljivki vpisana v deci, zato delimo, da dobimo pravo vrednost
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: RequestGenuineValidation()

DESC: Poslje zahtevo za preverjanje pristnosti povezane naprave. Funkcija bo ob prejetem odgovoru iz naprave povišala
      števec GenuineValidationCount, ki ga pridobimo s klicem funkcije GetGenuineValidationResult

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
        GenuineValidationID -returns the id, it will become the result of GetGenuineValidationResult() on success

RESULT: true - success, false - failure
---------------------------------------------------------------------------------------------------*/
bool RequestGenuineValidation(TMyData* board)
{
	// izberemo nov validation ID in ga shranimo
	unsigned char key1 = 1;
	long int challenge1 = 0xabcdefabcdefabcd;
	unsigned char key2 = 2;
	long int challenge2 = 0xabcdefabcdefabcd;
	unsigned char key3 = 3;
	long int challenge3 = 0xabcdefabcdefabcd;

	unsigned char tmp[30];
	tmp[0] = (unsigned char)key1;
	tmp[1] = (unsigned char)challenge1;
	tmp[2] = (unsigned char)(challenge1 >> 8);
	tmp[3] = (unsigned char)(challenge1 >> 16);
	tmp[4] = (unsigned char)(challenge1 >> 24);
	tmp[5] = (unsigned char)(challenge1 >> 32);
	tmp[6] = (unsigned char)(challenge1 >> 40);
	tmp[7] = (unsigned char)(challenge1 >> 48);
	tmp[8] = (unsigned char)(challenge1 >> 56);
	tmp[9] = (unsigned char)0x11;

	tmp[10] = (unsigned char)key2;
	tmp[11] = (unsigned char)challenge2;
	tmp[12] = (unsigned char)(challenge2 >> 8);
	tmp[13] = (unsigned char)(challenge2 >> 16);
	tmp[14] = (unsigned char)(challenge2 >> 24);
	tmp[15] = (unsigned char)(challenge2 >> 32);
	tmp[16] = (unsigned char)(challenge2 >> 40);
	tmp[17] = (unsigned char)(challenge2 >> 48);
	tmp[18] = (unsigned char)(challenge2 >> 56);
	tmp[19] = (unsigned char)0x12;

	tmp[20] = (unsigned char)key3;
	tmp[21] = (unsigned char)challenge3;
	tmp[22] = (unsigned char)(challenge3 >> 8);
	tmp[23] = (unsigned char)(challenge3 >> 16);
	tmp[24] = (unsigned char)(challenge3 >> 24);
	tmp[25] = (unsigned char)(challenge3 >> 32);
	tmp[26] = (unsigned char)(challenge3 >> 40);
	tmp[27] = (unsigned char)(challenge3 >> 48);
	tmp[28] = (unsigned char)(challenge3 >> 56);
	tmp[29] = (unsigned char)0x13;

	return board->PushCommand(EIGPCommand::CMD_GENUINE_VALIDATION, tmp, 30) == 0;
}

EBoardValidity IsValidHardware(ThIGPboard hInst)
{
	if (!hInst)
		return EBoardValidity::NOT_CONNECTED;

	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return EBoardValidity::NOT_CONNECTED;

	if (pMyData->DeviceStatus.ValidHardware == EBoardValidity::NOT_CONNECTED)
	{
		RequestGenuineValidation(pMyData);
		pMyData->DeviceStatus.ValidHardware = EBoardValidity::WAITING_FOR_RESPONSE;
	}

	return pMyData->DeviceStatus.ValidHardware;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDeviceUniqueID()

DESC: Vraca unikatno stevilko naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: DeviceUniqueID1,2 - 16 bytes long CPU id from LPC mcu
        DeviceInstanceID - is 4byte software generated device ID - it may change when device EEPROM is wiped
                           (for example when upgrading the device or if an checksum error occours!)
---------------------------------------------------------------------------------------------------*/
bool GetDeviceUniqueID(ThIGPboard hInst, uint64_t& DeviceUniqueID1, uint64_t& DeviceUniqueID2, uint32_t& DeviceInstanceID)
{
	if (!hInst)
		return false;

	TMyData* pMyData = (TMyData*)hInst;

	ScopedLock lock(pMyData->DeviceStatus);
	if (!pMyData->DeviceStatus.CommunicationOK)
		return -2;
	lock.unlock();

	DeviceUniqueID1 = (int64_t)pMyData->DeviceStatus.DeviceUniqueID[0];
	DeviceUniqueID1 <<= 32;
	DeviceUniqueID1 |= (int64_t)pMyData->DeviceStatus.DeviceUniqueID[1];

	DeviceUniqueID2 = (int64_t)pMyData->DeviceStatus.DeviceUniqueID[2];
	DeviceUniqueID2 <<= 32;
	DeviceUniqueID2 |= (int64_t)pMyData->DeviceStatus.DeviceUniqueID[3];

	DeviceInstanceID = pMyData->DeviceStatus.DeviceInstanceID;

	// was id already read from device??
	if (0 == DeviceUniqueID1 && 0 == DeviceUniqueID2)
		return false;    // NO

	return true;    // YES
}


}    // namespace IGPboard
