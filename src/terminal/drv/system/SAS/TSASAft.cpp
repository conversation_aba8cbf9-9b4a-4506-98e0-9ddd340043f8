#include "drv/system/SAS/TSASAft.h"

#include "YUtils.h"
#include "drv/system/SAS/TSASModule.h"

#define MAX_CREDITS 9999999999UL

const std::map<int, const char*> pAFTRegCodeTxts = { { AFT_REGCODE_INIT_REGISTRATION, "Initialize registration" },
	                                                 { AFT_REGCODE_REGISTER_GM, "Register gaming machine" },
	                                                 { AFT_REGCODE_REQEST_ACK, "Request operator acknowledgment" },
	                                                 { AFT_REGCODE_UNREGISTER_GM, "Unregister gaming machine" },
	                                                 { AFT_REGCODE_READ_CURRENT_REG, "Read current registration" } };

const std::map<int, const char*> pAFTRegStatusTxts = { { AFT_REGSTATUS_READY, "Gaming machine registration ready" },
	                                                   { AFT_REGSTATUS_REGISTERED, "Gaming machine registered" },
	                                                   { AFT_REGSTATUS_PENDING, "Gaming machine registration pending" },
	                                                   { AFT_REGSTATUS_NOT_REGISTERED, "Gaming machine not registered" } };

const std::map<int, const char*> pAFTTransferStatusCodesTxts = {
	{ TSC_FULL_TR_SUCCESSFUL, "Full transfer successful" },
	{ TSC_PARTIAL_TR_SUCCESSFUL, "Partial transfer successful" },
	{ TSC_TR_PENDING, "Transfer pending" },
	{ TSC_TR_CANCELLED_BY_HOST, "Transfer cancelled by host" },
	{ TSC_TRANSACTION_ID_NOT_UNIQUE, "Transaction ID not unique (same as last successful transfer logged in history)" },
	{ TSC_NOT_VALID_TR_FUNCTION, "Not a valid transfer function (unsupported type, amount, index, etc.)" },
	{ TSC_NOT_VALID_AMOUNT_OR_EXPIRATION, "Not a valid transfer amount or expiration (non-BCD, etc.)" },
	{ TSC_AMOUNT_EXCEEDS_GM_TR_LIMIT, "Transfer amount exceeds the gaming machine transfer limit" },
	{ TSC_AMOUNT_NOT_MULTIPLE_GM_DENOM, "Transfer amount not an even multiple of machine denomination" },
	{ TSC_UNABLE_TO_PERFORM_PARTIAL_TR, "Gaming machine unable to perform partial transfers to the host" },
	{ TSC_UNABLE_TO_PERFORM_AT_THIS_TIME, "Gaming machine currently unable to perform transfers(door open,cashout in progress, etc.)" },
	{ TSC_GM_NOT_REGISTRED, "Gaming machine not registered (required for debit transfers)" },
	{ TSC_REGISTRATION_KEY_NOT_MATCH, "Registration key does not match" },
	{ TSC_NOT_POSID, "No POS ID (required for debit transfers" },
	{ TSC_NO_WON_CREDITS_FOR_CASHOUT, "No won credits available for cahout" },
	{ TSC_NO_GM_DENOMINATION_SET, "No gaming machine denomination set (unable to perform cents to credits conversion)" },
	{ TSC_EXP_NOT_VALID_TR_TO_TICKET, "Expiration not valid for transfer to ticket (already expired)" },
	{ TSC_TR_TO_TICKET_NOT_AVAILABLE, "Transfer to ticket device no available" },
	{ TSC_RESTRICTED_AMOUNTS_DIFF_POOL, "Unable to accept transfer due to existing restricted amounts from different pool" },
	{ TSC_UNABLE_PRINT_RECEIPT, "Unable to print transaction receipt (receipt device not currently available)" },
	{ TSC_MISSING_DATA_TO_PRINT_RECEIPT, "Insufficient data to print transaction receipt (required fields missing)" },
	{ TSC_RECEIPT_NOT_ALLOWED_FOR_TRTYPE, "Transaction receipt not allowed for specified transfer type" },
	{ TSC_ASSET_ZERRO_OR_DOES_NOT_MATCH, "Asset number zerro or does not match" },
	{ TSC_GM_NOT_LOCKED, "Gaming machine not locked (transfer specified lock required)" },
	{ TSC_TRANSACTION_ID_NOT_VALID, "Transaction ID not valid" },
	{ TSC_UNEXPECTED_ERROR, "Unexpected error" },
	{ TSC_NOT_COMPATIBLE_WITH_CURR_TR, "Not compatible with current transfer in progress" },
	{ TSC_UNSUPPORTED_TRANSFER_CODE, "Unsupported transfer code" },
	{ TSC_NO_TR_INFORMATION_AVAILABLE, "No transfer information available" }
};

const std::map<int, const char*> pAFTReceiptStatusCodesTxts = { { RSC_RECEIPT_PRINTED, "Receipt printed" },
	                                                            { RSC_RECEIPT_PRINTING_IN_PROGRESS, "Receipt printing in progress (not complete)" },
	                                                            { RSC_RECEIPT_PENDING, "Receipt pending (not complete)" },
	                                                            { RSC_NO_RECEIPT_REQ_OR_NOT_PRINTED, "No receipt requested or receipt not printed" } };

const std::map<int, const char*> pAFTTransferTypesTxts = {
	{ TT_INHOUSE_H_TO_GM, "Transfer in-house amount from host to gaming machine" },
	{ TT_BONUS_COIN_OUT_WIN_H_TO_GM, "Transfer bonus coin out win anount from host to gaming machine" },
	{ TT_BONUS_JACKPOT_WIN_H_TO_GM, "Transfer bonus jackpot win amount from host to gaming machine (force attendant pay lockup)" },
	{ TT_INHOUSE_H_TO_TICKET, "Transfer in-house amount from host to ticket (only one amount type allowed per transfer)" },
	{ TT_DEBIT_H_TO_GM, "Transfer debit amount from host to gaming machine" },
	{ TT_DEBIT_H_TO_TICKET, "Transfer debit amount from host to ticket" },
	{ TT_INHOUSE_GM_TO_H, "Transfer in-house amount from gaming machine to host" },
	{ TT_INHOUSE_WIN_GM_TO_H, "Transfer win amount (in-house) from gaming machine to host" }
};

inline const char* FindMsg(const std::map<int, const char*>& map, int iCode)
{
	auto find = map.find(iCode);
	return find != map.end() ? find->second : "";
}


const char* GetAFTRegStatusTxt(int iCode)
{
	return FindMsg(pAFTRegStatusTxts, iCode);
}

const char* GetAFTRegCodeTxt(int iCode)
{
	return FindMsg(pAFTRegCodeTxts, iCode);
}

// table 8.3e
const char* GetAFTTransferStatusCodesTxt(int iCode)
{
	return FindMsg(pAFTTransferStatusCodesTxts, iCode);
}

// table 8.3g
const char* GetAFTReceiptStatusCodesTxt(int iCode)
{
	return FindMsg(pAFTReceiptStatusCodesTxts, iCode);
}

// table 8.3d
const char* GetAFTTransferTypesTxt(int iCode)
{
	return FindMsg(pAFTTransferTypesTxts, iCode);
}


int TSASAft::AddTransaction(TSASAftTransaction* cTrans)
{
	unsigned int nCurrentPos = pCurrentTransactionNumber->AsInteger();
	TString sTrans, sMirrorName;

	// get current position
	{
		nCurrentPos++;
		if (nCurrentPos > AFT_TRANSACTIONS_HISTORY_BUFF_LEN)
			nCurrentPos = 1;
		// transaction to string
		cTrans->ToStr(sTrans);
		{
			sMirrorName = "AFT_HISTORY_" + std::to_string(nCurrentPos);
			if (pSASModule->ParamSet(sMirrorName, sTrans) && pSASModule->ParamSet("AFT_CURRENT", std::to_string(nCurrentPos)))
				return true;
		}
	}
	return false;
}


/* Returns the last valid transaction id, if there are no  transactions then returns "" */
/* Returns false if last tr id can not be retrieved                                     */
int TSASAft::GetLastTransactionID(TString& sLastTrID)
{
	unsigned int nCurrentPos = pCurrentTransactionNumber->AsInteger();
	TString sTrans, sMirrorName;
	TSASAftTransaction cTrans;

	// get current position

	if (nCurrentPos > AFT_TRANSACTIONS_HISTORY_BUFF_LEN)
		return false;
	if (GetTransaction(nCurrentPos, &cTrans))
	{
		sLastTrID = cTrans.sTransactionID;
		return true;
	}

	return false;
}

/* nTransIndex: 00: current or most recent */
/*              01 - 7F: absolute position */
/*              81 - FF: relative position */
int TSASAft::GetTransaction(int nTransIndex, TSASAftTransaction* cTrans)
{
	int nPos = 0;
	unsigned int uCurrentPos = pCurrentTransactionNumber->AsInteger();
	TString sTrans, sMirrorName, sCurrentTransaction;

	// get current position
	// get position
	// absoulute position
	if (nTransIndex > 0 && nTransIndex <= 0x7F)
		nPos = nTransIndex;
	// relative position
	if (nTransIndex >= 0x81 && nTransIndex <= 0xFF)
	{
		nTransIndex = 0xFF - nTransIndex;
		nPos = uCurrentPos - nTransIndex;
		if (nPos <= 0)
			nPos = AFT_TRANSACTIONS_HISTORY_BUFF_LEN + nPos;
	}
	// current or most recent
	if (nTransIndex == 0)
	{
		nPos = uCurrentPos;
		sCurrentTransaction = pCurrentTransaction->Value().c_str();
	}

	if (nPos > 0 && nPos <= AFT_TRANSACTIONS_HISTORY_BUFF_LEN)
	{
		if (sCurrentTransaction.empty())
		{
			// get from mirror
			sMirrorName = "AFT_HISTORY_" + std::to_string(nPos);
			pSASModule->ParamGet(sMirrorName, sTrans);
		}
		else
			sTrans = sCurrentTransaction;

		if (!sTrans.empty() && cTrans->FromStr(sTrans))
			return true;
		else
			TLOG(LogSAS, Error, "AFT transaction string invalid: %s", sTrans.c_str());
	}

	return false;
}

/* Returns true if nIndex-th bit in cChar is set */
/* The right most bit has index 0                   */
inline bool IsBitSet(unsigned char cChar, int nIndex)
{
	return ((cChar & (1 << nIndex)) == (1 << nIndex));
}

/* Commit transaction to buffer, if credits == 0 omit the commision */
int TSASAft::CommitToBuffer(TSASAftTransaction* cTrans, TString* sResult, int bCopyTobuffer, int bRememberTransferFlag)
{
	unsigned int uCurrentPos;
	TString sTrans, sMirrorName;

	if (bRememberTransferFlag)
	{
		// after a succsessful transfer: remember transfer flags (for: Host Cashout Enable)
		// if bit 0 is 1
		if (IsBitSet(cTrans->cTransferFlags, 0))
		{
			pAftTransferFlags->SetValue((long)cTrans->cTransferFlags);
			pApp->AddToStoreQueue(pAftTransferFlags);
		}
	}

	// current trans to ""
	pCurrentTransaction->SetValue("");
	pApp->AddToStoreQueue(pCurrentTransaction);
	// change trans state
	pTransferStatus->SetValue((long)(bCopyTobuffer ? AFT_TS_TRANS_COMMITED : AFT_TS_READY_FOR_CYCLE));
	pApp->AddToStoreQueue(pTransferStatus);

	// if Credits > 0 write to buffer
	if (bCopyTobuffer)
	{
		// get current position
		uCurrentPos = pCurrentTransactionNumber->AsInteger();

		// increment current position
		uCurrentPos++;
		if (uCurrentPos > AFT_TRANSACTIONS_HISTORY_BUFF_LEN)
			uCurrentPos = 1;
		// put to mirror
		sMirrorName = "AFT_HISTORY_" + std::to_string(uCurrentPos);
		cTrans->cTransactionBufferPosition = uCurrentPos;
		cTrans->ToStr(sTrans);

		if (pSASModule->ParamSet(sMirrorName, sTrans))
		{
			pCurrentTransactionNumber->SetValue((long)uCurrentPos);
			pApp->AddToStoreQueue(pCurrentTransactionNumber);
		}
	}
	return true;
}


/* ------------------------- TSASAftTransaction ----------------------------- */

void TSASAftTransaction::ToStr(TString& sRes) const
{
	sRes = yutils::Format("%u|%u|%u|%u|%u|%lu|%lu|%lu|%u|%u|%u|%s|%s|%s|%s|%u|%lu|%lu|%lu", cTransferCode, cTransactionBufferPosition, cTransferStatus,
	                      cReceiptStatusCode, cTransferType, CashableAmountCents, RestrictedAmountCents, NonRestrictedAmountCents, cTransferFlags, uAssetNumber,
	                      cTransactionIDLength, sTransactionID.c_str(), sTransDate.c_str(), sTransTime.c_str(), sExpiration.c_str(), shPoolID, CashableMeterCents,
	                      RestrictedMeterCents, NonrestrictedMeterCents);
}

/* Parses the transaction string     */
/* If string not valid returns false */
bool TSASAftTransaction::FromStr(const TString& sVal)
{
	auto parts = yutils::Split(sVal, "|", false);
	if (parts.size() != 19)
		return false;

	if (!yutils::strToUInt2(parts[0], cTransferCode))
		return false;
	if (!yutils::strToUInt2(parts[1], cTransactionBufferPosition))
		return false;
	if (!yutils::strToUInt2(parts[2], cTransferStatus))
		return false;
	if (!yutils::strToUInt2(parts[3], cReceiptStatusCode))
		return false;
	if (!yutils::strToUInt2(parts[4], cTransferType))
		return false;

	if (!yutils::strToUInt2(parts[5], CashableAmountCents))
		return false;
	if (!yutils::strToUInt2(parts[6], RestrictedAmountCents))
		return false;
	if (!yutils::strToUInt2(parts[7], NonRestrictedAmountCents))
		return false;

	if (!yutils::strToUInt2(parts[8], cTransferFlags))
		return false;
	if (!yutils::strToUInt2(parts[9], uAssetNumber))
		return false;
	if (!yutils::strToUInt2(parts[10], cTransactionIDLength))
		return false;

	sTransactionID = parts[11];
	sTransDate = parts[12];
	sTransTime = parts[13];
	sExpiration = parts[14];

	if (!yutils::strToUInt2(parts[15], shPoolID))
		return false;

	if (!yutils::strToUInt2(parts[16], CashableMeterCents))
		return false;
	if (!yutils::strToUInt2(parts[17], RestrictedMeterCents))
		return false;
	if (!yutils::strToUInt2(parts[18], NonrestrictedMeterCents))
		return false;

	return true;
}


/* Fills the counter values according to the Transaction type and current mirror values */
int TSASAftTransaction::GetCountersAndTime(const TSASModule* sas)
{
	CashableMeterCents = RestrictedMeterCents = NonrestrictedMeterCents = 0;
	switch (cTransferType)
	{
		case TT_INHOUSE_H_TO_GM:
			sas->MeterGet(METER_IN_HOUSE_CASHABLE_TO_GM_CENTS, &CashableMeterCents);
			sas->MeterGet(METER_IN_HOUSE_RESTRICTED_TO_GM_CENTS, &RestrictedMeterCents);
			sas->MeterGet(METER_IN_HOUSE_NONRESTRICTED_TO_GM_CENTS, &NonrestrictedMeterCents);
			break;

		case TT_INHOUSE_GM_TO_H:
		case TT_INHOUSE_WIN_GM_TO_H:
			sas->MeterGet(METER_IN_HOUSE_CASHABLE_TO_HOST_CENTS, &CashableMeterCents);
			sas->MeterGet(METER_IN_HOUSE_RESTRICTED_TO_HOST_CENTS, &RestrictedMeterCents);
			sas->MeterGet(METER_IN_HOUSE_NONRESTRICTED_TO_HOST_CENTS, &NonrestrictedMeterCents);
			break;

		case TT_BONUS_COIN_OUT_WIN_H_TO_GM:
		case TT_BONUS_JACKPOT_WIN_H_TO_GM:
			sas->MeterGet(METER_BONUS_CASHABLE_TO_GM_CENTS, &CashableMeterCents);
			sas->MeterGet(METER_BONUS_NONRESTRICTED_TO_GM_CENTS, &NonrestrictedMeterCents);
			break;
	}

	// get date and time
	sas->GetTime(&sTransDate, &sTransTime);
	return true;
}


/* Store transfer flag to mirror */
int TSASAftTransaction::UpdateTransferFlag(uint8_t aftFlags)
{
	unsigned int uMirrorTransFlag = 0;
	// if bit 0 is 0
	if (!IsBitSet(cTransferFlags, 0))
	{
		uMirrorTransFlag = aftFlags;
		// Cashout mode can only be soft, tickets and receipts are not supported
		cTransferFlags = uMirrorTransFlag & 0x4B;
	}
	return true;
}

/* Returns the amounts sum in cents */
uint64_t TSASAftTransaction::GetAmountsSum() const
{
	return CashableAmountCents + RestrictedAmountCents + NonRestrictedAmountCents;
}

/* Returns the string to be loged in events */
TString TSASAftTransaction::FormatAftTransferEvent(const TSASModule* sas, uint64_t uBaseAccount) const
{
	std::string money;
	if (CashableAmountCents)
		money += " | cashable: " + sas->FormatToCurrency(CashableAmountCents);

	if (RestrictedAmountCents)
		money += " | restricted: " + sas->FormatToCurrency(RestrictedAmountCents);

	if (NonRestrictedAmountCents)
		money += " | non restricted: " + sas->FormatToCurrency(NonRestrictedAmountCents);

	std::string prefix;
	switch (cTransferType)
	{
		case TT_INHOUSE_H_TO_GM: prefix = "To gaming machine:"; break;
		case TT_INHOUSE_GM_TO_H: prefix = "From gaming machine:"; break;
		case TT_INHOUSE_WIN_GM_TO_H: prefix = "Win from gaming machine to host:"; break;
		case TT_BONUS_COIN_OUT_WIN_H_TO_GM: prefix = "Bonus coin out win amount from host to gaming machine:"; break;
		case TT_BONUS_JACKPOT_WIN_H_TO_GM: prefix = "Bonus jackpot win amount from host to gaming machine:"; break;
		default: prefix = "Unknown transfer:"; break;
	}

	std::string ret = prefix + money;
	if (!sTransactionID.empty())
		ret += " | TransactionID=" + sTransactionID;

	return ret;
}


/* Decrease values in a way they become divided with base account unit */
int TSASAftTransaction::MakeBaseAccountMultiplied(uint64_t uBaseAccount)
{
	// only if we have a partial transfer
	if (cTransferCode != AFT_TC_PARTIAL_TRANSFER)
		return true;

	CashableAmountCents = (CashableAmountCents / uBaseAccount) * uBaseAccount;
	RestrictedAmountCents = (RestrictedAmountCents / uBaseAccount) * uBaseAccount;
	NonRestrictedAmountCents = (NonRestrictedAmountCents / uBaseAccount) * uBaseAccount;

	return true;
}

// returns true if only partial amounts can be transfered due to credit or aft in limits
bool CalcPartialAmounts(uint64_t uLimit, uint64_t uCurrentCredits, uint64_t uBaseAccount, uint64_t uCashable, uint64_t uRestricted, uint64_t uNonRestricted,
                        uint64_t& uPartialCashable, uint64_t& uPartialRestricted, uint64_t& uPartialNonRestricted)
{
	bool bRes = false;
	uint64_t uOddValue = 0, uPartialAmount = 0, uMin = 0;

	uPartialCashable = uCashable;
	uPartialRestricted = uRestricted;
	uPartialNonRestricted = uNonRestricted;
	// -------------- check base account -------------------------
	if (uCashable % uBaseAccount != 0)
	{
		uPartialCashable = (uCashable / uBaseAccount) * uBaseAccount;
		bRes = true;
	}
	if (uRestricted % uBaseAccount != 0)
	{
		uPartialRestricted = (uRestricted / uBaseAccount) * uBaseAccount;
		bRes = true;
	}
	if (uNonRestricted % uBaseAccount != 0)
	{
		uPartialNonRestricted = (uNonRestricted / uBaseAccount) * uBaseAccount;
		bRes = true;
	}
	// -------------- check limits -------------------------
	uint64_t uAmountToLimit = uLimit ? (uLimit - uCurrentCredits * uBaseAccount) : 0U;
	uPartialAmount = uPartialCashable + uPartialNonRestricted + uPartialRestricted;
	if (uLimit && uAmountToLimit < uPartialAmount)
	{
		uOddValue = uPartialAmount - uAmountToLimit;
		uMin = std::min(uPartialCashable, uOddValue);
		uPartialCashable -= uMin;
		uOddValue -= uMin;
		bRes = true;
	}
	uPartialAmount = uPartialCashable + uPartialNonRestricted + uPartialRestricted;
	if (uLimit && uAmountToLimit < uPartialAmount)
	{
		uOddValue = uPartialAmount - uAmountToLimit;
		uMin = std::min(uPartialNonRestricted, uOddValue);
		uPartialNonRestricted -= uMin;
		uOddValue -= uMin;
		bRes = true;
	}
	uPartialAmount = uPartialCashable + uPartialNonRestricted + uPartialRestricted;
	if (uLimit && uAmountToLimit < uPartialAmount)
	{
		uOddValue = uPartialAmount - uAmountToLimit;
		uMin = std::min(uPartialRestricted, uOddValue);
		uPartialRestricted -= uMin;
		uOddValue -= uMin;
		bRes = true;
	}

	return bRes;
}

/* Returns ACK or ERR_ACK if not sucsesful */
TString TSASAft::ProcessTransaction(const TString& sTransaction)
{
	TString sError, sCashableAmount, sMirrorCmds, sCredit, sResult;
	TString sLastTrID, sPlayerWarning, sPlayerCmds, sLastCashOut;
	uint64_t iDBCredit = 0;
	int nTransferStatusCode = -1;
	uint64_t iDBCreditTransfered = 0;
	int bTransferBonusWin = false, bTransferBonusJackpot = false;
	uint64_t uCreditLimit = 0, u = 0;
	uint64_t uAFTInLimit = 0;
	uint64_t uAFTOutLimit = 0;
	uint64_t uBeforeCredit = 0;
	uint64_t uAfterCredit = 0;
	bool bSumAmountsIsZero = false;
	bool bPartialTransferWasDone = false;
	// have to be int64 since trans can have 10 digit numbers
	uint64_t uLongLong = 0, uAmount = 0;
	uint64_t uPartialCashableAmount = 0, uPartialRestrictedAmount = 0, uPartialNonRestrictedAmount = 0;
	uint64_t uNewCreditCents = 0;
	uint64_t uLimit = 0;
	uint64_t uNewRestrictedCreditMeter = 0;
	// UNUSED unsigned int       uNewNonRestrictedCreditMeter = 0;
	// UNUSED unsigned int       uMMCountInc = 0;
	TSASAftTransaction cTrans;

	// get transaction
	if (!cTrans.FromStr(sTransaction))
	{
		nTransferStatusCode = TSC_NOT_VALID_TR_FUNCTION;
		TLOG(LogSAS, Error, "AFT transaction string invalid: %s", sTransaction.c_str());
	}

	// get credit limit
	if (!pMaxCredits)
	{
		sError = "MAX CREDITS limit not defined.";
		nTransferStatusCode = TSC_UNEXPECTED_ERROR;
	}
	else
	{
		uCreditLimit = pMaxCredits->AsInteger();
	}

	// get base account
	uint64_t uBaseAccount = 0;    // TOLE SEM(enej) iz unsigned int prestavil na uint64_t
	if (!pSASModule->BaseAccountUnitGet(&uBaseAccount))
		nTransferStatusCode = TSC_NO_GM_DENOMINATION_SET;

	// get aft in limit
	if (!pAftInLimit)
	{
		sError = "AFT to GM limit not defined.";
		nTransferStatusCode = TSC_UNEXPECTED_ERROR;
	}
	else
	{
		uAFTInLimit = pAftInLimit->AsInteger();
	}

	if (uBaseAccount)
		uAFTInLimit = (uAFTInLimit * 100) / uBaseAccount;

	// get aft out limit
	if (!pAftMaxPayout)
	{
		sError = "AFT to host limit not defined.";
		nTransferStatusCode = TSC_UNEXPECTED_ERROR;
	}
	else
	{
		uAFTOutLimit = pAftMaxPayout->AsInteger();
	}

	if (uBaseAccount)
		uAFTOutLimit = (uAFTOutLimit * 100) / uBaseAccount;

	// decrease values if not an even multiplayer of base account
	// cTrans.MakeBaseAccountMultiplied();

	// transaction ID must be different from the last valid transfer
	GetLastTransactionID(sLastTrID);
	if (sLastTrID == cTrans.sTransactionID)
		nTransferStatusCode = TSC_TRANSACTION_ID_NOT_UNIQUE;

	// if win credits are waiting do not allow other types of transactions
	if (pSASModule->AFTHostWinCashoutCreditsGet() > 0 && cTrans.cTransferType != TT_INHOUSE_WIN_GM_TO_H)
	{
		sError = "Win from game is waiting to be paid";
		nTransferStatusCode = TSC_UNEXPECTED_ERROR;
	}
	// if no win credits are waiting and win to host transaction
	if (pSASModule->AFTHostWinCashoutCreditsGet() == 0 && cTrans.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
	{
		sError = "No win credits for cashout";
		nTransferStatusCode = TSC_NO_WON_CREDITS_FOR_CASHOUT;
	}
	// get credits
	// if ((iDBCredit = Get(ITEM_CURR_CREDIT, iclient_id)) == -1)
	if (!pSASModule->MeterGet(METER_CREDIT, &uLongLong))
	{
		sError = "Cant get credits";
		nTransferStatusCode = TSC_UNEXPECTED_ERROR;
	}
	iDBCredit = uLongLong;

	uAmount = cTrans.GetAmountsSum();
	if (cTrans.cTransferType == TT_BONUS_COIN_OUT_WIN_H_TO_GM)
		bTransferBonusWin = true;
	if (cTrans.cTransferType == TT_BONUS_JACKPOT_WIN_H_TO_GM)
		bTransferBonusJackpot = true;

	// bonus transfer cannot be restricted
	if ((bTransferBonusWin || bTransferBonusJackpot) && cTrans.RestrictedAmountCents)
	{
		sError = "Bonus transfer cannot have restricted amounts";
		nTransferStatusCode = TSC_NOT_VALID_TR_FUNCTION;
	}

	if (nTransferStatusCode == -1)
	{
		// ------------ From Host To Gaming machine ---------------------
		if ((cTrans.cTransferType == TT_INHOUSE_H_TO_GM) || bTransferBonusWin || bTransferBonusJackpot)
		{
			// if partial transfers allowed
			if (cTrans.cTransferCode == AFT_TC_PARTIAL_TRANSFER)
			{
				uLimit = 0;
				if (uCreditLimit != 0 && uAFTInLimit != 0)    // oba vklopljena, izberemo manjsega
					uLimit = std::min(uCreditLimit * uBaseAccount, uAFTInLimit * uBaseAccount);
				else if (uAFTInLimit != 0 && uCreditLimit == 0)    // samo AFT limit vklopljen
					uLimit = uAFTInLimit * uBaseAccount;
				else if (uCreditLimit != 0 && uAFTInLimit == 0)    // samo Credit limit vklopljen
					uLimit = uCreditLimit * uBaseAccount;

				// if (uLimit && iDBCredit * uBaseAccount <= uLimit)
				//	uLimit = uLimit - iDBCredit * uBaseAccount;

				bPartialTransferWasDone = true;
				// if transfer for less then base account unit
				if ((cTrans.CashableAmountCents && (cTrans.CashableAmountCents < uBaseAccount)) ||
				    (!cTrans.CashableAmountCents && cTrans.RestrictedAmountCents && (cTrans.RestrictedAmountCents < uBaseAccount)) ||
				    (!cTrans.CashableAmountCents && !cTrans.RestrictedAmountCents && cTrans.NonRestrictedAmountCents && (cTrans.NonRestrictedAmountCents < uBaseAccount)))
					nTransferStatusCode = TSC_AMOUNT_NOT_MULTIPLE_GM_DENOM;
				// if credit limit enabled and does not allow to transfer
				else if (uLimit && uLimit <= uBaseAccount * iDBCredit)
					nTransferStatusCode = TSC_AMOUNT_EXCEEDS_GM_TR_LIMIT;
				else if (CalcPartialAmounts(uLimit, iDBCredit, uBaseAccount, cTrans.CashableAmountCents, cTrans.RestrictedAmountCents, cTrans.NonRestrictedAmountCents,
				                            uPartialCashableAmount, uPartialRestrictedAmount, uPartialNonRestrictedAmount))
				{
					cTrans.CashableAmountCents = uPartialCashableAmount;
					cTrans.RestrictedAmountCents = uPartialRestrictedAmount;
					cTrans.NonRestrictedAmountCents = uPartialNonRestrictedAmount;
					uAmount = cTrans.GetAmountsSum();
				}
			}
			else
			{
				uNewCreditCents = iDBCredit * uBaseAccount + uAmount;
				// if bonus transfer don't check limits
				if (!bTransferBonusWin && !bTransferBonusJackpot)
				{
					// check credit limit
					if (uCreditLimit && (uNewCreditCents > uCreditLimit * uBaseAccount))
					{
						sPlayerWarning = sError = "Credit limit reached";
						nTransferStatusCode = TSC_UNEXPECTED_ERROR;
					}
					// check aft in limit
					if (uAFTInLimit && uAmount > uAFTInLimit * 100)
					{
						sPlayerWarning = sError = "Transfer limit reached";
						nTransferStatusCode = TSC_AMOUNT_EXCEEDS_GM_TR_LIMIT;
					}
				}
				// division with base account
				if (uAmount % uBaseAccount != 0)
					nTransferStatusCode = TSC_AMOUNT_NOT_MULTIPLE_GM_DENOM;
			}

			if (nTransferStatusCode == -1)
			{
				// do payin and increment counters
				if (((!bTransferBonusWin && !bTransferBonusJackpot) &&
				     pSASModule->_AFTPayIn(cTrans.CashableAmountCents / uBaseAccount, cTrans.RestrictedAmountCents / uBaseAccount,
				                           cTrans.NonRestrictedAmountCents / uBaseAccount, &uBeforeCredit, &uAfterCredit)) ||
				    ((bTransferBonusWin || bTransferBonusJackpot) &&
				     pSASModule->_AFTExternalBonusAward(bTransferBonusJackpot, cTrans.CashableAmountCents / uBaseAccount, cTrans.RestrictedAmountCents / uBaseAccount,
				                                        cTrans.NonRestrictedAmountCents / uBaseAccount, &uBeforeCredit, &uAfterCredit)))
				{
					// --------------  update electronic counters !!! ONLY IF NOT JACKPOTS; those are updated in balance manager! --------------
					if (!bTransferBonusWin && !bTransferBonusJackpot)
					{
						// se povecuje v IncrementPayINCounters
						// icredits = uAmount / uBaseAccount;
						// pSASModule->MeterAdd(METER_TOTAL_ELECTONIC_TRANSFERS_TO_GM, icredits);
						// pSASModule->MeterAdd(METER_TOTAL_DROP, icredits); //NOTE: tole sem umaknil, ker so se payout dvakrat povečali(tukaj in v _AFTPayIN() )
					}
					// --- Cashable ---
					if (cTrans.CashableAmountCents)
					{
						if (bTransferBonusWin || bTransferBonusJackpot)
						{
							pSASModule->MeterAdd(METER_BONUS_CASHABLE_TO_GM_CENTS, cTrans.CashableAmountCents);
							pSASModule->MeterAdd(METER_BONUS_CASHABLE_TO_GM_QTY, 1);
						}
						else
						{
							pSASModule->MeterAdd(METER_IN_HOUSE_CASHABLE_TO_GM_CENTS, cTrans.CashableAmountCents);
							pSASModule->MeterAdd(METER_IN_HOUSE_CASHABLE_TO_GM_QTY, 1);
						}
					}
					// --- Restricted ---
					if (cTrans.RestrictedAmountCents)
					{
						pSASModule->MeterAdd(METER_IN_HOUSE_RESTRICTED_TO_GM_CENTS, cTrans.RestrictedAmountCents);
						pSASModule->MeterAdd(METER_IN_HOUSE_RESTRICTED_TO_GM_QTY, 1);

						pSASModule->ParamSet("PREV_RESTRICTED_EXPIRATION", cTrans.sExpiration);
						pSASModule->ParamSet("PREV_POOL_ID", std::to_string(cTrans.shPoolID));
					}
					// --- NonRestricted ---
					if (cTrans.NonRestrictedAmountCents)
					{
						if (bTransferBonusWin || bTransferBonusJackpot)
						{
							pSASModule->MeterAdd(METER_BONUS_NONRESTRICTED_TO_GM_CENTS, cTrans.NonRestrictedAmountCents);
							pSASModule->MeterAdd(METER_BONUS_NONRESTRICTED_TO_GM_QTY, 1);
						}
						else
						{
							pSASModule->MeterAdd(METER_IN_HOUSE_NONRESTRICTED_TO_GM_CENTS, cTrans.NonRestrictedAmountCents);
							pSASModule->MeterAdd(METER_IN_HOUSE_NONRESTRICTED_TO_GM_QTY, 1);
						}
					}

					// -------------- Transaction To Buff --------------
					cTrans.cTransferStatus = bPartialTransferWasDone ? TSC_PARTIAL_TR_SUCCESSFUL : TSC_FULL_TR_SUCCESSFUL;
					cTrans.GetCountersAndTime(pSASModule);
					CommitToBuffer(&cTrans, &sMirrorCmds, true, true);

					// -------------- Event to database --------------
					TLOG(LogSAS, Info, "AFT: %s", cTrans.FormatAftTransferEvent(pSASModule, uBaseAccount).c_str());
					sResult = "ACK";
				}
				else
					nTransferStatusCode = TSC_UNEXPECTED_ERROR;
			}
		}
		// ------------ From Gaming machine to Host ---------------------
		if (cTrans.cTransferType == TT_INHOUSE_GM_TO_H || cTrans.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
		{
			uint64_t uCashableCredit = 0, uRestrictedCredit = 0, uNonRestrictedCredit = 0;
			uint64_t lRestrictedCredit = 0, lNonRestrictedCredit = 0;

			// if win is transfered to host
			if (cTrans.cTransferType == TT_INHOUSE_WIN_GM_TO_H && pSASModule->AFTHostWinCashoutCreditsGet() > 0)
			{
				iDBCredit = pSASModule->AFTHostWinCashoutCreditsGet();
				// we cant allow to transfer less then the whole win from game
				if (cTrans.CashableAmountCents < iDBCredit * uBaseAccount)
				{
					sError = "Cannot transfer just a part of win from game";
					nTransferStatusCode = TSC_UNEXPECTED_ERROR;
				}
			}

			if (!pSASModule->MeterGet(METER_RESTRICTED_CREDIT, &lRestrictedCredit) ||
			    !pSASModule->MeterGet(METER_NONRESTRICTED_CREDIT,
			                          &lNonRestrictedCredit))    /// izplačilo NONRESTRICTED v RULETI trenutno ni podprto, zato je vedno 0NONRESTRICTED
			{
				sError = "Cannot get meters for restricted, nonrestricted credit";
				nTransferStatusCode = TSC_UNEXPECTED_ERROR;
			}
			uRestrictedCredit = lRestrictedCredit;
			uNonRestrictedCredit = lNonRestrictedCredit;

			uCashableCredit = iDBCredit;

			// iDBCredit vsebuje tudi že uRestrictedCredit in uNonRestrictedCredit, zato moramo odšteti
			if (uCashableCredit > uRestrictedCredit)
				uCashableCredit = uCashableCredit - uRestrictedCredit;
			else
				uCashableCredit = 0;
			if (uCashableCredit > uNonRestrictedCredit)
				uCashableCredit = uCashableCredit - uNonRestrictedCredit;
			else
				uCashableCredit = 0;

			uint64_t uCashableAmount = cTrans.CashableAmountCents, uRestrictedAmount = cTrans.RestrictedAmountCents,
			         uNonRestrictedAmount = cTrans.NonRestrictedAmountCents;
			// ----------------- Partial transfer GM -> host ------------------------
			if (cTrans.cTransferCode == AFT_TC_PARTIAL_TRANSFER)
			{
				iDBCreditTransfered = 0;
				// allow to transfer exact amount specified or less
				// ------------------------------- cashable ---------------------------------
				// if exceeds pay out limit
				if (uAFTOutLimit && uCashableAmount > uAFTOutLimit * uBaseAccount)
					uCashableAmount = uAFTOutLimit * uBaseAccount;
				if (uCashableCredit * uBaseAccount > uCashableAmount)
					uCashableCredit = uCashableAmount / uBaseAccount;
				// if exceeds pay out limit
				// if (uAFTOutLimit && uCashableCredit > uAFTOutLimit)
				//  uCashableCredit = uAFTOutLimit;
				iDBCreditTransfered += uCashableCredit;

				// ------------------------------ restricted --------------------------------
				if (uAFTOutLimit && uRestrictedAmount > (uAFTOutLimit - uCashableCredit) * uBaseAccount)
					uRestrictedAmount = (uAFTOutLimit - uCashableCredit) * uBaseAccount;
				if (uRestrictedCredit * uBaseAccount > uRestrictedAmount)
				{
					u = uRestrictedAmount / uBaseAccount;
					uNewRestrictedCreditMeter = uRestrictedCredit - u;
					uRestrictedCredit = u;
				}
				iDBCreditTransfered += uRestrictedCredit;

				// ----------------------------- nonrestricted ------------------------------
				if (uAFTOutLimit && uNonRestrictedAmount > (uAFTOutLimit - uCashableCredit - uRestrictedCredit) * uBaseAccount)
					uNonRestrictedAmount = (uAFTOutLimit - uCashableCredit - uRestrictedCredit) * uBaseAccount;
				if (uNonRestrictedCredit * uBaseAccount > uNonRestrictedAmount)
				{
					u = uNonRestrictedAmount / uBaseAccount;
					// UNUSED uNewNonRestrictedCreditMeter = uNonRestrictedCredit - u;
					uNonRestrictedCredit = u;
				}
				iDBCreditTransfered += uNonRestrictedCredit;
			}
			else
			{
				// ----------------- Full transfer ------------------------
				iDBCreditTransfered = iDBCredit;
				// check if enough credit
				if (uCashableCredit * (uint64_t)uBaseAccount > uCashableAmount)
				{
					sError = yutils::Format("Credit on GM (%s) greater than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uCashableCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uCashableAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}
				if (uCashableCredit * (uint64_t)uBaseAccount < uCashableAmount)
				{
					sError = yutils::Format("Credit on GM (%s) smaller than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uCashableCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uCashableAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}

				if (uRestrictedCredit * (uint64_t)uBaseAccount > uRestrictedAmount)
				{
					sError = yutils::Format("Restricted credit on GM (%s) greater than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uRestrictedCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uRestrictedAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}
				if (uRestrictedCredit * (uint64_t)uBaseAccount < uRestrictedAmount)
				{
					sError = yutils::Format("Restricted credit on GM (%s) smaller than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uRestrictedCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uRestrictedAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}

				if (uNonRestrictedCredit * (uint64_t)uBaseAccount > uNonRestrictedAmount)
				{
					sError = yutils::Format("NonRestricted credit on GM (%s) greater than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uNonRestrictedCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uNonRestrictedAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}
				if (uNonRestrictedCredit * (uint64_t)uBaseAccount < uNonRestrictedAmount)
				{
					sError = yutils::Format("NonRestricted credit on GM (%s) smaller than the requested amount to transfer to host (%s).",
					                        pSASModule->FormatToCurrency(uNonRestrictedCredit * (uint64_t)uBaseAccount, true).c_str(),
					                        pSASModule->FormatToCurrency(uNonRestrictedAmount, true).c_str());
					nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
				}

				// check aft out limit
				if (uAFTOutLimit && iDBCredit > uAFTOutLimit)
				{
					sError = "Transfer amount exceeds max AFT payout limit";
					nTransferStatusCode = TSC_AMOUNT_EXCEEDS_GM_TR_LIMIT;
				}
			}

			// if all amounts are 0 -> Hand Pay
			if (cTrans.GetAmountsSum() == 0)
			{
				bSumAmountsIsZero = true;
				// sError.Format("All three requested amounts are zero. Handpay will be performed.");
				// nTransferStatusCode = TSC_NOT_VALID_AMOUNT_OR_EXPIRATION;
			}

			// if all OK
			if (nTransferStatusCode == -1)
			{
				// decrement credit
				// unsigned int bCreditDecremented = false;
				unsigned int bJackpotPaying = false;
				uint64_t uCreditBefore = 0, uCreditAfter = 0;

				uint64_t bCreditDecremented = pSASModule->_AFTPayOut(uCashableCredit, uRestrictedCredit, uNonRestrictedCredit, &uCreditBefore, &uCreditAfter);

				if (iDBCreditTransfered + uCreditAfter != uCreditBefore)
				{
					TLOG(
					  LogSAS, Error,
					  "WARNING: AFT PayOut, credit transferred to host (%d) plus credit remaining on GM after transaction (%d) different from the credit on GM before transaction (%d).",
					  iDBCreditTransfered, uCreditAfter, uCreditBefore);
				}

				// bCreditDecremented = ((Set(ITEM_CURR_CREDIT, iclient_id, 0)) != -1);

				if (bCreditDecremented)
				{
					// --------------  update electronic counters --------------
					// METER_TOTAL_CREDIT_CANCEL is updated by PayOut() function


					//!!! spodnjo vrstico smo zakomentirali, ker se skupni counter povecuje ze v TCounterSystem::IncrementPayOUTCounters
					// pSASModule->MeterAdd(METER_TOTAL_ELECTONIC_TRANSFERS_TO_HOST, iDBCreditTransfered /*prej iDBCredit, ki ne vsebuje tudi restricted in
					// nonrestricted*/);

					if (uCashableCredit > 0)
					{
						pSASModule->MeterAdd(METER_IN_HOUSE_CASHABLE_TO_HOST_CENTS, uCashableCredit * uBaseAccount);
						pSASModule->MeterAdd(METER_IN_HOUSE_CASHABLE_TO_HOST_QTY, 1);
					}
					if ((uRestrictedCredit > 0) && !bJackpotPaying)
					{
						unsigned int uPollID;
						pSASModule->MeterAdd(METER_IN_HOUSE_RESTRICTED_TO_HOST_CENTS, uRestrictedCredit * uBaseAccount);
						pSASModule->MeterAdd(METER_IN_HOUSE_RESTRICTED_TO_HOST_QTY, 1);

						pSASModule->MeterReset(METER_RESTRICTED_CREDIT);
						pSASModule->MeterAdd(METER_RESTRICTED_CREDIT, uNewRestrictedCreditMeter);
						pSASModule->ParamGet("PREV_RESTRICTED_EXPIRATION", cTrans.sExpiration);
						pSASModule->ParamGet("PREV_POOL_ID", uPollID);
						cTrans.shPoolID = (short)uPollID;
						// reset restricted expiration values
						pSASModule->ParamSet("PREV_RESTRICTED_EXPIRATION", "");
						pSASModule->ParamSet("PREV_POOL_ID", "0");
					}
					if ((uNonRestrictedCredit > 0) && !bJackpotPaying)
					{
						pSASModule->MeterAdd(METER_IN_HOUSE_NONRESTRICTED_TO_HOST_CENTS, uNonRestrictedCredit * uBaseAccount);
						pSASModule->MeterAdd(METER_IN_HOUSE_NONRESTRICTED_TO_HOST_QTY, 1);
						//            pSASModule->MeterReset(METER_NONRESTRICTED_CREDIT);
						//            pSASModule->MeterAdd(METER_NONRESTRICTED_CREDIT, uNewNonRestrictedCreditMeter);
					}

					// -------------- Transaction To Buff --------------
					cTrans.CashableAmountCents = uCashableCredit * uBaseAccount;
					cTrans.RestrictedAmountCents = uRestrictedCredit * uBaseAccount;
					cTrans.NonRestrictedAmountCents = uNonRestrictedCredit * uBaseAccount;
					if ((uRestrictedCredit > 0) && !bJackpotPaying)
					{
						unsigned int uPollID;
						// set expiration and poll_id
						pSASModule->ParamGet("PREV_RESTRICTED_EXPIRATION", cTrans.sExpiration);
						pSASModule->ParamGet("PREV_POOL_ID", uPollID);
						cTrans.shPoolID = (short)uPollID;
					}
					cTrans.cTransferStatus = (cTrans.cTransferCode == AFT_TC_PARTIAL_TRANSFER) ? TSC_PARTIAL_TR_SUCCESSFUL : TSC_FULL_TR_SUCCESSFUL;
					cTrans.GetCountersAndTime(pSASModule);
					CommitToBuffer(&cTrans, &sMirrorCmds, !bSumAmountsIsZero);

					// -------------- Event to database --------------
					TLOG(LogSAS, Info, "AFT: ", cTrans.FormatAftTransferEvent(pSASModule, uBaseAccount).c_str());
					sResult = "ACK";
				}
				else
					nTransferStatusCode = TSC_UNEXPECTED_ERROR;
			}
		}
	}
	// Transaction not sucsesful
	if (nTransferStatusCode != -1)
	{
		cTrans.cTransferStatus = nTransferStatusCode;
		// set current time
		cTrans.GetCountersAndTime(pSASModule);
		// set all amounts to 0
		cTrans.CashableAmountCents = cTrans.RestrictedAmountCents = cTrans.NonRestrictedAmountCents = 0;
		CommitToBuffer(&cTrans, &sMirrorCmds);

		const char* cTransferStatus = GetAFTTransferStatusCodesTxt(nTransferStatusCode);
		// MachineEvent(EVENT_AFT_TRANSFER_ERROR, iclient_id, NULL_REC_VALUE, NULL_REC_VALUE, cTransferStatus + ": " + sError);
		TLOG(LogSAS, Error, "AFT %s: %s: %s", cTransferStatus, cTrans.FormatAftTransferEvent(pSASModule, uBaseAccount).c_str(), sError.c_str());


		if (!sPlayerWarning.empty())
			pSASModule->ShowMessage(sPlayerWarning, 6000);

		sResult = "ERR_ACK";
	}
	return sResult;
}


void TSASAft::AFTWorkTask()
{
	TSASAftTransaction cAftTransaction, cLastTransaction;
	TSASTimer tCompletionStatusReqTimeOut;
	TString sTicketsToPrint, sTicketToPrint, sTicketRecToPrint, sCmd, sRes, sTrans;
	bool bAFTWinCashoutInProgress = false;
	unsigned int uTransferStatus;

	// reset the aft transfer status in order to avoid possible problems
	pTransferStatus->SetValue((long)AFT_TS_READY_FOR_CYCLE);
	pApp->AddToStoreQueue(pTransferStatus);
	while (!pSASModule->IsAwaitingClose())
	{
		// AFT WIN CASHOUT - pay out win from game
		if ((pSASModule->AFTHostWinCashoutCreditsGet() > 0) && !bAFTWinCashoutInProgress)
		{
			if (StartCashout())
				bAFTWinCashoutInProgress = true;
			else
			{
				pSASModule->AFTHostWinCashoutFailed(pSASModule->AFTHostWinCashoutCreditsGet());
				pSASModule->AFTHostWinCashoutCreditsSet(0);
				bAFTWinCashoutInProgress = false;
			}
		}

		// AFT CASHOUT
		const uint64_t creditsToCashout = pSASModule->AFTCashoutCreditsGet();
		if (creditsToCashout)
		{
			pApp->WriteLog(LT_INFO, NO_GAME_ID, "Starting AFT cashout for %lu credits!", creditsToCashout);
			const bool bSuccess = StartCashout();
			pSASModule->AFTCashoutCreditsSet(0);
			if (!bSuccess)
			{
				TString sPayoutPriority;
				pSASModule->ParamGet("SAS_CASHOUT_PRIORITY", sPayoutPriority);

				// if the cashout priority is TICKET, it makes no sense to attempt a ticket payout, because it was done first before trying an AFT transfer
				if (sPayoutPriority == "TICKET" || !((TSASModule*)pSASModule)->_TryCashoutViaTicket(creditsToCashout))
					pSASModule->StartHandpay_sasRequest(creditsToCashout, pSASModule->getHandpayMode());
			}
		}

		// ------------ Lock ------------
		if (m_bLock)
		{
			// m_bBillAcceptorDisabled = true;
			pParamBillAcceptorEnabled_AFT->SetValue(0, true);

			// pSASModule->SASExceptionAdd(SAS_EXC_AFT_GAME_LOCKED);
			m_tLockExcResendTimer.Start(5000);
			m_bLock = false;
			// set the lock timer
			m_tLockTimer.Start(m_uLockTimeout);
			pSASModule->ShowMessage(LocalizedMessage(ELECTRONIC_MONEY_TRANSFER_STRING));
		}

		if (!pParamBillAcceptorEnabled_AFT->AsBoolean() /*m_bBillAcceptorDisabled*/ && pSASModule->GetAFTLock() == AFT_GAME_NOT_LOCKED)
		{
			// m_bBillAcceptorDisabled = false;
			pParamBillAcceptorEnabled_AFT->SetValue(1, true);
		}

		// ------------ Cancel Lock ------------
		if (m_bCancelLock)
		{
			m_bCancelLock = false;
			m_tHideMsgTimer.Start(2000);
		}

		// ------------ Hide Msg ------------
		if (m_tHideMsgTimer.IsSet() && m_tHideMsgTimer.IsTimeout())
		{
			m_tHideMsgTimer.Stop();
			pSASModule->HideMessage();
		}

		// resend 0x6F every 5 seconds while locked
		if (pSASModule->GetAFTLock() == AFT_GAME_LOCKED && m_tLockExcResendTimer.IsTimeout())
		{
			pSASModule->SASExceptionAdd(SAS_EXC_AFT_GAME_LOCKED);
			m_tLockExcResendTimer.Start(5000);
		}

		// if in lock state and timer expieres or link down
		if (pSASModule->GetAFTLock() == AFT_GAME_LOCKED && (m_tLockTimer.IsTimeout() || pSASModule->IsLinkDown()))
			LockCancel();

		// ------------ transaction pending, process it ------------
		// if (GetTransferStatus() == AFT_TS_SENT_040)
		sTrans = pCurrentTransaction->Value().c_str();
		if (!sTrans.empty())
		{
			// log if TransferStatus is not AFT_TS_SENT_040
			if (GetTransferStatus() != AFT_TS_SENT_040)
			{
				TLOG(LogSAS, Error, "AFT_CURRENT_TRANSACTION is not empty, the transfer status is not AFT_TS_SENT_040 but %d", GetTransferStatus());
				/** tole sem dodal poskusno, ker če je transfer status v nekem drugem stanju, ne smemo procesirati */
				/** izgleda da tole resuje problem, ampak preveri se sideeffects!!!! */
				pCurrentTransaction->SetValue("");
				pApp->AddToStoreQueue(pCurrentTransaction);
				continue;    // gremo ponovno
			}

			sRes = ProcessTransaction(sTrans);
			if (sRes == "ACK" || sRes == "ERR_ACK")
			{
				TLOG(LogSAS, Info, "AFT transfer complete");
				pSASModule->SASExceptionAdd(SAS_EXC_AFT_TRANSFER_COMPLETE);
			}

			// ------------- From GM to HOST ----------------
			if (!cAftTransaction.FromStr(sTrans))
			{
				TLOG(LogSAS, Error, "AFT transaction string invalid: %s", sTrans.c_str());
				// todo: maybe do something about this?
			}

			if (cAftTransaction.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
			{
				if (sRes == "ERR_ACK")
				{
					pSASModule->AFTHostWinCashoutFailed(pSASModule->AFTHostWinCashoutCreditsGet());
				}
				pSASModule->AFTHostWinCashoutCreditsSet(0);
				bAFTWinCashoutInProgress = false;
			}
			if (cAftTransaction.cTransferType == TT_INHOUSE_GM_TO_H)
			{
				// in case of unsucsesfull Transfer from GM to HOST, go in Hand Pay
				if (sRes == "ERR_ACK")
					pSASModule->StartHandpay_sasRequest(0, pSASModule->getHandpayMode());

				pSASModule->SetAFTCardPresent(false);
				//        m_bAFTCardPresent = false;
			}
			// show the transfer message
			if (sRes == "ACK")
			{
				if (GetTransaction(0, &cLastTransaction))
				{
					if (cLastTransaction.GetAmountsSum() > 0)
					{
						// play transfer sound
						if (cAftTransaction.cTransferType == TT_INHOUSE_GM_TO_H || cAftTransaction.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
						{
							pSASModule->PlaySound("electronic_payout.wav");
						}
						else if (cAftTransaction.cTransferType == TT_INHOUSE_H_TO_GM)
						{
							pSASModule->ParamSet("AFT_AUTO_CASHOUT_STATE", std::to_string(AFT_ACO_CASH_IN));
							// pSASModule->PlaySound("electronic_payin.wav");
						}
						else
						{
							pSASModule->ShowMessages({ LocalizedMessage(TRANSFERED_STRING), pSASModule->FormatToCurrency(cLastTransaction.GetAmountsSum()) });
						}
					}
					m_tHideMsgTimer.Start(3500);
					if (cAftTransaction.cTransferType == TT_INHOUSE_GM_TO_H || cAftTransaction.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
						pSASModule->ParamSet("AFT_AUTO_CASHOUT_STATE", std::to_string(AFT_ACO_CASH_OUT_DONE));
				}
			}

			if (cAftTransaction.cTransferType == TT_INHOUSE_H_TO_GM)
				pSASModule->SetAFTCardPresent(true);    // m_bAFTCardPresent = true;
		}

		uTransferStatus = GetTransferStatus();
		if (uTransferStatus != AFT_TS_TRANS_COMMITED)
			tCompletionStatusReqTimeOut.Stop();
		// If transfer commited, wait for interogation Poll 72 to be ACKED
		else
		{
			if (!tCompletionStatusReqTimeOut.IsSet())
				tCompletionStatusReqTimeOut.Start(15000);

			// reissue exception EXC_AFT_TRANSFER_COMPLETE every 15 secs
			if (tCompletionStatusReqTimeOut.IsTimeout())
			{
				TLOG(LogSAS, Info, "AFT transfer complete on completion request timeout");
				pSASModule->SASExceptionAdd(SAS_EXC_AFT_TRANSFER_COMPLETE);
				tCompletionStatusReqTimeOut.Start(15000);
			}
		}

		// ---------------------- automatic CASHOUT ------------------------------
		if (pAftAutoCashout->Value() == "1" && IsAFTInHouseEnabled())
		{
			unsigned int uAutoCashoutState = pAftAutoCashoutState->AsInteger();

			switch (uAutoCashoutState)
			{
				case AFT_ACO_CASH_IN:
					if (pSASModule->MeterEqual(METER_CREDIT, 0))
					{
						pAftAutoCashoutState->SetValue((long)AFT_ACO_WAITING);
						pApp->AddToStoreQueue(pAftAutoCashoutState);
					}
					break;
				case AFT_ACO_WAITING:
					if (!m_tAutoCashoutTimer.IsSet())
						m_tAutoCashoutTimer.Start(AFT_ACO_DELAY);
					break;
				case AFT_ACO_DO_CASH_OUT:
					if (!m_tAutoCashoutTimer.IsSet())
						m_tAutoCashoutTimer.Start(AFT_ACO_RESTART_DELAY);
					break;
			}

			// Check timer
			if (m_tAutoCashoutTimer.IsSet() && m_tAutoCashoutTimer.IsTimeout())
			{
				m_tAutoCashoutTimer.Stop();
				const int state = pAftAutoCashoutState->AsInteger();
				if (state == AFT_ACO_WAITING || state == AFT_ACO_DO_CASH_OUT)
				{
					pAftAutoCashoutState->SetValue((long)AFT_ACO_DO_CASH_OUT);
					pApp->AddToStoreQueue(pAftAutoCashoutState);
					pSASModule->SetAFTCardPresent(false);    // m_bAFTCardPresent = false;
					// perform card out only if credit is 0
					if (pSASModule->MeterEqual(METER_CREDIT, 0))
					{
						printf("************CARDOUT1\n");
						StartCashout();
					}
					else
					{
						// credit increased meanwhile, stop with automatic cashout
						pAftAutoCashoutState->SetValue((long)AFT_ACO_NONE);
						pApp->AddToStoreQueue(pAftAutoCashoutState);
					}
				}
			}
		}
		mWorkSemaphore.try_acquire_for(std::chrono::milliseconds(100));
	}    // while
}

TSASAft::TSASAft()
{
	pSASModule = pApp->GetModuleByName<TSASModule>("SAS1");
	if (!pSASModule)
		return;

	pCurrentTransactionNumber = pApp->GetParam("AFT_CURRENT");
	if (!pCurrentTransactionNumber)
		throw std::runtime_error("Can't start SAS AFT without AFT_CURRENT");

	pCurrentTransaction = pApp->GetParam("AFT_CURRENT_TRANSACTION");
	if (!pCurrentTransaction)
		throw std::runtime_error("Can't start SAS AFT without AFT_CURRENT_TRANSACTION");

	pTransferStatus = pApp->GetParam("AFT_TRANSFER_STATUS");
	if (!pTransferStatus)
		throw std::runtime_error("Can't start SAS AFT without AFT_TRANSFER_STATUS");

	pAftTransferFlags = pApp->GetParam("AFT_TRANSFER_FLAGS");
	if (!pAftTransferFlags)
		throw std::runtime_error("Can't start SAS AFT without AFT_TRANSFER_FLAGS");

	pAftAssetNumber = pApp->GetParam("AFT_ASSET_NUMBER");
	pAftAutoCashout = pApp->GetParam("AFT_AUTO_CASHOUT");
	pAftAutoCashoutState = pApp->GetParam("AFT_AUTO_CASHOUT_STATE");
	pAftRestrictedEnabled = pApp->GetParam("AFT_RESTRICTED_ENABLED");
	pAftNonRestrictedEnabled = pApp->GetParam("AFT_NONRESTRICTED_ENABLED");
	pAftSupported = pApp->GetParam("AFT_SUPPORTED");
	pAftBonusingEnabled = pApp->GetParam("AFT_BONUS_ENABLED");
	pAftInHouseEnabled = pApp->GetParam("AFT_INHOUSE_ENABLED");

	if (!pAftAssetNumber || !pAftAutoCashout || !pAftAutoCashoutState || !pAftRestrictedEnabled || !pAftNonRestrictedEnabled || !pAftSupported || !pAftBonusingEnabled ||
	    !pAftInHouseEnabled)
		throw std::runtime_error(
		  "Can't start SAS AFT without AFT_ASSET_NUMBER, AFT_AUTO_CASHOUT, AFT_AUTO_CASHOUT_STATE, AFT_RESTRICTED_ENABLED, AFT_NONRESTRICTED_ENABLED, AFT_SUPPORTED, AFT_BONUS_ENABLED, AFT_INHOUSE_ENABLED");

	pMaxCredits = pApp->GetParam("MAX_CREDITS");
	pAftInLimit = pApp->GetParam("AFT_IN_LIMIT");
	pAftMaxPayout = pApp->GetParam("AFT_MAX_PAY_OUT");

	pParamBillAcceptorEnabled_AFT = pApp->GetParam("AFT_BILL_ACCEPTOR_ENABLED", { ParameterDomain::MINE, ParameterDomain::CACHE });

	pSASModule->SetAFTCardPresent(false);    //  m_bAFTCardPresent = false;
	m_mHistMut = SDL_CreateMutex();
	m_mTransMut = SDL_CreateMutex();
	m_hWorkTask = std::thread(&TSASAft::AFTWorkTask, this);
	pthread_setname_np(m_hWorkTask.native_handle(), "AFT");
}

TSASAft::~TSASAft()
{
	if (m_mHistMut)
		SDL_DestroyMutex(m_mHistMut);
	if (m_mTransMut)
		SDL_DestroyMutex(m_mTransMut);
}

int TSASAft::Close()
{
	if (m_hWorkTask.joinable())
		m_hWorkTask.join();

	return true;
}


/* Get value of "aft_transfer_status" */
/* If not exist retun -1              */
int TSASAft::GetTransferStatus()
{
	return pTransferStatus->AsInteger();
}


int TSASAft::TransferFundsResponse(TSASAftTransaction& cA)
{
	TString sLastTrID;
	unsigned int uAssetNumber = 0;

	// unsuported transfer types
	switch (cA.cTransferType)
	{
		case TT_INHOUSE_H_TO_TICKET:
		case TT_DEBIT_H_TO_GM:
		case TT_DEBIT_H_TO_TICKET:
			cA.cTransferStatus = TSC_UNSUPPORTED_TRANSFER_CODE;
			return false;
			break;
		case TT_INHOUSE_H_TO_GM: {
			// check if transfer of restricted to GM is disabled)
			if (pAftRestrictedEnabled->AsInteger() == 0)
			{
				// if there is a restricted amount
				if (cA.RestrictedAmountCents)
				{
					cA.cTransferStatus = TSC_UNSUPPORTED_TRANSFER_CODE;
					return false;
				}
			}
			if (pAftNonRestrictedEnabled->AsInteger() == 0)
			{
				// if there is a non-restricted amount
				if (cA.NonRestrictedAmountCents)
				{
					cA.cTransferStatus = TSC_UNSUPPORTED_TRANSFER_CODE;
					return false;
				}
			}
		}
		break;
		case TT_INHOUSE_GM_TO_H:
		case TT_INHOUSE_WIN_GM_TO_H:
			if (!IsAFTInHouseEnabled())
			{
				cA.cTransferStatus = TSC_UNSUPPORTED_TRANSFER_CODE;
				return false;
			}
			break;
		case TT_BONUS_COIN_OUT_WIN_H_TO_GM:
		case TT_BONUS_JACKPOT_WIN_H_TO_GM:
			if (!IsAFTBonusEnabled())
			{
				cA.cTransferStatus = TSC_UNSUPPORTED_TRANSFER_CODE;
				return false;
			}
			break;
	}

	// transaction receipt
	if (TransferFlag(AFT_FLAGS_TRANSACTION_RECEIPT_REQUESTED, cA.cTransferFlags))
	{
		cA.cTransferStatus = TSC_UNABLE_PRINT_RECEIPT;
		return false;
	}

	// check if client in tilt (it can be in cash out or aft locked)
	/*if ((pClientStatus->GetState() != CLI_STATUS_GAME) &&
	    (pClientStatus->GetState() != CLI_STATUS_CASH_OUT) &&
	    (pClientStatus->GetState() != CLI_STATUS_AFT_LOCK)
	    !pSASModule->IsAFTLock())
  */
	// are AFTTransactions possible ?
	if (!pSASModule->AreAFTTransactionsEnabled())
	{
		cA.cTransferStatus = TSC_UNABLE_TO_PERFORM_AT_THIS_TIME;
		return false;
	}


	// ready for cycle
	if (GetTransferStatus() != AFT_TS_READY_FOR_CYCLE)
	{
		cA.cTransferStatus = TSC_UNABLE_TO_PERFORM_AT_THIS_TIME;
		return false;
	}

	// ASSET number
	uAssetNumber = pAftAssetNumber->AsInteger();
	if ((cA.uAssetNumber == 0) || (cA.uAssetNumber != uAssetNumber))
	{
		cA.cTransferStatus = TSC_ASSET_ZERRO_OR_DOES_NOT_MATCH;
		return false;
	}

	// when initiating transfer transaction index must be zero
	if (cA.cTransactionIndex == 0)
	{
		// transaction ID must be different from the last valid transfer
		GetLastTransactionID(sLastTrID);
		if (sLastTrID == cA.sTransactionID)
		{
			cA.cTransferStatus = TSC_TRANSACTION_ID_NOT_UNIQUE;
			return false;
		}

		//------------ GM To Host --------------------
		if (cA.cTransferType == TT_INHOUSE_GM_TO_H || cA.cTransferType == TT_INHOUSE_WIN_GM_TO_H)
		{
			if (m_CashOutPollReceived)
				SDL_SemPost(m_CashOutPollReceived);
		}

		// transfer is pending
		cA.cTransferStatus = TSC_TR_PENDING;

		m_nPendingTransferStatus = AFT_TS_SENT_040;
		cA.ToStr(m_sPendingCurrentTransaction);
		m_bWaitFor72ToBeSent = true;
		return true;
	}
	else
		cA.cTransferStatus = TSC_NOT_VALID_TR_FUNCTION;
	return false;
}

int TSASAft::TransBuffResponse(int nTransactionIndex, TSASAftTransaction* cAftTr)
{
	return GetTransaction(nTransactionIndex, cAftTr);
}

/* Returns state of transfer flag          */
/* if nFlag == -1 -> get value from mirror */
int TSASAft::TransferFlag(int nBit, int nFlag)
{
	TString sStr;
	int nResult = false;
	unsigned int uTransferFlags = 0;

	if (nFlag == -1)
		uTransferFlags = pAftTransferFlags->AsInteger();
	else
		uTransferFlags = nFlag;
	nResult = (uTransferFlags & nBit) > 0 ? true : false;

	return nResult;
}

void TSASAft::DoWorkImmediately()
{
	mWorkSemaphore.release();
}

/* Returns the AFT Lock status */
char TSASAft::GetLockStatus()
{
	if (GetTransferStatus() != AFT_TS_READY_FOR_CYCLE)
		return AFT_GAME_NOT_LOCKED;
	return pSASModule->GetAFTLock();
}

void TSASAft::CheckPendingLock()
{
	CheckPendingLock(m_pendingLock);
}

void TSASAft::CheckPendingLock(uint8_t transferConditions)
{
	const Bitflag<ETransferCondition, uint8_t> conditionFlags(transferConditions);
	if (conditionFlags.IsEmpty())
		return;

	size_t numOK = 0;
	size_t numBlockedByGameround = 0;
	size_t numBlockedByGame = 0;
	size_t numConditions = 0;
	for (ETransferCondition condition : conditionFlags)
	{
		numConditions++;
		const ETransferConditionState state = pSASModule->GetTransferCondition(condition);
		if (state == ETransferConditionState::IMPOSSIBLE)
			return;
		if (state == ETransferConditionState::OK)
			numOK++;
		else if (state == ETransferConditionState::BLOCKED_BY_ACTIVE_GAMEROUND)
			numBlockedByGameround++;
		else if (state == ETransferConditionState::BLOCKED_BY_ACTIVE_GAME)
			numBlockedByGame++;
	}

	if (numConditions == numOK)
	{
		m_pendingLock = 0;
		m_bLock = true;
		pSASModule->_AFTLock(EAFTLockAction::NONE_REQUIRED);
		pSASModule->SASExceptionAdd(SAS_EXC_AFT_GAME_LOCKED);
	}
	else
	{
		if (numBlockedByGame)
			pSASModule->_AFTLock(EAFTLockAction::FINISH_GAME);
		else if (numBlockedByGameround)
			pSASModule->_AFTLock(EAFTLockAction::FINISH_GAMEROUND);
		// else{ blocked for other reasons, no action can be taken }
		m_pendingLock = transferConditions;
	}
}

bool TSASAft::IsAFTInHouseEnabled()
{
	return pAftSupported->AsInteger() == 1 && pAftInHouseEnabled->AsInteger() == 1;
}

bool TSASAft::IsAFTBonusEnabled()
{
	return pAftSupported->AsInteger() == 1 && pAftBonusingEnabled->AsInteger() == 1;
}

/* Starts the Cashout sequence for AFT */
bool TSASAft::StartCashout()
{
	TSASTimer tLPWaitTimeout;
	bool bRes = false;
	int nCard = false;
	int nExc = SAS_EXC_AFT_REQ_FOR_HOST_CASHOUT;

	// if we have to cashout credits from win
	if (pSASModule->AFTHostWinCashoutCreditsGet() > 0)
		nExc = SAS_EXC_AFT_REQ_FOR_HOST_TO_CASH_OUT_WIN;

	if (m_CashOutPollReceived)
		SDL_DestroySemaphore(m_CashOutPollReceived);
	m_CashOutPollReceived = SDL_CreateSemaphore(0);
	// SDL_Delay(1000);
	//  usleep(1000000);//počakamo, predno dodamo priority exception
	pSASModule->SASExceptionAdd(nExc);
	tLPWaitTimeout.Start(8000);

	// if(m_bAFTCardPresent)
	if (pSASModule->IsAFTCardPresent())
	{
		nCard = true;
		if (pSASModule->MeterEqual(METER_CREDIT, 0))
		{
			printf("************CARDOUT2\n");
			pSASModule->ShowMessage(LocalizedMessage(ELECTRONIC_CARD_OUT_STRING));
		}
		else
			pSASModule->ShowMessage(LocalizedMessage(ELECTRONIC_CASH_OUT_STRING));
	}

	while (!tLPWaitTimeout.IsTimeout())    // if LP not received witin 8 seconds give up
	{
		// if comm link down
		if (pSASModule->IsLinkDown())
		{
			bRes = false;
			break;
		}

		// if LP received
		if (SDL_SemWaitTimeout(m_CashOutPollReceived, 800) == 0)
		{
			bRes = true;
			break;
		}
		// resend exception
		pSASModule->SASExceptionAdd(nExc);
	}
	SDL_DestroySemaphore(m_CashOutPollReceived);
	m_CashOutPollReceived = NULL;
	// remove a possibly waiting 0x6A from the queue
	pSASModule->SASRemovePriorityException(nExc);

	/*if (!tLPWaitTimeout.IsTimeout())
	    SDL_Delay(1500);*/

	if (!bRes || nCard)
		pSASModule->HideMessage();

	return bRes;
}


/* Return curr amounts in cents */
void TSASAft::GetCurrentAmounts(uint64_t& CurrCashableCents, uint64_t& CurrRestCents, uint64_t& CurrNonRestCents, uint64_t& CurrTransLimitCents)
{
	uint64_t totalCredit = 0;
	pSASModule->MeterGet(METER_CREDIT, &totalCredit);    // here in credits
	pSASModule->MeterGet(METER_RESTRICTED_CREDIT, &CurrRestCents);    // here in credits
	pSASModule->MeterGet(METER_NONRESTRICTED_CREDIT, &CurrNonRestCents);    // here in credits

	uint64_t uBaseAccount;
	pSASModule->BaseAccountUnitGet(&uBaseAccount);

	CurrRestCents *= uBaseAccount;    // translate to cents
	CurrNonRestCents *= uBaseAccount;    // translate to cents
	CurrCashableCents = totalCredit * uBaseAccount - (CurrRestCents + CurrNonRestCents);

	// current transfer limit
	uint64_t uCreditLimit = 0;
	if (pMaxCredits)
		uCreditLimit = pMaxCredits->AsInteger() * uBaseAccount;

	if (uCreditLimit)
		uCreditLimit = std::min(uCreditLimit, MAX_CREDITS);    // 9.999 milijard oz. 10 cifer
	else
		uCreditLimit = MAX_CREDITS;
	uCreditLimit -= totalCredit * uBaseAccount;    // maximum possible additional credits

	CurrTransLimitCents = 0;
	if (pAftInLimit)
		CurrTransLimitCents = pAftInLimit->AsInteger() * 100;

	if (CurrTransLimitCents)
		CurrTransLimitCents = std::min(CurrTransLimitCents, uCreditLimit);
	else
		CurrTransLimitCents = uCreditLimit;
}

void TSASAft::LockRequest(uint8_t cTransferCondition, uint32_t lockTimeoutMs)
{    // NOTE: Ta funkcija se klice iz TSASChannelWorkThread, ni pa thread safe - lahko je tukaj problem odletavanja!!!
	TLOG(LogApp, Info, "SAS lock request received with conditions %02X and timeout of %ums...", (int)cTransferCondition, lockTimeoutMs);
	m_uLockTimeout = lockTimeoutMs;
	CheckPendingLock(cTransferCondition);
}

void TSASAft::LockCancel()
{
	// m_bBillAcceptorDisabled = false;
	pParamBillAcceptorEnabled_AFT->SetValue(1, true);

	if (pSASModule->GetAFTLock() == AFT_GAME_LOCKED)
	{
		m_bCancelLock = true;
	}
	pSASModule->_AFTUnlock();
}
