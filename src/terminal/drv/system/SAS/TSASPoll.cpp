#include "drv/system/SAS/TSASPoll.h"

#include "Cryptography.h"
#include "YUtils.h"
#include "drv/system/SAS/TSASChannel.h"
#include "drv/system/SAS/TSASModule.h"

/* --------------------- TSASPoll -------------------------------*/

struct LongPoolStruct locLongPolls[256];

#define ADD_LONG_POLL(paPoll, paType, paPctLen, paDescription) \
	locLongPolls[paPoll].cType = paType;                       \
	locLongPolls[paPoll].nPctLen = paPctLen;                   \
	strcpy(locLongPolls[paPoll].sDescription, paDescription);

/**
 *
 * @return
 */
int sas_PopulateLongPollsDesc()
{
	// reset descriptions
	for (int i = 0; i < 256; i++)
	{
		locLongPolls[i].cType = ' ';
		locLongPolls[i].nPctLen = 0;
		locLongPolls[i].sDescription[0] = '\0';
	}
	// populate polls descriptions
	ADD_LONG_POLL(0x01, 'S', 4, "Shutdown (lock out play)");
	ADD_LONG_POLL(0x02, 'S', 4, "Startup (enable play)");
	ADD_LONG_POLL(0x03, 'S', 4, "Sound off (all sounds disabled)");
	ADD_LONG_POLL(0x04, 'S', 4, "Sound on (all sounds enabled)");
	ADD_LONG_POLL(0x05, 'S', 4, "Reel spin sound disabled");
	ADD_LONG_POLL(0x06, 'S', 4, "Enable bill acceptor");
	ADD_LONG_POLL(0x07, 'S', 4, "Disable bill acceptor");
	ADD_LONG_POLL(0x08, 'S', 9, "Configure bill denominations");
	ADD_LONG_POLL(0x09, 'M', 7, "Enable/disable game N");
	ADD_LONG_POLL(0x0A, 'S', 4, "Enter maintenance mode");
	ADD_LONG_POLL(0x0B, 'S', 4, "Exit maintenance mode");
	ADD_LONG_POLL(0x0E, 'S', 5, "Enable/disable real time event reporting");
	ADD_LONG_POLL(0x0F, 'R', 2, "Send meters 10 through 15");
	ADD_LONG_POLL(0x10, 'R', 2, "Send jurisdictional canceled credit meter");
	ADD_LONG_POLL(0x11, 'R', 2, "Send coin in meter");
	ADD_LONG_POLL(0x12, 'R', 2, "Send coin out meter");
	ADD_LONG_POLL(0x13, 'R', 2, "Send total drop meter");
	ADD_LONG_POLL(0x14, 'R', 2, "Send jackpot meter");
	ADD_LONG_POLL(0x15, 'R', 2, "Send games played meter");
	ADD_LONG_POLL(0x16, 'R', 2, "Send games won meter");
	ADD_LONG_POLL(0x17, 'R', 2, "Send games lost meter");
	ADD_LONG_POLL(0x18, 'R', 2, "Send games since last power up and door closure");
	ADD_LONG_POLL(0x19, 'R', 2, "Send meters 11-15");
	ADD_LONG_POLL(0x1A, 'R', 2, "Send credits");
	ADD_LONG_POLL(0x1B, 'R', 2, "Send hand pay information");
	ADD_LONG_POLL(0x1C, 'R', 2, "Send meters");
	ADD_LONG_POLL(0x1E, 'R', 2, "Send bill meters");
	ADD_LONG_POLL(0x1F, 'R', 2, "Send gaming machine ID & information");
	ADD_LONG_POLL(0x20, 'R', 2, "Send dollar value of bills meter");
	ADD_LONG_POLL(0x21, 'S', 6, "ROM signature verification");
	ADD_LONG_POLL(0x2A, 'R', 2, "Send true coin in");
	ADD_LONG_POLL(0x2B, 'R', 2, "Send true coin out");
	ADD_LONG_POLL(0x2C, 'R', 2, "Send current hopper level");
	ADD_LONG_POLL(0x2D, 'M', 6, "Send hand paid credits");
	ADD_LONG_POLL(0x2E, 'S', 6, "Delay game");
	ADD_LONG_POLL(0x2F, 'M', -1, "Send selected meters command");
	ADD_LONG_POLL(0x31, 'R', 2, "Send 1.00 bills in meter");
	ADD_LONG_POLL(0x32, 'R', 2, "Send 2.00 bills in meter");
	ADD_LONG_POLL(0x33, 'R', 2, "Send 5.00 bills in meter");
	ADD_LONG_POLL(0x34, 'R', 2, "Send 10.00 bills in meter");
	ADD_LONG_POLL(0x35, 'R', 2, "Send 20.00 bills in meter");
	ADD_LONG_POLL(0x36, 'R', 2, "Send 50.00 bills in meter");
	ADD_LONG_POLL(0x37, 'R', 2, "Send 100.00 bills in meter");
	ADD_LONG_POLL(0x38, 'R', 2, "Send 500.00 bills in meter");
	ADD_LONG_POLL(0x39, 'R', 2, "Send 1000.00 bills in meter");
	ADD_LONG_POLL(0x3A, 'R', 2, "Send 200.00 bills in meter");
	ADD_LONG_POLL(0x3B, 'R', 2, "Send 25.00 bills in meter");
	ADD_LONG_POLL(0x3C, 'R', 2, "Send 2000.00 bills in meter");
	ADD_LONG_POLL(0x3D, 'R', 2, "Send cash out ticket information");
	ADD_LONG_POLL(0x3E, 'R', 2, "Send 2500.00 bills in meter");
	ADD_LONG_POLL(0x3F, 'R', 2, "Send 5000.00 bills in meter");
	ADD_LONG_POLL(0x40, 'R', 2, "Send 10000.00 bills in meter");
	ADD_LONG_POLL(0x41, 'R', 2, "Send 20000.00 bills in meter");
	ADD_LONG_POLL(0x42, 'R', 2, "Send 25000.00 bills in meter");
	ADD_LONG_POLL(0x43, 'R', 2, "Send 50000.00 bills in meter");
	ADD_LONG_POLL(0x44, 'R', 2, "Send 100000.00 bills in meter");
	ADD_LONG_POLL(0x45, 'R', 2, "Send 250.00 bills in meter");
	ADD_LONG_POLL(0x46, 'R', 2, "Send credit amount of all bills accepted");
	ADD_LONG_POLL(0x47, 'R', 2, "Send coin amount accepted from an external coin acceptor");
	ADD_LONG_POLL(0x48, 'R', 2, "Send last accepted bill information");
	ADD_LONG_POLL(0x49, 'R', 2, "Send number of bills currently in the stacker");
	ADD_LONG_POLL(0x4A, 'R', 2, "Send total credit amount of all bills currently in the stacker");
	ADD_LONG_POLL(0x4C, 'S', 10, "Set secure Enhanced Validation ID");
	ADD_LONG_POLL(0x4D, 'S', 5, "Send enhanced validation information");
	ADD_LONG_POLL(0x4F, 'R', 11, "Send Current Hopper Status");
	ADD_LONG_POLL(0x50, 'S', 5, "Send validation meters");
	ADD_LONG_POLL(0x51, 'R', 2, "Send total number of games implemented");
	ADD_LONG_POLL(0x52, 'M', 6, "Send Game n meters");
	ADD_LONG_POLL(0x53, 'M', 6, "Send game n configurations");
	ADD_LONG_POLL(0x54, 'R', 2, "Send SAS version ID and gaming machine serial number");
	ADD_LONG_POLL(0x55, 'R', 2, "Send selected game number");
	ADD_LONG_POLL(0x56, 'R', 2, "Send enabled game numbers");
	ADD_LONG_POLL(0x57, 'R', 2, "Send pending cashout information");
	ADD_LONG_POLL(0x58, 'S', 13, "Receive Validation Number Response");
	ADD_LONG_POLL(0x59, 'S', -1, "Send Enabled Currency Codes");    // SAS 6.0.3
	ADD_LONG_POLL(0x5A, 'S', 8, "Send Supported Bills");    // SAS 6.0.3
	ADD_LONG_POLL(0x5B, 'S', 13, "Send Bill Meters");    // SAS 6.0.3
	ADD_LONG_POLL(0x5C, 'S', 6, "Foreign Bill Reporting Mode");    // SAS 6.0.3
	ADD_LONG_POLL(0x5D, 'R', -1, "Send Non-SAS Progressive Win Data");    // SAS 6.0.3
	ADD_LONG_POLL(0x5E, 'R', -1, "Send Configured Progressive Controllers");    // SAS 6.0.3
	ADD_LONG_POLL(0x5F, 'S', 8, "Send Progressive Broadcast Values");    // SAS 6.0.3
	ADD_LONG_POLL(0x6E, 'S', -1, "Send Authentication Info");
	ADD_LONG_POLL(0x6F, 'M', -1, "Send Extended Meters for Game N");
	ADD_LONG_POLL(0x70, 'R', 2, "Send Ticket Validation Data Response");
	ADD_LONG_POLL(0x71, 'S', -1, "Redeem ticket");
	ADD_LONG_POLL(0x72, 'S', -1, "AFT transfer funds");
	ADD_LONG_POLL(0x73, 'S', -1, "AFT register gaming machine");
	ADD_LONG_POLL(0x74, 'S', 8, "AFT game lock and status request");
	ADD_LONG_POLL(0x75, 'S', -1, "Set AFT receipt data");
	ADD_LONG_POLL(0x76, 'S', -1, "Set custom AFT ticket data");
	ADD_LONG_POLL(0x77, 'S', 9, "Send Progressive Accounting Data");    // SAS 6.0.3
	ADD_LONG_POLL(0x7A, 'S', -1, "Extended Progressive Broadcast");    // SAS 6.0.3
	ADD_LONG_POLL(0x7B, 'S', -1, "Extended validation status");
	ADD_LONG_POLL(0x7C, 'S', -1, "Set extended ticket data");
	ADD_LONG_POLL(0x7D, 'S', -1, "Set ticket data");
	ADD_LONG_POLL(0x7E, 'R', 2, "Send current Date and Time");
	ADD_LONG_POLL(0x7F, 'S', 11, "Set Date and Time");
	ADD_LONG_POLL(0x80, 'S', 11, "Single Level Progressive Broadcast");
	ADD_LONG_POLL(0x83, 'M', 6, "Send Cumulative Progressive Win");
	ADD_LONG_POLL(0x84, 'R', 11, "Send Progressive Win Amount");
	ADD_LONG_POLL(0x85, 'R', 11, "Send SAS Progressive Win Amount");
	ADD_LONG_POLL(0x86, 'S', -1, "Multiple Progressive Levels Broadcast");
	ADD_LONG_POLL(0x87, 'R', -1, "Send Multiple SAS Progressive Win Amounts");
	ADD_LONG_POLL(0x8A, 'S', 5, "Initiate a legacy bonus pay");
	ADD_LONG_POLL(0x8B, 'S', 15, "Initiate multiplied jackpot mode");
	ADD_LONG_POLL(0x8C, 'M', 13, "Enter/exit Tournament mode");
	ADD_LONG_POLL(0x8E, 'R', -1, "Send Card Info");
	ADD_LONG_POLL(0x8F, 'R', 2, "Send physical reel stop");
	ADD_LONG_POLL(0x90, 'R', 14, "Send legacy bonus win amount");
	ADD_LONG_POLL(0x94, 'S', 4, "Remote Handpay Reset");
	ADD_LONG_POLL(0x95, 'M', -1, "Send Tournament Games Played");
	ADD_LONG_POLL(0x96, 'M', -1, "Send Tournament Games Won");
	ADD_LONG_POLL(0x97, 'M', -1, "Send Tournament Credits Wagered");
	ADD_LONG_POLL(0x98, 'M', -1, "Send Tournament Credits Won");
	ADD_LONG_POLL(0x99, 'M', -1, "Send meters 95-98");
	ADD_LONG_POLL(0x9A, 'M', 6, "Send Legacy Bonus Meters");
	ADD_LONG_POLL(0xA0, 'M', 6, "Send enabled features");
	ADD_LONG_POLL(0xA4, 'M', 6, "Send Cash out Limit");
	ADD_LONG_POLL(0xA8, 'S', 5, "Enable jackpot Handpay Reset");
	ADD_LONG_POLL(0xAA, 'S', 5, "Enable/Disable game auto rebet");
	ADD_LONG_POLL(0xAF, 'M', -1, "Send Extended Meters for Game N");
	ADD_LONG_POLL(0xB0, 'S', -1, "Multi-denomi preamble");
	ADD_LONG_POLL(0xB1, 'R', 2, "Send current player denomination");
	ADD_LONG_POLL(0xB2, 'R', 2, "Send enabled player denominations");
	ADD_LONG_POLL(0xB3, 'R', 2, "Send token denomination");
	ADD_LONG_POLL(0xB4, 'M', 8, "Send wager category info");
	ADD_LONG_POLL(0xB5, 'M', 6, "Send extended game N info");
	ADD_LONG_POLL(0xB6, 'S', 6, "Meter Collect Status");    // SAS 6.0.3
	ADD_LONG_POLL(0xB7, 'S', -1, "Set Machine Numbers");    // SAS 6.0.3
	ADD_LONG_POLL(0xC0, 'S', 5, "Enable/Disable Asynchronous Game Play");    // SAS 6.0.3
	ADD_LONG_POLL(0xC1, 'S', 4, "Send Gaming Machine Status");    // SAS 6.0.3
	ADD_LONG_POLL(0xC2, 'S', 4, "Send Games in Play");    // SAS 6.0.3

	return true;
}
/* ----------------------- Meter names table ---------------------*/
struct MeterIDsStruct stMeterIDs[] = {
	{ 0x00, &METER_TOTAL_IN, 4 },
	{ 0x01, &METER_TOTAL_COIN_OUT, 4 },
	{ 0x02, &METER_JACKPOT, 4 },
	{ 0x03, &METER_TOTAL_HAND_PAY_CANCELED_CREDIT, 4 },
	{ 0x04, &METER_TOTAL_CREDIT_CANCEL, 4 },
	{ 0x05, &METER_GAMES_PLAYED, 4 },
	{ 0x06, &METER_GAMES_WON, 4 },
	{ 0x07, &METER_GAMES_LOST, 4 },
	{ 0x08, &METER_COIN_IN, 4 },
	{ 0x09, &METER_COIN_OUT, 4 },
	{ 0x0A, &METER_COINS_TO_CASHBOX, 4 },
	{ 0x0B, &METER_BILL_DROP, 4 },
	{ 0x0C, &METER_CREDIT, 4 },
#ifdef SAS_TICKETING
	// meters from 0X0D through 0X14 are maintained here for backwards compatibility only
	{ 0X0D, &METER_CASHABLE_TICKET_IN_CENTS, 5 },
	{ 0X0E, &METER_CASHABLE_TICKET_OUT_CENTS, 5 },
	{ 0X0F, &METER_RESTRICTED_TICKET_IN_CENTS, 5 },
	{ 0X10, &METER_RESTRICTED_TICKET_OUT_CENTS, 5 },
	{ 0X11, &METER_CASHABLE_TICKET_IN_QTY, 4 },
	{ 0X12, &METER_CASHABLE_TICKET_OUT_QTY, 4 },
	{ 0X13, &METER_RESTRICTED_TICKET_IN_QTY, 4 },
	{ 0X14, &METER_RESTRICTED_TICKET_OUT_QTY, 4 },
	{ 0X15, &METER_TICKET_IN_CREDITS, 4 },
	{ 0X16, &METER_TICKET_OUT_CREDITS, 4 },
#endif
	{ 0x17, &METER_TOTAL_ELECTONIC_TRANSFERS_TO_GM, 4 },
	{ 0x18, &METER_TOTAL_ELECTONIC_TRANSFERS_TO_HOST, 4 },
	{ 0x19, &METER_RESTRICTED_CREDITS_PLAYED, 4 },
	{ 0x1A, &METER_NONRESTRICTED_CREDITS_PLAYED, 4 },
	{ 0x1B, &METER_RESTRICTED_CREDIT, 4 },
	{ 0X1C, &METER_TOTAL_MACHINE_PAID_PAYTABLE_WIN, 4 },
	{ 0X1D, &METER_TOTAL_MACHINE_PAID_PROGRESSIVE_WIN, 4 },
	{ 0X1E, &METER_TOTAL_MACHINE_PAID_EXTERNAL_BONUS_WIN, 4 },
	{ 0X1F, &METER_TOTAL_ATTENDANT_PAID_PAYTABLE_WIN, 4 },
	{ 0X20, &METER_TOTAL_ATTENDANT_PAID_PROGRESSIVE_WIN, 4 },
	{ 0X21, &METER_TOTAL_ATTENDANT_PAID_EXTERNAL_BONUS_WIN, 4 },
	{ 0x22, &METER_TOTAL_WON_CREDITS, 4 },
	{ 0x23, &METER_TOTAL_HAND_PAY_CANCELED_CREDIT, 4 },
	{ 0x24, &METER_TOTAL_DROP, 4 },
	{ 0x25, &METER_GAMES_SINCE_LAST_POWER_UP, 4 },
	{ 0x26, &METER_GAMES_SINCE_LAST_SLOT_DOOR_CLOSURE, 4 },
	{ 0x27, &METER_TOTAL_CREDITS_FROM_EXTERNAL_COIN_ACCEPTOR, 4 },
#ifdef SAS_TICKETING
	{ 0X28, &METER_CASHABLE_AND_NONRESTRICTED_TICKET_IN_CREDITS, 4 },
	{ 0X29, &METER_CASHABLE_TICKET_IN_CREDITS, 4 },
	{ 0X2A, &METER_RESTRICTED_TICKET_IN_CREDITS, 4 },
	{ 0X2B, &METER_NONRESTRICTED_TICKET_IN_CREDITS, 4 },
	{ 0X2C, &METER_CASHABLE_TICKET_OUT_CREDITS, 4 },
	{ 0X2D, &METER_RESTRICTED_TICKET_OUT_CREDITS, 4 },
	{ 0X35, &METER_CASHABLE_TICKET_IN_QTY, 4 },
	{ 0X36, &METER_RESTRICTED_TICKET_IN_QTY, 4 },
	{ 0X37, &METER_NONRESTRICTED_TICKET_IN_QTY, 4 },
	{ 0X38, &METER_CASHABLE_TICKET_OUT_QTY, 4 },
	{ 0X39, &METER_RESTRICTED_TICKET_OUT_QTY, 4 },
#endif
	{ 0x2E, &METER_ELECTRONIC_CASHABLE_TO_GM, 4 },
	{ 0x2F, &METER_ELECTRONIC_RESTRICTED_TO_GM, 4 },
	{ 0x30, &METER_ELECTRONIC_NONRESTRICTED_TO_GM, 4 },
	{ 0x31, &METER_ELECTRONIC_DEBIT_TO_GM, 4 },
	{ 0x32, &METER_ELECTRONIC_CASHABLE_TO_HOST, 4 },
	{ 0x33, &METER_ELECTRONIC_RESTRICTED_TO_HOST, 4 },
	{ 0x34, &METER_ELECTRONIC_NONRESTRICTED_TO_HOST, 4 },
	{ 0x3E, &METER_BILLS_IN_STACKER, 4 },
	{ 0x3F, &METER_BILLS_IN_STACKER_CREDIT, 4 },
	{ 0x40, &METER_TOTAL_BILLS_1, 4 },
	{ 0x41, &METER_TOTAL_BILLS_2, 4 },
	{ 0x42, &METER_TOTAL_BILLS_5, 4 },
	{ 0x43, &METER_TOTAL_BILLS_10, 4 },
	{ 0x44, &METER_TOTAL_BILLS_20, 4 },
	{ 0x45, &METER_TOTAL_BILLS_25, 4 },
	{ 0x46, &METER_TOTAL_BILLS_50, 4 },
	{ 0x47, &METER_TOTAL_BILLS_100, 4 },
	{ 0x48, &METER_TOTAL_BILLS_200, 4 },
	{ 0x49, &METER_TOTAL_BILLS_250, 4 },
	{ 0x4A, &METER_TOTAL_BILLS_500, 4 },
	{ 0x4B, &METER_TOTAL_BILLS_1000, 4 },
	{ 0x4C, &METER_TOTAL_BILLS_2000, 4 },
	{ 0x4D, &METER_TOTAL_BILLS_2500, 4 },
	{ 0x4E, &METER_TOTAL_BILLS_5000, 4 },
	{ 0x4F, &METER_TOTAL_BILLS_10000, 4 },
	{ 0x50, &METER_TOTAL_BILLS_20000, 4 },
	{ 0x51, &METER_TOTAL_BILLS_25000, 4 },
	{ 0x52, &METER_TOTAL_BILLS_50000, 4 },
	{ 0x53, &METER_TOTAL_BILLS_100000, 4 },
	{ 0x54, &METER_TOTAL_BILLS_200000, 4 },
	{ 0x55, &METER_TOTAL_BILLS_250000, 4 },
	{ 0x56, &METER_TOTAL_BILLS_500000, 4 },
	{ 0x57, &METER_TOTAL_BILLS_1000000, 4 },
	{ 0x58, &METER_CREDITS_TO_DROP, 4 },
	{ 0x59, &METER_TOTAL_BILLS_1_TO_DROP, 4 },
	{ 0x5A, &METER_TOTAL_BILLS_2_TO_DROP, 4 },
	{ 0x5B, &METER_TOTAL_BILLS_5_TO_DROP, 4 },
	{ 0x5C, &METER_TOTAL_BILLS_10_TO_DROP, 4 },
	{ 0x5D, &METER_TOTAL_BILLS_20_TO_DROP, 4 },
	{ 0x5E, &METER_TOTAL_BILLS_50_TO_DROP, 4 },
	{ 0x5F, &METER_TOTAL_BILLS_100_TO_DROP, 4 },
	{ 0x60, &METER_TOTAL_BILLS_200_TO_DROP, 4 },
	{ 0x61, &METER_TOTAL_BILLS_500_TO_DROP, 4 },
	{ 0x62, &METER_TOTAL_BILLS_1000_TO_DROP, 4 },
	{ 0x63, &METER_CREDITS_TO_HOPPER, 4 },
	{ 0x64, &METER_TOTAL_BILLS_1_TO_HOPPER, 4 },
	{ 0x65, &METER_TOTAL_BILLS_2_TO_HOPPER, 4 },
	{ 0x66, &METER_TOTAL_BILLS_5_TO_HOPPER, 4 },
	{ 0x67, &METER_TOTAL_BILLS_10_TO_HOPPER, 4 },
	{ 0x68, &METER_TOTAL_BILLS_20_TO_HOPPER, 4 },
	{ 0x69, &METER_TOTAL_BILLS_50_TO_HOPPER, 4 },
	{ 0x6A, &METER_TOTAL_BILLS_100_TO_HOPPER, 4 },
	{ 0x6B, &METER_TOTAL_BILLS_200_TO_HOPPER, 4 },
	{ 0x6C, &METER_TOTAL_BILLS_500_TO_HOPPER, 4 },
	{ 0x6D, &METER_TOTAL_BILLS_1000_TO_HOPPER, 4 },
	{ 0x6E, &METER_CREDITS_FROM_HOPPER, 4 },
	{ 0x6F, &METER_TOTAL_BILLS_1_FROM_HOPPER, 4 },
	{ 0x70, &METER_TOTAL_BILLS_2_FROM_HOPPER, 4 },
	{ 0x71, &METER_TOTAL_BILLS_5_FROM_HOPPER, 4 },
	{ 0x72, &METER_TOTAL_BILLS_10_FROM_HOPPER, 4 },
	{ 0x73, &METER_TOTAL_BILLS_20_FROM_HOPPER, 4 },
	{ 0x74, &METER_TOTAL_BILLS_50_FROM_HOPPER, 4 },
	{ 0x75, &METER_TOTAL_BILLS_100_FROM_HOPPER, 4 },
	{ 0x76, &METER_TOTAL_BILLS_200_FROM_HOPPER, 4 },
	{ 0x77, &METER_TOTAL_BILLS_500_FROM_HOPPER, 4 },
	{ 0x78, &METER_TOTAL_BILLS_1000_FROM_HOPPER, 4 },

	{ 0x79, &METER_SESSIONS_PLAYED, 4 },

	{ 0x7A, &METER_TIP_MONEY_CREDITS, 4 },
	{ 0x7B, &METER_TOTAL_FOREIGN_BILL_CONVERTED_CENTS, 5 },
	{ 0x7C, &METER_TOTAL_FOREIGN_BILL_CONVERTED_QTY, 4 },

	{ 0x7F, &METER_WEIGHTED_AVERAGE_THEOR_PAYBACK_PERCENTAGE, 4 },

#ifdef SAS_TICKETING
	{ 0X80, &METER_CASHABLE_TICKET_IN_CENTS, 5 },
	{ 0X81, &METER_CASHABLE_TICKET_IN_QTY, 4 },
	{ 0X82, &METER_RESTRICTED_TICKET_IN_CENTS, 5 },
	{ 0X83, &METER_RESTRICTED_TICKET_IN_QTY, 4 },
	{ 0x84, &METER_NONRESTRICTED_TICKET_IN_CENTS, 5 },
	{ 0x85, &METER_NONRESTRICTED_TICKET_IN_QTY, 4 },
	{ 0X86, &METER_CASHABLE_TICKET_OUT_CENTS, 5 },
	{ 0X87, &METER_CASHABLE_TICKET_OUT_QTY, 4 },
	{ 0X88, &METER_RESTRICTED_TICKET_OUT_CENTS, 5 },
	{ 0X89, &METER_RESTRICTED_TICKET_OUT_QTY, 4 },

	{ 0X8A, &METER_DEBIT_TICKET_OUT_CENTS, 5 },
	{ 0X8B, &METER_DEBIT_TICKET_OUT_QTY, 4 },

	{ 0x8C, &METER_HANDPAY_RECEIPT_CENTS, 5 },
	{ 0x8D, &METER_HANDPAY_RECEIPT_QTY, 4 },
	{ 0x8E, &METER_HANDPAY_FROM_WIN_RECEIPT, 5 },
	{ 0x8F, &METER_HANDPAY_FROM_WIN_RECEIPT_QTY, 4 },
	{ 0x90, &METER_HANDPAY_FROM_CASHOUT_NORECEIPT, 5 },
	{ 0x91, &METER_HANDPAY_FROM_CASHOUT_NORECEIPT_QTY, 4 },
	{ 0x92, &METER_HANDPAY_FROM_WIN_NORECEIPT, 5 },
	{ 0x93, &METER_HANDPAY_FROM_WIN_NORECEIPT_QTY, 4 },
#endif
	{ 0xA0, &METER_IN_HOUSE_CASHABLE_TO_GM_CENTS, 5 },
	{ 0xA1, &METER_IN_HOUSE_CASHABLE_TO_GM_QTY, 4 },
	{ 0xA2, &METER_IN_HOUSE_RESTRICTED_TO_GM_CENTS, 5 },
	{ 0xA3, &METER_IN_HOUSE_RESTRICTED_TO_GM_QTY, 4 },
	{ 0xA4, &METER_IN_HOUSE_NONRESTRICTED_TO_GM_CENTS, 5 },
	{ 0xA5, &METER_IN_HOUSE_NONRESTRICTED_TO_GM_QTY, 4 },

	{ 0xA6, &METER_DEBIT_TO_GM_CENTS, 5 },
	{ 0xA7, &METER_DEBIT_TO_GM_QTY, 4 },
	{ 0xA8, &METER_IN_HOUSE_CASHABLE_TO_TICKET_CENTS, 5 },
	{ 0xA9, &METER_IN_HOUSE_CASHABLE_TO_TICKET_QTY, 4 },
	{ 0xAA, &METER_IN_HOUSE_RESTRICTED_TO_TICKET_CENTS, 5 },
	{ 0xAB, &METER_IN_HOUSE_RESTRICTED_TO_TICKET_QTY, 4 },
	{ 0xAC, &METER_DEBIT_TO_TICKET_CENTS, 5 },
	{ 0xAD, &METER_DEBIT_TO_TICKET_QTY, 4 },

	{ 0xAE, &METER_BONUS_CASHABLE_TO_GM_CENTS, 5 },
	{ 0xAF, &METER_BONUS_CASHABLE_TO_GM_QTY, 4 },
	{ 0xB0, &METER_BONUS_NONRESTRICTED_TO_GM_CENTS, 5 },
	{ 0xB1, &METER_BONUS_NONRESTRICTED_TO_GM_QTY, 4 },

	{ 0xB8, &METER_IN_HOUSE_CASHABLE_TO_HOST_CENTS, 5 },
	{ 0xB9, &METER_IN_HOUSE_CASHABLE_TO_HOST_QTY, 4 },
	{ 0xBA, &METER_IN_HOUSE_RESTRICTED_TO_HOST_CENTS, 5 },
	{ 0xBB, &METER_IN_HOUSE_RESTRICTED_TO_HOST_QTY, 4 },
	{ 0xBC, &METER_IN_HOUSE_NONRESTRICTED_TO_HOST_CENTS, 5 },
	{ 0xBD, &METER_IN_HOUSE_NONRESTRICTED_TO_HOST_QTY, 4 },

	{ 0xC0, &METER_BETS_RETURNED, 4 },
	{ 0xC1, &METER_TIP_MONEY_FROM_HAND_PAID_CANCELED_CREDITS, 5 },
	{ 0xC2, &METER_TIP_MONEY_FROM_REGULAR_CASHABLE_TICKET_OUT, 4 },
	{ 0xC3, &METER_TIP_MONEY_FROM_KEY_OFF_FUNDS, 4 },

	{ 0XFA, &METER_ELECTRONIC_IN_CASHABLE, 4 },
	{ 0XFB, &METER_ELECTRONIC_IN_RESTRICTED, 4 },
	{ 0XFC, &METER_ELECTRONIC_IN_NONRESTRICTED, 4 },
	{ 0XFD, &METER_ELECTRONIC_OUT_CASHABLE, 4 },
	{ 0XFE, &METER_ELECTRONIC_OUT_RESTRICTED, 4 },
	{ 0XFF, &METER_ELECTRONIC_OUT_NONRESTRICTED, 4 },
};


/* Returns true, meter id and len for meter value if meter name for passed code exist */
/* Otherwise returns false */
int GetMeterID(unsigned char cCode, unsigned short* nMeterID, unsigned char* cLen)
{
	int nLen, i;

	nLen = sizeof(stMeterIDs) / sizeof(stMeterIDs[0]);
	for (i = 0; i < nLen; i++)
	{
		// if code found
		if (stMeterIDs[i].cCode == cCode)
		{
			// strcpy(sMeterName, stMeterNames[i].sMeterName);
			*nMeterID = *(stMeterIDs[i].pnMeterID);
			*cLen = stMeterIDs[i].cLen;
			return true;
		}
	}
	return false;
}

/* Map Bill value to bill meter ID */
static int GetBillMeterID(int nBillValue, unsigned short* nBillMeterID)
{
	switch (nBillValue)
	{
		case 1: *nBillMeterID = METER_TOTAL_BILLS_1; break;
		case 2: *nBillMeterID = METER_TOTAL_BILLS_2; break;
		case 5: *nBillMeterID = METER_TOTAL_BILLS_5; break;
		case 10: *nBillMeterID = METER_TOTAL_BILLS_10; break;
		case 20: *nBillMeterID = METER_TOTAL_BILLS_20; break;
		case 25: *nBillMeterID = METER_TOTAL_BILLS_25; break;
		case 50: *nBillMeterID = METER_TOTAL_BILLS_50; break;
		case 100: *nBillMeterID = METER_TOTAL_BILLS_100; break;
		case 200: *nBillMeterID = METER_TOTAL_BILLS_200; break;
		case 250: *nBillMeterID = METER_TOTAL_BILLS_250; break;
		case 500: *nBillMeterID = METER_TOTAL_BILLS_500; break;
		case 1000: *nBillMeterID = METER_TOTAL_BILLS_1000; break;
		case 2000: *nBillMeterID = METER_TOTAL_BILLS_2000; break;
		case 2500: *nBillMeterID = METER_TOTAL_BILLS_2500; break;
		case 5000: *nBillMeterID = METER_TOTAL_BILLS_5000; break;
		case 10000: *nBillMeterID = METER_TOTAL_BILLS_10000; break;
		case 20000: *nBillMeterID = METER_TOTAL_BILLS_20000; break;
		case 25000: *nBillMeterID = METER_TOTAL_BILLS_25000; break;
		case 50000: *nBillMeterID = METER_TOTAL_BILLS_50000; break;
		case 100000: *nBillMeterID = METER_TOTAL_BILLS_100000; break;
		case 200000: *nBillMeterID = METER_TOTAL_BILLS_200000; break;
		case 250000: *nBillMeterID = METER_TOTAL_BILLS_250000; break;
		case 500000: *nBillMeterID = METER_TOTAL_BILLS_500000; break;
		case 1000000: *nBillMeterID = METER_TOTAL_BILLS_1000000; break;
		default: *nBillMeterID = 0; return false;
	}
	return true;
}


/* maps cValType to meter name for quantity and amount in cents according to table 15.13c */
static void GetValidationMetersPair(unsigned char cValType, int& nQty, int& nCents)
{
	switch (cValType)
	{
		// cashable ticket from cashout
		case 0x00:
			nQty = METER_CASHABLE_TICKET_OUT_QTY;
			nCents = METER_CASHABLE_TICKET_OUT_CENTS;
			break;
		// restricted promotional ticket from cashout
		case 0x01:
			nQty = METER_RESTRICTED_TICKET_OUT_QTY;
			nCents = METER_RESTRICTED_TICKET_OUT_CENTS;
			break;
		// handpay from cashout (receipt printed)
		case 0x10:
			nQty = METER_HANDPAY_RECEIPT_QTY;
			nCents = METER_HANDPAY_RECEIPT_CENTS;
			break;
		// handpay from cashout (no receipt)
		case 0x40:
			nQty = 0;    //"handpay no receipt qty";
			nCents = 0;    //"handpay no receipt cents";
			break;
		// cashable ticket redeemed (in)
		case 0x80:
			nQty = METER_CASHABLE_TICKET_IN_QTY;
			nCents = METER_CASHABLE_TICKET_IN_CENTS;
			break;
		// restricted promotional ticket redemeed
		case 0x81:
			nQty = METER_RESTRICTED_TICKET_IN_QTY;
			nCents = METER_RESTRICTED_TICKET_IN_CENTS;
			break;
		// nonrestricted promotional ticket redemeed
		case 0x82:
			nQty = 0;    //"nonrestricted ticket in qty";
			nCents = 0;    //"nonrestricted ticket in cents";
			break;
		default:
			nQty = 0;    // unknown counter
			nCents = 0;    // unknown counter
			break;
	}
}


/////////// comparision ////////////
int TSASPoll::operator==(TSASPoll& pPct)
{
	if (m_Len != pPct.m_Len)
		return false;

	for (int i = 0; i < m_Len; i++)
		if (m_Buf[i] != pPct.m_Buf[i])
			return false;

	return true;
}

/*void TSASPoll::operator = ( TSASPoll& pPct )
{
  m_Len = pPct.m_Len ;
  memcpy(m_Buf, pPct.m_Buf, SAS_MAX_PCT_LEN);
  m_nParityPos = pPct.m_nParityPos;
}
*/

/////////// Append ////////////
/* Appends a character, nCount times */
int TSASPoll::AppendChar(unsigned char c, int nCount)
{
	int nResult = true;

	while (nCount > 0)
	{
		if (m_Len < SAS_MAX_PCT_LEN)
		{
			m_Buf[m_Len++] = c;
		}
		else
			nResult = false;
		nCount--;
	}
	return nResult;
}

/* Append short LSB */
/* ALL binary data is LSB */
int TSASPoll::AppendShort(unsigned short s)
{
	if (m_Len + 2 < SAS_MAX_PCT_LEN)
	{
		m_Buf[m_Len++] = s % 256;
		m_Buf[m_Len++] = s / 256;
		return true;
	}
	return false;
}


/* Appends sCounterName's value as nBCDLen bytes MSB BCD */
int TSASPoll::AppendCounter(unsigned short nMeterID, int nBCDLen, int gameN)
{
	uint64_t nCounterValue = 0;
	// UNUSED int               nMultiplier = 1;
	int gameID = atoi(pSASModule->GetGameSASIdentifier(gameN).c_str());    // from SAS game number to our game ID

	if (m_Len + nBCDLen < SAS_MAX_PCT_LEN)
	{
		nCounterValue = 0;
		//    SASI_GetMeter(sMeter, &nCounterValue);
		pSASModule->MeterGet(nMeterID, &nCounterValue, gameID);
		// append value
		if (AppendBCD(nCounterValue, nBCDLen))
			return true;
	}
	return false;
}


/* Appends integer value as MSB BCD, nBCDLen is number of appended bytes */
/* Missing leading 0 are appended                                                */
int TSASPoll::AppendBCD(uint64_t nValue, int nBCDLen)
{
	if (nBCDLen <= 0)
		return true;

	if (m_Len + nBCDLen >= SAS_MAX_PCT_LEN)
		return false;

	std::vector<unsigned char> buf;
	ToBCD(nValue, nBCDLen, buf);

	return AppendBuf(buf.data(), nBCDLen);
}

int TSASPoll::AppendBCD(const TString& nValue, int nBCDLen)
{
	std::vector<unsigned char> buf;
	nValue.ToBCD(buf, nBCDLen);

	if ((int)buf.size() != nBCDLen)
	{
		TLOG(LogSAS, Error, "AppendBCD different length than requested: %d != %d", (int)buf.size(), nBCDLen);
		return false;
	}

	return AppendBuf(buf.data(), nBCDLen);
}

int TSASPoll::AppendStr(const std::string& str, size_t nLen)
{
	if (m_Len + nLen >= SAS_MAX_PCT_LEN)
		return false;

	for (size_t i = 0; i < nLen; i++)
	{
		if (i < str.length())
		{
			char c = str[i];
			AppendChar(c);
		}
		else
			AppendChar(0);
	}

	return true;
}

/* Appends string as binary to packet */
/* if not sucsesfull return false     */
int TSASPoll::AppendStrAsBin(const TString& sStr, size_t nLen)
{
	if (m_Len + nLen >= SAS_MAX_PCT_LEN)
		return false;

	unsigned char ch;
	for (size_t i = 0; i < nLen; i = i + 2)
	{
		ch = 0;
		sStr.GetHexByte(i, ch);
		AppendChar(ch);
	}

	return true;
}


/* Get TString from packet - more than 4bytes binary data */
int TSASPoll::GetBinAsStr(int nStart, int nLen, TString& sResult)
{
	sResult.clear();
	if (m_Len < nStart + nLen)
		return false;

	sResult = crypto::BytesToHex(m_Buf + nStart, nLen, true);
	return true;
}

/* Appends buffer to packet       */
/* if not sucsesfull return false */
int TSASPoll::AppendBuf(const unsigned char* pBuf, int nLen)
{
	if (m_Len + nLen >= SAS_MAX_PCT_LEN)
		return false;

	for (int i = 0; i < nLen; i++) AppendChar(*(pBuf + i));
	return true;
}

/* Append unsigned int binary LSB */
int TSASPoll::AppendInt(unsigned int n, int nLen)
{
	int i;

	if ((m_Len + nLen < SAS_MAX_PCT_LEN) && (nLen < 5))
	{
		for (i = 0; i < nLen; i++)
		{
			m_Buf[m_Len++] = n % 256;
			n = n / 256;
		}
		return true;
	}
	return false;
}

/* Converts bill value to SAS Bill Denomonation code */
static int GetBillDenominationCode(int nBillValue)
{
	int nBillCode;

	switch (nBillValue)
	{
		case 1: nBillCode = 0; break;
		case 2: nBillCode = 1; break;
		case 5: nBillCode = 2; break;
		case 10: nBillCode = 3; break;
		case 20: nBillCode = 4; break;
		case 25: nBillCode = 5; break;
		case 50: nBillCode = 6; break;
		case 100: nBillCode = 7; break;
		case 200: nBillCode = 8; break;
		case 250: nBillCode = 9; break;
		case 500: nBillCode = 10; break;
		case 1000: nBillCode = 11; break;
		case 2000: nBillCode = 12; break;
		case 2500: nBillCode = 13; break;
		case 5000: nBillCode = 14; break;
		case 10000: nBillCode = 15; break;
		case 20000: nBillCode = 16; break;
		case 25000: nBillCode = 17; break;
		case 50000: nBillCode = 18; break;
		case 100000: nBillCode = 19; break;
		case 200000: nBillCode = 20; break;
		case 250000: nBillCode = 21; break;
		case 500000: nBillCode = 22; break;
		case 1000000: nBillCode = 23; break;
		default: nBillCode = 0; break;
	}
	return nBillCode;
}


/* Clears the packet and builds a R long poll response packet */
/* If the poll is not supporeted an empty packet is returned */
int TSASPoll::BuildRLongPollResponse(TSASPoll* pInputPct, void* pSas)
{
	int nCommand;
	unsigned char cMachineAddr;
	TSASChannel* pSASChannel = NULL;

	pSASChannel = (TSASChannel*)pSas;
	cMachineAddr = ((TSASChannel*)pSas)->SASChannelConf.cSASAddr;

	Clear();

	if ((nCommand = pInputPct->GetByte(1)) == -1)
		return false;

	AppendChar((unsigned char)cMachineAddr);
	AppendChar((unsigned char)nCommand);

	switch (nCommand)
	{
		// ---------------- "Send meters 10 through 15" ----------------
		case 0x0F:
			AppendCounter(METER_TOTAL_CREDIT_CANCEL, 4);
			AppendCounter(METER_TOTAL_IN, 4);
			AppendCounter(METER_TOTAL_COIN_OUT, 4);
			AppendCounter(METER_TOTAL_DROP, 4);
			AppendCounter(METER_JACKPOT, 4);
			AppendCounter(METER_GAMES_PLAYED, 4);
			break;
		// ---------------- "Send jurisdictional canceled credit meter" ----------------
		case 0x10: AppendCounter(METER_TOTAL_CREDIT_CANCEL, 4); break;
		// ---------------- "Coin in", which means total in ----------------
		case 0x11: AppendCounter(METER_TOTAL_IN, 4); break;
		// ----------------  "Coin out", which means total out ----------------
		case 0x12: AppendCounter(METER_TOTAL_COIN_OUT, 4); break;
		// ---------------- "Total drop" ----------------
		case 0x13: AppendCounter(METER_TOTAL_DROP, 4); break;
		// ---------------- "Send jackpot meter" ----------------
		case 0x14: AppendCounter(METER_JACKPOT, 4); break;
		// ---------------- "Send games played" ----------------
		case 0x15: AppendCounter(METER_GAMES_PLAYED, 4); break;
		// ---------------- "Send games won" ----------------
		case 0x16: AppendCounter(METER_GAMES_WON, 4); break;
		// ---------------- "Send games lost" ----------------
		case 0x17: AppendCounter(METER_GAMES_LOST, 4); break;
		// ---------------- "Send games since last power up and door closure" ----------------
		case 0x18:
			AppendCounter(METER_GAMES_SINCE_LAST_POWER_UP, 2);
			AppendCounter(METER_GAMES_SINCE_LAST_SLOT_DOOR_CLOSURE, 2);
			break;
		// ---------------- "Send meters 11 through 15" ----------------
		case 0x19:
			AppendCounter(METER_TOTAL_IN, 4);
			AppendCounter(METER_TOTAL_COIN_OUT, 4);
			AppendCounter(METER_TOTAL_DROP, 4);
			AppendCounter(METER_JACKPOT, 4);
			AppendCounter(METER_GAMES_PLAYED, 4);
			break;
		// ---------------- "Send credits" ----------------
		case 0x1A: AppendCounter(METER_CREDIT, 4); break;
		// ---------------- "Send hand pay information" ----------------
		case 0x1B: {
			const auto pendingHandpay = pSASModule->GetHandpayPending();
			if (!pendingHandpay)
			{
				Build(SAS_PCT_NACK, pSASChannel);
				return true;
			}

			// progressive group
			AppendChar((unsigned char)0);

			// Level
			switch (*pendingHandpay)
			{
				case EHandpayTriggerSource::User: AppendChar(0x80); break;
				default: AppendChar(0x00); break;
			};

			// amount of hand pay in base account unit
			// SASI_GetHandPayCredits(&nAmount);
			AppendBCD(pSASModule->HandpayCreditsGet(), 5);
			// partial pay
			AppendBCD(0, 2);
			// reset id
			AppendBCD(0, 1);

			if (pSASModule->SASType == magic::SAS_6_0_3)
			{
				// session game win amount
				AppendBCD(0, 5);
				// session game win amount
				AppendBCD(0, 5);
			}
			else
			{
				// 10 unused bytes
				AppendChar((unsigned char)0, 10);
			}
			break;
		}
		// ---------------- "Send meters" ----------------
		case 0x1C:
			AppendCounter(METER_TOTAL_IN, 4);
			AppendCounter(METER_TOTAL_COIN_OUT, 4);
			AppendCounter(METER_TOTAL_DROP, 4);
			AppendCounter(METER_JACKPOT, 4);
			AppendCounter(METER_GAMES_PLAYED, 4);
			AppendCounter(METER_GAMES_WON, 4);
			AppendCounter(METER_EXTERNAL_AND_DEPOSIT_DOOR_OPEN, 4);
			AppendCounter(METER_POWER_RESET, 4);
			break;
		case 0x1D: break;
		// ---------------- "Send bill meters" ----------------
		case 0x1E:
			AppendCounter(METER_TOTAL_BILLS_1, 4);
			AppendCounter(METER_TOTAL_BILLS_5, 4);
			AppendCounter(METER_TOTAL_BILLS_10, 4);
			AppendCounter(METER_TOTAL_BILLS_20, 4);
			AppendCounter(METER_TOTAL_BILLS_50, 4);
			AppendCounter(METER_TOTAL_BILLS_100, 4);
			break;
		// ---------------- "Send Gaming Machine ID and Information" ----------------
		case 0x1F: {
			unsigned char cGameID[] = "IM";    // IMAXA
			unsigned char cAdditionalID[] = "AXA";
			unsigned char cDenomCode = 0, cMaxBet = 0;
			unsigned char sPaybackPercentage[32];
			unsigned int uMaxBet = 0;
			unsigned int nPaybackPecentage = 0;

			// game ID in ASCII
			AppendBuf(cGameID, 2);
			AppendBuf(cAdditionalID, 3);
			// denomination code
			//        SASI_GetDenominationCodeForBaseAccount(&cDenomCode);
			cDenomCode = pSASModule->BaseDenomiCodeGet();
			AppendChar(cDenomCode);
			// max bet (in units of game credits independent of SAS denomination)
			//        SASI_GetMaxBetOnStraightup(&uMaxBet);
			uMaxBet = pSASModule->GetMaxBet();
			cMaxBet = (uMaxBet && uMaxBet < 0xFF) ? uMaxBet : 0xFF;

			AppendChar(cMaxBet);
			// Progressive group
			AppendChar(0);
			// Game options
			AppendChar(0, 2);
			// Paytable
			AppendStr(pSASModule->GetPaytableID(), 6);
			// Pay back percentage
			nPaybackPecentage = pSASModule->GetRTP();
			sprintf((char*)sPaybackPercentage, "%u", nPaybackPecentage);
			AppendBuf(sPaybackPercentage, 4);
		}
		break;
		// ---------------- "Send dollar value of bills meter" ----------------
		case 0x20: AppendCounter(METER_TOTAL_BILLS_IN_CURRENCY, 4); break;
		case 0x21: break;
		// ---------------- "Send current promotional credits" ----------------
		case 0x27: AppendCounter(METER_RESTRICTED_CREDIT, 4); break;
		case 0x28: break;
		// ---------------- "Send true coin in" ----------------
		case 0x2A: AppendCounter(METER_COIN_IN, 4); break;
		// ---------------- "Send true coin out" ----------------
		case 0x2B: AppendCounter(METER_COIN_OUT, 4); break;
		// ---------------- "Send current hopper level" ----------------
		case 0x2C: AppendCounter(METER_HOPPER_LEVEL, 4); break;
		case 0x2E: break;
		// ---------------- "Send 1.00 bill in meter" ----------------
		case 0x31: AppendCounter(METER_TOTAL_BILLS_1, 4); break;
		// ---------------- "Send 2.00 bill in meter" ----------------
		case 0x32: AppendCounter(METER_TOTAL_BILLS_2, 4); break;
		// ---------------- "Send 5.00 bill in meter" ----------------
		case 0x33: AppendCounter(METER_TOTAL_BILLS_5, 4); break;
		// ---------------- "Send 10.00 bill in meter" ----------------
		case 0x34: AppendCounter(METER_TOTAL_BILLS_10, 4); break;
		// ---------------- "Send 20.00 bill in meter" ----------------
		case 0x35: AppendCounter(METER_TOTAL_BILLS_20, 4); break;
		// ---------------- "Send 50.00 bill in meter" ----------------
		case 0x36: AppendCounter(METER_TOTAL_BILLS_50, 4); break;
		// ---------------- "Send 100.00 bill in meter" ----------------
		case 0x37: AppendCounter(METER_TOTAL_BILLS_100, 4); break;
		// ---------------- "Send 500.00 bill in meter" ----------------
		case 0x38: AppendCounter(METER_TOTAL_BILLS_500, 4); break;
		// ---------------- "Send 1000.00 bill in meter" ----------------
		case 0x39: AppendCounter(METER_TOTAL_BILLS_1000, 4); break;
		// ---------------- "Send 200.00 bill in meter" ----------------
		case 0x3A: AppendCounter(METER_TOTAL_BILLS_200, 4); break;
		// ---------------- "Send 25.00 bill in meter" ----------------
		case 0x3B: AppendCounter(METER_TOTAL_BILLS_25, 4); break;
		// ---------------- "Send 2000.00 bill in meter" ----------------
		case 0x3C:
			AppendCounter(METER_TOTAL_BILLS_2000, 4);
			break;
			// ---------------- "Send cash out ticket information" ----------------
#ifdef SAS_TICKETING
		case 0x3D:
			// if secure enhanced or system validation not respond to validation controler
			if (!(pSASChannel->SASTicketing.IsSecureEnhancedOrSystemValidation()))
			{
				TString s8DigitVal, sTicketValidation, sTicketAmount;
				int nValLen;

				pSASModule->ParamGet("PRINTED_VALIDATION_NUM", sTicketValidation);
				pSASModule->ParamGet("PRINTED_TICKET_AMOUNT", sTicketAmount);
				nValLen = sTicketValidation.length();
				// if not validation channel or val. number invalid
				if (nValLen < 8)
					// validation number all 0s
					AppendBCD(0, 4);
				else
				{
					// only last 8 digits
					s8DigitVal = sTicketValidation.Mid(nValLen - 8);
					AppendBCD(s8DigitVal, 4);
				}
				// ticket amount
				AppendBCD(sTicketAmount, 5);
			}
			break;
#endif
		// ---------------- "Send 2500.00 bill in meter" ----------------
		case 0x3E: AppendCounter(METER_TOTAL_BILLS_2500, 4); break;
		// ---------------- "Send 5000.00 bill in meter" ----------------
		case 0x3F: AppendCounter(METER_TOTAL_BILLS_5000, 4); break;
		// ---------------- "Send 10000.00 bill in meter" ----------------
		case 0x40: AppendCounter(METER_TOTAL_BILLS_10000, 4); break;
		// ---------------- "Send 20000.00 bill in meter" ----------------
		case 0x41: AppendCounter(METER_TOTAL_BILLS_20000, 4); break;
		// ---------------- "Send 25000.00 bill in meter" ----------------
		case 0x42: AppendCounter(METER_TOTAL_BILLS_25000, 4); break;
		// ---------------- "Send 50000.00 bill in meter" ----------------
		case 0x43: AppendCounter(METER_TOTAL_BILLS_50000, 4); break;
		// ---------------- "Send 100000.00 bill in meter" ----------------
		case 0x44: AppendCounter(METER_TOTAL_BILLS_100000, 4); break;
		// ---------------- "Send 250.00 bill in meter" ----------------
		case 0x45: AppendCounter(METER_TOTAL_BILLS_250, 4); break;
		// ---------------- "Send credit amount of all bills accepted" ----------------
		case 0x46:
			// UNUSED double           fDenomination;
			uint64_t nCredits;    // UNUSED , nBillsInCurrency;

			// in base account unit
			//      SASI_GetMeter("total bill in credit", &nCredits);
			pSASModule->MeterGet(METER_TOTAL_BILL_IN_CREDIT, &nCredits);
			AppendBCD(nCredits, 4);
			break;
		case 0x47: break;
		// ---------------- "Send last accepted bill" ----------------
		case 0x48: {
			unsigned int nCountryCode = 0, nBillValue = 0, nBillDenominationCode = 0;
			unsigned short nBillMeterID = 0;
			uint64_t nBillCount = 0;
			// UNUSED char             str[128];

			// Country currency code
			// nCountryCode = SASI_GetCurrencyCountryCode();
			nCountryCode = pSASModule->CurrencyCountryCodeGet();
			AppendBCD(nCountryCode, 1);
			// Bill Denomination
			// SASI_GetLastAcceptedBillValue(&nBillValue);
			nBillValue = pSASModule->LastAcceptedBillValueGet();
			nBillDenominationCode = GetBillDenominationCode(nBillValue);
			AppendBCD(nBillDenominationCode, 1);

			// Number of accepted bills of this type
			if (nBillValue > 0)
			{
				GetBillMeterID(nBillValue, &nBillMeterID);
				// sprintf(str, "total bills %d", nBillValue);
				//          SASI_GetMeter(str, &nBillCount);
				// pSASModule->MeterGet(str, &nBillCount);
				pSASModule->MeterGet(nBillMeterID, &nBillCount);
			}
			AppendBCD(nBillCount, 4);
			break;
		};
		// ---------------- "Send number of bills currently in the stacker" ----------------
		case 0x49: AppendCounter(METER_BILLS_IN_STACKER, 4); break;
		// ---------------- "Send total credit amount of all bills currently in the stacker" ----------------
		case 0x4A: AppendCounter(METER_BILLS_IN_STACKER_CREDIT, 4); break;
		// ---------------- "Send total number of games implemented" ----------------
		case 0x51:
			// SAS book says: if a gaming machine does not support
			// multi-game extensions it must respond with the number of games implemented
			// equal to 0
			AppendBCD(pSASModule->NumberOfImplementedGames(), 2);
			break;
		// ---------------- "Send SAS version ID and gaming machine serial number" ----------------
		case 0x54: {
			const std::string serial = pSASModule->MachineSerialNumberGet();
			AppendChar(3 + serial.length());
			AppendStr(SAS_VERSION_ID, 3);
			AppendStr(serial, (int)serial.length());
			break;
		}
		// ---------------- "Send selected game number" ----------------
		case 0x55: {
			// Only for multigame machines
			if (pSASModule->NumberOfImplementedGames())
			{
				AppendBCD(pSASModule->SelectedGameNumber(), 2);
			}
			break;
		}
		// ---------------- "Send enabled game numbers" ----------------
		case 0x56: {
			// Only for multigame machines
			if (pSASModule->NumberOfImplementedGames())
			{
				std::vector<int> enabledGames;
				pSASModule->GetEnabledGames(enabledGames);
				AppendChar(1 + 2 * enabledGames.size());    // Number of bytes after this parameter
				AppendChar(enabledGames.size());    // Number of enabled games
				for (const int& gameID : enabledGames) AppendBCD(gameID, 2);    // Append each game ID
			}
			break;
		}
		// ---------------- "Send Pendinding Cashout Information Response"  ---------------
#ifdef SAS_TICKETING
		case 0x57: {
			TString sValidation;

			if (pSASChannel->SASTicketing.IsSystemValidation())
			{
				AppendChar(pSASChannel->SASTicketing.m_cSystemCashoutType);
				AppendBCD(pSASChannel->SASTicketing.m_sSystemCashoutAmount, 5);
				pSASChannel->SASTicketing.m_cSystemValidationState = SYSTEM_VALIDATION_RECEIVED_57;

				// because we have received a response to this exception, so if a general poll is slow and hasn't polled a repeated exception yet, remove it to prevent
				// sending it again
				pSASModule->SASRemovePriorityException(SAS_EXC_SYSTEM_VALIDATION_REQUEST);

				SDL_SemPost(pSASChannel->SASTicketing.SystemValidationSemaphore);
			}
			break;
		}
#endif
		// ---------------- "Send Ticket Validation Data Response" ----------------
#ifdef SAS_TICKETING
		case 0x70: {
			TString sValidation;

			if (pSASChannel->SASTicketing.TicketInEnabled())
			{
				// ticket status
				sValidation = pSASChannel->SASTicketing.m_sTicketInEscrow.getCopy();
				if (sValidation.empty())
				{
					// len
					AppendChar(1);
					// no ticket in escrow
					AppendChar(0xFF);
				}
				else
				{
					// len
					AppendChar(16);
					// ticket in escrow, data follows
					AppendChar(0x00);
					// amount
					AppendChar(0, 5);
					// validation parsing code
					AppendChar(0);
					// validation data
					AppendBCD(sValidation, 9);
					pSASChannel->SASTicketing.TicketInAfter070(sValidation);
				}
			}
		}
		break;
#endif
		// ---------------- Send current date and time ----------------
		case 0x7E: {
			TString sTime = "000000", sDate = "01012000";

			pSASModule->GetTime(&sDate, &sTime);
			AppendBCD(sDate, 4);
			AppendBCD(sTime, 3);
		}
		break;
		// ---------------- Send phyisical reel stop ----------------
		case 0x8F: {
			unsigned char cLastWinNum = 0xFF;

			if (pSas)
				cLastWinNum = ((TSASChannel*)pSas)->SASExceptions.getLastExcWinNum();
			if (cLastWinNum > 37)
				cLastWinNum = 0xFF;
			AppendChar(cLastWinNum);
			AppendChar(0xFF, 8);
		}
		break;

		// ---------------- "Send current player denomination" ----------------
		case 0xB1: {
			unsigned char cCurrentDenom = 0;

			// SASI_GetCurrentDenominationCode(&cCurrentDenom);
			cCurrentDenom = pSASModule->CurrentDenomiCodeGet();
			AppendChar(cCurrentDenom);
			break;
		}
		// ---------------- "Send enabled denominations" ----------------
		case 0xB2: {
			unsigned char nLen = 1, cNumDenoms = 0;    // UNUSED , cTokenDenom = 0;
			unsigned char sEnabledDenoms[128];

			AppendChar(nLen);
			AppendChar(cNumDenoms);

			// SASI_GetEnabledDenominationsCode(&sEnabledDenoms[0], &cNumDenoms);
			cNumDenoms = pSASModule->EnabledDenomiGet(sEnabledDenoms);
			AppendBuf(sEnabledDenoms, cNumDenoms);
			nLen = nLen + cNumDenoms;
			// update len
			SetByte(2, nLen);
			SetByte(3, cNumDenoms);
			break;
		}

		default: break;
	}
	if (m_Len > 2)
	{
		AppendShort(CcittCrc(m_Buf, m_Len, 0));
	}
	else
		Clear();

	return true;
}

/* Clears the packet and builds a M long poll response packet */
/* If the poll is not supporeted an empty packet is returned */
int TSASPoll::BuildMLongPollResponse(TSASPoll* pInputPct, void* pSas)
{
	int nCommand;
	unsigned char cMachineAddr;
	TSASChannel* pSASChannel = NULL;

	pSASChannel = (TSASChannel*)pSas;

	cMachineAddr = ((TSASChannel*)pSas)->SASChannelConf.cSASAddr;

	Clear();

	if ((nCommand = pInputPct->GetByte(1)) == -1)
		return false;

	AppendChar((unsigned char)cMachineAddr);
	AppendChar((unsigned char)nCommand);

	switch (nCommand)
	{
		// ---------------- "Send hand paid credits" ----------------
		case 0x2D: {
			uint64_t nHandPayCredits;
			int gameN = pInputPct->GetBCDAsInt(2, 2);
			//          if ((strcmp(sGameNum, "0000") == 0) && SASI_GetMeter("total hand pay canceled credits", &nHandPayCredits))
			if ((gameN <= pSASModule->NumberOfImplementedGames()) && pSASModule->MeterGet(METER_TOTAL_HAND_PAY_CANCELED_CREDIT, &nHandPayCredits, gameN))
			{
				AppendBCD(0, 2);
				AppendBCD(nHandPayCredits, 4);
			}
		}
		break;
		// ---------------- "Send selected meters command" ----------------
		case 0x2F: {
			// char           sMeterName[128];
			unsigned char cLen, cMeterCode, cOutLen, cMeterLen;
			unsigned short nMeterNameID;
			int i;
			unsigned int uPayback;

			cLen = pInputPct->GetByte(2);
			int gameN = pInputPct->GetBCDAsInt(3, 2);
			// if for gamming machine
			if (gameN <= pSASModule->NumberOfImplementedGames())
			{
				// length, 2 for now
				AppendChar(2);
				// game number
				AppendBCD((unsigned int)gameN, 2);

				cOutLen = 2;
				for (i = 5; i < cLen + 3; i++)
				{
					cMeterCode = pInputPct->GetByte(i);
					if (GetMeterID(cMeterCode, &nMeterNameID, &cMeterLen))
					{
						// meter code
						AppendChar(cMeterCode);
						AppendCounter(nMeterNameID, cMeterLen, gameN);
						cOutLen += cMeterLen + 1;
					}
					// theoretical payback percentage (special case)
					if (cMeterCode == 0x7F)
					{
						uPayback = pSASModule->GetRTP(gameN);
						// meter code
						AppendChar(cMeterCode);
						AppendBCD(uPayback, 4);
						cOutLen += 4 + 1;
					}
				}
				SetByte(2, cOutLen);
			}
		}
		break;
		// ---------------- "Send game N meters" ----------------
		case 0x52: {
			int gameN = pInputPct->GetBCDAsInt(2, 2);
			// if for gamming machine
			if (gameN <= pSASModule->NumberOfImplementedGames())
			{
				// game number 0000
				AppendBCD((unsigned int)gameN, 2);
				AppendCounter(METER_TOTAL_IN, 4, gameN);
				AppendCounter(METER_TOTAL_COIN_OUT, 4, gameN);
				AppendCounter(METER_JACKPOT, 4, gameN);
				AppendCounter(METER_GAMES_PLAYED, 4, gameN);
			}
			else
			{
				// TO DO
			}
		}
		break;
		// ---------------- "Enable/disable game n" ----------------
		case 0x09: {
			int gameN = pInputPct->GetBCDAsInt(2, 2);

			pSASModule->SetGameAvailability(gameN, pInputPct->GetByte(4));
			Build(SAS_PCT_ACK, pSASChannel);
			return true;    // Because this method (build long M poll) does not allow for messages of length < 2, we have to return here
			break;
		}
		// ---------------- "Send game n configuration" ----------------
		case 0x53: {
			int gameN = pInputPct->GetBCDAsInt(2, 2);
			AppendBCD(gameN, 2);    // game ID
			AppendBuf((unsigned char*)"IM", 2);    // game manufacturer ASCII ID in SAS document C-1 appendix table
			AppendStr(pSASModule->GetGameSASIdentifier(gameN), 3);    // additional game id
			AppendChar(pSASModule->CurrentDenomiCodeGet());
			uint64_t maxBet = pSASModule->GetMaxBet(gameN);
			AppendChar((maxBet && maxBet < 0xFF) ? maxBet : 0xFF);
			AppendChar(0);    // Progresive group?
			AppendChar(0, 2);    // Game options

			std::string gameName = "", paytableName = "";
			int wagerCategories = 0;
			int progressiveGroup = 0;
			// this is a bit-flag of JP levels in which the game is signed up
			int32_t progressiveLevels = 0;
			pSASModule->GetGameInfo(gameN, gameName, paytableName, wagerCategories, progressiveGroup, progressiveLevels);

			AppendStr(paytableName, 6);    // Paytable ID

			char rtpValStr[5];
			snprintf(rtpValStr, 5, "%04d", pSASModule->GetRTP(gameN));
			rtpValStr[4] = '\0';
			AppendBuf((unsigned char*)rtpValStr, 4);    // Base theoretical payback % for max bet
			break;
		}
		// ---------------- "Send extended meters for game N" ----------------
		case 0x6F:
		case 0xAF: {
			// UNUSED char           sMeterName[128];
			unsigned char cLen, cOutLen, cMeterLen;
			unsigned int cMeterCode;
			unsigned short nMeterID;

			cLen = pInputPct->GetByte(2);
			int gameN = pInputPct->GetBCDAsInt(3, 2);
			// if for gamming machine
			if (gameN <= pSASModule->NumberOfImplementedGames())
			{
				// length, 2 for now
				AppendChar(2);
				// game number
				AppendBCD((unsigned int)gameN, 2);
				cOutLen = 2;
				for (int i = 5; i < cLen + 3; i = i + 2)
				{
					// max 12 meters can be returned
					if (i < (5 + 12 * 2))
					{
						pInputPct->GetInt(i, 2, &cMeterCode);
						if (GetMeterID(cMeterCode, &nMeterID, &cMeterLen))
						{
							// meter code
							AppendChar(cMeterCode);
							AppendChar(0);
							// meter size is 5 BCD
							AppendInt(SAS_EXTENDED_METER_SIZE, 1);
							// meter value
							AppendCounter(nMeterID, SAS_EXTENDED_METER_SIZE, gameN);
							cOutLen += SAS_EXTENDED_METER_SIZE + 3;
						}
						// if meter not supported
						else
						{
							// meter code
							AppendChar(cMeterCode);
							AppendChar(0);
							// meter size is 0
							AppendInt(0, 1);
							cOutLen += 3;
						}
					}
				}
				SetByte(2, cOutLen);
			}
		}
		break;

		// ---------------- "Send Enabled Features" ----------------
		case 0xA0: {
			unsigned char bFeatures1, bFeatures2, bFeatures3, bFeatures4;
			TString sStr;
			unsigned char b;

			int gameN = pInputPct->GetBCDAsInt(2, 2);

			// If Game number is 0000 (gaming machine)
			if (gameN <= pSASModule->NumberOfImplementedGames())
			{
				// FEATURES 1
				bFeatures1 = 0;
				// Jackpot multiplier
				if (pSASModule->hasJackpotMultiplier(gameN))
					bFeatures1 = bFeatures1 | 1;
				// AFT bonus awards !! ONLY FOR GAME ID 0
				if (pSASModule->ParamEqual("AFT_SUPPORTED", "1") && pSASModule->ParamEqual("AFT_BONUS_ENABLED", "1"))
					bFeatures1 = bFeatures1 | (1 << 1);
				// Legacy bonus awards
				if (pSASModule->hasLegacyBonusAwards(gameN))
					bFeatures1 = bFeatures1 | (1 << 2);
				// Tournament mode supported
				if (pSASModule->hasTournament(gameN))
					bFeatures1 = bFeatures1 | (1 << 3);
#ifdef SAS_TICKETING
				if (pSASModule->supportsTicketing(gameN))
				{
					// support ticketing extensions to set custom expiration time from system with long polls 7B and 7C
					bFeatures1 = bFeatures1 | (1 << 4);

					// validation type 0 - for Standard or none
					b = 0;
					if (pSASChannel->SASTicketing.IsSecureEnhancedValidation())
						b = 2;
					if (pSASChannel->SASTicketing.IsSystemValidation())
						b = 1;
					bFeatures1 = bFeatures1 | (b << 5);

					// ticket redemption
					b = 0;
					if (pSASChannel->SASTicketing.TicketInEnabled())
						b = 1;
					bFeatures1 = bFeatures1 | (b << 7);
				}
#endif

				// FEATURES 2
				bFeatures2 = 0;
				// won credits metered when won
				bFeatures2 = bFeatures2 | (1);
				// tickets to total drop and total canceled credits
#ifdef SAS_TICKETING
				if (pSASModule->supportsTicketing(gameN))
					bFeatures2 = bFeatures2 | (1 << 2);
#endif
				// Extended meters - supported
				bFeatures2 = bFeatures2 | (1 << 3);

				// bit 4 - component authentication - not supported
				// bit 5 - reserved (SAS 6.0.2)
				// bit 5 - jackpot key off to machine (SAS 6.0.3) - not supported

				//   AFT supported !! ONLY FOR GAME ID 0
				if (pSASModule->ParamEqual("AFT_SUPPORTED", "1") && (gameN == 0))
					bFeatures2 = bFeatures2 | (1 << 6);
				// bit 7 - multi denomination - not supported

				// FEATURES 3
				bFeatures3 = 0;
				// maximum pooling rate - supported
				bFeatures3 = bFeatures3 | (1);
				// multiple sas progressive win reporting - not supported

				// bit 2-7 - reserved (SAS 6.0.2)
				// bit 2 - meter change notification (SAS 6.0.3) - not supported
				// bit 3 - reserved (SAS 6.0.3)
				// bit 4 - session play (SAS 6.0.3) - not supported
				// bit 5 - foreign currency redemption (SAS 6.0.3) - not supported
				// bit 6 - non-SAS progressive hit reporting (SAS 6.0.3) - not supported
				// bit 7 - enhanced progressive data reporting (SAS 6.0.3) - not supported

				// FEATURES 4
				bFeatures4 = 0;
				// bit 0 - max progressive payback
				// bit 1 - extended handpay information
				// asynchronous game play event
				bFeatures4 = bFeatures4 | (1 << 2);
				// bit 3-7 - reserved (SAS 6.0.3)

				// build response
				AppendBCD((unsigned int)gameN, 2);
				AppendChar(bFeatures1);
				AppendChar(bFeatures2);
				AppendChar(bFeatures3);

				if (pSASModule->SASType == magic::SAS_6_0_3)
				{
					AppendChar(bFeatures4);
					AppendChar(0, 2);
				}
				else
					AppendChar(0, 3);
			}
			else
				Clear();
			break;
		}
		// ---------------- "Send wager category info" ----------------
		case 0xB4: {
			int gameN = pInputPct->GetBCDAsInt(2, 2);
			int wagerCategory = pInputPct->GetBCDAsInt(4, 2);

			if (wagerCategory)
			{
				Build(SAS_PCT_NACK, pSASChannel);
				return true;
			}

			std::string gameName = "", paytableName = "";
			int wagerCategories = 0;
			int progressiveGroup = 0;
			// this is a bit-flag of JP levels in which the game is signed up
			int32_t progressiveLevels = 0;
			pSASModule->GetGameInfo(gameN, gameName, paytableName, wagerCategories, progressiveGroup, progressiveLevels);

			// length of bytes following
			AppendChar(18);
			AppendBCD((unsigned int)gameN, 2);
			// wager category
			AppendBCD(wagerCategories, 2);
			// RTP
			char rtpValStr[5];
			snprintf(rtpValStr, 5, "%04d", pSASModule->GetRTP(gameN));
			AppendBuf((unsigned char*)rtpValStr, 4);    // Base theoretical payback % for max bet
			AppendChar(9);
			uint64_t meter;
			pSASModule->MeterGet(METER_TOTAL_IN, &meter, gameN);
			AppendBCD(meter % 1000000000UL, 9);
			break;
		}
		// ---------------- "Send extended game N info" ----------------
		case 0xB5: {
			int gameN = pInputPct->GetBCDAsInt(2, 2);

			std::string gameName = "", paytableName = "";
			int wagerCategories = 0;
			int progressiveGroup = 0;
			int gameType = 0;
			int gameInfoLen = 0;
			uint8_t* gameInfo;

			// this is a bit-flag of JP levels in which the game is signed up
			int32_t progressiveLevels = 0;
			pSASModule->GetGameInfo(gameN, gameName, paytableName, wagerCategories, progressiveGroup, progressiveLevels);

			if (pSASModule->SASType == magic::SAS_6_0_3)
			{
				gameInfo = pSASModule->GetGameTypeInfo(gameN, gameType, gameInfoLen);

				// length of bytes following
				AppendChar(21 + gameName.length() + paytableName.length() +
				           gameInfoLen);    // 13 + 1 (num player denoms) + 5 (max bet total) + 1 (game type) + 1 (game info length)
			}
			else
			{
				// length of bytes following
				AppendChar(13 + gameName.length() + paytableName.length());
			}

			AppendBCD((unsigned int)gameN, 2);

			uint64_t maxBet = pSASModule->GetMaxBet(gameN);
			AppendBCD((maxBet && maxBet < 9999) ? maxBet : 9999, 2);

			AppendChar(progressiveGroup);
			AppendInt(progressiveLevels, 4);
			AppendChar(gameName.length());
			AppendBuf((unsigned char*)gameName.c_str(), gameName.length());
			AppendChar(paytableName.length());
			AppendBuf((unsigned char*)paytableName.c_str(), paytableName.length());
			AppendBCD(wagerCategories, 2);

			if (pSASModule->SASType == magic::SAS_6_0_3)
			{
				// num player denoms
				AppendChar(0);
				// player denoms - zero - not supported

				// max bet total
				AppendBCD((maxBet && maxBet < 9999999999) ? maxBet : 9999999999, 5);
				// game type
				AppendChar(gameType);
				// game info length
				AppendChar(gameInfoLen);
				// game info - optional - no data for now
				if (gameInfoLen > 0 && gameInfo)
				{
					for (int i = 0; i < gameInfoLen; i++) AppendChar(reinterpret_cast<unsigned char>(gameInfo[i]), 1);
					delete[] gameInfo;
				}
			}

			break;
		}
		default: break;
	}
	if (m_Len > 2)
	{
		AppendShort(CcittCrc(m_Buf, m_Len, 0));
	}
	else
		Clear();

	return true;
}

/* Returns true if nIndex-th bit in cChar is set */
/* The right most bit has index 0                   */
static int IsBitSet(unsigned char cChar, int nIndex)
{
	if ((cChar & (1 << nIndex)) == (1 << nIndex))
		return true;
	return false;
}

/* Clears the packet and builds a R long poll response packet */
/* If the poll is not supported no packet is returned */
int TSASPoll::BuildSLongPollResponse(TSASPoll* pInputPct, void* pSas, bool* bRealTimeEventReport)
{
	int nCommand;
	unsigned char bByte;
	unsigned char cMachineAddr;
	TSASChannel* pSASChannel = NULL;

	pSASChannel = (TSASChannel*)pSas;

	cMachineAddr = pSASChannel->SASChannelConf.cSASAddr;
	Clear();
	if ((nCommand = pInputPct->GetByte(1)) == -1)
		return false;

	switch (nCommand)
	{
		// ---------------- "Shutdown (lock out play)" ----------------
		case 0x01:
			// GM lock
			pSASModule->_PlaystationLockRequest();
			Build(SAS_PCT_ACK, pSASChannel);
			break;
		// ---------------- "Startup (enable play)" ----------------
		case 0x02:
			// GM unlock
			pSASModule->_PlaystationUnlockRequest();
			Build(SAS_PCT_ACK, pSASChannel);
			break;
		// ---------------- "Sound ON (all sound enable - UNMUTE)" ----------------
		case 0x04:
			pSASModule->_SoundEnable();
			Build(SAS_PCT_ACK, pSASChannel);
			break;
		// ---------------- "Sound OFF (all sound disable - MUTE)" ----------------
		case 0x03:
			pSASModule->_SoundDisable();
			Build(SAS_PCT_ACK, pSASChannel);
			break;
		// ---------------- "Bill ENABLE (Enable Bill Acceptor)" ----------------
		case 0x06:
			// pSASModule->bBillAcceptorEnabled = true;
			if (pSASModule->pParamBillAcceptorEnabled)
			{
				pSASModule->pParamBillAcceptorEnabled->SetValue(1, true);
			}
			Build(SAS_PCT_ACK, pSASChannel);
			break;
		// ---------------- "Bill DISABLE (Disable Bill Acceptor)" ----------------
		case 0x07:
			// pSASModule->bBillAcceptorEnabled = false;
			if (pSASModule->pParamBillAcceptorEnabled)
			{
				pSASModule->pParamBillAcceptorEnabled->SetValue(0, true);
			}
			Build(SAS_PCT_ACK, pSASChannel);
			break;
			// ---------------- "Enable/Disable Real Time event reporting" ----------------
		case 0x0E:
			// third byte
			bByte = pInputPct->GetByte(2);
			if (bRealTimeEventReport)
			{
				*bRealTimeEventReport = (bByte == 1 ? true : false);
				Build(SAS_PCT_ACK, pSASChannel);
			}
			else
				Build(SAS_PCT_NACK, pSASChannel);
			break;
#ifdef SAS_TICKETING
			// ---------------- Set secure Enhanced Validation ID ----------------
		case 0x4C:
			// if secure enhanced validation enabled
			if (pSASChannel->SASTicketing.IsSecureEnhancedValidation())
			{
				unsigned int nMachineId, nSeqNumber;
				int bUpdateValues = true;
				pInputPct->GetInt(2, 3, &nMachineId);
				pInputPct->GetInt(5, 3, &nSeqNumber);
				// if the sent values are the same as previously sent
				if ((nMachineId == pSASChannel->SASTicketing.m_nPrevSentValidationID) && (nSeqNumber == pSASChannel->SASTicketing.m_nPrevSentSeqNum))
				{
					// has the sequence number been incremented since
					if ((unsigned int)pSASChannel->SASTicketing.GetSeqNum() > nSeqNumber)
						bUpdateValues = false;
				}
				// set sent values to mirror
				if ((nMachineId != 0) && bUpdateValues)
				{
					pSASChannel->SASTicketing.SetMachineValidationIDAndSeqNum(nMachineId, nSeqNumber);
				}
				// build response
				{
					AppendChar(cMachineAddr);
					AppendChar(nCommand);
					AppendInt(pSASChannel->SASTicketing.GetValidationID(), 3);
					AppendInt(pSASChannel->SASTicketing.GetSeqNum(), 3);
					AppendShort(CcittCrc(m_Buf, m_Len, 0));
				}
				// remember sent values as previous
				pSASChannel->SASTicketing.m_nPrevSentSeqNum = nSeqNumber;
				pSASChannel->SASTicketing.m_nPrevSentValidationID = nMachineId;
			}
			break;
			// ---------------- Send enhanced validation information ----------------
		case 0x4D:
			// if secure enhanced or system validation enabled
			if (pSASChannel->SASTicketing.IsSystemValidation() || pSASChannel->SASTicketing.IsSecureEnhancedValidation())
			{
				unsigned char cFuncCode;
				ValidationRecord* pVanRec;
				int nRecPos = -1;

				pSASChannel->SASTicketing.m_nLastSentValidationRec = -1;
				cFuncCode = pInputPct->GetByte(2);
				// current validation info
				if ((cFuncCode == 0) || (cFuncCode == 0xFF))
					nRecPos = pSASChannel->SASTicketing.GetFirstUnread();
				// index of val. info
				else if (cFuncCode >= 1 && cFuncCode <= MAX_VALIDATION_RECS)
					nRecPos = cFuncCode - 1;

				// if there are no unread records send all 0's
				if (((cFuncCode == 0) || (cFuncCode == 0xFF)) &&
				    (pSASChannel->SASTicketing.IsValidationBufferEmpty() || (pSASChannel->SASTicketing.UnreadRecords() == 0)))
					nRecPos = -1;

				AppendChar(cMachineAddr);
				AppendChar(nCommand);
				// if no such validation record or no unread records send just 0s
				// if (nRecPos == -1 || (pSASChannel->SASTicketing.UnreadRecords() == 0))
				//  AppendChar(0, 17 + 14);
				// else if (nRecPos >= 0 && nRecPos < MAX_VALIDATION_RECS)
				if (nRecPos >= 0 && nRecPos < MAX_VALIDATION_RECS)
				// fill record data in
				{
					SDL_LockMutex(pSASChannel->SASTicketing.m_mValMut);
					pVanRec = &(pSASChannel->SASTicketing.m_aVlnRecs[nRecPos]);
					AppendChar(pVanRec->cValidationType);
					AppendChar(pVanRec->cSeqNum);
					AppendBuf((unsigned char*)(pVanRec->cDate), 4);
					AppendBuf((unsigned char*)(pVanRec->cTime), 3);
					AppendBuf((unsigned char*)(pVanRec->cValidationNumber), 8);
					AppendBuf((unsigned char*)(pVanRec->cAmount), 5);
					AppendShort(pVanRec->shTicketNumber);
					AppendChar(pVanRec->cValidationSystemID);
					AppendBuf((unsigned char*)(pVanRec->cExpiration), 4);
					AppendShort(pVanRec->shPoolID);
					// mark it as unread only if asked for current record
					if (cFuncCode == 0x00)
						pSASChannel->SASTicketing.m_nLastSentValidationRec = nRecPos;
					SDL_UnlockMutex(pSASChannel->SASTicketing.m_mValMut);
					// printf("Validation record %d sent: \n  val type = %d \n  ticket number = %d \n  expiration (LSB) = 0x%X)\n", nRecPos, pVanRec->cValidationType,
					// pVanRec->shTicketNumber, *((unsigned int*)pVanRec->cExpiration));
				}
				else
					// send all 0s
					AppendChar(0, 17 + 14);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));
			}
			break;
#endif
#ifdef SAS_TICKETING
		// ---------------- Send validation meters ----------------
		case 0x50: {
			unsigned char cValType = 0;
			// TString       sQty, sAmountInCents;
			int nQty = 0, nAmountInCents = 0;

			cValType = pInputPct->GetByte(2);
			// TODO
			GetValidationMetersPair(cValType, nQty, nAmountInCents);
			if ((nQty != 0) && (nAmountInCents != 0))
			{
				AppendChar(cMachineAddr);
				AppendChar(nCommand);
				AppendChar(cValType);
				// TODO use counter indexes
				AppendCounter(nQty, 4);
				AppendCounter(nAmountInCents, 5);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));
			}
		}
		break;
#endif
#ifdef SAS_TICKETING

			// ---------------- Receive Validation Number Response ----------------
		case 0x58:
			// if system validation enabled
			if (pSASChannel->SASTicketing.IsSystemValidation())
			{
				unsigned char cSystemID, cResponseStatus = 0x00;
				TString sValidationNumber, sValidationSystemID;

				cSystemID = pInputPct->GetByte(2);
				if ((pInputPct->GetBCDAsStr(2, 1, sValidationSystemID) == -1) || (pInputPct->GetBCDAsStr(3, 8, sValidationNumber) == -1))
				{
					cResponseStatus = 0x81;    // improper validation
				}
				else if (pSASChannel->SASTicketing.m_cSystemValidationState == SYSTEM_VALIDATION_NO_ACTION)
				{
					cResponseStatus = 0x80;    // not in cashout
				}
				else if (cSystemID != 0)
				{
					// -------------------------- Check if already printed --------------------------
					// check last printed validation number with the sValidation
					// if equal reject the printing of the ticket
					if (sValidationSystemID == pSASModule->GetLastPrintedTicketNumber())
					{
						cResponseStatus = 0x81;    // improper validation
					}
					else
					{
						pSASChannel->SASTicketing.m_sValidationSystemID = sValidationSystemID;
						pSASChannel->SASTicketing.m_sSystemValidationNumber = sValidationNumber;
						if (pSASChannel->SASTicketing.m_cSystemValidationState == SYSTEM_VALIDATION_RECEIVED_57)
							pSASChannel->SASTicketing.m_cSystemValidationState = SYSTEM_VALIDATION_ACKED;
						else
							pSASChannel->SASTicketing.m_cSystemValidationState = SYSTEM_VALIDATION_58_RECEIVED_BEFORE_57;

						cResponseStatus = 0x00;    // acknowledged
					}
				}
				else
				{
					pSASChannel->SASTicketing.m_cSystemValidationState = SYSTEM_VALIDATION_HOST_DENIED;
					cResponseStatus = 0x00;    // acknowledged
				}

				AppendChar(cMachineAddr);
				AppendChar(nCommand);
				AppendChar(cResponseStatus);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));

				// because we have received a response to this exception, so if a general poll is slow and hasn't polled a repeated exception yet, remove it to prevent
				// sending it again
				pSASModule->SASRemovePriorityException(SAS_EXC_SYSTEM_VALIDATION_REQUEST);

				SDL_SemPost(pSASChannel->SASTicketing.SystemValidationSemaphore);
			}
			break;
#endif
			// ---------------- AFT Transfer Funds ----------------
		case 0x72: {
			unsigned int uCurrentPos = 0;
			TSASAftTransaction cA;
			TString sCurrTrans;

			if (pSASChannel && pSASModule->ParamEqual("AFT_SUPPORTED", "1"))
			{
				// address
				AppendChar((unsigned char)cMachineAddr);
				// command
				AppendChar((unsigned char)nCommand);
				// append length
				AppendChar(0x01);
				// read packet
				// UNUSED uLength = pInputPct->GetByte(2);
				cA.cTransferCode = pInputPct->GetByte(3);
				cA.cTransactionIndex = pInputPct->GetByte(4);
				cA.cTransferType = pInputPct->GetByte(5);
				cA.cTransferFlags = pInputPct->GetByte(21);
				// check if interrogation poll
				if (cA.cTransferCode == 0xFE || cA.cTransferCode == 0xFF)
				{
					// if buffer empty and no current trans
					pSASModule->ParamGet("AFT_CURRENT", uCurrentPos);

					if (uCurrentPos == 0)
					{
						pSASModule->ParamGet("AFT_CURRENT_TRANSACTION", sCurrTrans);

						if (sCurrTrans.empty())
						{
							AppendChar(0x00);
							AppendChar(0xFF);
							AppendChar(0xFF);
							goto crc72;
						}
					}

					// if transaction index invalid
					if (!(pSASChannel->SASAft.TransBuffResponse(cA.cTransactionIndex, &cA)))
					{
						AppendChar(cA.cTransactionIndex);
						AppendChar(0xFF);
						AppendChar(0xFF);
						goto crc72;
					}
				}
				// check if unsupported transfer code
				else if (cA.cTransferCode > AFT_TC_PARTIAL_TRANSFER)
				{
					AppendChar(cA.cTransactionIndex);
					AppendChar(TSC_UNSUPPORTED_TRANSFER_CODE);
					TLOG(LogSAS, Error, "AFT Error: %s", GetAFTTransferStatusCodesTxt(TSC_UNSUPPORTED_TRANSFER_CODE));
					goto crc72;
				}
				// check if lock requiered
				else if (IsBitSet(cA.cTransferFlags, 6) && pSASModule->GetAFTLock() != AFT_GAME_LOCKED)
				{
					AppendChar(cA.cTransactionIndex);
					AppendChar(TSC_GM_NOT_LOCKED);
					TLOG(LogSAS, Error, "AFT Error: %s", GetAFTTransferStatusCodesTxt(TSC_GM_NOT_LOCKED));
					goto crc72;
				}
				else
				{
					cA.CashableAmountCents = pInputPct->GetBCDAsUInt64(6, 5);
					cA.RestrictedAmountCents = pInputPct->GetBCDAsUInt64(11, 5);
					cA.NonRestrictedAmountCents = pInputPct->GetBCDAsUInt64(16, 5);
					pInputPct->GetInt(22, 4, &(cA.uAssetNumber));
					pInputPct->GetBuf(cA.cRegistrationKey, 26, 20);
					cA.cTransactionIDLength = pInputPct->GetByte(46);
					pInputPct->GetStr(47, cA.cTransactionIDLength, cA.sTransactionID);
					pInputPct->GetBCDAsStr(47 + cA.cTransactionIDLength, 4, cA.sExpiration);
					pInputPct->GetInt(51 + cA.cTransactionIDLength, 2, &(cA.shPoolID));
					cA.cReceiptDataLength = pInputPct->GetByte(53 + cA.cTransactionIDLength);
					// update transfer flag with actual values
					cA.UpdateTransferFlag(pSASChannel->SASAft.pAftTransferFlags->AsInteger());
					if (!pSASChannel->SASAft.TransferFundsResponse(cA))
					{
						TLOG(LogSAS, Error, "AFT Error: %s", GetAFTTransferStatusCodesTxt(cA.cTransferStatus));
					}
				}
				// Append response
				AppendChar(cA.cTransactionBufferPosition);
				AppendChar(cA.cTransferStatus);
				AppendChar(cA.cReceiptStatusCode);
				AppendChar(cA.cTransferType);
				AppendBCD(cA.CashableAmountCents, 5);
				AppendBCD(cA.RestrictedAmountCents, 5);
				AppendBCD(cA.NonRestrictedAmountCents, 5);
				AppendChar(cA.cTransferFlags);
				AppendInt(cA.uAssetNumber, 4);
				AppendChar(cA.cTransactionIDLength);
				AppendBuf((unsigned char*)cA.sTransactionID.c_str(), cA.cTransactionIDLength);
				AppendBCD(cA.sTransDate, 4);
				AppendBCD(cA.sTransTime, 3);
				AppendBCD(cA.sExpiration, 4);
				AppendInt(cA.shPoolID, 2);

				AppendChar(SAS_EXTENDED_METER_SIZE);
				AppendBCD(cA.CashableMeterCents, SAS_EXTENDED_METER_SIZE);

				AppendChar(SAS_EXTENDED_METER_SIZE);
				AppendBCD(cA.RestrictedMeterCents, SAS_EXTENDED_METER_SIZE);

				AppendChar(SAS_EXTENDED_METER_SIZE);
				AppendBCD(cA.NonrestrictedMeterCents, SAS_EXTENDED_METER_SIZE);

			crc72:
				// length
				SetByte(2, m_Len - 3);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));

				pSASChannel->SASAft.DoWorkImmediately();
			}
		}
		break;
			// ---------------- AFT register gaming machine -----------
		case 0x73: {
			unsigned char cRegCode;
			unsigned char uRegStatus = 0;
			unsigned int uAssetNumber;
			TString sRegistrationKey;

			// if (((CSas*)pSas)->AFTEnabled())
			if (pSASModule->ParamEqual("AFT_SUPPORTED", "1"))
			{
				AppendChar((unsigned char)cMachineAddr);    // address
				AppendChar((unsigned char)nCommand);    // command
				AppendChar((unsigned char)0x1D);    // length

				// TO DO before init, operator must enter asset_number and send exception 0x6C
				unsigned int uOPAssetNumber = 0;    // 1;

				// Registration status placeholder
				AppendChar(uRegStatus);    // Placeholder, will get assigned at the end of the switch

				// registration code
				cRegCode = pInputPct->GetByte(3);
				switch (cRegCode)
				{
					case AFT_REGCODE_INIT_REGISTRATION:
						uRegStatus = AFT_REGSTATUS_READY;
						// check if host AssetNumber = gm AssetNumber
						pInputPct->GetInt(4, 4, &uAssetNumber);
						pSASModule->ParamGet("AFT_ASSET_NUMBER", uOPAssetNumber);
						// g_pMirror->Get("aft_asset_number", &uOPAssetNumber);
						AppendInt(uOPAssetNumber, 4);

						pInputPct->GetBinAsStr(8, 20, sRegistrationKey);
						pSASModule->ParamSet("AFT_REGISTRATION_KEY", sRegistrationKey);

						break;
					case AFT_REGCODE_REGISTER_GM:
						uRegStatus = AFT_REGSTATUS_REGISTERED;

						pInputPct->GetInt(4, 4, &uAssetNumber);
						pSASModule->ParamSet("AFT_ASSET_NUMBER", std::to_string((int)uAssetNumber));
						AppendInt(uAssetNumber, 4);    // Asset Number     - 4 binary

						pInputPct->GetBinAsStr(8, 20, sRegistrationKey);
						pSASModule->ParamSet("AFT_REGISTRATION_KEY", sRegistrationKey);
						pSASModule->SASExceptionAdd(SAS_EXC_AFT_REGISTRATION_ACKNOWLEDGED);

						break;
					case AFT_REGCODE_REQEST_ACK:
						uRegStatus = AFT_REGSTATUS_PENDING;

						// pInputPct->GetInt(4, 4, &uAssetNumber);
						pSASModule->ParamGet("AFT_ASSET_NUMBER", uAssetNumber);
						AppendInt(uAssetNumber, 4);    // Asset Number     - 4 binary

						pInputPct->GetBinAsStr(8, 20, sRegistrationKey);
						pSASModule->ParamSet("AFT_REGISTRATION_KEY", sRegistrationKey);

						break;
						// omited data fields (asset number, registration key and POS ID)!!!
						// response with most recently received registration key ... -> from mirror
					case AFT_REGCODE_UNREGISTER_GM:
					case AFT_REGCODE_READ_CURRENT_REG:
						if (cRegCode == AFT_REGCODE_READ_CURRENT_REG)
						{
							unsigned int iRegStatus;
							pSASModule->ParamGet("AFT_REGISTRATION_STATUS", iRegStatus);
							uRegStatus = iRegStatus;
						}
						else    // AFT_REGCODE_UNREGISTER_GM
						{
							uRegStatus = AFT_REGSTATUS_NOT_REGISTERED;
							pSASModule->SASExceptionAdd(SAS_EXC_AFT_REGISTRATION_CANCELLED);
						}

						pSASModule->ParamGet("AFT_ASSET_NUMBER", uAssetNumber);
						AppendInt(uAssetNumber, 4);    // Asset Number     - 4 binary

						pSASModule->ParamGet("AFT_REGISTRATION_KEY", sRegistrationKey);
						if (sRegistrationKey.length() != 40)
						{
							pSASModule->ParamSet("AFT_REGISTRATION_KEY", TString("0000000000000000000000000000000000000000"));
							pSASModule->ParamGet("AFT_REGISTRATION_KEY", sRegistrationKey);
						}

						break;
				}

				// Registration status update
				pSASModule->ParamSet("AFT_REGISTRATION_STATUS", std::to_string(uRegStatus));
				m_Buf[3] = uRegStatus;

				AppendStrAsBin(sRegistrationKey, 40);    // Registration Key - 20 binary
				AppendChar(0, 4);    // POSID - 4 binary

				AppendShort(CcittCrc(m_Buf, m_Len, 0));    // CRC - 2 binary
			}
		}
		break;
			// ---------------- AFT Lock and Status Request Command ----------------
		case 0x74: {
			uint8_t cLockCode, cTransferCondition;
			uint8_t cAvailTrans = 0, cHostCashoutStatus = 0, cAFTStatus = 0;
			unsigned int uAssetNum, uPoolID;
			TString sLockTimeOut, sRestrictedExpiration;
			uint64_t CurrCashable, CurrRest, CurrNonRest, CurrTransLimit;

			if (pSASChannel && pSASModule->ParamEqual("AFT_SUPPORTED", "1"))
			{
				// address
				AppendChar((unsigned char)cMachineAddr);
				// command
				AppendChar((unsigned char)nCommand);
				// append length
				AppendChar(0x23);
				// read packet
				cLockCode = pInputPct->GetByte(2);
				cTransferCondition = pInputPct->GetByte(3);

				// lock request
				if (cLockCode == AFT_LOCK_REQUEST_LOCK)
				{
					const uint32_t lockTimeout = pInputPct->GetBCDAsInt(4, 2);    // v stotinkah sekunde po SAS-u
					pSASChannel->SASAft.LockRequest(cTransferCondition, lockTimeout * 10);
				}

				// cancel lock request
				if (cLockCode == AFT_LOCK_REQUEST_CANCEL_LOCK)
					pSASChannel->SASAft.LockCancel();

				// --- Response ---
				// asset num
				pSASModule->ParamGet("AFT_ASSET_NUMBER", uAssetNum);
				AppendInt(uAssetNum, 4);
				// lock status
				AppendChar(pSASChannel->SASAft.GetLockStatus());
				if (pSASChannel->SASAft.IsAFTInHouseEnabled())
				{
					// --- Available transfers ---
					for (ETransferCondition condition : ETransferCondition::_values())
					{
						const ETransferConditionState state = pSASModule->GetTransferCondition(condition);
						if (state == ETransferConditionState::OK)
						{
							// because according to the SAS book, there is some magic extra condition (win amount pending cashout to host) between bonus to GM and the
							// previous ones
							if (condition >= ETransferCondition::BonusAwardToMachine)
								cAvailTrans = cAvailTrans | (1 << (condition._to_integral() + 1));
							else
								cAvailTrans = cAvailTrans | (1 << condition._to_integral());
						}
					}

					// --- Host Cashout Status ---
					// Cashout to host enabled
					cHostCashoutStatus = cHostCashoutStatus | 1;
					cHostCashoutStatus = cHostCashoutStatus | 2;

					// --- AFT Status ---
					// AFT registered
					if (pSASModule->ParamEqual("AFT_REGISTRATION_STATUS", std::to_string(AFT_REGSTATUS_REGISTERED)))
						cAFTStatus = cAFTStatus | 8;
					// In house transfers
					if (pSASChannel->SASAft.IsAFTInHouseEnabled())
						cAFTStatus = cAFTStatus | 16;
					// Bonus awards
					if (pSASChannel->SASAft.IsAFTBonusEnabled())
						cAFTStatus = cAFTStatus | 32;
					// Any aft enabled
					if (pSASChannel->SASAft.IsAFTInHouseEnabled() || pSASChannel->SASAft.IsAFTBonusEnabled())
						cAFTStatus = cAFTStatus | 128;
				}
				AppendChar(cAvailTrans);
				AppendChar(cHostCashoutStatus);
				AppendChar(cAFTStatus);
				// Trans buff len
				AppendChar(AFT_TRANSACTIONS_HISTORY_BUFF_LEN);
				// Current cashable amount
				pSASChannel->SASAft.GetCurrentAmounts(CurrCashable, CurrRest, CurrNonRest, CurrTransLimit);
				AppendBCD(CurrCashable, 5);
				AppendBCD(CurrRest, 5);
				AppendBCD(CurrNonRest, 5);
				AppendBCD(CurrTransLimit, 5);
				// Restricted expiration
				pSASModule->ParamGet("PREV_RESTRICTED_EXPIRATION", sRestrictedExpiration);
				if (sRestrictedExpiration.empty())
					AppendBCD(0, 4);
				else
					AppendBCD(sRestrictedExpiration, 4);
				// Restricted pool ID
				if (!pSASModule->ParamGet("PREV_POOL_ID", uPoolID))
					uPoolID = 0;
				AppendInt(uPoolID, 2);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));    // CRC - 2 binary
			}
		}
		break;
#ifdef SAS_TICKETING
			// ---------------- Extended validation status ----------------
		case 0x7B: {
			unsigned char /*UNUSED cControlLSB, cControlMSB,*/ cStatusLSB, cStatusMSB, nLen, cAddr;
			unsigned int uAssetNumber, uCashableExpiration, uRestrictedExpiration;
			TString sTicketExpiration, sRestrictedExpiration, sStr;

			cAddr = pInputPct->GetByte(0);
			nLen = pInputPct->GetByte(2);
			// UNUSED cControlLSB = pInputPct->GetByte(3);
			// UNUSED cControlMSB = pInputPct->GetByte(4);
			cStatusLSB = pInputPct->GetByte(5);
			cStatusMSB = pInputPct->GetByte(6);

			// cashable and receipt ticket expiration
			if (nLen > 4)
				if (pInputPct->GetBCDAsStr(7, 2, sTicketExpiration))
					if (yutils::strToUInt2(sTicketExpiration, uCashableExpiration) && (uCashableExpiration != 0))
						pSASChannel->SASTicketing.SetExpiration(VAL_CASHABLE_TICKET, uCashableExpiration, 0x7B);
			// restricted ticket default expiration
			if (nLen > 6)
				if (pInputPct->GetBCDAsStr(9, 2, sRestrictedExpiration))
					if (yutils::strToUInt2(sRestrictedExpiration, uRestrictedExpiration) && (uRestrictedExpiration != 0))
						pSASChannel->SASTicketing.SetExpiration(VAL_RESTRICTED_PROMOTIONAL_TICKET, uRestrictedExpiration, 0x7B);

			// -------------------- build response -----------------------
			cStatusLSB = 0;
			cStatusMSB = 0;
			if (pSASChannel->SASTicketing.PrinterAvailable())
			{
				// use printer as cashout device
				cStatusLSB = cStatusLSB | 1;
				// use printer as handpay receipt device
				if (pSASChannel->SASTicketing.PrintHandPayReceipt())
					cStatusLSB = cStatusLSB | 2;
				// validate handpays and handpay receipts
				if (pSASChannel->SASTicketing.ValidateHandPaysAndReceipts())
					cStatusLSB = cStatusLSB | 4;
				// print restricted tickets
				if (pSASChannel->SASTicketing.PrintPromoTickets())
					cStatusLSB = cStatusLSB | 8;
			}
			// use ticket redemption
			if (pSASChannel->SASTicketing.TicketInEnabled())
				cStatusLSB = cStatusLSB | 32;
			// validation configuration
			if (pSASChannel->SASTicketing.IsValidationConfigurationSet())
				cStatusMSB = cStatusMSB | 128;
			// build packet
			// if not a global broadcast
			if (cAddr != 0)
			{
				AppendChar((unsigned char)cMachineAddr);
				AppendChar((unsigned char)nCommand);
				AppendChar((unsigned char)0x0A);
				pSASModule->ParamGet("AFT_ASSET_NUMBER", uAssetNumber);    // prej bil samo  ASSET_NUM
				AppendInt(uAssetNumber, 4);
				AppendChar(cStatusLSB);
				AppendChar(cStatusMSB);
				AppendBCD(pSASChannel->SASTicketing.GetIntExpiration(VAL_CASHABLE_TICKET), 2);
				AppendBCD(pSASChannel->SASTicketing.GetIntExpiration(VAL_RESTRICTED_PROMOTIONAL_TICKET), 2);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));
			}
		}

		break;
			// ---------------- Extended validation status ----------------
		case 0x7C: {
			int nLen, nDataLen, nPos;
			unsigned int nStatusFlag = 0;
			unsigned char cDataCode;
			TString sData;

			nLen = pInputPct->GetByte(2);
			nPos = 3;
			while (nLen > 0)
			{
				// get one item
				cDataCode = pInputPct->GetByte(nPos++);
				nDataLen = pInputPct->GetByte(nPos++);
				pInputPct->GetStr(nPos, nDataLen, sData);
				nPos = nPos + nDataLen;
				nLen = nLen - nDataLen - 2;
				// if data len is zero, keep previous values
				if (nDataLen > 0)
				{
					switch (cDataCode)
					{
						case 0x00:
							nStatusFlag = 1;
							pSASModule->ParamSet("TICKET_LOCATION", sData, ParameterDomain::GLOBAL, false);
							break;
						case 0x01:
							nStatusFlag = 1;
							pSASModule->ParamSet("TICKET_ADDRESS_1", sData, ParameterDomain::GLOBAL, false);
							break;
						case 0x02:
							nStatusFlag = 1;
							pSASModule->ParamSet("TICKET_ADDRESS_2", sData, ParameterDomain::GLOBAL, false);
							break;
						case 0x10:
							nStatusFlag = 1;
							pSASModule->ParamSet("RESTRICTED_TICKET_TXT", sData, ParameterDomain::GLOBAL, false);
							break;
					}
				}
			}

			// if some data was sent
			if (pInputPct->GetByte(2) > 0)
				pSASModule->ParamSet("TICKET_DATA_STATUS_FLAG", nStatusFlag);
			// if not a global broadcast
			if (!pInputPct->IsLongPoll(FOR_ADDRESS_0, pSas))
			{
				// build response
				AppendChar((unsigned char)cMachineAddr);
				AppendChar((unsigned char)nCommand);
				pSASModule->ParamGet("TICKET_DATA_STATUS_FLAG", nStatusFlag);
				AppendChar(nStatusFlag);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));
			}
		}
		break;
			// ---------------- Redeem Ticket ----------------
		case 0x71:
			// if ticket in enabled
			if (pSASChannel->SASTicketing.TicketInEnabled())
			{
				unsigned char cTransferCode, cLen, cParsingCode;
				TString sAmount, sValidation, sRestrictedExpiration;
				unsigned int uPoolID = 0, uResExp;

				cLen = pInputPct->GetByte(2);
				cTransferCode = pInputPct->GetByte(3);
				AppendChar((unsigned char)cMachineAddr);
				AppendChar((unsigned char)nCommand);
				// request for gaming machine status code
				if (cTransferCode == TTC_REQUEST_FOR_CURRENT_TICKET_STATUS)
				{
					// if a request for a completion status was sent after 0x68
					if (pSASChannel->SASTicketing.m_cRedemptionStatus == REDEMPTION_WAITING_FOR_COMPLETION_STATUS_POLL)
					{
						pSASChannel->SASTicketing.m_cRedemptionStatus = REDEMPTION_COMPLETED;
						// stop the ticket transfer complete timer
						pSASChannel->SASTicketing.m_tTicketTransferCompleteSent.Reset();
					}

					// if status code other than 40 sent, cancel the reissue of exception 68
					if (pSASChannel->SASTicketing.m_cGMStatusCode != GMSC_TICKET_REDEMPTION_PENDING)
						pSASChannel->SASTicketing.m_tTicketTransferCompleteSent.Reset();

					// build packet
					// if there has been a previous redemption cycle, return current redemption data
					if (pSASChannel->SASTicketing.m_sRedemptionValidation != "000000000000000000")
					{
						// number of bytes following
						AppendChar(16);
						AppendChar(pSASChannel->SASTicketing.m_cGMStatusCode);
						// transfer amount
						AppendBCD(pSASChannel->SASTicketing.m_sRedemptionAmount, 5);
						// parsing code
						AppendChar(0);
						// validation
						AppendBCD(pSASChannel->SASTicketing.m_sRedemptionValidation, 9);
					}
					else
					// return just the status
					{
						// number of bytes following
						AppendChar(1);
						AppendChar(pSASChannel->SASTicketing.m_cGMStatusCode);
					}
					AppendShort(CcittCrc(m_Buf, m_Len, 0));
				}
				else
				{
					// amount
					pInputPct->GetBCDAsStr(4, 5, sAmount);
					cParsingCode = pInputPct->GetByte(9);
					if (cParsingCode == 0)
						// validation
						pInputPct->GetBCDAsStr(10, 9, sValidation);
					// Restricted Expiration
					if (cLen >= 20)
					{
						pInputPct->GetBCDAsStr(19, 4, sRestrictedExpiration);
						yutils::strToUInt2(sRestrictedExpiration, uResExp);
						// if restricted expiration is "00000000" the default value must be used ("")
						if (uResExp == 0)
							sRestrictedExpiration = "";
					}
					// Pool ID
					if (cLen >= 22)
						pInputPct->GetInt(23, 2, &uPoolID);

					// not reading Target ID and Target ID Length - not supported

					// if host sends another 0x71 with different data (during the same redemption cycle), send 0xC0
					if ((pSASChannel->SASTicketing.m_cRedemptionStatus > REDEMPTION_070_RECEIVED) &&
					    ((pSASChannel->SASTicketing.m_sRedemptionAmount != sAmount) || (pSASChannel->SASTicketing.m_sRedemptionValidation != sValidation)))
					{
						// build packet
						AppendChar(1);
						// machine status code 0xC0
						AppendChar(GMSC_NOT_COMPATIBLE_WITH_CURRENT_REDEMPTION_CYCLE);
						AppendShort(CcittCrc(m_Buf, m_Len, 0));
						break;
					}
					// call before packet is build, in case m_cGMStatusCode is changed
					pSASChannel->SASTicketing.TicketInAfter071(sValidation, sAmount, cTransferCode, sRestrictedExpiration, uPoolID);
					// build reedem ticket response
					// number of bytes following
					AppendChar(16);
					AppendChar(pSASChannel->SASTicketing.m_cGMStatusCode);
					// transfer amount
					AppendBCD(pSASChannel->SASTicketing.m_sRedemptionAmount, 5);
					// parsing code
					AppendChar(0);
					// validation
					AppendBCD(pSASChannel->SASTicketing.m_sRedemptionValidation, 9);
					AppendShort(CcittCrc(m_Buf, m_Len, 0));

					pSASChannel->SASTicketing.m_cRedemptionStatus = REDEMPTION_071_SENT;
				}
				// if status code 40 sent
				if (pSASChannel->SASTicketing.m_cGMStatusCode == GMSC_TICKET_REDEMPTION_PENDING)
				{
					pSASChannel->SASTicketing.m_cRedemptionStatus = REDEMPTION_071_40_SENT;
				}
			}
			break;
			// ---------------- Set ticket data ----------------
		case 0x7D: {
			unsigned int nHostID, nLength, nStatusFlag = 0;
			unsigned char cExpiration, cLocationLen, cAddr1Len, cAddr2Len;
			TString sLocation, sAddr1, sAddr2;

			nLength = pInputPct->GetByte(2);
			if (nLength > 0)
			{
				nStatusFlag = 1;
				pInputPct->GetInt(3, 2, &nHostID);
				nLength -= 2;
				pSASModule->ParamSet("HOST_ID", nHostID);
				if (nLength > 0)
				{
					// expiration
					cExpiration = pInputPct->GetByte(5);
					nLength -= 1;
					pSASChannel->SASTicketing.SetExpiration(VAL_CASHABLE_TICKET, (int)cExpiration, 0x7D);
					// Location
					if (nLength > 0)
					{
						cLocationLen = pInputPct->GetByte(6);
						nLength -= 1;
						if (cLocationLen > 0)
						{
							pInputPct->GetStr(7, cLocationLen, sLocation);
							nLength -= cLocationLen;
							pSASModule->ParamSet("TICKET_LOCATION", sLocation, ParameterDomain::GLOBAL, false);
							nStatusFlag = 1;
						}
						// Address 1
						if (nLength > 0)
						{
							cAddr1Len = pInputPct->GetByte(7 + cLocationLen);
							nLength -= 1;
							if (cAddr1Len > 0)
							{
								pInputPct->GetStr(8 + cLocationLen, cAddr1Len, sAddr1);
								nLength -= cAddr1Len;
								pSASModule->ParamSet("TICKET_ADDRESS_1", sAddr1, ParameterDomain::GLOBAL, false);
								nStatusFlag = 1;
							}

							// Address 2
							if (nLength > 0)
							{
								cAddr2Len = pInputPct->GetByte(8 + cLocationLen + cAddr1Len);
								nLength -= 1;
								if (cAddr2Len > 0)
								{
									pInputPct->GetStr(9 + cLocationLen + cAddr1Len, cAddr2Len, sAddr2);
									nLength -= cAddr2Len;
									pSASModule->ParamSet("TICKET_ADDRESS_2", sAddr2, ParameterDomain::GLOBAL, false);
									nStatusFlag = 1;
								}
							}
						}
					}
				}
				pSASModule->ParamSet("TICKET_DATA_STATUS_FLAG", nStatusFlag);
			}

			// if not a global broadcast
			if (!pInputPct->IsLongPoll(FOR_ADDRESS_0, pSas))
			{
				unsigned int nDataFlag = 0;
				AppendChar((unsigned char)cMachineAddr);
				AppendChar((unsigned char)nCommand);
				pSASModule->ParamGet("TICKET_DATA_STATUS_FLAG", nDataFlag);
				AppendChar(nDataFlag);
				AppendShort(CcittCrc(m_Buf, m_Len, 0));
				// reset ticket data status flag
				pSASModule->ParamSet("TICKET_DATA_STATUS_FLAG", 0);
			}
		}
		break;
#endif
		// ---------------- Set Date and time ----------------
		case 0x7F: {
			TString sDate, sTime, sCmd;

			pInputPct->GetBCDAsStr(2, 4, sDate);
			pInputPct->GetBCDAsStr(6, 3, sTime);

			pSASModule->SetTime(sDate, sTime);
			// if not a global broadcast we have to answer
			if (pInputPct->GetByte(0) != 0)
			{
				Build(SAS_PCT_ACK, pSASChannel);
			}
		}
		break;
		// ---------------- Remote Handpay Reset ----------------
		case 0x94: {
			const uint8_t cResponseStatus = pSASModule->RemoteConfirmHandpay();

			AppendChar(cMachineAddr);
			AppendChar(nCommand);
			AppendChar(cResponseStatus);
			AppendShort(CcittCrc(m_Buf, m_Len, 0));
		}
		break;
		// ---------------- Enable/disable game auto rebet ----------------
		case 0xAA: {
			const bool enabled = pInputPct->GetByte(2);
			TLOG(LogSAS, Normal, "Enable/disable games auto rebet: %s", (enabled ? "enable" : "disable"));

			pSASModule->SetAutoRebet(enabled);

			Build(SAS_PCT_ACK, pSASChannel);
		}
		break;
		// ---------------- Set Machine Numbers ----------------
		case 0xB7: {
			unsigned int uLength, uNewAssetNumber, uFloorLocLength = 0;
			unsigned int uAssetNumber = 0;    // 1;
			TString sFloorLocation = "", sNewFloorLocation = "";

			pSASModule->ParamGet("AFT_ASSET_NUMBER", uAssetNumber);
			// not supported for now
			// pSASModule->ParamGet("FLOOR_LOCATION", sFloorLocation);

			uLength = pInputPct->GetByte(2);
			pInputPct->GetInt(3, 4, &uNewAssetNumber);
			if (uNewAssetNumber != 0)
			{
				// not supporting for now
				// pSASModule->ParamSet("AFT_ASSET_NUMBER", std::to_string(uNewAssetNumber), ParameterDomain::MINE, false);
				uAssetNumber = uNewAssetNumber;
			}

			uFloorLocLength = pInputPct->GetByte(7);
			if (uFloorLocLength > 0 && uLength > 5)
			{
				pInputPct->GetStr(8, uFloorLocLength, sNewFloorLocation);
				// not supported for now
				// pSASModule->ParamSet("FLOOR_LOCATION", sNewFloorLocation, ParameterDomain::MINE, false);
				sFloorLocation = sNewFloorLocation;
			}

			AppendChar(cMachineAddr);
			// length
			AppendChar(6 + sFloorLocation.length());
			// control flags - not supported for now
			AppendChar(0);
			// asset number
			AppendInt(uAssetNumber, 4);
			// floor location length
			AppendChar(sFloorLocation.length());
			// floor location
			if (sFloorLocation.length() > 0)
				AppendStr(sFloorLocation, sFloorLocation.length());
			AppendShort(CcittCrc(m_Buf, m_Len, 0));
		}
		break;
		case 0xC0: {
			if (pSASModule->SASType != magic::SAS_6_0_3 || !pSASChannel->m_bRealTimeEventReport)
			{
				Build(SAS_PCT_NACK, pSASChannel);
				break;
			}

			const int enabled = pInputPct->GetByte(2);
			pSASModule->SetAsynchronousGamePlay(enabled);

			Build(SAS_PCT_ACK, pSASChannel);
		}
		break;
		case 0xC1: {
			// address
			AppendChar(cMachineAddr);
			// command
			AppendChar(nCommand);
			// length
			AppendChar(3);
			// game lock status
			const uint8_t cLockStatus = pSASModule->GetLockStatus();
			AppendChar(cLockStatus);
			// bill validator status
			const uint8_t cBillAcceptorStatus = pSASModule->GetBillAcceptorStatus();
			AppendChar(cBillAcceptorStatus);

			// games in play status
			uint8_t playing = 0;
			// if one with value 1 or greater, means ih has game started but not ended
			auto foundInProgress = std::find_if(mActiveGames.begin(), mActiveGames.end(), [](const auto& item) -> bool { return item.GameStartSequence > 0; });
			if (foundInProgress != mActiveGames.end())
				playing = playing | 1;
			// if one with value 0 exists, means it has game ended but not yet result
			auto foundWaitingResolution = std::find_if(mActiveGames.begin(), mActiveGames.end(), [](const auto& item) -> bool { return item.GameStartSequence == 0; });
			if (foundWaitingResolution != mActiveGames.end())
				playing = playing | (1 << 2);
		}
		break;
		case 0xC2: {
			int countInProgress = std::count_if(mActiveGames.begin(), mActiveGames.end(), [](const auto& item) { return item.GameStartSequence > 0; });
			int countWaitingResolution = std::count_if(mActiveGames.begin(), mActiveGames.end(), [](const auto& item) { return item.GameStartSequence == 0; });

			// address
			AppendChar(cMachineAddr);
			// command
			AppendChar(nCommand);
			// length
			AppendChar(2 + (3 * countInProgress) + (3 * countWaitingResolution));
			// number of games in progress
			AppendChar(countInProgress);
			for (const auto item : mActiveGames)
			{
				if (item.GameStartSequence > 0)
				{
					// game number
					AppendBCD(item.GameNumber, 2);
					// game sequence number
					AppendChar(item.GameCycleSequence);
				}
			}
			// number of games waiting resolution
			AppendChar(countWaitingResolution);
			for (const auto item : mActiveGames)
			{
				if (item.GameStartSequence == 0)
				{
					// game number
					AppendBCD(item.GameNumber, 2);
					// game sequence number
					AppendChar(item.GameCycleSequence);
				}
			}
		}
		break;
		default: break;
	}

	if (m_Len < 1)
		Clear();
	return true;
}


/* Clears the packet and builds a general poll response packet */
int TSASPoll::BuildGeneralPollResponse(unsigned char bException, void* pSas)
{
	Clear();
	return AppendChar(bException);
}


/* Clears the packet and builds a general poll response packet */
int TSASPoll::BuildRealTimeEventResponse(const ExceptionEntry& entry, void* pSas)
{
	unsigned char cMachineAddr;
	TSASChannel* pSASChannel = NULL;

	pSASChannel = (TSASChannel*)pSas;
	;
	cMachineAddr = pSASChannel->SASChannelConf.cSASAddr;
	Clear();

	AppendChar((unsigned char)cMachineAddr);
	AppendChar((unsigned char)0xFF);
	AppendChar((unsigned char)entry.code);

	TString sExtra = entry.extra;

	switch (entry.code)
	{
			// ---------------- Bill Accepted ----------------
			/* case 0x4F:
			   if (pBillAcceptor)
			   {
			     unsigned int     nCountryCode, nBillDenominationCode;
			     unsigned __int64 nBillValue, nBillCount;
			     CStr             str;

			     // Country code
			     nCountryCode = GetSasCountryCode((g_Currency.GetSymName()).c_str());

			     AppendBCD(nCountryCode, 1);
			     // Bill Denomination
			     nBillValue = pBillAcceptor->GetBillValue();
			     nBillDenominationCode = GetBillDenominationCode(nBillValue);
			     AppendBCD(nBillDenominationCode, 1);

			     // Number of accepted bills of this type
			     nBillCount = 0;
			     if (nBillValue > 0)
			     {
			       str.Format("total bills %d", nBillValue);
			       g_pMirror->Get(str, &nBillCount);
			     }
			     AppendBCD(nBillCount, 4);
			   }
			   else
			     Clear();
			   break;
			 */
		// ---------------- Game Start ----------------
		case SAS_EXC_GAME_HAS_STARTED: {
			TString sItem;
			uint64_t uCreditsWagered = 0;
			uint64_t uTotalCoinIn = 0;
			unsigned int uWagerType = 0;
			unsigned char cWagerType = 0;

			if (pSASModule->SASType == magic::SAS_6_0_3 && pSASModule->GetAsynchronousGamePlay() > 0)
			{
				unsigned int uGameNumber = 0;
				unsigned int uGameCycleSequence = 0;

				if (!sExtra.empty())
				{
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameNumber);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameCycleSequence);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uCreditsWagered);
					if (sExtra.Subtract("#", &sItem))
					{
						yutils::strToUInt2(sItem, uWagerType);
						cWagerType = (unsigned char)uWagerType;
					}
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uTotalCoinIn);
				}

				// length
				AppendChar(26);
				// game number
				AppendBCD(uGameNumber, 2);
				// game cycle sequence
				AppendChar(uGameCycleSequence);
				// game start sequence
				auto it = std::find_if(mActiveGames.begin(), mActiveGames.end(),
				                       [&](const auto& item) -> bool { return item.GameNumber == uGameNumber && item.GameCycleSequence == uGameCycleSequence; });

				if (it == mActiveGames.end())
				{
					ActiveGame game;
					game.GameNumber = uGameNumber;
					game.GameCycleSequence = uGameCycleSequence;
					game.GameStartSequence = 1;
					mActiveGames.push_back(game);

					AppendChar(game.GameStartSequence);
				}
				else
				{
					it->GameStartSequence++;
					AppendChar(it->GameStartSequence);
				}

				// credits wagered final
				AppendBCD(uCreditsWagered, 5);
				// credits wagered informative
				AppendBCD(uCreditsWagered, 5);
				// wager type
				AppendChar(cWagerType);
				// progressive group
				AppendChar(0);
				// total coin in meter size
				AppendChar(9);
				// total coin in meter
				AppendBCD(uTotalCoinIn, 9);
			}
			else
			{
				// get extra data for the exception
				if (!sExtra.empty())
				{
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uCreditsWagered);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uTotalCoinIn);
					if (sExtra.Subtract("#", &sItem))
					{
						yutils::strToUInt2(sItem, uWagerType);
						cWagerType = (unsigned char)uWagerType;
					}
				}
				// coins waggered for the current game
				// in units of game denomination
				AppendBCD(uCreditsWagered, 2);
				// Coin in meter (total in including wagger)
				AppendBCD(uTotalCoinIn, 4);
				// wager type
				AppendChar(cWagerType);
				// progressive group for this game (only if this game is SAS progressive)
				AppendChar(0);
			}
		}
		break;
		// ---------------- Game End ----------------
		case SAS_EXC_GAME_HAS_ENDED: {
			uint64_t uWin = 0;
			TString sItem;
			if (pSASModule->SASType == magic::SAS_6_0_3 && pSASModule->GetAsynchronousGamePlay() > 0)
			{
				unsigned int uGameNumber = 0;
				unsigned int uGameCycleSequence = 0;
				uint64_t uCreditsWagered = 0;
				uint64_t uCurrentCredits = 0;
				uint64_t uTotalCoinOut = 0;
				unsigned int uWagerType = 0;
				unsigned char cWagerType = 0;

				if (!sExtra.empty())
				{
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameNumber);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameCycleSequence);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uCreditsWagered);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uWin);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uCurrentCredits);
					if (sExtra.Subtract("#", &sItem))
					{
						yutils::strToUInt2(sItem, uWagerType);
						cWagerType = (unsigned char)uWagerType;
					}
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uTotalCoinOut);
				}

				auto it = std::find_if(mActiveGames.begin(), mActiveGames.end(),
				                       [&](const auto& item) -> bool { return item.GameNumber == uGameNumber && item.GameCycleSequence == uGameCycleSequence; });
				if (it != mActiveGames.end())
				{
					it->GameStartSequence = 0;
				}
				else
				{
					// error - we didn't get game start before game end!!!
				}

				// length
				AppendChar(35);
				// game number
				AppendBCD(uGameNumber, 2);
				// game cycle sequence
				AppendChar(uGameCycleSequence);
				// bet credits wagered resolved
				AppendBCD(uCreditsWagered, 5);
				// bet credits wagered transferred
				AppendBCD(0, 5);
				// game win
				AppendBCD(uWin, 5);
				// current credits
				AppendBCD(uCurrentCredits, 5);
				// wager type
				AppendChar(cWagerType);
				// status
				AppendChar(0);
				// total coin out meter size
				AppendChar(9);
				// total coin in meter
				AppendBCD(uTotalCoinOut, 9);
			}
			else
			{
				// get extra data for the exception
				if (!sExtra.empty())
				{
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uWin);
				}
				// game win in units of credits
				AppendBCD(uWin, 4);
			}
		}
		break;
		// ---------------- Game End ----------------
		case SAS_EXC_REEL_N_HAS_STOPED: {
			unsigned int uWinNum = 0;
			TString sItem;
			if (pSASModule->SASType == magic::SAS_6_0_3 && pSASModule->GetAsynchronousGamePlay() > 0)
			{
				unsigned int uGameNumber = 0, uGameCycleSequence = 0;
				unsigned int uStatus = 0;
				bool bPlayerInvolved = true;

				if (!sExtra.empty())
				{
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameNumber);
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uGameCycleSequence);
				}

				auto it = std::find_if(mActiveGames.begin(), mActiveGames.end(),
				                       [&](const auto& item) -> bool { return item.GameNumber == uGameNumber && item.GameCycleSequence == uGameCycleSequence; });

				if (it != mActiveGames.end())
				{
					mActiveGames.erase(it);
					bPlayerInvolved = true;
				}
				else
				{
					// game start/end where not sent
					bPlayerInvolved = false;
				}

				// length
				AppendChar(5);
				// game number
				AppendBCD(uGameNumber, 2);
				// game cycle sequence
				AppendChar(uGameCycleSequence);
				// status
				if (!bPlayerInvolved)
					uStatus = uStatus | 1;
				AppendChar(uStatus);
				// result length
				AppendChar(0);
				// result
			}
			else
			{
				// get extra data for the exception
				if (!sExtra.empty())
				{
					// game Reel Number
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uWinNum);
					AppendChar((unsigned char)uWinNum);

					// game win number
					if (sExtra.Subtract("#", &sItem))
						yutils::strToUInt2(sItem, uWinNum);
					AppendChar((unsigned char)uWinNum);
				}
			}
		}
		break;
		// ---------------- Game Recall Entered ----------------
		case SAS_EXC_GAME_RECALL_HAS_BEEN_ENTERED: {
			// get extra data for the exception
			if (!sExtra.empty())
			{
				TString sItem;
				// game Number
				if (sExtra.Subtract("#", &sItem))
					AppendBCD(yutils::strToInt(sItem, 0), 2);

				// game index
				if (sExtra.Subtract("#", &sItem))
					AppendBCD(yutils::strToInt(sItem, 0), 2);
			}
		}
		break;
		case SAS_EXC_GAME_SELECTED: {
			// get extra data for the exception
			if (!sExtra.empty())
			{
				TString sItem;
				int gameN = 0;
				// game number
				if (sExtra.Subtract("#", &sItem))
					yutils::strToInt2(sItem, gameN);
				AppendBCD(gameN, 2);
			}
		}
	}

	if (m_Len > 1)
	{
		AppendShort(CcittCrc(m_Buf, m_Len, 0));
	}
	else
		Clear();
	return true;
}

void TSASPoll::Clear()
{
	for (int i = 0; i < SAS_MAX_PCT_LEN; i++) m_Buf[i] = 0;
	m_Len = 0;
	m_nParityPos = -1;
	m_tLastByteReceive.Stop();
}

/* Calculates a 16-bit CRC of a string */
unsigned short TSASPoll::CcittCrc(unsigned char* s, int len, unsigned short crcval)
{
	unsigned char c, q;
	for (; len; len--)
	{
		c = *s++;
		q = (crcval ^ c) & 017;
		crcval = (crcval >> 4) ^ (q * 010201);
		q = (crcval ^ (c >> 4)) & 017;
		crcval = (crcval >> 4) ^ (q * 010201);
	}
	return crcval;
}


/* Get byte of the packet, first has index 0 */
/* If there is no such one return -1         */
int TSASPoll::GetByte(int n)
{
	if (m_Len < n)
		return -1;
	return m_Buf[n];
}

/* Get buf from packet               */
/* If there is no such one return -1 */
int TSASPoll::GetBuf(char* pBuf, int nStart, int nCount)
{
	int i;

	if (m_Len < nStart + nCount)
		return -1;
	for (i = 0; i < nCount; i++) pBuf[i] = m_Buf[nStart + i];
	return true;
}

/* Get byte of the packet, first has index 0 */
/* If there is no such one return -1         */
int TSASPoll::SetByte(int n, unsigned char cVal)
{
	if (m_Len < n)
		return false;
	m_Buf[n] = cVal;
	return true;
}

/* Get byte of the packet, first has index 0 */
/* If there is no such one return -1         */
int TSASPoll::GetBCDAsStr(int nStart, int nLen, TString& sResult)
{
	sResult.clear();
	if (m_Len < nStart + nLen)
		return false;

	sResult.reserve(nLen * 2);

	for (int i = nStart; i < nStart + nLen; i++)
	{
		sResult.push_back((m_Buf[i] >> 4) + '0');
		sResult.push_back((m_Buf[i] & 0b1111) + '0');
	}
	return true;
}

int TSASPoll::GetBCDAsInt(int nStart, int nLen)
{
	if (m_Len < nStart + nLen)
		return 0;

	int value = 0;
	for (int i = nStart; i < nStart + nLen; i++)
	{
		value *= 100;
		uint8_t byte = m_Buf[i];
		value += (byte >> 4) * 10 + (byte & 0b1111);
	}
	return value;
}

uint64_t TSASPoll::GetBCDAsUInt64(int nStart, int nLen)
{
	return ::GetBCDAsUInt64((const char*)m_Buf + nStart, nLen);
}


/* Get CStr from packet */
int TSASPoll::GetStr(int nStart, int nLen, TString& sResult)
{
	sResult.clear();
	if (m_Len < nStart + nLen)
		return false;

	sResult += std::string((const char*)m_Buf + nStart, (size_t)nLen);
	return true;
}

/* Get Integer in packet */
/* If there is no integer return false */
/* ALL Binary data is LSB (less significant byte first */
int TSASPoll::GetInt(int nStart, int nLen, unsigned int* nResult)
{
	int i;

	*nResult = 0;
	if ((m_Len < nStart + nLen) || (nLen > 4))
		return false;

	for (i = nStart + nLen - 1; i >= nStart; i--)
	{
		*nResult = *nResult * 256;
		*nResult = *nResult + m_Buf[i];
	}
	return true;
}

/* Returns the packet type of a long poll  */
/* If there is no packet type returns ' '  */
char TSASPoll::GetType(void* pSas)
{
	char cRetVal = ' ';
	int nCommand;

	if (IsLongPoll(FOR_ANY_CLIENT, pSas))
	{
		if ((nCommand = GetByte(1)) != -1)
			cRetVal = locLongPolls[nCommand].cType;
	}

	return cRetVal;
}

/* Returns true if the packet is a global broadcast */
/* A long poll with address 0                       */
/* Otherwise returns false  */
int TSASPoll::IsGlobalBroadcast()
{
	// it has to be implemented
	// if ((GetByte(0) == 0) && (m_Len > 2))
	//  return true;

	return false;
}


/* Returns true if the packet is 0x80, a sync */
/* Otherwise returns false                    */
int TSASPoll::IsSync80()
{
	if ((GetByte(0) == 0x80) && (m_Len == 1))
		return true;

	return false;
}

/* Returns true if packet is recognized as valid and length complete SAS packet */
/* NOTE: CRC is not checked */
/* Otherwise returns false  */
int TSASPoll::IsComplete(void* pSas)
{
	unsigned char bCommand;

	if (IsGeneralPoll(FOR_ANY_CLIENT, pSas) || IsSync80())
	{
		// general poll packets have length 1
		if (m_Len == 1)
			return true;
	}
	else if (IsLongPoll(FOR_ANY_CLIENT, pSas))
	{
		// we have the command byte
		if (m_Len >= 2)
		{
			if (GetByte(1) != -1)
			{
				bCommand = GetByte(1);
				// check packet length
				if (locLongPolls[bCommand].nPctLen == m_Len)
					return true;
				// packet length is the 3rd packet byte, not including CRC (2) and header (3)
				if (locLongPolls[bCommand].nPctLen == -1 && m_Len > 2)
					if ((GetByte(2) + 3 + 2) == m_Len)
						return true;
			}
		}
	}

	return false;
}


/* Returns true if the packet is a general poll */
/* If packet is not generall poll return false  */
int TSASPoll::IsGeneralPoll(int nTarget, void* pSas)
{
	int nFirstByte;
	unsigned char cMachineAddr;

	cMachineAddr = ((TSASChannel*)pSas)->SASChannelConf.cSASAddr;

	if ((nFirstByte = GetByte(0)) == -1)
		return false;

	// sync is not considered as a Generall Poll
	if (IsSync80())
		return false;

	// first bit must be set
	if ((m_Len == 1) && (nFirstByte & 0x80) == 0x80)
	{
		// For any client
		if (nTarget == FOR_ANY_CLIENT)
		{
			return true;
		}
		// this client
		else if (nTarget == FOR_ME)
		{
			// is gaming machine address ored with x80
			if (nFirstByte == (cMachineAddr | 0x80))
				return true;
		}
		// other client
		else if (nTarget == FOR_OTHER_CLIENT)
		{
			// adreess is not gaming machine address ored with x80
			if (nFirstByte != (cMachineAddr | 0x80))
				return true;
		}
	}

	return false;
}

/* Returns true if the packet is a long poll */
/* If packet is not a long poll return false  */
int TSASPoll::IsLongPoll(int nTarget, void* pSas)
{
	int nFirstByte;
	unsigned char cMachineAddr;

	cMachineAddr = ((TSASChannel*)pSas)->SASChannelConf.cSASAddr;

	if ((nFirstByte = GetByte(0)) == -1)
		return false;

	// len must be at least 2
	if (m_Len <= 1)
		return false;

	// first bit must be 0
	if ((nFirstByte & 0x80) == 0)
	{
		// Global broadcast
		if ((nTarget == FOR_ADDRESS_0) && (nFirstByte == 0))
		{
			return true;
		}
		// For any client
		if (nTarget == FOR_ANY_CLIENT)
		{
			return true;
		}
		// for this client
		else if (nTarget == FOR_ME)
		{
			// if first byte is gaming machine address
			if (nFirstByte == cMachineAddr)
				return true;
		}
		// for other client
		else if (nTarget == FOR_OTHER_CLIENT)
		{
			// if first byte is not gaming machine address
			if (nFirstByte != cMachineAddr)
				return true;
		}
	}

	return false;
}


/* Prints packet data in hex format to screen */
void TSASPoll::LogPoll()
{
	char* sPack;

	sPack = GetAsStr();

	TLOG(LogSAS, Normal, "Poll content: %s", sPack);
}

/* Prints packet data in hex format to CStr */
char* TSASPoll::GetAsStr()
{
	static char sRes[SAS_MAX_PCT_LEN * 6];
	char sTemp[64];

	strcpy(sRes, "");
	for (int i = 0; i < m_Len; i++)
	{
		if (m_nParityPos == i)
			sprintf(sTemp, "|%x ", m_Buf[i]);
		else
			sprintf(sTemp, "%x ", m_Buf[i]);
		strcat(sRes, sTemp);
	}
	return sRes;
}


/* Checks if the packet's CRC is OK */
/* Supposing the last two bytes in packet are CRC */
/* Returns false if CRC mismatch or packet len is not correct */
int TSASPoll::IsCrcOK()
{
	unsigned short usCalculatedCrc, usPacketCrc;

	if (m_Len < 2)
		return false;

	usCalculatedCrc = CcittCrc(m_Buf, m_Len - 2, 0);
	usPacketCrc = m_Buf[m_Len - 2] + (m_Buf[m_Len - 1] << 8);
	if (usCalculatedCrc != usPacketCrc)
		return false;
	return true;
}


/* Build ACK or NACK packet */
void TSASPoll::Build(int nType, TSASChannel* pSas)
{
	unsigned char cMachineAddr = pSas->SASChannelConf.cSASAddr;

	Clear();
	if (nType == SAS_PCT_ACK)
		AppendChar((unsigned char)cMachineAddr);

	if (nType == SAS_PCT_NACK)
		AppendChar((unsigned char)(cMachineAddr | 0x80));
}

/* Converts nValue to nBufLen length BCD MSB first */
/* if not sucsesful returns false */
void TSASPoll::ToBCD(uint64_t nValue, int BCDLen, std::vector<uint8_t>& outBuf)
{
	// clear buffer
	outBuf = std::vector<uint8_t>(BCDLen, 0);

	// negative valus can not be converted to BCD !!! Dan: This is always false
	// if (nValue < 0)
	//  return false;

	for (int idxFromBack = 0; idxFromBack < BCDLen; idxFromBack++)
	{
		int twoDigits = nValue % 100;
		nValue /= 100;

		outBuf[BCDLen - 1 - idxFromBack] = ((twoDigits / 10) << 4) + twoDigits % 10;
	}
}
void TSASPoll::Init()
{
	m_Len = 0;
	m_nParityPos = -1;

	pSASModule = (TSASModule*)pApp->GetModuleByName("SAS1");
	if (!pSASModule)
		return;
}
