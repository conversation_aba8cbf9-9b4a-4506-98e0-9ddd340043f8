#include "drv/system/SAS/TSASExceptions.h"

#include "Cryptography.h"
#include "YUtils.h"
#include "drv/system/SAS/TSASModule.h"

/* Initialize exceptions object */
TSASExceptions::TSASExceptions()
{
	pSASModule = pApp->GetModuleByName<TSASModule>("SAS1");
	if (!pSASModule)
		throw std::runtime_error("Cannot get SAS module!");

	TString excString;
	if (pSASModule->_ExceptionsLoad(excString))
	{
		ExcToArray(excString);
	}

	m_cLastExcWinNum = UNDEF_WIN_NUM;
	// priority exceptions are written as 2 char hex entries in a string, as for example: 5700, where 57 stands for exception code, 00 for exception status
	// they are listed in a priority order (first with highest priority)
	const std::set<unsigned char> priority_exceptions = { 0x57, 0x67, 0x68, 0x3F, 0x6A, 0x6B, 0x6F, 0x3D, 0x3E, 0x69, 0x6C, 0x6D, 0x51, 0x52, 0x70 };
	m_sPriorityExc.Lock();
	for (unsigned char exc : priority_exceptions) m_sPriorityExc->emplace(exc, false);
	m_sPriorityExc.Unlock();
}

void TSASExceptions::AddException(unsigned char bExcCode, const TString& sExtra, bool bToFront)
{
	ExceptionEntry entry;
	entry.code = bExcCode;
	entry.extra = sExtra;
	AddException(entry, bToFront);
}


void TSASExceptions::AddException(const ExceptionEntry& strExc, bool bToFront)
{
	if (strExc.code == SAS_EXC_NO_ACTIVITY)
		return;

	// printf("AddException()\n");
	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, LockMut(EXCEPTION CODE=%X, extra=%s, ToFront=%d)", bExcCode,
	// sExtra.c_str(), bToFront);

	// handle priority exceptions
	if (IsPriorityExc(strExc.code))
	{
		// pApp->WriteLog(LS_SAS|LT_DEBUG_ERROR, NO_GAME_ID, "TSASExceptions::AddException, IsPriorityExc");
		AddPriorityExc(strExc.code);
		// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, AddPriorityExc");
		return;
	}

	ScopedLock lock(mExceptionArray);

	// Get exceptions from mirror
	// if (g_pData->Get(m_sMirrorExcName, &sExceptions))
	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, _ExceptionsLoad");

	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, ExcToArray");
	ArrayAdd(strExc, bToFront);
	// pApp->WriteLog(LS_SAS|LT_DEBUG_ERROR, NO_GAME_ID, "TSASExceptions::AddException, ArrayAdd");

	// put exceptions to data
	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, ArrayToExc");
	pApp->DoAsyncTask(
	  [this]() {
		  TString sExceptions;
		  ArrayToExc(sExceptions);
		  pSASModule->_ExceptionsStore(sExceptions);
	  },
	  "sas-exception-save");
	//  pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, _ExceptionsStore");

	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::AddException, UnlockMut");
	// printf("AddException() END\n");
}

static unsigned char HexDigitToint(unsigned char ch)
{
	ch = tolower(ch);
	if (ch >= '0' && ch <= '9')
	{
		ch = ch - '0';
		return ch;
	}
	if (ch >= 'a' && ch <= 'f')
	{
		ch = ch - 'a' + 10;
		return ch;
	}
	return 0;
}

// Get two signs hex as unsigned char
unsigned char Get2HexAsChar(unsigned char c1, unsigned char c2)
{
	unsigned char ch1, ch2;

	ch1 = HexDigitToint(c1);
	ch2 = HexDigitToint(c2);
	return (unsigned char)(ch1 << 4) + ch2;
}


/* Get exception from queue        */
/* If not sucsesfull returns false */
/* If the queue is empty set bECode to EXC_NO_ACTIVITY */
bool TSASExceptions::GetException(ExceptionEntry* stExc, bool bRemoveFromQueue)
{
	TString sExceptions;
	int nWinNum;

	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, LockMut, (cMethod=%d)", cMethod);

	// handle priority exceptions
	if (GetPriorityExc(stExc, bRemoveFromQueue))
	{
		// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, GetPriorityExc");
		return true;
	}
	ScopedLock lock(mExceptionArray);

	// Get exceptions from data
	if (pSASModule->_ExceptionsLoad(sExceptions))
	{
		// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, _ExceptionsLoad");
		ExcToArray(sExceptions);
		// queue is not empty
		if (!mExceptionArray->empty())
		{
			ArrayGet(stExc, bRemoveFromQueue);
			// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, ArrayGet");
			// if reel N Tilt exception the extra char is the winning number
			if (stExc->code == SAS_EXC_REEL_N_HAS_STOPED)
			{
				// shranimo WIN NUMBER za morebitni 0x8F long poll
				// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, SAS_EXC_REEL_N_HAS_STOPED");
				m_cLastExcWinNum = UNDEF_WIN_NUM;    // unknown win num
				if (yutils::strToInt2(stExc->extra.Mid(3), nWinNum)) /*z Mid preskocimo "01#"*/
					m_cLastExcWinNum = (unsigned char)nWinNum;
				// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, stExc->extra.Mid");
			}
			// if removed from queue store it
			if (bRemoveFromQueue)
			{
				bOverrun = false;
				ArrayToExc(sExceptions);
				// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, ArrayToExc");
				pApp->DoAsyncTask(std::bind(&TSASModule::_ExceptionsStoreStr, pSASModule, sExceptions));
			}
			// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, _ExceptionsStore");
		}
		else
		{
			// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, SAS_EXC_NO_ACTIVITY");
			stExc->code = (unsigned char)SAS_EXC_NO_ACTIVITY;
			stExc->extra.clear();
		}

		// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, UnlockMut2_1x");
		return true;
	}
	// pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, NO_GAME_ID, "TSASExceptions::GetException, UnlockMut3");
	return false;
}


// ---------------------- priority exceptions ----------------------

/* Adds the exception in a priority list if there is such an entry */
void TSASExceptions::AddPriorityExc(unsigned char cExc)
{
	m_sPriorityExc.Lock();
	auto find = m_sPriorityExc->find(cExc);

	if (find != m_sPriorityExc->end())
	{
		find->second = true;
	}
	/*
	else
	  pApp->WriteLog(LS_SAS|LT_DEBUG_ERROR, NO_GAME_ID, "Priority Exception(%X) already in queue", cExc);
	*/
	m_sPriorityExc.Unlock();
}

/* Removes the exception from the priority list if there is such an entry */
void TSASExceptions::RemovePriorityExc(unsigned char cExc)
{
	m_sPriorityExc.Lock();
	auto find = m_sPriorityExc->find(cExc);

	if (find != m_sPriorityExc->end())
	{
		find->second = false;
	}

	m_sPriorityExc.Unlock();
}

/* Returns true if there is a priority exception waiting */
bool TSASExceptions::GetPriorityExc(ExceptionEntry* stExc, bool bRemoveFromQueue)
{
	ScopedLock lock(m_sPriorityExc);
	for (auto& pair : &m_sPriorityExc)
	{
		if (pair.second)
		{
			if (bRemoveFromQueue)
				pair.second = false;
			stExc->code = pair.first;
			stExc->extra.clear();
			return true;
		}
	}
	return false;
}

/* Returns true if passed exception is a priority exception */
bool TSASExceptions::IsPriorityExc(unsigned char cExc) const
{
	ScopedLock lock(m_sPriorityExc);
	return m_sPriorityExc->find(cExc) != m_sPriorityExc->end();
}

// ---------------------- Exception ----------------------

// Returns true if cCode is validation specific exception code
/* UNUSED
static int IsValidationExc(unsigned char bCode)
{
  if (bCode == 0x3F || bCode == 0x57 || bCode == 0x67 || bCode == 0x68)
    return true;
  return false;
}
*/

// ------------------- exceptions to array -------------------
void TSASExceptions::ExcToArray(TString sExceptions)
{
	ScopedLock lock(mExceptionArray);
	mExceptionArray->clear();

	TString sOne;
	while (sExceptions.Subtract("#", &sOne))
	{
		if (!sOne.empty())
		{
			ExceptionEntry aMember;
			sOne.GetHexByte(0, aMember.code);
			if (sOne.length() > 2 && sOne.length() < 512)
			{
				std::vector<unsigned char> extra;
				crypto::HexToBytes(sOne.substr(2), extra);
				aMember.extra.assign(extra.begin(), extra.end());
			}
			mExceptionArray->push_back(aMember);
		}
	}
}

void TSASExceptions::ArrayToExc(TString& sExceptions)
{
	sExceptions.clear();

	ScopedLock lock(mExceptionArray);
	for (const ExceptionEntry& e : &mExceptionArray)
	{
		TString sHex = crypto::BytesToHex(e.extra.data(), e.extra.size(), true);
		sExceptions += yutils::Format("%02x%s#", (int)e.code, sHex.c_str());
	}
}

void TSASExceptions::ArrayAdd(const ExceptionEntry& entry, bool bToFront)
{
	mExceptionArray.Lock();
	if (mExceptionArray->size() == MAX_EXCEPTIONS)
	{
		mExceptionArray->pop_front();
		if (!bOverrun)
		{
			bOverrun = true;
			AddPriorityExc(SAS_EXC_BUFFER_FULL_FOR_GENERAL_POLL);
		}
	}
	// -------- to front -----------
	if (bToFront)
	{
		// move all one right
		mExceptionArray->push_front(entry);
	}
	// -------------- to the end ----------------
	else
	{
		// if buffer already full
		mExceptionArray->push_back(entry);
	}
	mExceptionArray.Unlock();
}


// Gets the first exception from the array
bool TSASExceptions::ArrayGet(ExceptionEntry* structExc, bool bRemoveFromQueue)
{
	ScopedLock lock(mExceptionArray);
	if (mExceptionArray->empty())
	{
		*structExc = ExceptionEntry();
		return false;
	}

	structExc->code = mExceptionArray->front().code;
	structExc->extra = mExceptionArray->front().extra;

	// remove it from array
	if (bRemoveFromQueue)
		mExceptionArray->pop_front();

	return true;
}

void SASExceptionsUnitTest()
{
	TSASExceptions SASExceptions;
	ExceptionEntry E;
	int i;

	//-----------------  Exception buffer full test -----------------
	for (i = 0; i < MAX_EXCEPTIONS + 5; i++)
	{
		E.code = i;
		// add to end
		SASExceptions.AddException(E);
	}
	// SDL_Delay(20000);
	for (i = 0; i < MAX_EXCEPTIONS + 10; i++)
	{
		SASExceptions.GetException(&E, true);
		TLOG(LogSAS, Normal, "Exception Get: %X", E.code);
	}

	//-----------------  Add priority exception and read it -----------------
	/*SASExceptions.AddException(0x1);
	SASExceptions.AddException(0x67);
	SASExceptions.AddException(0x2);
	SASExceptions.AddException(0x70);
	for (i = 0; i < MAX_EXCEPTIONS + 10; i++)
	{
	  SASExceptions.GetException(&E, REMOVE_FROM_QUEUE);
	  pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, "Exception Get:%X", E.code);
	}
  */

	//----------------- Generate 42 exceptions and Get them from buffer -----------------
	/*for (i = 0; i < MAX_EXCEPTIONS + 10; i++)
	{
	  E.code = i;
	  // add to end
	  SASExceptions.AddException(E);
	}

	for (i = 0; i < MAX_EXCEPTIONS + 10; i++)
	{
	  // Get from front
	  SASExceptions.GetException(&E, REMOVE_FROM_QUEUE);
	  pApp->WriteLog(LS_SAS|LT_CONSOLE_MESSAGE, "Exception Get:%X", E.code);
	}
	*/
}