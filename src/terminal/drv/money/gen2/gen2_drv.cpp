#include "drv/money/gen2/gen2_drv.h"

#include "MyUtils.h"
#include "TApplication.h"
#include "drv/money/gen2/gen2_l.h"

#define TIME_OUT                15000
#define MAX_SEND_RECIVE_RETRIES 3

namespace gen2drv
{
using namespace rtfwk_sdl2;

const std::string TemplateVersion = "IGP-004";    // must be max 7 characters!
const std::vector<std::string> Templates = {
	"^R|$|DR|^", "^R|1|R|104|1060|65|1020|0|3|1|8|2|2|0|0||^", "^R|2|R|113|1060|31|502|0|3|1|8|1|1|0|0||^", "^R|3|R|113|520|31|520|0|3|0|8|1|1|0|0||^",
	//"^R|4|R|208|1060|89|1020|0|3|1|3|2|2|0|1|CASHOUT TICKET|^", "^R|5|R|208|1060|89|1020|0|3|1|3|2|2|0|1|JACKPOT TICKET|^",
	/*"^R|6|R|208|1060|89|1020|0|3|1|3|2|2|0|1|DEMO TICKET|^",*/ "^R|7|R|208|1060|89|1020|0|3|1|3|2|2|0|1|VOID TICKET VOID|^",
	/*"^R|8|R|208|1060|89|1020|0|3|1|3|2|2|0|1|JACKPOT RECEIPT|^", "^R|h|R|208|1060|89|1020|0|3|1|3|2|2|0|1|CASHOUT RECEIPT|^",*/
	/*"^R|9|R|208|1060|89|1020|0|3|1|3|2|2|0|1|VOID DEMO VOID|^", "^R|A|R|358|822|31|210|0|3|0|8|1|1|0|1|VALIDATION|^",*/ "^R|B|R|358|607|31|607|0|3|0|8|1|1|0|0||^",
	"^R|C|R|383|860|31|220|0|3|1|8|1|1|0|0||^", "^R|D|R|383|640|31|180|0|3|1|8|1|1|0|0||^", "^R|E|R|383|435|31|434|0|3|0|8|1|1|0|0||^",
	"^R|F|R|398|1060|24|1020|0|3|1|7|1|1|0|0||^", "^R|G|R|419|1060|24|1020|0|3|1|7|1|1|0|0||^", "^R|I|R|491|1060|69|1020|0|3|1|5|2|2|0|0||^",
	"^R|J|R|500|987|24|327|0|3|2|7|1|1|0|1|Ticket Void after|^", "^R|K|R|500|650|24|193|0|3|0|7|1|1|0|0||^", "^R|L|R|500|452|24|366|0|3|0|7|1|1|0|0||^",
	"^R|N|R|268|1060|69|1020|0|3|1|5|2|2|0|0||^", "^R|O|R|279|1060|24|1020|0|3|1|7|1|1|0|0||^", "^R|P|R|303|1060|24|1020|0|3|1|7|1|1|0|0||^",
	"^R|Q|R|331|860|31|220|0|3|1|8|1|1|0|0||^", "^R|R|R|331|640|31|180|0|3|1|8|1|1|0|0||^", "^R|S|R|331|430|31|425|0|3|0|8|1|1|0|0||^",
	"^R|T|R|361|822|31|210|0|3|0|5|1|1|0|1|VALIDATION|^", "^R|U|R|361|592|31|592|0|3|0|5|1|1|0|0||^", "^R|Z|R|202|240|108|624|0|1|1|l|4|8|108|0||^",
	"^R|X|R|362|240|108|624|0|1|1|l|4|8|108|0||^", /*"^R|a|R|18|10|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^",*/
	/*"^R|b|R|18|55|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^", "^R|c|R|18|105|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^",*/
	/*"^R|d|R|18|160|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^", "^R|e|R|18|210|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^",*/
	/*"^R|f|R|18|275|490|35|0|0|1|5|1|1|0|1|VOID VOID VOID VOID|^", "^R|g|R|427|1155|265|744|0|3|1|3|6|6|0|1|VOID|^",*/ "^R|i|R|125|8|370|31|0|0|0|8|1|1|0|0||^",
	"^R|j|R|208|1060|89|1020|0|3|1|3|2|2|0|0||^", "^R|k|R|358|822|31|210|0|3|0|8|1|1|0|0||^", "^R|m|R|361|822|31|210|0|3|0|5|1|1|0|0||^",
	/*"^R|n|R|18|10|490|35|0|0|1|5|1|1|0|0||^", "^R|o|R|18|55|490|35|0|0|1|5|1|1|0|0||^", "^R|p|R|427|1155|265|744|0|3|1|3|6|6|0|0||^",*/
	"^R|q|R|500|987|24|405|0|3|2|7|1|1|0|0||^",    // /* GRAPHICS */ "^R|s|R|50|365|400|364|0|0|0|#G001|0|0|0|0||^",

	// PLAYAWAY STUFF
	"^R|s|R|330|1060|120|624|0|3|1|l|4|8|120|0||^", /* PlayAway barcode */
	"^R|u|R|80|370|31|370|0|3|1|8|1|1|0|0||^", /* PlayAway token above QR */
	"^R|v|R|215|1210|89|960|0|3|1|3|2|2|0|0||^", /* PlayAway title */
	"^R|z|R|110|1210|31|460|0|3|2|8|1|1|0|0||^", /* PlayAway address 1 */
	"^R|w|R|110|730|31|460|0|3|0|8|1|1|0|0||^", /* PlayAway address 2 */
	"^R|x|R|480|1210|69|960|0|3|1|5|2|2|0|0||^", /* PlayAway player name */
	"^R|y|R|205|1220|24|960|0|3|1|7|1|1|0|0||^", /* PlayAway 'must be paid in on same machine' */
	"^R|p|R|480|1070|420|60|0|2|1|8|2|2|0|0||^", /* PlayAway machine name */
	"^R|r|R|480|370|420|33|0|2|1|7|1|1|0|0||^", /* PlayAway scan with phone vertical */
	"^R|f|R|110|1120|31|200|0|3|0|7|1|1|0|0||^", /* PlayAway date */
	"^R|g|R|453|1120|24|200|0|3|0|7|1|1|0|0||^", /* PlayAway time */
	"^R|n|R|486|370|30|370|0|3|1|7|1|1|0|0||^", /* PlayAway scan with phone below QR */
	"^R|o|R|365|1220|31|960|0|3|1|8|1|1|0|0||^", /* PlayAway token below barcode */
	"^R|e|R|395|1220|31|960|0|3|1|7|1|1|0|0||^", /* PlayAway session below barcode */


	//"^T|0|R|500|1240|i|1|2|3|4|A|B|C|D|E|F|G|I|J|K|L|Z|^",    // nekoč popravljen template 0, j namesto 4, da lahko vpišemo sami naslov
	//"^T|1|R|500|1240|i|1|2|3|5|A|B|C|D|E|F|G|I|J|K|L|Z|^",
	//"^T|2|R|500|1240|i|1|2|3|6|A|B|C|D|E|F|G|I|J|K|L|Z|^",    // nekoč PLAYABLE ONLY popravljen template 2, j namesto 6, da lahko vpišemo sami naslov
	"^T|3|R|500|1240|1|2|3|7|N|O|P|Q|R|S|T|U|J|K|L|X|^",    //"^T|4|R|500|1240|i|1|2|3|8|N|O|P|Q|R|S|T|U|J|K|L|X|^",
	                                                        //"^T|5|R|500|1240|i|1|2|3|9|N|O|P|Q|R|S|T|U|J|K|L|X|^",
	//"^T|6|R|500|1240|a|b|c|d|e|f|g|^", "^T|7|R|500|1240|i|1|2|3|h|A|B|C|D|E|F|G|I|J|K|L|Z|^",
	//"^T|8|R|500|1240|i|1|2|3|h|N|O|P|Q|R|S|T|U|J|K|L|X|^",    // nekoč, zdaj se za vse uporablja Template9 in TemplateA
	"^T|9|R|500|1240|i|1|2|3|j|k|B|C|D|E|F|G|I|q|L|Z|^", "^T|A|R|500|1240|i|1|2|3|j|N|O|P|Q|R|S|m|U|q|L|X|^",    //"^T|B|R|500|1240|n|o|p|^",
	/* GRAPHICS */    //"^T|C|R|500|1240|s|^",
	// opis template paketa - ki se shrani v printer-ju
	("^z|V|P|" + TemplateVersion + "|^"),
	// save to eeprom
	"^z|$|^"
};

//---------------------------------------------------------------------------------------------------
//------------------ SUPPORT FUNCTIONS --------------------------------------------------------------
void SetNextState(TMyData* pMyData, TpSAFunction pNextStateFunction)
{
	if (NULL == pMyData || NULL == pNextStateFunction)
		return;
	pMyData->PreviusState = pMyData->NextState;
	pMyData->NextState = pNextStateFunction;

	if (pMyData->PreviusState != pMyData->NextState && pMyData->NextState == (TpSAFunction)stateOperationMode)
	{
		ScopedLock lock(*pMyData);
		// komanda se je izvršila šele ko se vrnemo v operationMode - zdaj začnemo sprejemati nove komande - READY smo samo, ko smo v stateOperationMode
		pMyData->Command = NULL;
	}
}

int Open(TMyData* pMyData)
{
	if (!pMyData)
		return -1;

	if (!std::filesystem::exists(pMyData->SerialPort))
		return -1;

	try
	{
		pMyData->Serial = std::make_unique<ImaxaSerial>(pMyData->SerialPort, pMyData->currentBaudrate);
	}
	catch (const std::exception& e)
	{
		TLOG(LogApp, Error, "Gen2: Could not open serial communication - %s", e.what());
		return -1;
	}

	return 0;
}

int Close(TMyData* pMyData)
{
	if (!pMyData)
		return -1;
	pMyData->Serial.reset();
	return 0;
}
int CheckDevicePresent(TMyData* pMyData)
{
	if (!pMyData)
		return -1;

	/* pogledamo kaj se dogaja z napravo */
	const uint8_t msgData = 0x05;
	std::unique_lock guard(pMyData->PrinterCommandMutex);
	if (pMyData->Serial->SendMessage(&msgData, 1) == 1)
		return 0;
	return -2;
}
//---------------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------
//---- M A I N    S T A T E S -----------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------
//---------------------------------------------------------------------------------------------------

/*---------------------------------------------------------------------------------------------------
FUNCTION: stateCommunicationError()

DESC:	SA funkcija, v primeru napake ugotovi za kakšno napako gre in ustrezno ukrepa

PARAMS: pMyData - handle na instanco komunikacijskega driverja

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int stateCommunicationError(TMyData* pMyData)
{
	typedef struct TStateData
	{
		unsigned int x;    // void param
	} TStateData;

	if (NULL == pMyData)
		return -1;

	if (NULL == pMyData->pStateData)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};

		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::COMMUNICATION_ERROR, true);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);

		pApp->OnComponentStatusChanged("ticket.status", "ERROR");
		pApp->OnComponentStatusChanged("ticket.version", "Connecting..");
	}
	Close(pMyData);
	usleep(500000);    // počakamo tudi zaradi uporabe procesorja
	Open(pMyData);

	if (0 == CheckDevicePresent(pMyData))
	{    // ali smo uspeno komunicirali
		delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		SetNextState(pMyData, (TpSAFunction)stateResetDevice);
		return 0;
	}

	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: statePrinterError()

DESC:	SA funkcija, v primeru napake na printerju

PARAMS: pMyData - handle na instanco komunikacijskega driverja

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int statePrinterError(TMyData* pMyData)
{
	typedef struct TStateData
	{
		unsigned int x;    // void param
	} TStateData;

	if (NULL == pMyData)
		return -1;

	if (NULL == pMyData->pStateData)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		// pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::TICKET_PRINTER_ERROR, true);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);

		pApp->OnComponentStatusChanged("ticket.status", "ERROR");
	}

	if (_updatePrinterStatus(pMyData) == 0)
	{
		ScopedLock lock(*pMyData);
		if (!pMyData->TicketPrinterStatusFlags.HasAnyFlags({ ETicketPrinterStatus::BUSY, ETicketPrinterStatus::PAPER_NOT_LOADED, ETicketPrinterStatus::SYSTEM_ERROR,
		                                                     ETicketPrinterStatus::HEAD_UP, ETicketPrinterStatus::PAPER_NOT_LOADED, ETicketPrinterStatus::TAKE_TICKET,
		                                                     ETicketPrinterStatus::PAPER_JAM, ETicketPrinterStatus::CHASIS_OPEN }))
		{
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)stateResetDevice);
		}
	}

	usleep(100000);    // počakamo zaradi uporabe procesorja

	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: stateResetDevice()

DESC:	SA funkcija, resetira napravo

PARAMS: pMyData - handle na instanco

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int stateResetDevice(TMyData* pMyData)
{
	typedef struct TStateData
	{
		Uint64 StateStart;
	} TStateData;

	// UNUSED TStateData* pStateData;

	if (NULL == pMyData)
		return -1;

	if (NULL == pMyData->pStateData)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		((TStateData*)pMyData->pStateData)->StateStart = SDL_GetTicks64();
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnResettingDevice, NULL, NULL);

		pApp->OnComponentStatusChanged("ticket.status", "RESETTING");
	}
	// UNUSED pStateData = (TStateData*)pMyData->pStateData;

	const std::vector<uint8_t> msg = { 0x5E, 0x72, 0x7C, 0x5E };
	{
		std::lock_guard lock(pMyData->PrinterCommandMutex);
		if (pMyData->Serial->SendMessage(msg))
		{
			std::this_thread::sleep_for(std::chrono::milliseconds(2500));    // pocakati moramo reset printerja
			pMyData->Serial->SendMessage("^z|V|S|U|^");    // select user defined version report

			std::this_thread::sleep_for(std::chrono::milliseconds(100));
			_updatePrinterStatus(pMyData);

			pMyData->Serial->SendMessage("^l|D|F|$|G|^");    // clear library (sometimes the printer thinks its flash is full for no reason)
			SDL_Delay(100);

			if (pMyData->SoftwareVersion != TemplateVersion)
			{
				pApp->WriteLog(LS_TICKET | LT_INFO, NO_GAME_ID, "GEN2: Updating printer templates to %s!", TemplateVersion.c_str());
				for (const std::string& tmp : Templates)
				{
					pMyData->Serial->SendMessage(tmp);
					std::this_thread::sleep_for(std::chrono::milliseconds(100));
				}
			}

			// posljemo printerju še podatke o konfiguraciji vrat - izkazalo se je, da nekateri printerji nočejo komunicirati, dokler mu ne pošljemo komande ^A - najbrž
			// ker pise, da je default interface PARALEL
			int baud_rate = 8;    // default 9600
			switch (pMyData->currentBaudrate)
			{
				case 9600: baud_rate = 8; break;
				case 19200: baud_rate = 9; break;
				case 38400: baud_rate = 10; break;
				case 57600: baud_rate = 11; break;
				default: break;    // default 9600
			}

			pMyData->MessageData.resize(100);
			pMyData->MessageData.resize(sprintf((char*)pMyData->MessageData.data(), "^A|%d|N|8|X|^", baud_rate));
			// ker pise, da je mozno da printer odgovori, ce pride do spremembe XON/XOFF, naredimo tudi receive, zato da spraznemo vhodno vrsto
			pMyData->Serial->SendReceiveMessage(pMyData->MessageData, 1, 100);

			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)stateOperationMode);
			return 0;
		}
	}

	if (MyUtils::IsTime(((TStateData*)pMyData->pStateData)->StateStart, 5000))    // ali že več kot 5 sekund nismo mogli poslati..
	{
		delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		SetNextState(pMyData, (TpSAFunction)statePrinterError);
		return 0;
	}

	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: statePrintTicket()

DESC:	SA funkcija, izpie ticket

PARAMS: pMyData - handle na instanco

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int statePrintTicket(TMyData* pMyData)
{
	typedef struct TStateData
	{
		Uint64 StateStart;
		Uint64 NextPhaseStart;
		int Status;
	} TStateData;
	TStateData* pStateData;

	if (NULL == pMyData)
		return -1;

	if (NULL == pMyData->pStateData)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		((TStateData*)pMyData->pStateData)->StateStart = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->NextPhaseStart = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->Status = 0;

		pApp->OnComponentStatusChanged("ticket.status", "PRINTING");
	}
	pStateData = (TStateData*)pMyData->pStateData;

	/* posljemo ticket printerju */
	if (0 == pStateData->Status && !MyUtils::IsTime(pStateData->StateStart, 11000))
	{
		_updatePrinterStatus(pMyData);
		ScopedLock lock(*pMyData);    // če trenutno tiskanje ni možno, čakamo
		if (pMyData->TicketPrinterStatusFlags.HasAnyFlags({ ETicketPrinterStatus::BUSY, ETicketPrinterStatus::SYSTEM_ERROR, ETicketPrinterStatus::HEAD_UP,
		                                                    ETicketPrinterStatus::PAPER_NOT_LOADED, ETicketPrinterStatus::TAKE_TICKET, ETicketPrinterStatus::PAPER_JAM,
		                                                    ETicketPrinterStatus::CHASIS_OPEN }))
		{
			lock.unlock();
			_updatePrinterStatus(pMyData);
			sleep(1);
			return 0;    // čakamo
		}

		std::vector<std::string> setupCommands;
		pMyData->PrePrintCommands.swap(setupCommands);
		lock.unlock();

		_clearPrinterErrorStatus(pMyData);

		if (!setupCommands.empty())
		{
			std::lock_guard lock(pMyData->PrinterCommandMutex);
			for (const std::string& cmd : setupCommands)
			{
				pMyData->Serial->SendMessage(cmd);
				_updatePrinterStatus(pMyData);
				_clearPrinterErrorStatus(pMyData);
				SDL_Delay(10);
			}
		}

		if (pMyData->TicketData[0] != "010123456789098769")    // si zapomnemo samo ce ni DEMO TICKET - ali je to dober pogoj?!?
			pApp->SetParam("TICKET_PRINT_TRY", pMyData->TicketData[0], ParameterDomain::MINE, "", "",
			               true);    // poskus tiskanja lističa, če po ponovnem zagonu na tiskalniku ostane bit TOP_OF_FORM, potem se ta koda upošteva za LastTicket
		std::lock_guard lock2(pMyData->PrinterCommandMutex);
		if (pMyData->Serial->SendMessage(pMyData->TicketLayout) > 0)
		{
			pApp->WriteLog(LS_TICKET | LT_CRITICAL_ERROR, NO_GAME_ID, "Sending ticket print try command %s", pMyData->TicketLayout.c_str());
			pStateData->Status = 1;
			pStateData->NextPhaseStart = SDL_GetTicks64();
			//         pMyData->FirstTicketPrinted=true;
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::PRINTING, true);
			pApp->WriteLog(LS_TICKET | LT_CRITICAL_INFO, NO_GAME_ID, "GEN2: Got a ticket to print.");
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
			                      (void*)ETicketPrintingStatus::PRINTING_TICKET);
			return 0;
		}
	}
	else if (1 == pStateData->Status)
	{    // čakamo BarCode in TopOfForm              /*TALE ZADEVA JE NEJASNA v NAVODILIH, pa tudi V PRAKSI NE DELUJE pravilno*/
		if (0 == _updatePrinterStatus(pMyData) && (pMyData->BarCode || pMyData->TopOfForm /*|| pMyData->BarCodeDataAccessed*/))
		{
			// uspešno smo tiskali
			if (pMyData->TicketData[0] != "010123456789098769")    // si zapomnemo samo ce ni DEMO TICKET - ali je to dober pogoj?!?
				pApp->SetParam("TICKET_PRINT_DONE", pMyData->TicketData[0], ParameterDomain::MINE, "", "",
				               true);    // čimprej, po TopOfForm, moramo vpisati v bazo da smo uspeli s tiskanjem
			pMyData->LastSuccessfulyPrintedTicketCode = pMyData->TicketData[0];
			// pMyData->TicketCompletedFlag = true;
			pApp->WriteLog(LS_TICKET | LT_CRITICAL_INFO, NO_GAME_ID, "GEN2: Ticket %s successfully printed.",
			               MyUtils::CreateLoggableTicketNumber(pMyData->TicketData[0]).c_str());
			_clearPrinterErrorStatus(pMyData);
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)stateOperationMode);
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::PRINTING, false);
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
			                      (void*)ETicketPrintingStatus::PRINT_SUCCESSFUL);
			return 0;
		}
	}

	if (MyUtils::IsTime(pStateData->StateStart, 25000))
	{
		delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		pApp->WriteLog(LS_TICKET | LT_CRITICAL_INFO, NO_GAME_ID, "GEN2: Ticket %s print TIMED OUT.", MyUtils::CreateLoggableTicketNumber(pMyData->TicketData[0]).c_str());
		SetNextState(pMyData, (TpSAFunction)statePrinterError);
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::PRINTING, false);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), (void*)ETicketPrintingStatus::PRINT_ERROR);
		return 0;
	}

	return 0;
}

int _updatePrinterStatus(TMyData* pMyData)
{
	if (!pMyData)
		return -1;
	// printf("_updatePrinterStatus\n");
	pMyData->MessageData = { 0x5 };
	std::unique_lock guard(pMyData->PrinterCommandMutex);
	if (!pMyData->Serial->SendReceiveMessage(pMyData->MessageData, 1, 150) || pMyData->MessageData.size() <= 10)
	{    // niti statusa ne dobimo, potem gre za communication error
		guard.unlock();
		ScopedLock lock(*pMyData);
		// pMyData->CommError = true;    // is not ready
		return -1;
	}
	else
	{
		guard.unlock();
		// sparsamo response
		ScopedLock lock(*pMyData);
		const Bitflag<ETicketPrinterInternalError> oldInternalStatusFlags = pMyData->InternalStatusFlags;

		// pMyData->CommError = true;
		pMyData->PrinterStatus = std::string((const char*)pMyData->MessageData.data(), pMyData->MessageData.size());
		size_t FirstStatusBytePos = pMyData->PrinterStatus.rfind("*S|");
		if (FirstStatusBytePos >= pMyData->MessageData.size())
			return 1;

		// printf("printer status %s\n", pMyData->PrinterStatus.c_str() + FirstStatusBytePos);

		const size_t pos_of_version = FirstStatusBytePos + 5;    // position of version
		FirstStatusBytePos = pMyData->PrinterStatus.find('|', pos_of_version);
		pMyData->SoftwareVersion = pMyData->PrinterStatus.substr(pos_of_version, FirstStatusBytePos - pos_of_version);
		FirstStatusBytePos++;    // skip pipe

		uint8_t statusByte1 = pMyData->MessageData[FirstStatusBytePos];
		uint8_t statusByte2 = pMyData->MessageData[FirstStatusBytePos + 2];
		uint8_t statusByte3 = pMyData->MessageData[FirstStatusBytePos + 4];
		uint8_t statusByte4 = pMyData->MessageData[FirstStatusBytePos + 6];
		uint8_t statusByte5 = pMyData->MessageData[FirstStatusBytePos + 8];

		auto beforeState = pMyData->TicketPrinterStatusFlags;

		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::PAPER_NOT_LOADED, (statusByte1 & STATUS_PRINTER_PAPER_LOADED) != 0);
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::HEAD_UP, statusByte1 & STATUS_PRINTER_HEAD_UP);
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::SYSTEM_ERROR, statusByte1 & STATUS_PRINTER_SYS_ERROR);

		bool tmpPrinterBusy = statusByte1 & STATUS_PRINTER_BUSY;
		if (tmpPrinterBusy)
		{
			if (pMyData->IsPrinterBusyTime == 0)
				pMyData->IsPrinterBusyTime = SDL_GetTicks64();

			if (MyUtils::IsTime(pMyData->IsPrinterBusyTime, 50))    // if it is busy for 50ms, set the IsPrinterBusy flag
				// pMyData->IsPrinterBusy = tmpPrinterBusy;
				pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::BUSY, tmpPrinterBusy);
		}
		else
		{
			pMyData->IsPrinterBusyTime = 0;
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::BUSY, tmpPrinterBusy);
		}

		//       if ( !pMyData->IsPrinterReady )
		//          printf("Gen2PrinterReady: IsPrinterBusy = %d, IsSystemError = %d, Command = %d, PreviousState = %d\n", pMyData->IsPrinterBusy, pMyData->IsSystemError,
		//          pMyData->Command, pMyData->PreviusState);

		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OUT_OF_MEMORY, statusByte2 & STATUS_PRINTER_BUFFER_JOB_MEM);
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_INTERNAL_BUFFER_OVERFLOW, statusByte2 & STATUS_PRINTER_BUFFER_OVERFLOW);
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_LIBRARY_LOAD_ERROR, statusByte2 & STATUS_PRINTER_LIBLOAD);
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_PRINTER_REGION_TRUNCATED, statusByte2 & STATUS_PRINTER_REGION_TRUNCATED);
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OBJECT_NOT_DEFINED_IN_PRINTER_LIB, statusByte2 & STATUS_PRINTER_LIBREF);

		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::TAKE_TICKET, statusByte3 & STATUS_PRINTER_PAPER_PRESENT);
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_UNSUPPORTED_COMMAND, statusByte3 & STATUS_PRINTER_UNSUPPORTED_COMMAND);

		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::PAPER_JAM, statusByte4 & STATUS_PRINTER_PAPER_JAM);
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::TICKET_LOW, statusByte4 & STATUS_PRINTER_PAPER_LOW);

		pMyData->BarCode = statusByte5 & STATUS_PRINTER_SF5_BARCODE;    // SF5_BCODE - used with TCL - template printing
		pMyData->BarCodeDataAccessed = statusByte5 & STATUS_PRINTER_SF5_BARCODE_DONE;    // SF5_BCODE_DONE
		// pMyData->BarCodeHasCompletedFlag = pMyData->BarCodeHasCBarCodeHasCompletedFlagompletedFlag || pMyData->BarCode; /*SF5BCODE - Validation Done */
		//|| pMyData->BarCodeDataAccessed; /*SF5_BCODE_DONE - Barcode data is accessed*/ //ali je barcode pravilno izpisan
		pMyData->TopOfForm = (bool)(statusByte5 & STATUS_PRINTER_TOP_FORM); /*SF5TOF - At top of Form */    // ali je cel ticket pravilno izpisan
		// pMyData->TicketCompletedFlag = pMyData->TicketCompletedFlag || pMyData->BarCode;
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::CHASIS_OPEN, statusByte5 & STATUS_PRINTER_SF5_CHASIS_OPEN);

		bool powerResetOccured = statusByte5 & STATUS_PRINTER_SF5_POWER_RESET;

		const Bitflag<ETicketPrinterInternalError> change = pMyData->InternalStatusFlags ^ oldInternalStatusFlags;

		if (pMyData->InitPowerResetHandled)
			pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_POWER_RESET, powerResetOccured);

		if (powerResetOccured)
		{
			pMyData->InitPowerResetEventOccured = true;
			pApp->WriteLog(LS_TICKET | LT_CONSOLE_MESSAGE, NO_GAME_ID, "GEN2: Printer was reset or power supply reset...");
		}

		if (pMyData->InitPowerResetEventOccured && !powerResetOccured)
			pMyData->InitPowerResetHandled = true;

		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_UNSUPPORTED_COMMAND) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_UNSUPPORTED_COMMAND))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Unsupported command");
			_clearPrinterErrorStatus(pMyData);
		}
		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OUT_OF_MEMORY) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OUT_OF_MEMORY))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Out of job memory - will try to print ticket anyway");
			_clearPrinterErrorStatus(pMyData);
		}
		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_INTERNAL_BUFFER_OVERFLOW) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_INTERNAL_BUFFER_OVERFLOW))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Printer internal buffer overflow - handshake problem?");
			_clearPrinterErrorStatus(pMyData);
		}
		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OBJECT_NOT_DEFINED_IN_PRINTER_LIB) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_OBJECT_NOT_DEFINED_IN_PRINTER_LIB))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Object not defined in printer library");
			_clearPrinterErrorStatus(pMyData);
		}
		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_PRINTER_REGION_TRUNCATED) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_PRINTER_REGION_TRUNCATED))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Print region had to be truncated");
			_clearPrinterErrorStatus(pMyData);
		}
		if (change.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_LIBRARY_LOAD_ERROR) &&
		    pMyData->InternalStatusFlags.HasFlag(ETicketPrinterInternalError::INTERNAL_ERROR_LIBRARY_LOAD_ERROR))
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Ticket library or object load error");
			_clearPrinterErrorStatus(pMyData);
		}

		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::INTERNAL_ERROR, pMyData->InternalStatusFlags.Value());
		if (beforeState != pMyData->TicketPrinterStatusFlags)
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);

		// printf("busy=%d, BarCode=%d, BarCodeAccessed=%d, TopOfForm=%d, IsSystemError=%d TicketToBeTaken=%d PowerWasReset=%d\n", pMyData->IsPrinterBusy,
		// pMyData->BarCode, pMyData->BarCodeDataAccessed, pMyData->TopOfForm, pMyData->IsSystemError, pMyData->TicketIsReadyToBeTaken, pMyData->PowerWasReset);
		return 0;
	}
}

int _clearPrinterErrorStatus(TMyData* pMyData)
{
	if (!pMyData)
		return -1;

	const std::vector<uint8_t> msgData = { 0x5E, 0x43, 0x7C, 0x5E };
	std::lock_guard lock(pMyData->PrinterCommandMutex);

	pMyData->Serial->SendMessage(msgData);

	// printf("\n\n TICKET ERROR_CLEAR \n\n");
	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: stateOperationMode()

DESC:	SA funkcija čaka, da naprava pošlje podatke - informacijo o dogajanju na panelu

PARAMS: pMyData - handle na instanco komunikacijskega driverja

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int stateOperationMode(TMyData* pMyData)
{
	typedef struct TStateData
	{
		Uint64 CommunicationWatchdog;
		Uint64 StatusOKWatchdog;
		bool PrinterERROR;
	} TStateData;

	TStateData* pStateData;

	if (NULL == pMyData)
		return -1;

	if (!pMyData->pStateData)
	{
		pMyData->pStateData = new TStateData();

		((TStateData*)pMyData->pStateData)->CommunicationWatchdog = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->StatusOKWatchdog = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->PrinterERROR = false;

		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);

		if (pMyData->PreviusState)
		{
			// pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::TICKET_OPERATING, true);
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::COMMUNICATION_ERROR, false);
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);
		}
		pApp->OnComponentStatusChanged("ticket.status", "OK");
		pApp->OnComponentStatusChanged("ticket.version", "generic");
	}
	pStateData = (TStateData*)pMyData->pStateData;

	/* ALI MORAMO KLICATI UKAZ */
	ScopedLock lock(*pMyData);
	if (NULL != pMyData->Command)
	{
		if (pMyData->pStateData)
			delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		SetNextState(pMyData, pMyData->Command);
		//		pMyData->Command = NULL; //komanda se izvrši šele, ko pridemo nazaj v tole funkcijo
		return 0;
	}
	lock.unlock();

	if (0 == _updatePrinterStatus(pMyData))
	{
		pStateData->CommunicationWatchdog = SDL_GetTicks64();    // resetiramo, ker je komunikacija ok


		// pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::COMMUNICATION_ERROR, false);
		// MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);

		if (NULL == pMyData->PreviusState)    // NUJNO PRED RESETOM - moramo preveriti ali je TopOfForm
		{
			if (pMyData->BarCode || pMyData->TopOfForm /*|| pMyData->BarCodeDataAccessed */)
			{
				//            _clearPrinterErrorStatus(pMyData);
				pMyData->LastSuccessfulyPrintedTicketCode = pApp->GetParam("TICKET_PRINT_TRY")->AsString();
				pApp->SetParam("TICKET_PRINT_DONE", pMyData->LastSuccessfulyPrintedTicketCode, ParameterDomain::MINE, "", "", true);
				pApp->WriteLog(LS_TICKET | LT_CRITICAL_INFO, NO_GAME_ID, "GEN2:Seems that ticket %s was printed OK.",
				               MyUtils::CreateLoggableTicketNumber(pMyData->LastSuccessfulyPrintedTicketCode).c_str());
			}
			else
				pApp->WriteLog(LS_TICKET | LT_INFO, NO_GAME_ID, "GEN2: No unconfirmed tickets pending...");
		}
		if (NULL == pMyData->PreviusState /*ali je prvi zagon driverja - ker smo morali najprej preveriti ali je TopOfForm*/)
		{
			ScopedLock lock(*pMyData);
			pMyData->Command = (TpSAFunction)stateResetDevice;    // kličemo tako, ker s tem zagotovimo NOT_READY
			return 0;
		}

		_clearPrinterErrorStatus(pMyData);

		if (!pMyData->TicketPrinterStatusFlags.HasFlag(ETicketPrinterStatus::SYSTEM_ERROR))
		{
			if (pStateData->PrinterERROR)
			{
				MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);
			}
			pStateData->PrinterERROR = false;
			pStateData->StatusOKWatchdog = SDL_GetTicks64();    // resetiramo, ker je status ok
		}
	}

	if (MyUtils::IsTime(pStateData->CommunicationWatchdog, 10000))    // Printer ERROR
	{
		ScopedLock lock(*pMyData);
		pMyData->Command = (TpSAFunction)stateCommunicationError;    // kličemo tako, ker s tem zagotovimo NOT_READY
		return 0;
	}

	usleep(100000);    // 100ms
	return 0;
}

int stateUploadGraphics(TMyData* pMyData)
{
	struct TStateData
	{
		Uint64 StateStart;
		Uint64 NextPhaseStart;
		int Status;
		int gIndex;
		std::vector<uint8_t> Bytes;
	};
	TStateData* pStateData;

	if (NULL == pMyData)
		return -1;

	if (NULL == pMyData->pStateData)
	{
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, true);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
		                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_UPLOADING);
		pMyData->pStateData = new TStateData();
		((TStateData*)pMyData->pStateData)->StateStart = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->NextPhaseStart = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->Status = 0;

		pApp->OnComponentStatusChanged("ticket.status", "PRINTING");
	}
	pStateData = (TStateData*)pMyData->pStateData;

	if (MyUtils::IsTime(pStateData->StateStart, 25000))
	{
		delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Graphics upload TIMED OUT!");
		SetNextState(pMyData, (TpSAFunction)statePrinterError);
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
		                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_UPLOAD_TIMED_OUT);
		return 0;
	}

	/* posljemo ticket printerju */
	if (0 == pStateData->Status)
	{
		_updatePrinterStatus(pMyData);
		ScopedLock lock(*pMyData);    // če trenutno graphics upload ni možen, čakamo
		if (pMyData->GraphicsObjectToUpload < 1)
		{
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)stateOperationMode);
			lock.unlock();
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
			                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_UPLOAD_NOT_POSSIBLE);
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: UPLOAD NOT POSSIBLE");
			return 0;
		}

		if (pMyData->TicketPrinterStatusFlags.HasAnyFlags({ ETicketPrinterStatus::BUSY, ETicketPrinterStatus::SYSTEM_ERROR, ETicketPrinterStatus::HEAD_UP,
		                                                    ETicketPrinterStatus::PAPER_NOT_LOADED, ETicketPrinterStatus::TAKE_TICKET, ETicketPrinterStatus::PAPER_JAM,
		                                                    ETicketPrinterStatus::CHASIS_OPEN }))
		{
			return 0;    // čakamo
		}


		pStateData->gIndex = 100 + pMyData->GraphicsObjectToUpload;    // valid IDs are from 101 up
		pMyData->GraphicsObjectToUpload = 0;
		pMyData->BytesToUpload.swap(pStateData->Bytes);
		pMyData->GraphicsSizes[pStateData->gIndex] = pMyData->GraphicsSizeToUpload;
		// pMyData->IsPrinterBusy = true;
		pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::BUSY, true);
		MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(), NULL);
		lock.unlock();
		_clearPrinterErrorStatus(pMyData);

		std::vector<uint8_t> deleteCmd;
		deleteCmd.resize(100);
		deleteCmd.resize(sprintf((char*)deleteCmd.data(), "^l|D|F|#G%03d|G|^", pStateData->gIndex));

		if (pMyData->Serial->SendMessage(deleteCmd))
		{
			pStateData->Status = 1;
			pStateData->NextPhaseStart = SDL_GetTicks64();
		}
		else
		{
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)statePrinterError);
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
			                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_DELETE_ERROR);
			return 0;
		}
	}
	else if (1 == pStateData->Status)
	{
		_clearPrinterErrorStatus(pMyData);

		char cmdStartBuffer[64];
		int cmdLen = sprintf(cmdStartBuffer, "^l|A|F|#G%03d|%d|", pStateData->gIndex, (int)pStateData->Bytes.size());
		std::vector<uint8_t> UploadCmd;
		UploadCmd.insert(UploadCmd.end(), cmdStartBuffer, cmdStartBuffer + cmdLen);

		UploadCmd.insert(UploadCmd.end(), pStateData->Bytes.begin(), pStateData->Bytes.end());
		UploadCmd.push_back('^');

		pApp->WriteLog(LS_TICKET | LT_INFO, NO_GAME_ID, "GEN2: Uploading graphics #%d with %lu bytes", pStateData->gIndex, UploadCmd.size());

		std::lock_guard lock(pMyData->PrinterCommandMutex);
		const int sentBytes = pMyData->Serial->SendMessage(UploadCmd);

		if (sentBytes == (int)UploadCmd.size())
		{
			pApp->WriteLog(LS_TICKET | LT_INFO, NO_GAME_ID, "GEN2: All bytes of command #%d sent!", pStateData->gIndex);
			pStateData->Status = 2;
			pStateData->NextPhaseStart = SDL_GetTicks64();
		}
		else
		{
			pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Graphics #%d data write ERROR (return %d)", pStateData->gIndex, sentBytes);
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, (TpSAFunction)statePrinterError);
			pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
			MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
			                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_WRITE_ERROR);
		}
	}
	else if (2 == pStateData->Status)
	{
		if (0 == _updatePrinterStatus(pMyData))
		{
			if (pMyData->TicketPrinterStatusFlags.HasFlag(ETicketPrinterStatus::SYSTEM_ERROR))
			{
				pApp->WriteLog(LS_TICKET | LT_WARNING, NO_GAME_ID, "GEN2: Graphics #%d upload ERROR.", pStateData->gIndex);
				_clearPrinterErrorStatus(pMyData);
				delete ((TStateData*)pMyData->pStateData);
				pMyData->pStateData = NULL;
				SetNextState(pMyData, (TpSAFunction)stateOperationMode);
				pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
				MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
				                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_UPLOAD_ERROR);
			}
			else if (!pMyData->TicketPrinterStatusFlags.HasFlag(ETicketPrinterStatus::BUSY))
			{
				pApp->WriteLog(LS_TICKET | LT_CRITICAL_INFO, NO_GAME_ID, "GEN2: Graphics #%d upload completed in %.1fs", pStateData->gIndex,
				               (SDL_GetTicks64() - pStateData->NextPhaseStart) * 1e-3f);

				delete ((TStateData*)pMyData->pStateData);
				pMyData->pStateData = NULL;
				SetNextState(pMyData, (TpSAFunction)stateOperationMode);
				pMyData->TicketPrinterStatusFlags.SetFlag(ETicketPrinterStatus::UPLOADING_GRAPHICS, false);
				MyUtils::PostSDLEvent(pMyData->SDL_EventCode_OnTicketStatusChange, (void*)pMyData->TicketPrinterStatusFlags.Value(),
				                      (void*)ETicketGraphicsUploadStatus::GRAPHICS_UPLOAD_SUCCESSFUL);
			}
		}
	}

	return 0;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: ebadrvMainInThread()

DESC:	Funkcija while true; ki teče v nitki in pogoanja StateAvtomat

PARAMS: hInst - handle na instanco komunikacijskega driverja
                Device - pot do datoteke porta(/dev/ttyS0,...)

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
int ebadrvMainInThread(void* hInst)
{
	if (NULL == hInst)
		return -1;
	// UNUSED char tmpStateMessage[500];

	TMyData* pMyData = (TMyData*)hInst;
	/* UNUSED
	int i=0;
	unsigned long XX;
	*/
	try
	{
		// izvajamo se v nitki
		while (true)
		{
			/* kličemo next state */
			try
			{
				/*ali je aplikacija zahtevala zaključek nitke - ali lahko nadaljujemo z nitko */
				SDL_LockMutex(pMyData->pQuitThreadMutex);
				if (pMyData->QuitThread)
				{
					SDL_UnlockMutex(pMyData->pQuitThreadMutex);
					break;
				}
				SDL_UnlockMutex(pMyData->pQuitThreadMutex);

				pMyData->NextState(hInst);
				usleep(2000);
			}
			catch (...)
			{
			};
		}
	}
	catch (...)
	{
		return -1;
	}
	return 0;
}

const std::vector<std::string> PortsToTry = { "/dev/ttyUSB0",    // first try usb ports, as we are device with USB serial
	                                          "/dev/ttyUSB1", "/dev/ttyUSB2", "/dev/ttyS0",   "/dev/ttyS1", "/dev/ttyS2",
	                                          "/dev/ttyS3",   "/dev/ttyUSB3", "/dev/ttyUSB4", "/dev/ttyS4" };

/*---------------------------------------------------------------------------------------------------
FUNCTION: Init()

DESC:	Funkcija ustvari instanco Thgen2drv in vrne handle - konstruktor

PARAMS: SerialFileName	- pot do serjskega porta npr. /dev/ttyS0
        Baudrate - if baudrate = 0, default value will be used
        UpdatePrinterTemplates - Ali naj driver nastavi template-e na lastne vrednosti
...
RESULT: instance handle; NULL - failure
---------------------------------------------------------------------------------------------------*/
Thgen2drv Init(const char* pSerialFileName, int Baudrate, int SDL_EventCode_OnStatusChange, int SDL_EventCode_OnResettingDevice,
               const Bitflag<ETicketPrinterStatus> statusFlags, std::unordered_set<std::string>& UsedPorts)
{
	if (NULL == pSerialFileName)
		return NULL;
	TMyData* pMyData;
	try
	{
		pMyData = new TMyData;

		/* pMyData init */
		pMyData->NextState = (TpSAFunction)stateOperationMode;    // stateResetDevice;
		pMyData->PreviusState = NULL;

		pMyData->TicketPrinterStatusFlags = statusFlags;
		pMyData->InternalStatusFlags.SetFlag(ETicketPrinterInternalError::INTERNAL_ERROR_POWER_RESET, false);
		pMyData->InitPowerResetHandled = false;
		pMyData->InitPowerResetEventOccured = false;

		pMyData->SDL_EventCode_OnResettingDevice = SDL_EventCode_OnResettingDevice;
		pMyData->SDL_EventCode_OnTicketStatusChange = SDL_EventCode_OnStatusChange;

		pMyData->Command = NULL;

		pMyData->BarCode = false;
		pMyData->BarCodeDataAccessed = false;
		pMyData->TopOfForm = false;

		pMyData->LastSuccessfulyPrintedTicketCode = pApp->GetParam("TICKET_PRINT_DONE")->AsString();
		if ("" == pMyData->LastSuccessfulyPrintedTicketCode)
			pMyData->LastSuccessfulyPrintedTicketCode = "NO_LAST_TICKET";

		pMyData->pStateData = NULL;

		pApp->OnComponentStatusChanged("ticket.version", "Connecting..");

		pMyData->TicketLayout.clear();

		pMyData->SerialPort = pSerialFileName;
		pMyData->currentBaudrate = Baudrate ? Baudrate : 57600;    // shranimo nastavitev za kasneje

		if (!strcmp("AUTO", pSerialFileName) || !strcmp("auto", pSerialFileName))
		{
			// poiskati moramo ustrezni serijski port
			for (const std::string& port : PortsToTry)
			{
				pMyData->SerialPort = port;

				if (UsedPorts.contains(port))
					continue;

				if (Open(pMyData))
					continue;

				// we try to communicate on this port, if successful, we will leave the port open and start the polling thread
				// printf("gen2drv: Autoconfig checking for device on %s - ", ports_to_try[port]);
				if (!CheckDevicePresent(pMyData))
				{
					rtfwk_sdl2::logman::WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "gen2drv: Autoconfig checking for device on %s - NO DEVICE", port.c_str());
					// printf("NO DEVICE\n");
					Close(pMyData);
					continue;
				}

				rtfwk_sdl2::logman::WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "gen2drv: Autoconfig checking for device on %s - FOUND", port.c_str());
				pApp->OnComponentStatusChanged("ticket.port", "A:" + port);
				// printf("FOUND\n");
				break;    // ostanemo na port-u
			}

			if (!pMyData->Serial)    // ce je AUTO, moramo koncati, ker communication error nima preverjanja port-a
			{
				pApp->OnComponentStatusChanged("ticket.status", "PORT FAILED");
				pApp->OnComponentStatusChanged("ticket.port", "AUTO");

				printf("gen2drv: AUTOCONFIG FAILED!\n");
				delete (pMyData);
				return NULL;
			}
		}
		else
		{
			pMyData->SerialPort = pSerialFileName;
			Open(pMyData);

			if (!pMyData->Serial)    // ce je AUTO, moramo koncati, ker communication error nima preverjanja port-a
			{
				pApp->OnComponentStatusChanged("ticket.status", "PORT FAILED");
				pApp->OnComponentStatusChanged("ticket.port", pMyData->SerialPort);

				printf("gen2drv: FAILED to communicate on port %s!\n", pMyData->SerialPort.c_str());
				delete (pMyData);
				return NULL;
			}
		}    // serial port search
		UsedPorts.insert(pMyData->SerialPort);
		pMyData->QuitThread = false;
		pMyData->pQuitThreadMutex = SDL_CreateMutex();
		pMyData->pSAThread = SDL_CreateThread(ebadrvMainInThread, "gen2drv", pMyData);
		printf("gen2drv: Thread started.\n");
	}
	catch (...)
	{
		return NULL;
	}
	return pMyData;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: Destroy()

DESC:	Funkcija izprazni resurse instance ctlks - destruktor

PARAMS: hInst - handle na instanco komunikacijskega driverja

RESULT: 0 success; 0<> failure
---------------------------------------------------------------------------------------------------*/
void Destroy(Thgen2drv hInst)
{
	if (NULL == hInst)
		return;
	try
	{
		printf("gen2drv: Waiting for thread to finish.\n");
		SDL_LockMutex(((TMyData*)hInst)->pQuitThreadMutex);
		((TMyData*)hInst)->QuitThread = true;
		SDL_UnlockMutex(((TMyData*)hInst)->pQuitThreadMutex);
		SDL_WaitThread(((TMyData*)hInst)->pSAThread, NULL);
		// SDL_KillThread(((TMyData *)hInst)->pSAThread);
		SDL_DestroyMutex(((TMyData*)hInst)->pQuitThreadMutex);
		printf("gen2drv: Thread terminated.\n");

		((TMyData*)hInst)->Serial.reset();

		delete ((TMyData*)hInst);
	}
	catch (...)
	{
	}
}

void ClearInternalTicketErrors(void* hInst)
{
	TMyData* pMyData = (TMyData*)hInst;
	_clearPrinterErrorStatus(pMyData);
}

}    // namespace gen2drv
