/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON><PERSON>   <PERSON>
 *   <PERSON><PERSON><PERSON>@localhost.localdomain   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
/*------------------HEADERS--------------------------*/
#include "drv/money/SSP/ssp_control_l.h"


/*------------------LIBRARY_FILES--------------------*/
#include <MyUtils.h>
#include <SDL2/SDL.h>
#include <TApplication.h>
#include <drv/money/MoneyAcceptorDriverEnums.h>
#include <stdlib.h>
#include <unistd.h>

#include <iostream>

#define ESCROW_MODE

/* Data Byte 1 */
#ifdef ESCROW_MODE
#define ESC_M 0x10
#else
#define ESC_M 0x00
#endif

/* Data Byte 2 */
#define EXT_N 0x00

#define TIME_OUT 8000

#ifdef ENABLE_CONTROL_LOG
#define control_log printf
#else
#define control_log(fmt, ...)
#endif

static void SetNextState(sspdrv::TMyData* pMyData, sspdrv::TpSAFunction pNextStateFunction)
{
	if ((pMyData == NULL) || (pNextStateFunction == NULL))
		return;

	pMyData->PreviusState = pMyData->NextState;
	pMyData->NextState = pNextStateFunction;
}

static int DoPoll(sspdrv::TMyData* pMyData)
{
	char Command = (char)0x11;
	int ret = -1;

	if (pMyData == NULL)
		return ret;

	pMyData->DataBytes = NULL;

	Command = (char)0x07;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

	if (ret < 0)
		return ret;

	pMyData->DataBytes = &(pMyData->Recived[0]);

	if ((pMyData->DataBytes != NULL) && (pMyData->RecivedLength == 1) && (pMyData->DataBytes[0] != 0xf0))
	{
		ret = -2;
		SetNextState(pMyData, sspdrv::stateDeviceError);
	}

	return ret;
}

namespace sspdrv
{
int GetEventData(TMyData* pMyData, int EscrowData)
{
	if (NULL == pMyData)
		return -1;

	if (EscrowData)
		return pMyData->SDLEventData_Denomi[EscrowData - 1];
	return -1;
}


int stateAckEscrowWithStack(void* priv)
{
	/* SSP nima Escrow-a. */
	return 0;
}


int stateAckEscrowWithReturn(void* priv)
{
	/* SSP nima Escrow-a. */
	return 0;
}

int stateWaitEscrowAck(void* priv)
{
	/* SSP nima Escrow-a. */
	return 0;
}

int stateDisableDevice(void* priv)
{
	TMyData* pMyData = (TMyData*)priv;
	char Command = (char)0x11;

	if (pMyData == NULL)
		return -1;

	strcpy(pMyData->StateMessage, "Device disabled!");

	/* Naprej normalen poll, po dokumentaciji. */
	Command = (char)0x07;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

	/* Nato onemogočimo napravo. */
	Command = (char)0x09;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);
	pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::INHIBIT, true);

	SetNextState(pMyData, stateStatusRequest);

	return 0;
}

int stateEnableDevice(void* priv)
{
	TMyData* pMyData = (TMyData*)priv;
	char Command = (char)0x11;

	if (pMyData == NULL)
		return -1;

	strcpy(pMyData->StateMessage, "Device disabled!");

	/* Napravo znova omogočimo. */
	Command = (char)0x0a;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);
	pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::INHIBIT, false);

	SetNextState(pMyData, stateStatusRequest);

	return 0;
}

int stateDeviceError(void* priv)
{
	TMyData* pMyData = (TMyData*)priv;

	typedef struct TStateData
	{
		Uint64 WatchDog;
		unsigned char LastError;
	} TStateData;

	TStateData* pStateData;

	if (pMyData == NULL)
		return -1;

	if (pMyData->pStateData == NULL)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		((TStateData*)pMyData->pStateData)->WatchDog = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->LastError = 0xff;
		strcpy(pMyData->StateMessage, "------ Device Error Waiting for device to become ready... -------");
	}
	pStateData = (TStateData*)pMyData->pStateData;

	if ((pMyData->ErrorCode == 0xe3) || (pMyData->ErrorCode == 0xe7))
	{
		/* Cash Box Removed */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKER_OPEN, true);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Stacker open! -------");
	}
	else if ((pMyData->ErrorCode == 0xe4) || (pMyData->ErrorCode == 0x00))
	{
		/* Cash Box Replaced */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKER_OPEN, false);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Stacker closed! -------");
	}

	// kinda not sure recheck with manual!
	if (pMyData->ErrorCode == 0xea)
	{
		/* Stacker Full */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKER_FULL, true);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Stacker full! -------");
	}
	else
	{
		/* Stacker Emptied */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKER_FULL, false);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Stacker emptied! -------");
	}

	if (pMyData->ErrorCode == 0xe9)
	{
		/* Jammed */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::ACCEPTOR_JAM, true);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Jam in acceptor! -------");
	}
	else
	{
		/* Jam cleared */
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::ACCEPTOR_JAM, false);
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
		strcpy(pMyData->StateMessage, "------ Device Error: Jam cleared! -------");
	}

	/* Če je communication error ali preveč časa nismo šli iz error state-a potem poskusimo resetirati napravo. */
	if ((pMyData->BillAcceptorStatusFlags.HasFlag(EBillAcceptorStatus::COMMUNICATION_ERROR)) || (SDL_GetTicks64() > (pStateData->WatchDog + TIME_OUT)))
	{
		delete ((TStateData*)pMyData->pStateData);
		pMyData->pStateData = NULL;
		SetNextState(pMyData, stateResetDevice);
		return 0;
	}

	return 0;
}

int stateStatusRequest(void* priv)
{
	TMyData* pMyData = (TMyData*)priv;

	typedef struct TStateData
	{
		Uint64 WatchDog;
		bool EscrowSignalSent;
	} TStateData;

	TStateData* pStateData;

	int ret;

	if (pMyData == NULL)
		return -1;

	if (pMyData->pStateData == NULL)
	{
		try
		{
			pMyData->pStateData = (void*)new (TStateData);
		}
		catch (...)
		{
			return -1;
		};
		((TStateData*)pMyData->pStateData)->WatchDog = SDL_GetTicks64();
		((TStateData*)pMyData->pStateData)->EscrowSignalSent = false;
	}
	pStateData = (TStateData*)pMyData->pStateData;

	ret = DoPoll(pMyData);

	if (ret >= 0)
	{
		pStateData->WatchDog = SDL_GetTicks64();

		/* Smo od aplikacije dobili ukaz? */
		SDL_LockMutex(pMyData->pCommandQueueMutex);
		if (!pMyData->CommandQueue.empty())
		{
			TpSAFunction Command = pMyData->CommandQueue.front();
			pMyData->CommandQueue.pop();
			SDL_UnlockMutex(pMyData->pCommandQueueMutex);
			delete ((TStateData*)pMyData->pStateData);
			pMyData->pStateData = NULL;
			SetNextState(pMyData, Command);
			return 0;
		}
		else
			SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	}

	if ((ret == -2) || (pMyData->DataBytes == NULL))
		return 0;

	// printf("Received %i bytes: %02X %02X\n", pMyData->RecivedLength, pMyData->Recived[0], pMyData->Recived[1]);
	if ((pMyData->RecivedLength == 1) && (pMyData->ErrorCode == 0xe7))
	{
		/* Cash Box Replaced */
		pMyData->ErrorCode = 0x00;
		SetNextState(pMyData, stateDeviceError);
	}
	else if (pMyData->RecivedLength == 2)
	{
		if (pMyData->Recived[0] == 0xf0)
		{
			switch (pMyData->Recived[1])
			{
				case 0xcc:
					/* Stacking */
					pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKING_STATE, true);
					break;
				case 0xe3:
				case 0xe7: /* NV200 pošlje to napako, ko odstranim stacker. */
					/* Cash Box Removed */
					pMyData->ErrorCode = pMyData->Recived[1];
					SetNextState(pMyData, stateDeviceError);
					break;
				case 0xe4:
					/* Cash Box Replaced */
					pMyData->ErrorCode = pMyData->Recived[1];
					SetNextState(pMyData, stateDeviceError);
					break;
				case 0xe8:
					/* Disabled */
					break;
				case 0xe9:
					/* Unsafe Jam */
					pMyData->ErrorCode = pMyData->Recived[1];
					SetNextState(pMyData, stateDeviceError);
					break;
				case 0xea:
					/* Safe Jam */
					pMyData->ErrorCode = pMyData->Recived[1];
					SetNextState(pMyData, stateDeviceError);
					break;
				case 0xeb:
					/* Stacked */
					pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKING_STATE, false);
					pMyData->EscrowNoteIndex = pMyData->Recived[2];
					MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStacked, (void*)GetEventData(pMyData, pMyData->EscrowNoteIndex), NULL);
					pMyData->EscrowNoteIndex = 0;
					break;
				case 0xec:
					/* Rejected */
					pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::RETURNING_STATE, false);
					pMyData->EscrowNoteIndex = pMyData->Recived[2];
					MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnTimeoutBillReturned, (void*)GetEventData(pMyData, pMyData->EscrowNoteIndex), NULL);
					pMyData->EscrowNoteIndex = 0;
					break;
				case 0xed:
					/* Rejecting */
					pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::RETURNING_STATE, true);
					break;
				case 0xf1:
					/* Slave Reset */
					break;
			}
		}
		MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
	}
	else if (pMyData->RecivedLength == 3)
	{
		if (pMyData->Recived[0] == 0xf0)
		{
			switch (pMyData->Recived[1])
			{
				case 0xe1:
					/* Note cleared from front at reset on channel */
					pMyData->EscrowNoteIndex = pMyData->Recived[2];
					MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnPowerOnWithBillInAcceptor, (void*)GetEventData(pMyData, pMyData->EscrowNoteIndex), NULL);
					pMyData->EscrowNoteIndex = 0;
					break;
				case 0xe2:
					/* Note cleared into cash box at reset on channel */
					pMyData->EscrowNoteIndex = pMyData->Recived[2];
					MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnPowerOnWithBillInStacker, (void*)GetEventData(pMyData, pMyData->EscrowNoteIndex), NULL);
					pMyData->EscrowNoteIndex = 0;
					break;
				case 0xe6:
					/* Fraud Attempt on channel */
					break;
				case 0xee:
					/* Credit on channel */
					pMyData->EscrowNoteIndex = pMyData->Recived[2];
					MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillAccepted, (void*)GetEventData(pMyData, pMyData->EscrowNoteIndex), NULL);
					pMyData->EscrowNoteIndex = 0;
					break;
				case 0xef:
					/* Read on channel */
					break;
			}
		}
	}

	return 0;
}

int stateResetDevice(void* priv)
{
	TMyData* pMyData = (TMyData*)priv;
	char Command = (char)0x11;

	/* Ponastavimo spremenljivke (nazaj) na začetno vrednost. */
	pMyData->Status = 0x10;
	pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::STACKING_STATE, false);
	pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::RETURNING_STATE, false);
	pMyData->EscrowNoteIndex = 0;

	/* Počisti podatke Extended Note Specification. */
	for (pMyData->ExtendedNotesNum = 0; pMyData->ExtendedNotesNum < 0x80; pMyData->ExtendedNotesNum++)
		memset(pMyData->ExtendedNoteSpecification[pMyData->ExtendedNotesNum], 0x00, 17);
	pMyData->ExtendedNotesNum = 0x00;

	Command = (char)0x01;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);
	sleep(64);

	unsigned char Inhibitcommand[3] = { 0x02, 0xff, 0xff };
	ssp_transport::SendReceiveMessage(pMyData->hTrans, Inhibitcommand, 3, pMyData->Recived, &pMyData->RecivedLength);

	Command = (char)0x03;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

	Command = (char)0x0a;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

	SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
	Command = (char)0x05;
	ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

	/*
	TODO: report firmware version
	unsigned char fwVersion[4];
	fwVersion[0] = pMyData->Recived[1];
	fwVersion[1] = pMyData->Recived[2];
	fwVersion[2] = pMyData->Recived[3];
	fwVersion[2] = pMyData->Recived[4];
	*/

	char countryCode[4];
	countryCode[0] = pMyData->Recived[5];
	countryCode[1] = pMyData->Recived[6];
	countryCode[2] = pMyData->Recived[7];
	countryCode[3] = '\0';
	pMyData->CurrencySign = countryCode;

	unsigned char valueMultiplier = (pMyData->Recived[8] << 16) | (pMyData->Recived[9] << 8) | pMyData->Recived[10];    // might be this or
	// unsigned char valueMultiplier =  pMyData->Recived[8] + pMyData->Recived[9] + pMyData->Recived[10];			  //might be this

	unsigned char numOfChannels = pMyData->Recived[11];

	if (valueMultiplier == 0)
	{
		// protocol version >= 6
		for (int i = 0; i < numOfChannels; i++)
			memcpy((void*)pMyData->VirtualChannels[i], (void*)pMyData->Recived[16 + 5 * numOfChannels + i * sizeof(int)], sizeof(int));
	}
	else
	{
		// protocol version < 6
		std::vector<unsigned char> channelValues;
		channelValues.insert(channelValues.end(), pMyData->Recived[12], numOfChannels);

		std::vector<unsigned char> channelSecurity;
		channelSecurity.insert(channelSecurity.end(), pMyData->Recived[12 + numOfChannels], numOfChannels);

		// std::vector<unsigned char> realValueMultiplier;
		// realValueMultiplier.insert(realValueMultiplier.end(), pMyData->Recived[12+ 2 * numOfChannels], 3);

		int i = 0;
		for (auto channel_value : channelValues)
		{
			pMyData->VirtualChannels[i] = channel_value * ((channelSecurity.at(i) == 0x02) ? valueMultiplier : 1);
			i++;
		}
	}
	SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
	MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillAssignmentOccured, NULL, NULL);
	SetNextState(pMyData, stateStatusRequest);

	pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::COMMUNICATION_ERROR, false);
	MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);

	return 0;
}
}    // namespace sspdrv
namespace ssp_control
{
int MainInThread(void* hInst)
{
	if (hInst == NULL)
		return -1;

	sspdrv::TMyData* pMyData = (sspdrv::TMyData*)hInst;
	try
	{
		while (true)
		{
			SDL_LockMutex(pMyData->pQuitThreadMutex);
			if (pMyData->QuitThread)
			{
				SDL_UnlockMutex(pMyData->pQuitThreadMutex);
				break;
			}
			SDL_UnlockMutex(pMyData->pQuitThreadMutex);

			pMyData->NextState(hInst);

			/* Zaspi za 150 ms. */
			usleep(150000);
		}
	}
	catch (...)
	{
		return -1;
	}

	return 0;
}


ThSspControl Init(sspdrv::TInitData* pInitData, std::unordered_set<std::string>& UsedPorts)
{
	int i, ret;
	char Command = (char)0x11;
	const char* ports_to_try[10] = { "/dev/ttyUSB0", "/dev/ttyUSB1", "/dev/ttyUSB2", "/dev/ttyUSB3", "/dev/ttyUSB4",
		                             "/dev/ttyS0",   "/dev/ttyS1",   "/dev/ttyS2",   "/dev/ttyS3",   "/dev/ttyS4" };

	if (pInitData == NULL)
		return NULL;

	sspdrv::TMyData* pMyData = NULL;

	try
	{
		pMyData = new sspdrv::TMyData;

		pMyData->pDeviceParams = new sspdrv::TDeviceParams;
		pMyData->pDeviceParams->pStructMutex = SDL_CreateMutex();
		pMyData->pDeviceParams->DenominationMask = pInitData->DenominationMask;
		pMyData->pDeviceParams->DenominationSecurityMask = pInitData->DenominationSecurityMask;

		pMyData->pDeviceParams->AcceptTickets = pInitData->AcceptTickets;
		pMyData->pDeviceParams->AcceptMoney = pInitData->AcceptMoney;
		pMyData->pDeviceParams->BarcodeCharactersCount = pInitData->BarcodeCharactersCount;
		pMyData->pDeviceParams->BarcodeType = pInitData->BarcodeType;

		pMyData->pDeviceStatus = new sspdrv::TDeviceStatus;
		pMyData->pDeviceStatus->pStructMutex = SDL_CreateMutex();
		pMyData->pDeviceStatus->DenominationMask = 0;
		pMyData->pDeviceStatus->DenominationSecurityMask = 0;
		pMyData->pDeviceStatus->EscrowData = 0;
		pMyData->pDeviceStatus->BootData[0] = (unsigned char)'\0';
		pMyData->BillAcceptorStatusFlags = pInitData->BillAcceptorStatusFlags;
		pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::INHIBIT, true);

		memset(pMyData->pDeviceStatus->TicketData, 0, 18);

		pMyData->PreviusState = NULL;
		while (!pMyData->CommandQueue.empty()) pMyData->CommandQueue.pop();
		pMyData->pCommandQueueMutex = SDL_CreateMutex();
		strcpy(pMyData->StateMessage, "Driver init...");
		strcpy(pMyData->StateMessageOld, "");

		pMyData->SDLEventCode_OnBillAccepted = pInitData->SDLEventCode_OnBillAccepted;
		pMyData->SDLEventCode_OnBillStacked = pInitData->SDLEventCode_OnBillStacked;
		pMyData->SDLEventCode_OnTimeoutBillReturned = pInitData->SDLEventCode_OnTimeoutBillReturned;
		pMyData->SDLEventCode_OnPowerOnWithBillInAcceptor = pInitData->SDLEventCode_OnPowerOnWithBillInAcceptor;
		pMyData->SDLEventCode_OnPowerOnWithBillInStacker = pInitData->SDLEventCode_OnPowerOnWithBillInStacker;
		pMyData->SDLEventCode_OnBillAssignmentOccured = pInitData->SDLEventCode_OnBillAssignmentOccured;

		pMyData->SDLEventCode_OnTicketAccepted = pInitData->SDLEventCode_OnTicketAccepted;
		pMyData->SDLEventCode_OnTicketStacked = pInitData->SDLEventCode_OnTicketStacked;
		pMyData->SDLEventCode_OnTimeoutTicketReturned = pInitData->SDLEventCode_OnTimeoutTicketReturned;

		pMyData->SDLEventData_Denomi = pInitData->SDLEventData_Denomi;
		pMyData->EscrowTimeout = pInitData->EscrowTimeOut;

		pMyData->pStateData = NULL;

		strncpy(pMyData->pSerialFileName, pInitData->pSerialFileName, 100);
		pMyData->hTrans = NULL;
		if (!strcmp(pMyData->pSerialFileName, "AUTO") || !strcmp(pMyData->pSerialFileName, "auto"))
		{
			/* Auto način - najdimo ustrezna serijska vrata. */
			for (i = 0; i < 10; i++)
			{
				if (UsedPorts.contains(ports_to_try[i]))
					continue;

				pMyData->hTrans = ssp_transport::Init((char*)ports_to_try[i], 0x00);

				if (pMyData->hTrans != NULL)
				{
					ret = ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);

					/* Če je bilo pošiljanje/prejemanje uspešno, break, pMyData->hTrans != NULL,
					   če ne, pa uničit instanco transport layerja in poskusit na novo na naslednjem portu. */
					if (ret >= 0)
					{
						strcpy(pMyData->pSerialFileName, ports_to_try[i]);
						TLOG(LogLoading, Info, "SSP: Device FOUND on %s", ports_to_try[i]);
						break;
					}
					else
					{
						ssp_transport::Destroy(pMyData->hTrans);
						pMyData->hTrans = NULL;
					}
				}
			}
		}
		else
		{
			/* Serijska vrata so nam bila podana. */
			pMyData->hTrans = ssp_transport::Init(pMyData->pSerialFileName, 0x00);
			if (pMyData->hTrans != NULL)
			{
				for (i = 0; i < 10; i++)
				{
					ret = ssp_transport::SendReceiveMessage(pMyData->hTrans, (unsigned char*)&Command, 1, pMyData->Recived, &pMyData->RecivedLength);
					if (ret >= 0)
						break;
				}

				if (ret < 0)
				{
					ssp_transport::Destroy(pMyData->hTrans);
					pMyData->hTrans = NULL;
				}
			}
		}
		/* Je na katerm izmed portov naprava, ki pravilno odgovarja? Če je, aplikaciji povemo, da je
		   bila komunikacija OK, v nasprotnem primeru pa Error. */
		if (pMyData->hTrans == NULL)
		{
			pMyData->BillAcceptorStatusFlags.SetFlag(EBillAcceptorStatus::COMMUNICATION_ERROR, true);
			MyUtils::PostSDLEvent(pMyData->SDLEventCode_OnBillStatusChanged, (void*)pMyData->BillAcceptorStatusFlags.Value(), NULL);
			return NULL;
		}

		UsedPorts.insert(pMyData->pSerialFileName);
		SetNextState(pMyData, sspdrv::stateResetDevice);
		pMyData->QuitThread = false;
		pMyData->pQuitThreadMutex = SDL_CreateMutex();
		pMyData->pSAThread = SDL_CreateThread(MainInThread, "sspcontrol", (void*)pMyData);
	}
	catch (...)
	{
		if (pMyData)
			delete (pMyData);
		return NULL;
	}

	return (ThSspControl)pMyData;
}

void Destroy(ThSspControl hInst)
{
	sspdrv::TMyData* pMyData = (sspdrv::TMyData*)hInst;

	if (hInst == NULL)
		return;
	try
	{
		printf("ssp_control: Waiting for thread to finish.\n");
		SDL_LockMutex(pMyData->pQuitThreadMutex);
		pMyData->QuitThread = true;
		SDL_UnlockMutex(pMyData->pQuitThreadMutex);
		SDL_WaitThread(pMyData->pSAThread, NULL);
		SDL_DestroyMutex(pMyData->pQuitThreadMutex);
		printf("ssp_control: Thread terminated.\n");

		ssp_transport::Destroy(pMyData->hTrans);

		SDL_DestroyMutex(pMyData->pCommandQueueMutex);

		SDL_DestroyMutex(pMyData->pDeviceParams->pStructMutex);
		SDL_DestroyMutex(pMyData->pDeviceStatus->pStructMutex);

		delete (pMyData->pDeviceParams);
		delete (pMyData->pDeviceStatus);

		delete (pMyData);

		// Vklopit to ob uporabi z aplikacijo.
	}
	catch (...)
	{
	}

	return;
}
}    // namespace ssp_control