/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON><PERSON>                                           <PERSON>
 <PERSON>   <PERSON><PERSON><PERSON>@localhost.localdomain                                           *
 *                                                                         *
 *   Copyright (C) 2021 by <PERSON><PERSON> (adaptacija za SSP)                  *
 *   <EMAIL>                                                      *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/


#ifdef HAVE_CONFIG_H
#include <config.h>
#endif
#include <SDL2/SDL.h>
#include <stdlib.h>
#include <unistd.h>

#include <iostream>

#include "drv/money/SSP/driver.h"
#include "drv/money/SSP/ssp_control.h"
#include "drv/money/SSP/ssp_control_l.h"

namespace sspdrv
{

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillStack1()
DESC:	Funkcija pozitivno potrdi stanje Escrow in sproži pospravljanje denarja v Stacker 1
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int BillStack1(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateAckEscrowWithStack);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: BillStack2()
DESC:	Funkcija pozitivno potrdi stanje Escrow in sproži pospravljanje denarja v Stacker 2
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int BillStack2(TSspDrv hInst)
{
	return BillStack1(hInst);
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: BillReturn()
DESC:	Funkcija negativno potrdi stanje Escrow in sproži zavrnitev denarja
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int BillReturn(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateAckEscrowWithReturn);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: ResetDevice()
DESC:	Funkcija resetira napravo
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int ResetDevice(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateResetDevice);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: DisableDevice()
DESC:	Funkcija onemogoči napravo
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int DisableDevice(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateDisableDevice);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: EnableDevice()
DESC:	Funkcija omogoči napravo
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int EnableDevice(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateEnableDevice);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetLastEscrowData()
DESC:	Funkcija vrne informacijo o vstavljenem bankovcu, če je naprava v stanju ESCROW
PARAMS: hInst - handle na instanco komunikacijskega driverja
RESULT: 00 - no bill accepted yet
        01 - denomination 1
        02 - denomination 2
        03 - denomination 3
        04 - denomination 4
        05 - denomination 5
        06 - denomination 6
        07 - denomination 7
        08 - denomination 8
        <0 - failure
---------------------------------------------------------------------------------------------------*/
int GetLastEscrowData(TSspDrv hInst)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	unsigned char EscrowData;

	if (pMyData->pDeviceStatus != NULL)
	{
		SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
		EscrowData = GetEventData(pMyData, pMyData->EscrowNoteIndex);
		SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
		return EscrowData;
	}

	return 0;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDeviceData()
DESC:	Funkcija vrne podatke o verziji naprave
PARAMS: hInst - pointer na handle instance komunikacijskega driverja
                ModelCode - kazalec na bufffer za ModelCode (10Byte ASCII)
                CountryCode - kazalec na bufffer za CountryCode (10Byte ASCII)
                ModelNumber - kazalec na bufffer za ModelNumber (10Byte ASCII)
                StackerType - kazalec na bufffer za StackerType (10Byte ASCII)
                InterfaceType - kazalec na bufffer za InterfaceType (10Byte ASCII)
                SoftwareVersion - kazalec na bufffer za SoftwareVersion (10Byte ASCII)
                SoftwareDate - kazalec na bufffer za SoftwareDate (10Byte ASCII)
                CRC - kazalec na bufffer za CRC (5Byte ASCII)
                BootData - kazalec na bufffer za BootData (4Byte ASCII)
RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int GetDeviceData(TSspDrv hInst, char* ModelCode, char* CountryCode, char* ModelNumber, char* StackerType, char* InterfaceType, char* SoftwareVersion, char* SoftwareDate,
                  char* CRC, char* BootData)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
	/* TODO: Ta funkcija naj nekaj naredi. */
	SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);

	return 0;
}

bool GetBillAcceptorChannelValues(TSspDrv hInst, std::vector<int>* channelData, std::string& currencySign)
{
	if (hInst == NULL)
		return RET_WRONG_INPUT_PARAMS;

	TMyData* pMyData = (TMyData*)hInst;

	if (pMyData->pDeviceStatus != NULL)
	{
		SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
		currencySign = pMyData->CurrencySign;
		for (auto channel : pMyData->VirtualChannels) channelData->emplace_back(channel);
		SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
	}
	return RET_SUCCESS;
}

}    // namespace sspdrv

/*
int main(int argc, char* argv[])
{
    TInitData* pInitData = new TInitData;
    void* p;
    int NoteIndex;
    SDL_Event test_event;
    if (argc < 2)
        strcpy(pInitData->pSerialFileName, "AUTO");
    else if (argc == 2)
        strcpy(pInitData->pSerialFileName, argv[1]);
    else
    {
        printf("Invalid number of parameters\n");
        return -1;
    }
    pInitData->SDLEventCode_OnBillAccepted = 0x8001;
    pInitData->SDLEventCode_OnBillStacked = 0x8002;
    pInitData->SDLEventCode_OnTimeoutBillReturned = 0x8003;
    pInitData->SDLEventCode_OnStackerOpen = 0x8009;
    pInitData->SDLEventCode_OnStackerClose = 0x8004;
    pInitData->SDLEventCode_OnJamInStacker = 0x8005;
    pInitData->SDLEventCode_OnJamInAcceptor = 0x8006;
    pInitData->SDLEventCode_OnCommunicationError = 0x8007;
    pInitData->SDLEventCode_OnCommunicationOK = 0x8008;
    pInitData->SDLEventData_Denomi1 = 5;
    pInitData->SDLEventData_Denomi2 = 5;
    pInitData->SDLEventData_Denomi3 = 10;
    pInitData->SDLEventData_Denomi4 = 20;
    pInitData->SDLEventData_Denomi5 = 50;
    pInitData->SDLEventData_Denomi6 = 100;
    pInitData->SDLEventData_Denomi7 = 200;
    pInitData->SDLEventData_Denomi8 = 500;
    pInitData->SDLEventData_Denomi9 = 1000;
    pInitData->EscrowTimeOut = 15000;
    p = control::Init(pInitData);
    while (true)
    {
        sleep(1);
        while (SDL_PollEvent(&test_event))
        {
            if (test_event.type != SDL_USEREVENT)
                continue;
            if (test_event.user.code == pInitData->SDLEventCode_OnBillAccepted)
            {
                NoteIndex = (int)(uintptr_t)test_event.user.data1;
                std::cout << "Bill with denomination " << NoteIndex << " accepted\n";
            }
            else if (test_event.user.code == pInitData->SDLEventCode_OnBillStacked)
            {
                NoteIndex = (int)(uintptr_t)test_event.user.data1;
                std::cout << "Bill with denomination " << NoteIndex << " stacked\n";
            }
            else if (test_event.user.code == pInitData->SDLEventCode_OnTimeoutBillReturned)
            {
                NoteIndex = (int)(uintptr_t)test_event.user.data1;
                std::cout << "Timeout and /or bill with denomination " << NoteIndex << " returned\n";
            }
            else if (test_event.user.code == pInitData->SDLEventCode_OnStackerOpen)
                std::cout << "Stacker open\n";
            else if (test_event.user.code == pInitData->SDLEventCode_OnStackerClose)
                std::cout << "Stacker close\n";
            else if (test_event.user.code == pInitData->SDLEventCode_OnJamInStacker)
                std::cout << "Jam in stacker\n";
            else if (test_event.user.code == pInitData->SDLEventCode_OnJamInAcceptor)
                std::cout << "Jam in acceptor\n";
            else if (test_event.user.code == pInitData->SDLEventCode_OnCommunicationError)
                std::cout << "Communication error\n";
            else if (test_event.user.code == pInitData->SDLEventCode_OnCommunicationOK)
                std::cout << "Communication OK\n";
            sleep(1);
        }
    }
    std::cout << "Press ENTER to exit...";
    std::cin.ignore();
    std::cout << "\n";
    std::cout << "Destroying control layer class instance... ";
    control::Destroy(p);
    std::cout << "done\n";
    return 0;
}
*/