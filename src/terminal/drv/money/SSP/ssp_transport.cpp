/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON><PERSON>   <PERSON>
 *   Mi<PERSON><PERSON>@localhost.localdomain   *
 *                                                                         *
 *   This program is free software; you can redistribute it and/or modify  *
 *   it under the terms of the GNU General Public License as published by  *
 *   the Free Software Foundation; either version 2 of the License, or     *
 *   (at your option) any later version.                                   *
 *                                                                         *
 *   This program is distributed in the hope that it will be useful,       *
 *   but WITHOUT ANY WARRANTY; without even the implied warranty of        *
 *   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the         *
 *   GNU General Public License for more details.                          *
 *                                                                         *
 *   You should have received a copy of the GNU General Public License     *
 *   along with this program; if not, write to the                         *
 *   Free Software Foundation, Inc.,                                       *
 *   59 Temple Place - Suite 330, Boston, MA  02111-1307, USA.             *
 ***************************************************************************/
#include "drv/money/SSP/ssp_transport.h"

#include <fcntl.h>
#include <stdlib.h>
#include <unistd.h>

#include <iostream>

#include "Cryptography.h"
#include "drv/money/SSP/ssp_transport_l.h"

const uint16_t CRC_16_SSP_TAB[] = {
	0x0000, 0x8005, 0x800F, 0x000A, 0x801B, 0x001E, 0x0014, 0x8011, 0x8033, 0x0036, 0x003C, 0x8039, 0x0028, 0x802D, 0x8027, 0x0022, 0x8063, 0x0066, 0x006C, 0x8069,
	0x0078, 0x807D, 0x8077, 0x0072, 0x0050, 0x8055, 0x805F, 0x005A, 0x804B, 0x004E, 0x0044, 0x8041, 0x80C3, 0x00C6, 0x00CC, 0x80C9, 0x00D8, 0x80DD, 0x80D7, 0x00D2,
	0x00F0, 0x80F5, 0x80FF, 0x00FA, 0x80EB, 0x00EE, 0x00E4, 0x80E1, 0x00A0, 0x80A5, 0x80AF, 0x00AA, 0x80BB, 0x00BE, 0x00B4, 0x80B1, 0x8093, 0x0096, 0x009C, 0x8099,
	0x0088, 0x808D, 0x8087, 0x0082, 0x8183, 0x0186, 0x018C, 0x8189, 0x0198, 0x819D, 0x8197, 0x0192, 0x01B0, 0x81B5, 0x81BF, 0x01BA, 0x81AB, 0x01AE, 0x01A4, 0x81A1,
	0x01E0, 0x81E5, 0x81EF, 0x01EA, 0x81FB, 0x01FE, 0x01F4, 0x81F1, 0x81D3, 0x01D6, 0x01DC, 0x81D9, 0x01C8, 0x81CD, 0x81C7, 0x01C2, 0x0140, 0x8145, 0x814F, 0x014A,
	0x815B, 0x015E, 0x0154, 0x8151, 0x8173, 0x0176, 0x017C, 0x8179, 0x0168, 0x816D, 0x8167, 0x0162, 0x8123, 0x0126, 0x012C, 0x8129, 0x0138, 0x813D, 0x8137, 0x0132,
	0x0110, 0x8115, 0x811F, 0x011A, 0x810B, 0x010E, 0x0104, 0x8101, 0x8303, 0x0306, 0x030C, 0x8309, 0x0318, 0x831D, 0x8317, 0x0312, 0x0330, 0x8335, 0x833F, 0x033A,
	0x832B, 0x032E, 0x0324, 0x8321, 0x0360, 0x8365, 0x836F, 0x036A, 0x837B, 0x037E, 0x0374, 0x8371, 0x8353, 0x0356, 0x035C, 0x8359, 0x0348, 0x834D, 0x8347, 0x0342,
	0x03C0, 0x83C5, 0x83CF, 0x03CA, 0x83DB, 0x03DE, 0x03D4, 0x83D1, 0x83F3, 0x03F6, 0x03FC, 0x83F9, 0x03E8, 0x83ED, 0x83E7, 0x03E2, 0x83A3, 0x03A6, 0x03AC, 0x83A9,
	0x03B8, 0x83BD, 0x83B7, 0x03B2, 0x0390, 0x8395, 0x839F, 0x039A, 0x838B, 0x038E, 0x0384, 0x8381, 0x0280, 0x8285, 0x828F, 0x028A, 0x829B, 0x029E, 0x0294, 0x8291,
	0x82B3, 0x02B6, 0x02BC, 0x82B9, 0x02A8, 0x82AD, 0x82A7, 0x02A2, 0x82E3, 0x02E6, 0x02EC, 0x82E9, 0x02F8, 0x82FD, 0x82F7, 0x02F2, 0x02D0, 0x82D5, 0x82DF, 0x02DA,
	0x82CB, 0x02CE, 0x02C4, 0x82C1, 0x8243, 0x0246, 0x024C, 0x8249, 0x0258, 0x825D, 0x8257, 0x0252, 0x0270, 0x8275, 0x827F, 0x027A, 0x826B, 0x026E, 0x0264, 0x8261,
	0x0220, 0x8225, 0x822F, 0x022A, 0x823B, 0x023E, 0x0234, 0x8231, 0x8213, 0x0216, 0x021C, 0x8219, 0x0208, 0x820D, 0x8207, 0x0202
};

namespace ssp_transport
{

/*---------------------------------------------------------------------------------------------------
FUNCTION: SendReceiveMessage()
DESC:	Funkcija pokliÄe SendMessage, poÄaka in pokliÄe ReciveMsg
PARAMS: handle,
                TMessage
                Retries - how many times should the function try to get the valid answer
                SendReciveDelay - delay betwen send and recive(ms)
   RESULT: >0 - success with retries; 0 - success; <0 - failure
---------------------------------------------------------------------------------------------------*/
int SendReceiveMessage(ThSspTrans hInst, unsigned char* pDataToSend, unsigned short SendLength, unsigned char* pDataRecived, unsigned short* RecivedLength)
{
	if (!hInst || !pDataToSend || !pDataRecived || !RecivedLength || !SendLength)
		return -1;

	unsigned short RetriesCounter = 0;

	while (RetriesCounter < MAX_RETRIES)
	//****** send message, wait receive message*****************//
	{
		RetriesCounter++;
		if (!SendMessage(hInst, pDataToSend, SendLength) && !usleep(100000) && !ReciveMsg(hInst, pDataRecived, RecivedLength))
		{
			return (RetriesCounter > 1);
		}
		else
		{
			printf("SendRecive error. Retrying.. %d of %d\n", RetriesCounter, MAX_RETRIES);
			usleep(5000);
		}
	}
	return (RetriesCounter > 1);
}

/*------------------serial port programming linux----------------------------------------------------------------
Funkcija: SendMessage()
DESC: Funkcija zgradi sporočilo za prenos proti napravi in ga pošlje na serijski port
PARAMS: hInst- To je nas komunikacijski handle
        pMyData- Pointer na podatke, ki jih želimo poslati
        DataLength- Dolzina podatkih ,ki jih pošiljamo
RESULT:
------------------------------------------------------------------------------------*/
int SendMessage(ThSspTrans hInst, unsigned char* pData, unsigned short DataLength)
{
	if (NULL == hInst || NULL == pData || 0 == DataLength)
		return -1;

	TMyData* pMyData = (TMyData*)hInst;
	unsigned char* pPacket = (unsigned char*)malloc(DataLength + HEAD_LENGTH + TAIL_LENGTH);
	int SendingWatchdog = 0;
	unsigned short BytesWritten = 0;
	unsigned short MessageLength = 0;

	if (NULL == pPacket)
		return -2;

	// zgradimo paket(po navodilih v SSP Smileyu)

	pPacket[0] = 0x7F;
	pPacket[1] = (pMyData->seq << 7) | (pMyData->SlaveId & 0x7F);    // glava je vedno 1(ga damo na bool vrednost)
	pPacket[2] = DataLength;
	for (int i = 0; i < DataLength; i++) pPacket[i + 3] = pData[i];
	unsigned short moj_crc = 0xffff;
	moj_crc = crypto::Crc_16(CRC_16_SSP_TAB, moj_crc, &pPacket[1], DataLength + 2);    // brez STX
	pPacket[DataLength + 3] = (unsigned char)moj_crc;    // dolžina našega paketa + prvi trije paketi
	pPacket[DataLength + 4] = ((unsigned char*)&moj_crc)[1];

	MessageLength = DataLength + 5;
	while (BytesWritten < MessageLength)
	{
		int tmp = write(pMyData->hSerial, pPacket, MessageLength);
		BytesWritten += tmp;
		if (SendingWatchdog++ > MAX_SEND_RETRIES || tmp < 1)
			return -3;
	}
	/*
	printf("Sending: ");
	for(int i =0; i < MessageLength; i++)
	   printf( "%x|", pPacket[i] );
	printf("\n");
	*/
	free(pPacket);
	return 0;
}
/*----------------------------------------------------------------------------------
Funkcija: ReciveMsg()
DESC: Funkcija prebere sporocilo iz naprbuffer[%d]=%x"ave, izlušči vsebino in vrne vsebino sporocila
RESULT: hInst- To je nas komunikacijski handle
        pDest- Pointer na buffer kamor bomo zapisali prejete podatke
        DataLength- Ob klicu gre za dolžino buferja(pDest), po klicu pa dolžino prejetih
                    podatkov
------------------------------------------------------------------------------------*/
#define MAX_READ_RETRIES 50

int ReciveMsg(ThSspTrans hInst, unsigned char* pDest, unsigned short* DataLength)
{
	if (NULL == hInst || NULL == pDest || 0 == DataLength)
		return -1;

	TMyData* pMyData = (TMyData*)hInst;

	unsigned char* pReciveMsg = (unsigned char*)malloc(261);
	int ReceivingWatchdog = 0;
	int BytesRead = 0;

	if (NULL == pReciveMsg)
		return -1;

	// trimamo vhodni buffer
	pReciveMsg[0] = 0;
	while (pReciveMsg[0] != 0x7F)
		if (read(pMyData->hSerial, &pReciveMsg[0], 1) < 1)
		{
			printf("No data!.\n");
			return -2;
		}
	// preberemo adreso in primerjamo, če je pošiljatelj pravi
	pReciveMsg[1] = pMyData->SlaveId - 1;
	while (pMyData->SlaveId != (pReciveMsg[1] & 0x7F))
		if (read(pMyData->hSerial, &pReciveMsg[1], 1) < 1)
		{
			printf("Invalid slave address!.\n");
			return -3;
		}

	// preberemo dolžino paketa
	while (!read(pMyData->hSerial, &pReciveMsg[2], 1))
	{
		if (ReceivingWatchdog++ > MAX_READ_RETRIES)
			return -4;
	}

	// beremo paket do konca
	ReceivingWatchdog = 0;
	while (BytesRead < pReciveMsg[2] + 2)
	{
		BytesRead += read(pMyData->hSerial, &pReciveMsg[BytesRead + 3], pReciveMsg[2] + 2 - BytesRead);
		if (ReceivingWatchdog++ > MAX_READ_RETRIES)
		{
			printf("Timeout while receiving packet body!.\n");
			return -4;
		}
	}

	// preverimo crc
	unsigned short moj_crc = 0xFFFF;
	unsigned short input_crc = (unsigned short)pReciveMsg[pReciveMsg[2] + 3];
	input_crc |= ((unsigned short)pReciveMsg[pReciveMsg[2] + 4]) << 8;
	moj_crc = crypto::Crc_16(CRC_16_SSP_TAB, moj_crc, &pReciveMsg[1], pReciveMsg[2] + 2);
	if (moj_crc != input_crc)    // crc se ne ujema z nasim, potem je napaka
	{
		printf("CRC Errror in recived packet.\n");
		return -5;
	}

	pMyData->seq = !pMyData->seq;    // logična negacija

	// skopiramo podatke klicatelju
	for (int i = 0; i < pReciveMsg[2]; i++) pDest[i] = pReciveMsg[i + 3];
	*DataLength = pReciveMsg[2];

	/* printf("Recived: ");
	for(int i =0; i < BytesRead+3; i++)
	   printf( "%x|", pReciveMsg[i] );
	printf("\n"); */

	free(pReciveMsg);
	return 0;
}


ThSspTrans Init(char* Device, unsigned char SlaveId)
{
	// preverjanje vhodnih parametrov

	if (NULL == Device || SlaveId > 0x7D)
		return NULL;

	TMyData* pMyData = new TMyData;

	pMyData->SlaveId = SlaveId;
	pMyData->seq = 1;

	pMyData->hSerial = open(Device, O_RDWR | O_NOCTTY | O_NONBLOCK);

	if (pMyData->hSerial < 0)
	{
		perror(Device);
		return NULL;
	}

	else
	{
		// std::cout << "Port je odprt:" << Device << std::endl;

		/* shranim stare nastavitve porta */
		pMyData->oldopt = new struct termios;
		tcgetattr(pMyData->hSerial, pMyData->oldopt);

		pMyData->newopt = new struct termios;
		bzero(pMyData->newopt, sizeof(struct termios));    // vse damo na nullo
		pMyData->newopt->c_cflag = B9600 | CS8 | CLOCAL | CREAD | CSTOPB;    // control option
		pMyData->newopt->c_oflag = 0;    // out put option
		tcflush(pMyData->hSerial, TCIOFLUSH);
		tcsetattr(pMyData->hSerial, TCSANOW, pMyData->newopt);
	}

	return (ThSspTrans)pMyData;
}


void Destroy(ThSspTrans hInst)
{
	if (NULL == hInst)
		return;
	try
	{
		close(((TMyData*)hInst)->hSerial);
		tcsetattr(((TMyData*)hInst)->hSerial, TCSANOW, ((TMyData*)hInst)->oldopt);


		delete (((TMyData*)hInst)->oldopt);
		delete (((TMyData*)hInst)->newopt);
		delete (((TMyData*)hInst));
	}

	catch (...)
	{
	}

	return;
}


}    // namespace ssp_transport
