#include <SDL2/SDL.h>

#include "drv/money/ICTProto/ict_drv.h"
#include "drv/money/ICTProto/ict_drv_l.h"

namespace ictdrv
{
/*---------------------------------------------------------------------------------------------------
FUNCTION: BillStack1()

DESC:	Funkcija pozitivno potrdi stanje Escrow in sproži pospravljanje denarja v Stacker 1

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int BillStack1(TICTdrv hInst)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateAckEscrowWithStack1);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: BillReturn()

DESC:	Funkcija negativno potrdi stanje Escrow in sproži zavrnitev denarja

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int BillReturn(TICTdrv hInst)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateAckEscrowWithReturn);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: DisableDevice()

DESC:	Funkcija onemogoči napravo

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int DisableDevice(TICTdrv hInst)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateDisableDevice);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: EnableDevice()

DESC:	Funkcija omogoči napravo

PARAMS: hInst - pointer na handle instance komunikacijskega driverja

RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int EnableDevice(TICTdrv hInst)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pCommandQueueMutex);
	pMyData->CommandQueue.push((TpSAFunction)stateEnableDevice);
	SDL_UnlockMutex(pMyData->pCommandQueueMutex);
	return RET_SUCCESS;
}

/*---------------------------------------------------------------------------------------------------
FUNCTION: GetLastEscrowData()

DESC:	Funkcija vrne informacijo o vstavljenem bankovcu, če je naprava v stanju ESCROW

PARAMS: hInst - handle na instanco komunikacijskega driverja

RESULT: 00 - no bill accepted yet
                01 - denomination 1
                02 - denomination 2
                03 - denomination 3
                04 - denomination 4
                05 - denomination 5
                06 - denomination 6
                07 - denomination 7
                08 - denomination 8

                <0 failure
---------------------------------------------------------------------------------------------------*/
int GetLastEscrowData(TICTdrv hInst)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	unsigned char EscrowData;

	if (NULL != pMyData->pDeviceStatus)
	{
		SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
		EscrowData = GetEventData(pMyData, pMyData->pDeviceStatus->EscrowData);    // is this ok
		SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
		return EscrowData;
	}
	return 0;
}


/*---------------------------------------------------------------------------------------------------
FUNCTION: GetDeviceData()

DESC:	Funkcija vrne podatke o verziji naprave

PARAMS: hInst - pointer na handle instance komunikacijskega driverja
                ModelCode - kazalec na bufffer za ModelCode (10Byte ASCII)
                CountryCode - kazalec na bufffer za CountryCode (10Byte ASCII)
                ModelNumber - kazalec na bufffer za ModelNumber (10Byte ASCII)
                StackerType - kazalec na bufffer za StackerType (10Byte ASCII)
                InterfaceType - kazalec na bufffer za InterfaceType (10Byte ASCII)
                SoftwareVersion - kazalec na bufffer za SoftwareVersion (10Byte ASCII)
                SoftwareDate - kazalec na bufffer za SoftwareDate (10Byte ASCII)
                CRC - kazalec na bufffer za CRC (5Byte ASCII)
                BootData - kazalec na bufffer za BootData (4Byte ASCII)

RESULT: 0 success; -2 device busy; -1 error (glej define v cctalk_drv_cmd.h)
---------------------------------------------------------------------------------------------------*/
int GetDeviceData(TICTdrv hInst, char* ModelCode, char* CountryCode, char* ModelNumber, char* StackerType, char* InterfaceType, char* SoftwareVersion, char* SoftwareDate,
                  char* CRC, char* BootData)
{
	if (NULL == hInst)
		return RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
	if (NULL != ModelCode)
		strcpy(ModelCode, "");
	if (NULL != CountryCode)
		strcpy(CountryCode, "");
	if (NULL != ModelNumber)
		strcpy(ModelNumber, "");
	if (NULL != StackerType)
		strcpy(StackerType, "");
	if (NULL != InterfaceType)
		strcpy(InterfaceType, "");
	if (NULL != SoftwareVersion)
		strcpy(SoftwareVersion, "");
	if (NULL != SoftwareDate)
		strcpy(SoftwareDate, "");
	if (NULL != CRC)
		strcpy(CRC, "");
	if (NULL != BootData)
		strcpy(BootData, "");
	SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
	return 0;
}

/*---------------------------------------------------------------------------------------------------
 FUNCTION: IsCommunicationOK()

 DESC: Funkcija vrne informacijo ali je komunkacija ok

 PARAMS: hInst - handle na instanco komunikacijskega driverja

 RESULT: true/false
 ---------------------------------------------------------------------------------------------------*/
bool IsCommunicationOK(TICTdrv hInst)
{
	if (NULL == hInst)
		return false;    // RET_WRONG_INPUT_PARAMS;
	TMyData* pMyData = (TMyData*)hInst;

	bool result = false;
	if (NULL != pMyData->pDeviceStatus)
	{
		SDL_LockMutex(pMyData->pDeviceStatus->pStructMutex);
		result = pMyData->pDeviceStatus->CommunicationEstablished;
		SDL_UnlockMutex(pMyData->pDeviceStatus->pStructMutex);
	}
	return result;
}


}    // namespace ictdrv
