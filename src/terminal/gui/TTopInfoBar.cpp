#include "gui/TTopInfoBar.h"

#include "MyUtils.h"
#include "TIGPlatformApp.h"
#include "TPanelBase.h"
#include "TotallyTemporaryDesignTokens.h"

TTopInfoBar::TTopInfoBar(Container* pParent) : Container()
{
	if (pParent)
		pParent->add(this);

	const Vector2D LogoSize = Vector2D(52, 48);

	setId("top-info-bar");

	logoBanner = new TStyleButton2(this, LocalizedMessage());
	logoBanner->setId("logo");
	logoBanner->setSize(LogoSize);
	logoBanner->setAllStatesToImage(Image::GetImage("splashLogo.png"));
	logoBanner->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_FIT;
	logoBanner->setOrigin(0.f, 0.f);
	logoBanner->setPosition(56, 32);
	logoBanner->Alpha = 0.85f;

	LayoutContainer* gameInfoHolder = new LayoutContainer();
	gameInfoHolder->setId("game-info-holder");
	gameInfoHolder->bResizeToFitLayout = { true, true };
	gameInfoHolder->setSize(Vector2D(300, 64));
	gameInfoHolder->setPosition(logoBanner->getDimension().right() + 24, 28);
	add(gameInfoHolder);

	GridLayout* gameInfoLayout = gameInfoHolder->setLayout<GridLayout>();
	gameInfoLayout->AddRows({ { 0.5f }, { 0.5f } });

	plGameName = new Label(White);
	plGameName->setId("game-name-label");
	plGameName->mTypography = tempDesignTokens::TitleFont_18;
	plGameName->TextColor = tempDesignTokens::GoldTextColor;
	plGameName->setSize(300, 28);
	plGameName->setAlignment(align::LEFT_CENTER);
	gameInfoHolder->add(plGameName);
	gameInfoLayout->Set(plGameName, { 0, 0, 1, 1, EChildAlignment::Center });

	plGameMinMax = new Label(White);
	plGameMinMax->setId("game-min-max-label");
	plGameMinMax->mTypography = tempDesignTokens::MainFont_400_16;
	plGameMinMax->setSize(300, 28);
	plGameMinMax->setAlignment(align::LEFT_CENTER);
	gameInfoHolder->add(plGameMinMax);
	gameInfoLayout->Set(plGameMinMax, { 0, 1, 1, 1, EChildAlignment::Center });

	LayoutContainer* topBarHolder = new LayoutContainer();
	topBarHolder->setId("top-bar-holder");
	topBarHolder->setHeight(64);
	topBarHolder->setPosition({ 1920, 24 });
	topBarHolder->setOrigin(1.f, 0.f);
	topBarHolder->bResizeToFitLayout = { true, false };
	auto* topBarHolderLayout = topBarHolder->setLayout<StackLayout>();
	topBarHolderLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	topBarHolderLayout->Alignment = EChildAlignment::Center;
	topBarHolderLayout->Padding = Vector2D(24, 0);
	topBarHolderLayout->bUsePaddingBeforeFirstAndAfterLast = true;
	add(topBarHolder);

	LayoutContainer* creditHolder = new LayoutContainer();
	creditHolder->setId("credit-holder");
	creditHolder->setHeight(64);
	creditHolder->bResizeToFitLayout = { true, false };
	creditHolder->Background = tempDesignTokens::TopInfoBarBackgroundColor;
	creditHolder->OutlineStyle = Outline({}, 0_px, 1_rem, 1_px);

	auto* creditLayout = creditHolder->setLayout<StackLayout>();
	creditLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	creditLayout->Alignment = EChildAlignment::Center;
	creditLayout->Padding = Vector2D(6, 0);
	creditLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	LayoutContainer* creditLabelRow = new LayoutContainer();
	creditLabelRow->setId("credit-label-row");
	creditLabelRow->bResizeToFitLayout = { true, true };

	auto* creditRowLayout = creditLabelRow->setLayout<StackLayout>();
	creditRowLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	creditRowLayout->Alignment = EChildAlignment::Center;

	pVisibilityIcon = new TStyleButton2(creditLabelRow, LocalizedMessage(), White);
	pVisibilityIcon->setId("visibility-icon");
	pVisibilityIcon->setOrigin(0.f, 0.5f);
	pVisibilityIcon->setAllStatesToImage(Image::GetImage("eye_crossed.png"));
	pVisibilityIcon->setSize(52);
	pVisibilityIcon->OutlineStyle.SetCornerRadius(Dimension(1.f, EDimensionUnit::rem));
	pVisibilityIcon->PressedBackground = tempDesignTokens::TopInfoBarBackgroundExpandedColor;
	pVisibilityIcon->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
	pVisibilityIcon->OnPressed += [this]() {
		bUserHiddenCreditValue = !bUserHiddenCreditValue;
		if (bUserHiddenCreditValue)
		{
			plCreditValueCurrency->setCaption(LocalizedMessage("*****"));
			pVisibilityIcon->setAllStatesToImage(Image::GetImage("eye.png"));
		}
		else
		{
			pVisibilityIcon->setAllStatesToImage(Image::GetImage("eye_crossed.png"));
			RefreshCreditMeter(credit::CreditArray(), MenuGUI()->pClient->BalanceManager()->GetBalance());
		}
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);

		json params(json::value_t::object);
		params["name"] = "creditVisibility";
		params["value"] = !bUserHiddenCreditValue;    // inverted for frontend
		pCommander->NanoAction(ENanoPlayerAction::SetParam, params);
	};

	const auto proclaimHatWidgetFunction = [](Widget* hatWidget, const LocalizedMessage& DisplayValue) {
		if (!hatWidget)
		{
			hatWidget = new LayoutContainer();

			LayoutContainer* hatContainer = dynamic_cast<LayoutContainer*>(hatWidget);
			hatContainer->setId("hat-container");
			hatContainer->bResizeToFitLayout = true;

			StackLayout* hatLayout = hatContainer->setLayout<StackLayout>();
			hatLayout->Direction = EStackLayoutDirection::HORIZONTAL;
			hatLayout->Alignment = EChildAlignment::Center;

			Label* styledBalanceLabel = new Label(LocalizedMessage(BALANCE_STRING));
			styledBalanceLabel->setId("balance-label");
			styledBalanceLabel->mTypography = tempDesignTokens::MainFont_400_20;
			styledBalanceLabel->setResizeMode(Label::AUTOSIZE);
			styledBalanceLabel->TextColor = tempDesignTokens::GreyTextColor;
			styledBalanceLabel->setPadding(Vector2D(10.f, 0));
			hatContainer->add(styledBalanceLabel);

			Label* styledCreditValueLabel = new Label(DisplayValue);
			styledCreditValueLabel->setId("credit-value-label");
			styledCreditValueLabel->mTypography = tempDesignTokens::MainFont_400_16;
			styledCreditValueLabel->setAlignment(align::LEFT_CENTER);
			styledCreditValueLabel->setResizeMode(Label::AUTOSIZE);
			styledCreditValueLabel->TextColor = tempDesignTokens::GreyTextColor;
			hatContainer->add(styledCreditValueLabel);
		}
		else
		{
			Label* styledCreditLabel = dynamic_cast<Label*>(hatWidget->findWidgetById("credit-value-label"));
			if (styledCreditLabel)
			{
				styledCreditLabel->setCaption(LocalizedMessage(CREDIT_STRING, [DisplayValue](const std::string& str, ELanguage lang) -> std::string {
					return std::format("(1 {} = {})", str, DisplayValue.Get(lang));
				}));
			}
		}
		return hatWidget;
	};
	CreateDenominationDropdown(creditLabelRow, 48, proclaimHatWidgetFunction, LocalizedMessage(DENOMINATION_TIP_STRING));

	plCreditValueCurrency = new Label(LocalizedMessage(CREDIT_STRING));
	plCreditValueCurrency->setId("credit-value-currency");

	plCreditValueCurrency->setWidth(250.f);
	plCreditValueCurrency->mTypography = tempDesignTokens::MainFont_600_24;
	plCreditValueCurrency->TextColor = tempDesignTokens::GoldTextColor;
	plCreditValueCurrency->setForceUpperCase(true);
	plCreditValueCurrency->setResizeMode(Label::NONE, Label::AUTOSIZE);
	creditLabelRow->add(plCreditValueCurrency);

	creditHolder->add(creditLabelRow);

	pQRPayoutBtn = new Button(LocalizedMessage(PLAY_AWAY_STRING));
	pQRPayoutBtn->setId("qr-payout-btn");
	pQRPayoutBtn->Text->mTypography = tempDesignTokens::MainFont_600_16;
	pQRPayoutBtn->Text->TextColor = tempDesignTokens::GoldTextColor;
	pQRPayoutBtn->Text->setForceUpperCase(true);
	pQRPayoutBtn->setSize({ 156, 56 });
	pQRPayoutBtn->Background = tempDesignTokens::Buttons_Gradient;
	pQRPayoutBtn->PressedBackground = tempDesignTokens::Buttons_Gradient_Pressed;
	pQRPayoutBtn->OutlineStyle.SetCornerRadius(Dimension(1.f, EDimensionUnit::rem));
	pQRPayoutBtn->OnPressed += [this]() {
		OnQRPayoutBtnPressed();
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
	};
	pQRPayoutBtn->OnVisibleChanged += [creditHolder](Widget* w, bool bVisible) {
		creditHolder->markLayoutDirty();
	};

	creditHolder->add(pQRPayoutBtn);
	pBtnPayout = new Button(LocalizedMessage(CASH_OUT_STRING));
	pBtnPayout->setId("btn-payout");
	pBtnPayout->Text->mTypography = tempDesignTokens::MainFont_600_16;
	pBtnPayout->Text->TextColor = tempDesignTokens::GoldTextColor;
	pBtnPayout->Text->setForceUpperCase(true);
	pBtnPayout->setSize({ 156, 56 });
	pBtnPayout->Background = tempDesignTokens::Buttons_Gradient;
	pBtnPayout->PressedBackground = tempDesignTokens::Buttons_Gradient_Pressed;
	pBtnPayout->OutlineStyle.SetCornerRadius(Dimension(1.f, EDimensionUnit::rem));
	pBtnPayout->OnPressed += [this]() {
		OnPayoutPressed();
	};
	creditHolder->add(pBtnPayout);

	creditHolder->setOrigin({ 1.0f, 0.f });
	creditHolder->setPosition({ 1689, 24 });
	topBarHolder->add(creditHolder);

	MenuGUI()->pClient->BalanceManager()->OnBalanceChanged += std::bind(&TTopInfoBar::RefreshCreditMeter, this, std::placeholders::_1, std::placeholders::_2);
	RefreshCreditMeter(credit::CreditArray(), MenuGUI()->pClient->BalanceManager()->GetBalance());

	// CREDITS
	LayoutContainer* userSettingsHolder = new LayoutContainer();
	userSettingsHolder->setId("player-balance");
	userSettingsHolder->bResizeToFitLayout = { true, false };
	userSettingsHolder->Background = tempDesignTokens::TopInfoBarBackgroundColor;
	userSettingsHolder->OutlineStyle = Outline({}, 0_px, 1_rem, 1_px);
	StackLayout* userSettingsLayout = userSettingsHolder->setLayout<StackLayout>();
	userSettingsLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	userSettingsLayout->Alignment = EChildAlignment::Center;
	userSettingsLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	pVolumeSliderContainer = new LayoutContainer();
	pVolumeSliderContainer->setId("volume-slider-container");
	pVolumeSliderContainer->setSize(414, 56);
	pVolumeSliderContainer->setOrigin(1.f, 0.5f);
	userSettingsHolder->add(pVolumeSliderContainer);
	pVolumeSliderContainer->bResizeToFitLayout = { true, false };
	userSettingsLayout->setPaddingFor(pVolumeSliderContainer, { 4, 0, 0, 0 });
	pVolumeSliderContainer->OutlineStyle = Outline({}, 0_px, 1_rem, 1_px);
	pVolumeSliderContainer->OutlineStyle.SetCornerRadius(1_rem, 8_px, 8_px, 1_rem);

	auto* volumeSliderContainerLayout = pVolumeSliderContainer->setLayout<StackLayout>();
	volumeSliderContainerLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	volumeSliderContainerLayout->Alignment = EChildAlignment::Center;
	volumeSliderContainerLayout->bUsePaddingBeforeFirstAndAfterLast = true;


	auto playBackVolume = pApp->GetParam("USER_PLAYBACK_VOLUME");
	pVolumeSlider = new TStyleSlider(pVolumeSliderContainer, "btnVolume", 0, 0, 340, 32, 101, playBackVolume);    // +1  ->  (0-100 are 101 different values)
	pVolumeSlider->setSliderButtonImages(Image::GetImage("sliderDot.png"), Image::GetImage("sliderDot.png"), Image::GetImage("sliderDot.png"));
	pVolumeSlider->showArrows(true);
	pVolumeSlider->setId("slider");
	pVolumeSlider->showValuesOnSlide(false);
	pVolumeSlider->setFocusMode(EFocusMode::Accept);
	pVolumeSlider->valueLabel()->mTypography = tempDesignTokens::MainFont_400_16;
	volumeSliderContainerLayout->setPaddingFor(pVolumeSlider, { 24, 0, 24, 0 });
	auto downImage = Image::GetImage("menuSettingsVolumeDown.png");
	auto upImage = Image::GetImage("menuSettingsVolumeUp.png");
	std::array<TSliderArrowImages, 2> sliderArrowImages = { TSliderArrowImages({ downImage, downImage, downImage, downImage }),
		                                                    TSliderArrowImages({ upImage, upImage, upImage, upImage }) };
	auto sliderArrows = pVolumeSlider->GetSliderArrows();
	sliderArrows[0]->OnPressed += [this]() {
		pVolumeSlider->setValue(ESliderInputType::Direct, 0);
	};
	sliderArrows[1]->OnPressed += [this]() {
		pVolumeSlider->setValue(ESliderInputType::Direct, 100);
	};
	pVolumeSlider->SetFlipRightArrowImage(false);
	pVolumeSlider->SetSliderButtonBackgroundColors(Transparent, Transparent, Transparent);
	pVolumeSlider->setSliderArrowsImages(sliderArrowImages);
	pVolumeSlider->SetWriteValueOutsideSlider(false);
	pVolumeSlider->TrackBrush = Transparent;
	pVolumeSlider->SetShowProgressBar(true);
	pVolumeSlider->SetTrackProgressBarColor(tempDesignTokens::TopInfoBarBackgroundColor);
	pVolumeSlider->markLayoutDirty();
	pVolumeSlider->setVisible(false);
	pVolumeSlider->setLoopAround(false);
	playBackVolume->OnParamChange += [this](const TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterSource& source) {
		if (changeFlag.HasFlag(ParameterProperty::VALUE))
		{
			if (pVolumeSlider->isVisible())
				LastVolumeActivityTimestamp = SDL_GetTicks64();
			pGuiApp->DeferToDrawSimple(
			  [this, value = param->AsInteger()]() {
				  if (value == 0)
					  pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeMute.png"));
				  else if (value < 50)
					  pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeDown.png"));
				  else
					  pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeUp.png"));
			  },
			  "TopInfoBar::SetAllStatesToImage");
		}
	};

	auto sliderVolume = playBackVolume->AsInteger();
	pVolumeIcon = new TStyleButton2(pVolumeSliderContainer, LocalizedMessage(), White);
	pVolumeIcon->setId("volume-icon");
	pVolumeIcon->setOrigin(1.f, 0.5f);
	if (sliderVolume == 0)
		pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeMute.png"));
	else if (sliderVolume < 50)
		pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeDown.png"));
	else
		pVolumeIcon->setAllStatesToImage(Image::GetImage("volumeUp.png"));
	pVolumeIcon->setSize(61);
	pVolumeIcon->OnPressed += [this]() {
		LastVolumeActivityTimestamp = SDL_GetTicks64();
		SetVolumeSliderProperties(!bVolumeOpened);
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
	};

	SetVolumeSliderProperties(false);

	pFlagsContainer = new LayoutContainer();
	pFlagsContainer->setId("flags-container");
	pFlagsContainer->setSize(300, 56);
	pFlagsContainer->setOrigin(1.f, 0.5f);
	userSettingsHolder->add(pFlagsContainer);
	pFlagsContainer->bResizeToFitLayout = { true, false };
	pFlagsContainer->OutlineStyle = Outline({}, 0_px, 1_rem, 1_px);
	pFlagsContainer->OutlineStyle.SetCornerRadius(8_px);

	auto* flagsLayout = pFlagsContainer->setLayout<StackLayout>();
	flagsLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	flagsLayout->Alignment = EChildAlignment::Center;
	flagsLayout->Padding = Vector2D(0, 0);
	flagsLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	auto langs = pApp->GetEnabledLanguages();
	for (auto& lang : langs)
	{
		auto obj = pApp->LangMessages->GetDescriptor(lang, false);
		if (obj)
		{
			Container* flagContainer = new Container();
			flagContainer->setId("flags-container" + std::to_string(lang));
			flagContainer->setSize(86, 48);
			flagContainer->setVisible(false);
			pFlagsContainer->add(flagContainer);
			mFlagContainers.insert({ lang, flagContainer });
			flagContainer->OnClicked += [this, lang](Widget* src, const Vector2D& pos) {
				pApp->SetLanguage(lang);
				SetLanguageContainerProperties(false);
				pGuiApp->PlaySound(tempDesignTokens::buttonPress);
			};
			flagsLayout->setPaddingFor(flagContainer, { 8, 0, 0, 0 });

			ImagePtr image;
			if (Image::GetImage(std::format("TextImages/{}/3.png", obj->Language._to_integral())))
			{
				image = Image::GetImage(std::format("TextImages/{}/3.png", obj->Language._to_integral()));
			}
			else
			{
				image = Image::GetImage("add.png");
			}

			Icon* pFlag = new Icon(image);
			pFlag->setId("flag");
			pFlag->setSize(24);
			pFlag->setPosition(16, 24);
			pFlag->setOrigin(0.f, 0.5f);
			flagContainer->add(pFlag);

			std::string isoCode = ISOLanguages::LanguageAsISO.at(lang).substr(0, 2);
			isoCode = MyUtils::ToUppercase(isoCode);

			Label* lIsoCode = new Label(LocalizedMessage(isoCode), White);
			lIsoCode->setId("lang-iso-code");
			lIsoCode->setPosition(48, 24);
			lIsoCode->setResizeMode(Label::ResizeMode::AUTOSIZE);
			lIsoCode->setAlignment(align::LEFT_CENTER);
			lIsoCode->setOrigin(0.f, 0.5f);
			flagContainer->add(lIsoCode);
			mFlagLabels.insert({ lang, lIsoCode });

			if (pApp->Language() == lang)
			{
				flagContainer->Background = tempDesignTokens::LobbyControlsSelectedFilter;
				flagContainer->OutlineStyle =
				  Outline(tempDesignTokens::GoldTextColor, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
				lIsoCode->TextColor = Black;
			}
		}
	}

	pLang = new TStyleButton2(pFlagsContainer, LocalizedMessage(), White);
	pLang->setId("language");
	pLang->Text->setImage(LANGUAGE_DESCRIPTION);
	pLang->setSize(64);
	pLang->OnPressed += [this]() {
		auto langs = pApp->GetEnabledLanguages();
		if (langs.size() > 1)    // if there is more that current language to choose from
		{
			LastFlagActivityTimestamp = SDL_GetTicks64();
			SetLanguageContainerProperties(!bFlagsOpened);
			pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		}
	};

	pAttendantContainer = new Container();
	pAttendantContainer->setSize(61, 56);
	pAttendantContainer->setId("call-attendant");
	pAttendantContainer->OnClicked += [this](Widget* w, const Vector2D& pos) {
		if (!bInService)
		{
			OnCallAttendantPressed();
			pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		}
	};
	userSettingsHolder->add(pAttendantContainer);
	userSettingsLayout->setPaddingFor(pAttendantContainer, { 0, 0, 4, 0 });

	userSettingsHolder->setHeight(64);
	userSettingsHolder->setOrigin(1.f, 0.f);
	topBarHolder->add(userSettingsHolder);
	pCommander = (TPlatformCommander*)pApp->GetModuleByName("Commander");
}

void TTopInfoBar::QRPayoutSetEnabled(const bool enabled)
{
	pQRPayoutBtn->setEnabled(enabled);
}
bool TTopInfoBar::QRPayoutIsEnabled() const
{
	return pQRPayoutBtn->isEnabled();
}
void TTopInfoBar::QRPayoutSetVisible(const bool visible)
{
	pQRPayoutBtn->setVisible(visible);
}

void TTopInfoBar::PayoutSetEnabled(const bool enabled)
{
	pBtnPayout->setEnabled(enabled);
}

bool TTopInfoBar::PayoutIsEnabled() const
{
	return pBtnPayout->isEnabled();
}

void TTopInfoBar::SetCreditValueCurrencyContentVisible(const bool visible)
{
	plCreditValueCurrency->setContentVisible(visible);
}

void TTopInfoBar::InService(bool inService)
{
	bInService = inService;
	if (bInService)
	{
		pAttendantContainer->Background = tempDesignTokens::GoldTextColor;
		pAttendantContainer->OutlineStyle =
		  Outline(tempDesignTokens::GoldTextColor, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
	}
	else
	{
		pAttendantContainer->Background = Transparent;
		pAttendantContainer->OutlineStyle = Outline(Transparent, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
	}
}

void TTopInfoBar::RefreshCreditMeter(const credit::CreditArray& oldBalance, const credit::CreditArray& newBalance) const
{
	if (!bUserHiddenCreditValue)
	{
		plCreditValueCurrency->setCaption(MenuGUI()->FormatCreditsAsCurrency(newBalance.Total()));
	}
}

Matrix<float, 4> colorToMatrix(LinearColor color)
{
	return Matrix<float, 4>({ { color.R(), 0.f, 0.f, 0.f }, { 0.f, color.G(), 0.f, 0.f }, { 0.f, 0.f, color.B(), 0.f }, { 0.f, 0.f, 0.f, color.A() } });
}

void TTopInfoBar::draw(Graphics* graphics)
{
	Container::draw(graphics);

	auto color = bInService ? colorToMatrix(Black) : colorToMatrix(tempDesignTokens::GoldTextColor);
	graphics->setBrush(ImageBrush(Image::GetImage("callAttendant.png"), Rectangle(), color));
	auto pos = pAttendantContainer->getAbsoluteDimension();
	graphics->fillRectangleWithBrush(Rectangle(pos.x() + pAttendantContainer->getWidth() / 2 - 12.f, pos.y() + pAttendantContainer->getHeight() / 2 - 12.f, 24.f, 24.f),
	                                 {});
	graphics->popBrush();
}

void TTopInfoBar::SetSelectedActiveGame(const int slotID)
{
	bool bActive = false;
	if (slotID >= 0)
	{
		pCommander->GetGameState(slotID, [this, &bActive](GameRunContext& ctx) {
			if (!ctx.RunningPackage)
				return;

			bActive = true;

			plGameName->setCaption(ctx.RunningGame->Info().Config.GameConfig.DisplayName);
			plGameName->setVisible(true);

			uint64_t minBetCredits = 0;
			uint64_t maxBetCredits = 0;
			if (ctx.RunningGame->LiveGame())
			{
				minBetCredits = ctx.RunningGame->LiveGame()->MinBet;
				maxBetCredits = ctx.RunningGame->LiveGame()->MaxBet;
			}

			LocalizedMessage minBetString = MenuGUI()->FormatCreditsAsCurrency(std::max(minBetCredits, 1UL));
			LocalizedMessage maxBetString = maxBetCredits == 0 ? LocalizedMessage("Unlimited") : LocalizedMessage(MenuGUI()->FormatCreditsAsCurrency(maxBetCredits));

			plGameMinMax->setCaption(minBetString + " - " + maxBetString);
			plGameMinMax->setVisible(true);
		});
	}

	if (!bActive)
	{
		plGameName->clear();
		plGameMinMax->clear();
		plGameName->setVisible(false);
		plGameMinMax->setVisible(false);
	}
}

constexpr uint64_t AUTO_CLOSE_TIMEOUT = 10000;
void TTopInfoBar::drawLogic(Graphics* graphics, float deltaTime)
{
	if (bVolumeOpened && MyUtils::IsTime(LastVolumeActivityTimestamp, AUTO_CLOSE_TIMEOUT))
	{
		SetVolumeSliderProperties(false);
		LastVolumeActivityTimestamp = 0;
	}

	if (bFlagsOpened && MyUtils::IsTime(LastFlagActivityTimestamp, AUTO_CLOSE_TIMEOUT))
	{
		SetLanguageContainerProperties(false);
		LastFlagActivityTimestamp = 0;
	}

	Container::drawLogic(graphics, deltaTime);
}

void TTopInfoBar::onLanguageChanged(ELanguage lang)
{
	for (auto& flags : mFlagContainers)
	{
		if (flags.first == pApp->Language())
		{
			flags.second->Background = tempDesignTokens::LobbyControlsSelectedFilter;
			flags.second->OutlineStyle =
			  Outline(tempDesignTokens::GoldTextColor, { 0.f, EDimensionUnit::px }, tempDesignTokens::RoundedCorner_1rem, { 1.f, EDimensionUnit::px });
		}
		else
		{
			flags.second->Background = Transparent;
			flags.second->OutlineStyle = Outline();
		}
	}

	for (auto& flags : mFlagLabels)
	{
		if (flags.first == pApp->Language())
		{
			flags.second->TextColor = Black;
		}
		else
		{
			flags.second->TextColor = White;
		}
	}

	Container::onLanguageChanged(lang);
}

void TTopInfoBar::CreateDenominationDropdown(LayoutContainer* pParent, const float height,
                                             const std::function<Widget*(Widget*, const LocalizedMessage&)>& proclaimHatFunction,
                                             const LocalizedMessage& itemSelectTipMessage)
{
	const std::vector<int> virtualCreditCoefficients = GetVirtualCreditCoefficients();
	auto* denominationDropdown = new Dropdown(pParent, height, proclaimHatFunction, itemSelectTipMessage);
	denominationDropdown->setId("denomination-dropdown");

	denominationDropdown->OnToggleBehavior += [denominationDropdown](Widget* hatWidget, const bool isDropdownOpen) {
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		if (isDropdownOpen)
		{
			denominationDropdown->OutlineStyle = Outline({}, {}, 8_px, 1_px);
			denominationDropdown->Background = tempDesignTokens::DropdownGreyColor;
		}
		else
		{
			denominationDropdown->OutlineStyle = Outline();
			denominationDropdown->Background = Transparent;
		}
	};

	for (const auto coefficientValue : virtualCreditCoefficients)
	{
		denominationDropdown->addItem(DropdownItemEntity(MenuGUI()->FormatCreditsAsCurrency(coefficientValue), std::to_string(coefficientValue)));
	}

	denominationDropdown->setSelectedItem(0);
	denominationDropdown->ItemTypography = tempDesignTokens::MainFont_400_16;
	denominationDropdown->TipLabelTypography = tempDesignTokens::MainFont_400_16;
	denominationDropdown->SelectedItemTypography = tempDesignTokens::MainFont_600_16;

	denominationDropdown->OnItemSelected += [](int selIndex, const DropdownItemEntity& value) {
		pGuiApp->PlaySound(tempDesignTokens::buttonPress);
		int coeff = std::stoi(value.mValue);
		std::shared_ptr<TAppParam> indivisibleDenomination = pApp->GetParam("ALLOW_INDIVISIBLE_DENOMINATIONS");
		const int64_t bettable = MenuGUI()->pClient->BalanceManager()->GetBettableCredit().Total();

		if (indivisibleDenomination->AsInteger() || bettable % coeff == 0)
		{
			MenuGUI()->pClient->SetVirtualCreditCoefficient(coeff);
		}
	};
}

std::vector<int> TTopInfoBar::GetVirtualCreditCoefficients()
{
	std::vector<int> virtualCreditCoefficients = {};
	for (int i = 1;; i++)
	{
		const auto param = pApp->GetParam("VIRTUAL_CREDIT_COEFF_" + std::to_string(i));
		if (!param)
			break;

		const int val = param->AsInteger();
		if (val > 0 && std::ranges::find(virtualCreditCoefficients, val) == virtualCreditCoefficients.end())
			virtualCreditCoefficients.push_back(val);
	}
	return virtualCreditCoefficients;
}

void TTopInfoBar::SetVolumeSliderProperties(bool sliderVisible)
{
	if (sliderVisible)
	{
		pVolumeSliderContainer->Background = tempDesignTokens::TopInfoBarBackgroundExpandedColor;
	}
	else
	{
		pVolumeSliderContainer->Background = Transparent;
	}
	pVolumeSlider->setVisible(sliderVisible);
	pVolumeIcon->setVisible(!sliderVisible);

	if (bFlagsOpened)
	{
		SetLanguageContainerProperties(false);
	}
	bVolumeOpened = sliderVisible;
}

void TTopInfoBar::SetLanguageContainerProperties(bool flagsVisible)
{
	if (flagsVisible)
	{
		pFlagsContainer->Background = tempDesignTokens::TopInfoBarBackgroundExpandedColor;
	}
	else
	{
		pFlagsContainer->Background = Transparent;
	}
	pLang->setVisible(!flagsVisible);
	for (const auto& flags : mFlagContainers) { flags.second->setVisible(flagsVisible); }

	if (bVolumeOpened)
	{
		SetVolumeSliderProperties(false);
	}

	bFlagsOpened = flagsVisible;
}
