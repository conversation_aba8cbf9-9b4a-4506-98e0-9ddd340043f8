/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include "comp/TMessageBox.h"

#include "MyUtils.h"

TMessageBox::TMessageBox(Container* pParent, float TopOffset, float BorderWidth)
{
	setId("msg-box");

	if (pParent)
		pParent->add(this);

	BoxLayout* box = setLayout<BoxLayout>();
	box->DefaultBehavior = { EChildAlignment::Center, Rectangle(20.f, 20.f) };

	setTabInEnabled(false);
	setVisible(false);

	OutlineStyle = Outline(Color(0, 95, 192, 196), { BorderWidth, EDimensionUnit::px });
	Background = Black.Fade(0.8f);

	OnClicked += [this](Widget* w, const Vector2D& pos) {
		if (getEffectiveVisiblity(false) && !HideTask)
			fadeOut(0.5s);
	};

	setConsumeAllMouseEvents(true);

	mpMainLabel = new Label();
	mpMainLabel->setId("label");
	mpMainLabel->setWrap(true);
	mpMainLabel->setResizeMode(Label::AUTOSIZE);
	mpMainLabel->setPadding(Vector2D(100.f, 30.f));
	add(mpMainLabel);
	box->PerWidgetBehaviors[mpMainLabel].Padding = Rectangle(20.f, 20.f, 20.f, 20.f);

	mpNoteLabel = new Label();
	mpNoteLabel->setId("note");
	mpNoteLabel->setVisible(false);
	mpNoteLabel->setResizeMode(Label::AUTOSIZE);
	mpNoteLabel->TextShadow = Shadow::MakeOffsetShadow(2_px, Black);
	mpNoteLabel->setCaption(LocalizedMessage(TOUCH_TO_CLOSE_STRING));
	add(mpNoteLabel);
	box->PerWidgetBehaviors[mpNoteLabel] = { ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Max), Rectangle(0.f, 0.f, 0.f, 5.f) };

	bResizeToFitLayout = true;
}

TMessageBox::TMessageBox(Container* pParent, const Vector2D& pos, const Typography& typography, float TopOffset, float BorderWidth) :
    TMessageBox(pParent, TopOffset, BorderWidth)
{
	mpMainLabel->mTypography = typography;

	setOrigin(0.5f);
	setPosition(pos);
}

TMessageBox::~TMessageBox()
{
	if (HideTask)
		HideTask->Remove();
}

void TMessageBox::ShowMessages(const std::vector<LocalizedMessage>& Lines, int Time)
{
	mpMainLabel->setCaptions(Lines);
	ShowMessage(Time);
}

void TMessageBox::ShowMessage(int Time)    // use SetCaption
{
	ShowForTime(Time);
	if (bUpdatePaddingOnShow)
	{
		BoxLayout* box = dynamic_cast<BoxLayout*>(layout());
		if (box)
			box->PerWidgetBehaviors[mpMainLabel].Padding = Rectangle(20.f, 20.f, 20.f, Time <= 0 ? 30.f : 20.f);
	}
	mpNoteLabel->setVisible(Time <= 0);

	if (bMoveToTopOnShow)
		moveToTop();
}

bool TMessageBox::hasModalFocus() const
{
	return getEffectiveVisiblity();
}

bool TMessageBox::hasModalMouseInputFocus() const
{
	return getEffectiveVisiblity();
}

void TMessageBox::ShowForTime(int TimeMS)
{
	if (!getEffectiveVisiblity())
		fadeIn();

	if (HideTask)
	{
		HideTask->Remove();
		HideTask.reset();
	}

	if (TimeMS > 0)
		HideTask = pApp->Delay(
		  [this]() {
			  fadeOut();
			  HideTask.reset();
		  },
		  TimeMS);
}
