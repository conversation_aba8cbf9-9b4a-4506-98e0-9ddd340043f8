#include "comp/TStyleSlider.h"

#include <string>

#include "MyUtils.h"
#include "TGuiApplication.h"
#include "TotallyTemporaryDesignTokens.h"

const Color NormalBackground = Color::Gray(77);
const Color NormalBorder = Color::Gray(190);
const Color HighlightedBackground = NormalBackground;
const Color HighlightedBorder = Red;
const Color PressedBackground = Color::Gray(162);
const Color PressedBorder = Color::Gray(200);
const Color DisabledBackground = Color::Gray(64);
const Color DisabledBorder = Color::Gray(77);

TStyleSlider::TStyleSlider(Container* pParent, const std::string& name, float x, float y, float w, float h, size_t numValues, const std::shared_ptr<TAppParam>& param,
                           bool is_string) : LayoutContainer()
{
	setDimension(x, y, w, h);
	setClippingMode(EClippingMode::KeepParentClippingArea);
	if (pParent)
		pParent->add(this);

	// action params
	setId(name);
	setActionEventId(name);

	SetParam(param);

	pLayout = setLayout<StackLayout>();
	pLayout->Alignment = EChildAlignment::Stretch;
	pLayout->Direction = EStackLayoutDirection::HORIZONTAL;

	pSliderArrows[0] = new TStyleButton2(this);
	pSliderArrows[0]->setId("arrowLeft");
	pSliderContentContainer = new Container(this, 0, 0, 0, 0);
	pSliderContentContainer->setId("content");

	pSliderProgressBarBG = new Widget();
	pSliderProgressBarBG->setY(h / 2);
	pSliderProgressBarBG->setHeight(2);
	pSliderProgressBarBG->setId("slider-background");
	pSliderProgressBarBG->setOrigin(0.f, 0.5f);
	pSliderProgressBarBG->Background = Color::FromRGBInteger(0x212628);
	pSliderProgressBarBG->setVisible(false);
	pSliderContentContainer->add(pSliderProgressBarBG);

	pSliderProgressBar = new Widget();
	pSliderProgressBar->setY(h / 2);
	pSliderProgressBar->setHeight(2);
	pSliderProgressBar->setId("progress-bar");
	pSliderProgressBar->Background = tempDesignTokens::GoldTextColor;
	pSliderProgressBar->setOrigin(0.f, 0.5f);
	pSliderProgressBar->setVisible(false);
	pSliderContentContainer->add(pSliderProgressBar);

	pSliderArrows[1] = new TStyleButton2(this);
	pSliderArrows[1]->setId("arrowRight");
	pSliderArrows[1]->BackgroundImageFlip = GPU_FLIP_HORIZONTAL;

	pLayout->setSizeModeFor(pSliderContentContainer, EWidgetSizeMode::FILL_AVAILABLE_SPACE);

	mSoundEffectName = "slider_change.wav";
	bool bLeft = true;
	for (TStyleButton2*& arrowBtn : pSliderArrows)
	{
		arrowBtn->OnPressed += [this, bLeft]() {
			if (bLeft)
				PrevValue();
			else
				NextValue();
		};
		bLeft = false;
		arrowBtn->ClickSound = mSoundEffectName;
		arrowBtn->setHeight(getHeight());
		arrowBtn->AutoRepeatFirstInterval = 1000;
		arrowBtn->AutoRepeatInterval = 150;
		arrowBtn->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_FIT_ONLY_DOWN;
		arrowBtn->setVisible(bShowArrows);
	}

	addMouseListener(this);

	std::array<TSliderArrowImages, 2> sliderArrowImages = { TSliderArrowImages({ Image::GetImage("sliderLeftN.png"), Image::GetImage("sliderLeftP.png"),
		                                                                         Image::GetImage("sliderLeftD.png"), Image::GetImage("sliderLeftH.png") }),
		                                                    TSliderArrowImages({ Image::GetImage("sliderLeftN.png"), Image::GetImage("sliderLeftP.png"),
		                                                                         Image::GetImage("sliderLeftD.png"), Image::GetImage("sliderLeftH.png") }) };

	setSliderArrowsImages(sliderArrowImages);

	pSliderButton = new TStyleButton2(this);
	pSliderButton->setId("button");
	pSliderButton->setMouseInput(EventPropagateMode::NONE);
	pSliderButton->setOrigin(0.5f);
	pSliderButton->setY(getHeight() / 2);
	pSliderButton->setSize(Vector2D(15, 29));
	pSliderButton->Background = NormalBackground;
	pSliderButton->OutlineStyle = Outline(NormalBorder, 2_px);
	pSliderButton->setFloating(true);
	pLayout->setPaddingFor(pSliderContentContainer, Rectangle(pSliderButton->getWidth() / 2, 0, pSliderButton->getWidth() / 2, 0));

	pSliderButton->OutlineStyle.AddModifier([this](Outline& o) {
		if (o.BorderBrush.type() != EBrushType::Color)
			return;

		if (!bEnabled)
			o.BorderBrush = DisabledBorder;
		else if (bPressed)
			o.BorderBrush = PressedBorder;
		else if (pSliderButton->DrawHighInseadOfNormal)
			o.BorderBrush = HighlightedBorder;
	});

	SliderButtonDisabledBackground = DisabledBackground;
	SliderButtonPressedBackground = PressedBackground;
	SliderButtonHighlightedBackground = HighlightedBackground;

	pSliderButton->Background.AddModifier([this](Brush& b) {
		if (b.type() != EBrushType::Color)
			return;

		if (!bEnabled)
			b = SliderButtonDisabledBackground;
		if (bPressed)
			b = SliderButtonPressedBackground;
		if (pSliderButton->DrawHighInseadOfNormal)
			b = SliderButtonHighlightedBackground;
	});

	for (size_t i = 0; i < numValues; i++) AddValue(std::to_string(i), std::to_string(i), i);

	// default values
	TrackBrush = Color::Gray(0x66);

	// label on top of everything
	plValue = new Label();
	plValue->setId("value-label");
	plValue->setSize(pSliderContentContainer->getSize());
	plValue->setAlignment(align::CENTER_CENTER);
	plValue->setVisible(bShowValuesOnSlide);
	plValue->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
	plValue->setClippingMode(EClippingMode::KeepParentClippingArea);
	plValue->mTypography = tempDesignTokens::MainFont_500_16;
	plValue->TextColor = tempDesignTokens::GreyTextColor;
	plValue->setMaxNumberOfLines(2);
	plValue->TextShadow = Shadow::MakeOffsetShadow(2_px, Black);
	plValue->setFloating(true);
	add(plValue);

	// eventi za gumbe levo, desno
	if (TerminalFlavour == EFlavour::AIK)
	{
		// aik gui ima obratno red/black
		pSliderArrows[0]->setActionEventId("btnBlack");
		pSliderArrows[1]->setActionEventId("btnRed");
	}
	else
	{
		pSliderArrows[0]->setActionEventId("btnRed");
		pSliderArrows[1]->setActionEventId("btnBlack");
	}

	Type = is_string ? ESliderType::SLIDER_STRING : ESliderType::SLIDER_INT;

	setFocusMode(EFocusMode::Accept);

	WarnImage = Image::GetImage("lock_locked_warn.png");

	OnEnabledChanged += [this](Widget* w, bool enabled) {
		bParamChanged = true;
	};

	pSliderContentContainer->OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pSliderButton->setX(getPxFromIndex(mCurrentIndex));
		if (!bWriteValueOutsideSlider)
			plValue->setDimension(w->getDimension());
		pSliderProgressBarBG->setWidth(pSliderContentContainer->getWidth());
	};

	OnFocusedChanged += [this](Widget* w, bool bFocused) {
		pSliderButton->DrawHighInseadOfNormal = bFocused;
		pSliderArrows[0]->DrawHighInseadOfNormal = bFocused;
		pSliderArrows[1]->DrawHighInseadOfNormal = bFocused;
	};

	pSliderButton->OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		pSliderProgressBar->setWidth(getPxFromIndex(mCurrentIndex) - getPxFromIndex(0));
	};
}

TStyleSlider::TStyleSlider(Container* pParent, float x, float y, float w, float h, const std::shared_ptr<TAppParam>& pParam, bool IsString) :
    TStyleSlider(pParent, {}, x, y, w, h, 0, pParam, IsString)
{
	showArrows(true);
}

size_t TStyleSlider::numValues() const
{
	return Values.size();
}

bool TStyleSlider::pressed() const
{
	return bPressed;
}

void TStyleSlider::SetTrackProgressBarColor(const Color& color)
{
	pSliderProgressBarBG->Background = color;
}

void TStyleSlider::setSliderBackground(const ImagePtr& background)
{
	pSliderContentContainer->setBackgroundImage(background);
}

void TStyleSlider::setSliderButtonImages(const ImagePtr& normal, const ImagePtr& pressed, const ImagePtr& disabled, const ImagePtr& high)
{
	// ustvarimo gumb, ki ga premikamo;
	if (normal)
		pSliderButton->setSize(Vector2D(normal->getSize()));

	pSliderButton->setNormalImage(normal);
	pSliderButton->setDownImage(pressed);
	pSliderButton->setHighImage(high ? high : pressed);
	pSliderButton->setDisabledImage(disabled);

	pSliderButton->Background = Transparent;
	pSliderButton->OutlineStyle = Outline();

	pLayout->setPaddingFor(pSliderContentContainer, Rectangle(pSliderButton->getWidth() / 2, 0, pSliderButton->getWidth() / 2, 0));
	markLayoutDirty();
}

void TStyleSlider::setSliderArrowsImages(const std::array<TSliderArrowImages, 2>& images)
{
	size_t idx = 0;
	for (TStyleButton2*& arrowBtn : pSliderArrows)
	{
		int w = 0;
		if (images[idx].Normal)
			w = images[idx].Normal->getWidth();
		if (images[idx].Pressed)
			w = std::max(w, images[idx].Pressed->getWidth());
		if (images[idx].Disabled)
			w = std::max(w, images[idx].Disabled->getWidth());
		if (images[idx].High)
			w = std::max(w, images[idx].High->getWidth());
		arrowBtn->setWidth(static_cast<float>(w));

		arrowBtn->setNormalImage(images[idx].Normal);
		arrowBtn->setDownImage(images[idx].Pressed);
		arrowBtn->setHighImage(images[idx].High ? images[idx].High : images[idx].Pressed);
		arrowBtn->setDisabledImage(images[idx].Disabled);
		idx++;
	}
}


void TStyleSlider::showValuesOnSlide(bool show)
{
	bShowValuesOnSlide = show;
	plValue->setVisible(bShowValuesOnSlide);
}

void TStyleSlider::showArrows(bool show)
{
	if (bShowArrows != show)
	{
		bShowArrows = show;
		bParamChanged = true;
	}
}

void TStyleSlider::HideControls()
{
	showArrows(false);
	showValuesOnSlide(false);
	pLayout->setPaddingFor(pSliderContentContainer, Rectangle());
}

void TStyleSlider::SetFlipRightArrowImage(const bool flip)
{
	bFlipArrows = flip;
	if (bFlipArrows)
		pSliderArrows[1]->BackgroundImageFlip = GPU_FLIP_HORIZONTAL;
	else
		pSliderArrows[1]->BackgroundImageFlip = GPU_FLIP_NONE;
}

void TStyleSlider::SetSliderButtonBackgroundColors(const Color& pressed, const Color& disabled, const Color& highlighted)
{
	SliderButtonPressedBackground = pressed;
	SliderButtonDisabledBackground = disabled;
	SliderButtonHighlightedBackground = highlighted;
}

void TStyleSlider::SetWriteValueOutsideSlider(const bool writeOutside)
{
	bWriteValueOutsideSlider = writeOutside;
	plValue->setFloating(!bWriteValueOutsideSlider);
	if (bWriteValueOutsideSlider)
	{
		plValue->moveToBottom();
		plValue->setPadding(Vector2D(24, 0));
		plValue->setSize({ 38, 24 });
	}
	markLayoutDirty();
}

void TStyleSlider::SetShowProgressBar(const bool progressBarVisible)
{
	pSliderProgressBarBG->setVisible(progressBarVisible);
	pSliderProgressBar->setVisible(progressBarVisible);
}

void TStyleSlider::setGeneratesActionOnPressed(bool generateAction)
{
	bActionOnPress = generateAction;
}

void TStyleSlider::setGeneratesActionOnReleased(bool generateAction)
{
	bActionOnRelease = generateAction;
}

void TStyleSlider::setGeneratesActionOnButtons(bool generateAction)
{
	bActionOnButtons = generateAction;
}

bool TStyleSlider::generatesActionOnPressed() const
{
	return bActionOnPress;
}

bool TStyleSlider::generatesActionOnButtons() const
{
	return bActionOnButtons;
}

bool TStyleSlider::generatesActionOnReleased() const
{
	return bActionOnRelease;
}

void TStyleSlider::mousePressed(MouseEvent& mouseEvent)
{
	Rectangle trackArea = pSliderContentContainer->getDimension();
	trackArea = trackArea.centerExpand(Vector2D(pSliderButton->getWidth(), 0.f));
	if (!trackArea.isPointInRect(mouseEvent.pos()))
		return;

	if (RestrictedState() != ELockedState::UNLOCKED)
	{
		ShowExplanationMessage();
		return;
	}

	pSliderButton->mousePressed(mouseEvent);

	bPressed = true;
	if (pSliderButton->getDimension().isPointInRect(mouseEvent.pos()))
	{
		if (bActionOnPress)
		{
			generateAction();
		}
	}
	else
	{
		int selectedItem = std::clamp(getIndexFromPx(mouseEvent.pos().X()), 0, (int)Values.size() - 1);
		int64_t intVal = Values.at(selectedItem).IntValue;
		const std::string strVal = Values.at(selectedItem).StrValue;
		const json selValue = Type == ESliderType::SLIDER_INT ? json(intVal) : json(strVal);
		const json limitedValue = Param() ? Param()->Rule().Limits.TryToLimit(selValue) : json();
		if (!limitedValue.is_null() && limitedValue != selValue)
		{
			// find the index of this value
			size_t foundIdx = 0;
			for (const TSliderValueDescriptor& val : Values)
			{
				if (Type == ESliderType::SLIDER_INT)
				{
					if (limitedValue.is_number_integer() && val.IntValue == limitedValue.get<int64_t>())
						break;
				}
				else
				{
					if (limitedValue.is_string() && val.StrValue == limitedValue.get<std::string>())
						break;
				}
				foundIdx++;
			}
			if (foundIdx < Values.size())
				setValue(ESliderInputType::Direct, foundIdx, true, bActionOnPress);
		}
		else
		{
			setValue(ESliderInputType::Direct, selectedItem, true, bActionOnPress);
		}
	}
	requestFocus();

	if (mouseEvent.getButton() == EMouseEventButton::LEFT)
		mSlidingX = mouseEvent.pos().X();
}

void TStyleSlider::mouseDragged(MouseEvent& mouseEvent)
{
	Rectangle trackArea = pSliderContentContainer->getDimension();
	if (!trackArea.isPointInRect(mouseEvent.pos()))
		return;

	if (RestrictedState() != ELockedState::UNLOCKED)
		return;

	float newX = std::clamp(mSlidingX + mouseEvent.dragged().X(), trackArea.left(), trackArea.right());
	pSliderButton->setX(newX);

	int selectedItem = std::clamp(getIndexFromPx(newX), 0, (int)Values.size() - 1);
	if (selectedItem != mCurrentIndex)
	{
		int64_t intVal = Values.at(selectedItem).IntValue;
		const std::string strVal = Values.at(selectedItem).StrValue;
		const json selValue = Type == ESliderType::SLIDER_INT ? json(intVal) : json(strVal);
		const json limitedValue = Param() ? Param()->Rule().Limits.TryToLimit(selValue) : json();
		if (!limitedValue.is_null() && limitedValue != selValue)
		{
			// don't move
		}
		else
		{
			// nastavljamo nov index in NE nastavimo x gumbu ker ga že tukaj
			setValue(ESliderInputType::Interactive, selectedItem, false, bGenerateActionOnSlide);
			if (bGenerateActionOnSlide)
				pGuiApp->PlaySound(mSoundEffectName);
		}
	}

	mouseEvent.consume();
}

void TStyleSlider::mouseReleased(MouseEvent& mouseEvent)
{
	if (RestrictedState() != ELockedState::UNLOCKED)
		return;

	if (bPressed)
	{
		mSlidingX = -1;
		bPressed = false;

		// ob supustu miske ga nastavimo na vrednost
		setValue(ESliderInputType::Direct, mCurrentIndex, true, bActionOnRelease);
		pGuiApp->PlaySound(mSoundEffectName);
		pSliderButton->mouseReleased(mouseEvent);
		mouseEvent.consume();
	}
}

int TStyleSlider::getIndexFromPx(float px_pos)
{
	return std::round((px_pos - pSliderContentContainer->getX()) * int(Values.size() - 1) / pSliderContentContainer->getWidth());
}

float TStyleSlider::getPxFromIndex(int index)
{
	if (Values.empty())
		return pSliderContentContainer->getX() + pSliderContentContainer->getWidth() / 2;
	else if (index >= (int)Values.size())
		return pSliderContentContainer->getX() + pSliderContentContainer->getWidth();
	else
		return pSliderContentContainer->getX() + index * pSliderContentContainer->getWidth() / int(Values.size() - 1);
}

void TStyleSlider::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	outAvailableActions.SetAvailable(EHardwareButtonAction::BetMax, NEXT_VALUE_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::BetPlus, NEXT_VALUE_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::BetMinus, PREV_VALUE_STRING);
}

bool TStyleSlider::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	switch (ev.Action)
	{
		case EHardwareButtonAction::BetMax:
		case EHardwareButtonAction::BetPlus:
			if (ev.bPressed)
			{
				NextValue();
				pGuiApp->PlaySound(mSoundEffectName);
			}
			return true;
		case EHardwareButtonAction::BetMinus:
			if (ev.bPressed)
			{
				PrevValue();
				pGuiApp->PlaySound(mSoundEffectName);
			}
			return true;
		default: break;
	}

	return false;
}

TStyleButton2* TStyleSlider::sliderButton() const
{
	return pSliderButton;
}

void TStyleSlider::AddValue(const LocalizedMessage& Caption, const std::string& SValue, int64_t IValue)
{
	TSliderValueDescriptor tmpV;
	tmpV.ValueCaption = Caption;
	tmpV.StrValue = SValue;
	tmpV.IntValue = IValue;
	Values.push_back(std::move(tmpV));

	pSliderButton->setX(getPxFromIndex(mCurrentIndex));

	if (Param())
	{
		if ((Type == ESliderType::SLIDER_INT && IValue == Param()->AsInteger()) || (Type == ESliderType::SLIDER_STRING && SValue == Param()->Value()))
			setValue(ESliderInputType::SetByCode, (int)Values.size() - 1, true, true, {});
	}
}

void TStyleSlider::ClearValues()
{
	Values.clear();
	mCurrentIndex = 0;
}

int TStyleSlider::SetValue(const ESliderInputType inputType, const std::string& SValue, int64_t IValue, const ParameterSource& source)
{
	const int idx = (Type == ESliderType::SLIDER_INT) ? FindValue(IValue) : FindValue(SValue);
	if (idx == -1)    // not found
	{
		if (Type == ESliderType::SLIDER_INT)
			AddValue("*" + SValue, SValue, IValue);
		else
			AddValue("_" + std::to_string(IValue), SValue, IValue);

		return SetValue(inputType, SValue, IValue, source);    // rekurzivno poklicemo, sedaj ga bomo nasli
	}
	else
	{
		setValue(inputType, idx, true, false, source);
		return idx;
	}
}


void TStyleSlider::setValue(const ESliderInputType inputType, int index, bool updatex, bool triggerAction, const ParameterSource& source)
{
	if (updatex && (mSlidingX < 0))
		pSliderButton->setX(getPxFromIndex(index));

	if (mCurrentIndex == index)
	{
		if (Param() && inputType != ESliderInputType::Interactive)
		{
			if (Type == ESliderType::SLIDER_STRING)
				Param()->SetValue(Values.at(mCurrentIndex).StrValue, source);
			else
				Param()->SetValue((int64_t)Values.at(mCurrentIndex).IntValue, false, source);
		}

		if (triggerAction)
		{
			if (mpOnChangeCallback)
				mpOnChangeCallback(inputType, mCurrentIndex);

			generateAction();
		}
		return;
	}

	mCurrentIndex = index;

	if (Param() && !Values.empty() && inputType != ESliderInputType::Interactive)
	{
		if (Type == ESliderType::SLIDER_STRING)
			Param()->SetValue(Values.at(mCurrentIndex).StrValue, source);
		else
			Param()->SetValue((int64_t)Values.at(mCurrentIndex).IntValue, false, source);
	}

	pSliderProgressBar->setWidth(getPxFromIndex(mCurrentIndex) - getPxFromIndex(0));

	if (triggerAction)
	{
		if (mpOnChangeCallback)
			mpOnChangeCallback(inputType, mCurrentIndex);

		generateAction();
	}
}

void TStyleSlider::setLoopAround(bool loopAround)
{
	bLoopAround = loopAround;
}

bool TStyleSlider::NextValue()
{
	if (Values.empty())
		return false;

	int selectIdx = mCurrentIndex;

	std::string selValue, limitedValue;
	do {
		selectIdx++;
		if (mCurrentIndex == selectIdx)    // this means we looped around completely and did not find a good value, break out of the function
			return false;

		if (selectIdx >= (int)Values.size())
		{
			if (!bLoopAround)
				return false;
			selectIdx = 0;
		}
		selValue = Values.at(selectIdx).StrValue;
		limitedValue = Param() ? Param()->LimitValueAsString(selValue) : selValue;
	} while (limitedValue != selValue);

	setValue(ESliderInputType::Direct, selectIdx, true, true);

	return true;
}

bool TStyleSlider::PrevValue()
{
	if (Values.empty())
		return false;

	int selectIdx = mCurrentIndex;

	std::string selValue, limitedValue;
	do {
		selectIdx--;
		if (mCurrentIndex == selectIdx)    // this means we looped around completely and did not find a good value, break out of the function
			return false;

		if (selectIdx < 0)
		{
			if (!bLoopAround)
				return false;
			selectIdx = (int)Values.size() - 1;
		}
		selValue = Values.at(selectIdx).StrValue;
		limitedValue = Param() ? Param()->LimitValueAsString(selValue) : selValue;
	} while (limitedValue != selValue);

	setValue(ESliderInputType::Direct, selectIdx, true, true);

	return true;
}

int TStyleSlider::FindValue(const std::string& SValue) const
{
	auto it = std::find_if(Values.begin(), Values.end(), [&SValue](const TSliderValueDescriptor& desc) -> bool { return desc.StrValue == SValue; });
	if (it == Values.end())
		return -1;

	return std::distance(Values.begin(), it);
}

int TStyleSlider::FindValue(int64_t IValue) const
{
	auto it = std::find_if(Values.begin(), Values.end(), [&IValue](const TSliderValueDescriptor& desc) -> bool { return desc.IntValue == IValue; });
	if (it == Values.end())
		return -1;

	return std::distance(Values.begin(), it);
}

void TStyleSlider::setCaption(const LocalizedMessage& Caption)
{
	if (pLabel)
		pLabel->setCaption(Caption);
}

TSliderValueDescriptor TStyleSlider::GetSelected() const
{
	if (mCurrentIndex < 0 || mCurrentIndex >= (int)Values.size())
		return TSliderValueDescriptor();
	else
		return Values[mCurrentIndex];
}

void TStyleSlider::draw(Graphics* graphics)
{
	if (!pSliderContentContainer->getBackgroundImage() && TrackBrush.Value() && !bProgressBarVisible)
	{
		Rectangle destRect(pSliderContentContainer->getX(), 0, pSliderContentContainer->getWidth(), pSliderContentContainer->getHeight() / 3.f);
		destRect.y() += (getHeight() - destRect.height()) / 2;

		graphics->setBrush(TrackBrush.Value());
		graphics->fillRectangleWithBrush(destRect);
	}

	LayoutContainer::draw(graphics);

	if (RestrictedState() == ELockedState::LOCKED && WarnImage)
	{
		Rectangle bounds = plValue->getTextBounds();
		Rectangle cbounds = pSliderContentContainer->getDimension();
		graphics->drawImage(WarnImage, bounds.left() + cbounds.left() - WarnImage->getWidth() - 5 /*padding*/, (cbounds.height() - WarnImage->getHeight()) / 2);
	}
}

void TStyleSlider::drawLogic(Graphics* graphics, float deltaTime)
{
	TDBParamControl::update();

	if (bParamChanged)
	{
		// enable/disable buttons
		bool widget_enabled = isEnabled() && (RestrictedState() == ELockedState::UNLOCKED);

		pSliderArrows[0]->setVisible(widget_enabled && bShowArrows);
		pSliderArrows[1]->setVisible(widget_enabled && bShowArrows);

		// ce je majhne slider, pol je vse nabasano in se slabo vidi - zao izklapljamo gumb za premikanje
		pSliderButton->setVisible(widget_enabled);

		bParamChanged = false;
	}

	// posodobimo izpis
	if (bShowValuesOnSlide)
	{
		if (!Values.empty() /* pParam*/)    // v primeru da imamo param, prikazujemo trenutno vrednost
		{
			plValue->setCaption(Values.at(mCurrentIndex).ValueCaption);
		}
		else    // v nasprotnem primeru prikazujemo trenutni index
			plValue->setCaption(std::to_string(mCurrentIndex + 1));
	}

	LayoutContainer::drawLogic(graphics, deltaTime);
}

void TStyleSlider::OnParamChanged(TAppParam* param, const ParamPropertyBitflag& changeFlag)
{
	TDBParamControl::OnParamChanged(param, changeFlag);
	if (changeFlag.HasFlag(ParameterProperty::RULES))
	{
		setVisible(RestrictedState() != ELockedState::HIDDEN);
		bParamChanged = true;
	}
	if (param && changeFlag.HasFlag(ParameterProperty::VALUE))
		SetValue(ESliderInputType::SetByCode, param->Value(), param->AsInteger());
}

void TStyleSlider::disableSound()
{
	mSoundEffectName.clear();
	for (TStyleButton2*& arrowBtn : pSliderArrows) arrowBtn->ClickSound.clear();
}

void TStyleSlider::setLabel(const LocalizedMessage& caption, const Color& labelColor, float FixedWidth)
{
	if (pLabel)
		remove(pLabel);

	pLabel = new Label(caption, labelColor);
	if (FixedWidth > 0.f)
		pLabel->setWidth(FixedWidth);
	pLabel->setResizeMode((FixedWidth > 0.f) ? Label::ResizeMode::RESIZE_DOWN_UNIFORM : Label::ResizeMode::AUTOSIZE, Label::ResizeMode::NONE);
	pLabel->setId("slider-caption");
	pLabel->setAlignment(align::RIGHT_CENTER);
	pLabel->setMaxNumberOfLines(2);
	pLabel->TextShadow = Shadow::MakeOffsetShadow(2_px, Black);
	pLabel->mTypography = mTypography;

	pLabel->TextColor.AddModifier([this](LinearColor& col) {
		if (Param() && Param()->Modified())
			col = Red;
	});
	add(pLabel);
	moveToBottom(pLabel);    // so it's first in the layout which is left to right
}

Label* TStyleSlider::label() const
{
	return pLabel;
}

Label* TStyleSlider::valueLabel() const
{
	return plValue;
}

std::array<TStyleButton2*, 2>& TStyleSlider::GetSliderArrows()
{
	return pSliderArrows;
}
