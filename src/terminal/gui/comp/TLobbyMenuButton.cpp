#include "gui/comp/TLobbyMenuButton.h"

#include "TotallyTemporaryDesignTokens.h"

TLobbyMenuButton::TLobbyMenuButton(const LobbyMenuButtonData& data)
{
	setSize(96);

	bContentAreaIncludesBorders = true;

	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	SelectedOutline = Outline(tempDesignTokens::Golden_Outline_Gradient_Transparent_LeftToRight, 2_px, tempDesignTokens::RoundedCorner_12, 1_px);

	Normal = data.ButtonImageEnabled;
	Disabled = data.ButtonImageDisabled;

	FocusMode = EFocusMode::Accept;

	GridLayout* layout = setLayout<GridLayout>();
	layout->AddRow({});
	layout->AddRow({});
	layout->AddColumn({ .Padding = 8.f });
	layout->bUsePaddingBeforeFirstAndAfterLast = true;

	plTitle = new Label(data.Title, White);
	plTitle->setId("title");
	plTitle->mTypography = tempDesignTokens::MainFont_400_16;
	plTitle->TextColor = tempDesignTokens::GoldTextColor;
	plTitle->setOrigin(0.5);
	plTitle->setAlignment(align::CENTER_CENTER);
	plTitle->setMaxNumberOfLines(2);
	plTitle->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
	add(plTitle);

	if (Normal)
	{
		piCategoryItem = new Icon();
		piCategoryItem->setImage(Normal);
		piCategoryItem->setId("icon");
		add(piCategoryItem);
		layout->Set(piCategoryItem, GridLayout::GridPosition(sVector2D(0), sVector2D(1), ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Max)));
		layout->Set(plTitle, GridLayout::GridPosition(sVector2D(0, 1), sVector2D(1), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Stretch)));
		selectedTypography = tempDesignTokens::MainFont_400_18_NoHeight;
		defaultTypography = tempDesignTokens::MainFont_400_16_NoHeight;
	}
	else
	{
		selectedTypography = tempDesignTokens::MainFont_400_18;
		defaultTypography = tempDesignTokens::MainFont_400_16;
		plTitle->setPadding(Rectangle(4.f, 12.f, 4.f, 12.f));
		layout->Set(plTitle, GridLayout::GridPosition(sVector2D(0), sVector2D(1, 2), ChildAlignment2D(EChildAlignment::Stretch, EChildAlignment::Stretch)));
	}
}

void TLobbyMenuButton::SetSelected(bool selected)
{
	IsSelected = selected;

	if (IsSelected)
		plTitle->mTypography = selectedTypography;
	else
		plTitle->mTypography = defaultTypography;

	plTitle->forceRedraw();

	if (!piCategoryItem)
		return;

	if (IsSelected && Normal)
		piCategoryItem->setImage(Normal);
	else if (!IsSelected && Disabled)
		piCategoryItem->setImage(Disabled);
}

bool TLobbyMenuButton::GetSelected() const
{
	return IsSelected;
}

const OutlineProperty& TLobbyMenuButton::getOutlineStyle() const
{
	return IsSelected ? SelectedOutline : LayoutContainer::getOutlineStyle();
}
