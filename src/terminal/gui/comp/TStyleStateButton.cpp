#include "comp/TStyleStateButton.h"

#include "MyUtils.h"
#include "TGuiApplication.h"

TStyleStateButton::TStyleStateButton(const LocalizedMessage& caption, std::string ClickSound) : Button()
{
	mActionsEnabled = true;

	setFocusMode(EFocusMode::Deny);

	drawImgXOfset = 0;
	drawImgYOfset = 0;
	this->ClickSound = ClickSound;

	CurrentState = 1;
}

void TStyleStateButton::addImage(ImagePtr stateImage)
{
	ImageStates.push_back(stateImage);
}

void TStyleStateButton::generateAction()
{
	if (!mActionsEnabled)
		return;

	if (!isEnabled() || !isVisible())
		return;

	if (!ClickSound.empty())
		pGuiApp->PlaySound(ClickSound);

	if (ImageStates.size() > 0)
	{    // samo v primeru ce imamo kaksno sliko nalozeno
		CurrentState++;
		if (CurrentState > (int)ImageStates.size())
			CurrentState = 1;
	}
}

void TStyleStateButton::draw(Graphics* graphics)
{
	Container::draw(graphics);    // skip base button draw

	if (!ImageStates.empty())
	{
		if (CurrentState > 0 && (CurrentState <= (int)ImageStates.size()))
		{
			EScaleMode scaleMode = EScaleMode::NONE;
			switch (std::max(Text->resizeModeX(), Text->resizeModeY()))
			{
				case Label::RESIZE_UP: scaleMode = EScaleMode::ZOOM_TO_CONTAIN; break;
				case Label::RESIZE_DOWN: scaleMode = EScaleMode::ZOOM_TO_CONTAIN_ONLY_DOWN; break;
				case Label::RESIZE_ANY: scaleMode = EScaleMode::ZOOM_TO_CONTAIN; break;
				case Label::RESIZE_UP_UNIFORM:
					scaleMode = EScaleMode::ZOOM_TO_CONTAIN;
					break;
					// Scales the text down uniformly to fit the label
				case Label::RESIZE_DOWN_UNIFORM: scaleMode = EScaleMode::ZOOM_TO_CONTAIN_ONLY_DOWN; break;
				case Label::RESIZE_ANY_UNIFORM: scaleMode = EScaleMode::ZOOM_TO_CONTAIN; break;
				case Label::AUTOSIZE: scaleMode = EScaleMode::STRETCH; break;
				default: break;
			}
			graphics->drawImageText(ImageStates.at(CurrentState - 1), Rectangle(drawImgXOfset, drawImgYOfset, getWidth() - drawImgXOfset, getHeight() - drawImgYOfset),
			                        Text->getAlignment(), scaleMode);
		}
	}
}

// MEHANIZEM ZA AUTO REPEAT TIPKO, igralec mora zadrzati tipko dlje casa, da se zatakne v AUTO REPEAT
void TStyleStateButton::mousePressed(MouseEvent& mouseEvent)
{
	if (!mActionsEnabled)
		return;

	Button::mousePressed(mouseEvent);
}
// void TStyleStateButton::mouseRelease(int x, int y, int button)
void TStyleStateButton::mouseReleased(MouseEvent& mouseEvent)
{
	if (!mActionsEnabled)
		return;

	Button::mouseReleased(mouseEvent);
}

void TStyleStateButton::drawLogic(Graphics* graphics, float deltaTime)
{
	Text->setVisible(ImageStates.empty());

	LayoutContainer::drawLogic(graphics, deltaTime);
}

void TStyleStateButton::setMessageID(int msgID)
{
	MsgID = msgID;
	ImageStates.clear();

	if (MsgID > 0)
	{
		ELanguage lang = Language();
		if (lang == ELanguage::Current)
			lang = pApp->Language();

		int c = 1;
		while (true)
		{
			const std::filesystem::path stateButtonPath =
			  std::filesystem::path("TextImages") / std::to_string((int)lang) / (std::to_string(MsgID) + "_" + std::to_string(c) + ".png");

			if (!std::filesystem::exists(pGuiApp->ImageDir() / stateButtonPath))    // file doesn't exist
				break;

			ImagePtr stateButton = Image::GetImage(stateButtonPath);
			if (!stateButton)
				break;

			addImage(stateButton);
			c++;
		}
	}
}
void TStyleStateButton::onLanguageChanged(ELanguage lang)
{
	Container::onLanguageChanged(lang);

	setMessageID(MsgID);
}
