#include "comp/TStyleRadioButton.h"

#include "TGuiApplication.h"

TStyleRadioButton::TStyleRadioButton(const std::string& group, Container* parent, const LocalizedMessage& Caption, const ColorProperty& color) :
    TStyleButton2(parent, Caption, std::move(color))
{
	mGroup = group;

	setSelected(true);
	setFocusMode(EFocusMode::Deny);
}

TStyleRadioButton::TStyleRadioButton(const std::string& group, Container* parent, const LocalizedMessage& Caption, float x, float y, float width, float height,
                                     const std::string& XMLFilename) : TStyleButton2(parent, Caption, x, y, width, height, XMLFilename)
{
	mGroup = group;

	setSelected(true);
	setFocusMode(EFocusMode::Deny);

	Text->setBitmapFont(TBitmapFont::FindBitmapFont(XMLFilename));
}

void TStyleRadioButton::mouseClicked(MouseEvent& mouseEvent)
{
	if (mouseEvent.getButton() != EMouseEventButton::LEFT)
		return;

	if (ToggleMarkedOnClick && isSelected())
	{
		setSelected(false);
		generateAction();
	}
	else if (!isSelected())
	{
		setSelected(true);
		generateAction();
	}

	mouseEvent.consume();
}

// void TStyleRadioButton::mousePress(int x, int y, int button)
void TStyleRadioButton::mousePressed(MouseEvent& mouseEvent)
{
	/// zato da dela tudi na mouse down
	if (!SensibleToMouseDown || isSelected())
		return;

	setSelected(true);
	generateAction();
	mouseEvent.consume();
}

void TStyleRadioButton::setSelected(bool selected)
{
	if (bSelected != selected)
	{
		if (selected)
		{
			layout<BoxLayout>()->DefaultBehavior.Padding.Pos += Vector2D(mSelectedOffsetPx);
		}
		else
		{
			layout<BoxLayout>()->DefaultBehavior.Padding.Pos -= Vector2D(mSelectedOffsetPx);
		}
	}

	bSelected = selected;

	if (getParent() && bSelected)
	{
		for (const ChildWidget& w : getParent()->children())
		{
			if (!w.isValid() || w.Child == (Widget*)this)
				continue;

			if (TStyleRadioButton* radio = dynamic_cast<TStyleRadioButton*>(w.Child))
			{
				if (radio->mGroup == mGroup)
				{
					radio->setSelected(false);
				}
			}
		}
	}
}

bool TStyleRadioButton::isSelected() const
{
	return bSelected;
}
