
#include "TActiveGamesView.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"

TActiveGamesView::TActiveGamesView(Container* pParent) : LayoutContainer()
{
	if (pParent)
		pParent->add(this);

	StackLayout* layout = setLayout<StackLayout>();
	layout->Direction = EStackLayoutDirection::VERTICAL;
	layout->Alignment = EChildAlignment::Stretch;
	layout->Padding = { 0, 0 };

	pTitleContainer = new LayoutContainer(this, 0, 0, 370, 40);
	pTitleContainer->setId("title-container");
	pTitleContainer->bResizeToFitLayout = { true, true };

	pTitleLayout = pTitleContainer->setLayout<StackLayout>();
	pTitleLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	pTitleLayout->Alignment = EChildAlignment::Center;
	pTitleLayout->Padding = { 24, 0 };
	pTitleLayout->bUsePaddingBeforeFirstAndAfterLast = true;

	plTitle = new Label(LocalizedMessage(MULTIPLAY_STRING));
	plTitle->setId("multiplay");
	plTitle->setDimension({ 0, 0, 240, 30 });
	plTitle->mTypography = tempDesignTokens::TitleFont_18;
	plTitle->setAlignment(align::LEFT_CENTER);
	pTitleContainer->add(plTitle);
	pTitleLayout->setSizeModeFor(plTitle, EWidgetSizeMode::FILL_AVAILABLE_SPACE);

	pbThumbnailView = new TStyleButton2(pTitleContainer, LocalizedMessage());
	pbThumbnailView->setOrigin(0.5f, 0.5f);
	pbThumbnailView->setId("thumbnail-view");
	pbThumbnailView->setSize(35);
	pbThumbnailView->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_FIT;
	pbThumbnailView->setAllStatesToImage(Image::GetImage("listView.png"));
	pbThumbnailView->OnPressed += [this]() {
		OnGameViewChanged(EGameViewMode::ExtendedThumbnails);
		// change color/switch icon
	};

	pbGridView = new TStyleButton2(pTitleContainer, LocalizedMessage());
	pbGridView->setOrigin(0.5f, 0.5f);
	pbGridView->setId("grid-view");
	pbGridView->setSize(35);
	pbGridView->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_FIT;
	pbGridView->setAllStatesToImage(Image::GetImage("gridView.png"));
	pbGridView->OnPressed += [this]() {
		OnGameViewChanged(EGameViewMode::Grid);
		// change color/switch icon
	};

	pWidgetsContainer = new VerticalBox(this, EChildAlignment::Max, false, 16.f);
	pWidgetsContainer->setId("status-widgets-container");
	pWidgetsContainer->layout()->bUsePaddingBeforeFirstAndAfterLast = true;

	pCommander = pApp->GetModuleByName<TPlatformCommander>("Commander");
}

void TActiveGamesView::AddStatusWidget(const std::string& endpoint)
{
	pGuiApp->DeferToDrawSimple(
	  [&, endpoint]() {
		  const std::optional<size_t> gameSlot = pCommander->GetGameIndexFromEndpoint(endpoint);
		  if (!gameSlot)
		  {
			  TLOG(LogGames, Warning, "Failed to add status widget for endpoint %s: game slot not found", endpoint.c_str());
			  return;
		  }

		  ActiveGameStatusWidget* widget = new ActiveGameStatusWidget(pWidgetsContainer, endpoint);
		  widget->OnDeath += [this](Widget* src) {
			  mActiveGameStatusWidgetsBySlot.erase(dynamic_cast<ActiveGameStatusWidget*>(src));
		  };

		  widget->OnExtendThumbnailClick += [this, widget]() {
			  // if collapsed then switch to extended and vice versa
			  EGameViewMode mode = (viewMode == EGameViewMode::CollapsedThumbnails) ? EGameViewMode::ExtendedThumbnails : EGameViewMode::CollapsedThumbnails;
			  OnGameViewChanged(mode);
			  widget->SetSelected(true, (viewMode == EGameViewMode::CollapsedThumbnails));
		  };

		  if (SelectedActiveSlotID == static_cast<int>(gameSlot.value()))
			  widget->SetSelected(true, (viewMode == EGameViewMode::CollapsedThumbnails));


		  mActiveGameStatusWidgetsBySlot.insert({ widget, gameSlot.value() });
	  },
	  "TActiveGamesView::AddStatusWidget", false, this);
}

void TActiveGamesView::SetSelectedActiveGame(const int slotID)
{
	SelectedActiveSlotID = slotID;
	for (const auto& [widget, slotId] : mActiveGameStatusWidgetsBySlot)
		if (slotId == SelectedActiveSlotID)
			widget->SetSelected(true, (viewMode == EGameViewMode::CollapsedThumbnails));
		else
			widget->SetSelected(false, (viewMode == EGameViewMode::CollapsedThumbnails));
}

void TActiveGamesView::SetGameViewMode(EGameViewMode mode)
{
	viewMode = mode;
	if (viewMode == EGameViewMode::Grid)
	{
		plTitle->setVisible(false);
		pTitleLayout->Direction = EStackLayoutDirection::VERTICAL;
		for (const auto& w : mMenuCategories) w->setVisible(false);

		pWidgetsContainer->setVisible(false);
	}
	else
	{
		plTitle->setVisible(true);
		pTitleLayout->Direction = EStackLayoutDirection::HORIZONTAL;
		for (const auto& w : mMenuCategories) w->setVisible(true);

		pWidgetsContainer->setVisible(true);
		if (mode == EGameViewMode::CollapsedThumbnails)
		{
			pWidgetsContainer->setOrigin(0.5f, 0.0f);
			plTitle->setVisible(false);
			pTitleLayout->Padding = { 12, 0 };
		}
		else
		{
			pWidgetsContainer->setOrigin(0.f);
			plTitle->setVisible(true);
			pTitleLayout->Padding = { 24, 0 };
		}
	}
	SetSelectedActiveGame(SelectedActiveSlotID);
}
