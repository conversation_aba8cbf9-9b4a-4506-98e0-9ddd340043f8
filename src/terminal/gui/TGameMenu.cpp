#include "TGameMenu.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"

TGameMenu::TGameMenu()
{
	setId("TGameMenu");

	mLayout = setLayout<GridLayout>();

	GridLayout::FieldSize pageStackField;
	pageStackField.Size = 730;
	mLayout->AddColumn(pageStackField);

	auto brush = ImageBrush(Image::GetImage("bgTextureSemiDark.png"));
	brush.tiling = true;
	Background = brush;
	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);

	CreateMenuCategory(ELobbyButtons::Settings);
	CreateMenuCategory(ELobbyButtons::History);
	CreateMenuCategory(ELobbyButtons::Info);

	LoadGameSettings(GENERAL_SETTINGS);
	LoadGameHistory(GENERAL_SETTINGS);
	LoadGameInfo(GENERAL_SETTINGS);

	mCloseButton = new Button();
	mCloseButton->setId("close-btn");
	mCloseButton->setBackgroundImage(Image::GetImage("gameThumbnailClose.png"));
	mCloseButton->Background = Black;
	mCloseButton->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_0, tempDesignTokens::RoundedCorner_12, tempDesignTokens::RoundedCorner_0,
	                                           tempDesignTokens::RoundedCorner_12);
	mCloseButton->setSize(56, 56);
	mCloseButton->setOrigin(1.f, 0.f);
	mCloseButton->setFloating(true);
	add(mCloseButton);

	OnDimensionChanged += [this](Widget* w, const Rectangle& oldDim, const Rectangle& targetDim) {
		mCloseButton->setPosition(getWidth() - 2, 2);
	};
}

void TGameMenu::SetSelectedCategory(ELobbyButtons category)
{
	for (auto& [value, stack] : mMenuCategories) { stack->setVisible(category == value); }
}

void TGameMenu::LoadGameSettings(const size_t slotID)
{
	TUserSettings* userSettings = new TUserSettings(pSettingsContent, slotID);
	mMenuSettings.insert({ slotID, userSettings });
	pSettingsLayout->Set(userSettings, GridLayout::GridPosition(sVector2D(0, 0), sVector2D(1), EChildAlignment::Stretch));
	pSettingsContent->setVisible(true);
	userSettings->setVisible(true);
}

void TGameMenu::SetSelectedActiveGame(const size_t slotID)
{
	for (auto& [slot, menu] : mMenuSettings) { menu->setVisible(slotID == slot); }
	for (auto& [slot, history] : mGameHistory) { history->setVisible(slotID == slot); }
	mGeneralInfo->SelectInfo(slotID);
}

void TGameMenu::RemoveGame(const size_t slotID)
{
	pGuiApp->DeferToDrawSimple(
	  [this, slotID]() {
		  auto it = mMenuSettings.find(slotID);
		  if (it != mMenuSettings.end())
		  {
			  pSettingsContent->remove(it->second);
			  mMenuSettings.erase(it);
			  pSettingsContent->markLayoutDirty();
		  }

		  auto itHistory = mGameHistory.find(slotID);
		  if (itHistory != mGameHistory.end())
		  {
			  pHistoryContent->remove(itHistory->second);
			  mGameHistory.erase(itHistory);
			  pHistoryContent->markLayoutDirty();
		  }

		  mGeneralInfo->RemoveInfo(slotID);
		  // Set menu active only if there are no more games left
		  if (mMenuSettings.size() == 1)
			  SetSelectedActiveGame(GENERAL_SETTINGS);
	  },
	  "TGameMenu::RemoveGame");
}

void TGameMenu::UpdateGameSettingValues(const std::string& settings, const json& value) const
{
	for (auto& games : mMenuSettings) games.second->UpdateSettings(settings, value);
}

void TGameMenu::SetCompactView(bool compact) const
{
	for (auto& games : mMenuSettings) games.second->SetCompactView(compact);
	for (auto& games : mGameHistory) games.second->SetCompactView(compact);
}

void TGameMenu::LoadGameHistory(const size_t slotID)
{
	TGameHistory* gameHistory = new TGameHistory(slotID);
	pHistoryContent->add(gameHistory);
	mGameHistory.insert({ slotID, gameHistory });
	pHistoryLayout->Set(gameHistory, GridLayout::GridPosition(sVector2D(0, 0), sVector2D(1), EChildAlignment::Stretch));
	pHistoryContent->setVisible(true);
	gameHistory->setVisible(true);
}

void TGameMenu::LoadGameInfo(const size_t slotID) const
{
	mGeneralInfo->LoadInfo(slotID);
}

void TGameMenu::CreateMenuCategory(const ELobbyButtons category)
{
	LayoutContainer* page = NULL;

	if (category == ELobbyButtons::Settings)
	{
		page = new LayoutContainer();
		pSettingsContent = page;
		pSettingsContent->setId("settings-content");
		pSettingsContent->setVisible(true);
		pSettingsLayout = pSettingsContent->setLayout<GridLayout>();
		pSettingsLayout->AddColumn({});
	}
	else if (category == ELobbyButtons::History)
	{
		page = new LayoutContainer();
		pHistoryContent = page;
		pHistoryContent->setId("history-content");
		pHistoryContent->setVisible(true);
		pHistoryLayout = pHistoryContent->setLayout<GridLayout>();
		pHistoryLayout->AddColumn({});
	}
	else if (category == ELobbyButtons::Info)
	{
		page = new LayoutContainer();
		pInfoContent = page;
		pInfoContent->setId("info-content");
		pInfoContent->setVisible(true);
		pInfoLayout = pInfoContent->setLayout<GridLayout>();
		pInfoLayout->AddColumn({});

		mGeneralInfo = new InfoPage();

		pInfoContent->add(mGeneralInfo);
		pInfoLayout->Set(mGeneralInfo, GridLayout::GridPosition(sVector2D(0, 0), sVector2D(1), EChildAlignment::Stretch));
	}

	if (page)
	{
		mLayout->Set(page, GridLayout::GridPosition(sVector2D(0, 0), sVector2D(1), EChildAlignment::Stretch));

		mMenuCategories.insert({ category, page });
		add(page);
	}
}
