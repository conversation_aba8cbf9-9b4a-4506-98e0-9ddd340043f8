#include "TPanelMenu.h"

#include "MyUtils.h"
#include "PackmanClient.h"
#include "TAttendantPayInPanel.h"
#include "TCountersPanel.h"
#include "THoperControl.h"
#include "THopperRetryPanel.h"
#include "TOtherStationsPanel.h"
#include "TPayoutPanel.h"
#include "TPowerMenuPanel.h"
#include "YUtils.h"
#include "common/TDBApplication.h"
#include "common/TMachineBalanceManager.h"
#include "common/TMenuGUI.h"
#include "common/TStandaloneApplication.h"
#include "drv/HID/THumanInputDevices.h"
#include "gui/focushandler.hpp"

void TPanelMenu::CheckForUSB()
{
	auto param = pApp->GetParam("ExportUSBDevName");    // poskusimo brati nastavitev
	if (!param)
	{    // ce nastavitev za ime kljuca ne obstaja, nalozimo iz env-variable
		pApp->LoadParamFromEnvironmentOptional("EXPORT_USB_KEY_DEV_NAME", "ExportUSBDevName", "sda");
		param = pApp->GetParam("ExportUSBDevName");    // nalozimo pravkar vneseno nastavitev
	}
	bool isVisible = false;
	bool currentState = false;
	while (!QuitUSBCheck.try_acquire_for(std::chrono::seconds(5)))
	{
		isVisible = !param->AsString().empty() && std::filesystem::exists("/dev/" + param->AsString());    //*ali je prisoten usb kljuc*/

		if (currentState != isVisible)
		{
			pGuiApp->DeferToDraw(std::bind(&TStyleConfirmButton2::setVisible, pBtnUpdateFromUSB, isVisible), "CheckForUSB", false, this);
			currentState = isVisible;
		}
	}
}

TPanelMenu::TPanelMenu(Container* pParent, float x, float y, float w, float h) : TMenuPanelBase(pParent, x, y, w, h, false)
{
	setId("panel-menu");
	setVisible(false);
	Background = Black.Fade(0.7f);
	setConsumeAllMouseEvents(true);
	setConsumeAllKeyEvents(true);
	mLastRPCUpdateTimestamp = 0;
	// bRequestModalFocus = true;
	bMoveToTopOnShow = false;

	PanelArea = Rectangle(0, 0, 480, 800);
	PanelArea.Pos = (getSize() - PanelArea.Size) / 2;

	pBtnInternet = new TStyleButton2(this);
	pBtnInternet->setNormalImage(Image::GetImage("no-internet.png"));
	pBtnInternet->setDownImage(pBtnInternet->getNormalImage());
	pBtnInternet->setHighImage(pBtnInternet->getNormalImage());
	pBtnInternet->setDisabledImage(Image::GetImage("internet.png"));
	pBtnInternet->setDimension(PanelArea.right() - TitleBarHeight, PanelArea.y(), TitleBarHeight, TitleBarHeight);
	pBtnInternet->BackgroundImageScaleMode = EScaleMode::ZOOM_TO_CONTAIN_ONLY_DOWN;
	pBtnInternet->BackgroundImageAlignment = align::CENTER_CENTER;
	pBtnInternet->setFocusMode(EFocusMode::Deny);
	pBtnInternet->OnPressed += [this]() {
		if (mInternetError)
			MenuGUI()->ShowMenuPopupMessage(*mInternetError, 1000);
	};

	auto packman = pApp->GetModuleByName<TPackmanClient>("Packman");
	packman->OnEvent += [this](const std::string& event, const json& ctx) {
		if (event == igp::InternetStatusChangedEventName)
		{
			mInternetError = pApp->GetModuleByName<TPackmanClient>("Packman")->GetInternetStatus();
			pGuiApp->DeferToDrawSimple([this]() { pBtnInternet->setEnabled(mInternetError.has_value()); }, "PackmanInternetEnabled", false, this);
		}
	};

	mInternetError = packman->GetInternetStatus();
	pBtnInternet->setEnabled(mInternetError.has_value());

	pLblKeyCaption = AddLabel(LocalizedMessage("ADMIN MANAGER KEY INSERTED:"), PanelArea.x(), PanelArea.y(), PanelArea.width(), TitleBarHeight);
	pLblKeyCaption->setId("key-caption");

	/* payin */
	pBtnPayin = AddButton("btnPayin", LocalizedMessage(PAYIN_STRING), PanelArea.x() + 16, PanelArea.y() + 94, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnPayin->OnPressed += [this]() {
		pPnlCredit->Show();
	};

	/* payout */
	pBtnPayout = AddButton("btnPayout", LocalizedMessage(PAYOUT_STRING), PanelArea.x() + 324, PanelArea.y() + 94, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnPayout->OnPressed += [this]() {
		((TPayoutPanel*)pPnlPayout)->bDirect = true;
		pPnlPayout->Show();
	};

	/* periodic counters */
	pBtnCounter = AddButton("btn2", LocalizedMessage(PERIODIC_COUNTERS_STRING), PanelArea.x() + 16, PanelArea.y() + 162, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnCounter->OnPressed += [this]() {
		((TCountersPanel*)pPnlCounters)->SetDisplayMode(TCountersPanel::CUSTOM_COUNTERS);
		pPnlCounters->Show();
	};

	/* lifetime counters */
	pBtnTotalCounter = AddButton("btn5", LocalizedMessage(LIFETIME_COUNTERS_STRING), PanelArea.x() + 170, PanelArea.y() + 162, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnTotalCounter->OnPressed += [this]() {
		if (pTotalsAllLog)
		{
			pTotalsAllLog->ShowLogPanel(0);
		}
		else
		{
			((TCountersPanel*)pPnlCounters)->SetDisplayMode(TCountersPanel::LIFETIME_COUNTERS);
			pPnlCounters->Show();
		}
	};

	/* setup */
	pBtnSetup = AddButton("btn8", LocalizedMessage(SETUP_STRING), PanelArea.x() + 324, PanelArea.y() + 162, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnSetup->OnPressed += [this]() {
		pPnlSetup->Show();
	};

	/* terminal */
	pBtnOtherStations = AddButton("btn1", LocalizedMessage(PLATFORMS_STRING), PanelArea.x() + 16, PanelArea.y() + 230, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnOtherStations->OnPressed += [this]() {
		pOtherStationsPanel->Show();
	};

	/* keyboard */
	pBtnKeyboard = AddButton("btnKeyboard", LocalizedMessage(KEYBOARD_STRING), PanelArea.x() + 170, PanelArea.y() + 230, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnKeyboard->OnPressed += []() {
		MenuGUI()->pKeyboard->ToggleShown();
	};

	/* power */
	pBtnPowerMenu = AddButton("btn7", LocalizedMessage(POWER_MENU_STRING), PanelArea.x() + 324, PanelArea.y() + 230, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnPowerMenu->OnPressed += [this]() {
		pPnlPowerMenu->Show();
	};

	/* update from USB */
	pBtnUpdateFromUSB = AddConfirmButton("btn13", LocalizedMessage(USB_LOAD_STRING), PanelArea.x() + 16, PanelArea.y() + 325, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnUpdateFromUSB->mCaptionList.push_back(LocalizedMessage(LOAD_QUESTION_STRING));
	pBtnUpdateFromUSB->mCaptionList.push_back(LocalizedMessage(LOADING_STRING));
	pBtnUpdateFromUSB->setVisible(false);
	pBtnUpdateFromUSB->OnPressed += [this]() {
		CopyUpdateFromUSBAndExecute();
	};

	/* print last ticket */
	pBtnPrintLastTicket = AddButton("btnMax", LocalizedMessage(PRINT_LAST_TICKET_STRING), PanelArea.x() + 324, PanelArea.y() + 325, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnPrintLastTicket->setVisible(false);
	pBtnPrintLastTicket->OnPressed += [this]() {
		bool wasPrinted = true;
		switch (MenuGUI()->pMoneyAcceptor->PrintLastTicket())
		{
			case -2: AddNotification({ LocalizedMessage(ERROR_STRING), LocalizedMessage(PRINTER_NOT_READY_STRING) }, 4, EVerbosity::Error); break;
			case -3:
				wasPrinted = false;
				AddNotification({ LocalizedMessage(ERROR_STRING), LocalizedMessage(NO_LAST_TICKET_STRING), LocalizedMessage(PRINTING_TEST_TICKET_STRING) }, 3,
				                EVerbosity::Error);
				break;
			case 0: AddNotification({ LocalizedMessage(PRINTING_LAST_TICKET_STRING) }, 4); break;
		}

		if (!wasPrinted)
		{
			time_t TimeSeconds = time(NULL);
			tm Time;
			localtime_r(&TimeSeconds, &Time);
			char strDate[50];
			char strTime[50];
			strftime(strDate, 50, "%F", &Time);
			strftime(strTime, 50, "%T", &Time);

			if (MenuGUI()->pMoneyAcceptor->PrintTicket("010123456789098769", "DEMO TICKET", "Ticket Address 1", "Ticket Address 2", "VOID  DEMO  VOID", "VOID 00000 EUR",
			                                           "ZERO", strDate, strTime, "Test ticket", "15 days", "Gamble or die.", "010123456789098769", "VALIDATION",
			                                           "Ticket void after", true))
				AddNotification({ LocalizedMessage(PRINTING_TEST_TICKET_STRING), LocalizedMessage("010123456789098769"), LocalizedMessage(TAKE_TICKET_STRING) }, 8,
				                EVerbosity::Important);
			else
				AddNotification(
				  { LocalizedMessage(PRINTING_TEST_TICKET_STRING), LocalizedMessage(ERROR_STRING), LocalizedMessage(PRINTER_CONNECTED_PAPPER_LOADED_STRING) }, 7,
				  EVerbosity::Error);
		}
	};

	/* hopper control */
	pBtnHoperControl = AddButton("btnPay", LocalizedMessage(HOPERS_STRING), PanelArea.x() + 170, PanelArea.y() + 325, 140, 54, &MenuGUI()->PanelBigButtons);
	pBtnHoperControl->setVisible(false);
	pBtnHoperControl->OnPressed += [this]() {
		pPnlHoperControl->Show();
	};

	std::string serialNum = pApp->MachineName();
	const bool bIsMultiplayer = packman->Features().contains(igp::MultiplayerFeatureName);
	if (bIsMultiplayer)
		serialNum += "#" + std::to_string(dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Number);
	pLblSerial = AddLabel(LocalizedMessage(SERIAL_NUMBER_STRING) + (": " + serialNum), PanelArea.x() + 24, PanelArea.y() + 410, 430, 20);
	pLblSerial->setId("serial");
	pLblValidity = AddLabel({}, PanelArea.x() + 24, PanelArea.y() + 430, 430, 20);
	pLblValidity->setId("validity");
	plJurisdiction = AddLabel({}, PanelArea.x() + 24, PanelArea.y() + 450, 430, 20);
	plJurisdiction->setId("jurisdiction");

	std::shared_ptr<TAppParam> licence_param = pApp->GetParam("LICENSE_VALID_UNTIL", { ParameterDomain::MINE, ParameterDomain::CACHE });
	if (licence_param && !licence_param->IsNull())
	{
		licence_param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::VALUE))
			{
				pLblValidity->setCaption(LocalizedMessage(SOFTWARE_LICENCE_VALID_UNTIL_STRING) + (" " + (param->AsString().empty() ? "UNREGISTRED" : param->AsString())));
			}
		};
		licence_param->OnParamChange(licence_param.get(), { ParameterProperty::VALUE }, ParameterChangeSource::Application);
	}

	// arrow left + right
	ImagePtr normalL = Image::GetImage("sliderLeftN.png");
	TStyleButton2* left = AddButton("btnLeft", {}, PanelArea.x() + 4, PanelArea.y() + 550 + 5, normalL->getWidth(), normalL->getHeight());
	left->setNormalImage(normalL);
	left->setDownImage(Image::GetImage("sliderLeftP.png"));
	left->setDisabledImage(Image::GetImage("sliderLeftD.png"));
	left->setHighImage(Image::GetImage("sliderLeftP.png"));
	left->ClickSound = "button_press.wav";
	left->OnPressed += [this]() {
		mMD5Idx--;
		if (mMD5Idx < 0)
			mMD5Idx = (mAssets.size() - 1);

		SetAssetCaptions();
	};

	TStyleButton2* right = AddButton("btnRight", {}, PanelArea.x() + 430, PanelArea.y() + 550 + 5, normalL->getWidth(), normalL->getHeight());
	right->setNormalImage(normalL);
	right->setDownImage(left->getDownImage());
	right->setDisabledImage(left->getDisabledImage());
	right->setHighImage(left->getHighImage());
	right->BackgroundImageFlip = GPU_FLIP_HORIZONTAL;
	right->ClickSound = "button_press.wav";
	right->OnPressed += [this]() {
		mMD5Idx++;
		if (mMD5Idx > (int)(mAssets.size() - 1))
			mMD5Idx = 0;

		SetAssetCaptions();
	};

	// name + manufacturer + version + md5
	mMD5Idx = 0;
	plAssetName = AddLabel(LocalizedMessage(NAME_STRING), PanelArea.x() + 44, PanelArea.y() + 530, 400, 20, Color::Gray(0xb4));
	plAssetName->setId("asset");
	plAssetManufacturer = AddLabel(LocalizedMessage(MANUFACTURER_STRING), PanelArea.x() + 44, PanelArea.y() + 550, 400, 20, Color::Gray(0xb4));
	plAssetManufacturer->setId("manufacturer");
	plAssetVersion = AddLabel(LocalizedMessage(VERSION_TEXT_STRING), PanelArea.x() + 44, PanelArea.y() + 570, 400, 20, Color::Gray(0xb4));
	plAssetVersion->setId("version");
	plAssetChecksum = AddLabel(LocalizedMessage(igp::ChecksumType._to_string()), PanelArea.x() + 44, PanelArea.y() + 590, 400, 20, Color::Gray(0xb4));
	plAssetChecksum->setId("checksum");
	plAssetCheck = AddLabel(LocalizedMessage(CHECKSUM_STRING), PanelArea.x() + 44, PanelArea.y() + 610, 400, 20, Color::Gray(0xb4));
	plAssetCheck->setId("checksum");

	UpdateAssetsData();

	/* exit */
	pBtnClose = AddButton("btnClear", LocalizedMessage(EXIT_STRING), PanelArea.x() + PanelArea.width() / 2, PanelArea.y() + 655, 294, 54, &MenuGUI()->PanelLongButtons);
	pBtnClose->setOrigin(0.5f, 0.f);
	pBtnClose->ClickSound = "menu_back.wav";
	pBtnClose->OnPressed += []() {
		pApp->GenerateAction("HideAdminMenu");
	};
	mCloseButtonEnabled = true;

	std::shared_ptr<TAppParam> adminMenuKeyOutParam = pApp->GetOrSetParam(
	  "CLOSE_ADMIN_MENU_ON_KEY_OUT", "1", "Close Menu On Key Out", "Automatically close the admin menu when the key is removed.", PARAM_READ_MINE_OR_GROUP_WRITE_GROUP);
	adminMenuKeyOutParam->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))    // samo ko se value spremeni
		{
			mCloseButtonEnabled = !param->AsInteger();
			pBtnClose->setEnabled(mCloseButtonEnabled);
		}
	};
	adminMenuKeyOutParam->OnParamChange(adminMenuKeyOutParam.get(), { ParameterProperty::OLD_VALUE }, ParameterChangeSource::Application);

	pLblTemperature = AddLabel({}, PanelArea.x() + 355, PanelArea.y() + 778, 115, 13);    //, VALUE_FONT_COLOR+0x0a0a0a);
	pLblTemperature->setId("temperature");
	pLblTemperature->setAlignment(align::RIGHT_CENTER);
	pLblTemperature->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 11_px });
	pLblTemperature->setVisible(true);

	pLblFPS = AddLabel({}, PanelArea.x() + 10, PanelArea.y() + 778, 115, 13);    //, VALUE_FONT_COLOR+0x0a0a0a);
	pLblFPS->setId("fps");
	pLblFPS->setAlignment(align::LEFT_CENTER);
	pLblFPS->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 11_px });
	pLblFPS->setVisible(true);

	pLblCurrentDateTime = AddLabel({}, PanelArea.x() + 10, PanelArea.y() + 710, 460, 23);
	pLblCurrentDateTime->setId("time");

	pLblPeripheralWarning = AddLabel(LocalizedMessage(PRINTER_READY_STRING), PanelArea.x() + 10, PanelArea.y() + 380, 460, 20);
	pLblPeripheralWarning->setId("peripheral");

	if (TerminalFlavour == EFlavour::BOSS)
		pLblNoKeyProgrammedWarning = AddLabel(LocalizedMessage(NO_KEY_PROGRAMMED_STRING), 0, 0, getWidth(), 30, Color::FromRGBInteger(0xFF0009));
	else
		pLblNoKeyProgrammedWarning = AddLabel(LocalizedMessage(NO_KEY_PROGRAMMED_STRING), 0, getHeight() - 85, getWidth(), 30, Color::FromRGBInteger(0xFF0009));

	pLblNoKeyProgrammedWarning->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 28_px });
	pLblNoKeyProgrammedWarning->setId("key-warning");

	std::shared_ptr<TAppParam> attendantPayin =
	  pApp->GetOrSetParam("ALLOW_PAYIN_BY_ATTENDANT", "0", "Allow Payin By Attendant", "Allow attendant payin with the key, regardless of the key priority.",
	                      PARAM_READ_MINE_OR_GROUP_OR_GLOBAL_WRITE_GLOBAL);
	attendantPayin->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
		{
			AllowPayinByAttendant = param->AsInteger();
		}
	};
	attendantPayin->OnParamChange(attendantPayin.get(), { ParameterProperty::OLD_VALUE }, ParameterChangeSource::Application);
	AllowPayinByAttendant = attendantPayin->AsInteger();

	int diff_y = 0;
	if (TerminalFlavour == EFlavour::EBX)
		diff_y = 20;

	// uporabimo drugacne parametre za ustvarjanje panelov
	/* izplačila */
	pPnlPayout = new TPayoutPanel(this, PanelArea.x(), PanelArea.y(), 480, 800);
	pPnlPayout->setVisible(false);

	/* vplačila */
	pPnlCredit = new TAttendantPayInPanel(this, PanelArea.x(), PanelArea.y(), 480, 800);
	pPnlCredit->setVisible(false);

	/* counter */
	pPnlCounters = new TCountersPanel(this, PanelArea.x(), PanelArea.y(), 480, 800);
	pPnlCounters->setVisible(false);
	pPnlCounters->bMoveToTopOnShow = true;

	/* totals all */
	if (pApp->GetParam("ScreenW")->AsInteger() == 1920)
		pTotalsAllLog = new TTotalsAllLog(this, 320, 28 - diff_y, 1280, 1024);
	else
		pTotalsAllLog = new TTotalsAllLog(this, 0, 0, 1280, 1024);
	pTotalsAllLog->bMoveToTopOnShow = true;

	/* power */
	pPnlPowerMenu = new TPowerMenuPanel(this, PanelArea.x(), PanelArea.y(), 480, 800);
	pPnlPowerMenu->setVisible(false);

	/* setup */
	if (pApp->GetParam("ScreenW")->AsInteger() == 1920)
		pPnlSetup = new TSetupPanel(this, 30, 28.f - diff_y, 1860, 1024);
	else
		pPnlSetup = new TSetupPanel(this, 0, 0, 1280, 1024);
	pPnlSetup->setVisible(false);

	/* hopper */
	pPnlHoperRetry = new THopperRetryPanel(this, PanelArea.x(), PanelArea.y(), 480, 800);
	pPnlHoperRetry->setVisible(false);

	/* hopper control */
	if (TerminalFlavour == EFlavour::AGTPLUS || TerminalFlavour == EFlavour::BOSS2)
		pPnlHoperControl = new THoperControl(this, 231, 157, 820, 710);
	else if (TerminalFlavour == EFlavour::EBX || TerminalFlavour == EFlavour::IMAXA || TerminalFlavour == EFlavour::GAMEART || TerminalFlavour == EFlavour::TRONIUS)
		pPnlHoperControl = new THoperControl(this, 550, 185, 820, 710);
	else
		pPnlHoperControl = new THoperControl(this, 0, 0, 820, 710);

	pPnlHoperControl->setVisible(false);

	/* terminals */
	if (pApp->GetParam("ScreenW")->AsInteger() == 1920)
		pOtherStationsPanel = new TOtherStationsPanel(this, 30, 28 - diff_y, 1860, 1024);
	else
		pOtherStationsPanel = new TOtherStationsPanel(this, 0, 0, 1280, 1024);

	const std::shared_ptr<TAppParam> jurisdiction_param = pApp->GetParam("JURISDICTION", { ParameterDomain::GLOBAL, ParameterDomain::CACHE });
	jurisdiction_param->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::VALUE))
			plJurisdiction->setCaption(LocalizedMessage(JURISDICTION_STRING) + (": " + param->AsString()));
	};
	jurisdiction_param->OnParamChange(jurisdiction_param.get(), { ParameterProperty::VALUE }, ParameterChangeSource::Application);

	// menu language slide
	std::shared_ptr<TAppParam> defMenuLanguage = pApp->GetOrSetParam("MENU_LANGUAGE", "11", "Menu language", "Default menu language", PARAM_READ_GLOBAL_WRITE_GLOBAL);

	std::shared_ptr<TAppParam> currMenuLanguage = std::make_shared<TAppParam>("CURRENT_MENU_LANGUAGE", pApp->ClientID());
	currMenuLanguage->SetValue(defMenuLanguage->AsString());

	pMenuLanguage = new TStyleSlider(this, PanelArea.x() + 115, PanelArea.y() + 760, 250, 30, currMenuLanguage, false);
	pMenuLanguage->setId("language-selector");
	for (ELanguage lang : ELanguage::_values())
	{
		if (lang <= ELanguage::None)
			continue;
		std::shared_ptr<TLangDescriptor> pTmp = pApp->LangMessages->GetDescriptor(lang, false);
		if (pTmp && !pTmp->AllMessages().empty())
			pMenuLanguage->AddValue(LocalizedMessage(pTmp->Language._to_string()), std::to_string(pTmp->Language._to_integral()), pTmp->Language._to_integral());
	}
	currMenuLanguage->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::VALUE))    // samo ko se value spremeni
		{
			auto lang = ELanguage::_from_integral_nothrow(param->AsInteger());
			MenuGUI()->MenuLanguage = lang ? *lang : ELanguage(ELanguage::English);    // spremenimo menu language
			SetLanguage(MenuGUI()->MenuLanguage);
			((TOtherStationsPanel*)pOtherStationsPanel)->mpPnlLastGames->SetLanguage(MenuGUI()->MenuLanguage);
		}
	};
	pMenuLanguage->SetValue(ESliderInputType::SetByCode, defMenuLanguage->AsString(), defMenuLanguage->AsInteger());

	SetLanguage(MenuGUI()->MenuLanguage);

	MenuGUI()->pAdminKeySystem->OnAdminKeyChanged += [this](TAdminKeySystem* sys, const TAdminKeyDescriptor& newKey) {
		if (newKey.ID == EAdminKeys::NoKey)
		{
			CancelDelayMenuShow();
			return;
		}

		if (newKey.Code == SINGLE_ACCESS_KEY)
		{
			mCloseButtonEnabled = pBtnClose->isEnabled();
			pBtnClose->setEnabled(true);
		}

		BeforeShow();
	};

	pApp->NewActionListener<TahMember<TPanelMenu>>("OnShowAdminMenu", this, &TPanelMenu::BeforeShow);

	pApp->NewActionListener<TahMember<TPanelMenu>>("OnUSBUpdateRequest", this, &TPanelMenu::CopyUpdateFromUSBAndExecute);

	pApp->NewActionListener<TahMember<TPanelMenu>>("OnIntegrityCheck", this, &TPanelMenu::UpdateAssetsData);

	USBThread = std::thread(std::bind(&TPanelMenu::CheckForUSB, this));
	pthread_setname_np(USBThread.native_handle(), "CheckUSB");

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (!bVisible)
			OnHide();
	};

	MenuGUI()->pHID->OnCalibrationHide += [this]() {
		if (MenuGUI()->pHID->GetTouchCalibrationComplete())
			pGuiApp->DeferToDrawSimple(std::bind(&TPanelMenu::ShowStationAssignDialogIfNotAssigned, this), "ShowStationAssignDialogIfNotAssigned", false, this);
	};
}

void TPanelMenu::BeforeShow()
{
	MenuGUI()->pKeyboard->SetInputType(EInputType::STRING);
	MenuGUI()->pKeyboard->Hide();

	pApp->GenerateAction("HideConsole");

	if (MenuGUI()->pClient->CLIENT_HANDPAY_LOCK->IsLocked() || TerminalFlavour == EFlavour::EBX)
		DelayedShow();
	else
		DelayedShowHandle = pApp->Delay(std::bind(&TPanelMenu::DelayedShow, this), 500, "TPanelMenu::DelayedShow", TASK_DRAW);
}

void TPanelMenu::DelayedShow()
{
	TPanelBase::Show();

	mMD5Idx = GetFirstAssetCheckFailed();
	if (mMD5Idx >= (int)mAssets.size())
		mMD5Idx = 0;
	SetAssetCaptions();

	pLblNoKeyProgrammedWarning->setVisible(0 == MenuGUI()->pAdminKeySystem->GetProgrammedKeysCount());

	if (MenuGUI()->pClient->CLIENT_HANDPAY_LOCK->IsLocked())    // če smo v handpay lock, takoj prikažemo payoutpanel
	{
		pApp->DoAsyncTask([this]() {
			FPendingHandpayInfo info;
			if (dynamic_cast<TStandaloneApplication*>(pApp)->GetPendingHandpay(info))
			{
				pGuiApp->DeferToDrawSimple(
				  [payoutEndPanel = pPnlPayout->pPayoutEnd, info]() {
					  payoutEndPanel->SetInfo(info);
					  payoutEndPanel->Show();
				  },
				  "GetPendingHandpay", false, pPnlPayout->pPayoutEnd);
			}
		});
	}

	/// če dela hoper, takoj prikazemo menu za kovance - to gre rekurzivno dol
	if ((MenuGUI()->pMoneyAcceptor->IsHoperErrorAndCoinsPending(RECYCLER_1) && MenuGUI()->pMoneyAcceptor->GetHoperCTG(RECYCLER_1)) ||
	    (MenuGUI()->pMoneyAcceptor->IsHoperErrorAndCoinsPending(HOPPER_1) && MenuGUI()->pMoneyAcceptor->GetHoperCTG(HOPPER_1)) ||
	    (MenuGUI()->pMoneyAcceptor->IsHoperErrorAndCoinsPending(HOPPER_2) && MenuGUI()->pMoneyAcceptor->GetHoperCTG(HOPPER_2)))
	{
		Hide();
		pPnlHoperRetry->Show();
	}
	if (MenuGUI()->pHID->GetTouchCalibrationComplete())
		ShowStationAssignDialogIfNotAssigned();
}

void TPanelMenu::OnHide()
{
	CancelDelayMenuShow();

	pApp->GenerateAction("AttendantMenuOnClose");
	if (pGuiApp->GUI() && pGuiApp->GUI()->IsLoaded() && MenuGUI()->pKeyboard)
		MenuGUI()->pKeyboard->Hide();

	pBtnClose->setEnabled(mCloseButtonEnabled);
}

TPanelMenu::~TPanelMenu()
{
	QuitUSBCheck.release();
	USBThread.join();
	pApp->UnregisterAllActionHandlersByRelatedObject(this);
}

void TPanelMenu::drawLogic(Graphics* graphics, float deltaTime)
{
	std::string cap = LocalizedMessage(KEY_STRING).Get(Language());
	cap += " ";
	if (MenuGUI()->pAdminKeySystem->GetCurrentKey().ID != EAdminKeys::NoKey)
	{
		cap += std::to_string((int)MenuGUI()->pAdminKeySystem->GetCurrentKey().ID);
		cap += " - ";
		cap += MenuGUI()->pAdminKeySystem->GetCurrentKey().Description;
	}
	else
	{
		cap += " - ";
		cap += MenuGUI()->pAdminKeySystem->GetLastKey().Description;
	}
	pLblKeyCaption->setCaption(cap);

	// PAYIN - je odvisen od parametra
	pBtnPayin->setVisible(AllowPayinByAttendant && MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::PAYIN));
	pBtnPayin->setEnabled(MenuGUI()->pClient->BalanceManager()->IsPayinPossible() && MenuGUI()->pClient->IsEnabled() && !MenuGUI()->pClient->IsInDemoMode());
	pBtnPayout->setEnabled(MenuGUI()->pClient->BalanceManager()->IsPayoutPossible() && MenuGUI()->pClient->BalanceManager()->GetBalance().Total() &&
	                       !MenuGUI()->pClient->IsInDemoMode());
	pBtnPayout->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::PAYOUT));
	pBtnCounter->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::COUNTERS));
	pBtnTotalCounter->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::TOTAL_COUNTERS));
	pBtnSetup->setVisible(dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Type != "UNKNOWN" && pPnlSetup->HasAnyPermission());

	pBtnPowerMenu->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::POWER));
	if (pBtnOtherStations)
		pBtnOtherStations->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::OTHER_STATIONS));

	pBtnHoperControl->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::HOPER_CONTROL) && MenuGUI()->pMoneyAcceptor->IsCoinConnected() &&
	                             (MenuGUI()->pMoneyAcceptor->IsHopperCommunicationOK(HOPPER_1) || MenuGUI()->pMoneyAcceptor->IsHopperCommunicationOK(HOPPER_2)));
	pBtnPrintLastTicket->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::PAYOUT) && MenuGUI()->pMoneyAcceptor->IsTicketPrinterConnected() &&
	                                MenuGUI()->pMoneyAcceptor->IsTicketPrinterReady());

	pLblCurrentDateTime->setCaption(yutils::FormatTime("%F %T", std::chrono::system_clock::now()));

	LocalizedMessage caption;
	if (MenuGUI()->pMoneyAcceptor->IsTicketPrinterConnected())
	{
		if (MenuGUI()->pMoneyAcceptor->IsTicketHeadUp())
			caption = LocalizedMessage(TICKET_HEAD_UP_STRING);
		else if (MenuGUI()->pMoneyAcceptor->IsTicketChasisOpen())
			caption = LocalizedMessage(TICKET_CHASIS_OPEN_STRING);
		else if (MenuGUI()->pMoneyAcceptor->IsTicketPaperJam())
			caption = LocalizedMessage(TICKET_PAPER_JAM_STRING);
		else if (!MenuGUI()->pMoneyAcceptor->IsTicketPaperLoaded())
		{
			if (MenuGUI()->pMoneyAcceptor->IsTicketLow())
				caption = LocalizedMessage(TICKET_PAPER_OUT_STRING);
			else
				caption = LocalizedMessage(TICKET_PAPER_NOT_LOADED_STRING);
		}
		else if (MenuGUI()->pMoneyAcceptor->IsTicketLow())
			caption = LocalizedMessage(TICKET_PAPER_LOW_STRING);
	}

	if (MenuGUI()->pMoneyAcceptor->IsBillAcceptorConnected())
	{
		LocalizedMessage billCaption;

		if (!MenuGUI()->pMoneyAcceptor->IsEnabled())
		{
			billCaption = LocalizedMessage(BILLACCEPTOR_INHIBIT_STRING);
		}
		else if (/* "JAM IN STACKER" == pMoneyAcceptor->getBillAcceptorStatus() || "STACKER OPEN" == pMoneyAcceptor->getBillAcceptorStatus() ||*/
		         MenuGUI()->pMoneyAcceptor->getBillAcceptorStatusForFlag(
		           EBillAcceptorStatus::STACKER_FULL) /*|| "JAM IN ACCEPTOR" == pMoneyAcceptor->getBillAcceptorStatus()*/)
		{
			billCaption = (LocalizedMessage(BILL_STRING) + (" " + LocalizedMessage(MSG_BILL_ERROR_STACKER_FULL)));
		}

		if (!billCaption.empty())
		{
			if (!caption.empty())
				caption = caption + (" - " + billCaption);
			else
				caption = billCaption;
		}
	}

	if (caption.empty())
	{
		pLblPeripheralWarning->setCaption("");
		pLblPeripheralWarning->setVisible(false);
	}
	else
	{
		pLblPeripheralWarning->setCaption(caption);
		pLblPeripheralWarning->setVisible(true);
	}

	pLblFPS->setCaption("FPS = " + MyUtils::int2str((int)pGuiApp->GUI()->getFPS()));

	if (MyUtils::IsTime(mLastRPCUpdateTimestamp, 1000, true))
	{
		// v novem menuju, v spodnjem desnem kotu namesto verzije prikazujemo temperaturo
		// prikazana vrednost se posodablja vsako sekundo
		const json temperatureJson = pApp->GetComponentStatus("cpu.temperature");
		const int64_t temperature = temperatureJson.is_string() ? yutils::strToInt(temperatureJson.get<std::string>().c_str(), -1) : -1;
		pLblTemperature->setCaption((temperatureJson.is_string() ? temperatureJson.get<std::string>() : "?") + "/90°C");
		if (temperature > 80)
			pLblTemperature->TextColor = Red;
		else if (temperature > 70)
			pLblTemperature->TextColor = Yellow;
		else
			pLblTemperature->TextColor = White;
	}

	// varovalka, če ni fokusa, potem se zapremo! - po vseh močeh sem tole poskusil rešiti bolje, pa se vdam, naj bo tako ...
	if (!Gui::Get() || !Gui::Get()->getFocused())
	{
		if (MenuGUI()->pHID->GetKeyboardType())    // ce bo potrebno
		{
			TLOG(LogGUI, Warning, "FOCUS_NONE PanelMENU CLOSING!!!!");
			pApp->GenerateAction("HideAdminMenu");
			return;
		}
	}

	TPanelBase::drawLogic(graphics, deltaTime);
}

void TPanelMenu::drawBackground(Graphics* graphics, const Outline& outline)
{
	TPanelBase::drawBackground(graphics, outline);

	Rectangle mainArea(0, 0, 480, 800);
	mainArea.Pos = (getSize() - mainArea.Size) / 2;

	// window background
	graphics->fillRectangleWithColor(mainArea, Color::Gray(51), 2.f);

	// title header
	graphics->fillRectangleWithColor(Rectangle(mainArea.x() + 1.f, mainArea.y() + 1.f, mainArea.width() - 2.f, TitleBarHeight), Color::Gray(32));

	// window border
	graphics->drawRectangle(mainArea, Color::Gray(220), 2.f, 2.f);

	Rectangle bottomArea(mainArea.x() + EDGE_MARGIN, 542, mainArea.width() - 2 * EDGE_MARGIN, 230);
	graphics->fillRectangleWithColor(bottomArea, Color::Gray(25), 2);
	graphics->drawRectangle(bottomArea, Color::Gray(200), 1.f, 2.f);
}

void TPanelMenu::CopyUpdateFromUSBAndExecute() {}

void TPanelMenu::UpdateAssetsData()
{
	mAssets.clear();

	const TPackmanClient* Packman = pApp->GetModuleByName<TPackmanClient>("Packman");

	const std::string jurisdiction = dynamic_cast<TStandaloneApplication*>(pApp)->Jurisdiction->AsString();

	Packman->ProductPackage().ForEach([this, Packman](const igp::FInstalledPackage& pack, const std::list<std::string>& parentPath) {
		mAssets.push_back(
		  AssetData(pack.ID.Name, pack.Info.Manufacturer, pack.Info.Manifest.versionString, pack.Checksum, !Packman->ChecksumFailures().contains(pack.ID)));
	});

	for (const auto& host : Packman->Hosts())
	{
		if (!host.second || !host.second->bDefault || host.second->Versions.empty())
			continue;

		for (const auto& [component, version] : host.second->Versions) { mAssets.push_back(AssetData(component, {}, version, {}, true)); }
	}

	Packman->ForAllGames([this, jurisdiction](const std::shared_ptr<igp::PlatformGamePackage>& game) -> bool {
		if (!game->Package.CanBeExecuted() || !game->Package.Game.ApprovedForJurisdictions.contains(jurisdiction))
			return true;

		mAssets.push_back(AssetData(igp::PackFileLocation(game->Package.Game.Name, igp::package_type::Game).filename(),
		                            game->Configurations.empty() ? "unknown" : game->Configurations.begin()->second->StaticGame().Manufacturer,
		                            game->Package.PackageInfo().Manifest.versionString, game->Package.GetChecksum(), game->Package.IsChecksumOK()));
		return true;
	});

	if (isVisible())
		SetAssetCaptions();
}

void TPanelMenu::SetAssetCaptions()
{
	if (mAssets.size() == 0)
		return;

	const bool md5_enabled = pApp->GetParam("DO_INTEGRITY_CHECK")->AsInteger();
	AssetData data = mAssets.at(mMD5Idx);

	plAssetName->setCaption(LocalizedMessage(NAME_STRING) + (": " + data.Name));
	plAssetManufacturer->setCaption(LocalizedMessage(MANUFACTURER_STRING) + (": " + data.Manufacturer));
	plAssetVersion->setCaption(LocalizedMessage(VERSION_TEXT_STRING) + (": " + data.Version));
	plAssetChecksum->setCaption(std::string(igp::ChecksumType._to_string()) + ": " + (md5_enabled ? data.Checksum : LocalizedMessage(INTEGRITY_CHECK_DISABLED_STRING)));

	plAssetCheck->setCaption(LocalizedMessage(CHECKSUM_STRING).Get() + " " + (data.CheckOK ? LocalizedMessage(OK_STRING).Get() : LocalizedMessage(FAILED_STRING).Get()));
	plAssetCheck->TextColor = data.CheckOK ? Green : Red;
	plAssetCheck->setVisible(md5_enabled);
}

int TPanelMenu::GetFirstAssetCheckFailed()
{
	// return the index of asset with failed check or last index
	int idx = 0;
	for (const AssetData& asset : mAssets)
	{
		if (!asset.CheckOK)
			break;

		idx++;
	}

	return idx;
}

void TPanelMenu::CancelDelayMenuShow()
{
	if (auto task = DelayedShowHandle.lock())
		task->Remove();
	DelayedShowHandle.reset();
}

bool TPanelMenu::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	if (ev.Action == EHardwareButtonAction::Autoplay)
	{
		if (ev.bPressed && pBtnClose->isEnabled() && isFocused())
		{
			pApp->GenerateAction("HideAdminMenu");
		}
		return true;
	}

	return TMenuPanelBase::DoAction_Implementation(ev);
}

void TPanelMenu::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	if (pBtnClose->isEnabled())
		outAvailableActions.SetAvailable(EHardwareButtonAction::Autoplay, EXIT_STRING);    // hide or exit

	outAvailableActions.SetAvailable(EHardwareButtonAction::Payout, PREV_ELEMENT_STRING);    // prev element
	outAvailableActions.SetAvailable(EHardwareButtonAction::GameSelect, NEXT_ELEMENT_STRING);    // next element
}


void TPanelMenu::ShowStationAssignDialogIfNotAssigned()
{
	if ((dynamic_cast<const TDBInterface*>(pApp)->RegisteredInstance().Type == "UNKNOWN") && pOtherStationsPanel)
		((TOtherStationsPanel*)pOtherStationsPanel)->AssignStationDialog(false);
}

void TPanelMenu::onLanguageChanged(ELanguage lang)
{
	TPanelBase::onLanguageChanged(lang);
	SetAssetCaptions();
}

TPanelBase* TPanelMenu::GetOpenPanel() const
{
	if (!getEffectiveVisiblity(false))
		return nullptr;

	for (auto it = children().rbegin(); it != children().rend(); it++)
	{
		TPanelBase* panel = dynamic_cast<TPanelBase*>(it->Child);
		if (panel && panel->getEffectiveVisiblity(false))
			return panel;
	}

	return nullptr;
}

bool TPanelMenu::IsAttendantMenuOpened() const
{
	return getEffectiveVisiblity(false) || GetOpenPanel() != nullptr;
}
