#include "TMachineConfig.h"

#include "MyUtils.h"
#include "TAdminKeySystem.h"
#include "TIGPlatformApp.h"
#include "YUtils.h"
#include "common/TAnyGameClient.h"

#define SLIDER_MARGIN 5.f
TMachineConfig::TMachineConfig(Container* pParent, float x, float y, float w, float h) : TSetupBase(pParent, x, y, w, h)
{
	setId("machine-config");
	mCaption = LocalizedMessage(GAMING_MACHINE_SETUP_STRING);

	// if (pBtnSaveForAll)
	//	pBtnSaveForAll->setVisible(true);

	SetChildrenTabInEnabled(false);    // izkljucimo tabin za komponente ki so do zdaj na panelu

	// payment
	mplPaymentTitle = AddLabel(LocalizedMessage(PAYMENT_STRING), 0, 0, w / 2 - 15, 0);
	mplPaymentTitle->setPadding(Rectangle(0, 5, 0, 0));
	mplPaymentTitle->setAlignment(align::CENTER_TOP);
	mplPaymentTitle->Background = Black.Fade(0.3f);
	mplPaymentTitle->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 26_px });

	pvsPayoutButtonDisabled =
	  AddValueSelect(LocalizedMessage(PAYOUT_BUTTON_DISABLED_STRING), pApp->GetParam("PAYOUT_BUTTON_DISABLED", { ParameterDomain::GROUP, ParameterDomain::CACHE }), 0,
	                 mplPaymentTitle->getY() + 40 + SLIDER_MARGIN, 250, 30, 250);
	pvsPayoutButtonDisabled->AddValue(LocalizedMessage(DISABLED_STRING), "1", 1);
	pvsPayoutButtonDisabled->AddValue(
	  LocalizedMessage(ENABLED_STRING, [](const std::string& msg) -> std::string { return yutils::Format("%s", MyUtils::ToUppercase(msg).c_str()); }), "0", 0);
	pvsPayoutButtonDisabled->AddValue(LocalizedMessage(DIRECT_PAYOUT_STRING), "2", 2);

	pvsAllowZeroPayout = AddCheckBox(LocalizedMessage(ALLOW_ZERO_PAYOUT_STRING), pApp->GetParam("ALLOW_ZERO_CASHOUT", { ParameterDomain::GROUP, ParameterDomain::CACHE }),
	                                 0, pvsPayoutButtonDisabled->getDimension().bottom() + SLIDER_MARGIN, 50, 30, 250);

	pvsAttendandAllowPayin =
	  AddCheckBox(LocalizedMessage(ATTENDANT_ALLOW_PAYIN_STRING), pApp->GetParam("ALLOW_PAYIN_BY_ATTENDANT", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0,
	              pvsAllowZeroPayout->getDimension().bottom() + EDGE_MARGIN, 50, 30, 250);

	pvsAllowPartialPayout =
	  AddCheckBox(LocalizedMessage(ATTENDANT_PARTIAL_PAYOUT_STRING), pApp->GetParam("ALLOW_PARTIAL_PAYOUT", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0,
	              pvsAttendandAllowPayin->getDimension().bottom() + EDGE_MARGIN, 50, 30, 250);

	const std::string credits = LocalizedMessage(CREDIT_STRING).Get(ELanguage::English);

	// when using a high base denomination, this helps skew options into the right range
	const int centMultiplier = MenuGUI()->pClient->CreditToMoney(1) * 100;

	const std::list<int> max_payin_options = { 100,   150,   200,   250,    300,    350,    400,    450,     500,     600,     700,     800,
		                                       900,   1000,  1250,  1500,   1750,   2000,   2500,   5000,    7000,    10000,   15000,   20000,
		                                       25000, 50000, 70000, 100000, 200000, 500000, 700000, 1000000, 2000000, 5000000, 7000000, 10000000 };
	pvsAttendandMaxPayin =
	  AddValueSelect(LocalizedMessage(ATTENDANT_PAYIN_MAXIMUM_STRING), pApp->GetParam("ATTENDANT_MAX_PAYIN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0,
	                 pvsAllowPartialPayout->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250);
	for (int option : max_payin_options)
		pvsAttendandMaxPayin->AddValue(MenuGUI()->FormatCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);
	pvsAttendandMaxPayin->AddValue(LocalizedMessage(NO_LIMIT_STRING), "0", 0);

	const std::list<int> credit_limit_options = { 10,     50,     100,    250,     500,     1000,    1500,     2000,      2500,     3000,
		                                          3500,   4000,   4500,   5000,    7000,    10000,   25000,    50000,     75000,    100000,
		                                          250000, 500000, 750000, 1000000, 2500000, 5000000, 10000000, 100000000, 999999999 };
	pvsMaxCredits = AddValueSelect(LocalizedMessage(BALANCE_LIMIT_STRING), pApp->GetParam("MAX_CREDITS", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0,
	                               pvsAttendandMaxPayin->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250);
	for (int option : credit_limit_options)
		pvsMaxCredits->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);

	const std::list<int> pay_in_out_notify_options = { 100,  250,  500,  1000,  1500,  2000,  2500,  3000,   3500,  4000,
		                                               4500, 5000, 7000, 10000, 25000, 50000, 75000, 100000, 500000 };
	pPayInAlarm = AddValueSelect(LocalizedMessage(PAY_IN_ALARM_STRING), pApp->GetOrSetParam("PAY_IN_ALARM", "0"), 0,
	                             pvsMaxCredits->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250, true);
	pPayInAlarm->AddValue(LocalizedMessage(OFF_STRING), "0", 0);
	for (int option : pay_in_out_notify_options)
		pPayInAlarm->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);

	pPayOutAlarm = AddValueSelect(LocalizedMessage(PAY_OUT_ALARM_STRING), pApp->GetOrSetParam("PAY_OUT_ALARM", "0"), 0,
	                              pPayInAlarm->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250, true);
	pPayOutAlarm->AddValue(LocalizedMessage(OFF_STRING), "0", 0);
	for (int option : pay_in_out_notify_options)
		pPayOutAlarm->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);

	mplPaymentTitle->setHeight((h - 54 - 2 * EDGE_MARGIN) / 2);

	// limits
	mplLimitsTitle = AddLabel(LocalizedMessage(LIMITS_STRING), 0, mplPaymentTitle->getDimension().bottom() + EDGE_MARGIN, w / 2 - 15.f, 0);
	mplLimitsTitle->setPadding(Rectangle(0, 5, 0, 0));
	mplLimitsTitle->setAlignment(align::CENTER_TOP);
	mplLimitsTitle->Background = Black.Fade(0.3f);
	mplLimitsTitle->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 26_px });

	const std::list<int> win_limit_options = { 10,   50,   100,   250,   500,   1000,  1500,   2000,   2500,   3000,   3500,    4000,    4500,
		                                       5000, 7000, 10000, 25000, 50000, 75000, 100000, 250000, 500000, 750000, 1000000, 2500000, 5000000 };

	cLimitsList = new TScrollArea(this, 0, mplLimitsTitle->getY() + 40 + EDGE_MARGIN, 537, 433 - 40 - EDGE_MARGIN);
	cLimitsList->setId("game-limits-list");
	cLimitsList->getContentLayout()->Alignment = EChildAlignment::None;
	cLimitsList->setVisible(true);
	cLimitsList->setScrollBarVisibility(TScrollArea::EScrollBarVisibility::Always);
	cLimitsList->getContentLayout()->Padding.Y() = 15.f;

	LayoutContainer* maxWinLimitLayout = CreateLayoutContainerWithStackLayout(cLimitsList->ContentContainer());
	pvsMaxWinLimit = AddValueSelect2(maxWinLimitLayout, LocalizedMessage(MANDATORY_HANDPAY_WIN_LIMIT_STRING),
	                                 pApp->GetParam("MAX_WIN_LIMIT", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0, 0, 250, 30, 250);
	for (int option : win_limit_options)
		pvsMaxWinLimit->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);
	pvsMaxWinLimit->AddValue(LocalizedMessage(NEVER_STRING), "0", 0);

	LayoutContainer* maxPossibleWinLimit = CreateLayoutContainerWithStackLayout(cLimitsList->ContentContainer());
	pvsMaxPossibleWinLimit = AddValueSelect2(maxPossibleWinLimit, LocalizedMessage(MAX_POSSIBLE_WIN_STRING),
	                                         pApp->GetParam("MAX_POSSIBLE_WIN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0, 50, 250, 30, 250);
	for (int option : win_limit_options)
		pvsMaxPossibleWinLimit->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);
	pvsMaxPossibleWinLimit->AddValue(LocalizedMessage(UNLIMITED_STRING), "0", 0);

	LayoutContainer* maxMaxBet = CreateLayoutContainerWithStackLayout(cLimitsList->ContentContainer());
	pvsMaxBet = AddValueSelect2(maxMaxBet, LocalizedMessage(MAX_BET_STRING), pApp->GetParam("MAX_BET_CENTS", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), 0, 0,
	                            250, 30, 250);
	// long lTmpCents;
	const std::list<int> max_bet_options = { 10,   20,    30,    40,    50,    60,    70,    80,    90,    100,   150,   200,   250,   300,  400,
		                                     500,  600,   700,   800,   900,   1000,  1500,  2000,  2500,  3000,  4000,  5000,  6000,  7000, 8000,
		                                     9000, 10000, 15000, 20000, 25000, 30000, 40000, 50000, 60000, 70000, 80000, 90000, 100000 };
	for (int option : max_bet_options)
		pvsMaxBet->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), std::to_string(option * centMultiplier), option * centMultiplier);
	pvsMaxBet->AddValue(LocalizedMessage(UNLIMITED_STRING), "0", 0);

	pApp->RebootOnParam("MAX_BET_CENTS");

	for (GameType type : GameType::_values())
	{
		if (type == GameType::Null)
			continue;

		LayoutContainer* pGroup = CreateLayoutContainerWithStackLayout(cLimitsList->ContentContainer());

		std::string suffix = type._to_string();
		pMaxBetSliders[type] = AddValueSelect2(pGroup, LocalizedMessage(MAX_BET_STRING) + ("\n" + suffix),
		                                       dynamic_cast<TIGPlatformApp*>(pApp)->GetGameTypeMaxBetParameter(type), 0, 0, 250, 30, 250, true);
		for (int option : max_bet_options)
			pMaxBetSliders[type]->AddValue(MenuGUI()->FormatCreditsAsCurrency(option * centMultiplier), yutils::Format("%.02f", option * centMultiplier * 1e-2),
			                               option * centMultiplier);
		pMaxBetSliders[type]->AddValue(LocalizedMessage(UNLIMITED_STRING), "0", 0);
	}

	mplLimitsTitle->setHeight((h - 54 - 2 * EDGE_MARGIN) / 2);

	const float SecondColumnX = w / 2.f;

	mplLocksTitle = AddLabel(LocalizedMessage(LOCK_PLATFORM_STRING), SecondColumnX, 0, w / 2, 0);
	mplLocksTitle->setPadding(Rectangle(0, 5, 0, 0));
	mplLocksTitle->setAlignment(align::CENTER_TOP);
	mplLocksTitle->Background = Black.Fade(0.3f);
	mplLocksTitle->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 26_px });

	cLocksList = new TScrollArea(this, SecondColumnX, mplLocksTitle->getY() + 40 + EDGE_MARGIN, 537, 433 - 40 - EDGE_MARGIN);
	cLocksList->setId("locks-list");
	cLocksList->getContentLayout()->Alignment = EChildAlignment::None;
	cLocksList->setVisible(true);
	cLocksList->getContentLayout()->Padding.Y() = 15.f;

	pSLockIfBigDoorOpen = AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	                                   new TToggleButton(pApp->GetParam("LOCK_IF_BIG_DOOR_OPEN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	                                   LocalizedMessage(LOCK_IF_BIG_DOOR_OPEN_STRING), 0, 0, 50, 30, 300);

	pSLockIfSmallDoorOpen = AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	                                     new TToggleButton(pApp->GetParam("LOCK_IF_SMALL_DOOR_OPEN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	                                     LocalizedMessage(LOCK_IF_SMALL_DOOR_OPEN_STRING), 0, pSLockIfBigDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	pSLockIfLogicDoorOpen = AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	                                     new TToggleButton(pApp->GetParam("LOCK_IF_LOGIC_DOOR_OPEN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	                                     LocalizedMessage(LOCK_IF_LOGIC_DOOR_OPEN_STRING), 0, pSLockIfSmallDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	pSLockIfDepositDoorOpen =
	  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	               new TToggleButton(pApp->GetParam("LOCK_IF_DEPOSIT_BOX_DOOR_OPEN", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	               LocalizedMessage(LOCK_IF_DEPOSIT_DOOR_OPEN_STRING), 0, pSLockIfLogicDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	pSLockIfCountersError = AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	                                     new TToggleButton(pApp->GetParam("LOCK_IF_MECHANICAL_COUNTERS_ERROR", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	                                     LocalizedMessage(LOCK_IF_COUNTERS_ERROR_STRING), 0, pSLockIfDepositDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	pSAutoDoorUnlock = AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	                                new TToggleButton(pApp->GetParam("AUTO_UNLOCK_ON_DOOR_ERROR", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	                                LocalizedMessage(AUTO_UNLOCK_DOOR_ERRORS_STRING), 0, pSLockIfCountersError->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	pSAutoMoneyPeripheralsUnlock =
	  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()),
	               new TToggleButton(pApp->GetParam("AUTO_UNLOCK_ON_MONEY_PERIPHERAL_ERROR", { ParameterDomain::GLOBAL, ParameterDomain::CACHE })),
	               LocalizedMessage(AUTO_UNLOCK_MONEY_ERRORS_STRING), 0, pSAutoDoorUnlock->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

	auto packman = pApp->GetModuleByName<TPackmanClient>("Packman");
	const bool bIsWheelConnected = packman->Features().contains(igp::WheelFeatureName);
	if (bIsWheelConnected)
	{
		std::shared_ptr<TAppParam> bigLock = pApp->GetOrSetParam("STOP_GAME_IF_BIG_DOOR_OPEN", "0", "Big Door Open Error",
		                                                         "Should the game be stopped if the big door is open.", PARAM_READ_GLOBAL_WRITE_GLOBAL);
		std::shared_ptr<TAppParam> smallLock = pApp->GetOrSetParam("STOP_GAME_IF_SMALL_DOOR_OPEN", "0", "Small Door Open Error",
		                                                           "Should the game be stopped if the small door is open.", PARAM_READ_GLOBAL_WRITE_GLOBAL);
		std::shared_ptr<TAppParam> logicLock = pApp->GetOrSetParam("STOP_GAME_IF_LOGIC_DOOR_OPEN", "1", "Logic Door Open Error",
		                                                           "Should the game be stopped if the logic door is open.", PARAM_READ_GLOBAL_WRITE_GLOBAL);
		std::shared_ptr<TAppParam> depositLock = pApp->GetOrSetParam("STOP_GAME_IF_DEPOSIT_BOX_DOOR_OPEN", "0", "Deposit Box Door Open Error",
		                                                             "Should the game be stopped if the DepositBox door is open.", PARAM_READ_GLOBAL_WRITE_GLOBAL);
		std::shared_ptr<TAppParam> cupolaLock = pApp->GetOrSetParam("STOP_GAME_IF_COUPOLA_OPEN", "1", "Coupola Open Error",
		                                                            "Should the game be stopped if the coupola door is open.", PARAM_READ_GLOBAL_WRITE_GLOBAL);

		pStopGameIfBigDoorOpen =
		  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()), new TToggleButton(bigLock),
		               LocalizedMessage(STOP_GAME_IF_BIG_DOOR_OPEN_STRING), 0, pSAutoMoneyPeripheralsUnlock->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);
		pStopGameIfSmallDoorOpen =
		  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()), new TToggleButton(smallLock),
		               LocalizedMessage(STOP_GAME_IF_SMALL_DOOR_OPEN_STRING), 0, pStopGameIfBigDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);
		pStopGameIfLogicDoorOpen =
		  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()), new TToggleButton(logicLock),
		               LocalizedMessage(STOP_GAME_IF_LOGIC_DOOR_OPEN_STRING), 0, pStopGameIfSmallDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);
		pStopGameIfDepositDoorOpen =
		  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()), new TToggleButton(depositLock),
		               LocalizedMessage(STOP_GAME_IF_DEPOSIT_DOOR_OPEN_STRING), 0, pStopGameIfLogicDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);
		pStopGameIfCupolaDoorOpen =
		  AddCheckBox2(CreateLayoutContainerWithStackLayout(cLocksList->ContentContainer()), new TToggleButton(cupolaLock),
		               LocalizedMessage(STOP_GAME_IF_COUPOLA_DOOR_OPEN_STRING), 0, pStopGameIfDepositDoorOpen->getDimension().bottom() + EDGE_MARGIN, 50, 30, 300);

		bigLock->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
				pStopGameIfBigDoorOpen->setChecked(param->AsBoolean());
		};

		smallLock->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
				pStopGameIfSmallDoorOpen->setChecked(param->AsBoolean());
		};

		logicLock->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
				pStopGameIfLogicDoorOpen->setChecked(param->AsBoolean());
		};

		depositLock->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
				pStopGameIfDepositDoorOpen->setChecked(param->AsBoolean());
		};

		cupolaLock->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
			if (changeFlag.HasFlag(ParameterProperty::OLD_VALUE))
				pStopGameIfCupolaDoorOpen->setChecked(param->AsBoolean());
		};
		cLocksList->setScrollBarVisibility(TScrollArea::EScrollBarVisibility::Always);
	}
	mplLocksTitle->setHeight((h - 54 - 2 * EDGE_MARGIN) / 2);

	mplLanguageAudioTitle = AddLabel(LocalizedMessage(LANGUAGE_AND_AUDIO_STRING), SecondColumnX, mplLocksTitle->getDimension().bottom() + EDGE_MARGIN, w / 2, 0);
	mplLanguageAudioTitle->setPadding(Rectangle(0, 5, 0, 0));
	mplLanguageAudioTitle->setAlignment(align::CENTER_TOP);
	mplLanguageAudioTitle->Background = Black.Fade(0.3f);
	mplLanguageAudioTitle->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 26_px });

	pLanguageVS = AddValueSelect(LocalizedMessage(TEXT_LANGUAGE_STRING), pApp->GetParam("DEF_LANGUAGE", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }),
	                             SecondColumnX, mplLanguageAudioTitle->getY() + 40 + EDGE_MARGIN, 250, 30, 250);    // def language
	pMenuLanguageVS = AddValueSelect(LocalizedMessage(MENU_LANGUAGE_STRING), pApp->GetParam("MENU_LANGUAGE", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }),
	                                 SecondColumnX, pLanguageVS->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250);    // menu language
	for (ELanguage lang : ELanguage::_values())
	{
		if (lang <= ELanguage::None)
			continue;
		std::shared_ptr<TLangDescriptor> pTmp = pApp->LangMessages->GetDescriptor(lang, false);
		if (pTmp && !pTmp->AllMessages().empty())
		{
			pLanguageVS->AddValue(pApp->LangMessages->GetLangCaption(pTmp->Language), std::to_string(pTmp->Language._to_integral()), pTmp->Language._to_integral());
			pMenuLanguageVS->AddValue(pApp->LangMessages->GetLangCaption(pTmp->Language), std::to_string(pTmp->Language._to_integral()), pTmp->Language._to_integral());
		}
	}
	pDefaultFocused = pLanguageVS;

	// volume za vse terminale na ruleti
	const std::list<int> volume_options = { 10, 20, 30, 40, 50, 55, 60, 65, 70, 75, 80, 85, 90, 95, 100 };
	pSVolumeSelect =
	  AddValueSelect(LocalizedMessage(SYSTEM_VOLUE_STRING),
	                 pApp->GetOrSetParam("PLAYBACK_VOLUME", "75", "Playback Volume", "Volume of sound for the game and sound effects on the station. A value of 0-100.",
	                                     PARAM_READ_MINE_OR_GROUP_OR_GLOBAL_WRITE_GLOBAL),
	                 SecondColumnX, pMenuLanguageVS->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250);
	pSVolumeSelect->AddValue(LocalizedMessage(MUTE_STRING), "0", 0);
	for (int option : volume_options) pSVolumeSelect->AddValue(std::to_string(option), std::to_string(option), option);
	pSVolumeSelect->disableSound();    // nocemo zvoka ker ob spremembi predvajamo vzorcnega na spodnji callback

	mAudioVolumeLevel = pSVolumeSelect->Param()->AsInteger();
	pSVolumeSelect->Param()->OnParamChange += [this](TAppParam* param, const ParamPropertyBitflag& changeFlag, const ParameterChangeSource source) {
		if (changeFlag.HasFlag(ParameterProperty::VALUE))    // samo ko se value spremeni
		{
			// dodali smo timer, da v primeru hitrega dodajanja, repet bets, undo bets, tisci timer dol in se izvede sele na koncu
			if (pVolumeUpdateTask && pVolumeUpdateTask->IsValid())
				pVolumeUpdateTask->Enable();
			else
				pVolumeUpdateTask = pApp->Delay(std::bind(&TMachineConfig::OnPlayChangeVolume, this), 500);
		}
	};

	const std::list<int> attract_time_options = { 1, 2, 3, 4, 5, 10, 20, 30, 45, 60, 120, 180, 240, 300, 360, 720 };
	pSoundCheckTime =
	  AddValueSelect(LocalizedMessage(AUDIO_CHECK_TIME_STRING), pApp->GetParam("PLAYBACK_CHECK_TIME", { ParameterDomain::GLOBAL, ParameterDomain::CACHE }), SecondColumnX,
	                 pSVolumeSelect->getDimension().bottom() + SLIDER_MARGIN, 250, 30, 250);
	pSoundCheckTime->AddValue(LocalizedMessage(OFF_STRING), "0", 0);
	for (int option : attract_time_options)
		pSoundCheckTime->AddValue(std::to_string(option >= 60 ? (option / 60) : option) + (option >= 60 ? "h" : "min"), std::to_string(option), option);

	mplLanguageAudioTitle->setHeight((h - 54 - 2 * EDGE_MARGIN) / 2);

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (bVisible)
		{
			mAudioVolumeLevel = pSVolumeSelect->Param()->AsInteger();
		}
	};
}

void TMachineConfig::drawLogic(Graphics* gfx, float dt)
{
	// payment
	mplPaymentTitle->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::ROULETTE_SETUP_PAYMENT));
	pvsAttendandAllowPayin->setVisible(mplPaymentTitle->isVisible());
	pvsAttendandMaxPayin->setVisible(pvsAttendandAllowPayin->getChecked() && mplPaymentTitle->isVisible());
	pvsAllowPartialPayout->setVisible(mplPaymentTitle->isVisible());
	pvsMaxCredits->setVisible(mplPaymentTitle->isVisible());
	// pvsMaxWinLimit->setVisible(mplPaymentTitle->isVisible());
	pPayInAlarm->setVisible(mplPaymentTitle->isVisible());
	pPayOutAlarm->setVisible(mplPaymentTitle->isVisible());

	mplLimitsTitle->setVisible(MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::ROULETTE_SETUP_PAYMENT));
	pvsMaxWinLimit->setVisible(mplLimitsTitle->isVisible());
	pvsMaxPossibleWinLimit->setVisible(mplLimitsTitle->isVisible());
	if (pvsMaxBet)
		pvsMaxBet->setVisible(mplLimitsTitle->isVisible());
	for (GameType type : GameType::_values())
	{
		if (type == GameType::Null)
			continue;

		if (pMaxBetSliders[type])
			pMaxBetSliders[type]->setVisible(mplLimitsTitle->isVisible());
	}


	TSetupBase::drawLogic(gfx, dt);
}

void TMachineConfig::AfterSaveData(const std::string& ClientIP)
{
	// do not change user playback volume
	// pApp->SetParam("USER_PLAYBACK_VOLUME", mAudioVolumeLevel, ParameterDomain::CACHE);
}

void TMachineConfig::OnPlayChangeVolume()
{
	mAudioVolumeLevel = pSVolumeSelect->Param()->AsInteger();
	MyUtils::SystemAsync("./set_audio_volume_igp.sh " + std::to_string(mAudioVolumeLevel));
	pGuiApp->PlaySound("win_1.wav");
}

LayoutContainer* TMachineConfig::CreateLayoutContainerWithStackLayout(Container* parent) const
{
	LayoutContainer* layout = new LayoutContainer(parent, 0.f, 0.f, 0.f, 0.f);
	layout->setId("layout-holder");
	layout->bResizeToFitLayout = true;
	StackLayout* stackLayout = layout->setLayout<StackLayout>();
	stackLayout->Alignment = EChildAlignment::Center;
	stackLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	return layout;
}

TMachineConfig::~TMachineConfig()
{
	pApp->UnregisterAllActionHandlersByRelatedObject(this);
}
