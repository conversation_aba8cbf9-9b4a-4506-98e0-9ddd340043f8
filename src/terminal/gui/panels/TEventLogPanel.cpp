#include "TEventLogPanel.h"

#include "MyUtils.h"
#include "Timing.h"
#include "YUtils.h"

TEventLogPanel::TEventLogPanel(Container* pParent, float x, float y, float w, float h) : TLogBase(pParent, x, y, w, h)
{
	setId("events");
	bShowPleaseWait = false;
	mCaption = LocalizedMessage(EVENT_LOG_STRING);

	int c1 = 180;
	int c2 = 102;

	FirstPage()->AddTitleColumn(LocalizedMessage(TIME_STRING), "LOG_TIME" /*IMENA SQL PARAMETROV MORAJO BITI VELIKE CRKE, CE NE HECA*/, c1);
	FirstPage()->AddTitleColumn(LocalizedMessage(CLIENT_STRING), "CLIENT" /*IMENA SQL PARAMETROV MORAJO BITI VELIKE CRKE, CE NE HECA*/, c2);
	FirstPage()->AddTitleColumn(LocalizedMessage(MESSAGE_STRING), "LOG_MESSAGE" /*IMENA SQL PARAMETROV MORAJO BITI VELIKE CRKE, CE NE HECA*/, -1);
	//	FirstPage()->setNLines(14);

	pMyDataSet.FieldTransformer = std::bind(&TEventLogPanel::TransformField, this, std::placeholders::_1, std::placeholders::_2);
	timeOffset = ytime::GetUTCOffsetSeconds();
}

int TEventLogPanel::OnRefresh(int FilterMode, bool disableCache)
{
	if (pMyDataSet.GetDBMngr()->DatabaseType == TGameDBSrv::EDBType::postgresql)
		pMyDataSet.DBSQL = "select EXTRACT(EPOCH FROM (LOG.LOG_TIME - '1970-01-01 00:00')) * 1000 as LOG_TIME, LOG.CLIENT_IP as CLIENT,";
	else
		pMyDataSet.DBSQL = "select DATEDIFF(millisecond, TIMESTAMP'1970-01-01 00:00', LOG.LOG_TIME) as LOG_TIME, LOG.CLIENT_IP as CLIENT,";

	pMyDataSet.DBSQL += " LOG.LOG_MESSAGE,";
	pMyDataSet.DBSQL += " LOG.LOG_ID";
	pMyDataSet.DBSQL += " from  LOG";
	pMyDataSet.DBSQL += " where log.LOG_TYPE_ID <> 100602";
	pMyDataSet.DBSQL += " and log.log_type_id <> 100601";
	pMyDataSet.DBSQL += " and (log.log_type_id < " + std::to_string(LS_TICKET);
	pMyDataSet.DBSQL += " or log.log_type_id > " + std::to_string(LS_TICKET | LOG_TYPE_MASK);
	pMyDataSet.DBSQL += ")and (log.log_type_id < " + std::to_string(LS_SAS);
	pMyDataSet.DBSQL += " or log.log_type_id > " + std::to_string(LS_SAS | LOG_TYPE_MASK);
	pMyDataSet.DBSQL += ")and (log.log_type_id < " + std::to_string(LS_OPERATOR_ACTIVITY);
	pMyDataSet.DBSQL += " or log.log_type_id > " + std::to_string(LS_OPERATOR_ACTIVITY | LOG_TYPE_MASK);
	pMyDataSet.DBSQL += ")";
	switch (FilterMode)
	{
		case 0:
			pMyDataSet.DBSQL += " and log.CLIENT_IP like '" + Station.ID + "'";    // This PB
			break;
		case 1:
			pMyDataSet.DBSQL += " and (log.CLIENT_IP like '" + Station.Type + "_%')";    // All Playboards of this type
			break;
		case 2:    // Game and Playboards
			break;
	}
	pMyDataSet.DBSQL += " order by log.LOG_ID descending";

	return 0;
}

void TEventLogPanel::TransformField(size_t colIdx, TDBField& field) const
{
	switch (colIdx)
	{
		case 0:    // timestamp
			field = TDBField::FromTimestamp(field.asInt64() / 1000 - timeOffset, "%F %T");
			break;
		case 1:    // client ip
			if (field.asString() == "*************")
				field = TDBField("WHEEL");
			else if (field.asString() == "DB")
				field = TDBField("DATABASE");
			else if (field.asString() == "*************")
				field = TDBField("REMOTE ADMIN");
			else if (field.asString().starts_with("192.168.0.") && field.asString() != "***********")
				field = TDBField("PLAYBOARD " + field.asString().substr(10));
			else
				field = TDBField(yutils::ReplaceAll(field.asString(), "_", " "));
			break;
		default: break;
	}
}
