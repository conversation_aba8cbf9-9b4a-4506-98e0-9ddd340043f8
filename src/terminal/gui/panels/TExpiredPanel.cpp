#include "TExpiredPanel.h"

#include "MyUtils.h"
#include "common/TStandaloneApplication.h"

TExpiredPanel::TExpiredPanel(Container* pParent, float x, float y, float w, float h) : TSetupBase(pParent, x, y, w, h)
{
	mCaption = LocalizedMessage(EXPIRE_MANAGER_STRING);

	auto param = pApp->GetParam("INSTALL_DATE");
	if (param)
	{
		AddLabel(LocalizedMessage(INSTALL_DATE_STRING) + ": " + param->AsString(), 0, 10, w, 30);
	}
	plblEnterSoftwareCode = AddLabel(LocalizedMessage(PLAEASE_ENTER_SOFTWARE_UNLOCK_CODE_STRING), 0, 40, w, 30);
	plblReportChallengeCode = AddLabel(LocalizedMessage(MSG_YOU_CAN_REQUEST_UNLOCK_BY_REPORTING_CHALLANGE), 0, 70, w, 30);

	const Typography font24({ .fontFamily = { { "Noto Sans" } }, .fontSize = 24_px });

	pName = AddLabel(LocalizedMessage(ROULETTE_NAME_STRING) + ": ", 0, 150, w, 50);
	pName->setForceUpperCase(true);
	pName->mTypography = font24;

	pChallengeCode = AddLabel(LocalizedMessage(CHALLENGE_CODE_STRING) + ": ", 0, 210, w, 50);
	pChallengeCode->mTypography = font24;

	pLicenceValidUntil = AddLabel(LocalizedMessage(SOFTWARE_LICENCE_VALID_UNTIL_STRING), 0, 270, w, 50);
	pLicenceValidUntil->setForceUpperCase(true);
	pLicenceValidUntil->mTypography = font24;

	mTextOldLength = 0;

	pCodeEdit = AddTextEdit("code", {}, {}, 0, 475, w - 100, 60, EInputType::HEX);
	pCodeEdit->bMoveCarretToEndWhenClicked = true;
	pCodeEdit->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 52_px });
	pCodeEdit->setEnableColoration(false);
	pDefaultFocused = pCodeEdit;

	pBtnClear = AddButton("btnClearAll", LocalizedMessage(CLEAR_STRING), w - 90, 470, 80, 54, &MenuGUI()->PanelButtons);
	pBtnClear->setOrigin(0.f, 1.f);
	pBtnClear->OnPressed += [this]() {
		pCodeEdit->setText({}, ParameterChangeSource::Application);
		mTextOldLength = 0;
	};

	pBtnExit->setVisible(true);
	pBtnExit->setEnabled(true);
	pBtnExit->setOrigin(0.f, 1.f);
	pBtnExit->setPosition(pBtnClear->getX(), pBtnClear->getY() + pBtnClear->getHeight() + 10);
	pBtnExit->Text->setCaption(LocalizedMessage(REFRESH_STRING));
	pBtnExit->OnPressed.clear();
	pBtnExit->OnPressed += [this]() {
		mButtonCounter++;
		if (mButtonCounter == 1)
			pBtnExit->Text->setCaption(LocalizedMessage(EXIT_STRING));

		if (mButtonCounter == 2)
			Hide();
		else
			OnRefresh();
	};

	// pBtnClear->setY(pCodeEdit->getY() + ((pCodeEdit->getHeight() - pBtnClear->getHeight()) / 2));    // sredinska poravnava
	// pBtnOK->setPosition(pBtnClear->getX(), pBtnClear->getY() + pBtnClear->getHeight() + 10);
	pBtnOK->setOrigin(1.f);
	pBtnOK->setPosition(w - (bEmbedded ? 0.f : EDGE_MARGIN), h - (bEmbedded ? 0.f : EDGE_MARGIN));

	mButtonCounter = 0;

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (bVisible)
		{
			mButtonCounter = 0;

			pBtnExit->Text->setCaption(LocalizedMessage(REFRESH_STRING));
			OnRefresh();
		}
	};
}

void TExpiredPanel::OnRefresh()
{
	std::string IPToUse = pApp->ClientID();

	// get challenge code
	auto param = pApp->GetExactParam(IPToUse, "CHALLENGE_CODE", { ParameterDomain::MINE });
	LocalizedMessage cCode = LocalizedMessage(CHALLENGE_CODE_STRING) + ": ";
	if (param)
		cCode = cCode + param->Value();
	else
		cCode = cCode + "NO_CHALLENGE_CODE";

	pChallengeCode->setCaption(cCode);

	// get machine name
	LocalizedMessage tmpName = LocalizedMessage(ROULETTE_NAME_STRING) + (": " + dynamic_cast<const TStandaloneApplication*>(pApp)->MachineName());
	pName->setCaption(tmpName);

	// get valid until
	param = pApp->GetExactParam(IPToUse, "LICENSE_VALID_UNTIL", { ParameterDomain::MINE });
	LocalizedMessage tmpValidity = LocalizedMessage(SOFTWARE_LICENCE_VALID_UNTIL_STRING) + " ";
	if (param)
		tmpValidity = tmpValidity + param->Value();
	else
		tmpValidity = tmpValidity + "UNREGISTRED";
	pLicenceValidUntil->setCaption(tmpValidity);

	// get expire code
	param = pApp->GetExactParam(IPToUse, "SOFTWARE_EXPIRE", { ParameterDomain::MINE });
	std::string tmpCode;
	if (param)
		tmpCode += param->Value();

	pCodeEdit->setText(AddTextChars(tmpCode), ParameterChangeSource::Application);
	mTextOldLength = pCodeEdit->getText().length();
}

void TExpiredPanel::drawLogic(Graphics* gfx, float dt)
{
	std::string text = RemoveTextChars(pCodeEdit->getText());
	if (text.length() > 0)
	{
		if (text.length() > 16)
		{    // only 16 chars allowed
			text = text.substr(0, 16);
		}

		// toUppercase
		pCodeEdit->setText(MyUtils::ToUppercase(AddTextChars(text)), ParameterChangeSource::Application);

		// kurzor premaknemo na konec samo ko dodajamo besedilo, da je zaradi : zmeraj na koncu
		// ter ko je koda ze napisana in zelimo popraviti samo eno crko
		if (mTextOldLength < pCodeEdit->getText().length() && pCodeEdit->getText().length() != 23)
			pCodeEdit->setCaretPosition(pCodeEdit->getText().length());

		mTextOldLength = pCodeEdit->getText().length();
	}

	TSetupBase::drawLogic(gfx, dt);
}

std::string TExpiredPanel::RemoveTextChars(const std::string& text)
{
	std::string str = text;
	str.erase(std::remove(str.begin(), str.end(), ':'), str.end());
	return str;
}

std::string TExpiredPanel::AddTextChars(const std::string& text)
{
	std::string str;
	for (unsigned int i = 0; i < text.length(); i++)
	{
		str += text.at(i);
		if (i != text.length() - 1 && ((i + 1) % 2 == 0))
			str += ":";
	}

	return str;
}

bool TExpiredPanel::Save(bool showMessage /* = true */)
{
	std::string Code;

	Code = RemoveTextChars(pCodeEdit->getText());
	if ("0000000000000000" == Code)
	{    // TODO: preverjanje CRC-ja vnešene kode..
		AddNotification({ LocalizedMessage(INVALID_CODE_STRING) }, 2, EVerbosity::Warning);
		return false;
	}
	else if (Code.empty())
	{
		AddNotification({ LocalizedMessage(INVALID_CODE_STRING) }, 2, EVerbosity::Warning);
		return false;
	}

	pApp->SetExactParam(pApp->ClientID(), "SOFTWARE_EXPIRE", Code, false, "The expire code", "The expiry code to check in-app");

	AddNotification({ LocalizedMessage(PARAMETERS_SAVED_STRING), LocalizedMessage(THANK_YOU_STRING) }, 2, EVerbosity::Important);

	return true;
}
