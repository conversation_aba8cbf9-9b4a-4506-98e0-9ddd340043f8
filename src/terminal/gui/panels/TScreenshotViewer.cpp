#include "TScreenshotViewer.h"

#include <utility>

#include "MyUtils.h"
#include "TIGPlatformApp.h"
#include "TPanelMenu.h"
#include "common/TDBApplication.h"

constexpr const int HALF_REEL = REEL_SIZE / 2;

const int REEL_BASE_ANIMATION_DURATION = 600;

// A value representing how easily animations are elongated when a large number of images needs to be scrolled through
// A lower numbers means animations will be elongated at lower image scroll numbers

const float REEL_SWIPE_MULTIPLIER = 10.f;
const float REEL_SWIPE_EXPONENT = 1.3f;

const float SCREENSHOT_RELATIVE_SIZE_X = 0.60f;    // The maximum relative size along the X axis of the selected image (relative to the entire screen width)
const float SCREENSHOT_RELATIVE_SIZE_Y = 0.8f;    // The maximum relative size along the Y axis of the selected image (relative to the entire screen height)

const int TIMESTAMP_FONT_SIZE = 28;
const int EXTRA_GAME_RESULTS_FONT_SIZE = 32;

const float SCREENSHOT_REEL_SCROLL_DEADZONE = 10.f;
const float SCREENSHOT_REEL_SCROLL_SMOOTHINGINTERP_SPEED = 0.1f;

// #define SCREENSHOTS_DEBUG

const int buttonHeight = 54;
const int buttonMargin = 10;
const int titleMargin = 10;

const int LogsAndCountersWidth = 650;

const int bottomBarHeight = 2 * buttonMargin + buttonHeight + 25;

void TScreenshotViewer::threadedLoad(uint64_t loaderID, int screenN, const SavedGameround& round, const FTranslatedTextToUse& translations)
{
	ScreenshotData loaded;

	if (!round.ScreenshotFile.empty())
		loaded.screenshot = MyUtils::LoadSurface(round.ScreenshotFile, TAppGuiModule::NativePixelFormatSDL);

	const auto modifyTime = std::filesystem::file_time_type::clock::to_sys(round.Time);
	const std::string dateString = yutils::FormatTime("%B %d, %Y", modifyTime);
	const std::string timeString = yutils::FormatTime("%T", modifyTime);

	timestampFont.Lock();
	auto ttfFontTimestamp = (&timestampFont)->SDL_Font;
	TTF_SetFontStyle(ttfFontTimestamp, TTF_STYLE_BOLD);
	loaded.modifiedDate = TTF_RenderText_Blended(ttfFontTimestamp, dateString.c_str(), Graphics::toGpuColor(White));
	loaded.modifiedTime = TTF_RenderText_Blended(ttfFontTimestamp, timeString.c_str(), Graphics::toGpuColor(White));
	TTF_SetFontStyle(ttfFontTimestamp, TTF_STYLE_NORMAL);
	timestampFont.Unlock();
	if (round.RoundInfo.NumResults > 1 || round.RoundInfo.TotalBet)
	{
		ScopedLock lock(numExtraResultsFont);
		auto ttfFontExtraResults = (&numExtraResultsFont)->SDL_Font;
		TTF_SetFontStyle(ttfFontExtraResults, TTF_STYLE_BOLD);
		if (round.RoundInfo.NumResults > 1)
		{
			const std::string extraTextCaption = "+" + std::to_string(round.RoundInfo.NumResults - 1) + " " + translations.Results;
			loaded.extraText = TTF_RenderUTF8_Blended(ttfFontExtraResults, extraTextCaption.c_str(), Graphics::toGpuColor(White));
		}

		if (round.RoundInfo.TotalBet)
		{
			const std::string betCaption = translations.Bet + ": " + MyUtils::currency2string(mDenomination * round.RoundInfo.TotalBet) + " " + mCurrency;
			loaded.betText = TTF_RenderUTF8_Blended(ttfFontExtraResults, betCaption.c_str(), Graphics::toGpuColor(White));
			const std::string winCaption = translations.Win + ": " + MyUtils::currency2string(mDenomination * round.RoundInfo.TotalWin) + " " + mCurrency;
			loaded.winText = TTF_RenderUTF8_Blended(ttfFontExtraResults, winCaption.c_str(), Graphics::toGpuColor(White));
		}
		const std::string denomCaption =
		  std::format("1 {} = {:.02f} {}", translations.Credit, MenuGUI()->pClient->CreditToMoney(round.RoundInfo.VirtualCreditCoefficient), mCurrency);
		loaded.denomiText = TTF_RenderUTF8_Blended(ttfFontExtraResults, denomCaption.c_str(), Graphics::toGpuColor(White));
		TTF_SetFontStyle(ttfFontExtraResults, TTF_STYLE_NORMAL);
	}

	// On completed
	pGuiApp->DeferToDraw(
	  [this, loaderID, screenN, loaded](Graphics* graphics) {
		  if (mActiveLoadID != loaderID)
			  return;

		  const float reelEndPos = (int)((mOverrideSwipePosition < 0.f) ? mTargetReelPos : mOverrideSwipePosition);

		  const bool bWillBeInReel = (std::abs(screenN - reelEndPos) <= HALF_REEL);    // if this screenshot will be inside the reel position we are animating to

		  if (bWillBeInReel && pReel[screenN].bAttemptedLoad)    // if it will not be in the reel or it didn't load anything, fuck it!
		  {
			  saveTextureToReel(screenN, loaded);
		  }

#ifdef SCREENSHOTS_DEBUG
		  printf("Deleting surfaces %d...\n", loadingScreenshotN);
#endif

		  if (loaded.extraText)
			  SDL_FreeSurface(loaded.extraText);
		  if (loaded.modifiedDate)
			  SDL_FreeSurface(loaded.modifiedDate);
		  if (loaded.modifiedTime)
			  SDL_FreeSurface(loaded.modifiedTime);
		  if (loaded.screenshot)
			  SDL_FreeSurface(loaded.screenshot);
	  },
	  "ScreenshotLoad", false, this);
}

void TScreenshotViewer::ReelItem::setScreenshot(const ImagePtr& screenshot)
{
	mScreenshot = screenshot;
}

void TScreenshotViewer::ReelItem::setTimestamp(const ImagePtr& timestamp)
{
	if (!timestamp)
		printf("ERROR! Timestamp text is null!!!\n");
	mTimestamp = timestamp;
	mTimestamp->setAlphaBlend(true);
}

void TScreenshotViewer::ReelItem::setDate(const ImagePtr& date)
{
	if (!date)
		printf("ERROR! Date text is null!!!\n");
	mDate = date;
	mDate->setAlphaBlend(true);
}

void TScreenshotViewer::ReelItem::setExtraText(const ImagePtr& text)
{
	mExtraScreensText = text;
}

void TScreenshotViewer::ReelItem::setBet(const ImagePtr& bet)
{
	mBet = bet;
}

void TScreenshotViewer::ReelItem::setWin(const ImagePtr& win)
{
	mWin = win;
}

void TScreenshotViewer::ReelItem::setCreditValue(const ImagePtr& win)
{
	mCreditValue = win;
}

void TScreenshotViewer::ReelItem::clear()
{
	mScreenshot.reset();
	mTimestamp.reset();
	mDate.reset();
	mExtraScreensText.reset();

	bAttemptedLoad = false;
}

TScreenshotViewer::TScreenshotViewer(Container* pParent, float x, float y, float w, float h) : TMenuPanelBase(pParent, x, y, w, h, true)
{
	setId("screenshot-viewer");
	Background = Black;
	setConsumeAllMouseEvents(true);

	ScreenshotArea.width() = w * SCREENSHOT_RELATIVE_SIZE_X;
	ScreenshotArea.height() = h * SCREENSHOT_RELATIVE_SIZE_Y;
	ScreenshotArea.x() = (w - ScreenshotArea.width()) / 2;
	ScreenshotArea.y() = (h - ScreenshotArea.height()) / 2;

	float sliderHeight = 64.0f;
	float extraSpaceRequiredY = std::max(0.f, sliderHeight + 3 * EXTRA_GAME_RESULTS_FONT_SIZE - ScreenshotArea.y());
	ScreenshotArea.y() += extraSpaceRequiredY;
	ScreenshotArea.height() -= extraSpaceRequiredY;

	Packman = pApp->GetModuleByName<TPackmanClient>("Packman");

	mMenuScreenDimension = pGuiApp->GUI()->GetMainScreenArea();

	timestampFont = SDLTrueTypeFont::Get("Noto Sans", TIMESTAMP_FONT_SIZE);
	numExtraResultsFont = SDLTrueTypeFont::Get("Noto Sans", EXTRA_GAME_RESULTS_FONT_SIZE);

	pScreenshotModule = pApp->GetModuleByName<TScreenshots>("Screenshots1");

	mSelectedGameN = -1;
	mSelectedStation = dynamic_cast<TDBInterface*>(pApp)->RegisteredInstance();

	UnfinishedGameImage = Image::GetImage("unfinished_game.png");

	pImageIcon = Image::GetImage("ImageIcon.png");
	pFirstScreenN = 0;

	pSlider = new TStyleSlider2(this, "game-slider", 0, 0, w, sliderHeight);
	pSlider->OnValueChanged += [this](int idx) {
		setSelectedScreenshot(idx);
	};

	pCounterList = new TScrollArea(this, buttonMargin, titleMargin + buttonMargin, LogsAndCountersWidth, h - bottomBarHeight - buttonMargin - titleMargin);
	pCounterList->setId("counter-list");
	pCounterList->Background = Black.Fade(0.4f);
	pCounterList->getContentLayout()->Alignment = EChildAlignment::Stretch;
	pCounterList->setVisible(false);

	pLogList =
	  new TScrollArea(this, w - buttonMargin - LogsAndCountersWidth, titleMargin + buttonMargin, LogsAndCountersWidth, h - bottomBarHeight - buttonMargin - titleMargin);
	pLogList->setId("log-list");
	pLogList->Background = Black.Fade(0.4f);
	pLogList->getContentLayout()->Alignment = EChildAlignment::Stretch;
	pLogList->setVisible(false);

	pSelectedTerminalLabel = AddLabel(LocalizedMessage(TERMINAL_STRING), buttonMargin, h, 170, 25);
	pSelectedTerminalLabel->setHeight(30);
	pSelectedTerminalLabel->setOrigin(0.f, 1.f);
	pSelectedTerminalLabel->setAlignment(align::LEFT_CENTER);

	pNoteScreenshotValuesLabel = AddLabel(LocalizedMessage(MSG_NOTE_SCREENSHOT_VALUES_INACCURATE), w / 2, h - buttonMargin, 0, 0);
	pNoteScreenshotValuesLabel->setResizeMode(Label::AUTOSIZE);
	pNoteScreenshotValuesLabel->setOrigin(0.5f, 1.f);
	pNoteScreenshotValuesLabel->TextColor = Color::FromRGBInteger(0xffc800);

	mpBtnBack = AddButton("btnClear", LocalizedMessage(BACK_STRING), buttonMargin, h - buttonHeight - buttonMargin - 25, 140, buttonHeight, &MenuGUI()->PanelBigButtons);
	mpBtnBack->ClickSound = "menu_back.wav";
	mpBtnBack->OnPressed += [this]() {
		if (pCanGoBack)
		{
			if (pZoomedScreenshot)
				zoomOut();
		}
		else
		{
			Hide();
		}
	};

	mpBtnRefresh =
	  AddButton("btnRefresh", LocalizedMessage(REFRESH_STRING), w - 10 - 140, h - buttonMargin - buttonHeight, 140, buttonHeight, &MenuGUI()->PanelBigButtons, White);
	mpBtnRefresh->ClickSound = "button_press.wav";
	mpBtnRefresh->OnPressed += std::bind(&TScreenshotViewer::ReloadDisplay, this);

	mpBtnReplay =
	  AddButton("btnReplay", LocalizedMessage(REPLAY_STRING), w - 4 * (10 + 140), h - buttonMargin - buttonHeight, 140, buttonHeight, &MenuGUI()->PanelBigButtons, White);
	mpBtnReplay->ClickSound = "button_press.wav";
	mpBtnReplay->setEnabled(false);
	bCanReplay = pApp->GetParam("EXPERIMENTAL_FEATURES")->AsBoolean();
	mpBtnReplay->OnPressed += std::bind(&TScreenshotViewer::ReplaySelected, this);

	mpBtnCounters = AddButton("btnCounters", LocalizedMessage(SHOW_COUNTERS_STRING), w - 3 * (10 + 140), h - buttonMargin - buttonHeight, 140, buttonHeight,
	                          &MenuGUI()->PanelBigButtons, White);
	mpBtnCounters->ClickSound = "button_press.wav";
	mpBtnCounters->OnPressed += std::bind(&TScreenshotViewer::OnCountersPressed, this);

	pCounterList->OnVisibleChanged += [this](Widget* w, bool bVisible) {
		mpBtnCounters->Text->setCaption(bVisible ? LocalizedMessage(HIDE_COUNTERS_STRING) : LocalizedMessage(SHOW_COUNTERS_STRING));
	};

	mpBtnCloseReplay = AddButton("btnCloseReplay", LocalizedMessage(CLOSE_GAME_STRING), w - 4 * (10 + 140), h - buttonMargin - buttonHeight, 140, buttonHeight,
	                             &MenuGUI()->PanelBigButtons, White);
	mpBtnCloseReplay->setOrigin(0.5f, 0.0f);
	mpBtnCloseReplay->ClickSound = "button_press.wav";
	mpBtnCloseReplay->OnPressed += std::bind(&TScreenshotViewer::CloseReplayGame, this);
	mpBtnCloseReplay->setVisible(false);

	mpBtnLogs = AddButton("btnLogs", LocalizedMessage(SHOW_LOGS_STRING), w - 2 * (10 + 140), h - buttonMargin - buttonHeight, 140, buttonHeight,
	                      &MenuGUI()->PanelBigButtons, White);
	mpBtnLogs->ClickSound = "button_press.wav";
	mpBtnLogs->OnPressed += std::bind(&TScreenshotViewer::OnLogsPressed, this);

	addMouseListener(this);
	updateTranslations();

	setVisible(false);

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (bVisible)
			OnShow();
		else
			OnHide();
	};
}

TScreenshotViewer::~TScreenshotViewer() {}

void TScreenshotViewer::SetInstance(const TGameDBSrv::Instance& inst)
{
	if (mSelectedStation.ID == inst.ID)
		return;
	mNumImages = -1;
	mSelectedStation = inst;
}

void TScreenshotViewer::onScreenshotSelected(int screenN) const
{
	std::pair<GameIdentifier, int>* data =
	  new std::pair<GameIdentifier, int>(pReel[screenN].round().RoundInfo.Game, mNumImages - 1 - screenN);    // How many gamerounds "ago" was this one played
	pApp->GenerateAction("HistoryRoundSelected", data);
}

void TScreenshotViewer::saveTextureToReel(int screenN, const ScreenshotData& loadedData)
{
	if (screenN < 0 || screenN >= (int)pReel.size())
		return;

#ifdef SCREENSHOTS_DEBUG
	printf("Texturing pic %d...\n", screenN);
#endif
	if (loadedData.screenshot)
	{
		GPU_Rect bottomScreenArea;
		bottomScreenArea.x = 0;
		bottomScreenArea.y = std::max(0, loadedData.screenshot->h - mMenuScreenDimension.height());
		bottomScreenArea.w = loadedData.screenshot->w;
		bottomScreenArea.h = std::min(mMenuScreenDimension.height(), loadedData.screenshot->h);
		pReel[screenN].setScreenshot(std::make_shared<Image>(GPU_CopyImageFromSurfaceRect(loadedData.screenshot, &bottomScreenArea), true));
	}
	else
	{
		pReel[screenN].setScreenshot(UnfinishedGameImage);
	}
	pReel[screenN].setDate(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.modifiedDate), true));
	pReel[screenN].setTimestamp(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.modifiedTime), true));

	if (loadedData.extraText)
		pReel[screenN].setExtraText(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.extraText), true));
	else
		pReel[screenN].setExtraText({});

	if (loadedData.betText)
		pReel[screenN].setBet(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.betText), true));
	else
		pReel[screenN].setBet({});

	if (loadedData.winText)
		pReel[screenN].setWin(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.winText), true));
	else
		pReel[screenN].setWin({});

	if (loadedData.denomiText)
		pReel[screenN].setCreditValue(std::make_shared<Image>(GPU_CopyImageFromSurface(loadedData.denomiText), true));
	else
		pReel[screenN].setCreditValue({});

	if (screenN == mTargetReelPos)
		onScreenshotSelected(screenN);

#ifdef SCREENSHOTS_DEBUG
	printf("Saved reel screenshot N %d!\n", screenN);
#endif
}

const float loadInAdvanceMultipliers[HALF_REEL + 1] = { 0.14f, 0.09f, 0.05f, 0.02f, 0.f };
// A random millisecond delay to offset the base load delays - to distribute image loading over several frames and make it look random
const int loadDelayRandomSeeds[REEL_SIZE] = { 10, 5, 24, 18, 17, 9, 12, 0, 15 };

void TScreenshotViewer::OnShow()
{
	pSelectedTerminalLabel->setCaption(MyUtils::ToLowercase(mSelectedStation.Type, true) + " " + std::to_string(mSelectedStation.Number));

	moveToTop();
	pGuiApp->GUI()->requestModalFocus(this, true);

	ReloadDisplay();
}

void TScreenshotViewer::OnHide()
{
	zoomOut();
	pZoomedScreenshot.reset();
	mNumImages = -1;
	pReel.clear();

	pGuiApp->GUI()->releaseModalFocus(this);
}

int TScreenshotViewer::ReloadDisplay()
{
	if (pCounterList->isVisible())
		pCounterList->setVisible(false);

	if (pLogList->isVisible())
		pLogList->setVisible(false);

	// spremenimo glede na izbran terminal če je treba
	std::map<uint64_t, SavedGameround> AllScreenshots;
	if (mSelectedStation.ID == pApp->ClientID())
	{
		mScreenshotDirectory = pScreenshotModule->screenDir();
		AllScreenshots = pScreenshotModule->myScreenshots();
	}
	else
	{
		mScreenshotDirectory = pApp->GetLogDir().parent_path().parent_path() / mSelectedStation.MAC / mSelectedStation.ID / "Screenshots";
		auto param = pApp->GetOrSetExactParam(mSelectedStation.ID, "NUMBER_OF_LAST_GAMES", std::to_string(MAX_OLD_GAMES_DEFAULT), "Maximum number of screenshots saved",
		                                      "Screenshots older than this number of games will get deleted", PARAM_READ_MINE_WRITE_MINE);
		TScreenshots::loadScreenshots(mScreenshotDirectory, AllScreenshots, param->AsInteger());
	}

	{
		ScopedLock lock(numExtraResultsFont);
		mDenomination = MenuGUI()->pClient->BalanceManager()->Denomination();
		iVector2D size;
		if (TTF_SizeText((&numExtraResultsFont)->SDL_Font, MenuGUI()->CurrencySymbol.c_str(), &size.X(), &size.Y()) != -1 && size.X() && size.Y())
			mCurrency = yutils::Trim(MenuGUI()->CurrencySymbol);
		else
			mCurrency = MenuGUI()->CurrencyCode;
	}
	mActiveLoadID++;

	pReel.clear();
	pReel.reserve(AllScreenshots.size());
	pSlider->clearValues();
	size_t gamesAgo = AllScreenshots.size();
	for (const auto& screen : AllScreenshots)
	{
		gamesAgo--;
		if (gamesAgo == 0)
			pSlider->addValue(LocalizedMessage(LATEST_GAME_STRING));
		else if (gamesAgo == 1)
			pSlider->addValue(LocalizedMessage(PREVIOUS_GAME_STRING));
		else
			pSlider->addValue((std::to_string(gamesAgo) + " ") + LocalizedMessage(GAMES_AGO_STRING));
		pReel.push_back(ReelItem(screen.first, screen.second));
	}

	// Get number of screenshots
	mNumImages = AllScreenshots.size();

	mOverrideSwipePosition = -1.f;
	mOnStartSwipeReelPos = -1.f;
	pZoomedScreenshot.reset();
	pCanGoBack = false;

	mSelectedGameN = mNumImages ? (mNumImages - 1) : 0;
	moveReel(mSelectedGameN);
	mTargetReelPos = mSelectedGameN;
	pSlider->setCurrentIndex(mSelectedGameN);

	return 0;
}

void TScreenshotViewer::animate(float fromScreenN, int toScreenN)
{
#ifdef SCREENSHOTS_DEBUG
	printf("Setting selected screenshot to %d...\n", toScreenN);
#endif
	mTargetReelPos = toScreenN;

	int newMin = std::max(0, toScreenN - HALF_REEL);
	int newMax = std::min(toScreenN + HALF_REEL, mNumImages - 1);

	// check all images in what was the previous target reel position
	for (int i = std::max(0, mSelectedGameN - HALF_REEL); i <= std::min(mSelectedGameN + HALF_REEL, mNumImages - 1); i++)
	{
		// if the image is outside the reel and outside the future reel position clear it!
		if ((i < newMin || i > newMax) && (i < pFirstScreenN || i >= std::min(mNumImages, pFirstScreenN + REEL_SIZE)) && pReel[i].bAttemptedLoad)
		{
			if (pZoomedScreenshot && (pZoomedScreenshot == pReel[i].screenshot()))
				pZoomedScreenshot.reset();
			pReel[i].clear();
		}
	}

	pSlider->setCurrentIndex(toScreenN);

	if (pReel[toScreenN].screenshot())
		onScreenshotSelected(toScreenN);
}

void TScreenshotViewer::ReplaySelected()
{
	if (mSelectedGameN == -1)
		return;

	if (pReel[mSelectedGameN].round().RoundInfo.YServerGameround.empty())
	{
		AddNotification({ LocalizedMessage(MSG_GAME_REPLAY_FAILED), LocalizedMessage(MISSING_GAME_INFO_STRING) }, 5, EVerbosity::Error);
	}
	else
	{
		boost::future<TIGPlatformApp::FGameStartResult> startedGame = dynamic_cast<TIGPlatformApp*>(pApp)->Replay(pReel[mSelectedGameN].round().RoundInfo);
		startedGame.then(boost::launch::sync, [this](boost::future<TIGPlatformApp::FGameStartResult> fut) {
			pGuiApp->DeferToDrawSimple(
			  [this, startedGame = fut.get()]() {
				  if (startedGame.pBrowserWindow)
				  {
					  add(startedGame.pBrowserWindow);
					  startedGame.pBrowserWindow->setSize(getSize());
					  moveToTop(mpBtnCloseReplay);
					  mpBtnCloseReplay->setVisible(true);
				  }
				  else
				  {
					  AddNotification({ LocalizedMessage(MSG_GAME_REPLAY_FAILED), startedGame.outErr }, 5, EVerbosity::Error);
				  }
			  },
			  "ReplaySelected", false, this);
		});
	}
}

bool TScreenshotViewer::canReplay() const
{
	return bCanReplay;
}

int TScreenshotViewer::moveReel(int finalPosition)
{
	finalPosition = std::max(0, finalPosition - HALF_REEL);

	int diff = finalPosition - pFirstScreenN;

	if (diff == 0)
		return 0;

	if (pCounterList->isVisible())
		pCounterList->setVisible(false);

	if (pLogList->isVisible())
		pLogList->setVisible(false);

	if (std::abs(diff) >= REEL_SIZE)
	{
		deleteReelTextures();
	}
	else if (diff != 0)
	{
		for (int i = pFirstScreenN; i < std::min(pFirstScreenN + REEL_SIZE, mNumImages); i++)
		{
			if (((i < finalPosition) || (i >= finalPosition + REEL_SIZE)) && pReel[i].bAttemptedLoad)
			{
#ifdef SCREENSHOTS_DEBUG
				printf("Clearing reel N %d...\n", i);
#endif
				if (pZoomedScreenshot && (pZoomedScreenshot == pReel[i].screenshot()))
					pZoomedScreenshot = NULL;
				pReel[i].clear();
			}
		}
	}
	pFirstScreenN = finalPosition;
	return diff;
}

void TScreenshotViewer::setSelectedScreenshot(int screenshotN)
{
	if (int(mTargetReelPos) != screenshotN)
	{
		mSelectedGameN = screenshotN;
		animate(mTargetReelPos, screenshotN);
		mSelectedGameN = -1;
		pSlider->setCurrentIndex(mSelectedGameN);
	}
}

void TScreenshotViewer::deleteReelTextures()
{
	for (int i = pFirstScreenN; i < std::min(pFirstScreenN + REEL_SIZE, mNumImages); i++)
	{
#ifdef SCREENSHOTS_DEBUG
		printf("Clearing reel N %d...\n", i);
#endif
		if (!pReel[i].bAttemptedLoad)
			continue;
		if (pZoomedScreenshot && (pZoomedScreenshot == pReel[i].screenshot()))
			pZoomedScreenshot = NULL;
		pReel[i].clear();
	}
}

float TScreenshotViewer::getReelPos() const
{
	return (mOverrideSwipePosition < 0.f) ? mTargetReelPos : (mOnStartSwipeReelPos - mScrollSmoothing);
}

void TScreenshotViewer::OnLogsPressed()
{
	if (mSelectedGameN >= 0)
	{
		const uint64_t gameroundID = pReel[mSelectedGameN].gameRoundID();

		pLogList->ClearContent();
		if (pLogList->isVisible())
		{
			pLogList->setVisible(false);
			mpBtnLogs->Text->setCaption(LocalizedMessage(SHOW_LOGS_STRING));
		}
		else if (gameroundID > 0)
		{
			mpBtnLogs->Text->setCaption(LocalizedMessage(LOADING_STRING));
			mpBtnLogs->setEnabled(false);
			MenuGUI()->pDBMngr->ExecuteAsync([this, gameroundID](TGameDBSrv* db, soci::session& sql) {
				std::vector<std::pair<uint64_t, std::string>> logList;
				int res = db->GetLogsByGameId(pApp->ClientID(), gameroundID, logList);
				pGuiApp->DeferToDrawSimple(
				  [this, logList, gameroundID, res]() {
					  if (res == 0 && logList.size() > 0)
					  {
						  bool BGColor = false;
						  for (const auto& log : logList)
						  {
							  Label* logTextLabel = new Label(LocalizedMessage(yutils::FormatTime("%c: ", time_t(log.first / 1000)) + log.second));
							  logTextLabel->setId("log");
							  logTextLabel->Background = BGColor ? Color::Gray(0x40) : Color::Gray(0x30);
							  logTextLabel->setResizeMode(Label::NONE, Label::AUTOSIZE);
							  logTextLabel->setAlignment(align::LEFT_TOP);
							  logTextLabel->setWrap(true);
							  logTextLabel->setPadding(Rectangle(10.f, 10.f, 40.f, 8.f));
							  pLogList->AddContent(logTextLabel);
							  BGColor = !BGColor;
						  }
						  pLogList->fadeIn();
					  }
					  else
					  {
						  AddNotification({ LocalizedMessage(NO_LOGS_IN_DB_STRING) + (" " + std::to_string(gameroundID)) }, EVerbosity::Info);
					  }
					  mpBtnLogs->Text->setCaption(LocalizedMessage(HIDE_LOGS_STRING));
				  },
				  "OnLogsPressed", false, this);
			});
		}
		else
		{
			pGuiApp->DeferToDrawSimple([this]() { AddNotification({ LocalizedMessage(NO_GAMEROUND_DATA_STRING) }, 3.5f, EVerbosity::Warning); }, "OnLogsPressed_NoData",
			                           false, this);
		}
	}
}

void TScreenshotViewer::OnCountersPressed()
{
	pCounterList->ClearContent();
	if (pCounterList->isVisible())
	{
		pCounterList->setVisible(false);
		return;
	}

	if (mSelectedGameN < 0)
		return;

	const FRoundInfo& round = pReel[mSelectedGameN].round().RoundInfo;
	if (round.Version == 0)
		return;

	struct CounterInfo
	{
		std::string name;
		int64_t value;
		int64_t old_value;
		int order;
		bool show_change;

		bool operator<(const CounterInfo& rhs) const { return order < rhs.order; }
	};

	std::set<CounterInfo> counterList;
	if (round.Version >= 2)
	{
		int numGambles = 0;
		for (const FCounterSnapshot& counter : round.Counters)
		{
			std::string name = "unknown";
			const bool bShouldShowChange = (counter.id > 0) && ((counter.id == LIFETIME_COUNTER_WIN) || (counter.value > counter.old_value) ||
			                                                    (counter.value != counter.old_value && counter.id >= CT_RESTRICTED && counter.id <= CT_CASHABLE_BANK));
			int order;
			if (counter.id < 0)    // gamble hack :)
			{
				order = -(counter.id + 1);
				numGambles = std::max(-counter.id, numGambles);
				name = counter.name;
			}
			else
			{
				auto nameFind = CounterNames.find(counter.id);
				if (nameFind != CounterNames.end())
					name = nameFind->second;

				if (counter.id == CT_CASHABLE || counter.id == CT_NONRESTRICTED)
					order = 100 + counter.id - 1;
				else if (counter.id == CT_RESTRICTED)
					order = 100 + counter.id + 2;
				else
					order = 100 + counter.id;
			}
			counterList.insert({ name, counter.value, counter.old_value, order, bShouldShowChange });
		}

		if (round.Version == 2 && numGambles)
			counterList.insert({ std::string(), 0, 0, numGambles, false });
	}

	const float padding = 10;
	const float rowHeight = 35;

	if (counterList.empty())
	{
		Label* notice = new Label(LocalizedMessage(COUNTERS_FILE_INCOMPATIBLE_STRING));
		notice->setId("notice");
		notice->setResizeMode(Label::RESIZE_DOWN_UNIFORM);
		notice->setHeight(rowHeight);
		notice->Background = Color::Gray(0x30);
		notice->setPadding(Rectangle(padding, padding));
		pCounterList->AddContent(notice);
	}
	else
	{
		GridLayout::FieldSize cellSize;
		cellSize.Padding = Vector2D(10.f, 30.f);
		int i = 0;
		for (const CounterInfo& counter : counterList)
		{
			Widget* added;
			if (!counter.name.empty())    // empty name = empty line
			{
				std::string displayValue;
				if (counter.show_change)
				{
					const int64_t change = counter.value - counter.old_value;
					displayValue = MyUtils::int2str(counter.old_value) + ((change >= 0) ? " + " : " - ") + MyUtils::int2str(std::abs(change)) + " = " +
					               MyUtils::int2str(counter.value);
				}
				else
				{
					displayValue = MyUtils::int2str(counter.value);
				}
				LayoutContainer* cont = new LayoutContainer();
				cont->setId("counter(" + counter.name + ")");
				GridLayout* layout = cont->setLayout<GridLayout>();
				layout->AddColumn(cellSize);
				layout->bUsePaddingBeforeFirstAndAfterLast = true;
				Label* counterLabel = new Label(LocalizedMessage(counter.name + ":"));
				counterLabel->setId("name");
				Label* counterValue = new Label(LocalizedMessage(displayValue));
				counterLabel->setId("value");
				counterLabel->setResizeMode(Label::AUTOSIZE);
				counterValue->setResizeMode(Label::AUTOSIZE);
				cont->add(counterValue);
				cont->add(counterLabel);
				layout->Set(counterLabel, GridLayout::GridPosition(sVector2D(0), sVector2D(1), ChildAlignment2D(EChildAlignment::Min, EChildAlignment::Center)));
				layout->Set(counterValue, GridLayout::GridPosition(sVector2D(0), sVector2D(1), ChildAlignment2D(EChildAlignment::Max, EChildAlignment::Center)));
				added = cont;
			}
			else
			{
				added = new Widget();
				added->setId("divider");
			}
			added->setHeight(rowHeight);
			pCounterList->AddContent(added);
			added->Background = (i % 2) ? Color::Gray(0x40) : Color::Gray(0x30);
			i++;
		}
	}
	pCounterList->setVisible(true);
}

void TScreenshotViewer::CloseReplayGame()
{
	pGuiApp->DeferToDrawSimple(
	  [this]() {
		  if (auto app = dynamic_cast<TIGPlatformApp*>(pApp))
		  {
			  auto pCommander = app->GetModuleByName<TPlatformCommander>("Commander");
			  pCommander->GetGameState(app->GetMaxNumberOfConcurrentGames(), [this](GameRunContext& ctx) { remove(ctx.GameWindow); });
			  app->CloseGame(app->GetMaxNumberOfConcurrentGames(), EClosingState::USER_CLOSE);
			  mpBtnCloseReplay->setVisible(false);
		  }
	  },
	  "CloseReplayGame");
}

bool TScreenshotViewer::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	if (TMenuPanelBase::DoAction_Implementation(ev))
		return true;

	switch (ev.Action)
	{
		case EHardwareButtonAction::BetPlus:
		case EHardwareButtonAction::BetMax:
			if (mSelectedGameN >= 0 && mSelectedGameN + 1 < mNumImages)
				setSelectedScreenshot(mSelectedGameN + 1);
			return true;
			break;
		case EHardwareButtonAction::BetMinus:
			if (mSelectedGameN > 0)
				setSelectedScreenshot(mSelectedGameN - 1);
			return true;
			break;
		default: break;
	}

	return false;
}

void TScreenshotViewer::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	TMenuPanelBase::GetAvailableActions_Implementation(outAvailableActions);

	if (mSelectedGameN >= 0 && mSelectedGameN + 1 < mNumImages)
	{
		outAvailableActions.SetAvailable(EHardwareButtonAction::BetPlus, NEXT_PAGE_STRING);
		outAvailableActions.SetAvailable(EHardwareButtonAction::BetMax, NEXT_PAGE_STRING);
	}

	if (mSelectedGameN > 0)
	{
		outAvailableActions.SetAvailable(EHardwareButtonAction::BetMinus, PREV_PAGE_STRING);
	}
}

void TScreenshotViewer::onLanguageChanged(ELanguage lang)
{
	TMenuPanelBase::onLanguageChanged(lang);
	updateTranslations();
}

void TScreenshotViewer::updateTranslations()
{
	CurrentTranslations.Results = LocalizedMessage(RESULTS_STRING).Get(Language());
	CurrentTranslations.Bet = LocalizedMessage(BET_STRING).Get(Language());
	CurrentTranslations.Win = LocalizedMessage(WIN_STRING).Get(Language());
	CurrentTranslations.Credit = LocalizedMessage(CREDIT_STRING).Get(Language());
}

void TScreenshotViewer::mouseClicked(MouseEvent& mouseEvent)
{
	if (pCounterList->isVisible())
		pCounterList->setVisible(false);

	if (pLogList->isVisible())
		pLogList->setVisible(false);

	if (mOverrideSwipePosition >= 0.f || mouseEvent.isConsumed())
		return;

	if (pZoomedScreenshot)
	{
		zoomOut();
		return;
	}

	const float reelPos = getReelPos();
	for (int layer = 0; layer <= HALF_REEL; layer++)
	{
		for (int imagediff = -layer; imagediff <= layer; imagediff += 2 * layer)
		{
			int screenN = std::roundf(getReelPos()) + imagediff;
			if (screenN < 0 || screenN >= mNumImages)
			{
				if (layer == 0)
					break;
				else
					continue;
			}

			const ImagePtr& drawnImage = pReel[screenN].screenshot() ? pReel[screenN].screenshot() : pImageIcon;

			const float thisImageInterpolator = (screenN - reelPos) / HALF_REEL;
			const float sizeF = std::lerp(1.f, 0.6f, std::abs(thisImageInterpolator));

			Rectangle imageBounds = ScreenshotArea.centerScale(sizeF);
			imageBounds.x() = (getWidth() - imageBounds.width()) / 2 + getWidth() * std::sin(M_PI * 0.5f * std::min(thisImageInterpolator * 0.8f, 1.f)) / 3;

			imageBounds = Graphics::computeFinalRect(Vector2D(drawnImage->getSize()), imageBounds, align::CENTER_CENTER, false, EScaleMode::ZOOM_TO_FIT_ONLY_DOWN, false);

			// pGuiApp->GUI()->getGraphics()->drawRectangle(imageBounds, Red);

			if (imageBounds.isPointInRect(mouseEvent.pos()))
			{
				if (screenN == (int)mTargetReelPos)    // if already animating to the clicked screenshot, then zoom it!
				{
					// if (!pSelectionAnimation.playing())    // only zoom it if the reel is not animating
					zoomIn(pReel[screenN].screenshot());
				}
				else    // if not already animating to this screenshot, then change the animation!
				{
					setSelectedScreenshot(screenN);
				}
				return;
			}

			if (layer == 0)
				break;
		}
	}
}

void TScreenshotViewer::mouseDragged(MouseEvent& event)
{
	if (pZoomedScreenshot)
		return;

	if ((event.mouseDown().DownPos.Y() > getHeight() - bottomBarHeight) || event.mouseDown().DownPos.Y() < pSlider->getHeight())
		return;

	if (pCounterList->isVisible())
		pCounterList->setVisible(false);

	if (pLogList->isVisible())
		pLogList->setVisible(false);

	bool bHasStartedSwiping = mOnStartSwipeReelPos != -1.f;
	float oldPos = mOverrideSwipePosition;
	const float diffx = event.dragged().X();
	if (!bHasStartedSwiping && std::abs(diffx) > SCREENSHOT_REEL_SCROLL_DEADZONE)
	{
		mOnStartSwipeReelPos = mTargetReelPos;
		mScrollSmoothing = 0.f;
		bHasStartedSwiping = true;

		oldPos = mTargetReelPos;
	}

	if (bHasStartedSwiping)
	{
		mScrollSmoothingTarget = diffx * 4 / getWidth();
		const float interp = std::min(std::max(1U, event.mouseDown().ElapsedSinceLastEvent) * 1e-3f / SCREENSHOT_REEL_SCROLL_SMOOTHINGINTERP_SPEED, 1.f);
		mScrollSmoothing = interp * mScrollSmoothingTarget + mScrollSmoothing * (1.f - interp);

		mOverrideSwipePosition = getReelPos();
		if (mOverrideSwipePosition < 0.f)
		{
			mOnStartSwipeReelPos -= mOverrideSwipePosition;
			mOverrideSwipePosition = 0.f;
		}
		else if (mOverrideSwipePosition > float(mNumImages - 1))
		{
			mOnStartSwipeReelPos += mNumImages - 1 - mOverrideSwipePosition;
			mOverrideSwipePosition = mNumImages - 1;
		}

		int newPos = std::roundf(mOnStartSwipeReelPos);
		int newMin = std::max(0, newPos - HALF_REEL);
		int newMax = std::min(newPos + HALF_REEL, mNumImages - 1);

		for (int i = std::max(0.f, oldPos - HALF_REEL); i <= std::min(oldPos + HALF_REEL, mNumImages - 1.f); i++)
		{
			if ((i < newMin || i > newMax) && pReel[i].bAttemptedLoad)
			{
				if (pZoomedScreenshot && (pZoomedScreenshot == pReel[i].screenshot()))
					pZoomedScreenshot = NULL;
				pReel[i].clear();
			}
		}
	}
}

void TScreenshotViewer::mouseReleased(MouseEvent& mouseEvent)
{
	if (mOverrideSwipePosition >= 0.f && mNumImages)
	{
		const float reelPos = getReelPos();
		const float centerReelPos = std::roundf(reelPos);

		// positive move delta means a swipe right, so reel should move left (towards old screenshots and low indexes)
		float moveDelta = (mScrollSmoothingTarget - mScrollSmoothing) * REEL_SWIPE_MULTIPLIER / 4.f;
		moveDelta = moveDelta * (std::pow(1.f + std::abs(moveDelta), REEL_SWIPE_EXPONENT) - 1.f);

		int targetScreenN = centerReelPos;
		if (std::abs(moveDelta) > 0.025f)
			targetScreenN = std::clamp((int)std::roundf(reelPos - moveDelta * 5), 0, mNumImages - 1);
		else if (std::abs(mouseEvent.dragged().X()) >= SCREENSHOT_REEL_SCROLL_DEADZONE)
			targetScreenN = (moveDelta > 0.f) ? std::max(0, targetScreenN - 1) : std::min(mNumImages - 1, targetScreenN + 1);

		if (reelPos != centerReelPos)
		{
			mSelectedGameN = std::roundf(mOverrideSwipePosition);
			animate(reelPos, targetScreenN);
			mSelectedGameN = -1;
		}
		mOverrideSwipePosition = -1.f;
		mOnStartSwipeReelPos = -1.f;

		mouseEvent.consume();
	}
}

void TScreenshotViewer::draw(Graphics* graphics)
{
	// Draw the background
	const Uint64 now = SDL_GetTicks64();

	// if swipe is in progress, take that value instead of the animated position
	const float newMiddleScreenN = getReelPos();
	int roundedScreenN = mNumImages ? std::clamp(0, (int)std::roundf(newMiddleScreenN), mNumImages - 1) : 0;

	if (std::max(0, roundedScreenN - HALF_REEL) != pFirstScreenN)
		moveReel(roundedScreenN);

	float zoomInterpolator = 0.f;
	float zoomedImageAlpha = 1.f;

	const bool bIsDrawingZoomedScreenshot = (bool)pZoomedScreenshot;
	if (bIsDrawingZoomedScreenshot)
		zoomInterpolator = 1.f;

	pSelectedTerminalLabel->setY(getHeight() - zoomInterpolator * 30);

	Rectangle timeAboveDestRect, dateAboveDestRect, zoomStartRect;

	for (int layer = HALF_REEL; layer >= 0; layer--)
	{
		for (int imagediff = -layer; imagediff <= layer; imagediff += 2 * layer)
		{
			int screenN = roundedScreenN + imagediff;
			if (screenN < 0 || screenN >= mNumImages)
			{
				if (layer == 0)
					break;
				else
					continue;
			}
			const float interpolatorForThisImage = (screenN - newMiddleScreenN) / HALF_REEL;
			const float interpolatorForThisImageAbsolute = std::abs(interpolatorForThisImage);
			float alphaFactor = 1.f;
			float sizeFactor = std::lerp(1.f, 0.6f, interpolatorForThisImageAbsolute);
			float alphaFactorDueToLocation = std::lerp(1.f, 0.4f, interpolatorForThisImageAbsolute);

			const ImagePtr& imgToDraw = pReel[screenN].screenshot() ? pReel[screenN].screenshot() : pImageIcon;
			if (imgToDraw == pImageIcon)    // loading!
			{
				alphaFactor = 0.5f + 0.5f * std::sin(now - layer * 150);
			}

			Rectangle sizeToDraw = ScreenshotArea.centerScale(sizeFactor);
			sizeToDraw.x() = (getWidth() - sizeToDraw.width()) / 2 + getWidth() * std::sin(M_PI * 0.5f * std::min(interpolatorForThisImage * 0.8f, 1.f)) / 3;

			sizeToDraw = Graphics::computeFinalRect(Vector2D(imgToDraw->getSize()), sizeToDraw, align::CENTER_CENTER, false, EScaleMode::ZOOM_TO_FIT_ONLY_DOWN, false);

			Rectangle timeDestRect, dateDestRect;

			if (pReel[screenN].timestamp() && pReel[screenN].date())
			{
				timeDestRect = dateDestRect = sizeToDraw;
				timeDestRect.Size = Vector2D(pReel[screenN].timestamp()->getSize()) * sizeFactor;
				dateDestRect.Size = Vector2D(pReel[screenN].date()->getSize()) * sizeFactor;
				timeDestRect.x() += (sizeToDraw.width() - timeDestRect.width()) / 2;
				dateDestRect.x() += (sizeToDraw.width() - dateDestRect.width()) / 2;

				dateDestRect.y() += sizeToDraw.height() + 15;
				timeDestRect.y() = dateDestRect.y() + dateDestRect.height() + 5;
				float labelOffset = 0.5f - std::lerp(0.f, 0.5f, interpolatorForThisImageAbsolute * HALF_REEL);
				dateDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - dateDestRect.width());
				timeDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - timeDestRect.width());
			}

			if (pZoomedScreenshot)
			{
				dateDestRect.x() = std::lerp(dateDestRect.x(), getWidth() - dateDestRect.width() - 10, zoomInterpolator);
				dateDestRect.y() = std::lerp(dateDestRect.y(), getHeight() - dateDestRect.height() - timeDestRect.height() - 15, zoomInterpolator);
				timeDestRect.Pos = timeDestRect.Pos.lerp(getSize() - timeDestRect.Size - 10, zoomInterpolator);

				dateAboveDestRect = dateDestRect;
				timeAboveDestRect = timeDestRect;
			}

			if (imgToDraw == pZoomedScreenshot)
				zoomStartRect = sizeToDraw;
			else
				alphaFactorDueToLocation *= (1.f - zoomInterpolator);

			DrawAlphaScope tr(graphics, alphaFactorDueToLocation * sizeFactor);

			const Color glowColor = White.Fade(0.5f);
			graphics->setBrush(glowColor);
			graphics->fillRectangleWithBrush(sizeToDraw, Outline(glowColor, 5_px, 1_px, 10_px));
			{
				DrawAlphaScope tr2(graphics, alphaFactor);
				graphics->drawImage2(imgToDraw, Rectangle(), sizeToDraw);
			}

			if (imgToDraw != pImageIcon)
			{
				if (pReel[screenN].timestamp() && pReel[screenN].date() && !pZoomedScreenshot)
				{
					graphics->drawImage2(pReel[screenN].timestamp(), Rectangle(), timeDestRect);
					graphics->drawImage2(pReel[screenN].date(), Rectangle(), dateDestRect);
				}

				const float labelOffset = 0.5f - std::lerp(0.f, 0.5f, interpolatorForThisImageAbsolute * HALF_REEL);
				{
					DrawAlphaScope tr3(graphics, 1.f - zoomInterpolator);

					float yOffset = 0.f;
					if (pReel[screenN].extraScreenshotsText())
					{
						Rectangle labelDestRect = sizeToDraw;
						labelDestRect.Size = Vector2D(pReel[screenN].extraScreenshotsText()->getSize()) * sizeFactor;
						labelDestRect.x() += (sizeToDraw.width() - labelDestRect.width()) / 2;
						labelDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - labelDestRect.width());
						labelDestRect.y() -= labelDestRect.height() + 10;
						yOffset += labelDestRect.height();
						graphics->drawImage2(pReel[screenN].extraScreenshotsText(), Rectangle(), labelDestRect);
					}
					if (pReel[screenN].win())
					{
						Rectangle labelDestRect = sizeToDraw;
						labelDestRect.Size = Vector2D(pReel[screenN].win()->getSize()) * sizeFactor;
						labelDestRect.x() += (sizeToDraw.width() - labelDestRect.width()) / 2;
						labelDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - labelDestRect.width());
						labelDestRect.y() -= labelDestRect.height() + 10 + yOffset;
						yOffset += labelDestRect.height();
						graphics->drawImage2(pReel[screenN].win(), Rectangle(), labelDestRect);
					}
					if (pReel[screenN].bet())
					{
						Rectangle labelDestRect = sizeToDraw;
						labelDestRect.Size = Vector2D(pReel[screenN].bet()->getSize()) * sizeFactor;
						labelDestRect.x() += (sizeToDraw.width() - labelDestRect.width()) / 2;
						labelDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - labelDestRect.width());
						labelDestRect.y() -= labelDestRect.height() + 10 + yOffset;
						yOffset += labelDestRect.height();
						graphics->drawImage2(pReel[screenN].bet(), Rectangle(), labelDestRect);
					}
					if (pReel[screenN].creditValue())
					{
						Rectangle labelDestRect = sizeToDraw;
						labelDestRect.Size = Vector2D(pReel[screenN].creditValue()->getSize()) * sizeFactor;
						labelDestRect.x() += (sizeToDraw.width() - labelDestRect.width()) / 2;
						labelDestRect.x() += ((interpolatorForThisImage < 0.f) ? -labelOffset : labelOffset) * (sizeToDraw.width() - labelDestRect.width());
						labelDestRect.y() -= labelDestRect.height() + 10 + yOffset;
						yOffset += labelDestRect.height();
						graphics->drawImage2(pReel[screenN].creditValue(), Rectangle(), labelDestRect);
					}
				}
			}

			if (layer == 0)
				break;
		}
	}

	TPanelBase::draw(graphics);    // narisemo se navigation panel

	if (pGameLoading)
	{
		graphics->drawImage2(pGameLoading, Rectangle(), Rectangle(0, getSize()));
	}

	if (bIsDrawingZoomedScreenshot)
	{
		if (zoomInterpolator > 0.f)
		{
			DrawAlphaScope zoomTr(graphics, zoomedImageAlpha);
			const Rectangle dest = Rectangle(zoomStartRect.asVector().lerp({ 0, 0, getWidth(), getHeight() }, zoomInterpolator));
			graphics->drawImageText2(pZoomedScreenshot, Rectangle(), dest, align::CENTER_CENTER, EScaleMode::ZOOM_TO_FIT);
		}

		const Rectangle darkenArea = { dateAboveDestRect.x() - 10, dateAboveDestRect.y() - 10, std::max(dateAboveDestRect.width(), timeAboveDestRect.width()) + 20,
			                           dateAboveDestRect.height() + timeAboveDestRect.height() + 25 };

		graphics->fillRectangleWithColor(darkenArea, Black.Fade(0.6f * zoomInterpolator));

		if (mSelectedGameN != -1)
		{
			if (timeAboveDestRect.isValidSize())
				graphics->drawImage2(pReel[mSelectedGameN].timestamp(), Rectangle(), timeAboveDestRect);
			if (dateAboveDestRect.isValidSize())
				graphics->drawImage2(pReel[mSelectedGameN].date(), Rectangle(), dateAboveDestRect);
		}
	}
}

void TScreenshotViewer::drawLogic(Graphics* graphics, float deltaTime)
{
	if (mOverrideSwipePosition >= 0.f)    // currently dragging
	{
		const float interp = std::min(deltaTime / SCREENSHOT_REEL_SCROLL_SMOOTHINGINTERP_SPEED, 1.f);
		mScrollSmoothing = interp * mScrollSmoothingTarget + mScrollSmoothing * (1.f - interp);
	}

	mSelectedGameN = mTargetReelPos;
	moveReel(mSelectedGameN);
	mTargetReelPos = mSelectedGameN;

	const int reelEndPos = (mOverrideSwipePosition >= 0.f) ? std::roundf(mOverrideSwipePosition) : mTargetReelPos;
	const float baseAnimDuration = (mOverrideSwipePosition >= 0.f) ? (2500 * SCREENSHOT_REEL_SCROLL_SMOOTHINGINTERP_SPEED) : REEL_BASE_ANIMATION_DURATION;
	const int64_t remainingAnimationTime = 0;

	// Iterate throught the final reel positons and decide which loaders to start
	for (int n = std::max(0, reelEndPos - HALF_REEL); n <= std::min(reelEndPos + HALF_REEL, mNumImages - 1); n++)
	{
		// If save location exists, but no screenshot is loaded yet, and the time elapsed is more than it should be for this image index, load this image
		if (!pReel[n].bAttemptedLoad)
		{
			const int64_t startLoadBeforeMs =
			  std::roundf(baseAnimDuration * loadInAdvanceMultipliers[(int)std::abs(n - reelEndPos)]) + loadDelayRandomSeeds[n % REEL_SIZE];
			if (remainingAnimationTime <= startLoadBeforeMs && !pReel[n].round().ScreenshotFile.empty())
			{
				pReel[n].bAttemptedLoad = true;
				pApp->DoAsyncTask(std::bind(&TScreenshotViewer::threadedLoad, this, mActiveLoadID, n, pReel[n].round(), CurrentTranslations));
#ifdef SCREENSHOTS_DEBUG
				printf("Started loading base screenshot %d\n", n);
#endif
			}
		}
	}

	const float zoom = pZoomedScreenshot ? 1.f : 0.f;
	const int btnY = std::roundf(std::lerp(getHeight() - buttonHeight - buttonMargin, getHeight(), zoom));
	mpBtnRefresh->setY(btnY);
	mpBtnCounters->setY(btnY);
	mpBtnLogs->setY(btnY);
	mpBtnReplay->setY(btnY);
	mpBtnBack->setY(std::roundf(std::lerp(getHeight() - buttonHeight - buttonMargin - 30, getHeight(), zoom)));
	mpBtnCounters->setEnabled(mSelectedGameN >= 0 && (size_t)mSelectedGameN < pReel.size() && !pReel[mSelectedGameN].round().RoundInfo.Counters.empty() &&
	                          MenuGUI()->pAdminKeySystem->KeyHasPermissionFor(EKeyRights::TOTAL_COUNTERS));
	mpBtnLogs->setEnabled(mSelectedGameN >= 0 && mSelectedGameN < mNumImages);
	mpBtnReplay->setEnabled(mSelectedGameN >= 0 && mSelectedGameN < mNumImages && canReplay() && !pReel[mSelectedGameN].round().RoundInfo.YServerGameround.empty());

	TMenuPanelBase::drawLogic(graphics, deltaTime);
}

void TScreenshotViewer::OnCameBackFromGamePlay()
{
	pGameLoading.reset();
	setMouseInput(EventPropagateMode::NORMAL);
}

void TScreenshotViewer::zoomIn(ImagePtr screenshot)
{
	pZoomedScreenshot = std::move(screenshot);
	setMouseInput(EventPropagateMode::NO_CHILDREN);
	pCanGoBack = true;
}

void TScreenshotViewer::zoomOut()
{
	if (!pZoomedScreenshot)
		return;
	pZoomedScreenshot.reset();
	setMouseInput(EventPropagateMode::NORMAL);
	pCanGoBack = false;
}
