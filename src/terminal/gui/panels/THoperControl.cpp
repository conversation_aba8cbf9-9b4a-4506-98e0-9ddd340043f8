/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include "THoperControl.h"

#include "MyUtils.h"
#include "YUtils.h"
#include "drv/money/TMoneyAcceptor.h"

constexpr float HopperControlXShift = 408.f;
THoperControl::THoperControl(Container* pParent, float x, float y, float w, float h) : TSetupBase(pParent, x, y, w, h)
{
	setId("hopper-control");
	mCaption = LocalizedMessage(HOPER_CONTROL_PANEL_STRING);

	pLblPaymentFillModeWarning = AddLabel(LocalizedMessage("WARNING: Refill mode enabled!"), 45, 22, 250, 23, Red);
	pLblPaymentFillModeWarning->setVisible(true);

	// AddCheckBox("TEST", "IS_ENABLED", "192.168.1.1", 100,100,100,20);

	for (size_t i = 0; i < HopperControls.size(); i++)
	{
		AddLabel(LocalizedMessage("Hoper " + std::to_string(i + 1)), 44 + HopperControlXShift * i, 75, 73, 24)->mTypography =
		  Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px });
		const int coinVal = MenuGUI()->pMoneyAcceptor->GetHoperCoinsValueInCents(i + 1);
		AddLabel(coinVal ? MenuGUI()->FormatCurrency(coinVal * 1e-2) : std::string("non-configured"), 125 + HopperControlXShift * i, 78, 120, 20);

		if (!coinVal)
			continue;

		HopperControls[i].lStatus = AddLabel({}, 208 + HopperControlXShift * i, 75, 160, 20);
		HopperControls[i].lStatus->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px });
		AddLabel(LocalizedMessage("Total to pay:"), 44 + HopperControlXShift * i, 299, 140, 20)->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].lTotalPay =
		  AddLabel({}, 227 + HopperControlXShift * i, 299, 160, 20) /*->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px })*/;
		AddLabel(LocalizedMessage("Already paid:"), 44 + HopperControlXShift * i, 321, 140, 20)->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].lPending =
		  AddLabel({}, 227 + HopperControlXShift * i, 321, 160, 20) /*->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px })*/;
		AddLabel(LocalizedMessage("Pending:"), 44 + HopperControlXShift * i, 342, 140, 20)->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].lPaid =
		  AddLabel({}, 227 + HopperControlXShift * i, 342, 160, 20) /*->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px })*/;

		HopperControls[i].pePayAmount = AddTextEdit("pay-amount", LocalizedMessage("Pay Amount"),
		                                            pApp->GetOrSetParam("LAST_H1_MANUAL_PAY", "0", "Last Hopper 1 manual pay", "", PARAM_READ_GLOBAL_WRITE_GLOBAL),
		                                            117 + HopperControlXShift * i, 119, 65, 30, EInputType::INTEGER);
		HopperControls[i].pePayAmount->pLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 10_px });
		HopperControls[i].pePayAmount->pLabel->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].pePayAmount->pLabel->setX(HopperControls[i].pePayAmount->pLabel->getX() + 25);
		HopperControls[i].pePayAmount->pLabel->setWidth(HopperControls[i].pePayAmount->pLabel->getWidth() - 25);

		AddLabel(LocalizedMessage("Hoper 1 Stock"), 44 + HopperControlXShift * i, 385, 173, 24)->mTypography =
		  Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px });
		HopperControls[i].peFillAmount = AddTextEdit("fill-amount", LocalizedMessage("Fill Amount"),
		                                             pApp->GetOrSetParam("LAST_H1_MANUAL_FILL", "0", "Last Hopper 1 manual fill", "", PARAM_READ_GLOBAL_WRITE_GLOBAL),
		                                             117 + HopperControlXShift * i, 421, 65, 30, EInputType::INTEGER);
		HopperControls[i].peFillAmount->pLabel->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 10_px });
		HopperControls[i].peFillAmount->pLabel->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].peFillAmount->pLabel->setX(HopperControls[i].peFillAmount->pLabel->getX() + 25);
		HopperControls[i].peFillAmount->pLabel->setWidth(HopperControls[i].peFillAmount->pLabel->getWidth() - 25);
		AddLabel(LocalizedMessage("Coins in stock:"), 44 + HopperControlXShift * i, 528, 140, 22)->setAlignment(align::RIGHT_CENTER);
		HopperControls[i].lStock =
		  AddLabel({}, 227 + HopperControlXShift * i, 528, 200, 20) /*->mTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = 18_px })*/;

		HopperControls[i].pCmdResume = AddButton("btn13", LocalizedMessage("Retry payment"), 227 + HopperControlXShift * i, 157, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdResume->Text->TextColor = Color::FromRGBInteger(0x10f010);
		HopperControls[i].pCmdResume->OnPressed += [i]() {
			MenuGUI()->pMoneyAcceptor->ResumeHoper(HOPPER_1 + i);
		};
		HopperControls[i].pCmdPay = AddButton("btn15", LocalizedMessage("Pay Coins Amount"), 44 + HopperControlXShift * i, 157, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdPay->OnPressed += [this, i]() {
			int64_t amount = yutils::strToInt(HopperControls[i].pePayAmount->getText().c_str(), 0);
			if (amount <= 0)
			{
				AddNotification({ LocalizedMessage("Invalid value"), HopperControls[i].pePayAmount->getText() }, 4, EVerbosity::Warning);
				return;
			}

			MenuGUI()->pMoneyAcceptor->PayViaHopperTestMode(amount, HOPPER_1 + i);
		};
		HopperControls[i].pCmdClear = AddButton("btn18", LocalizedMessage("Abort Payment"), 227 + HopperControlXShift * i, 227, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdClear->Text->TextColor = Color::FromRGBInteger(0xf05050);
		HopperControls[i].pCmdClear->OnPressed += [i]() {
			MenuGUI()->pMoneyAcceptor->ClearHoperCTG(HOPPER_1 + i, 1);
		};
		HopperControls[i].pCmdPayAll = AddButton("btn21", LocalizedMessage("Pay until empty"), 44 + HopperControlXShift * i, 227, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdClear->OnPressed += [i]() {
			MenuGUI()->pMoneyAcceptor->PayViaHopperTestMode(0xFFFF /*velika stevilka, da zagotovo izpraznemo*/, HOPPER_1 + i);
		};
		HopperControls[i].pCmdRefill = AddButton("btn14", LocalizedMessage("Refill Amount"), 44 + HopperControlXShift * i, 459, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdRefill->OnPressed += [this, i]() {
			int64_t amount = yutils::strToInt(HopperControls[i].peFillAmount->getText().c_str(), 0);
			if (amount <= 0 || amount > 54800)
			{
				AddNotification({ LocalizedMessage("Invalid value"), HopperControls[i].peFillAmount->getText() }, 4, EVerbosity::Warning);
				return;
			}

			MenuGUI()->pMoneyAcceptor->FillHoper(amount, HOPPER_1 + i);
		};
		if (MenuGUI()->pMoneyAcceptor)
			MenuGUI()->pMoneyAcceptor->FillHoper(atoi(HopperControls[0].peFillAmount->getText().c_str()), 1);
		HopperControls[i].pCmdSetStock0 =
		  AddButton("btn17", LocalizedMessage("Reset Stock to 0"), 227 + HopperControlXShift * i, 459, 140, 54, &MenuGUI()->PanelBigButtons);
		HopperControls[i].pCmdSetStock0->OnPressed += [i]() {
			MenuGUI()->pMoneyAcceptor->SetHoperStock(0, HOPPER_1 + i);
		};
	}

	pBtnOK->setVisible(false);

	// v tem panelu je pBtnExit("Back") viden
	pBtnExit->setVisible(true);
	pBtnExit->setEnabled(true);

	PanelDemandRestart();

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (bVisible)
		{
			// moramo preko timed task, ker isVisible() se vedno vraca false, sele ko se zakjluci ta funkcija, bo isVisible()==true..
			pApp->Defer(std::bind(&THoperControl::EnableTestModeWithRetry, this), "HopperEffectiveVisible", true);
		}
		else
		{
			if (MenuGUI()->pMoneyAcceptor->isTestMode())
			{
				MenuGUI()->pMoneyAcceptor->ClearHoperCTG(1);
				MenuGUI()->pMoneyAcceptor->ClearHoperCTG(2);
			}

			// izklop v tudi ce ni vkjlucen, lahko da tece timer za vklop!
			// z zakasnitvijo izkjlucimo TestMode, ker mora informacija ClearHopperCTG priti do hopper vezja
			pApp->Delay(std::bind(&TMoneyAcceptor::setTestMode, MenuGUI()->pMoneyAcceptor, false),
			            1100 /* 1100 zato, da je daljsi kot zgoraj v EnableTestModeWithRetry()*/, "TMoneyAcceptor::DisableTestMode, 1100");

			SaveForAll(false);    // naj se shranijo vrednosti v TEditBox-ih, da shranimo zadnje polnjenje
		}
	};
}

void THoperControl::drawLogic(Graphics* gfx, float dt)
{
	for (size_t i = 0; i < HopperControls.size(); i++)
	{
		if (!HopperControls[i].lStatus)
			continue;

		HopperControls[i].lStatus->clear();
		HopperControls[i].pCmdResume->setVisible(false);
		HopperControls[i].pCmdPay->setVisible(MenuGUI()->pMoneyAcceptor->isTestMode());
		HopperControls[i].pCmdPay->setVisible(false);    // TO SEM SKRIL, ker BEGA UPORABNIKE
		HopperControls[i].pCmdClear->setVisible(MenuGUI()->pMoneyAcceptor->isTestMode());
		HopperControls[i].pCmdPayAll->setVisible(MenuGUI()->pMoneyAcceptor->isTestMode());
		HopperControls[i].pePayAmount->setVisible(HopperControls[i].pCmdPay->isVisible());

		if (MenuGUI()->pMoneyAcceptor->GetHoperStock(i + 1) < 1)
			HopperControls[i].lStatus->setCaption("Stock EMPTY!");
		else if (MenuGUI()->pMoneyAcceptor->IsHoperLow(i + 1))
			HopperControls[i].lStatus->setCaption("Stock LOW(<100)!");
		else if (MenuGUI()->pMoneyAcceptor->IsHoperFull(i + 1))
			HopperControls[i].lStatus->setCaption("Stock FULL!");
		else if (MenuGUI()->pMoneyAcceptor->IsHoperErrorAndCoinsPending(i + 1))
		{
			HopperControls[i].lStatus->setCaption("ERROR!");
			HopperControls[i].pCmdResume->setVisible(true);
		}
		HopperControls[i].lStock->setCaption("" + MyUtils::int2str(MenuGUI()->pMoneyAcceptor->GetHoperStock(i + 1)));
		HopperControls[i].lTotalPay->setCaption(MyUtils::int2str(MenuGUI()->pMoneyAcceptor->GetHoperCTG(i + 1) + MenuGUI()->pMoneyAcceptor->GetHoperCP(i + 1)) +
		                                        " coins (" +
		                                        MenuGUI()->FormatCurrency((MenuGUI()->pMoneyAcceptor->GetHoperCTG(i + 1) + MenuGUI()->pMoneyAcceptor->GetHoperCP(i + 1)) *
		                                                                  MenuGUI()->pMoneyAcceptor->GetHoperCoinsValueInCents(1) / 100.0) +
		                                        ")");
		HopperControls[i].lPending->setCaption(
		  MyUtils::int2str(MenuGUI()->pMoneyAcceptor->GetHoperCP(i + 1)) + " coins (" +
		  MenuGUI()->FormatCurrency(MenuGUI()->pMoneyAcceptor->GetHoperCP(i + 1) * MenuGUI()->pMoneyAcceptor->GetHoperCoinsValueInCents(i + 1) / 100.0) + ")");
		HopperControls[i].lPaid->setCaption(
		  MyUtils::int2str(MenuGUI()->pMoneyAcceptor->GetHoperCTG(i + 1)) + " coins (" +
		  MenuGUI()->FormatCurrency(MenuGUI()->pMoneyAcceptor->GetHoperCTG(i + 1) * MenuGUI()->pMoneyAcceptor->GetHoperCoinsValueInCents(i + 1) / 100.0) + ")");
	}

	TSetupBase::drawLogic(gfx, dt);
}

bool THoperControl::EnableTestModeWithRetry()    // ker ce hopper izplacuje, moramo poskusati kasneje
{
	if (!isVisible())
		return false;

	if (MenuGUI()->pMoneyAcceptor->setTestMode(true))
		return true;

	pApp->Delay(std::bind(&THoperControl::EnableTestModeWithRetry, this), 1000, "THoperControl::EnableTestModeWithRetry, 1000");
	return false;
}
