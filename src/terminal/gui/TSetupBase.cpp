#include "TSetupBase.h"

#include "PackmanClient.h"
#include "TAdminKeySystem.h"
#include "common/TAnyGameClient.h"
#include "common/TMenuGUI.h"

TSetupBase::TSetupBase(Container* pParent, float x, float y, float w, float h, bool bIsEmbedded) : TCaptionedMenuPanel(pParent, x, y, w, h, bIsEmbedded)
{
	pDefaultFocused = NULL;
	setFocusMode(EFocusMode::Accept);

	pCenterButtonArea = new LayoutContainer(this, w / 2, h - (bEmbedded ? 0.f : EDGE_MARGIN), 0.f, 54);
	pCenterButtonArea->setOrigin(0.5f, 1.f);
	pCenterButtonArea->bResizeToFitLayout = { true, false };
	StackLayout* stackLayout = pCenterButtonArea->setLayout<StackLayout>();
	stackLayout->Direction = EStackLayoutDirection::HORIZONTAL;
	stackLayout->Alignment = EChildAlignment::Stretch;
	stackLayout->Padding.X() = EDGE_MARGIN;

	pBtnExit = AddButton("btnClear", LocalizedMessage(EXIT_STRING), 0, 0, 80, 54, &MenuGUI()->PanelButtons);
	pBtnExit->ClickSound = "menu_back.wav";
	pBtnExit->setVisible(false);
	pBtnExit->setEnabled(false);
	pBtnExit->OnPressed += std::bind(&TPanelBase::Hide, this);

	pBtnOK = AddButton("btnRepeat", LocalizedMessage(SAVE_STRING), 0, 0, 80, 54, &MenuGUI()->PanelButtons, Color::FromRGBInteger(0x008500));
	pBtnOK->ClickSound = "menu_validate.wav";
	pBtnOK->setOrigin(1.f);
	pBtnOK->setPosition(w - (bEmbedded ? 0.f : EDGE_MARGIN), h - (bEmbedded ? 0.f : EDGE_MARGIN));
	pBtnOK->OnPressed += [this]() {
		if (MenuGUI()->pAdminKeySystem->IsKeyAuthenticatedAtTheMoment())
		{
			Save();
		}
		else
			AddNotification({ LocalizedMessage(INSERT_ADMIN_KEY_STRING), LocalizedMessage(FOR_CHANGES_TO_TAKE_EFFECT_STRING) }, 3.5f);
	};

	auto packman = pApp->GetModuleByName<TPackmanClient>("Packman");
	const bool bIsMultiplayer = packman->Features().contains(igp::MultiplayerFeatureName);
	if (bIsMultiplayer)
	{
		pBtnSaveForAll = AddButton("btnSaveForAll", LocalizedMessage(SAVE_FOR_ALL_STRING), 0, 0, 80, 54, &MenuGUI()->PanelButtons, Color::FromRGBInteger(0x008500));
		pBtnSaveForAll->ClickSound = "menu_validate.wav";
		pBtnSaveForAll->setOrigin(1.f);
		pBtnSaveForAll->setPosition(w - (bEmbedded ? 0.f : EDGE_MARGIN) - pBtnOK->getWidth(), h - (bEmbedded ? 0.f : EDGE_MARGIN));
		pBtnSaveForAll->setVisible(false);
		pBtnSaveForAll->OnPressed += [this]() {
			if (MenuGUI()->pAdminKeySystem->IsKeyAuthenticatedAtTheMoment())
				SaveForAll();
			else
				AddNotification({ LocalizedMessage(INSERT_ADMIN_KEY_STRING), LocalizedMessage(FOR_CHANGES_TO_TAKE_EFFECT_STRING) }, 3.5f);
		};
	}

	SetChildrenTabInEnabled(false);
	bSkipRefreshAtShow = false;

	pApp->NewActionListener("screenKeyboardKeyReleased", [this](const std::string& actionName, void* userData) {
		if ((long)userData == SDLK_RETURN)
		{
			// if the panel is visible and it is focused or one of its children is focused, save!
			if (getEffectiveVisiblity(false) && (isFocused() || containsWidget(GetCurrentFocusedWidget(), true)))
				Save();
		}
	});

	OnEffectiveVisibleChanged += [this](Widget* src, bool bVisible) {
		if (bVisible)
		{
			if (!bSkipRefreshAtShow)
				Refresh();
		}
		else
		{
			ForAllParamsOnPanel([](const std::shared_ptr<TAppParam>& param) { param->Reset(); });

			OnPanelLeft();
			Refresh();

			MenuGUI()->HideMenuPopupMessage();

			MenuGUI()->pKeyboard->SetInputType(EInputType::STRING);
			MenuGUI()->pKeyboard->Hide();
		}
	};
}

void TSetupBase::drawLogic(Graphics* graphics, float deltaTime)
{
	if (MenuGUI()->pAdminKeySystem->IsKeyAuthenticatedAtTheMoment())
	{
		pBtnOK->setEnabled(true);
		pBtnOK->Text->removeCaptionLine(1);

		if (pBtnSaveForAll)
		{
			pBtnSaveForAll->setEnabled(true);
			pBtnSaveForAll->Text->removeCaptionLine(1);
		}
	}
	else
	{
		pBtnOK->setEnabled(false);
		pBtnOK->Text->setCaptionLine(1, LocalizedMessage(INSERT_KEY_STRING));

		if (pBtnSaveForAll)
		{
			pBtnSaveForAll->setEnabled(false);
			pBtnSaveForAll->Text->setCaptionLine(1, LocalizedMessage(INSERT_KEY_STRING));
		}
	}
	TMenuPanelBase::drawLogic(graphics, deltaTime);
}

TStyleButton2* TSetupBase::AddActionButton(const std::string& name, const LocalizedMessage& Caption, const std::function<void()>& onPressed, bool bRequireConfirm)
{
	TStyleButton2* btn;
	if (bRequireConfirm)
		btn = AddConfirmButton2(pCenterButtonArea, name, Caption, 0, 0, 140, 54, &MenuGUI()->PanelBigButtons);
	else
		btn = AddButton2(pCenterButtonArea, name, Caption, 0, 0, 140, 54, &MenuGUI()->PanelBigButtons);

	if (onPressed)
		btn->OnPressed += onPressed;

	return btn;
}

void TSetupBase::PanelDemandRestart()
{
	updateChildrenPlacement();
	ForAllParamsOnPanel([](const std::shared_ptr<TAppParam>& param) { pApp->RebootOnParam(param->Name()); });
}

bool TSetupBase::SaveForAll(bool showMessage /* = true */)
{
	std::string BeforeSaveResult;
	BeforeSaveResult = BeforeSaveData(pApp->GetGlobalDomain());
	if (!BeforeSaveResult.empty())
	{
		AddNotification({ LocalizedMessage(ERROR_STRING), BeforeSaveResult }, 4, EVerbosity::Error);
		return 0;
	}

	pApp->DoAsyncTask([this, showMessage]() {
		const bool bSuccess = MenuGUI()->pDBMngr->ExecuteSync<bool>(
		  [this](TGameDBSrv* db, soci::session& sql) {
			  ForAllParamsOnPanel([db](const std::shared_ptr<TAppParam>& param) {
				  if (param /*&& param->Modified()*/)
				  {
					  if (param->GetRawDomain() != pApp->GetGlobalDomain())
					  {
						  db->ClearAllParamsWithName(param->Name());
						  ParameterDomain prefered_domain = param->GetPreferedSaveLocation();
						  param->SetPreferedSaveLocation(param->GetSaveForAllLocation());
						  param->Save(EParamSaveMode::LOG_CHANGE, true, ParameterChangeSource::User);

						  param->SetPreferedSaveLocation(prefered_domain);
					  }
					  else if (param->Modified())
					  {
						  param->Save(EParamSaveMode::LOG_CHANGE, true, ParameterChangeSource::User);
					  }
				  }
			  });
			  return true;
		  },
		  false);


		pApp->Defer(
		  [this, showMessage, bSuccess]() {
			  AfterSaveData(pApp->GetGlobalDomain());

			  if (bSuccess)
			  {
				  AddNotification({ LocalizedMessage(PARAMETERS_SAVED_STRING), LocalizedMessage(YOU_MUST_RESTART_ALL_PLAYBOARDS_STRING),
				                    LocalizedMessage(FOR_CHANGES_TO_TAKE_EFFECT_STRING) },
				                  -1, EVerbosity::Important);
			  }
			  else if (showMessage)
			  {
				  AddNotification({ LocalizedMessage(ERROR_STRING), LocalizedMessage(PARAM_NOT_SAVED_STRING) }, -1, EVerbosity::Error);
			  }
		  },
		  "SetupBase::SaveForAll");
	});
	return true;
}

bool TSetupBase::Save(bool showMessage /* = true */)
{
	std::string BeforeSaveResult = BeforeSaveData(pApp->ClientID());
	if (!BeforeSaveResult.empty())
	{
		AddNotification({ LocalizedMessage(ERROR_STRING), BeforeSaveResult }, 4, EVerbosity::Error);
		return false;
	}

	pApp->DoAsyncTask([this, showMessage]() {
		const bool bSuccess = MenuGUI()->pDBMngr->ExecuteSync<bool>(
		  [this](TGameDBSrv* db, soci::session& sql) {
			  ForAllParamsOnPanel([](const std::shared_ptr<TAppParam>& param) {
				  if (param && param->Modified())
				  {
					  param->Save(EParamSaveMode::LOG_CHANGE, true, ParameterChangeSource::User);
				  }
			  });
			  return true;
		  },
		  false, 0, false, true, { ETransactionFlags::WaitTransaction });

		pApp->Defer(
		  [this, showMessage, bSuccess]() {
			  AfterSaveData(pApp->ClientID());

			  if (!bSuccess)
			  {
				  AddNotification({ LocalizedMessage(ERROR_STRING), LocalizedMessage(PARAM_NOT_SAVED_STRING) }, 0, EVerbosity::Error);
			  }
			  else if (showMessage)
			  {
				  if (MenuGUI()->pClient->CLIENT_RESTART_REQUIRED_LOCK->IsLocked())
					  AddNotification({ LocalizedMessage(PARAMETERS_SAVED_STRING), LocalizedMessage(YOU_MUST_RESTART_WHOLE_MACHINE_STRING),
					                    LocalizedMessage(FOR_CHANGES_TO_TAKE_EFFECT_STRING) },
					                  0, EVerbosity::Important);
				  else
					  AddNotification({ LocalizedMessage(PARAMETERS_SAVED_STRING) }, 3.5f, EVerbosity::Important);
			  }
		  },
		  "SetupBase::Save");
	});

	return false;
}

void ForAllParamsInContainer(const Container* cont, const std::function<void(const std::shared_ptr<TAppParam>&)>& worker)
{
	for (const auto& w : cont->children())
	{
		if (!w.isValid())
			continue;

		if (const Container* container = dynamic_cast<Container*>(w.Child))
			ForAllParamsInContainer(container, worker);

		if (const TDBParamControl* control = dynamic_cast<TDBParamControl*>(w.Child))
		{
			if (auto param = control->Param())
				worker(param);
		}
	}
}

void TSetupBase::ForAllParamsOnPanel(const std::function<void(const std::shared_ptr<TAppParam>&)>& worker) const
{
	ForAllParamsInContainer(this, worker);

	for (const auto& param : ExtraParamsToSave) worker(param);
}
