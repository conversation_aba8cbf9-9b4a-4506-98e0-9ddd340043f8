#include "TMenuTabHolder.h"

#include "TIGPlatformApp.h"
#include "TotallyTemporaryDesignTokens.h"

const std::string CutoutShaderSource_FacingRight = R"(
uniform vec4 uCutoutArea;
uniform vec4 uCornerRadius;
uniform float uFeather;
uniform float uTopCurveRadius;
uniform float uBottomCurveRadius;
uniform float uCurveRadius;

in vec2 texCoord;

out vec4 fragColor;

float roundedRectMaskUniform(vec2 distFromCenter, vec2 rectSize, float cornerRadius, float feather)
{
    float edgeDistance = distanceToRoundedBox(distFromCenter, rectSize, cornerRadius);

    float smoothedMask = 1.0 - smoothstep(0.0, feather, edgeDistance);

    return smoothedMask;
}

void main(void)
{
	vec2 uvScreenSpace = texCoord * uSize;
	float btnMask = roundedRectMask(uvScreenSpace - (uCutoutArea.xy + uCutoutArea.zw * 0.5), uCutoutArea.zw, uCornerRadius, uFeather);
	float maskTopCurve = 1.0 - roundedRectMaskUniform(uvScreenSpace - (uCutoutArea.xy + vec2(uCutoutArea.z, 0.0) - vec2(uTopCurveRadius + uFeather)), vec2(2.0 * uTopCurveRadius), uTopCurveRadius, uFeather);
	float maskBottomCurve = 1.0 - roundedRectMaskUniform(uvScreenSpace - (uCutoutArea.xy + uCutoutArea.zw + vec2(-uBottomCurveRadius-uFeather, uBottomCurveRadius+uFeather)), vec2(2.0 * uBottomCurveRadius), uBottomCurveRadius, uFeather);
	float maskOverall = step(uCutoutArea.y - uTopCurveRadius - uFeather, uvScreenSpace.y) * (1.0 - step(uCutoutArea.y + uCutoutArea.w + uBottomCurveRadius + uFeather, uvScreenSpace.y));

	float maskCurve = mix(maskTopCurve, maskBottomCurve, step(uCutoutArea.y + uCutoutArea.w * 0.5, uvScreenSpace.y));
    fragColor = BRUSH_0(texCoord);
	fragColor.a *= mix(btnMask, maskCurve, step(uCutoutArea.x + uCutoutArea.z - uCurveRadius, uvScreenSpace.x)) * maskOverall;
}
)";

TMenuTabHolder::TMenuTabHolder()
{
	setId("TMenuTabHolder");

	OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);

	FocusMode = EFocusMode::Accept;

	auto brush = ImageBrush(Image::GetImage("bgTextureDark.png"));
	brush.tiling = true;
	Background = brush;

	gridLayout = setLayout<GridLayout>();
	gridLayout->AddRows({ { .Size = 504, .Margin = Vector2D(8, 0) },
	                      { .Size = 96, .Margin = Vector2D(0, 8) },
	                      { .Size = 96, .Margin = Vector2D(0, 8) },
	                      { .Size = 96, .Margin = Vector2D(0, 8) },
	                      { .Size = 96, .Margin = Vector2D(0, 8) } });
	gridLayout->AddColumn({ .Margin = 8 });
	gridLayout->bUseMarginBeforeFirstAndAfterLast = true;

	gameCategoryContainer = new LayoutContainer();
	gameCategoryContainer->setSize(96, 500);
	gameCategoryContainer->setId("game-category-container");
	gameCategoryContainer->OutlineStyle.SetCornerRadius(tempDesignTokens::RoundedCorner_12);
	gameCategoryContainer->bResizeToFitLayout = { false, true };
	add(gameCategoryContainer);

	brush = ImageBrush(Image::GetImage("bgTextureSemiDark.png"));
	brush.tiling = true;
	MenuButtonBackground = brush;

	SelectedMenuButtonShadow = Shadow(Black, {}, {}, 10_px, {}, false);

	auto layout = gameCategoryContainer->setLayout<StackLayout>();
	layout->Direction = EStackLayoutDirection::VERTICAL;
	layout->Alignment = EChildAlignment::Center;

	gridLayout->Set(gameCategoryContainer, { 0, 0, 1, 1, ChildAlignment2D(EChildAlignment::Center, EChildAlignment::Min) });

	for (ELobbyGameCategory value : ELobbyGameCategory::_values())
	{
		TLobbyMenuButton* menuCategory = addCategory(value);
		gameCategoryContainer->add(menuCategory);
		menuCategory->OnClicked += [this, value](Widget* w, const Vector2D& pos) {
			pGuiApp->PlaySound(tempDesignTokens::buttonPress);
			SetSelectedCategory(value);
		};
	}

	for (ELobbyButtons value : ELobbyButtons::_values())
	{
		TLobbyMenuButton* menuCategory = addCategory(value);
		add(menuCategory);
		if (value == ELobbyButtons::ChangeGame)
			gridLayout->Set(menuCategory, { 0, 0, 1, 1, ChildAlignment2D(EChildAlignment::Min) });
		else
			gridLayout->Set(menuCategory, { 0, static_cast<uint8_t>(value), 1, 1, ChildAlignment2D(EChildAlignment::Min) });
		menuCategory->OnClicked += [this, value](Widget* w, const Vector2D& pos) {
			pGuiApp->PlaySound(tempDesignTokens::buttonPress);
			SetSelectedButton(value);
		};
	}

	mLobbyButtons[ELobbyButtons::CloseAllGames]->OnClicked += [this](Widget* w, const Vector2D& pos) {
		mLobbyButtons[ELobbyButtons::CloseAllGames]->plTitle->setCaption(LocalizedMessage(CLOSING_ALL_GAMES_STRING));
	};

	SetSelectedCategory(ELobbyGameCategory::AllGames);
	SetSelectedButton(ELobbyButtons::AddGame);

	SelectionCutoutShader = pGuiApp->GUI()->getGraphics()->createShaderFromProgram(pGuiApp->GUI()->getGraphics()->compileShaderProgram(
	  "menu-tab-bg",
	  ShaderCompileSettings({ .FragShader = CutoutShaderSource_FacingRight,
	                          .NumBrushes = 1,
	                          .AdditionalUniforms = { "uCutoutArea", "uFeather", "uCurveRadius", "uTopCurveRadius", "uBottomCurveRadius", "uCornerRadius" } })));
}

void TMenuTabHolder::drawBackground(Graphics* graphics, const Outline& outline)
{
	LayoutContainer::drawBackground(graphics, outline);

	if (SelectionCutoutShader)
	{
		Widget* selectedWidget = nullptr;

		bool isTop = false;
		bool isBottom = false;

		if (gameCategoryContainer->isVisible())
		{
			auto it = mGameCategories.find(mSelectedCategory);
			if (it != mGameCategories.end() && it->second->GetSelected())
			{
				selectedWidget = it->second;

				if (it->first == ELobbyGameCategory::AllGames)
					isTop = true;
			}
		}

		if (!selectedWidget)
		{
			auto itButtons = mLobbyButtons.find(mSelectedButton);
			if (itButtons != mLobbyButtons.end() && itButtons->second->GetSelected())
			{
				selectedWidget = itButtons->second;

				if (itButtons->first == ELobbyButtons::ChangeGame || itButtons->first == ELobbyButtons::AddGame)
					isTop = true;
				if (itButtons->first == ELobbyButtons::Settings)
					isBottom = true;
			}
		}

		if (!selectedWidget)
			return;

		const Outline widgetOutline = selectedWidget->getOutlineStyle().Value();
		graphics->pushBrush(MenuButtonBackground.Value());

		if (gameCategoryContainer->isVisible())
			graphics->fillRectangleWithBrush(gameCategoryContainer->getRelativeDimension(this), gameCategoryContainer->getOutlineStyle().Value());

		Rectangle cutoutArea = selectedWidget->getRelativeDimension(this);
		cutoutArea.width() += getWidth() - cutoutArea.right();    // expand to right edge
		DimensionBox cornerRadius = widgetOutline.CornerRadius;
		cornerRadius[1] = cornerRadius[2] = Dimension();    // square out right side
		graphics->renderBoxShadow(cutoutArea, cornerRadius, SelectedMenuButtonShadow.Value());

		const float featherPx = widgetOutline.Feather.getValue(EDimensionUnit::px);
		const float maxPossibleRadius = cutoutArea.Size.min() / 2.f - featherPx;


		SelectionCutoutShader->Activate(
		  [&](ShaderProgram& s) {
			  s.SetRectangle("uCutoutArea", cutoutArea);
			  s.SetFloat("uFeather", featherPx);
			  s.SetFloat("uCurveRadius", 16.f);
			  s.SetFloat("uBottomCurveRadius", (isBottom) ? 0.f : 16.f);
			  s.SetFloat("uTopCurveRadius", (isTop) ? 0.f : 16.f);
			  s.SetRectangle("uCornerRadius", cornerRadius.asRectangle(EDimensionUnit::px, maxPossibleRadius));
			  graphics->shaderPaint(s, Rectangle(0, getSize()));
		  },
		  { graphics->getBrush() });



		graphics->popBrush();
	}
}

bool TMenuTabHolder::shouldDrawBackground(const Brush& background, const Outline& outline) const
{
	if (SelectionCutoutShader)
		return true;

	return LayoutContainer::shouldDrawBackground(background, outline);
}

TLobbyMenuButton::LobbyMenuButtonData getMenuButtonData(ELobbyButtons category)
{
	TLobbyMenuButton::LobbyMenuButtonData data;
	switch (category)
	{
		// settings categories
		case ELobbyButtons::AddGame:
			data.Title = LocalizedMessage(ADD_GAME_STRING);
			data.ButtonImageEnabled = Image::GetImage("addGame.png");
			data.ButtonImageDisabled = Image::GetImage("addGame.png");
			break;
		case ELobbyButtons::CloseAllGames:
			data.Title = LocalizedMessage(CLOSE_ALL_GAMES_STRING);
			data.ButtonImageEnabled = Image::GetImage("menuClose.png");
			data.ButtonImageDisabled = Image::GetImage("menuClose.png");
			break;
		case ELobbyButtons::Settings:
			data.Title = LocalizedMessage(SETTINGS_STRING);
			data.ButtonImageEnabled = Image::GetImage("settingsMenu.png");
			data.ButtonImageDisabled = Image::GetImage("settingsMenu.png");
			break;
		case ELobbyButtons::Info:
			data.Title = LocalizedMessage(INFO_STRING);
			data.ButtonImageEnabled = Image::GetImage("infoMenu.png");
			data.ButtonImageDisabled = Image::GetImage("infoMenu.png");
			break;
		case ELobbyButtons::History:
			data.Title = LocalizedMessage(HISTORY_STRING);
			data.ButtonImageEnabled = Image::GetImage("historyMenu.png");
			data.ButtonImageDisabled = Image::GetImage("historyMenu.png");
			break;
		case ELobbyButtons::ChangeGame:
			data.Title = LocalizedMessage(CHANGE_GAME_STRING);
			data.ButtonImageEnabled = Image::GetImage("changeGame.png");
			data.ButtonImageDisabled = Image::GetImage("changeGame.png");
			break;
		default:;
	}
	return data;
}

TLobbyMenuButton::LobbyMenuButtonData getGameCategoryData(ELobbyGameCategory category)
{
	TLobbyMenuButton::LobbyMenuButtonData data;
	switch (category)
	{
		// games categories
		case ELobbyGameCategory::AllGames: data.Title = LocalizedMessage(ALL_GAMES_STRING); break;
		case ELobbyGameCategory::RouletteGames: data.Title = LocalizedMessage(ROULETTE_STRING); break;
		case ELobbyGameCategory::CardGames: data.Title = LocalizedMessage(CARD_GAMES_STRING); break;
		case ELobbyGameCategory::iGames: data.Title = LocalizedMessage(I_GAMES_STRING); break;
		case ELobbyGameCategory::ActiveGames: data.Title = LocalizedMessage(ACTIVE_GAMES_STRING); break;
		default:;
	}
	return data;
}

TLobbyMenuButton* TMenuTabHolder::addCategory(ELobbyGameCategory gameCategory)
{
	TLobbyMenuButton* pMenuCategory = new TLobbyMenuButton(getGameCategoryData(gameCategory));
	pMenuCategory->setId("gameCategoryButton-" + std::string(gameCategory._to_string()));
	mGameCategories.insert({ gameCategory, pMenuCategory });
	return pMenuCategory;
}

TLobbyMenuButton* TMenuTabHolder::addCategory(ELobbyButtons lobbyButton)
{
	TLobbyMenuButton* pMenuCategory = new TLobbyMenuButton(getMenuButtonData(lobbyButton));
	pMenuCategory->setId("lobbyButton-" + std::string(lobbyButton._to_string()));
	auto brush = ImageBrush(Image::GetImage("bgTextureSemiDark.png"));
	brush.tiling = true;
	pMenuCategory->Background = brush;
	mLobbyButtons.insert({ lobbyButton, pMenuCategory });
	return pMenuCategory;
}

void TMenuTabHolder::SetSelectedCategory(ELobbyGameCategory selectedCategory)
{
	for (auto const& [category, button] : mGameCategories) button->SetSelected(category == selectedCategory);

	mSelectedCategory = selectedCategory;
	OnGameCategorySelected(mSelectedCategory);
}

void TMenuTabHolder::DeselectAllLobbyButtons()
{
	for (auto const& [category, button] : mLobbyButtons) button->SetSelected(false);
}

void TMenuTabHolder::SetSelectedButton(ELobbyButtons selectedCategory, bool triggerEvent)
{
	for (auto const& [category, button] : mLobbyButtons) button->SetSelected(category == selectedCategory);

	mSelectedButton = selectedCategory;
	if (triggerEvent)
		OnLobbyButtonPressed(mSelectedButton);
}

void TMenuTabHolder::SetCategoryVisible(ELobbyGameCategory category, bool visible)
{
	auto it = mGameCategories.find(category);
	if (it != mGameCategories.end())
	{
		it->second->setVisible(visible);
	}
}

void TMenuTabHolder::SetLobbyVisible(const bool visible)
{
	bLobbyVisible = visible;
	gameCategoryContainer->setVisible(bLobbyVisible);
	SetNumberOfActiveGames(ActiveGameCounter);
	mLobbyButtons[ELobbyButtons::AddGame]->setVisible(!bLobbyVisible && ActiveGameCounter != dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames());
	mLobbyButtons[ELobbyButtons::ChangeGame]->setVisible(!bLobbyVisible && ActiveGameCounter == dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames());
}

void TMenuTabHolder::SetNumberOfActiveGames(const int numberOfActiveGames)
{
	ActiveGameCounter = numberOfActiveGames;

	if (ActiveGameCounter == 0)
		mLobbyButtons[ELobbyButtons::CloseAllGames]->plTitle->setCaption(LocalizedMessage(CLOSE_ALL_GAMES_STRING));

	if (!bLobbyVisible && ActiveGameCounter > 1)
		mLobbyButtons[ELobbyButtons::CloseAllGames]->setVisible(true);
	else
		mLobbyButtons[ELobbyButtons::CloseAllGames]->setVisible(false);

	if (ActiveGameCounter > 0)
		mLobbyButtons[ELobbyButtons::History]->setVisible(true);
	else
		mLobbyButtons[ELobbyButtons::History]->setVisible(false);

	if (ActiveGameCounter == dynamic_cast<TIGPlatformApp*>(pApp)->GetMaxNumberOfConcurrentGames())
	{
		mLobbyButtons[ELobbyButtons::AddGame]->setVisible(false);
		mLobbyButtons[ELobbyButtons::ChangeGame]->setVisible(true);
	}
	else
	{
		mLobbyButtons[ELobbyButtons::AddGame]->setVisible(true);
		mLobbyButtons[ELobbyButtons::ChangeGame]->setVisible(false);
	}
}

TLobbyMenuButton* TMenuTabHolder::GetLobbyButton(ELobbyButtons category)
{
	auto it = mLobbyButtons.find(category);
	if (it != mLobbyButtons.end())
	{
		return it->second;
	}
	return NULL;
}
