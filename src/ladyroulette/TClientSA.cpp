#include "TClientSA.h"

#include "TClientSAStates.h"
#include "TLadyRouletteApp.h"
#include "common/TRouletteBaseDBSrv.h"

TClientSA::TClientSA(Container* parent, float x, float y, float width, float height, const std::string& ComponentName) :
    TGuiComponent(parent, x, y, width, height, ComponentName)
{
	pDBMngr = NULL;

	mpVideo1 = NULL;
	mpVideo2 = NULL;

	mpShownVideo = NULL;
	mpLoadedVideo = NULL;

	mSyncTime = 0;

	mPhase = GAME_PAUSED_STATE;
	mPhaseSubId = -1;
	pDBMngr = pApp->GetModuleByName<TDBManager>("DBManager1");
	pRNGManager = pApp->GetModuleByName<TRNGManager>("RNGManager1");

	// misc
	mVideoPath = pGuiApp->ImageDir() / "LADYROULETTE";    // naložimo delovno pot map projekta

	// mDiffTimer = SDL_GetTicks64();

	// master audio
	mGotMasterAudio = false;
	/*
	SDL_AudioSpec wavSpec;
	std::string audioFilename = mVideoPath + "/LADY_RES/Master_Audio_02_2.wav";
	if (std::filesystem::exists(audioFilename))
	{
	    if (SDL_LoadWAV(audioFilename.c_str(), &wavSpec, &mWavBuffer, &mWavLength) != NULL)
	    {
	        mMasterAudioDeviceId = SDL_OpenAudioDevice(NULL, 0, &wavSpec, NULL, 0);
	        SDL_QueueAudio(mMasterAudioDeviceId, mWavBuffer, mWavLength);

	        mGotMasterAudio = true;
	    }
	    if (mGotMasterAudio)
	        SDL_PauseAudioDevice(mMasterAudioDeviceId, 1);
	}
	*/

	// init libav
	// av_register_all();
	// avcodec_register_all();
	// avfilter_register_all();
	av_log_set_level(AV_LOG_QUIET);

	// videos
	mIntermezzoPath = mVideoPath / "LADY_RES" / "Intermezzo.mp4";
	mpIntermezzoVideo = new TVideo(this, 0, 0, getWidth(), getHeight(), "mpIntermezzoVideo");
	mpIntermezzoVideo->DisableAudio();
	mpIntermezzoVideo->LoadVideo(mIntermezzoPath, true, VIDEO_BUFFER);
	mpIntermezzoVideo->LoopVideo();
	mpIntermezzoVideo->Play();

	mpVideo1 = new TVideo(this, 0, 0, getWidth(), getHeight(), "mpVideo1");
	mpVideo1->QuitThreadAfterLoadFinish(true);
	// mpVideo1->DisableAudio();
	mpVideo1->setVisible(false);

	mpVideo2 = new TVideo(this, 0, 0, getWidth(), getHeight(), "mpVideo2");
	mpVideo2->QuitThreadAfterLoadFinish(true);
	// mpVideo2->DisableAudio();
	mpVideo2->setVisible(false);

	// state automat
	SetNext(new TStatePhasePaused(this));

	pApp->NewActionListener<TahMember<TClientSA>, ACTION_ASYNC>("OnGameStart", this, &TClientSA::OnGameStart);

	// polanje glavnega StateAvtomata
	mPollTask = pApp->AddTimedTask<TtthLambda>(
	  [this]() {
		  if (pApp->status() != EApplicationState::Running)
			  return;

		  LogicLoop();
	  },
	  10, TTimedTaskHandler::ENDLESS, "ClientSA Poll");


	if (pApp->GetParam("LadyForWeb")->AsInteger() == 1)
	{
		mTimetablePanel = new TTimetablePanel(this);
		mTimetablePanel->setVisible(false);
	}
}

TClientSA::~TClientSA()
{
	if (auto task = mPollTask.lock())
		task->Remove();

	pApp->UnregisterAllActionHandlersByRelatedObject(this);

	if (mGotMasterAudio)
	{
		SDL_CloseAudioDevice(mMasterAudioDeviceId);
		SDL_FreeWAV(mWavBuffer);
	}
}

void TClientSA::drawLogic(Graphics* graphics, float deltaTime)
{
	short SubPhase;
	int newPhase = rmclientdrv::GamePhaseAndSubPhase(pRNGManager->GetCurrentRPC(), SubPhase);
	mPhaseSubId = SubPhase;
	if (newPhase != mPhase)    // samo ob spremembi faze
	{
		switch (newPhase)
		{
			case STARTING_GAME_STATE: pApp->GenerateAction("SAGameInitExit"); break;
			case BETS_OPEN_STATE: pApp->GenerateAction("SAOpenBetsEnter"); break;
			case WAITING_WIN_NUMBER_STATE: pApp->GenerateAction("SACloseBetsEnter"); break;
			case CLOSING_GAME_STATE: /*pApp->GenerateAction("SAClosingGameEnter");* / SlowDownVideo();*/ break;
			default: break;
		}
	}
	mPhase = newPhase;

	// loop master audio
	if (mGotMasterAudio)
	{
		Uint32 size = SDL_GetQueuedAudioSize(mMasterAudioDeviceId);
		if (size == 0)
			SDL_QueueAudio(mMasterAudioDeviceId, mWavBuffer, mWavLength);
	}

	TGuiComponent::drawLogic(graphics, deltaTime);
}


void TClientSA::LoadVideo(const std::filesystem::path& filename)
{
	// to ni lih prav, ker client ne sme brat file ampak bi moral dobit podatke iz serverja
	std::string folder = filename.parent_path();
	if (mVideoFolder != folder)
	{
		// reset pos and size of videos
		mpVideo1->setDimension(Rectangle(0, getSize()));

		mpVideo2->setDimension(Rectangle(0, getSize()));

		if (std::filesystem::exists(mVideoPath / folder / "transform.json"))
		{
			std::ifstream info(mVideoPath / folder / "transform.json");
			json transform;
			try
			{
				info >> transform;
				if (transform.contains("scale"))    // ali scalamo ali pa cropamo
				{
					double scale = atof(transform["scale"].get<std::string>().c_str());
					if (scale != 1.0)
					{
						pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Scaling videos for factor %.2f.", scale);

						mpVideo1->setDimension(mpVideo1->getDimension().centerScale(scale));
						mpVideo2->setDimension(mpVideo2->getDimension().centerScale(scale));
					}
				}
				else if (transform.contains("crop"))
				{
					int left = transform["crop"]["left"].get<int>();
					int right = transform["crop"]["right"].get<int>();
					int top = transform["crop"]["top"].get<int>();
					int bottom = transform["crop"]["bottom"].get<int>();

					if (left > 0 || right > 0 || top > 0 || bottom > 0)
					{
						pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Croping videos for left = %d, right = %d, top = %d, bottom = %d.", left, right, top,
						               bottom);

						// crop
						mpVideo1->setDimension(-left, -top, mpVideo1->getWidth() + left + right, mpVideo1->getHeight() + top + bottom);
						mpVideo2->setDimension(-left, -top, mpVideo2->getWidth() + left + right, mpVideo2->getHeight() + top + bottom);
					}
				}

				if (transform.contains("move"))
				{
					int x = transform["move"]["x"].get<int>();
					int y = transform["move"]["y"].get<int>();

					if (x > 0 || y > 0)
					{
						pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Moving videos for x = %d, y = %d.", x, y);
						if (x > abs(mpVideo1->getX()) || y > abs(mpVideo1->getY()))
							pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID,
							               "\t[CLIENT] WARNING: Moving for x = %d / max = %d, y = %d / max = %d can result with black border.", x, abs(mpVideo1->getX()),
							               y, abs(mpVideo1->getY()));

						// move
						mpVideo1->setPosition(mpVideo1->getPosition() + Vector2D(x, y));

						mpVideo2->setPosition(mpVideo2->getPosition() + Vector2D(x, y));
					}
				}
			}
			catch (const std::exception& e)
			{
				pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Error parsing transform.json => %s", e.what());
			}
		}

		mVideoFolder = folder;
	}

	const std::filesystem::path filepath = mVideoPath / filename;
	if (mLoadedVideo == 1)
	{
		mpVideo2->LoadVideo(filepath, true, VIDEO_BUFFER);
		pGuiApp->DeferToDraw(std::bind(&TVideo::setVisible, mpVideo2, true), "TClientSA::LoadVideo", false, mpVideo2);
		// mpVideo2->setVisible(true);

		mpLoadedVideo = mpVideo2;
		mLoadedVideo = 2;
	}
	else
	{
		mpVideo1->LoadVideo(filepath, true, VIDEO_BUFFER);
		pGuiApp->DeferToDraw(std::bind(&TVideo::setVisible, mpVideo1, true), "TClientSA::LoadVideo", false, mpVideo1);
		// mpVideo1->setVisible(true);

		mpLoadedVideo = mpVideo1;
		mLoadedVideo = 1;
	}

	pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Video '%s' loaded for id %d", filename.c_str(), mLoadedVideo);
}

void TClientSA::FreeVideo()
{
	if (mpShownVideo)
	{
		const std::filesystem::path& shown = mpShownVideo->GetFilePath();
		std::filesystem::path loaded;
		if (mpLoadedVideo)
			loaded = mpLoadedVideo->GetFilePath();

		if (shown == loaded)
		{
			pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] ########## This should not happen - loaded and showned videos are the same. ##########");
			pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] ########## mpVideo1 = %s, mpVideo2 = %s [%d]. ##########", mpVideo1->GetFilename().c_str(),
			               mpVideo2->GetFilename().c_str(), mLoadedVideo);
		}

		mpShownVideo = NULL;
	}

	if (mLoadedVideo == 1)
	{
		mpVideo2->setVisible(false);
		mpVideo2->FreeVideo();
	}
	else
	{
		mpVideo1->setVisible(false);
		mpVideo1->FreeVideo();
	}
}

void TClientSA::SwitchVideo()
{
	if (mpLoadedVideo && mpLoadedVideo->IsLoaded(true))
	{
		mpLoadedVideo->Play();
		moveToTop(mpLoadedVideo);
	}
	else
	{
		pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT ERROR][%d] ########## No loaded video for switching! ##########", mPhase);
	}

	if (mpShownVideo)
		FreeVideo();

	mpShownVideo = mpLoadedVideo;
	mpLoadedVideo = NULL;
}

void TClientSA::SlowDownVideo()
{
	if (mpShownVideo && mpShownVideo->isVisible())
	{
		const std::string time = rmclientdrv::GetVideoRPCTimeAndPhase20(pRNGManager->GetCurrentRPC());
		int rpc_time = atoi(time.substr(0, time.find(';')).c_str());
		int elapsed_time = mpShownVideo->GetElapsedTime();
		int diff = rpc_time - elapsed_time;
		// pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Slow down video diff = %dms!", diff);

		if (diff <= 0)    // if video is ahead, pause it
			diff = abs(diff);
		else    // if video is behind, we have no way to sped it up yet
			diff = 0;

		if (diff > 0)
		{
			pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Pausing video for %dms!", diff);
			if (diff < 5000)    // dodaten check
				mpShownVideo->PauseFor(diff);
			else
				pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Video diff %dms is to big: [rpc: %dms, video: %dms]!", diff, rpc_time, elapsed_time);
		}
	}
}

void TClientSA::OnGameStart()
{
	auto RPC = pRNGManager->GetCurrentRPC();
	uint64_t game_id = rmclientdrv::GameID(RPC);
	int phase = rmclientdrv::GamePhase(RPC);

	if (phase < BETS_OPEN_STATE || phase >= BETS_ARE_CLOSING_STATE)
		return;

	int clientInGame = pDBMngr->ExecuteSync<TRouletteBaseDBSrv, int>(
	  [game_id](TRouletteBaseDBSrv* db, soci::session& sql) { return db->CountClientsInGame(pApp->ClientID(), game_id, true); }, 0, 0U, false, true,
	  { ETransactionFlags::ReadOnly });

	if (clientInGame == 0)
		return;

	int elapsed_time = mpShownVideo->GetElapsedTime();

	int time_left_ms = mpShownVideo->GetRemainingTime() - 5000;    // mCurrentLadyData.at(mCurrentVideoNum)->phase23_time[mCurrentVideoSequence] - elapsed_time;
	int seek_for_ms = round(((double)time_left_ms / (double)(clientInGame + 1)));

	if (seek_for_ms < 3000)
		return;

	pApp->GenerateAction("AfterGameStart", (void*)seek_for_ms);

	pApp->WriteLog(LT_CONSOLE_MESSAGE, NO_GAME_ID, "\t[CLIENT] Start button was pressed! Seek video for %dms", seek_for_ms);
	mpShownVideo->SeekTo(elapsed_time + seek_for_ms, false);
}

void TClientSA::ShowTimetable()
{
	if (mTimetablePanel && !mTimetablePanel->getEffectiveVisiblity(false) && mTimetablePanel->IsReady())
	{
		mTimetablePanel->fadeIn();
		mTimetablePanel->moveToTop();
	}
}

void TClientSA::HideTimetable()
{
	if (mTimetablePanel && mTimetablePanel->getEffectiveVisiblity(false))
		mTimetablePanel->fadeOut();
}