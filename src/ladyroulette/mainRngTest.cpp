
#include <dirent.h>
#include <math.h>
#include <stdlib.h>

#include <array>
#include <filesystem>
#include <fstream>
#include <iostream>
#include <list>
#include <string>
#include <vector>

#include "Cryptography.h"
#include "TRouletteRNG.h"
#include "YUtils.h"
#include "roulette/ThunderLogic.h"

enum EReturn
{
	RETURN_OK,
	RETURN_USAGE,
	RETURN_ERROR
};

std::string formatBalance(int64_t balance)
{
	bool isNeg = balance < 0;
	std::string bal = std::to_string(std::abs(balance));
	for (int i = (int)bal.length() - 3; i > 0; i -= 3) { bal.insert(i, "."); }
	std::string ret = (isNeg ? "-" : (balance == 0 ? "" : "+")) + bal;
	return ret;
}

int exitProgram(EReturn returnType)
{
	if (returnType == RETURN_USAGE)
	{
		printf("Usage:\n\trouletterng [options] <parameters>\n");
		printf("Options (can be combined like -do, -dp, ...):\n");
		printf("\t-h --help\t->\t Shows this output\n");
		printf("\t-d --double-zero\t->\t Runs the RNG with a double zero configuration (default off)\n");
		printf("\t-f --fbp\t->\t Big player protection (default 0, in range [0,100])\n");
		printf("\t-o --output\t->\t Specify an output file (default DATETIME_NGAMES.txt)\n");
		printf("\t-g --games\t->\t The number of games to play (default 1)\n");
		printf("\t-p --preset\t->\t Sets the preset with which the RNG should play (default -1[no preset], valid values up to and including 8)\n");
		printf("\t-0 --no-output\t->\t Does not write results to a file (default off)\n");
		printf("\t-s --sum\t->\t Writes results as the number of times each number fell (default off)\n");
		printf("\t-l --loss\t->\t Sets the starting negative balance of the machine (default 0, use +xxxx to set a positive balance)\n");
		printf("\t-a --average\t->\t Sets the lifetime average bet (default 100)\n");
		printf("\t-r --range\t->\t Sets the betting range of the player (default fixed at lifetime average bet, takes two parameters min and max)\n");
		printf("\t-t --thunder\t->\t Uses a Thunder algorithm. Argument should be the thunder config file.\n");
		printf("\t-T --thunder2\t->\t Uses a Thunder2 algorithm. Argument should be the thunder config file.\n");
		printf("\t-b --bet-style\t->\t Sets the betting style of the player (0-default: always bets on zero, n>0: distributes bets randomly across n numbers)\n");
		printf("Examples:\n");
		printf("\trouletterng -g 10000 -d -f 10\t\t->\tPlays 10000 games in double zero configuration with big player protection set to 10\n");
		printf("\trouletetrng -g 100 -p 1 --output file.txt\t->\tPlays 100 games with preset 1 and outputs results to file.txt\n");
		printf("\trouletetrng -g 100 -p 2 -r 1 50\t->\tPlays 100 games with preset 2 and bets distributed from 1 to 50\n");
	}
	else if (returnType == RETURN_ERROR)
	{
		printf("!!! An error occurred executing the program\n");
		return EXIT_FAILURE;
	}

	// printf("Encryption done!\n");
	return EXIT_SUCCESS;
}

int main(int argc, char* args[])
{
	setlocale(LC_ALL, "");
	uint64_t nGames = 1;
	int FBP = 0;
	bool isHelp = false, isDoubleZero = false, isNoOutput = false, isSum = false;
	std::string outputFileOverride;
	int thunderType = 0;
	int preset = -1;
	int average = 100;
	int rangeMin = -1;
	int rangeMax = -1;
	int64_t loss = 0;
	int betStyle = 0;
	std::filesystem::path thunderConfigFileLocation;
	// Load parameters into vector
	std::vector<std::string>* options = new std::vector<std::string>[argc];
	std::string* params = new std::string[argc];
	std::list<std::string> optionsRequireParam;
	for (int i = 1; i < argc; i++)
	{
		params[i].clear();
		const std::string argument = args[i];
		if (argument[0] == '-')
		{
			if (optionsRequireParam.size())
			{
				printf("Option %s requires a parameter!\n", optionsRequireParam.front().c_str());
				return exitProgram(RETURN_USAGE);
			}
			if (argument.length() < 2)
			{
				printf("Invalid empty option '-'\n");
				return exitProgram(RETURN_USAGE);
			}
			else
			{
				if (argument[1] == '-')
				{
					options[i].push_back(argument.substr(2, argument.length() - 2));
					if (options[i].back() == "games" || options[i].back() == "output" || options[i].back() == "fbp" || options[i].back() == "loss" ||
					    options[i].back() == "preset" || options[i].back() == "range" || options[i].back() == "average" || options[i].back() == "bet-style" ||
					    options[i].back() == "thunder" || options[i].back() == "thunder2")
					{
						optionsRequireParam.push_back(options[i].back());
						if (options[i].back() == "range")
							optionsRequireParam.push_back(options[i].back());
					}
				}
				else
				{
					std::string optionsInThisFlag = argument.substr(1);
					for (const char& a : optionsInThisFlag)
					{
						bool requireParam = false;
						switch (a)
						{
							case 'h': options[i].push_back("help"); break;
							case 'g':
								options[i].push_back("games");
								requireParam = true;
								break;
							case 'o':
								options[i].push_back("output");
								requireParam = true;
								break;
							case 'f':
								options[i].push_back("fbp");
								requireParam = true;
								break;
							case 'r':
								options[i].push_back("range");
								requireParam = true;
								optionsRequireParam.push_back(options[i].back());
								break;
							case 'p':
								options[i].push_back("preset");
								requireParam = true;
								break;
							case 'a':
								options[i].push_back("average");
								requireParam = true;
								break;
							case 'b':
								options[i].push_back("bet-style");
								requireParam = true;
								break;
							case 'd': options[i].push_back("double-zero"); break;
							case '0': options[i].push_back("no-output"); break;
							case 's': options[i].push_back("sum"); break;
							case 't':
								options[i].push_back("thunder");
								requireParam = true;
								break;
							case 'T':
								options[i].push_back("thunder2");
								requireParam = true;
								break;
							case 'l':
								options[i].push_back("loss");
								requireParam = true;
								break;
							default: printf("Unknown option flag %s!\n", std::string(&a, 1).c_str()); return exitProgram(RETURN_USAGE);
						}
						if (requireParam)
							optionsRequireParam.push_back(options[i].back());
					}
				}
			}
		}
		else if (argument.length())
		{
			params[i] = argument;
			optionsRequireParam.pop_front();
		}
	}

	if (optionsRequireParam.size())
	{
		printf("Option %s requires another parameter!\n", optionsRequireParam.front().c_str());
		return exitProgram(RETURN_USAGE);
	}

	for (int i = 1; i < argc; i++)
	{
		const std::vector<std::string>& optionVec = options[i];
		int paramI = i;
		for (const std::string& option : optionVec)
		{
			if (option == "help")
			{
				isHelp = true;
			}
			else if (option == "games")
			{
				nGames = std::stoul(params[++paramI]);
			}
			else if (option == "fbp")
			{
				FBP = std::stoi(params[++paramI]);
			}
			else if (option == "preset")
			{
				preset = std::stoi(params[++paramI]);
			}
			else if (option == "average")
			{
				average = std::stoi(params[++paramI]);
			}
			else if (option == "range")
			{
				rangeMin = std::stoi(params[++paramI]);
				rangeMax = std::stoi(params[++paramI]);
			}
			else if (option == "output")
			{
				outputFileOverride = params[++paramI];
				isNoOutput = false;
			}
			else if (option == "bet-style")
			{
				betStyle = std::stoi(params[++paramI]);
				isNoOutput = false;
			}
			else if (option == "double-zero")
			{
				isDoubleZero = true;
			}
			else if (option == "no-output")
			{
				isNoOutput = true;
			}
			else if (option == "sum")
			{
				isSum = true;
			}
			else if (option == "thunder")
			{
				thunderType = 1;
				thunderConfigFileLocation = params[++paramI];
			}
			else if (option == "thunder2")
			{
				thunderType = 2;
				thunderConfigFileLocation = params[++paramI];
			}
			else if (option == "loss")
			{
				std::string lossParam = params[++paramI];
				loss = (lossParam[0] == '+') ? -std::stol(lossParam.substr(1)) : std::stol(lossParam);
			}
			else
			{
				printf("Unknown option '%s'\n", option.c_str());
				return exitProgram(RETURN_USAGE);
			}
		}
	}


	if (isHelp)
		return exitProgram(RETURN_USAGE);

	std::unique_ptr<ThunderRNG> thunderRNG;
	if (thunderType)
	{
		std::ifstream thunderConf(thunderConfigFileLocation);
		if (!thunderConf.is_open())
		{
			printf("Could not open thunder configuration file at '%s'\n", thunderConfigFileLocation.c_str());
			return exitProgram(RETURN_ERROR);
		}

		Json::Reader r;
		json meep;
		if (!r.parse(thunderConf, meep))
		{
			printf("Could not parse thunder configuration file at '%s': %s\n", thunderConfigFileLocation.c_str(), r.getFormattedErrorMessages().c_str());
			return exitProgram(RETURN_ERROR);
		}

		try
		{
			switch (thunderType)
			{
				case 1: {
					FThunderConfig conf;
					meep = FThunderConfig::Schema().GenerateConfig(meep);
					conf.LoadFromJSON(meep);
					thunderRNG = std::make_unique<ThunderRNG1>(conf);
					break;
				}
				case 2: {
					FThunderConfig2_Setup conf;
					meep = FThunderConfig2_Setup::Schema().GenerateConfig(meep);
					conf.LoadFromJSON(meep);
					thunderRNG = std::make_unique<ThunderRNG2>(FThunderConfig2(conf));
					break;
				}
			}
		}
		catch (const std::exception& e)
		{
			printf("Malformed thunder configuration file at '%s': %s\n", thunderConfigFileLocation.c_str(), e.what());
			return exitProgram(RETURN_ERROR);
		}
	}

	if (rangeMin < 0)
	{
		rangeMin = rangeMax = average;
	}

	printf("============ PLAYING %lu GAME%s with preset %d and FBP %-2d ============\n", nGames, (nGames == 1) ? "" : "S", preset, FBP);
	printf(" 0%% [%-50s] 100%%\n     ", "");
	TRouletteRNG RNG;
	if (loss)
		RNG.SetCurrentProfit(-loss);
	RNG.SetDoubleZero(isDoubleZero);
	RNG.SetFixedLifetimeAverageBet(average);

	std::array<int64_t, 38> winValues;
	std::array<uint64_t, 38> numWin;
	numWin.fill(0);
	int lastProgress = 0;
	std::vector<uint8_t> accumulatedWinNums(nGames, 255);
	double thunderAvgRTP = 0.0;
	const int numbersTotal = isDoubleZero ? 38 : 37;
	for (uint64_t gameI = 0UL; gameI < nGames; gameI++)
	{
		winValues.fill(0);
		RNG.ClearWinValues();
		int64_t totalBet = 0;
		if (betStyle > 0)
		{
			int numBets = betStyle;
			for (int i = 0; i < numBets; i++)
			{
				int64_t bet = crypto::GetRandomInRange(rangeMin, rangeMax) / numBets;
				winValues[crypto::GetRandomInRange(0, numbersTotal - 1)] = bet;
				totalBet += bet;
			}
		}
		else
		{
			totalBet = crypto::GetRandomInRange(rangeMin, rangeMax);
			winValues[0] = totalBet;
		}

		RNG.RegisterTotalBet(totalBet);
		if (thunderRNG)
		{
			std::list<uint16_t> rns;
			thunderRNG->GenerateMultipliers(rns);
			thunderRNG->AssignMultipliersToNumbers(rns);
			// printf("Thunder nums: %s\tBoard multiplier: %u\n", yutils::PrintMap(thunderRNG->SelectedNumbers).c_str(), thunderRNG->CurrentRound.BaseBoardMultiplier);

			for (uint8_t num = 0; num < 38; num++)
			{
				auto find = thunderRNG->SelectedNumbers.find(num);
				const uint16_t multiplier = (find == thunderRNG->SelectedNumbers.end()) ? thunderRNG->CurrentRound.BaseBoardMultiplier : find->second;
				winValues[num] *= multiplier;
			}

			thunderAvgRTP += thunderRNG->CurrentRound.GetRTP(numbersTotal);
		}
		else
		{
			for (int64_t& winVal : winValues) winVal *= 36;
		}

		RNG.RegisterWinValues(winValues.data(), true);

		int winNumber = RNG.CalculateNextWinNumber(preset, FBP);
		numWin[winNumber]++;
		RNG.GameCompleted(winNumber);

		accumulatedWinNums[gameI] = winNumber;

		for (; lastProgress < ((int)(((gameI + 1) * 50) / nGames)); lastProgress++)
		{
			printf("*");
			fflush(stdout);
		}
	}

	printf("\n================= COMPLETED %lu GAMES =================\n", nGames);
	printf("NUMBER | No. of wins | Relative freq. | Abs. & rel. deviation | Relative sigma deviation\n");
	const double normalFreq = 100. / numbersTotal;
	const double normalNumberOfTimesNumberFalls = double(nGames) / numbersTotal;
	const double sigma = std::sqrt(nGames * (1. - 1. / numbersTotal) / numbersTotal);
	for (int i = 0; i < numbersTotal; i++)
	{
		const double freq = (numWin[i] * 100.) / nGames;
		const double sigmaDeviation = (numWin[i] - normalNumberOfTimesNumberFalls) / sigma;
		printf("%-6s | %-11lu | %-12f %% | %s%.4f%%  &  %s%.4f%% | %s%f\n", i == 37 ? "00" : std::to_string(i).c_str(), numWin[i], freq,
		       (freq > normalFreq) ? "+" : (freq == normalFreq ? "=" : "-"), std::abs(freq - normalFreq), (freq > normalFreq) ? "+" : (freq == normalFreq ? "=" : "-"),
		       std::abs((freq - normalFreq) / normalFreq), (sigmaDeviation > 0.) ? "+" : (sigmaDeviation == 0. ? "=" : "-"), std::abs(sigmaDeviation));
	}
	printf("====================================================\n");
	printf("\tNormal # hits [sigma]: %f [%f]\n", normalNumberOfTimesNumberFalls, sigma);
	printf("\tMachine balance (starting): %s\t(%s)\n", formatBalance(RNG.Balance()).c_str(), formatBalance(-loss).c_str());
	printf("\tMachine profit: %s\n", formatBalance(RNG.Balance() + loss).c_str());
	printf("\tRTP (excl. starting balance): %f%%\n",
	       ((loss < 0) ? (100. * RNG.TotalWin() / (RNG.TotalBet() + loss)) : (100. * (RNG.TotalWin() - loss) / RNG.TotalBet())));
	printf("\tRTP (incl. starting balance): %f%%\n", 100. * RNG.TotalWin() / RNG.TotalBet());
	if (thunderRNG)
		printf("\tTheoretical RTP: %f%%\n", 100. * thunderAvgRTP / nGames);
	printf("====================================================\n");

	if (!isNoOutput)
	{
		if (outputFileOverride.empty())
		{
			time_t curr_time = time(NULL);
			tm curr_tm;
			localtime_r(&curr_time, &curr_tm);
			char timestring[32];
			strftime(timestring, 32, "%F_%T", &curr_tm);
			outputFileOverride = timestring;
			outputFileOverride += "_" + std::to_string(nGames) + ".txt";
		}

		std::ofstream outFile(outputFileOverride);

		if (outFile.is_open())
		{
			if (isSum)
			{
				outFile << "No. of games: " << nGames << std::endl;
				outFile << "Mode: " << (isDoubleZero ? "Double zero" : "Single zero") << std::endl;
				outFile << "Preset: " << ((preset < 0) ? "none" : std::to_string(preset)) << std::endl;
				outFile << "FBP : " << FBP << std::endl;

				outFile << std::endl;

				for (int i = 0; i < numbersTotal; i++) { outFile << numWin[i] << std::endl; }
			}
			else
			{
				for (const uint8_t& wonNum : accumulatedWinNums) outFile << std::to_string(int(wonNum)) << std::endl;
			}
		}
	}

	return exitProgram(EReturn::RETURN_OK);
}
