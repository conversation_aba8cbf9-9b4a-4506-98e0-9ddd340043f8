#include "ServerHubUtils.h"

#include <oneapi/tbb/parallel_for.h>

#include "Cryptography.h"

std::string serverhub::BinToUTF8(const std::string& data)
{
	std::string encoded;
	encoded.resize(data.size() + (data.size() + 6) / 7);
	const uint8_t* data_u8 = reinterpret_cast<const uint8_t*>(data.data());
	tbb::parallel_for(0UL, data.size(), 7UL, [&encoded, &data, data_u8](size_t block_idx) {
		uint8_t& leftover = reinterpret_cast<uint8_t&>(encoded[data.size() + block_idx / 7]);
		for (size_t idx = block_idx; idx < std::min(block_idx + 7, data.size()); idx++)
		{
			encoded[idx] = data_u8[idx] >> 1;
			leftover = (leftover << 1) | (data_u8[idx] & 1);
		}
	});
	return encoded;
}

std::string serverhub::UTF8ToBin(const std::string& data)
{
	std::string decoded;
	decoded.resize(data.size() * 7 / 8);
	const uint8_t* data_u8 = reinterpret_cast<const uint8_t*>(data.data());
	tbb::parallel_for(0UL, decoded.size(), 7UL, [&decoded, data_u8](size_t block_idx) {
		uint8_t leftover = data_u8[decoded.size() + block_idx / 7];
		for (size_t idx = std::min(block_idx + 7, decoded.size()) - 1; idx >= block_idx; idx--)
		{
			decoded[idx] = (data_u8[idx] << 1) | (leftover & 1);
			leftover = leftover >> 1;
			if (!idx)
				break;
		}
	});
	return decoded;
}

std::string serverhub::HashSubNodeName(const std::string& name)
{
	return crypto::GenRandID(8, name.c_str(), name.length());
}
