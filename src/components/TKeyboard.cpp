#include "components/TKeyboard.h"

#include <cmath>
#include <rapidxml.hpp>

#include "MyUtils.h"
#include "TGuiApplication.h"
#include "YUtils.h"
#include "gui/guiexception.hpp"

#define MOVE_IN_ANIM_DURATION 0.7
#define MOVE_IN_CURVE         10.0

TKeyboard::TKeyboard(Container* pParent, float x, float y, float w, float h) : Container(pParent, x, y, w, h)
{
	setId("keyboard");
	setFocusMode(EFocusMode::Ignore);
	setVisible(false);

	mPenguinTexture = Image::GetImage("LinuxPenguin.png");

	mPenguinDisabledTexture = mPenguinTexture->copy();
	GPU_SetRGBA(mPenguinDisabledTexture->getTexture(), 255, 255, 255, (Uint8)128);

	OutlineStyle.BoxShadow = Shadow { .color = Black, .blur = 15_px };

	// Parse Xml of paytable layout
	rapidxml::xml_document<> doc;
	rapidxml::xml_node<>* root_node;

	// Read the xml file into a vector
	std::ifstream theFile;
	theFile.open((pGuiApp->ImageDir() / "KeyboardLayout.xml").c_str(), std::ifstream::in);
	if (!theFile.is_open())
		throw GuiException("WARNING: Could not find keyboard layout XML!");
	std::vector<char> buffer;
	yutils::FileToVector(theFile, buffer);
	buffer.push_back('\0');

	// Parse the buffer using the xml file parsing library into doc
	doc.parse<0>(&buffer[0]);
	theFile.close();

	// Find our root node
	root_node = doc.first_node("keyboard");
	if (!root_node)
		throw GuiException("WARNING: Could not read keyboard layout XML!");

	// Load the default properties of the keyboard
	int baseWidth = atoi(root_node->first_attribute("buttonWidth")->value());
	int baseHeight = atoi(root_node->first_attribute("buttonHeight")->value());
	spacingV = atoi(root_node->first_attribute("verticalSpacing")->value());
	spacingH = atoi(root_node->first_attribute("horizontalSpacing")->value());
	int fontLarge = atoi(root_node->first_attribute("fontLarge")->value());
	int fontSmall = atoi(root_node->first_attribute("fontSmall")->value());
	mMargin = atoi(root_node->first_attribute("margin")->value());

	BoldKeyTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = { (float)fontLarge, EDimensionUnit::px } });
	RegularKeyTypography = Typography({ .fontFamily = { { "Noto Sans" } }, .fontSize = { (float)fontSmall, EDimensionUnit::px } });

	// Iterate over the rows
	float rowY = mMargin;
	int rowI = 0;
	float keyboardWidth = 0;
	for (rapidxml::xml_node<>* key_row = root_node->first_node("row"); key_row; key_row = key_row->next_sibling("row"))
	{
		rapidxml::xml_attribute<>* rowH = key_row->first_attribute("height");
		float height = rowH ? atoi(rowH->value()) : baseHeight;
		float keyX = 2 * mMargin;
		rowButtons[rowI] = 0;
		// Iterate over all the keys of this row
		for (rapidxml::xml_node<>* btn_node = key_row->first_node("btn"); btn_node; btn_node = btn_node->next_sibling("btn"))
		{
			const std::string name(btn_node->first_attribute("name")->value());
			const std::string symbol(btn_node->first_attribute("symbol")->value());
			rapidxml::xml_attribute<>*shiftSym, *altSym, *alignOverride, *widthOverride, *letterAtt, *font, *color;
			shiftSym = btn_node->first_attribute("shiftSymbol");
			altSym = btn_node->first_attribute("altSymbol");
			alignOverride = btn_node->first_attribute("align");
			widthOverride = btn_node->first_attribute("width");
			letterAtt = btn_node->first_attribute("letter");
			font = btn_node->first_attribute("font");
			color = btn_node->first_attribute("color");

			float width = widthOverride ? atoi(widthOverride->value()) : baseWidth;
			unsigned int align = alignOverride ? ((std::string(alignOverride->value()) == "right") ? 1 : -1) : 0;
			const std::string symbolShift(shiftSym ? shiftSym->value() : "");
			const std::string symbolAlt(altSym ? altSym->value() : "");
			const bool isLetter = letterAtt ? (std::string(letterAtt->value()) == "true" ? true : false) : false;
			const std::string fontName(font ? font->value() : "");
			const std::string colorName(color ? color->value() : "");
			bool disabled = false;
			if (btn_node->first_attribute("disabled"))
				disabled = true;
			// Create a key from the loaded button attributes
			TKeyboardButton* btn = new TKeyboardButton(this, symbol, keyX, rowY, width, height, disabled, "KeyboardBtn" + name);
			btn->Keycode = keycodeFromKeyName(name, KMOD_NONE);
			if (!SelectedButton && btn->Keycode.code() == SDLK_RETURN)
				SelectedButton = btn;
			if (shiftSym)
			{
				btn->setShiftSymbol(symbolShift);
				btn->ShiftKeycode = keycodeFromKeyName(name, KMOD_SHIFT);
			}
			if (altSym)
			{
				btn->setAltSymbol(symbolAlt);
				btn->AltKeycode = keycodeFromKeyName(name, KMOD_ALT);
			}
			btn->setAlignment(align);
			if (fontName == "small")
				btn->setOverrideTypography(RegularKeyTypography);
			if (!colorName.empty())
			{
				std::vector<std::string> colorValues = yutils::Split(colorName, ",", true);
				Color textColor = { (Uint8)atoi(colorValues[0].c_str()), (Uint8)atoi(colorValues[1].c_str()), (Uint8)atoi(colorValues[2].c_str()), (Uint8)1 };
				btn->setOverrideFontColor(textColor);
			}

			btn->setIsLetter(isLetter);
			/*if (name == "DownArrow")
			    btn->setOverrideImage(TKeyboard::mArrowTexture, 0.2f, 0.f, TKeyboard::mArrowDisabledTexture);
			else if (name == "UpArrow")
			    btn->setOverrideImage(TKeyboard::mArrowTexture, 0.2f, 180.f, TKeyboard::mArrowDisabledTexture);
			else if (name == "LeftArrow")
			    btn->setOverrideImage(TKeyboard::mArrowTexture, 0.2f, 90.f, TKeyboard::mArrowDisabledTexture);
			else if (name == "RightArrow")
			    btn->setOverrideImage(TKeyboard::mArrowTexture, 0.2f, -90.f, TKeyboard::mArrowDisabledTexture);
			else */
			if (name == "Windows")
				btn->setOverrideImage(mPenguinTexture, 0.8f, mPenguinDisabledTexture);

			rowButtons[rowI]++;
			keyX += width + spacingH;
		}

		rowStart[rowI] = rowY;
		rowSizes[rowI] = height;

		rowI++;
		rowY += height + spacingV;

		keyboardWidth = std::max(keyboardWidth, keyX);
	}

	setVisible(false);
	Background = Color::Gray(55);
	setY(getParent()->getHeight());
	if (TerminalFlavour == EFlavour::EBX)
	{
		setHeight(rowY + baseHeight + mMargin - spacingV);
	}
	else
	{
		setHeight(rowY + mMargin - spacingV);
	}
	mLowerBounds = rowY - spacingV;

	moveButton = new TKeyboardButton(this, "DARROWD", getWidth() - (int)std::round(baseWidth * 1.4f) - mMargin, mMargin, (int)std::round(baseWidth * 1.4f),
	                                 (int)std::round(baseHeight * 1.4f), false, "KeyboardMoveBtn");
	moveButton->setOverrideImage(Image::GetImage("DoubleArrowDown.png"), 0.75f);
	moveButton->setOverrideImageFlip(SDL_FLIP_VERTICAL);
	moveButton->setRenderScale(0.6f);

	moveButton->OnKeyPress = [this](bool bPressed) {
		if (bPressed)
			return;

		switchTopBottom(false);
	};

	closeButton = new TKeyboardButton(this, "EXIT", getWidth() - (int)std::round(baseWidth * 1.4f) - mMargin, mLowerBounds - (int)std::round(baseHeight * 1.4f),
	                                  (int)std::round(baseWidth * 1.4f), (int)std::round(baseHeight * 1.4f), false, "KeyboardCloseBtn");
	closeButton->OnKeyPress = [this](bool bPressed) {
		if (bPressed)
			return;
		ToggleShown();
	};
	closeButton->setOverrideImage(Image::GetImage("CrossExitIcon.png"), 0.75f);
	closeButton->setRenderScale(0.6f);

	int consoleBtnWidth = std::min(200.f, closeButton->getX() - keyboardWidth);
	consoleButton = new TKeyboardButton(this, "Console", keyboardWidth + (closeButton->getX() - keyboardWidth) / 2.f - (consoleBtnWidth / 2.f), mLowerBounds - baseHeight,
	                                    consoleBtnWidth, baseHeight, false, "KeyboardConsoleBtn");
	consoleButton->OnKeyPress = [](bool bPressed) {
		if (!bPressed)
			return;

		pApp->GenerateAction("ToggleConsole");
	};
	consoleButton->setOverrideTypography(RegularKeyTypography);

	isAnimating = 0;
	animStart = 0U;
	animReverse = false;
	animRanges = { getY(), getY() - getHeight() };
	bIsAtBottom = true;
	isAnimatingMoveButton = false;

	updateChildrenPlacement();
}

EKeyboardState TKeyboard::getState() const
{
	const float screenH = getParent() ? getParent()->getHeight() : 0.f;
	if (!isVisible())
	{
		return EKeyboardState::HIDDEN;
	}
	else
	{
		if (!isAnimating)
			return EKeyboardState::SHOWN;

		if (animRanges.Y() < animRanges.X())
		{    // moving up
			if (animRanges.Y() == 0)
				return (animRanges.X() == (screenH - getHeight())) ? EKeyboardState::MOVING_TO_OPPOSITE_SIDE : EKeyboardState::MOVING_INTO_PLACE;
			if (animRanges.Y() == -getHeight())
				return EKeyboardState::MOVING_OFF_SCREEN;
			if (animRanges.Y() == screenH - getHeight())
				return EKeyboardState::MOVING_INTO_PLACE;
		}
		else
		{    // moving down
			if (animRanges.Y() == screenH)
				return EKeyboardState::MOVING_OFF_SCREEN;
			if (animRanges.Y() == screenH - getHeight())
				return (animRanges.X() == 0) ? EKeyboardState::MOVING_TO_OPPOSITE_SIDE : EKeyboardState::MOVING_INTO_PLACE;
			if (animRanges.Y() == 0)
				return EKeyboardState::MOVING_INTO_PLACE;
		}
	}
	return EKeyboardState::UNDEFINED_STATE;
}

void TKeyboard::ToggleShown()
{
	const float screenH = getParent()->getHeight();

	Uint64 now = SDL_GetTicks64();
	if (isVisible())
	{
		if (!isAnimating)
		{
			animRanges = { getY(), bIsAtBottom ? screenH : (-getHeight()) };
			isAnimating = true;
			animStart = now;
		}
		else
		{
			Vector2D newRange;
			if (bIsAtBottom && (animRanges.Y() < animRanges.X()))    // Supposed to be at the bottom and is moving up, close it then
				newRange = Vector2D(screenH - getHeight(), screenH);
			else if (bIsAtBottom)    // Supposed to be at bottom and is moving down, open it then, or close it if it is only first arriving to this position
				if (animRanges.Y() == screenH)
					newRange = Vector2D(screenH, screenH - getHeight());
				else
					newRange = Vector2D(0, screenH);
			else if (animRanges.Y() <
			         animRanges.X())    // Supposed to be at the top and is moving up, open it then, or close it if it is only first arriving to this position
				if (animRanges.Y() == 0)
					newRange = Vector2D(screenH - getHeight(), -getHeight());
				else
					newRange = Vector2D(-getHeight(), 0);
			else    // Supposed to be up and is moving down (opening from top), close it then
				newRange = Vector2D(0, -getHeight());
			double timeOfNewAnim = timeOfAnimAtCurrentPosition(newRange);
			animStart = now - (int)std::round(timeOfNewAnim * 1e3);
			animRanges = newRange;
		}
	}
	else
	{
		Widget* focused = Gui::Get()->getFocused();
		if (focused && !containsWidget(focused, true))
		{
			const Rectangle rect = focused->getAbsoluteDimension();
			const bool bHasSpaceAtTop = rect.top() >= getHeight();
			const bool bHasSpaceAtBottom = (screenH - rect.bottom()) > getHeight();
			if (bIsAtBottom && !bHasSpaceAtBottom && bHasSpaceAtTop)
				switchTopBottom(true);    // start at bottom if there is not enough space up top
			else if (!bIsAtBottom && bHasSpaceAtBottom && !bHasSpaceAtTop)
				switchTopBottom(true);    // start at top if there is not enough space down below
		}

		setVisible(true);
		moveToTop();
		isAnimating = true;
		if (bIsAtBottom)
			animRanges = Vector2D(screenH, screenH - getHeight());
		else
			animRanges = Vector2D(-getHeight(), 0);
		animStart = now;
	}
}

void TKeyboard::Show()
{
	const EKeyboardState state = getState();
	if (state == EKeyboardState::HIDDEN || state == EKeyboardState::MOVING_OFF_SCREEN)
		ToggleShown();
}

void TKeyboard::Hide()
{
	const EKeyboardState state = getState();
	if (!(state == EKeyboardState::HIDDEN || state == EKeyboardState::MOVING_OFF_SCREEN))
		ToggleShown();
}

void TKeyboard::SetInputType(EInputType input)
{
	if (mModsBeforeInputMode)
	{
		modifiers = *mModsBeforeInputMode;
		mModsBeforeInputMode.reset();
	}

	switch (input)
	{
		case EInputType::HEX: {
			for (const auto& child : children())
			{
				TKeyboardButton* key = dynamic_cast<TKeyboardButton*>(child.Child);
				if (!key || key->getSymbol() == "DARROWD" || key->getSymbol() == "EXIT" || key->getSymbol() == "CONSOLE")
					continue;

				if (!(key->Keycode.isNumber() || (key->Keycode.code() >= SDLK_a && key->Keycode.code() <= SDLK_f)))
				{
					if (key->getId() != "KeyboardBtnLeftArrow" && key->getId() != "KeyboardBtnRightArrow" && key->getId() != "KeyboardBtnBackspace" &&
					    key->getId() != "KeyboardBtnTab" && key->getId() != "KeyboardBtnReturn")
					{
						key->setEnabled(false);
					}
				}
			}
			mModsBeforeInputMode = modifiers;
			modifiers = KMOD_CAPS;
			break;
		}
		case EInputType::INTEGER:
		case EInputType::FLOAT: {
			for (const auto& child : children())
			{
				TKeyboardButton* key = dynamic_cast<TKeyboardButton*>(child.Child);
				if (!key || key->getSymbol() == "DARROWD" || key->getSymbol() == "EXIT" || key->getSymbol() == "CONSOLE")
					continue;

				if (!(key->Keycode.isNumber() || key->Keycode.code() == SDLK_MINUS || (input == EInputType::FLOAT && key->Keycode.code() == SDLK_PERIOD)))
				{
					if (key->getId() != "KeyboardBtnLeftArrow" && key->getId() != "KeyboardBtnRightArrow" && key->getId() != "KeyboardBtnBackspace" &&
					    key->getId() != "KeyboardBtnTab" && key->getId() != "KeyboardBtnReturn")
					{
						key->setEnabled(false);
					}
				}
			}
			mModsBeforeInputMode = modifiers;
			modifiers = KMOD_NONE;
			break;
		}
		default: {
			for (const auto& child : children())
			{
				TKeyboardButton* key = dynamic_cast<TKeyboardButton*>(child.Child);
				if (key)
					key->setEnabled(true);
			}
			break;
		}
	}

	findButtonByName("KeyboardBtnCapsLock")->setKeyPressed(modifiers & KMOD_CAPS);
	findButtonByName("KeyboardBtnLeftShift")->setKeyPressed(modifiers & KMOD_LSHIFT);
	findButtonByName("KeyboardBtnRightShift")->setKeyPressed(modifiers & KMOD_RSHIFT);
	findButtonByName("KeyboardBtnAlt")->setKeyPressed(modifiers & KMOD_LALT);
	findButtonByName("KeyboardBtnAltGr")->setKeyPressed(modifiers & KMOD_RALT);
	findButtonByName("KeyboardBtnLeftCtrl")->setKeyPressed(modifiers & KMOD_LCTRL);
	findButtonByName("KeyboardBtnRightCtrl")->setKeyPressed(modifiers & KMOD_RCTRL);
	findButtonByName("KeyboardBtnWindows")->setKeyPressed(modifiers & KMOD_LGUI);
	findButtonByName("KeyboardBtnFn")->setKeyPressed(modifiers & KMOD_MODE);
}

bool TKeyboard::DoAction_Implementation(const HardwareButtonEvent& ev)
{
	switch (ev.Action)
	{
		case EHardwareButtonAction::BetMinus: pGuiApp->PostSDLKeyboardEvent(ev.bPressed, SDLK_BACKSPACE, (SDL_Keymod)getModifiers()); return true;
		case EHardwareButtonAction::Payout:
		case EHardwareButtonAction::GameSelect:
			if (ev.bPressed)
			{
				SelectedButton =
				  childWalk(SelectedButton, ev.Action == EHardwareButtonAction::GameSelect, [](Widget* w) -> bool { return dynamic_cast<TKeyboardButton*>(w); });
			}
			return true;
		case EHardwareButtonAction::StartGame: {
			if (TKeyboardButton* btn = dynamic_cast<TKeyboardButton*>(SelectedButton))
				btn->setKeyPressed(ev.bPressed, true);
		}
			return true;
		case EHardwareButtonAction::Autoplay: Hide(); return true;
		default: break;
	}

	return false;
}

void TKeyboard::GetAvailableActions_Implementation(HardwareButtonInformation& outAvailableActions) const
{
	outAvailableActions.SetAvailable(EHardwareButtonAction::BetMinus, BACKSPACE_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::Payout, PREV_ELEMENT_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::GameSelect, NEXT_ELEMENT_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::StartGame, PRESS_SELECTED_KEY_STRING);
	outAvailableActions.SetAvailable(EHardwareButtonAction::Autoplay, EXIT_STRING);
}

void TKeyboard::SetSelectedButton(Widget* btn)
{
	if (SelectedButton == btn)
		return;

	if (SelectedButton)
		SelectedButton->Background.ClearModifiers();

	SelectedButton = btn;

	if (SelectedButton)
		SelectedButton->Background.AddModifier([](Brush& b) { b = b.brighten(1.3f); });
}

unsigned int TKeyboard::getModifiers() const
{
	return modifiers;
}

uint32_t* TKeyboard::getModifiersPtr()
{
	return &modifiers;
}

void TKeyboard::drawLogic(Graphics* graphics, float deltaTime)
{
	if (isAnimating)
	{
		bool bHasLeftScreen = false;
		double elapsed = (SDL_GetTicks64() - animStart) * 1e-3;
		double A;
		if (elapsed >= MOVE_IN_ANIM_DURATION)
			A = 1.;
		else
			A = std::log(MOVE_IN_CURVE * elapsed + 1.) / std::log(MOVE_IN_CURVE * MOVE_IN_ANIM_DURATION + 1.);

		setY((1. - A) * animRanges.X() + A * animRanges.Y());
		if (elapsed >= MOVE_IN_ANIM_DURATION)
		{
			isAnimating = false;
			bHasLeftScreen = (getY() >= getParent()->getHeight()) || (getY() <= -getHeight());
		}

		if (bHasLeftScreen)
			setVisible(false);
	}

	if (isAnimatingMoveButton)
	{
		double elapsed = (SDL_GetTicks64() - moveAnimStart) * 1e-3;
		double A;
		if (elapsed >= MOVE_IN_ANIM_DURATION)
			A = 1.;
		else
			A = std::log(MOVE_IN_CURVE * elapsed + 1.) / std::log(MOVE_IN_CURVE * MOVE_IN_ANIM_DURATION + 1.);

		if (bIsAtBottom)
			A = 1. - A;
		float pos = (1. - A) * mMargin + A * (mLowerBounds - moveButton->getHeight());
		moveButton->setY(pos);
		pos = A * mMargin + (1. - A) * (mLowerBounds - moveButton->getHeight());
		closeButton->setY(pos);
		pos = 4 * (0.25 - std::pow(A - 0.5, 2.)) * (closeButton->getWidth() + mMargin);
		closeButton->setX(getWidth() - closeButton->getWidth() - mMargin - pos);
		if (elapsed >= MOVE_IN_ANIM_DURATION)
			isAnimatingMoveButton = false;
	}

	Container::drawLogic(graphics, deltaTime);
}

void TKeyboard::keyPressed(KeyEvent& keyEvent)
{
	keyEvent.consume();
}

void TKeyboard::keyReleased(KeyEvent& keyEvent)
{
	keyEvent.consume();
}

double TKeyboard::timeOfAnimAtCurrentPosition(const Vector2D& newRange)
{
	double retTime = (SDL_GetTicks64() - animStart) * 1e-3;
	retTime = std::pow(MOVE_IN_CURVE * MOVE_IN_ANIM_DURATION + 1., ((double)animRanges.Y() - newRange.Y()) / (newRange.X() - newRange.Y())) *
	          std::pow(MOVE_IN_CURVE * retTime + 1., ((double)animRanges.X() - animRanges.Y()) / (newRange.X() - newRange.Y()));
	return (retTime - 1.) / MOVE_IN_CURVE;
}

SDL_Keycode TKeyboard::keycodeFromKeyName(const std::string& keyName, unsigned int modifiers)
{
	SDL_Keycode key = SDLK_UNKNOWN;
	if (modifiers & KMOD_SHIFT)
	{
		if (keyName == "Digit1")
		{
			key = SDLK_EXCLAIM;
		}
		else if (keyName == "Digit2")
		{
			key = SDLK_QUOTEDBL;
		}
		else if (keyName == "Digit3")
		{
			key = 248;
		}
		else if (keyName == "Digit4")
		{
			key = SDLK_DOLLAR;
		}
		else if (keyName == "Digit5")
		{
			key = SDLK_PERCENT;
		}
		else if (keyName == "Digit6")
		{
			key = SDLK_CARET;
		}
		else if (keyName == "Digit7")
		{
			key = SDLK_AMPERSAND;
		}
		else if (keyName == "Digit8")
		{
			key = SDLK_ASTERISK;
		}
		else if (keyName == "Digit9")
		{
			key = SDLK_LEFTPAREN;
		}
		else if (keyName == "Digit0")
		{
			key = SDLK_RIGHTPAREN;
		}
		else if (keyName == "Hyphen")
		{
			key = SDLK_UNDERSCORE;
		}
		else if (keyName == "Plus")
		{
			key = SDLK_EQUALS;
		}
		else if (keyName == "Colon")
		{
			key = SDLK_COLON;
		}
		else if (keyName == "Apostrophe")
		{
			key = SDLK_AT;
		}
		else if (keyName == "Hash")
		{
			key = '~';
		}
		else if (keyName == "BackSlash")
		{
			key = SDLK_SEPARATOR;
		}
		else if (keyName == "Comma")
		{
			key = SDLK_LESS;
		}
		else if (keyName == "Period")
		{
			key = SDLK_GREATER;
		}
		else if (keyName == "Slash")
		{
			key = SDLK_QUESTION;
		}
	}
	else if (modifiers == KMOD_NONE)
	{
		if (keyName == "Escape")
		{
			key = SDLK_ESCAPE;
		}
		else if (keyName == "F1")
		{
			key = SDLK_F1;
		}
		else if (keyName == "F2")
		{
			key = SDLK_F2;
		}
		else if (keyName == "F3")
		{
			key = SDLK_F3;
		}
		else if (keyName == "F4")
		{
			key = SDLK_F4;
		}
		else if (keyName == "F5")
		{
			key = SDLK_F5;
		}
		else if (keyName == "F6")
		{
			key = SDLK_F6;
		}
		else if (keyName == "F7")
		{
			key = SDLK_F7;
		}
		else if (keyName == "F8")
		{
			key = SDLK_F8;
		}
		else if (keyName == "F9")
		{
			key = SDLK_F9;
		}
		else if (keyName == "F10")
		{
			key = SDLK_F10;
		}
		else if (keyName == "F11")
		{
			key = SDLK_F11;
		}
		else if (keyName == "F12")
		{
			key = SDLK_F12;
		}
		else if (keyName == "PrintScreen")
		{
			key = SDLK_PRINTSCREEN;
		}
		else if (keyName == "Delete")
		{
			key = SDLK_DELETE;
		}
		else if (keyName == "Insert")
		{
			key = SDLK_INSERT;
		}
		else if (keyName == "BackQuote")
		{
			key = SDLK_BACKQUOTE;
		}
		else if (keyName == "Digit1")
		{
			key = SDLK_1;
		}
		else if (keyName == "Digit2")
		{
			key = SDLK_2;
		}
		else if (keyName == "Digit3")
		{
			key = SDLK_3;
		}
		else if (keyName == "Digit4")
		{
			key = SDLK_4;
		}
		else if (keyName == "Digit5")
		{
			key = SDLK_5;
		}
		else if (keyName == "Digit6")
		{
			key = SDLK_6;
		}
		else if (keyName == "Digit7")
		{
			key = SDLK_7;
		}
		else if (keyName == "Digit8")
		{
			key = SDLK_8;
		}
		else if (keyName == "Digit9")
		{
			key = SDLK_9;
		}
		else if (keyName == "Digit0")
		{
			key = SDLK_0;
		}
		else if (keyName == "Hyphen")
		{
			key = SDLK_MINUS;
		}
		else if (keyName == "Plus")
		{
			key = SDLK_PLUS;
		}
		else if (keyName == "Backspace")
		{
			key = SDLK_BACKSPACE;
		}
		else if (keyName == "Home")
		{
			key = SDLK_HOME;
		}
		else if (keyName == "Tab")
		{
			key = SDLK_TAB;
		}
		else if (keyName == "Q")
		{
			key = SDLK_q;
		}
		else if (keyName == "W")
		{
			key = SDLK_w;
		}
		else if (keyName == "E")
		{
			key = SDLK_e;
		}
		else if (keyName == "R")
		{
			key = SDLK_r;
		}
		else if (keyName == "T")
		{
			key = SDLK_t;
		}
		else if (keyName == "Y")
		{
			key = SDLK_y;
		}
		else if (keyName == "U")
		{
			key = SDLK_u;
		}
		else if (keyName == "I")
		{
			key = SDLK_i;
		}
		else if (keyName == "O")
		{
			key = SDLK_o;
		}
		else if (keyName == "P")
		{
			key = SDLK_p;
		}
		else if (keyName == "BracketLeft")
		{
			key = SDLK_LEFTBRACKET;
		}
		else if (keyName == "BracketRight")
		{
			key = SDLK_RIGHTBRACKET;
		}
		else if (keyName == "Return")
		{
			key = SDLK_RETURN;
		}
		else if (keyName == "PageUp")
		{
			key = SDLK_PAGEUP;
		}
		else if (keyName == "CapsLock")
		{
			key = SDLK_CAPSLOCK;
		}
		else if (keyName == "A")
		{
			key = SDLK_a;
		}
		else if (keyName == "S")
		{
			key = SDLK_s;
		}
		else if (keyName == "D")
		{
			key = SDLK_d;
		}
		else if (keyName == "F")
		{
			key = SDLK_f;
		}
		else if (keyName == "G")
		{
			key = SDLK_g;
		}
		else if (keyName == "H")
		{
			key = SDLK_h;
		}
		else if (keyName == "J")
		{
			key = SDLK_j;
		}
		else if (keyName == "K")
		{
			key = SDLK_k;
		}
		else if (keyName == "L")
		{
			key = SDLK_l;
		}
		else if (keyName == "Colon")
		{
			key = SDLK_SEMICOLON;
		}
		else if (keyName == "Apostrophe")
		{
			key = SDLK_QUOTE;
		}
		else if (keyName == "Hash")
		{
			key = SDLK_HASH;
		}
		else if (keyName == "PageDown")
		{
			key = SDLK_PAGEDOWN;
		}
		else if (keyName == "LeftShift")
		{
			key = SDLK_LSHIFT;
		}
		else if (keyName == "BackSlash")
		{
			key = SDLK_BACKSLASH;
		}
		else if (keyName == "Z")
		{
			key = SDLK_z;
		}
		else if (keyName == "X")
		{
			key = SDLK_x;
		}
		else if (keyName == "C")
		{
			key = SDLK_c;
		}
		else if (keyName == "V")
		{
			key = SDLK_v;
		}
		else if (keyName == "B")
		{
			key = SDLK_b;
		}
		else if (keyName == "N")
		{
			key = SDLK_n;
		}
		else if (keyName == "M")
		{
			key = SDLK_m;
		}
		else if (keyName == "Comma")
		{
			key = SDLK_COMMA;
		}
		else if (keyName == "Period")
		{
			key = SDLK_PERIOD;
		}
		else if (keyName == "Slash")
		{
			key = SDLK_SLASH;
		}
		else if (keyName == "RightShift")
		{
			key = SDLK_RSHIFT;
		}
		else if (keyName == "UpArrow")
		{
			key = SDLK_PAGEDOWN;
		}
		else if (keyName == "End")
		{
			key = SDLK_END;
		}
		else if (keyName == "LeftCtrl")
		{
			key = SDLK_LCTRL;
		}
		else if (keyName == "Windows")
		{
			key = SDLK_LGUI;
		}
		else if (keyName == "Alt")
		{
			key = SDLK_LALT;
		}
		else if (keyName == "Space")
		{
			key = SDLK_SPACE;
		}
		else if (keyName == "AltGr")
		{
			key = SDLK_RALT;
		}
		else if (keyName == "Fn")
		{
			key = SDLK_MODE;
		}
		else if (keyName == "RightCtrl")
		{
			key = SDLK_RCTRL;
		}
		else if (keyName == "LeftArrow")
		{
			key = SDLK_LEFT;
		}
		else if (keyName == "DownArrow")
		{
			key = SDLK_DOWN;
		}
		else if (keyName == "RightArrow")
		{
			key = SDLK_RIGHT;
		}
	}
	return key;
}

bool TKeyboard::hasModalMouseInputFocus() const
{
	return getEffectiveVisiblity();
}

bool TKeyboard::hasModalFocus() const
{
	return getEffectiveVisiblity();
}

void TKeyboard::switchTopBottom(bool bInstant)
{
	bIsAtBottom = !bIsAtBottom;
	moveButton->setOverrideImageFlip(bIsAtBottom ? SDL_FLIP_VERTICAL : SDL_FLIP_NONE);
	if (getEffectiveVisiblity() && !bInstant)
	{
		if (isAnimating)
		{
			Vector2D newRange;
			if (bIsAtBottom)
				newRange = Vector2D(0, getParent()->getHeight() - getHeight());
			else
				newRange = Vector2D(getParent()->getHeight() - getHeight(), 0);
			double timeOfNewAnim = timeOfAnimAtCurrentPosition(newRange);
			animStart = SDL_GetTicks64() - (int)std::round(timeOfNewAnim * 1e3);
			animRanges = newRange;
		}
		else
		{
			animRanges = { getY(), bIsAtBottom ? ((int)getParent()->getHeight() - getHeight()) : 0 };
			isAnimating = true;
			animStart = SDL_GetTicks64();
		}
		moveAnimStart = SDL_GetTicks64();
	}
	else
	{
		moveAnimStart = 0;
		setY(bIsAtBottom ? getParent()->getHeight() : -getHeight());
	}
	isAnimatingMoveButton = true;
}
