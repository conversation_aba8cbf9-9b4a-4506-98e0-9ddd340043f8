#include <gtest/gtest.h>

#include <fstream>

#include "Cryptography.h"
#include "TApplication.h"
#include "roulette/ThunderLogic.h"

class ThunderConfigRTPTest : public ::testing::Test
{
   public:
	void SetUp() override { Setup.LoadFromJSON(json::parse(std::ifstream(PROJECT_ROOT + std::string("/test/platform/fireballConfig.json")))); }
	void TearDown() override {}

	roulette::FThunderConfig2_Setup Setup;
};

TEST_F(ThunderConfigRTPTest, AverageRTP)
{
	// GIVEN
	roulette::FThunderConfig2 config(Setup);

	// WHEN
	const auto setupRTP = Setup.RTPLimits;
	const auto configRTP = config.GetRTPLimits();

	TLOG(LogApp, Critical, "SETUP Min = %.2f, Max = %.2f, Average = %.2f", setupRTP.Min, setupRTP.Max, setupRTP.Average);
	EXPECT_EQ(setupRTP.Average, 0.0);
	TLOG(LogApp, Critical, "CONFIG Min = %.2f, Max = %.2f, Average = %.2f", configRTP.Min, configRTP.Max, configRTP.Average);
	EXPECT_TRUE(configRTP.Average <= setupRTP.Max && configRTP.Average >= setupRTP.Min);
	EXPECT_TRUE(configRTP.Average <= configRTP.Max && configRTP.Average >= configRTP.Min);
}
