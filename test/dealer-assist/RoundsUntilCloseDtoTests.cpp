//
// Unit tests for RoundsUntilCloseDto
//

#include <gtest/gtest.h>

#include "dealer-assist/dto/RoundsUntilCloseDto.h"

using namespace dealer_assist;

class RoundsUntilCloseDtoTest : public ::testing::Test
{
   protected:
    // Test data constants
    static constexpr uint32_t DEFAULT_ROUNDS_REMAINING = 5;
    static constexpr uint32_t DEFAULT_MAX_ROUNDS = 10;
    
    static constexpr uint32_t ZERO_ROUNDS_REMAINING = 0;
    static constexpr uint32_t ZERO_MAX_ROUNDS = 0;
};

// Basic Constructor Tests
TEST_F(RoundsUntilCloseDtoTest, Constructor_WithValidParameters)
{
    // GIVEN & WHEN
    RoundsUntilCloseDto dto(DEFAULT_ROUNDS_REMAINING, DEFAULT_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), DEFAULT_ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), DEFAULT_MAX_ROUNDS);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose);
}

TEST_F(RoundsUntilCloseDtoTest, Constructor_WithZeroValues)
{
    // GIVEN & WHEN
    RoundsUntilCloseDto dto(ZERO_ROUNDS_REMAINING, ZERO_MAX_ROUNDS, ERoundsCountdownType::GameChange);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), ZERO_ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), ZERO_MAX_ROUNDS);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::GameChange);
}

TEST_F(RoundsUntilCloseDtoTest, Constructor_WithMaxValues)
{
    // GIVEN
    constexpr uint32_t MAX_UINT32 = std::numeric_limits<uint32_t>::max();

    // WHEN
    RoundsUntilCloseDto dto(MAX_UINT32, MAX_UINT32, ERoundsCountdownType::TableClose);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), MAX_UINT32);
    EXPECT_EQ(dto.GetMaxRounds(), MAX_UINT32);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose);
}

// Getter Tests
TEST_F(RoundsUntilCloseDtoTest, GetRoundsRemaining_ReturnsCorrectValue)
{
    // GIVEN
    constexpr uint32_t EXPECTED_ROUNDS = 3;
    RoundsUntilCloseDto dto(EXPECTED_ROUNDS, DEFAULT_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // WHEN
    uint32_t result = dto.GetRoundsRemaining();

    // THEN
    EXPECT_EQ(result, EXPECTED_ROUNDS);
}

TEST_F(RoundsUntilCloseDtoTest, GetMaxRounds_ReturnsCorrectValue)
{
    // GIVEN
    constexpr uint32_t EXPECTED_MAX_ROUNDS = 15;
    RoundsUntilCloseDto dto(DEFAULT_ROUNDS_REMAINING, EXPECTED_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // WHEN
    uint32_t result = dto.GetMaxRounds();

    // THEN
    EXPECT_EQ(result, EXPECTED_MAX_ROUNDS);
}

// JSON Serialization Tests
TEST_F(RoundsUntilCloseDtoTest, ToJSON_WithValidData)
{
    // GIVEN
    RoundsUntilCloseDto dto(DEFAULT_ROUNDS_REMAINING, DEFAULT_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // WHEN
    json result = dto.ToJSON();

    // THEN
    EXPECT_EQ(result["roundsRemaining"].get<uint32_t>(), DEFAULT_ROUNDS_REMAINING);
    EXPECT_EQ(result["maxRounds"].get<uint32_t>(), DEFAULT_MAX_ROUNDS);
}

TEST_F(RoundsUntilCloseDtoTest, ToJSON_WithZeroValues)
{
    // GIVEN
    RoundsUntilCloseDto dto(ZERO_ROUNDS_REMAINING, ZERO_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // WHEN
    json result = dto.ToJSON();

    // THEN
    EXPECT_EQ(result["roundsRemaining"].get<uint32_t>(), ZERO_ROUNDS_REMAINING);
    EXPECT_EQ(result["maxRounds"].get<uint32_t>(), ZERO_MAX_ROUNDS);
}

TEST_F(RoundsUntilCloseDtoTest, ToJSON_ContainsAllRequiredFields)
{
    // GIVEN
    RoundsUntilCloseDto dto(DEFAULT_ROUNDS_REMAINING, DEFAULT_MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // WHEN
    json result = dto.ToJSON();

    // THEN
    EXPECT_TRUE(result.contains("roundsRemaining"));
    EXPECT_TRUE(result.contains("maxRounds"));
    EXPECT_TRUE(result.contains("countdownType"));
    EXPECT_EQ(result.size(), 3); // Ensure all required fields are present
}

// JSON Deserialization Tests
TEST_F(RoundsUntilCloseDtoTest, FromJSON_WithValidJSON)
{
    // GIVEN
    json inputJson;
    inputJson["roundsRemaining"] = DEFAULT_ROUNDS_REMAINING;
    inputJson["maxRounds"] = DEFAULT_MAX_ROUNDS;

    // WHEN
    RoundsUntilCloseDto dto = RoundsUntilCloseDto::FromJSON(inputJson);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), DEFAULT_ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), DEFAULT_MAX_ROUNDS);
}

TEST_F(RoundsUntilCloseDtoTest, FromJSON_WithZeroValues)
{
    // GIVEN
    json inputJson;
    inputJson["roundsRemaining"] = ZERO_ROUNDS_REMAINING;
    inputJson["maxRounds"] = ZERO_MAX_ROUNDS;

    // WHEN
    RoundsUntilCloseDto dto = RoundsUntilCloseDto::FromJSON(inputJson);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), ZERO_ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), ZERO_MAX_ROUNDS);
}

// Round-trip Serialization Tests
TEST_F(RoundsUntilCloseDtoTest, ToJSONAndBack_PreservesAllData)
{
    // GIVEN
    RoundsUntilCloseDto originalDto(DEFAULT_ROUNDS_REMAINING, DEFAULT_MAX_ROUNDS, ERoundsCountdownType::GameChange);

    // WHEN
    json jsonData = originalDto.ToJSON();
    RoundsUntilCloseDto reconstructedDto = RoundsUntilCloseDto::FromJSON(jsonData);

    // THEN
    EXPECT_EQ(originalDto.GetRoundsRemaining(), reconstructedDto.GetRoundsRemaining());
    EXPECT_EQ(originalDto.GetMaxRounds(), reconstructedDto.GetMaxRounds());
    EXPECT_EQ(originalDto.GetCountdownType(), reconstructedDto.GetCountdownType());
}

TEST_F(RoundsUntilCloseDtoTest, ToJSONAndBack_WithEdgeCaseValues)
{
    // GIVEN
    constexpr uint32_t LARGE_VALUE = 999999;
    RoundsUntilCloseDto originalDto(LARGE_VALUE, LARGE_VALUE, ERoundsCountdownType::GameChange);

    // WHEN
    json jsonData = originalDto.ToJSON();
    RoundsUntilCloseDto reconstructedDto = RoundsUntilCloseDto::FromJSON(jsonData);

    // THEN
    EXPECT_EQ(originalDto.GetRoundsRemaining(), reconstructedDto.GetRoundsRemaining());
    EXPECT_EQ(originalDto.GetMaxRounds(), reconstructedDto.GetMaxRounds());
}

// Edge Case Tests
TEST_F(RoundsUntilCloseDtoTest, RoundsRemaining_GreaterThanMaxRounds)
{
    // GIVEN
    constexpr uint32_t ROUNDS_REMAINING = 15;
    constexpr uint32_t MAX_ROUNDS = 10;

    // WHEN
    RoundsUntilCloseDto dto(ROUNDS_REMAINING, MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // THEN - Should allow this scenario (business logic validation is elsewhere)
    EXPECT_EQ(dto.GetRoundsRemaining(), ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), MAX_ROUNDS);
}

// Business Logic Scenario Tests
TEST_F(RoundsUntilCloseDtoTest, TypicalGameScenario_TableClosingActive)
{
    // GIVEN - Table is closing, 3 rounds remaining out of 5 max
    constexpr uint32_t ROUNDS_REMAINING = 3;
    constexpr uint32_t MAX_ROUNDS = 5;

    // WHEN
    RoundsUntilCloseDto dto(ROUNDS_REMAINING, MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), MAX_ROUNDS);
}

TEST_F(RoundsUntilCloseDtoTest, TypicalGameScenario_TableClosingInactive)
{
    // GIVEN - Table closing not requested, showing max rounds available
    constexpr uint32_t MAX_ROUNDS = 5;

    // WHEN
    RoundsUntilCloseDto dto(MAX_ROUNDS, MAX_ROUNDS, ERoundsCountdownType::TableClose);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), MAX_ROUNDS);
    EXPECT_EQ(dto.GetMaxRounds(), MAX_ROUNDS);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose);
}

// New Tests for Countdown Type Functionality
TEST_F(RoundsUntilCloseDtoTest, CountdownType_TableClose)
{
    // GIVEN & WHEN
    RoundsUntilCloseDto dto(3, 5, ERoundsCountdownType::TableClose);

    // THEN
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose);
}

TEST_F(RoundsUntilCloseDtoTest, CountdownType_GameChange)
{
    // GIVEN & WHEN
    RoundsUntilCloseDto dto(2, 4, ERoundsCountdownType::GameChange);

    // THEN
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::GameChange);
}

TEST_F(RoundsUntilCloseDtoTest, ToJSON_IncludesCountdownType)
{
    // GIVEN
    RoundsUntilCloseDto dto(3, 5, ERoundsCountdownType::GameChange);

    // WHEN
    json result = dto.ToJSON();

    // THEN
    EXPECT_EQ(result["roundsRemaining"].get<uint32_t>(), 3);
    EXPECT_EQ(result["maxRounds"].get<uint32_t>(), 5);
    EXPECT_EQ(result["countdownType"].get<std::string>(), "GameChange");
}

TEST_F(RoundsUntilCloseDtoTest, FromJSON_WithCountdownType)
{
    // GIVEN
    json inputJson;
    inputJson["roundsRemaining"] = 2;
    inputJson["maxRounds"] = 4;
    inputJson["countdownType"] = "TableClose";

    // WHEN
    RoundsUntilCloseDto dto = RoundsUntilCloseDto::FromJSON(inputJson);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), 2);
    EXPECT_EQ(dto.GetMaxRounds(), 4);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose);
}

TEST_F(RoundsUntilCloseDtoTest, FromJSON_WithoutCountdownType_DefaultsToTableClose)
{
    // GIVEN - JSON without countdownType field (backward compatibility)
    json inputJson;
    inputJson["roundsRemaining"] = 1;
    inputJson["maxRounds"] = 3;

    // WHEN
    RoundsUntilCloseDto dto = RoundsUntilCloseDto::FromJSON(inputJson);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), 1);
    EXPECT_EQ(dto.GetMaxRounds(), 3);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::TableClose); // Default value
}

TEST_F(RoundsUntilCloseDtoTest, GameChangeScenario_Active)
{
    // GIVEN - Game change countdown active
    constexpr uint32_t ROUNDS_REMAINING = 2;
    constexpr uint32_t MAX_ROUNDS = 3;

    // WHEN
    RoundsUntilCloseDto dto(ROUNDS_REMAINING, MAX_ROUNDS, ERoundsCountdownType::GameChange);

    // THEN
    EXPECT_EQ(dto.GetRoundsRemaining(), ROUNDS_REMAINING);
    EXPECT_EQ(dto.GetMaxRounds(), MAX_ROUNDS);
    EXPECT_EQ(dto.GetCountdownType(), ERoundsCountdownType::GameChange);
}
