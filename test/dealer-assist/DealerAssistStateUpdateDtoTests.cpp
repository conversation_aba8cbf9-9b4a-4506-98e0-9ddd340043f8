//
// Created by <PERSON><PERSON><PERSON> on 12. 02. 24.
//

#include <gtest/gtest.h>

#include "dealer-assist/dto/DealerAssistInitResponseDto.h"
#include "dealer-assist/dto/DealerAssistStateUpdateDto.h"

using namespace dealer_assist;

TEST(DealerAssistStateUpdateDtoTest, DefaultConstructor)
{
	// GIVEN
	DealerAssistStateUpdateDto dto;

	// THEN
	EXPECT_EQ(dto.GetPhase(), EDealerAssistPhase::WaitingForPlayers);
	EXPECT_EQ(dto.GetRoundId(), 0);
}

TEST(DealerAssistStateUpdateDtoTest, ParameterizedConstructor)
{
	// GIVEN
	DealerAssistStateUpdateDto dto(1, 2, 3);

	// THEN
	EXPECT_EQ(dto.GetPhase(), 1);
	EXPECT_EQ(dto.GetRoundId(), 2);
	EXPECT_EQ(dto.GetDealingPhase(), 3);
}

TEST(DealerAssistStateUpdateDtoTest, ToJSON)
{
	// GIVEN
	DealerAssistStateUpdateDto dto(1, 2, 3);

	// WHEN
	json val = dto.ToJSON();

	// THEN
	EXPECT_EQ(val["phase"].get<uint32_t>(), 1);
	EXPECT_EQ(val["gameRound"].get<uint32_t>(), 2);
	EXPECT_EQ(val["dealingPhase"].get<uint32_t>(), 3);
}

TEST(DealerAssistStateUpdateDtoTest, FromJSON)
{
	// GIVEN
	json val(json::value_t::object);
	val["phase"] = 1;
	val["dealingPhase"] = 3;
	val["gameRound"] = 2;

	// WHEN
	DealerAssistStateUpdateDto dto = DealerAssistStateUpdateDto::FromJSON(val);

	// THEN
	EXPECT_EQ(dto.GetPhase(), 1);
	EXPECT_EQ(dto.GetRoundId(), 2);
	EXPECT_EQ(dto.GetDealingPhase(), 3);
}

TEST(DealerAssistStateUpdateDtoTest, ToJSONAndBack)
{
	// GIVEN
	DealerAssistStateUpdateDto dto(1, 2, 3);

	// WHEN
	json val = dto.ToJSON();
	DealerAssistStateUpdateDto result = DealerAssistStateUpdateDto::FromJSON(val);

	// THEN
	EXPECT_EQ(dto.GetPhase(), result.GetPhase());
	EXPECT_EQ(dto.GetRoundId(), result.GetRoundId());
	EXPECT_EQ(dto.GetDealingPhase(), result.GetDealingPhase());
}