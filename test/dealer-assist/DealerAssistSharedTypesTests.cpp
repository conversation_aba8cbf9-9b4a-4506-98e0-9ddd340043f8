//
// Created by <PERSON><PERSON><PERSON> on 12. 01. 24.
//

#include <gtest/gtest.h>

#include "dealer-assist/DealerAssistSharedTypes.h"

// using namespace yserver::gamehost::baccarat;
using namespace dealer_assist;

TEST(FScannedCard, HeartsSuite)
{
	// GIVEN
	auto suite = ECardSuite::Hearts;
	FScannedCard aceCard = FScannedCard("1H2");
	FScannedCard kingCard = FScannedCard("KH1");
	FScannedCard queenCard = FScannedCard("QH4");
	FScannedCard jackCard = FScannedCard("JH3");
	FScannedCard fourCard = FScannedCard("4H5");
	FScannedCard sevenCard = FScannedCard("7H5");
	FScannedCard tenCard = FScannedCard("0H4");

 	// THEN
 	EXPECT_EQ(aceCard.Barcode(), "1H2");
 	EXPECT_EQ(aceCard.Face(), ECardFace::Ace);
 	EXPECT_EQ(aceCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(aceCard.Deck(), 2);
 	EXPECT_EQ(aceCard.ToInt(), 2301);

 	EXPECT_EQ(kingCard.Barcode(), "KH1");
 	EXPECT_EQ(kingCard.Face(), ECardFace::King);
 	EXPECT_EQ(kingCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(kingCard.Deck(), 1);
 	EXPECT_EQ(kingCard.ToInt(), 1313);

 	EXPECT_EQ(queenCard.Barcode(), "QH4");
 	EXPECT_EQ(queenCard.Face(), ECardFace::Queen);
 	EXPECT_EQ(queenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(queenCard.Deck(), 4);
 	EXPECT_EQ(queenCard.ToInt(), 4312);

 	EXPECT_EQ(jackCard.Barcode(), "JH3");
 	EXPECT_EQ(jackCard.Face(), ECardFace::Jack);
 	EXPECT_EQ(jackCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(jackCard.Deck(), 3);
 	EXPECT_EQ(jackCard.ToInt(), 3311);

 	EXPECT_EQ(fourCard.Barcode(), "4H5");
 	EXPECT_EQ(fourCard.Face(), ECardFace::Four);
 	EXPECT_EQ(fourCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(fourCard.Deck(), 5);
 	EXPECT_EQ(fourCard.ToInt(), 5304);

 	EXPECT_EQ(sevenCard.Barcode(), "7H5");
 	EXPECT_EQ(sevenCard.Face(), ECardFace::Seven);
 	EXPECT_EQ(sevenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(sevenCard.Deck(), 5);
 	EXPECT_EQ(sevenCard.ToInt(), 5307);

 	EXPECT_EQ(tenCard.Barcode(), "0H4");
 	EXPECT_EQ(tenCard.Face(), ECardFace::Ten);
 	EXPECT_EQ(tenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(tenCard.Deck(), 4);
 	EXPECT_EQ(tenCard.ToInt(), 4310);
 }

 TEST(FScannedCard, ClubsSuite)
 {
 	// GIVEN
 	auto suite = ECardSuite::Clubs;
 	FScannedCard aceCard = FScannedCard("1C8");
 	FScannedCard jackCard = FScannedCard("JC1");
 	FScannedCard queenCard = FScannedCard("QC7");
 	FScannedCard twoCard = FScannedCard("2C2");
 	FScannedCard threeCard = FScannedCard("3C1");
 	FScannedCard fourCard = FScannedCard("4C1");

 	// THEN
 	EXPECT_EQ(aceCard.Barcode(), "1C8");
 	EXPECT_EQ(aceCard.Face(), ECardFace::Ace);
 	EXPECT_EQ(aceCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(aceCard.Deck(), 8);
 	EXPECT_EQ(aceCard.ToInt(), 8101);

 	EXPECT_EQ(jackCard.Barcode(), "JC1");
 	EXPECT_EQ(jackCard.Face(), ECardFace::Jack);
 	EXPECT_EQ(jackCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(jackCard.Deck(), 1);
 	EXPECT_EQ(jackCard.ToInt(), 1111);

 	EXPECT_EQ(queenCard.Barcode(), "QC7");
 	EXPECT_EQ(queenCard.Face(), ECardFace::Queen);
 	EXPECT_EQ(queenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(queenCard.Deck(), 7);
 	EXPECT_EQ(queenCard.ToInt(), 7112);

 	EXPECT_EQ(twoCard.Barcode(), "2C2");
 	EXPECT_EQ(twoCard.Face(), ECardFace::Two);
 	EXPECT_EQ(twoCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(twoCard.Deck(), 2);
 	EXPECT_EQ(twoCard.ToInt(), 2102);

 	EXPECT_EQ(threeCard.Barcode(), "3C1");
 	EXPECT_EQ(threeCard.Face(), ECardFace::Three);
 	EXPECT_EQ(threeCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(threeCard.Deck(), 1);
 	EXPECT_EQ(threeCard.ToInt(), 1103);

 	EXPECT_EQ(fourCard.Barcode(), "4C1");
 	EXPECT_EQ(fourCard.Face(), ECardFace::Four);
 	EXPECT_EQ(fourCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(fourCard.Deck(), 1);
 	EXPECT_EQ(fourCard.ToInt(), 1104);
 }

 TEST(FScannedCard, SpadesSuite)
 {
 	// GIVEN
 	auto suite = ECardSuite::Spades;
 	FScannedCard aceCard = FScannedCard("1S3");
 	FScannedCard queenCard = FScannedCard("QS3");
 	FScannedCard kingCard = FScannedCard("KS7");
 	FScannedCard twoCard = FScannedCard("2S7");
 	FScannedCard threeCard = FScannedCard("3S2");
 	FScannedCard fourCard = FScannedCard("4S6");
 	FScannedCard sixCard = FScannedCard("6S3");
 	FScannedCard sevenCard = FScannedCard("7S7");
 	FScannedCard eightCard = FScannedCard("8S4");

 	// THEN
 	EXPECT_EQ(aceCard.Barcode(), "1S3");
 	EXPECT_EQ(aceCard.Face(), ECardFace::Ace);
 	EXPECT_EQ(aceCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(aceCard.Deck(), 3);
 	EXPECT_EQ(aceCard.ToInt(), 3401);

 	EXPECT_EQ(queenCard.Barcode(), "QS3");
 	EXPECT_EQ(queenCard.Face(), ECardFace::Queen);
 	EXPECT_EQ(queenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(queenCard.Deck(), 3);
 	EXPECT_EQ(queenCard.ToInt(), 3412);

 	EXPECT_EQ(kingCard.Barcode(), "KS7");
 	EXPECT_EQ(kingCard.Face(), ECardFace::King);
 	EXPECT_EQ(kingCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(kingCard.Deck(), 7);
 	EXPECT_EQ(kingCard.ToInt(), 7413);

 	EXPECT_EQ(twoCard.Barcode(), "2S7");
 	EXPECT_EQ(twoCard.Face(), ECardFace::Two);
 	EXPECT_EQ(twoCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(twoCard.Deck(), 7);
 	EXPECT_EQ(twoCard.ToInt(), 7402);

 	EXPECT_EQ(threeCard.Barcode(), "3S2");
 	EXPECT_EQ(threeCard.Face(), ECardFace::Three);
 	EXPECT_EQ(threeCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(threeCard.Deck(), 2);
 	EXPECT_EQ(threeCard.ToInt(), 2403);

 	EXPECT_EQ(fourCard.Barcode(), "4S6");
 	EXPECT_EQ(fourCard.Face(), ECardFace::Four);
 	EXPECT_EQ(fourCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(fourCard.Deck(), 6);
 	EXPECT_EQ(fourCard.ToInt(), 6404);

 	EXPECT_EQ(sixCard.Barcode(), "6S3");
 	EXPECT_EQ(sixCard.Face(), ECardFace::Six);
 	EXPECT_EQ(sixCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(sixCard.Deck(), 3);
 	EXPECT_EQ(sixCard.ToInt(), 3406);

 	EXPECT_EQ(sevenCard.Barcode(), "7S7");
 	EXPECT_EQ(sevenCard.Face(), ECardFace::Seven);
 	EXPECT_EQ(sevenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(sevenCard.Deck(), 7);
 	EXPECT_EQ(sevenCard.ToInt(), 7407);

 	EXPECT_EQ(eightCard.Barcode(), "8S4");
 	EXPECT_EQ(eightCard.Face(), ECardFace::Eight);
 	EXPECT_EQ(eightCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(eightCard.Deck(), 4);
 	EXPECT_EQ(eightCard.ToInt(), 4408);
 }

 TEST(FScannedCard, DiamondsSuite)
 {
 	// GIVEN
 	auto suite = ECardSuite::Diamonds;
 	FScannedCard threeCard = FScannedCard("3D8");
 	FScannedCard fourCard = FScannedCard("4D7");
 	FScannedCard eightCard = FScannedCard("8D1");
 	FScannedCard nineCard = FScannedCard("9D4");
 	FScannedCard tenCard = FScannedCard("0D1");
 	FScannedCard jackCard = FScannedCard("JD2");
 	FScannedCard kingCard = FScannedCard("KD3");
 	FScannedCard aceCard = FScannedCard("1D2");

 	// THEN
 	EXPECT_EQ(threeCard.Barcode(), "3D8");
 	EXPECT_EQ(threeCard.Face(), ECardFace::Three);
 	EXPECT_EQ(threeCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(threeCard.Deck(), 8);
 	EXPECT_EQ(threeCard.ToInt(), 8203);

 	EXPECT_EQ(fourCard.Barcode(), "4D7");
 	EXPECT_EQ(fourCard.Face(), ECardFace::Four);
 	EXPECT_EQ(fourCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(fourCard.Deck(), 7);
 	EXPECT_EQ(fourCard.ToInt(), 7204);

 	EXPECT_EQ(eightCard.Barcode(), "8D1");
 	EXPECT_EQ(eightCard.Face(), ECardFace::Eight);
 	EXPECT_EQ(eightCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(eightCard.Deck(), 1);
 	EXPECT_EQ(eightCard.ToInt(), 1208);

 	EXPECT_EQ(nineCard.Barcode(), "9D4");
 	EXPECT_EQ(nineCard.Face(), ECardFace::Nine);
 	EXPECT_EQ(nineCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(nineCard.Deck(), 4);
 	EXPECT_EQ(nineCard.ToInt(), 4209);

 	EXPECT_EQ(tenCard.Barcode(), "0D1");
 	EXPECT_EQ(tenCard.Face(), ECardFace::Ten);
 	EXPECT_EQ(tenCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(tenCard.Deck(), 1);
 	EXPECT_EQ(tenCard.ToInt(), 1210);

 	EXPECT_EQ(jackCard.Barcode(), "JD2");
 	EXPECT_EQ(jackCard.Face(), ECardFace::Jack);
 	EXPECT_EQ(jackCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(jackCard.Deck(), 2);
 	EXPECT_EQ(jackCard.ToInt(), 2211);

 	EXPECT_EQ(kingCard.Barcode(), "KD3");
 	EXPECT_EQ(kingCard.Face(), ECardFace::King);
 	EXPECT_EQ(kingCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(kingCard.Deck(), 3);
 	EXPECT_EQ(kingCard.ToInt(), 3213);

 	EXPECT_EQ(aceCard.Barcode(), "1D2");
 	EXPECT_EQ(aceCard.Face(), ECardFace::Ace);
 	EXPECT_EQ(aceCard.Suite()._to_index(), ECardSuite(suite)._to_index());
 	EXPECT_EQ(aceCard.Deck(), 2);
 	EXPECT_EQ(aceCard.ToInt(), 2201);
 }