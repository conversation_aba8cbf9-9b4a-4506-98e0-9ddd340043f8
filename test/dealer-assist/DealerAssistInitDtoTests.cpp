//
// Created by <PERSON><PERSON><PERSON> on 11. 03. 24.
//

#include <gtest/gtest.h>

#include "dealer-assist/dto/DealerAssistInitResponseDto.h"

using namespace dealer_assist;

TEST(DealerAssistInitResponseDtoTest, DefaultConstructor)
{
	// GIVEN
	DealerAssistInitResponseDto dto;

	// THEN
	EXPECT_EQ(dto.mBetTimeInSec, 0);
	EXPECT_EQ(dto.mDecisionTimeInSec, 0);
	EXPECT_EQ(dto.mAnimationTimeInSec, 0);
	EXPECT_EQ(dto.mTimeInRound, 0);
	EXPECT_EQ(dto.mPlayerCount, 0);
}

TEST(DealerAssistInitResponseDtoTest, ToJSON)
{
	// GIVEN
	DealerAssistInitResponseDto dto;
	dto.mBetTimeInSec = 15000;
	dto.mDecisionTimeInSec = 5000;
	dto.mAnimationTimeInSec = 20;
	dto.mPlayerCount = 10;
	dto.mTableId = "table123";

	// WHEN
	json val = dto.ToJSON();

	// THEN
	EXPECT_EQ(val["configuration"]["bettingWindowInSeconds"].get<uint32_t>(), 15000);
	EXPECT_EQ(val["configuration"]["decisionTimeInSeconds"].get<uint32_t>(), 5000);
	EXPECT_EQ(val["configuration"]["animationTimeInSeconds"].get<uint32_t>(), 20);
	EXPECT_EQ(val["playerCount"].get<uint32_t>(), 10);
	EXPECT_EQ(val["configuration"]["tableId"].get<std::string>(), "table123");
}

TEST(DealerAssistInitResponseDtoTest, FromJSON)
{
	// GIVEN
	json val(json::value_t::object);
	val["playerCount"] = 10;
	val["configuration"] = json::object();
	val["configuration"]["bettingWindowInSeconds"] = 15000;
	val["configuration"]["decisionTimeInSeconds"] = 5000;
	val["configuration"]["animationTimeInSeconds"] = 20;
	val["configuration"]["tableId"] = "table123";
	val["configuration"]["tableMode"] = "LandBase";
	val["configuration"]["videoStreamUrl"] = "http://test.com";
	val["configuration"]["videoStreamId"] = "stream123";
	val["configuration"]["videoStreamType"] = "WebRTC";
	val["configuration"]["appVersion"] = "1.0.0";
	val["configuration"]["numOfDecks"] = 8;
	val["chatMessages"] = json::array();
	val["tableState"] = json::object();
	val["roundState"] = json::object();

	// WHEN
	const DealerAssistInitResponseDto dto = DealerAssistInitResponseDto::FromJSON(val);

	// THEN
	EXPECT_EQ(dto.mPlayerCount, 10);
	EXPECT_EQ(dto.mBetTimeInSec, 15000);
	EXPECT_EQ(dto.mDecisionTimeInSec, 5000);
	EXPECT_EQ(dto.mAnimationTimeInSec, 20);
	EXPECT_EQ(dto.mTableId, "table123");
}

TEST(DealerAssistInitResponseDtoTest, ToJSONAndBack)
{
	// GIVEN
	DealerAssistInitResponseDto dto;
	dto.mBetTimeInSec = 15000;
	dto.mDecisionTimeInSec = 5000;
	dto.mAnimationTimeInSec = 20;
	dto.mPlayerCount = 10;
	dto.mTableId = "table123";
	dto.mTableMode = ETableMode::LandBase;
	dto.mStreamUrl = "http://test.com";
	dto.mStreamId = "stream123";
	dto.mStreamType = EStreamType::WebRTC;
	dto.mAppVersion = version::Version::FromString("1.0.0");
	dto.mNumOfDecks = 8;

	// WHEN
	json val = dto.ToJSON();
	DealerAssistInitResponseDto result = DealerAssistInitResponseDto::FromJSON(val);

	// THEN
	EXPECT_EQ(dto.mBetTimeInSec, result.mBetTimeInSec);
	EXPECT_EQ(dto.mDecisionTimeInSec, result.mDecisionTimeInSec);
	EXPECT_EQ(dto.mAnimationTimeInSec, result.mAnimationTimeInSec);
	EXPECT_EQ(dto.mPlayerCount, result.mPlayerCount);
	EXPECT_EQ(dto.mTableId, result.mTableId);
}