//
// Created by <PERSON><PERSON><PERSON> on 11. 03. 24.
//

#include <gtest/gtest.h>

#include "dealer-assist/dto/DealerAssistInitResponseDto.h"

using namespace dealer_assist;

TEST(DealerAssistInitResponseDtoTest, DefaultConstructor)
{
	// GIVEN
	DealerAssistInitResponseDto dto;

	// THEN
	EXPECT_EQ(dto.mBetTimeInSec, 0);
	EXPECT_EQ(dto.mDecisionTimeInSec, 0);
	EXPECT_EQ(dto.mAnimationTimeInSec, 0);
	EXPECT_EQ(dto.mTimeInRound, 0);
	EXPECT_EQ(dto.mPlayerCount, 0);
}

 TEST(DealerAssistInitResponseDtoTest, ToJSON)
 {
 	// GIVEN
 	DealerAssistInitResponseDto dto(1, 2, 15000, 5000, 20);

 	// WHEN
 	json val = dto.ToJSON();

 	// THEN
 	EXPECT_EQ(val["betTime"].get<uint32_t>(), 15000);
 	EXPECT_EQ(val["pauseAfterRound"].get<uint32_t>(), 5000);
 	EXPECT_EQ(val["gameId"].get<uint32_t>(), 1);
 	EXPECT_EQ(val["phase"].get<uint32_t>(), 2);
 	EXPECT_EQ(val["timeInRound"].get<uint32_t>(), 20);
 }

 TEST(DealerAssistInitResponseDtoTest, FromJSON)
 {
 	// GIVEN
 	json val(json::value_t::object);
 	val["betTime"] = 15000;
 	val["pauseAfterRound"] = 5000;
 	val["gameId"] = 1;
 	val["phase"] = 2;
 	val["timeInRound"] = 20;

 	// WHEN
 	DealerAssistInitResponseDto dto = DealerAssistInitResponseDto::FromJSON(val);

 	// THEN
 	EXPECT_EQ(dto.GetTableState().mGameId, 1);
 	EXPECT_EQ(dto.GetTableState().mPhase, 2);
 	EXPECT_EQ(dto.GetGameSettings().mBetTime, 15000);
 	EXPECT_EQ(dto.GetGameSettings().mPauseAfterRound, 5000);
 	EXPECT_EQ(dto.GetGameSettings().mTimeInRound, 20);
 }

 TEST(DealerAssistInitResponseDtoTest, ToJSONAndBack)
 {
 	// GIVEN
 	DealerAssistInitResponseDto dto(1, 2, 15000, 5000, 20);

 	// WHEN
 	json val = dto.ToJSON();
 	DealerAssistInitResponseDto result = DealerAssistInitResponseDto::FromJSON(val);

 	// THEN
 	EXPECT_EQ(dto.GetTableState().mGameId, result.GetTableState().mGameId);
 	EXPECT_EQ(dto.GetTableState().mPhase, result.GetTableState().mPhase);
 	EXPECT_EQ(dto.GetGameSettings().mBetTime, result.GetGameSettings().mBetTime);
 	EXPECT_EQ(dto.GetGameSettings().mPauseAfterRound, result.GetGameSettings().mPauseAfterRound);
 	EXPECT_EQ(dto.GetGameSettings().mTimeInRound, result.GetGameSettings().mTimeInRound);
 }