set(DEALER_ASSIST_TEST_SOURCES
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistStateUpdateDtoTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistInitDtoTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistSharedTypesTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/RoundsUntilCloseDtoTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/UnifiedMultiplierTests.cpp
)

set(DEALER_ASSIST_SOURCES
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/DealerAssistSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistInitResponseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/DealerAssistStateUpdateDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/RoundsUntilCloseDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/GameRecordDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/dto/CardBurnDto.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/backend/DealerAssistBackendSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/dealer-assist/ScannedCard.cpp
        ${PROJECT_SOURCE_DIR}/src/common/CreditArray.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/YSharedTypes.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/TDealerGamesExtraData.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/dealer/TDealersStake.cpp
        ${PROJECT_SOURCE_DIR}/src/yserver/hosts/TBetsSharedTypes.cpp
)

add_executable(dealer-assist-test ${DEALER_ASSIST_TEST_SOURCES} ${DEALER_ASSIST_SOURCES})

target_include_directories(dealer-assist-test PRIVATE
        ${PROJECT_SOURCE_DIR}/include
        ${PROJECT_SOURCE_DIR}/include/dealer-assist
        ${PROJECT_SOURCE_DIR}/include/common
        ${PROJECT_SOURCE_DIR}/include/yserver/hosts
        ${PROJECT_SOURCE_DIR}/modules/rtfwk-sdl2/include
        ${PROJECT_SOURCE_DIR}/test
)

target_link_libraries(dealer-assist-test GTest::gtest_main GTest::gmock GTest::gmock_main rtfwk-nogfx)

gtest_discover_tests(dealer-assist-test)
