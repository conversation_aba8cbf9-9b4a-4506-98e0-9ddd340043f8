set(DEALER_ASSIST_TEST_SOURCES
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistStateUpdateDtoTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistInitResponseDtoTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/DealerAssistSharedTypesTests.cpp
        ${PROJECT_SOURCE_DIR}/test/dealer-assist/RoundsUntilCloseDtoTests.cpp
)

set(DEALER_ASSIST_SOURCES
)
add_executable(dealer-assist-test ${DEALER_ASSIST_TEST_SOURCES} ${DEALER_ASSIST_SOURCES})

target_link_libraries(dealer-assist-test GTest::gtest_main GTest::gmock GTest::gmock_main rtfwk-nogfx jwt-cpp websocketpp yprotocol-client)
gtest_discover_tests(dealer-assist-test)
