#include <gtest/gtest.h>

#include "Cryptography.h"
#include "evolution/dto/StandardRespDto.h"

class StandardRespDtoTest : public testing::Test
{
   protected:
	EStatus Status = EStatus::OK;
	double Balance = 123.45;
	std::optional<double> Bonus = 67.89;
	std::optional<bool> bRetransmission = true;
	std::optional<std::string> Sid = crypto::GenRandUUIDv4();
};

TEST_F(StandardRespDtoTest, CreateStandardRespDto)
{
	// when
	const StandardRespDto dto(Status, Balance, Bonus, bRetransmission, Sid);

	// then
	EXPECT_EQ(dto.Status, Status);
	EXPECT_EQ(dto.Balance, Balance);
	EXPECT_EQ(dto.Bonus, Bonus);
	EXPECT_EQ(dto.bRetransmission, bRetransmission);
	EXPECT_EQ(dto.Sid, Sid);
}

TEST_F(StandardRespDtoTest, DtoToJson)
{
	// given
	const StandardRespDto dto(Status, Balance, Bonus, bRetransmission, Sid);

	// when
	const auto json = dto.ToJSON();

	// then
	EXPECT_EQ(json["status"].get<std::string>(), Status._to_string());
	EXPECT_EQ(json["balance"].get<double>(), Balance);
	EXPECT_EQ(json["bonus"].get<double>(), Bonus);
	EXPECT_EQ(json["retransmission"].get<bool>(), bRetransmission);
	EXPECT_EQ(json["sid"].get<std::string>(), Sid);
}

TEST_F(StandardRespDtoTest, DtoToJsonToDto)
{
	// given
	const StandardRespDto dto(Status, Balance, Bonus, bRetransmission, Sid);

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = StandardRespDto::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(StandardRespDtoTest, DtoToJsonToDtoErrorStandardRespDto)
{
	// given
	const StandardRespDto dto(Status, {}, {}, {}, Sid);

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = StandardRespDto::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}
