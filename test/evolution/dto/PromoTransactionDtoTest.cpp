#include <gtest/gtest.h>

#include "Cryptography.h"
#include "evolution/dto/PromoTransactionDto.h"

class PromoTransactionDtoTest : public testing::Test
{
   protected:
	std::string Type = "JackpotWin";
	PromoTransactionOriginDto Origin = PromoTransactionOriginDto(EPromoTransactionOriginType::SpinGifts);
	std::string ID = crypto::GenRandUUIDv4();
	double Amount = 123.45;
	std::optional<std::string> VoucherID = crypto::GenRandUUIDv4();
	std::optional<std::uint32_t> RemainingRounds = 5;
	std::list<JackpotDto> Jackpots = { JackpotDto(crypto::GenRandUUIDv4(), 123.45) };
	std::optional<double> PlayableBalance = 123.45;
	std::optional<std::string> BonusConfigID = crypto::GenRandUUIDv4();
	std::optional<std::string> RewardID = crypto::GenRandUUIDv4();
};

TEST_F(PromoTransactionDtoTest, ConstructDto)
{
	// when
	const PromoTransactionDto dto(Type, Origin, ID, Amount, VoucherID, RemainingRounds, Jackpots, PlayableBalance, BonusConfigID, RewardID);

	// then
	EXPECT_EQ(dto.Type, Type);
	EXPECT_EQ(dto.Origin, Origin);
	EXPECT_EQ(dto.ID, ID);
	EXPECT_EQ(dto.Amount, Amount);
	EXPECT_EQ(dto.VoucherID, VoucherID);
	EXPECT_EQ(dto.RemainingRounds, RemainingRounds);
	EXPECT_EQ(dto.Jackpots, Jackpots);
	EXPECT_EQ(dto.PlayableBalance, PlayableBalance);
	EXPECT_EQ(dto.BonusConfigID, BonusConfigID);
	EXPECT_EQ(dto.RewardID, RewardID);
}

TEST_F(PromoTransactionDtoTest, ConstructEmptyDto)
{
	// when
	const PromoTransactionDto dto;

	// then
	EXPECT_TRUE(dto.Type.empty());
	EXPECT_EQ(dto.Origin, std::nullopt);
	EXPECT_TRUE(dto.ID.empty());
	EXPECT_EQ(dto.Amount, 0);
	EXPECT_FALSE(dto.VoucherID.has_value());
	EXPECT_FALSE(dto.RemainingRounds.has_value());
	EXPECT_TRUE(dto.Jackpots.empty());
	EXPECT_FALSE(dto.PlayableBalance.has_value());
	EXPECT_FALSE(dto.BonusConfigID.has_value());
	EXPECT_FALSE(dto.RewardID.has_value());
}

TEST_F(PromoTransactionDtoTest, DtoToJson)
{
	// given
	const PromoTransactionDto dto(Type, Origin, ID, Amount, VoucherID, RemainingRounds, Jackpots, PlayableBalance, BonusConfigID, RewardID);

	// when
	const auto json = dto.ToJSON();

	// then
	EXPECT_EQ(json["type"].get<std::string>(), Type);
	EXPECT_EQ(json["origin"], Origin.ToJSON());
	EXPECT_EQ(json["origin"]["type"].get<std::string>(), Origin.Type.value()._to_string());
	EXPECT_EQ(json["id"].get<std::string>(), ID);
	EXPECT_EQ(json["amount"].get<double>(), Amount);
	EXPECT_EQ(json["voucherId"].get<std::string>(), VoucherID);
	EXPECT_EQ(json["remainingRounds"].get<uint32_t>(), RemainingRounds);
	EXPECT_EQ(json["jackpots"][0], Jackpots.front().ToJSON());
	EXPECT_EQ(json["jackpots"][0]["id"].get<std::string>(), Jackpots.front().ID);
	EXPECT_EQ(json["jackpots"][0]["winAmount"].get<double>(), Jackpots.front().WinAmount);
	EXPECT_EQ(json["playableBalance"].get<double>(), PlayableBalance);
	EXPECT_EQ(json["bonusConfigId"].get<std::string>(), BonusConfigID);
	EXPECT_EQ(json["rewardId"].get<std::string>(), RewardID);
}

TEST_F(PromoTransactionDtoTest, DtoToJsonToDto)
{
	// given
	const PromoTransactionDto dto(Type, Origin, ID, Amount, VoucherID, RemainingRounds, Jackpots, PlayableBalance, BonusConfigID, RewardID);

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = PromoTransactionDto::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}
