#include <gtest/gtest.h>

#include "TestUtils.h"
#include "Cryptography.h"
#include "evolution/dto/CreditReqDto.h"

class CreditReqDtoTest : public testing::Test
{
   protected:
	std::string Sid = crypto::GenRandUUIDv4();
	std::string UserId = crypto::GenRandUUIDv4();
	std::string Currency = "USD";
	GameDto Game = GameDto("blackjack", GameDetailsDto(TableDto("tableId", "vid")), crypto::GenRandUUIDv4());
	TransactionDto Transaction = TransactionDto(crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), 123.45);
	std::string Uuid = crypto::GenRandUUIDv4();

	json GetFullJson() const
	{
		json val(json::value_t::object);
		val["sid"] = Sid;
		val["userId"] = UserId;
		val["currency"] = Currency;
		val["game"] = Game.ToJSON();
		val["transaction"] = Transaction.ToJSON();
		val["uuid"] = Uuid;
		return val;
	}

	static CreditReqDto GetDtoFromJson(const json& val)
	{
		CreditReqDto dto;
		dto.LoadSubconfiguration(val);
		return dto;
	}
};

TEST_F(CreditReqDtoTest, ConvertJsonToDto)
{
	// given
	const auto val = GetFullJson();

	// when
	const auto dto = GetDtoFromJson(val);

	// then
	EXPECT_EQ(dto.Sid, Sid);
	EXPECT_EQ(dto.UserId, UserId);
	EXPECT_EQ(dto.Currency, ECurrency::_from_string(Currency.c_str()));
	EXPECT_EQ(dto.Game, Game);
	EXPECT_EQ(dto.Transaction, Transaction);
	EXPECT_EQ(dto.Uuid, Uuid);
}

TEST_F(CreditReqDtoTest, ThrowWhenSidIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("sid");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'sid'");
}

TEST_F(CreditReqDtoTest, ThrowWhenUserIdIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("userId");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'userId'");
}

TEST_F(CreditReqDtoTest, ThrowWhenCurrencyIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("currency");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'currency'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("game");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameIdIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"].erase("id");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game.id'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameTypeIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"].erase("type");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game.type'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameDetailsIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"].erase("details");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game.details'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameDetailsTableIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"]["details"].erase("table");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game.details.table'");
}

TEST_F(CreditReqDtoTest, ThrowWhenGameDetailsTableIdIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"]["details"]["table"].erase("id");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'game.details.table.id'");
}

TEST_F(CreditReqDtoTest, DontThrowWhenGameDetailsTableVidIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val["game"]["details"]["table"].erase("vid");

	// when
	const auto dto = GetDtoFromJson(val);

	// then
	EXPECT_EQ(dto.Game.GameDetails.Table.Vid, std::nullopt);
}

TEST_F(CreditReqDtoTest, ThrowWhenTransactionIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("transaction");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'transaction'");
}

TEST_F(CreditReqDtoTest, ThrowWhenUuidIsMissing)
{
	// GIVEN
	json val = GetFullJson();
	val.erase("uuid");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&val] { return GetDtoFromJson(val); }, "Missing required member 'uuid'");
}

TEST_F(CreditReqDtoTest, DtoToJsonToDto)
{
	// given
	const auto dto = GetDtoFromJson(GetFullJson());

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto, dto2);
}
