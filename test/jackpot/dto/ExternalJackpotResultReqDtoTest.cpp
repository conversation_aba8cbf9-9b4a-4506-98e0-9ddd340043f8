#include <gtest/gtest.h>

#include "TestUtils.h"
#include "jackpot/JackpotTestUtils.h"
#include "jackpot/dto/ExternalJackpotResultReqDto.h"

class ExternalJackpotResultReqDtoTest : public ::testing::Test
{
   protected:
	std::string LevelId = crypto::GenRandUUIDv4();
	bool isWon = true;
	bool isFlagged = true;

	// Helper function to set common properties
	json GetFullJson()
	{
		json json(json::value_t::object);
		json["levelId"] = LevelId;
		json["isWon"] = isWon;
		json["isFlagged"] = isFlagged;
		return json;
	}

	static ExternalJackpotResultReqDto GetDtoFromJson(const json& json)
	{
		return ExternalJackpotResultReqDto(ExternalJackpotResultReqDto::ExternalJackpotResultReqSchema.GenerateConfig(json));
	}
};

/**
 * @brief Test that the full DTO can be converted to JSON
 */
TEST_F(ExternalJackpotResultReqDtoTest, ConvertJsonToDto)
{
	// given
	json json = GetFullJson();

	// when
	ExternalJackpotResultReqDto dto = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto.LevelID, LevelId);
	EXPECT_EQ(dto.IsWon, isWon);
	EXPECT_EQ(dto.IsFlagged, isFlagged);
}

TEST_F(ExternalJackpotResultReqDtoTest, ThrowWhenLevelIdIsMissing)
{
	// given
	json json = GetFullJson();
	json.erase("levelId");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); }, "Missing required member 'levelId'");
}

TEST_F(ExternalJackpotResultReqDtoTest, ThrowWhenIsWonIsMissing)
{
	// given
	json json = GetFullJson();
	json.erase("isWon");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); }, "Missing required member 'isWon'");
}

TEST_F(ExternalJackpotResultReqDtoTest, DontThrowWhenIsFlaggedIsMissing)
{
	// given
	json json = GetFullJson();
	json.erase("isFlagged");

	// when
	const auto dto = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto.IsFlagged, false);
}

TEST_F(ExternalJackpotResultReqDtoTest, DtoToJsonToDto)
{
	// given
	ExternalJackpotResultReqDto dto = { LevelId, isWon, isFlagged };

	// when
	json json = dto.ToJSON();
	ExternalJackpotResultReqDto dto2 = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(ExternalJackpotResultReqDtoTest, DtoToJsonToDtoWithClientConfig)
{
	// given
	ExternalJackpotResultReqDto dto = { LevelId, isWon };
	dto.ClientConfig = jackpot::testutils::GetClientConfigDto();

	// when
	json json = dto.ToJSON();
	ExternalJackpotResultReqDto dto2 = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto, dto2);
}
