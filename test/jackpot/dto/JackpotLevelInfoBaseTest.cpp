#include <gtest/gtest.h>

#include "Cryptography.h"
#include "jackpot/dto/JackpotLevelInfoBase.h"

class JackpotLevelInfoBaseTest : public testing::Test
{
   protected:
	std::string id = crypto::GenRandUUIDv4();
	EJackpotLevelType type = EJackpotLevelType::Mystery;
	EJackpotPotType potType = EJackpotPotType::Progressive;
	std::string currency = "USD";
	std::string clientGroupId = crypto::GenRandUUIDv4();
	double increment = 0.01;
	double incrementVisibleRel = 0.6;
	std::string externalTriggerType1 = "44Catcher";
	ExternalTriggerTypeValues externalTriggerTypeValues1 = { 0.235, 0.000012 };
	std::string externalTriggerType2 = "SuitedMatch";
	ExternalTriggerTypeValues externalTriggerTypeValues2 = { 0.266, 0.0000098 };
	std::unordered_map<std::string, ExternalTriggerTypeValues> externalTriggerTypeData = { { externalTriggerType1, externalTriggerTypeValues1 },
		                                                                                   { externalTriggerType2, externalTriggerTypeValues2 } };
	double minPot = 100;
	double maxPot = 10000;
	double avgPayout = 1000;
	double minBet = 1.5;
	double maxBet = 100;
	double minWinnableBet = 3.5;
	double maxWinnableBet = 80.99;
	uint16_t from = 800;
	uint16_t to = 1600;
	std::string timezone = "Europe/Ljubljana";
	Bitflag<EWeekday, uint8_t> weekdays = Bitflag<EWeekday, uint8_t>(0b01101001);
	std::string name = "test";
	EActionAfterWin AutoActionAfterWin = EActionAfterWin::Reset;
	uint32_t AutoActionAfterWinOffsetSec = 50;
	uint32_t MinTimeBetweenWinsSec = 2;
	bool useFixedPaidIn = true;
	bool keepPotAboveMinPot = true;
	std::string templateId = crypto::GenRandUUIDv4();
	std::string levelReference = crypto::GenRandUUIDv4();
	bool demo = true;
	json customData = { { "key1", "value1" }, { "key2", "value2" } };

	json GetFullJson() const
	{
		json weekDaysJson(json::value_t::array);
		for (const auto weekDay : EWeekday::_values())
		{
			if (weekdays.HasFlag(weekDay))
				weekDaysJson.push_back(weekDay._to_string());
		}

		json val;
		val[jackpotlevelkey::ID] = id;
		val[jackpotlevelkey::TYPE] = type._to_string();
		val[jackpotlevelkey::POT_TYPE] = potType._to_string();
		val[jackpotlevelkey::CURRENCY] = currency;
		val[jackpotlevelkey::CLIENT_GROUP_ID] = clientGroupId;
		val[jackpotlevelkey::INCREMENT] = increment;
		val[jackpotlevelkey::INCREMENT_VISIBLE_REL] = incrementVisibleRel;
		val[jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA] = externalTriggerTypeData;
		val[jackpotlevelkey::MIN_POT] = minPot;
		val[jackpotlevelkey::MAX_POT] = maxPot;
		val[jackpotlevelkey::AVG_PAYOUT] = avgPayout;
		val[jackpotlevelkey::MIN_BET] = minBet;
		val[jackpotlevelkey::MAX_BET] = maxBet;
		val[jackpotlevelkey::MIN_WINNABLE_BET] = minWinnableBet;
		val[jackpotlevelkey::MAX_WINNABLE_BET] = maxWinnableBet;
		val[jackpotlevelkey::TIME_FROM] = from;
		val[jackpotlevelkey::TIME_TO] = to;
		val[jackpotlevelkey::TIMEZONE] = timezone;
		val[jackpotlevelkey::WEEK_DAYS] = std::move(weekDaysJson);
		val[jackpotlevelkey::NAME] = name;
		val[jackpotlevelkey::AUTO_ACTION_AFTER_WIN] = AutoActionAfterWin._to_string();
		val[jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET] = AutoActionAfterWinOffsetSec;
		val[jackpotlevelkey::MIN_TIME_BETWEEN_WINS] = MinTimeBetweenWinsSec;
		val[jackpotlevelkey::USE_FIXED_PAID_IN] = useFixedPaidIn;
		val[jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT] = keepPotAboveMinPot;
		val[jackpotlevelkey::TEMPLATE_ID] = templateId;
		val[jackpotlevelkey::LEVEL_REFERENCE] = levelReference;
		val[jackpotlevelkey::DEMO] = demo;
		val[jackpotlevelkey::CUSTOM_DATA] = customData;
		return val;
	}
};

TEST_F(JackpotLevelInfoBaseTest, TestKeyValues)
{
	EXPECT_STREQ(jackpotlevelkey::ID, "id");
	EXPECT_STREQ(jackpotlevelkey::TYPE, "type");
	EXPECT_STREQ(jackpotlevelkey::POT_TYPE, "potType");
	EXPECT_STREQ(jackpotlevelkey::CURRENCY, "currency");
	EXPECT_STREQ(jackpotlevelkey::CLIENT_GROUP_ID, "clientGroupId");
	EXPECT_STREQ(jackpotlevelkey::INCREMENT, "increment");
	EXPECT_STREQ(jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA, "externalTriggerTypeData");
	EXPECT_STREQ(jackpotlevelkey::MIN_POT, "minPot");
	EXPECT_STREQ(jackpotlevelkey::MAX_POT, "maxPot");
	EXPECT_STREQ(jackpotlevelkey::AVG_PAYOUT, "avgPayout");
	EXPECT_STREQ(jackpotlevelkey::MIN_BET, "minBet");
	EXPECT_STREQ(jackpotlevelkey::MAX_BET, "maxBet");
	EXPECT_STREQ(jackpotlevelkey::MIN_WINNABLE_BET, "minWinnableBet");
	EXPECT_STREQ(jackpotlevelkey::MAX_WINNABLE_BET, "maxWinnableBet");
	EXPECT_STREQ(jackpotlevelkey::TIME_FROM, "from");
	EXPECT_STREQ(jackpotlevelkey::TIME_TO, "to");
	EXPECT_STREQ(jackpotlevelkey::TIMEZONE, "timezone");
	EXPECT_STREQ(jackpotlevelkey::WEEK_DAYS, "weekDays");
	EXPECT_STREQ(jackpotlevelkey::NAME, "name");
	EXPECT_STREQ(jackpotlevelkey::AUTO_ACTION_AFTER_WIN, "autoActionAfterWin");
	EXPECT_STREQ(jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET, "autoActionAfterWinOffsetSec");
	EXPECT_STREQ(jackpotlevelkey::MIN_TIME_BETWEEN_WINS, "minTimeBetweenWins");
	EXPECT_STREQ(jackpotlevelkey::USE_FIXED_PAID_IN, "useFixedPaidIn");
	EXPECT_STREQ(jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT, "keepPotAboveMinPot");
	EXPECT_STREQ(jackpotlevelkey::TEMPLATE_ID, "templateId");
	EXPECT_STREQ(jackpotlevelkey::LEVEL_REFERENCE, "levelReference");
	EXPECT_STREQ(jackpotlevelkey::DEMO, "demo");
	EXPECT_STREQ(jackpotlevelkey::CUSTOM_DATA, "customData");
}

TEST_F(JackpotLevelInfoBaseTest, ConvertJsonToDto)
{
	// given
	json json = GetFullJson();

	// when
	auto dto = JackpotLevelInfoBase::FromJSON(json);

	// then
	EXPECT_EQ(dto.ID, id);
	EXPECT_EQ(dto.Type, type);
	EXPECT_EQ(dto.PotType, potType);
	EXPECT_EQ(dto.Currency._to_string(), currency);
	EXPECT_EQ(dto.ClientGroupID, clientGroupId);
	EXPECT_EQ(dto.Increment, increment);
	EXPECT_EQ(dto.ExternalTriggerTypeData, externalTriggerTypeData);
	EXPECT_EQ(dto.MinPot, minPot);
	EXPECT_EQ(dto.MaxPot, maxPot);
	EXPECT_EQ(dto.AvgPayout, avgPayout);
	EXPECT_EQ(dto.MinBet, minBet);
	EXPECT_EQ(dto.MaxBet, maxBet);
	EXPECT_EQ(dto.MinWinnableBet, minWinnableBet);
	EXPECT_EQ(dto.MaxWinnableBet, maxWinnableBet);
	EXPECT_EQ(dto.TimeFrom, from);
	EXPECT_EQ(dto.TimeTo, to);
	EXPECT_EQ(dto.Timezone, timezone);
	EXPECT_EQ(dto.WeekDays, weekdays);
	EXPECT_EQ(dto.Name, name);
	EXPECT_EQ(dto.AutoActionAfterWin, AutoActionAfterWin);
	EXPECT_EQ(dto.AutoActionAfterWinOffsetSec, AutoActionAfterWinOffsetSec);
	EXPECT_EQ(dto.MinTimeBetweenWinsSec, MinTimeBetweenWinsSec);
	EXPECT_EQ(dto.bUseFixedPaidIn, useFixedPaidIn);
	EXPECT_EQ(dto.bKeepPotAboveMinPot, keepPotAboveMinPot);
	EXPECT_EQ(dto.TemplateID, templateId);
	EXPECT_EQ(dto.LevelReference, levelReference);
	EXPECT_EQ(dto.bDemo, demo);
	EXPECT_EQ(dto.CustomData, customData);
}

TEST_F(JackpotLevelInfoBaseTest, DtoToJsonToDto)
{
	// given
	auto dto = JackpotLevelInfoBase();
	dto.ID = id;
	dto.Type = type;
	dto.PotType = potType;
	dto.Currency = ECurrency::USD;
	dto.ClientGroupID = clientGroupId;
	dto.Increment = increment;
	dto.ExternalTriggerTypeData = externalTriggerTypeData;
	dto.MinPot = minPot;
	dto.MaxPot = maxPot;
	dto.AvgPayout = avgPayout;
	dto.MinBet = minBet;
	dto.MaxBet = maxBet;
	dto.MinWinnableBet = minWinnableBet;
	dto.MaxWinnableBet = maxWinnableBet;
	dto.TimeFrom = from;
	dto.TimeTo = to;
	dto.Timezone = timezone;
	dto.WeekDays = weekdays;
	dto.Name = name;
	dto.AutoActionAfterWin = AutoActionAfterWin;
	dto.AutoActionAfterWinOffsetSec = AutoActionAfterWinOffsetSec;
	dto.MinTimeBetweenWinsSec = MinTimeBetweenWinsSec;
	dto.bUseFixedPaidIn = useFixedPaidIn;
	dto.bKeepPotAboveMinPot = keepPotAboveMinPot;
	dto.TemplateID = templateId;
	dto.LevelReference = levelReference;
	dto.bDemo = demo;
	dto.CustomData = customData;

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = JackpotLevelInfoBase::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(JackpotLevelInfoBaseTest, JsonToDtoToJson)
{
	// GIVEN
	auto json = GetFullJson();

	// WHEN
	auto dto = JackpotLevelInfoBase::FromJSON(json);
	auto json2 = dto.ToJSON();

	// THEN
	EXPECT_EQ(json, json2);
}

TEST_F(JackpotLevelInfoBaseTest, EmptyJsonToDtoToJson)
{
	// GIVEN
	const auto json1(json::value_t::object);

	// WHEN
	auto dto = JackpotLevelInfoBase::FromJSON(json1);
	auto json2 = dto.ToJSON();

	// THEN
	EXPECT_EQ(json2[jackpotlevelkey::ID], "");
	EXPECT_EQ(json2[jackpotlevelkey::TYPE], EJackpotLevelType(EJackpotLevelType::Mystery)._to_string());
	EXPECT_EQ(json2[jackpotlevelkey::POT_TYPE], EJackpotPotType(EJackpotPotType::Progressive)._to_string());
	EXPECT_EQ(json2[jackpotlevelkey::CURRENCY], ECurrency(ECurrency::NONE)._to_string());
	EXPECT_EQ(json2[jackpotlevelkey::CLIENT_GROUP_ID], "");
	EXPECT_EQ(json2[jackpotlevelkey::INCREMENT], 0.);
	EXPECT_TRUE(json2[jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA].empty());
	EXPECT_EQ(json2[jackpotlevelkey::MIN_POT], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::MAX_POT], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::AVG_PAYOUT], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::MIN_BET], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::MAX_BET], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::MIN_WINNABLE_BET], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::MAX_WINNABLE_BET], 0.);
	EXPECT_EQ(json2[jackpotlevelkey::TIME_FROM], 0);
	EXPECT_EQ(json2[jackpotlevelkey::TIME_TO], 0);
	EXPECT_EQ(json2[jackpotlevelkey::TIMEZONE], "Etc/GMT");
	EXPECT_EQ(json2[jackpotlevelkey::WEEK_DAYS], json::array({ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday" }));
	EXPECT_EQ(json2[jackpotlevelkey::NAME], "");
	EXPECT_EQ(json2[jackpotlevelkey::AUTO_ACTION_AFTER_WIN], EActionAfterWin(EActionAfterWin::Nothing)._to_string());
	EXPECT_EQ(json2[jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET], 0);
	EXPECT_EQ(json2[jackpotlevelkey::MIN_TIME_BETWEEN_WINS], 0);
	EXPECT_FALSE(json2[jackpotlevelkey::USE_FIXED_PAID_IN]);
	EXPECT_FALSE(json2[jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT]);
	EXPECT_FALSE(json2.contains(jackpotlevelkey::TEMPLATE_ID));
	EXPECT_FALSE(json2.contains(jackpotlevelkey::LEVEL_REFERENCE));
	EXPECT_FALSE(json2[jackpotlevelkey::DEMO]);
	EXPECT_EQ(json2[jackpotlevelkey::CUSTOM_DATA], json());
}

TEST_F(JackpotLevelInfoBaseTest, EmptyDtoToJson)
{
	// GIVEN
	const auto dto = JackpotLevelInfoBase();

	// WHEN
	const auto jsonDto = dto.ToJSON();

	// THEN
	EXPECT_EQ(jsonDto[jackpotlevelkey::ID], "");
	EXPECT_EQ(jsonDto[jackpotlevelkey::TYPE], EJackpotLevelType(EJackpotLevelType::Mystery)._to_string());
	EXPECT_EQ(jsonDto[jackpotlevelkey::POT_TYPE], EJackpotPotType(EJackpotPotType::Progressive)._to_string());
	EXPECT_EQ(jsonDto[jackpotlevelkey::CURRENCY], ECurrency(ECurrency::NONE)._to_string());
	EXPECT_EQ(jsonDto[jackpotlevelkey::CLIENT_GROUP_ID], "");
	EXPECT_EQ(jsonDto[jackpotlevelkey::INCREMENT], 0.);
	EXPECT_TRUE(jsonDto[jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA].empty());
	EXPECT_EQ(jsonDto[jackpotlevelkey::MIN_POT], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MAX_POT], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::AVG_PAYOUT], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MIN_BET], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MAX_BET], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MIN_WINNABLE_BET], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MAX_WINNABLE_BET], 0.);
	EXPECT_EQ(jsonDto[jackpotlevelkey::TIME_FROM], 0);
	EXPECT_EQ(jsonDto[jackpotlevelkey::TIME_TO], 0);
	EXPECT_EQ(jsonDto[jackpotlevelkey::TIMEZONE], nullptr);
	EXPECT_EQ(jsonDto[jackpotlevelkey::WEEK_DAYS], json::array({ "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday" }));
	EXPECT_EQ(jsonDto[jackpotlevelkey::NAME], "");
	EXPECT_EQ(jsonDto[jackpotlevelkey::AUTO_ACTION_AFTER_WIN], EActionAfterWin(EActionAfterWin::Nothing)._to_string());
	EXPECT_EQ(jsonDto[jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET], 0);
	EXPECT_EQ(jsonDto[jackpotlevelkey::MIN_TIME_BETWEEN_WINS], 0);
	EXPECT_FALSE(jsonDto[jackpotlevelkey::USE_FIXED_PAID_IN]);
	EXPECT_FALSE(jsonDto[jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT]);
	EXPECT_FALSE(jsonDto.contains(jackpotlevelkey::TEMPLATE_ID));
	EXPECT_FALSE(jsonDto.contains(jackpotlevelkey::LEVEL_REFERENCE));
	EXPECT_FALSE(jsonDto[jackpotlevelkey::DEMO]);
	EXPECT_EQ(jsonDto[jackpotlevelkey::CUSTOM_DATA], json());
}
