#include <gtest/gtest.h>

#include "Cryptography.h"
#include "TestUtils.h"
#include "Timing.h"
#include "jackpot/dto/ClientGroupDto.h"

class ClientGroupDtoTest : public ::testing::Test
{
   protected:
	std::string ClientGroupId = crypto::GenRandUUIDv4();

	ClientGroupDto GetDto() const { return ClientGroupDto(ClientGroupId); }
};

/**
 * @brief Test that the full DTO can be converted to JSON
 */
TEST_F(ClientGroupDtoTest, ConvertDtoToJson)
{
	// given
	const auto dto = GetDto();

	// when
	json json = dto.ToJSON();

	// then
	EXPECT_EQ(json["clientGroupId"].get<std::string>(), ClientGroupId);
}

/**
 * Test that the full JSON can be converted to DTO
 */
TEST_F(ClientGroupDtoTest, ConvertJsonToDto)
{
	// given
	json json(json::value_t::object);
	json["clientGroupId"] = ClientGroupId;

	// when
	const auto dto = ClientGroupDto::FromJSON(json);

	// then
	EXPECT_EQ(dto.ClientGroupID, ClientGroupId);
}

TEST_F(ClientGroupDtoTest, DtoToJsonToDto)
{
	// given
	const auto dto = GetDto();

	// when
	const auto json = dto.ToJSON();
	const auto dto2 = ClientGroupDto::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}

/**
 * Test that the empty JSON can be converted to DTO
 */
TEST_F(ClientGroupDtoTest, ConvertEmptyJsonToDto)
{
	// given
	json json(json::value_t::object);

	// when
	const auto dto = ClientGroupDto::FromJSON(json);

	// then
	EXPECT_EQ(dto.ClientGroupID, std::string());
}

TEST_F(ClientGroupDtoTest, TestEmptyFilter)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter;

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByMatchedStartsWith)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter(std::format("^{}.*", ClientGroupId.substr(0, 4)));

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByMatchedContains)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter(std::format(".*{}.*", ClientGroupId.substr(4, 4)));

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByMatchedEndsWith)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter(std::format(".*{}$", ClientGroupId.substr(ClientGroupId.size() - 4)));

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByUnmatchedContains)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter(".*unmatched.*");

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByUnmatchedEndsWith)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter(".*unmatched$");

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(ClientGroupDtoTest, TestFilterByUnmatchedStartsWith)
{
	// given
	const auto dto = GetDto();
	ClientGroupFilter filter("^unmatched.*");

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}
