#include <gtest/gtest.h>

#include "jackpot/dto/JackpotValuesDto.h"

class JackpotValuesDtoTest : public ::testing::Test
{
   protected:
	double Pot = 20.4;
	double BackPot = 5.6;
	double DisplayPotStart = 0.6;
	EJackpotLevelStatus Status = EJackpotLevelStatus::Counting;
	uint64_t LastActiveTS = ytime::GetSystemTimeMsec();
	uint64_t CountersSinceTS = ytime::GetSystemTimeMsec() - 100000;

	JackpotValuesDto Values = {
		.Pot = Pot, .BackPot = BackPot, .DisplayPotStart = DisplayPotStart, .Status = Status, .LastActiveTS = LastActiveTS, .CountersSinceTS = CountersSinceTS
	};
};

TEST_F(JackpotValuesDtoTest, ConvertDtoToJson)
{
	// given
	const auto dto = Values;

	// when
	json json = dto.ToJSON();

	// then
	EXPECT_EQ(json["pot"].get<double>(), Pot);
	EXPECT_EQ(json["backPot"].get<double>(), BackPot);
	EXPECT_EQ(json["displayPotStart"].get<double>(), DisplayPotStart);
	EXPECT_EQ(json["status"].get<std::string>(), Status._to_string());
	EXPECT_EQ(json["lastActive"].get<uint64_t>(), LastActiveTS);
	EXPECT_EQ(json["countersSince"].get<uint64_t>(), CountersSinceTS);
}

TEST_F(JackpotValuesDtoTest, ConvertJsonToDto)
{
	// given
	json json(json::value_t::object);
	json["pot"] = Pot;
	json["backPot"] = BackPot;
	json["displayPotStart"] = DisplayPotStart;
	json["status"] = Status._to_string();
	json["countersSince"] = CountersSinceTS;
	json["lastActive"] = LastActiveTS;

	// when
	const auto dto = JackpotValuesDto::FromJSON(json);

	// then
	EXPECT_EQ(dto.Pot, Pot);
	EXPECT_EQ(dto.BackPot, BackPot);
	EXPECT_EQ(dto.DisplayPotStart, DisplayPotStart);
	EXPECT_EQ(dto.Status, Status);
	EXPECT_EQ(dto.CountersSinceTS, CountersSinceTS);
	EXPECT_EQ(dto.LastActiveTS, LastActiveTS);
}

TEST_F(JackpotValuesDtoTest, DtoToJsonToDto)
{
	// given
	const auto dto = Values;

	// when
	const json json = dto.ToJSON();
	const auto dto2 = JackpotValuesDto::FromJSON(json);

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(JackpotValuesDtoTest, ConvertEmptyJsonToDto)
{
	// given
	const json json(json::value_t::object);

	// when
	const JackpotValuesDto dto = JackpotValuesDto::FromJSON(json);

	// then
	EXPECT_EQ(dto.Pot, 0);
	EXPECT_EQ(dto.BackPot, 0);
	EXPECT_EQ(dto.DisplayPotStart, 0);
	EXPECT_EQ(dto.Status, EJackpotLevelStatus::Disabled);
	EXPECT_EQ(dto.CountersSinceTS, 0);
	EXPECT_EQ(dto.LastActiveTS, 0);
}

TEST_F(JackpotValuesDtoTest, GetDisplayedPot_PotEqMinPot)
{
	// given
	Values.Pot = 20.4;
	JackpotLevelInfoBase info;
	info.MinPot = Values.Pot;

	// when
	const double displayedPot = Values.GetDisplayedPot(info);

	// then
	EXPECT_EQ(displayedPot, Values.Pot);
}

TEST_F(JackpotValuesDtoTest, GetDisplayedPot_PotGtMinPot)
{
	// given
	Values.Pot = 20.4;
	JackpotLevelInfoBase info;
	info.MinPot = 10.0;

	// when
	const double displayedPot = Values.GetDisplayedPot(info);

	// then
	EXPECT_EQ(displayedPot, Values.Pot);
}

TEST_F(JackpotValuesDtoTest, GetDisplayedPot_PotZero)
{
	// given
	Values.Pot = 0.0;
	JackpotLevelInfoBase info;
	info.MinPot = 10.0;

	// when
	const double displayedPot = Values.GetDisplayedPot(info);

	// then
	EXPECT_EQ(displayedPot, info.MinPot * Values.DisplayPotStart);
}

TEST_F(JackpotValuesDtoTest, GetDisplayedPot_PotLtMinPot)
{
	// given
	Values.Pot = 5.0;
	JackpotLevelInfoBase info;
	info.MinPot = 10.0;

	// when
	const double displayedPot = Values.GetDisplayedPot(info);

	// then
	EXPECT_EQ(displayedPot, 8);    // 5 is 50% of 10, minDisplayPot = 6 (displayPotStart * minPot), 50% between 6 and 10 (minPot) is 8
}
