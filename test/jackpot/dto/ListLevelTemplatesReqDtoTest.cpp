#include <gtest/gtest.h>

#include "Cryptography.h"
#include "YUtils.h"
#include "jackpot/dto/ListLevelTemplatesReqDto.h"

class ListLevelTemplatesReqDtoTest : public ::testing::Test
{
   protected:
	std::unordered_set<std::string> LevelTemplateIDs = { crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4() };
	std::unordered_set<std::string> ClientGroupIDs = { crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4() };

	LevelTemplateFilter Filter = LevelTemplateFilter(LevelTemplateIDs, ClientGroupIDs);
};

TEST_F(ListLevelTemplatesReqDtoTest, ConvertDtoToQueryString)
{
	// given
	ListLevelTemplatesReqDto dto(Filter);

	// when
	web::QueryString query = dto.ToQueryString();

	// then
	EXPECT_EQ(query.Get("template-ids"), yutils::Join(LevelTemplateIDs, ","));
	EXPECT_EQ(query.Get("client-group-ids"), yutils::Join(ClientGroupIDs, ","));
}

TEST_F(ListLevelTemplatesReqDtoTest, ConvertQueryStringToDto)
{
	// given
	const web::QueryString query = { { "template-ids", yutils::Join(LevelTemplateIDs, ",") }, { "client-group-ids", yutils::Join(ClientGroupIDs, ",") } };

	// when
	const ListLevelTemplatesReqDto dto(query);

	// then
	EXPECT_EQ(dto.Filter.LevelTemplateIDs, LevelTemplateIDs);
	EXPECT_EQ(dto.Filter.ClientGroupIDs, ClientGroupIDs);
}

TEST_F(ListLevelTemplatesReqDtoTest, DtoToQueryStringToDto)
{
	// given
	const ListLevelTemplatesReqDto dto(Filter);

	// when
	const auto query = dto.ToQueryString();
	const ListLevelTemplatesReqDto dto2(query);

	// then
	EXPECT_EQ(dto, dto2);
}
