#include <gtest/gtest.h>

#include "Cryptography.h"
#include "jackpot/dto/JackpotLevelReconfiguredEventDto.h"
#include "jackpot/dto/SaveJackpotLevelReqDto.h"

class JackpotLevelReconfiguredEventDtoTest : public ::testing::Test
{
   protected:
	std::string id = crypto::GenRandUUIDv4();
	EJackpotLevelType type = EJackpotLevelType::Mystery;
	EJackpotPotType potType = EJackpotPotType::Progressive;
	ECurrency currency = ECurrency::EUR;
	std::string clientGroupId = crypto::GenRandUUIDv4();
	double increment = 0.01;
	double minPot = 100;
	double maxPot = 10000;
	double avgPayout = 1000;
	double minBet = 1;
	double maxBet = 100;
	double minWinnableBet = 1.8;
	double maxWinnableBet = 89.99;
	uint64_t from = 800;
	uint64_t to = 1600;
	std::string timezone = "Europe/Berlin";
	Bitflag<EWeekday, uint8_t> weekdays = Bitflag<EWeekday, uint8_t>(0b01101101);
	std::string name = "test";
	EActionAfterWin AutoActionAfterWin = EActionAfterWin::Reset;
	uint32_t AutoActionAfterWinOffsetSec = 50;
	uint32_t MinTimeBetweenWinsSec = 2;
	bool bUseFixedPaidIn = true;
	bool bKeepPotAboveMinPot = true;

	JackpotLevelInfoBase GetJackpotLevelInfo() const
	{
		JackpotLevelInfoBase levelInfo = JackpotLevelInfoBase();
		levelInfo.ID = id;
		levelInfo.Type = type;
		levelInfo.PotType = potType;
		levelInfo.Currency = currency;
		levelInfo.ClientGroupID = clientGroupId;
		levelInfo.Increment = increment;
		levelInfo.MinPot = minPot;
		levelInfo.MaxPot = maxPot;
		levelInfo.AvgPayout = avgPayout;
		levelInfo.MinBet = minBet;
		levelInfo.MaxBet = maxBet;
		levelInfo.MinWinnableBet = minWinnableBet;
		levelInfo.MaxWinnableBet = maxWinnableBet;
		levelInfo.TimeFrom = from;
		levelInfo.TimeTo = to;
		levelInfo.Timezone = timezone;
		levelInfo.WeekDays = weekdays;
		levelInfo.Name = name;
		levelInfo.AutoActionAfterWin = AutoActionAfterWin;
		levelInfo.AutoActionAfterWinOffsetSec = AutoActionAfterWinOffsetSec;
		levelInfo.MinTimeBetweenWinsSec = MinTimeBetweenWinsSec;
		levelInfo.bUseFixedPaidIn = bUseFixedPaidIn;
		levelInfo.bKeepPotAboveMinPot = bKeepPotAboveMinPot;
		return levelInfo;
	}
};

TEST_F(JackpotLevelReconfiguredEventDtoTest, CreateJackpotLevelReconfiguredEventDto)
{
	// GIVEN
	const auto levelInfo = GetJackpotLevelInfo();

	// WHEN
	const JackpotLevelInfoBase dto = static_cast<JackpotLevelInfoBase>(JackpotLevelReconfiguredEventDto(levelInfo));

	// THEN
	EXPECT_EQ(dto, levelInfo);
}

TEST_F(JackpotLevelReconfiguredEventDtoTest, JackpotLevelReconfiguredEventDtoToJson)
{
	// GIVEN
	JackpotLevelInfoBase levelInfo = GetJackpotLevelInfo();

	// WHEN
	auto json = JackpotLevelReconfiguredEventDto(levelInfo).ToJSON();

	// THEN
	EXPECT_EQ(json["levelInfo"], levelInfo.ToJSON());
}

TEST_F(JackpotLevelReconfiguredEventDtoTest, DtoToJsonToDto)
{
	// GIVEN
	auto dto = JackpotLevelReconfiguredEventDto(GetJackpotLevelInfo());

	// WHEN
	auto json = dto.ToJSON();
	auto dto2 = JackpotLevelReconfiguredEventDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}

TEST_F(JackpotLevelReconfiguredEventDtoTest, DtoToJsonToDtoWithRecipientsAndEventClientGroupID)
{
	// GIVEN
	auto dto = JackpotLevelReconfiguredEventDto(GetJackpotLevelInfo());
	dto.Recipients = { crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4() };
	dto.EventClientGroupID = crypto::GenRandUUIDv4();

	// WHEN
	auto json = dto.ToJSON();
	auto dto2 = JackpotLevelReconfiguredEventDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}

TEST_F(JackpotLevelReconfiguredEventDtoTest, JsonToDtoToJson)
{
	// GIVEN
	auto json = JackpotLevelReconfiguredEventDto(GetJackpotLevelInfo()).ToJSON();

	// WHEN
	auto dto = JackpotLevelReconfiguredEventDto::FromJSON(json);
	auto json2 = dto.ToJSON();

	// THEN
	EXPECT_EQ(json, json2);
}