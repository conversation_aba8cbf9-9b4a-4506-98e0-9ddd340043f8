#include <gtest/gtest.h>

#include <format>

#include "Cryptography.h"
#include "jackpot/dto/ClientInfoDto.h"

class ClientInfoDtoTest : public ::testing::Test
{
   protected:
	std::string ClientID = crypto::GenRandUUIDv4();
	std::string ClientGroupID1 = crypto::GenRandUUIDv4();
	std::string ClientGroupID2 = crypto::GenRandUUIDv4();
	std::unordered_set<std::string> ClientGroupIDs = { ClientGroupID1, ClientGroupID2 };
	bool OnlyConnected = true;
	std::unordered_set<EJackpotClientType> ClientTypes = { EJackpotClientType::Client };
	std::vector<std::string> PathToClient = { crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4() };
	std::unordered_map<std::string, EJackpotLevelStatus> ClientLevelsStatus = { { crypto::GenRandUUIDv4(), EJackpotLevelStatus::Counting },
		                                                                        { crypto::GenRandUUIDv4(), EJackpotLevelStatus::Counting },
		                                                                        { crypto::GenRandUUIDv4(), EJackpotLevelStatus::Disabled } };
};

TEST_F(ClientInfoDtoTest, ConstructorTest)
{
	// when
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client);

	// then
	EXPECT_EQ(dto.ClientID, ClientID);
	EXPECT_EQ(dto.ClientGroupIDs, ClientGroupIDs);
	EXPECT_EQ(dto.IsConnected, OnlyConnected);
	EXPECT_EQ(dto.PathToClient, PathToClient);
	EXPECT_EQ(dto.ClientType, EJackpotClientType::Client);
}

TEST_F(ClientInfoDtoTest, DtoToJson)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);

	// when
	json json = dto.ToJSON();

	// then
	EXPECT_EQ(json["clientId"].get<std::string>(), ClientID);
	EXPECT_EQ(json["clientGroupIds"].size(), 2);
	EXPECT_TRUE(ClientGroupIDs.contains(json["clientGroupIds"][0].get<std::string>()));
	EXPECT_TRUE(ClientGroupIDs.contains(json["clientGroupIds"][1].get<std::string>()));
	EXPECT_EQ(json["isConnected"].get<bool>(), OnlyConnected);
	EXPECT_EQ(json["pathToClient"].get<std::string>(), std::format("{}/{}", PathToClient[0], PathToClient[1]));
	EXPECT_EQ(json["clientType"].get<std::string>(), "Client");
	EXPECT_EQ(json["clientLevelsStatus"].size(), 3);
	for (const auto& [levelId, status] : ClientLevelsStatus) EXPECT_EQ(json["clientLevelsStatus"][levelId].get<std::string>(), status._to_string());
}

TEST_F(ClientInfoDtoTest, JsonToDto)
{
	// given
	json groupIdsJson(json::value_t::array);
	for (const auto& clientGroupID : ClientGroupIDs) groupIdsJson.push_back(clientGroupID);
	json clientLevelsStatusJson(json::value_t::object);
	for (const auto& [levelId, status] : ClientLevelsStatus) clientLevelsStatusJson[levelId] = status._to_string();

	json json(json::value_t::object);
	json["clientId"] = ClientID;
	json["clientGroupIds"] = std::move(groupIdsJson);
	json["isConnected"] = OnlyConnected;
	json["pathToClient"] = std::format("{}/{}", PathToClient[0], PathToClient[1]);
	json["clientType"] = "Client";
	json["clientLevelsStatus"] = std::move(clientLevelsStatusJson);

	// when
	auto dto = ClientInfoDto::FromJSON(json);

	// then
	EXPECT_EQ(dto.ClientID, ClientID);
	EXPECT_EQ(dto.ClientGroupIDs, ClientGroupIDs);
	EXPECT_EQ(dto.IsConnected, OnlyConnected);
	EXPECT_EQ(dto.PathToClient, PathToClient);
	EXPECT_EQ(dto.ClientType, EJackpotClientType::Client);
	EXPECT_EQ(dto.ClientLevelsStatus, ClientLevelsStatus);
}

TEST_F(ClientInfoDtoTest, DtoToJsonToDto)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);

	// when
	auto dto2 = ClientInfoDto::FromJSON(dto.ToJSON());

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(ClientInfoDtoTest, MatchWithEmptyFilter)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter;

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, MatchWithClientID)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientIDs = { ClientID } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, NoMatchWithWrongClientID)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientIDs = { crypto::GenRandUUIDv4() } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(ClientInfoDtoTest, MatchWithOneClientGroupID)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientGroupIDs = { ClientGroupID1, crypto::GenRandUUIDv4() } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, MatchWithAllClientGroupIDs)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientGroupIDs = ClientGroupIDs };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, NoMatchWithWrongClientGroupID)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientGroupIDs = { crypto::GenRandUUIDv4() } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(ClientInfoDtoTest, MatchWithOnlyConnected)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .OnlyConnected = OnlyConnected };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, NoMatchWithNotConnected)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, false, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .OnlyConnected = true };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(ClientInfoDtoTest, MatchWithClientType)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::Client, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientTypes = { EJackpotClientType::Client, EJackpotClientType::ServerChild } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(ClientInfoDtoTest, NoMatchWithWrongClientType)
{
	// given
	ClientInfoDto dto(ClientID, ClientGroupIDs, OnlyConnected, PathToClient, EJackpotClientType::ServerChild, ClientLevelsStatus);
	ClientInfoFilter filter = { .ClientTypes = { EJackpotClientType::Client } };

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}
