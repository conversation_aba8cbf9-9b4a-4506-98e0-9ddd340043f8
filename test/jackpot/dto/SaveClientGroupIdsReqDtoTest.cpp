#include <gtest/gtest.h>

#include "TestUtils.h"
#include "Cryptography.h"
#include "jackpot/dto/SaveClientGroupIdsReqDto.h"

class SaveClientGroupIdsReqDtoTest : public ::testing::Test
{
   protected:
	std::string ClientID = crypto::GenRandUUIDv4();
	std::unordered_set<std::string> ClientGroupIds = { crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4() };

	// Helper function to set common properties
	json GetFullJson()
	{
		json clientGroupIdsJson(json::value_t::array);
		for (const auto& groupId : ClientGroupIds) clientGroupIdsJson.push_back(groupId);

		json json(json::value_t::object);
		json["clientId"] = ClientID;
		json["clientGroupIds"] = std::move(clientGroupIdsJson);
		return json;
	}

	static SaveClientGroupIdsReqDto GetDtoFromJson(const json& json)
	{
		SaveClientGroupIdsReqDto dto;
		dto.LoadSubconfiguration(json);
		return dto;
	}
};

TEST_F(SaveClientGroupIdsReqDtoTest, ThrowWhenClientIdIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json.erase("clientId");

	// when and then
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); }, "Missing required member 'clientId'");
}

TEST_F(SaveClientGroupIdsReqDtoTest, DontThrowWhenClientGroupIdsIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json.erase("clientGroupIds");

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_TRUE(dto.ClientGroupIDs.empty());
}

TEST_F(SaveClientGroupIdsReqDtoTest, DtoToJsonToDto)
{
	// GIVEN
	SaveClientGroupIdsReqDto dto = GetDtoFromJson(GetFullJson());

	// WHEN
	auto json = dto.ToJSON();
	auto dto2 = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.ClientID, dto2.ClientID);
	EXPECT_EQ(dto.ClientGroupIDs, dto2.ClientGroupIDs);
	// TODO: check why comparison of dto and dto2 fails
}
