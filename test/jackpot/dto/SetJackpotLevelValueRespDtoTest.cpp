#include <gtest/gtest.h>

#include "Cryptography.h"
#include "jackpot/dto/SetJackpotLevelValueRespDto.h"

class SetJackpotLevelValueRespDtoTest : public ::testing::Test
{
   protected:
	std::string levelId = crypto::GenRandUUIDv4();
	double potChangedBy = 123.45;
	double backPotChangedBy = -12.3;
};

TEST_F(SetJackpotLevelValueRespDtoTest, CreateSetJackpotLevelValueRespDto)
{
	// WHEN
	SetJackpotLevelValueRespDto dto = { levelId, potChangedBy, backPotChangedBy };

	// THEN
	EXPECT_EQ(dto.LevelID, levelId);
	EXPECT_EQ(dto.PotChangedBy, potChangedBy);
	EXPECT_EQ(dto.BackPotChangedBy, backPotChangedBy);
}

TEST_F(SetJackpotLevelValueRespDtoTest, ConvertSetJackpotLevelValueRespDtoToJson)
{
	// GIVEN
	SetJackpotLevelValueRespDto dto = { levelId, potChangedBy, backPotChangedBy };

	// WHEN
	json json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["levelId"].get<std::string>(), levelId);
	EXPECT_EQ(json["potChangedBy"].get<double>(), potChangedBy);
	EXPECT_EQ(json["backPotChangedBy"].get<double>(), backPotChangedBy);
}

TEST_F(SetJackpotLevelValueRespDtoTest, DtoToJsonToDto)
{
	// GIVEN
	SetJackpotLevelValueRespDto dto = { levelId, potChangedBy, backPotChangedBy };

	// WHEN
	json json = dto.ToJSON();
	SetJackpotLevelValueRespDto dto2 = SetJackpotLevelValueRespDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}
