#include <gtest/gtest.h>

#include "Cryptography.h"
#include "Timing.h"
#include "jackpot/dto/ListJackpotHistoryRespDto.h"

class ListJackpotHistoryRespDtoTest : public ::testing::Test
{
   protected:
	std::string levelId1 = crypto::GenRandUUIDv4();
	std::string levelId2 = crypto::GenRandUUIDv4();
	std::string levelName1 = "level1";
	std::string levelName2 = "level2";

	// history
	std::string ClientId = crypto::GenRandUUIDv4();
	std::string ClientLocation = "clientLocation";
	double Pot = 100.0;
	uint64_t Created = ytime::GetSystemTimeMsec();
	uint64_t Updated = Created + 1000;
	std::string Description = "description";
	EHistoryEntryType Type = EHistoryEntryType::WonPending;
	uint32_t WinCounter = 1;
	std::string BetId1 = crypto::GenRandUUIDv4();
	std::string PlayerName1 = "player1";
	std::string RoundReference1 = crypto::GenRandUUIDv4();
	double amount1 = 10.1;
	uint64_t credits1 = 101;
	std::string BetId2 = crypto::GenRandUUIDv4();
	std::string PlayerName2 = "player2";
	std::string RoundReference2 = crypto::GenRandUUIDv4();
	double amount2 = 20.2;
	uint64_t credits2 = 202;
	WonBetDto wonBet1 = { BetId1, PlayerName1, RoundReference1, ClientLocation, ClientId, amount1, credits1 };
	WonBetDto wonBet2 = { BetId2, PlayerName2, RoundReference2, ClientLocation, ClientId, amount2, credits2 };
	std::vector<WonBetDto> WonBets = { wonBet1, wonBet2 };

	// Helper function to set common properties
	ListLevelHistoryDto GetListLevelHistoryDto(const std::string& levelId, const std::string& levelName)
	{
		std::list<JackpotHistoryDto> history = { { crypto::GenRandUUIDv4(), levelId, ClientId, Pot, Created, Updated, Type, WonBets, WinCounter },
			                                     { crypto::GenRandUUIDv4(), levelId, ClientId, Pot, Created, Updated, Type, WonBets, WinCounter } };
		return ListLevelHistoryDto(levelId, levelName, history);
	}
};

TEST_F(ListJackpotHistoryRespDtoTest, CreateListJackpotLevelsRespDto)
{
	// WHEN
	auto level1 = GetListLevelHistoryDto(levelId1, levelName1);
	auto level2 = GetListLevelHistoryDto(levelId2, levelName2);
	std::list<ListLevelHistoryDto> Levels = { level1, level2 };
	ListJackpotHistoryRespDto dto = { Levels };

	// THEN
	EXPECT_EQ(dto.Levels.size(), 2);
}

TEST_F(ListJackpotHistoryRespDtoTest, ConvertListJackpotLevelsRespDtoToJson)
{
	// GIVEN
	auto level1 = GetListLevelHistoryDto(levelId1, levelName1);
	auto level2 = GetListLevelHistoryDto(levelId2, levelName2);
	std::list<ListLevelHistoryDto> Levels = { level1, level2 };
	ListJackpotHistoryRespDto dto = { Levels };

	json json1 = level1.ToJSON();
	json json2 = level2.ToJSON();

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["levels"].size(), 2);
	EXPECT_EQ(json["levels"][0], json1);
	EXPECT_EQ(json["levels"][1], json2);
}

TEST_F(ListJackpotHistoryRespDtoTest, CreateListLevelDataDto)
{
	// GIVEN

	// WHEN
	auto dto = GetListLevelHistoryDto(levelId1, levelName1);

	// THEN
	EXPECT_EQ(dto.ID, levelId1);
	EXPECT_EQ(dto.History.size(), 2);
}

TEST_F(ListJackpotHistoryRespDtoTest, ConvertListLevelDataDtoToJson)
{
	// GIVEN
	auto dto = GetListLevelHistoryDto(levelId1, levelName1);

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["id"], levelId1);
	EXPECT_EQ(json["history"].size(), 2);
	EXPECT_EQ(json["history"][0], dto.History.front().ToJSON());
	EXPECT_EQ(json["history"][1], dto.History.back().ToJSON());
}

TEST_F(ListJackpotHistoryRespDtoTest, JsonToDtoToJson)
{
	// GIVEN
	auto dto = ListJackpotHistoryRespDto({ GetListLevelHistoryDto(levelId1, levelName1), GetListLevelHistoryDto(levelId2, levelName2) });

	// WHEN
	auto json = dto.ToJSON();
	auto dto2 = ListJackpotHistoryRespDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}
