#include <gtest/gtest.h>

#include "Cryptography.h"
#include "TestUtils.h"
#include "jackpot/dto/SaveJackpotLevelReqDto.h"

class SaveJackpotLevelReqDtoTest : public ::testing::Test
{
   protected:
	std::string id = crypto::GenRandUUIDv4();
	EJackpotLevelType type = EJackpotLevelType::Mystery;
	EJackpotPotType potType = EJackpotPotType::Progressive;
	std::string currency = "USD";
	std::string clientGroupId = crypto::GenRandUUIDv4();
	double increment = 0.01;
	std::string externalTriggerType1 = "44Catcher";
	ExternalTriggerTypeValues externalTriggerTypeValues1 = { 0.235, 0.000012 };
	std::string externalTriggerType2 = "SuitedMatch";
	ExternalTriggerTypeValues externalTriggerTypeValues2 = { 0.266, 0.0000098 };
	std::unordered_map<std::string, ExternalTriggerTypeValues> externalTriggerTypeData = { { externalTriggerType1, externalTriggerTypeValues1 },
		                                                                                   { externalTriggerType2, externalTriggerTypeValues2 } };
	double minPot = 100;
	double maxPot = 10000;
	double avgPayout = 1000;
	double minBet = 1.5;
	double maxBet = 100;
	double minWinnableBet = 3.5;
	double maxWinnableBet = 89.9;
	uint16_t from = 800;
	uint16_t to = 1600;
	std::string timezone = "Europe/Ljubljana";
	Bitflag<EWeekday, uint8_t> weekdays = Bitflag<EWeekday, uint8_t>(0b01101001);
	std::string name = "test";
	EActionAfterWin AutoActionAfterWin = EActionAfterWin::Reset;
	uint32_t AutoActionAfterWinOffsetSec = 50;
	uint32_t MinTimeBetweenWinsSec = 2;
	bool useFixedPaidIn = true;
	bool keepPotAboveMinPot = true;
	std::string templateId = crypto::GenRandUUIDv4();
	std::string levelReference = crypto::GenRandUUIDv4();
	bool demo = true;

	// Helper function to set common properties
	json GetFullJson() const
	{
		json weekDaysJson(json::value_t::array);
		for (const auto weekDay : EWeekday::_values())
		{
			if (weekdays.HasFlag(weekDay))
				weekDaysJson.push_back(weekDay._to_string());
		}

		json val(json::value_t::object);
		val[jackpotlevelkey::ID] = id;
		val[jackpotlevelkey::TYPE] = type;
		val[jackpotlevelkey::POT_TYPE] = potType;
		val[jackpotlevelkey::CURRENCY] = currency;
		val[jackpotlevelkey::CLIENT_GROUP_ID] = clientGroupId;
		val[jackpotlevelkey::INCREMENT] = increment;
		val[jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA] = externalTriggerTypeData;
		val[jackpotlevelkey::MIN_POT] = minPot;
		val[jackpotlevelkey::MAX_POT] = maxPot;
		val[jackpotlevelkey::AVG_PAYOUT] = avgPayout;
		val[jackpotlevelkey::MIN_BET] = minBet;
		val[jackpotlevelkey::MAX_BET] = maxBet;
		val[jackpotlevelkey::MIN_WINNABLE_BET] = minWinnableBet;
		val[jackpotlevelkey::MAX_WINNABLE_BET] = maxWinnableBet;
		val[jackpotlevelkey::TIME_FROM] = from;
		val[jackpotlevelkey::TIME_TO] = to;
		val[jackpotlevelkey::TIMEZONE] = timezone;
		val[jackpotlevelkey::WEEK_DAYS] = std::move(weekDaysJson);
		val[jackpotlevelkey::NAME] = name;
		val[jackpotlevelkey::AUTO_ACTION_AFTER_WIN] = AutoActionAfterWin;
		val[jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET] = AutoActionAfterWinOffsetSec;
		val[jackpotlevelkey::MIN_TIME_BETWEEN_WINS] = MinTimeBetweenWinsSec;
		val[jackpotlevelkey::USE_FIXED_PAID_IN] = useFixedPaidIn;
		val[jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT] = keepPotAboveMinPot;
		val[jackpotlevelkey::TEMPLATE_ID] = templateId;
		val[jackpotlevelkey::LEVEL_REFERENCE] = levelReference;
		val[jackpotlevelkey::DEMO] = demo;
		val[jackpotlevelkey::CUSTOM_DATA] = json::object();

		json outerJson(json::value_t::object);
		outerJson["levelInfo"] = std::move(val);
		return outerJson;
	}

	static SaveJackpotLevelReqDto GetDtoFromJson(const json& json)
	{
		SaveJackpotLevelReqDto dto;
		dto.LoadSubconfiguration(json);
		return dto;
	}
};

TEST_F(SaveJackpotLevelReqDtoTest, ConvertJsonToDto)
{
	// given
	json json = GetFullJson();

	// when
	auto dto = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto.JackpotLevelInfo, JackpotLevelInfoBase::FromJSON(json["levelInfo"]));
}

TEST_F(SaveJackpotLevelReqDtoTest, ConvertMinJsonToDto)
{
	// given
	json val(json::value_t::object);
	val[jackpotlevelkey::TYPE] = type._to_string();
	val[jackpotlevelkey::POT_TYPE] = potType._to_string();
	val[jackpotlevelkey::CURRENCY] = currency;
	val[jackpotlevelkey::CLIENT_GROUP_ID] = clientGroupId;
	val[jackpotlevelkey::MIN_POT] = minPot;
	val[jackpotlevelkey::MAX_POT] = maxPot;
	val[jackpotlevelkey::TIMEZONE] = timezone;
	val[jackpotlevelkey::NAME] = name;

	json json(json::value_t::object);
	json["levelInfo"] = std::move(val);

	// when
	auto dto = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto.JackpotLevelInfo.ID, "");    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.Type, type);
	EXPECT_EQ(dto.JackpotLevelInfo.PotType, potType);
	EXPECT_EQ(dto.JackpotLevelInfo.Currency._to_string(), currency);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.ClientGroupID, clientGroupId);
	EXPECT_EQ(dto.JackpotLevelInfo.Increment, 0);    // default value
	EXPECT_TRUE(dto.JackpotLevelInfo.ExternalTriggerTypeData.empty());    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MinPot, minPot);
	EXPECT_EQ(dto.JackpotLevelInfo.MaxPot, maxPot);
	EXPECT_EQ(dto.JackpotLevelInfo.AvgPayout, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MinBet, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MaxBet, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MinWinnableBet, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MaxWinnableBet, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.TimeFrom, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.TimeTo, 0);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.Timezone, timezone);
	EXPECT_EQ(dto.JackpotLevelInfo.WeekDays, dto.JackpotLevelInfo.WeekDays.All());    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.Name, name);
	EXPECT_EQ(dto.JackpotLevelInfo.AutoActionAfterWin, EActionAfterWin::Nothing);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.AutoActionAfterWinOffsetSec, 50);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.MinTimeBetweenWinsSec, 7200);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.bUseFixedPaidIn, false);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.bKeepPotAboveMinPot, false);    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.TemplateID, "");    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.LevelReference, "");    // default value
	EXPECT_EQ(dto.JackpotLevelInfo.bDemo, false);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DtoToJsonToDto)
{
	// given
	auto dto = GetDtoFromJson(GetFullJson());

	// when
	json json = dto.ToJSON();
	const auto dto2 = GetDtoFromJson(json);

	// then
	EXPECT_EQ(dto, dto2);
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenIdIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::ID);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.ID, "");    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenTypeIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::TYPE);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); }, "Missing required member 'levelInfo." + std::string(jackpotlevelkey::TYPE) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenPotTypeIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::POT_TYPE);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); },
	                                         "Missing required member 'levelInfo." + std::string(jackpotlevelkey::POT_TYPE) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenIncrementIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::INCREMENT);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.Increment, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenHiddenIncrementIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::INCREMENT_VISIBLE_REL);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.IncrementVisibleRel, std::nullopt);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenExternalTriggerTypeDataIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_TRUE(dto.JackpotLevelInfo.ExternalTriggerTypeData.empty());    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenPaidInInExternalTriggerTypeDataIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"][jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA][externalTriggerType1].erase("paidIn");

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.ExternalTriggerTypeData[externalTriggerType1].PaidIn, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenWinProbabilityInExternalTriggerTypeDataIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"][jackpotlevelkey::EXTERNAL_TRIGGER_TYPE_DATA][externalTriggerType1].erase("winProbability");

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.ExternalTriggerTypeData[externalTriggerType1].WinProbability, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenNameIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::NAME);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); }, "Missing required member 'levelInfo." + std::string(jackpotlevelkey::NAME) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenCurrencyIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::CURRENCY);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.Currency, ECurrency::EUR);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenClientGroupIdIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::CLIENT_GROUP_ID);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); },
	                                         "Missing required member 'levelInfo." + std::string(jackpotlevelkey::CLIENT_GROUP_ID) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenMinPotIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MIN_POT);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); },
	                                         "Missing required member 'levelInfo." + std::string(jackpotlevelkey::MIN_POT) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenMaxPotIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MAX_POT);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); },
	                                         "Missing required member 'levelInfo." + std::string(jackpotlevelkey::MAX_POT) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenAvgPayoutIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::AVG_PAYOUT);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.AvgPayout, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenMinBetIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MIN_BET);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.MinBet, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenMaxBetIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MAX_BET);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.MaxBet, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenMinWinnableBetIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MIN_WINNABLE_BET);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.MinWinnableBet, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenMaxWinnableBetIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MAX_WINNABLE_BET);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.MaxWinnableBet, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenFromIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::TIME_FROM);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.TimeFrom, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenToIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::TIME_TO);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.TimeTo, 0);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, ThrowWhenTimezoneIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::TIMEZONE);

	// WHEN and THEN
	testutils::ExpectFuncThrows<SchemaError>([&json] { return GetDtoFromJson(json); },
	                                         "Missing required member 'levelInfo." + std::string(jackpotlevelkey::TIMEZONE) + "'");
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenWeekDaysIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::WEEK_DAYS);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.WeekDays, dto.JackpotLevelInfo.WeekDays.All());    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenAutoActionAfterWinIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::AUTO_ACTION_AFTER_WIN);

	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.AutoActionAfterWin, EActionAfterWin::Nothing);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenAutoActionAfterWinOffsetIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::AUTO_ACTION_AFTER_WIN_OFFSET);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.AutoActionAfterWinOffsetSec, 50);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenMinTimeBetweenWinsIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::MIN_TIME_BETWEEN_WINS);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.MinTimeBetweenWinsSec, 7200);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenUseFixedPaidInIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::USE_FIXED_PAID_IN);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.bUseFixedPaidIn, false);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenKeepPotAboveMinPotIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::KEEP_POT_ABOVE_MIN_POT);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.bKeepPotAboveMinPot, false);    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenTemplateIdIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::TEMPLATE_ID);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.TemplateID, "");    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenLevelReferenceIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::LEVEL_REFERENCE);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.LevelReference, "");    // default value
}

TEST_F(SaveJackpotLevelReqDtoTest, DontThrowWhenDemoIsMissing)
{
	// GIVEN
	json json = GetFullJson();
	json["levelInfo"].erase(jackpotlevelkey::DEMO);

	// WHEN
	auto dto = GetDtoFromJson(json);

	// THEN
	EXPECT_EQ(dto.JackpotLevelInfo.bDemo, false);    // default value
}
