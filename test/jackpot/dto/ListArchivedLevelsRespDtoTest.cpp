#include <gtest/gtest.h>

#include "Cryptography.h"
#include "jackpot/dto/ListArchivedLevelsRespDto.h"

class ListArchivedLevelsRespDtoTest : public ::testing::Test
{
   protected:
	std::string serverId1 = crypto::GenRandUUIDv4();
	std::string serverId2 = crypto::GenRandUUIDv4();
	std::string levelId1 = crypto::GenRandUUIDv4();
	std::string levelId2 = crypto::GenRandUUIDv4();
	int64_t TimezoneOffset = ytime::GetUTCOffsetSeconds() / 60;
	CountersDto Counters1 = CountersDto(10, 210.2, 81.2, 80.3, 99.0);
	CountersDto Counters2 = CountersDto(20, 220.2, 82.2, 81.3, 100.0);

	// Helper function to set common properties
	ListLevelDataDto GetListLevelDataDto(const std::string& id, const std::string& serverId, CountersDto counters)
	{
		JackpotLevelInfoBase levelInfo;
		levelInfo.ID = id;
		levelInfo.Timezone = "Europe/Ljubljana";

		const auto jackpotLevel = JackpotLevelDto(levelInfo, serverId);

		return ListLevelDataDto(jackpotLevel, counters, {}, {});
	}
};

TEST_F(ListArchivedLevelsRespDtoTest, CreateDto)
{
	// WHEN
	auto level1 = GetListLevelDataDto(levelId1, serverId1, Counters1);
	auto level2 = GetListLevelDataDto(levelId2, serverId2, Counters2);
	std::vector<ListLevelDataDto> Levels = { level1, level2 };
	ListArchivedLevelsRespDto dto = { Levels };

	// THEN
	EXPECT_EQ(dto.Levels.size(), 2);
	EXPECT_EQ(dto.Levels[0].Level.Info.ID, levelId1);
	EXPECT_EQ(dto.Levels[1].Level.Info.ID, levelId2);
	EXPECT_EQ(dto.Levels[0].Level.ServerID, serverId1);
	EXPECT_EQ(dto.Levels[1].Level.ServerID, serverId2);
	EXPECT_EQ(dto.Levels[0].Counters, Counters1);
	EXPECT_EQ(dto.Levels[1].Counters, Counters2);
}

TEST_F(ListArchivedLevelsRespDtoTest, DtoToJson)
{
	// GIVEN
	auto level1 = GetListLevelDataDto(levelId1, serverId1, Counters1);
	auto level2 = GetListLevelDataDto(levelId2, serverId2, Counters2);
	std::vector<ListLevelDataDto> Levels = { level1, level2 };
	ListArchivedLevelsRespDto dto = { Levels };
	json json1 = level1;
	json json2 = level2;

	// WHEN
	json j = dto;

	// THEN
	EXPECT_EQ(j["levels"].size(), 2);
	EXPECT_EQ(j["levels"][0], json1);
	EXPECT_EQ(j["levels"][1], json2);
}

TEST_F(ListArchivedLevelsRespDtoTest, DtoToJsonToDto)
{
	// GIVEN
	auto level1 = GetListLevelDataDto(levelId1, serverId1, Counters1);
	auto level2 = GetListLevelDataDto(levelId2, serverId2, Counters2);
	std::vector<ListLevelDataDto> Levels = { level1, level2 };
	ListArchivedLevelsRespDto dto = { Levels };

	// WHEN
	const json j = dto;
	const auto dto2 = j.get<ListArchivedLevelsRespDto>();

	// THEN
	EXPECT_EQ(dto, dto2);
}

TEST_F(ListArchivedLevelsRespDtoTest, DtoToJsonToDto2)
{
	// GIVEN
	auto level1 = GetListLevelDataDto(levelId1, serverId1, Counters1);
	auto level2 = GetListLevelDataDto(levelId2, serverId2, Counters2);
	std::vector<ListLevelDataDto> Levels = { level1, level2 };
	ListArchivedLevelsRespDto dto = { Levels };

	// WHEN
	const json j = dto.ToJSON();
	const auto dto2 = ListArchivedLevelsRespDto::FromJSON(j);

	// THEN
	EXPECT_EQ(dto, dto2);
}
