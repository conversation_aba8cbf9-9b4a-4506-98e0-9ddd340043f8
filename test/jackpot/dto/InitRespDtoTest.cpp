#include <gtest/gtest.h>

#include "Cryptography.h"
#include "jackpot/dto/InitRespDto.h"

class InitRespDtoTest : public ::testing::Test
{
   protected:
	InitBetDto betData1 = { "bet1", crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), EJackpotBetStatus::Lost };
	InitBetDto betData2 = { "bet2", crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), EJackpotBetStatus::WonPendingConfirmation };
	InitBetDto betData3 = { "bet3", crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), EJackpotBetStatus::Won };
	InitBetDto betData4 = { "bet4", crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), crypto::GenRandUUIDv4(), EJackpotBetStatus::Lost };
	InitLevelDataDto levelData1 = { "level1", EJackpotLevelStatus::Counting, EJackpotLevelStatus::Counting, ECurrency::EUR, { betData1, betData2 }, std::nullopt };
	InitLevelDataDto levelData2 = { "level1", EJackpotLevelStatus::Counting, EJackpotLevelStatus::Counting, ECurrency::EUR, { betData3, betData4 }, 12345 };
	std::vector<InitLevelDataDto> LevelsData = { levelData1, levelData2 };
	ECurrency ClientCurrency = ECurrency::EUR;
	double Denomination = 0.0;
	std::string Version = "1.2.3";
	std::string ExternalTriggerType = "44Catcher";
};

TEST_F(InitRespDtoTest, CreateInitDto)
{
	// WHEN
	InitRespDto dto = { LevelsData, ClientCurrency, Denomination, Version, ExternalTriggerType };

	// THEN
	EXPECT_EQ(dto.LevelsData.size(), 2);
	EXPECT_EQ(dto.LevelsData[0].PendingBetsData.size(), 2);
	EXPECT_EQ(dto.LevelsData[1].PendingBetsData.size(), 2);
	EXPECT_EQ(dto.LevelsData[0].PendingBetsData[0], betData1);
	EXPECT_EQ(dto.LevelsData[0].PendingBetsData[1], betData2);
	EXPECT_EQ(dto.LevelsData[1].PendingBetsData[0], betData3);
	EXPECT_EQ(dto.LevelsData[1].PendingBetsData[1], betData4);
	EXPECT_EQ(dto.LevelsData[0].ExpectedWin, std::nullopt);
	EXPECT_EQ(dto.LevelsData[1].ExpectedWin, 12345);
	EXPECT_EQ(dto.Denomination, Denomination);
	EXPECT_EQ(dto.ServerVersion, Version);
	EXPECT_EQ(dto.ExternalTriggerType, ExternalTriggerType);
	EXPECT_EQ(dto.ClientCurrency, ClientCurrency);
}

TEST_F(InitRespDtoTest, ConvertInitDtoToJson)
{
	// GIVEN
	InitRespDto dto = { LevelsData, ClientCurrency, Denomination, Version, ExternalTriggerType };

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["levels"].size(), 2);
	EXPECT_EQ(json["levels"][0], levelData1.ToJSON());
	EXPECT_EQ(json["levels"][1], levelData2.ToJSON());
	EXPECT_EQ(json["denomination"].get<double>(), Denomination);
	EXPECT_EQ(json["serverVersion"].get<std::string>(), Version);
	EXPECT_EQ(json["externalTriggerType"].get<std::string>(), ExternalTriggerType);
	EXPECT_EQ(json["clientCurrency"].get<std::string>(), ClientCurrency._to_string());
}

TEST_F(InitRespDtoTest, DtoToJsonToDto)
{
	// GIVEN
	InitRespDto dto = { LevelsData, ClientCurrency, Denomination, Version, ExternalTriggerType };

	// WHEN
	auto json = dto.ToJSON();
	auto dto2 = InitRespDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}

TEST_F(InitRespDtoTest, ConvertInitDtoToJsonNoExternalTriggerType)
{
	// GIVEN
	InitRespDto dto = { LevelsData, ClientCurrency, Denomination, Version };

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["levels"].size(), 2);
	EXPECT_EQ(json["levels"][0], levelData1.ToJSON());
	EXPECT_EQ(json["levels"][1], levelData2.ToJSON());
	EXPECT_EQ(json["denomination"].get<double>(), Denomination);
	EXPECT_EQ(json["serverVersion"].get<std::string>(), Version);
	EXPECT_EQ(json["externalTriggerType"].get<std::string>(), "");
	EXPECT_EQ(json["clientCurrency"].get<std::string>(), ClientCurrency._to_string());
}

TEST_F(InitRespDtoTest, InitRespDtoFromEmptyJson)
{
	// GIVEN
	const json json(json::value_t::object);

	// WHEN
	auto dto = InitRespDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto.LevelsData.size(), 0);
	EXPECT_EQ(dto.Denomination, 0.0);
	EXPECT_EQ(dto.ServerVersion, "");
	EXPECT_EQ(dto.ExternalTriggerType, "");
	EXPECT_EQ(dto.ClientCurrency, ECurrency::NONE);
}

TEST_F(InitRespDtoTest, CreateInitLevelDataDto)
{
	// GIVEN
	std::string levelId = "level1";
	EJackpotLevelStatus status = EJackpotLevelStatus::Counting;
	std::optional<EJackpotLevelStatus> clientStatus = EJackpotLevelStatus::Counting;
	ECurrency currency = ECurrency::EUR;
	std::vector betsData = { betData1, betData2 };
	std::optional<double> expectedWin = 12345;

	// WHEN
	InitLevelDataDto dto = { levelId, status, clientStatus, currency, betsData, expectedWin };

	// THEN
	EXPECT_EQ(dto.LevelID, levelId);
	EXPECT_EQ(dto.LevelStatus, status);
	EXPECT_EQ(dto.ClientStatus, clientStatus);
	EXPECT_EQ(dto.LevelCurrency, currency);
	EXPECT_EQ(dto.PendingBetsData.size(), 2);
	EXPECT_EQ(dto.PendingBetsData[0], betData1);
	EXPECT_EQ(dto.PendingBetsData[1], betData2);
	EXPECT_EQ(dto.ExpectedWin, expectedWin);
}

TEST_F(InitRespDtoTest, ConvertInitLevelDataDtoToJson)
{
	// GIVEN
	std::string levelId = "level1";
	EJackpotLevelStatus status = EJackpotLevelStatus::Counting;
	std::optional<EJackpotLevelStatus> clientStatus = EJackpotLevelStatus::Counting;
	ECurrency currency = ECurrency::EUR;
	std::vector<InitBetDto> betsData = { betData1, betData2 };
	double expectedWin = 12345;
	InitLevelDataDto dto = { levelId, status, clientStatus, currency, betsData, expectedWin };

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["levelId"].get<std::string>(), levelId);
	EXPECT_EQ(json["levelStatus"].get<std::string>(), status._to_string());
	EXPECT_EQ(json["clientStatus"].get<std::string>(), clientStatus.value()._to_string());
	EXPECT_EQ(json["levelCurrency"].get<std::string>(), currency._to_string());
	EXPECT_EQ(json["pendingBets"].size(), 2);
	EXPECT_EQ(json["pendingBets"][0], betData1.ToJSON());
	EXPECT_EQ(json["pendingBets"][1], betData2.ToJSON());
	EXPECT_EQ(json["expectedWin"].get<double>(), expectedWin);
}

TEST_F(InitRespDtoTest, CreateInitBetDto)
{
	// GIVEN
	std::string betId = "bet1";
	std::string betReference = crypto::GenRandUUIDv4();
	std::string playerReference = crypto::GenRandUUIDv4();
	std::string roundReference = crypto::GenRandUUIDv4();
	EJackpotBetStatus status = EJackpotBetStatus::Lost;

	// WHEN
	InitBetDto dto = { betId, betReference, playerReference, roundReference, status };

	// THEN
	EXPECT_EQ(dto.BetID, betId);
	EXPECT_EQ(dto.Status, status);
	EXPECT_EQ(dto.BetReference, betReference);
	EXPECT_EQ(dto.PlayerReference, playerReference);
	EXPECT_EQ(dto.RoundReference, roundReference);
}

TEST_F(InitRespDtoTest, ConvertInitBetDtoToJson)
{
	// GIVEN
	std::string betId = "bet1";
	std::string betReference = crypto::GenRandUUIDv4();
	std::string playerReference = crypto::GenRandUUIDv4();
	std::string roundReference = crypto::GenRandUUIDv4();
	EJackpotBetStatus status = EJackpotBetStatus::Lost;
	InitBetDto dto = { betId, betReference, playerReference, roundReference, status };

	// WHEN
	auto json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["betId"].get<std::string>(), betId);
	EXPECT_EQ(json["status"].get<std::string>(), status._to_string());
	EXPECT_EQ(json["betReference"].get<std::string>(), betReference);
	EXPECT_EQ(json["playerReference"].get<std::string>(), playerReference);
	EXPECT_EQ(json["roundReference"].get<std::string>(), roundReference);
}

TEST_F(InitRespDtoTest, InitLevelDataDtoFromJson)
{
	// GIVEN
	auto json = levelData1.ToJSON();

	// WHEN
	auto dto = InitLevelDataDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, levelData1);
}

TEST_F(InitRespDtoTest, InitLevelDataDtoFromEmptyJson)
{
	// GIVEN
	const json json(json::value_t::object);

	// WHEN
	auto dto = InitLevelDataDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto.LevelID, "");
	EXPECT_EQ(dto.LevelStatus, EJackpotLevelStatus::Disabled);
	EXPECT_FALSE(dto.ClientStatus.has_value());
	EXPECT_EQ(dto.LevelCurrency, ECurrency::EUR);
	EXPECT_EQ(dto.PendingBetsData.size(), 0);
	EXPECT_FALSE(dto.ExpectedWin.has_value());
}
