#include <gtest/gtest.h>

#include "Cryptography.h"
#include "TestUtils.h"
#include "Timing.h"
#include "YUtils.h"
#include "jackpot/dto/BetDto.h"

class BetDtoTest : public ::testing::Test
{
   protected:
	std::string BetId = crypto::GenRandUUIDv4();
	std::string ClientId = crypto::GenRandUUIDv4();
	std::string ClientLocation = "testLocation";
	std::string ExternalTriggerType = "44Catcher";
	std::string LevelId = crypto::GenRandUUIDv4();
	std::string PlayerName = "test";
	time_t BetTime = static_cast<time_t>(ytime::GetSystemTimeMsec());
	double Amount = 100;
	double PaidInAmount = 1;
	double WonAmount = 1000000.12789;
	double CreditedAmount = 1000000.12;
	uint64_t Credits = 100000012;
	double Denomination = 0.01;
	EJackpotBetStatus Status = EJackpotBetStatus::Lost;
	std::string BetReference = crypto::GenRandUUIDv4();
	std::string PlayerReference = crypto::GenRandUUIDv4();
	std::string SessionReference = crypto::GenRandUUIDv4();
	std::string RoundReference = crypto::GenRandUUIDv4();
	bool IsFlagged = true;

	BetDto GetDto() const
	{
		return BetDto(BetId, ClientId, LevelId, PlayerName, BetTime, Amount, PaidInAmount, Denomination, Status, BetReference, PlayerReference, SessionReference,
		              RoundReference, ClientLocation, ExternalTriggerType, WonAmount, CreditedAmount, Credits, IsFlagged);
	}
};

/**
 * @brief Test that the full DTO can be converted to JSON
 */
TEST_F(BetDtoTest, ConvertDtoToJson)
{
	// given
	const auto dto = GetDto();

	// when
	json json = dto;

	// then
	EXPECT_EQ(json["id"].get<std::string>(), BetId);
	EXPECT_EQ(json["clientId"].get<std::string>(), ClientId);
	EXPECT_EQ(json["levelId"].get<std::string>(), LevelId);
	EXPECT_EQ(json["betTime"].get<uint64_t>(), BetTime);
	EXPECT_EQ(json["amount"].get<double>(), Amount);
	EXPECT_EQ(json["paidInAmount"].get<double>(), PaidInAmount);
	EXPECT_EQ(json["wonAmount"].get<double>(), WonAmount);
	EXPECT_EQ(json["creditedAmount"].get<double>(), CreditedAmount);
	EXPECT_EQ(json["credits"].get<uint64_t>(), Credits);
	EXPECT_EQ(json["denomination"].get<double>(), Denomination);
	EXPECT_EQ(json["playerName"].get<std::string>(), PlayerName);
	EXPECT_EQ(json["status"].get<std::string>(), Status._to_string());
	EXPECT_EQ(json["betReference"].get<std::string>(), BetReference);
	EXPECT_EQ(json["playerReference"].get<std::string>(), PlayerReference);
	EXPECT_EQ(json["sessionReference"].get<std::string>(), SessionReference);
	EXPECT_EQ(json["roundReference"].get<std::string>(), RoundReference);
	EXPECT_EQ(json["clientLocation"].get<std::string>(), ClientLocation);
	EXPECT_EQ(json["isFlagged"].get<bool>(), IsFlagged);
	EXPECT_EQ(json["externalTriggerType"].get<std::string>(), ExternalTriggerType);
}

/**
 * Test that the full JSON can be converted to DTO
 */
TEST_F(BetDtoTest, ConvertJsonToDto)
{
	// given
	json json(json::value_t::object);
	json["id"] = BetId;
	json["clientId"] = ClientId;
	json["levelId"] = LevelId;
	json["betTime"] = BetTime;
	json["amount"] = Amount;
	json["paidInAmount"] = PaidInAmount;
	json["wonAmount"] = WonAmount;
	json["creditedAmount"] = CreditedAmount;
	json["credits"] = Credits;
	json["denomination"] = Denomination;
	json["playerName"] = PlayerName;
	json["status"] = Status._to_string();
	json["betReference"] = BetReference;
	json["playerReference"] = PlayerReference;
	json["sessionReference"] = SessionReference;
	json["roundReference"] = RoundReference;
	json["clientLocation"] = ClientLocation;
	json["isFlagged"] = IsFlagged;
	json["externalTriggerType"] = ExternalTriggerType;

	// when
	BetDto dto = json.get<BetDto>();

	// then
	EXPECT_EQ(dto.ID, BetId);
	EXPECT_EQ(dto.ClientID, ClientId);
	EXPECT_EQ(dto.LevelID, LevelId);
	EXPECT_EQ(dto.PlayerName, PlayerName);
	EXPECT_EQ(dto.BetTimeMs, BetTime);
	EXPECT_EQ(dto.Amount, Amount);
	EXPECT_EQ(dto.PaidInAmount, PaidInAmount);
	EXPECT_EQ(dto.WonAmount, WonAmount);
	EXPECT_EQ(dto.CreditedAmount, CreditedAmount);
	EXPECT_EQ(dto.Credits, Credits);
	EXPECT_EQ(dto.Denomination, Denomination);
	EXPECT_EQ(dto.Status, Status);
	EXPECT_EQ(dto.BetReference, BetReference);
	EXPECT_EQ(dto.PlayerReference, PlayerReference);
	EXPECT_EQ(dto.SessionReference, SessionReference);
	EXPECT_EQ(dto.RoundReference, RoundReference);
	EXPECT_EQ(dto.ClientLocation, ClientLocation);
	EXPECT_EQ(dto.IsFlagged, IsFlagged);
	EXPECT_EQ(dto.ExternalTriggerType, ExternalTriggerType);
}

TEST_F(BetDtoTest, DtoToJsonToDto)
{
	// given
	const auto dto = GetDto();

	// when
	json json = dto;
	BetDto dto2 = json.get<BetDto>();

	// then
	EXPECT_EQ(dto, dto2);
}

/**
 * Test that the empty JSON can be converted to DTO
 */
TEST_F(BetDtoTest, ConvertEmptyJsonToDto)
{
	// given
	json json(json::value_t::object);

	// when
	BetDto dto = json.get<BetDto>();

	// then
	EXPECT_EQ(dto.ID, "");
	EXPECT_EQ(dto.ClientID, "");
	EXPECT_EQ(dto.LevelID, "");
	EXPECT_EQ(dto.PlayerName, "");
	EXPECT_EQ(dto.BetTimeMs, 0);
	EXPECT_EQ(dto.Amount, 0);
	EXPECT_EQ(dto.PaidInAmount, 0);
	EXPECT_EQ(dto.WonAmount, 0);
	EXPECT_EQ(dto.CreditedAmount, 0);
	EXPECT_EQ(dto.Credits, 0);
	EXPECT_EQ(dto.Denomination, 0);
	EXPECT_EQ(dto.Status, EJackpotBetStatus::Lost);
	EXPECT_EQ(dto.BetReference, "");
	EXPECT_EQ(dto.PlayerReference, "");
	EXPECT_EQ(dto.SessionReference, "");
	EXPECT_EQ(dto.RoundReference, "");
	EXPECT_EQ(dto.ClientLocation, "");
	EXPECT_EQ(dto.IsFlagged, false);
	EXPECT_EQ(dto.ExternalTriggerType, "");
}

TEST_F(BetDtoTest, ToBetData)
{
	// given
	const auto dto = GetDto();

	// when
	BetDataDto betData = dto.ToBetData();

	// then
	EXPECT_EQ(betData.BetID, BetId);
	EXPECT_EQ(betData.Username, PlayerName);
	EXPECT_EQ(betData.Status, Status);
	EXPECT_EQ(betData.PaidInAmount, PaidInAmount);
	EXPECT_EQ(betData.AmountWon, CreditedAmount);
	EXPECT_EQ(betData.BetReference, BetReference);
	EXPECT_EQ(betData.PlayerReference, PlayerReference);
	EXPECT_EQ(betData.RoundReference, RoundReference);
	EXPECT_EQ(betData.ClientLocation, ClientLocation);
}

TEST_F(BetDtoTest, ToWonBet)
{
	// given
	const auto dto = GetDto();

	// when
	WonBetDto wonBet = dto.ToWonBet();

	// then
	EXPECT_EQ(wonBet.BetID, BetId);
	EXPECT_EQ(wonBet.PlayerName, PlayerName);
	EXPECT_EQ(wonBet.RoundReference, RoundReference);
	EXPECT_EQ(wonBet.AmountWon, CreditedAmount);
	EXPECT_EQ(wonBet.ClientLocation, ClientLocation);
	EXPECT_EQ(wonBet.CreditsWon, Credits);
}

TEST_F(BetDtoTest, ToInitBet)
{
	// given
	const auto dto = GetDto();

	// when
	InitBetDto initBet = dto.ToInitBet();

	// then
	EXPECT_EQ(initBet.BetID, BetId);
	EXPECT_EQ(initBet.Status, Status);
	EXPECT_EQ(initBet.BetReference, BetReference);
	EXPECT_EQ(initBet.PlayerReference, PlayerReference);
	EXPECT_EQ(initBet.RoundReference, RoundReference);
}

TEST_F(BetDtoTest, ToBetProcessed)
{
	// given
	const auto dto = GetDto();

	// when
	BetProcessedDto betProcessed = dto.ToBetProcessed();

	// then
	EXPECT_EQ(betProcessed.BetID, BetId);
	EXPECT_EQ(betProcessed.BetReference, BetReference);
	EXPECT_EQ(betProcessed.PlayerReference, PlayerReference);
	EXPECT_EQ(betProcessed.RoundReference, RoundReference);
	EXPECT_EQ(betProcessed.BetStatus, Status);
	EXPECT_EQ(betProcessed.AmountWon, CreditedAmount);
	EXPECT_EQ(betProcessed.CreditsWon, Credits);
}

TEST_F(BetDtoTest, ToJackpotWinner)
{
	// given
	const auto dto = GetDto();

	// when
	JackpotWinnerDto jackpotWinner = dto.ToJackpotWinner();

	// then
	EXPECT_EQ(jackpotWinner.BetID, BetId);
	EXPECT_EQ(jackpotWinner.BetReference, BetReference);
	EXPECT_EQ(jackpotWinner.Username, PlayerName);
	EXPECT_EQ(jackpotWinner.PlayerReference, PlayerReference);
	EXPECT_EQ(jackpotWinner.Location, ClientLocation);
	EXPECT_EQ(jackpotWinner.AmountWon, CreditedAmount);
	EXPECT_EQ(jackpotWinner.CreditsWon, Credits);
	EXPECT_EQ(jackpotWinner.RoundReference, RoundReference);
}

TEST_F(BetDtoTest, TestEmptyFilter)
{
	// given
	const auto dto = GetDto();
	BetFilter filter;

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(BetDtoTest, TestFilterByMatchedClientId)
{
	// given
	const auto dto = GetDto();
	BetFilter filter(ClientId);

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(BetDtoTest, TestFilterByUnmatchedClientId)
{
	// given
	const auto dto = GetDto();
	BetFilter filter("unmatched");

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(BetDtoTest, TestFilterByNullOptClientId)
{
	// given
	const auto dto = GetDto();
	BetFilter filter(std::nullopt);

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(BetDtoTest, TestFilterByMatchedStatus)
{
	// given
	const auto dto = GetDto();
	BetFilter filter({}, { EJackpotBetStatus::Lost });

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(BetDtoTest, TestFilterByUnmatchedStatus)
{
	// given
	const auto dto = GetDto();
	BetFilter filter({}, { EJackpotBetStatus::Won });

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_FALSE(match);
}

TEST_F(BetDtoTest, TestFilterByEmptyStatus)
{
	// given
	const auto dto = GetDto();
	BetFilter filter({}, {});

	// when
	bool match = dto.Match(filter);

	// then
	EXPECT_TRUE(match);
}

TEST_F(BetDtoTest, TestFilterByMatchedClientIdAndStatus)
{
	// given
	auto dto = GetDto();
	dto.Status = EJackpotBetStatus::Lost;

	// when
	const BetFilter filter(ClientId, { EJackpotBetStatus::Lost });

	// when
	EXPECT_TRUE(dto.Match(filter));
}

TEST_F(BetDtoTest, TestFilterByMatchedFromTime)
{
	// given
	const auto dto = GetDto();

	// when
	const BetFilter filter({}, {}, BetTime - 1000);

	// when
	EXPECT_TRUE(dto.Match(filter));
}

TEST_F(BetDtoTest, TestFilterByUnmatchedFromTime)
{
	// given
	const auto dto = GetDto();

	// when
	const BetFilter filter({}, {}, BetTime + 1000);

	// when
	EXPECT_FALSE(dto.Match(filter));
}

TEST_F(BetDtoTest, TestFilterByMatchedToTime)
{
	// given
	const auto dto = GetDto();

	// when
	const BetFilter filter({}, {}, {}, BetTime + 1000);

	// when
	EXPECT_TRUE(dto.Match(filter));
}

TEST_F(BetDtoTest, TestFilterByUnmatchedToTime)
{
	// given
	const auto dto = GetDto();

	// when
	const BetFilter filter({}, {}, {}, BetTime - 1000);

	// when
	EXPECT_FALSE(dto.Match(filter));
}

TEST_F(BetDtoTest, SerializeDeserialize)
{
	const auto dto = GetDto();

	const std::string serialized = yutils::SerializeToString(dto);
	const BetDto deserialized = yutils::DeserializeFromString<BetDto>(serialized);

	EXPECT_EQ(dto, deserialized);
}

TEST_F(BetDtoTest, BinaryVsJsonSerialization_1Mio)
{
	const auto dto = GetDto();
	const size_t iterations = 200000;

	// Measure JSON serialization time
	auto startJson = std::chrono::high_resolution_clock::now();
	for (size_t i = 0; i < iterations; ++i)
	{
		const json json = dto;
		const BetDto dto2 = json.get<BetDto>();
	}
	auto endJson = std::chrono::high_resolution_clock::now();
	auto durationJson = std::chrono::duration_cast<std::chrono::milliseconds>(endJson - startJson).count();

	// Measure binary serialization time
	auto startBinary = std::chrono::high_resolution_clock::now();
	for (size_t i = 0; i < iterations; ++i)
	{
		const std::string serialized = yutils::SerializeToString(dto);
		const BetDto dto3 = yutils::DeserializeFromString<BetDto>(serialized);
	}
	auto endBinary = std::chrono::high_resolution_clock::now();
	auto durationBinary = std::chrono::duration_cast<std::chrono::milliseconds>(endBinary - startBinary).count();

	std::cout << "JSON serialization time: " << durationJson << " ms" << std::endl;
	std::cout << "Binary serialization time: " << durationBinary << " ms" << std::endl;
	EXPECT_LT(durationBinary, durationJson);    // Binary serialization should be faster
}
