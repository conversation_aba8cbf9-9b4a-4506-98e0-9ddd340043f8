#include <gtest/gtest.h>

#include "Cryptography.h"
#include "Timing.h"
#include "YUtils.h"
#include "jackpot/dto/GetServerInfoRespDto.h"

class GetServerInfoRespDtoTest : public ::testing::Test
{
   protected:
	std::string serverId = crypto::GenRandUUIDv4();
	time_t startTimeS = 1713533338;    // "2024-04-19T13:28:58+0000" in seconds since epoch
	std::string version = "1.0.0";
	bool isRamClear = true;
	int64_t timezoneOffset = -120;
};

TEST_F(GetServerInfoRespDtoTest, CreateDto)
{
	// WHEN
	GetServerInfoRespDto dto = { serverId, startTimeS, version, isRamClear, timezoneOffset };

	// THEN
	EXPECT_EQ(dto.ServerID, serverId);
	EXPECT_EQ(dto.StartTimeS, startTimeS);
	EXPECT_EQ(dto.Version, version);
	EXPECT_EQ(dto.bIsRamClear, isRamClear);
	EXPECT_EQ(dto.TimezoneOffset, timezoneOffset);
}

TEST_F(GetServerInfoRespDtoTest, ConvertDtoToJson)
{
	// GIVEN
	GetServerInfoRespDto dto = { serverId, startTimeS, version, isRamClear, timezoneOffset };

	// WHEN
	json json = dto.ToJSON();

	// THEN
	EXPECT_EQ(json["serverId"].get<std::string>(), serverId);
	EXPECT_EQ(json["version"].get<std::string>(), version);
	EXPECT_EQ(json["startTime"].get<std::string>(), yutils::FormatTime("%Y-%m-%dT%H:%M:%S%z", dto.StartTimeS));
	EXPECT_EQ(json["isRamClear"].get<bool>(), isRamClear);
	EXPECT_EQ(json["timezoneOffset"].get<int64_t>(), timezoneOffset);
}

TEST_F(GetServerInfoRespDtoTest, DtoToJsonToDto)
{
	// GIVEN
	GetServerInfoRespDto dto = { serverId, startTimeS, version, isRamClear, timezoneOffset };

	// WHEN
	json json = dto.ToJSON();
	GetServerInfoRespDto dto2 = GetServerInfoRespDto::FromJSON(json);

	// THEN
	EXPECT_EQ(dto, dto2);
}

TEST_F(GetServerInfoRespDtoTest, JsonToDtoToJson)
{
	// GIVEN
	json json(json::value_t::object);
	json["serverId"] = serverId;
	json["startTime"] = yutils::FormatTime("%Y-%m-%dT%H:%M:%S%z", startTimeS);
	json["version"] = version;
	json["isRamClear"] = isRamClear;
	json["timezoneOffset"] = timezoneOffset;

	// WHEN
	const GetServerInfoRespDto dto = GetServerInfoRespDto::FromJSON(json);
	const auto json2 = dto.ToJSON();

	// THEN
	EXPECT_EQ(json, json2);
}
