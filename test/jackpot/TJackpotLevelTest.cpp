#include <LocalDateTime.h>
#include <gmock/gmock.h>
#include <gtest/gtest.h>

#include "jackpot/JackpotDao.h"
#include "jackpot/TJackpotLevel.h"

using namespace imaxa::jackpot;

class MockJackpotDao : public JackpotDao
{
   public:
	MockJackpotDao() : JackpotDao("/config/dummy_path", false) {}
	// MOCK_METHOD(rocksdb::DB*, GetDatabase, (), (const));
	// MOCK_METHOD(void, SaveJackpotLevelConfig, (const imaxa::jackpot::JackpotLevelInfoBase& jackpotLevel));
	// MOCK_METHOD(std::vector<imaxa::jackpot::JackpotLevelInfoBase>, ListJackpotLevelConfigs, ());
	// MOCK_METHOD(std::optional<imaxa::jackpot::JackpotLevelInfoBase>, GetJackpotLevelConfig, (const std::string& id));
	// MOCK_METHOD(EJackpotLevelStatus, GetStatus, (const std::string& jackpotLevelId));
};

class TJackpotLevelTest : public ::testing::Test
{
   protected:
	std::shared_ptr<MockJackpotDao> mockJackpotDao = std::make_shared<MockJackpotDao>();
	std::string ServerID = "serverIdMock";
	std::string LevelId = "levelIdMock";
	EJackpotLevelType LevelType = EJackpotLevelType::External;
	EJackpotPotType PotType = EJackpotPotType::Progressive;
	ECurrency Currency = ECurrency::EUR;
	double Increment = 0.01;
	std::optional<double> IncrementVisibleRel = 0.4;
	double MinBet = 0.01;
	double MaxBet = 100;
	double MinWinnableBet = 0.89;
	double MaxWinnableBet = 99.88;
	double MinPot = 0;
	double MaxPot = 100;
	int64_t TimeFrom = 0;
	int64_t TimeTo = 0;
	std::string Timezone = "Europe/Ljubljana";
	std::string Name = "nameMock";
	EActionAfterWin AutoActionAfterWin = EActionAfterWin::Reset;
	uint32_t AutoActionAfterWinOffsetSec = 50;
	uint32_t MinTimeBetweenWinsSec = 2;
	bool UseFixedPaidIn = false;
	bool KeepPotAboveMinPot = true;

	JackpotLevelInfoBase getJackpotLevelInfo() const
	{
		JackpotLevelInfoBase info;
		info.ID = LevelId;
		info.Type = LevelType;
		info.PotType = PotType;
		info.Currency = Currency;
		info.Increment = Increment;
		info.IncrementVisibleRel = IncrementVisibleRel;
		info.MinBet = MinBet;
		info.MaxBet = MaxBet;
		info.MinWinnableBet = MinWinnableBet;
		info.MaxWinnableBet = MaxWinnableBet;
		info.MinPot = MinPot;
		info.MaxPot = MaxPot;
		info.TimeFrom = TimeFrom;
		info.TimeTo = TimeTo;
		info.Timezone = Timezone;
		info.Name = Name;
		info.AutoActionAfterWin = AutoActionAfterWin;
		info.AutoActionAfterWinOffsetSec = AutoActionAfterWinOffsetSec;
		info.MinTimeBetweenWinsSec = MinTimeBetweenWinsSec;
		info.bUseFixedPaidIn = UseFixedPaidIn;
		info.bKeepPotAboveMinPot = KeepPotAboveMinPot;
		return info;
	}


   public:
	void SetUp() override {}
	void TearDown() override {}
};

TEST_F(TJackpotLevelTest, IsHotSeatWon_ShouldReturnNulloptWhenLevelTypeNotHotseat)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.Type = EJackpotLevelType::Mystery;
	auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);

	// WHEN
	auto result = jackpotLevel.IsHotSeatWon_AssumeLocked();

	// THEN
	EXPECT_EQ(result, false);
}

// FIXME: this test does not work currently - it seems GetStatus is not properly mocked
// TEST_F(TJackpotLevelTest, CheckIfHotSeatWon_ShouldReturnNulloptWhenStatusNotCounting)
//{
//	// GIVEN
//	auto jackpotLevelInfo = getJackpotLevelInfo();
//	jackpotLevelInfo.Type = EJackpotLevelType::HotSeat;
//	EXPECT_CALL(*mockJackpotDao, GetStatus(testing::_)).WillOnce(testing::Return(EJackpotLevelStatus::Won));
//	auto jackpotLevel = TJackpotLevel(mockJackpotDao, LevelId, getJackpotLevelInfo());
//	rocksdb::WriteBatch batch;
//
//	// WHEN
//	auto result = jackpotLevel.CheckIfHotSeatWon(batch);
//
//	// THEN
//	EXPECT_EQ(jackpotLevel.Status(), EJackpotLevelStatus::Won);
//
//	EXPECT_EQ(result, std::nullopt);
//	EXPECT_EQ(batch.Count(), 0);
//}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenTimeBetweenFromAndToSummerTime)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 930;    // 7.30 in UTC (9.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 1020;    // 8.20 in UTC (10.20 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716796920000;    // Monday, 27 May 2024 10:02:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenTimeBetweenFromAndToWinterTime)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 930;    // 8.30 in UTC (9.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 1020;    // 9.20 in UTC (10.20 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1706346120000;    // Saturday, 27 January 2024 10:02:00 GMT+01:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenTimeBetweenFromAndToOverMidnight)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2310;    // 21.10 in UTC (23.10 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 50;    // 22.50 in UTC (0.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716848100000;    // Tuesday, 28 May 2024 00:15:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnFalseWhenTimeNotBetweenFromAndTo)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 930;    // 7.30 in UTC (9.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 1020;    // 8.20 in UTC (10.20 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716793800000;    // Monday, 27 May 2024 09:10:00 GMT+02:00
	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_FALSE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnFalseWhenTimeNotBetweenFromAndToOverMidnight)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2310;    // 21.10 in UTC (23.10 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 50;    // 22.50 in UTC (0.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716850500000;    // Tuesday, 28 May 2024 00:55:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_FALSE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenCorrectDayOfWeek)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b00000100);    // only Wednesday
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716379200000;    // Wednesday, 22 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnFalseWhenCorrectDayOfWeekUtcButNotLocal)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b00000100);    // only Wednesday
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1717023600000;    // Wednesday, 29 May 2024 23:00:00; Thursday, 30 May 2024 01:00:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_FALSE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnFalseWhenNotCorrectDayOfWeek)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01101111);    // only not Friday
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716552000000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_FALSE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenCorrectDayOfWeekWhenStartedButNotAfterMidnight)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2310;    // 21.10 in UTC (23.10 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 50;    // 22.50 in UTC (0.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b00000001);    // only Monday
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716849900000;    // Tuesday, 28 May 2024 00:45:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnFalseWhenWrongDayOfWeekWhenStartedButValidAfterMidnight)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2310;    // 21.10 in UTC (23.10 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 50;    // 22.50 in UTC (0.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b00000010);    // only Tuesday
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto time = 1716849900000;    // Tuesday, 28 May 2024 00:45:00 GMT+02:00

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(time);

	// THEN
	EXPECT_FALSE(result);
}

TEST_F(TJackpotLevelTest, CanBePlayedAtTime_ShouldReturnTrueWhenDefault)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const auto now = ytime::GetSystemTimeMsec();

	// WHEN
	auto result = jackpotLevel.CanBePlayedAtTime_AssumeLocked(now);

	// THEN
	EXPECT_TRUE(result);
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromLtTimeTo)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 1330;    // 13.30 in UTC (15.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 1520;    // 15.20 in UTC (17.20 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = DEFAULT_TIMEZONE;
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716552000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716557400);    // Friday, 24 May 2024 13:30:00 UTC
	EXPECT_EQ(result->second, 1716564000);    // Friday, 24 May 2024 15:20:00 UTC
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromGtTimeToAndCurrentTimeGtTimeTo)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2130;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 50;    // 0.50 in UTC (2.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = DEFAULT_TIMEZONE;
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716552000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716586200);    // Friday, 24 May 2024 21:30:00 UTC
	EXPECT_EQ(result->second, 1716598200);    // Friday, 25 May 2024 0:50:00 UTC
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromGtTimeToAndCurrentTimeLtTimeTo)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2130;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 250;    // 2.50 in UTC (4.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = DEFAULT_TIMEZONE;
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716513000;    //  Friday, 24 May 2024 01:10:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716499800);    // Thursday, 23 May 2024 21:30:00 UTC
	EXPECT_EQ(result->second, 1716519000);    // Friday, 24 May 2024 2:50:00 UTC
}

// ljubljana tz
TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromLtTimeToTz)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 1530;    // 13.30 in UTC (15.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 1720;    // 15.20 in UTC (17.20 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716552000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716557400);    // Friday, 24 May 2024 13:30:00 UTC
	EXPECT_EQ(result->second, 1716564000);    // Friday, 24 May 2024 15:20:00 UTC
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromGtTimeToAndCurrentTimeGtTimeToTz)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2330;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 250;    // 0.50 in UTC (2.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716552000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716586200);    // Friday, 24 May 2024 21:30:00 UTC
	EXPECT_EQ(result->second, 1716598200);    // Saturday, 25 May 2024 0:50:00 UTC
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_WhenTimeFromGtTimeToAndCurrentTimeLtTimeToTz)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2330;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 450;    // 2.50 in UTC (4.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01111111);    // all days
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716513000;    //  Friday, 24 May 2024 01:10:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_TRUE(result.has_value());
	EXPECT_EQ(result->first, 1716499800);    // Thursday, 23 May 2024 21:30:00 UTC
	EXPECT_EQ(result->second, 1716519000);    // Friday, 24 May 2024 2:50:00 UTC
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_StartTimeDateNotIncluded)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2330;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 250;    // 0.50 in UTC (2.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01101111);    // all days except Friday
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716552000;    //  Friday, 24 May 2024 12:00:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_FALSE(result.has_value());
}

TEST_F(TJackpotLevelTest, GetHotSeatTimes_StartTimeDateNotIncluded2)
{
	// GIVEN
	auto jackpotLevelInfo = getJackpotLevelInfo();
	jackpotLevelInfo.TimeFrom = 2330;    // 21.30 in UTC (23.30 in Ljubljana in summer time)
	jackpotLevelInfo.TimeTo = 450;    // 2.50 in UTC (4.50 in Ljubljana in summer time)
	jackpotLevelInfo.Timezone = "Europe/Ljubljana";
	jackpotLevelInfo.WeekDays = Bitflag<EWeekday, uint8_t>(0b01110111);    // all days except thursday
	const auto jackpotLevel = TJackpotLevel(mockJackpotDao, jackpotLevelInfo, ServerID);
	const time_t timeSec = 1716513000;    //  Friday, 24 May 2024 01:10:00 UTC

	// WHEN
	auto result = jackpotLevel.GetHotSeatTimes_AssumeLocked(timeSec);

	// THEN
	EXPECT_FALSE(result.has_value());
}
