#pragma once

#include "Cryptography.h"
#include "jackpot/client/JackpotClientConfig.h"
#include "jackpot/dto/JackpotLevelInfoBase.h"

namespace jackpot::testutils
{
inline JackpotClientConfig GetClientConfigDto()
{
	return { "clientId", ECurrency::EUR, 0.01, "externalTriggerType" };
}

inline JackpotLevelInfoBase GetJackpotLevelInfo(const EJackpotLevelType levelType = EJackpotLevelType::Mystery, const double MinPot = 0, const double MaxPot = 0)
{
	return JackpotLevelInfoBase(crypto::GenRandUUIDv4(), levelType, EJackpotPotType::Fixed, ECurrency::NONE, "test", 0.1, 0.05, {}, MinPot, MaxPot, 0, 0, 0,
	                            "Europe/Ljubljana", {}, 1, 10, 1.5, 9.5, "test", EActionAfterWin::Reset, 30, 20, false, false, "templateId", "levelRef", true,
	                            json::object());
}
}    // namespace jackpot::testutils
