## role unit tests
##

Import('env')
Import('env_cpp11')
Import('boostlibs')
Import('platform_libs')
Import('polyfill_libs')

env = env.Clone ()
env_cpp11 = env_cpp11.Clone ()

BOOST_LIBS = boostlibs(['unit_test_framework','system','random'],env) + [platform_libs]

objs = env.Object('client_boost.o', ["client.cpp"], LIBS = BOOST_LIBS)
objs += env.Object('server_boost.o', ["server.cpp"], LIBS = BOOST_LIBS)
prgs = env.Program('test_client_boost', ["client_boost.o"], LIBS = BOOST_LIBS)
prgs += env.Program('test_server_boost', ["server_boost.o"], LIBS = BOOST_LIBS)

if 'WSPP_CPP11_ENABLED' in env_cpp11:
   BOOST_LIBS_CPP11 = boostlibs(['unit_test_framework'],env_cpp11) + [platform_libs] + [polyfill_libs]
   objs += env_cpp11.Object('client_stl.o', ["client.cpp"], LIBS = BOOST_LIBS_CPP11)
   objs += env_cpp11.Object('server_stl.o', ["server.cpp"], LIBS = BOOST_LIBS_CPP11)
   prgs += env_cpp11.Program('test_client_stl', ["client_stl.o"], LIBS = BOOST_LIBS_CPP11)
   prgs += env_cpp11.Program('test_server_stl', ["server_stl.o"], LIBS = BOOST_LIBS_CPP11)

Return('prgs')
