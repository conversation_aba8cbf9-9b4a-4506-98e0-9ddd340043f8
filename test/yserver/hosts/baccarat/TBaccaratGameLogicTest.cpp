//
// Created by <PERSON><PERSON><PERSON> on 22. 11. 23.
//

#include <gtest/gtest.h>

#include "yserver/hosts/baccarat/TBaccaratGameLogic.h"

using namespace yserver;
using namespace yserver::gamehost::baccarat;

/* Testing evaluation of baccarat game result.
 * 1. Player and banker are dealt two cards each. (player hand value = 2, banker hand value = 6)
 * 2. If either hand totals 8 or 9, each of them stands.
 * 3. If the Player's total is between 0-5, they draw a third card. So, with a total of 2, the Player will draw a third card.
 * 4. Banker draws a third card depends on the Player's third card and the Banker's current total.
 * 4.1. If the Banker's total is 0-2, the Banker always draws a third card regardless of the Player's third card.
 * 4.2. If the Banker's total is 3-6, the decision to draw a third card depends on the value of the Player's third card.
 * 4.3. With a total of 6, the Banker will draw a third card if the Player’s third card is a 6 or 7.
 * 4.4. The Banker stands on a total of 7 regardless of the Player’s third card.
 */

TEST(BaccaratGameLogicTest, ShouldPlayerDrawThirdCardTrue)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(2, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(303, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);

	// WHEN
	auto shouldPlayerDrawThirdCard = game.ShouldPlayerDrawThirdCard();    // Player total is 5 - should draw third card

	// THEN
	EXPECT_TRUE(shouldPlayerDrawThirdCard);
}

TEST(BaccaratGameLogicTest, ShouldPlayerDrawThirdCardFalse)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(104, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(302, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);

	auto shouldPlayerDrawThirdCard = game.ShouldPlayerDrawThirdCard();    // Player total is 6 - shouldn't draw third card

	// THEN
	EXPECT_FALSE(shouldPlayerDrawThirdCard);
}

TEST(BaccaratGameLogicTest, ShouldBankerDrawThirdCardTrue)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(2, EBaccaratSide::Player);
	game.AddCard(211, EBaccaratSide::Banker);
	game.AddCard(313, EBaccaratSide::Player);
	game.AddCard(1, EBaccaratSide::Banker);

	auto shouldBankerDrawThirdCard =
	  game.ShouldBankerDrawThirdCard();    // If the Banker's total is 0-2, the Banker always draws a third card regardless of the Player's third card.

	// THEN
	EXPECT_TRUE(shouldBankerDrawThirdCard);
}

TEST(BaccaratGameLogicTest, ShouldBankerDrawThirdCardFalse)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(2, EBaccaratSide::Player);
	game.AddCard(211, EBaccaratSide::Banker);
	game.AddCard(313, EBaccaratSide::Player);
	game.AddCard(407, EBaccaratSide::Banker);

	game.AddCard(7, EBaccaratSide::Player);    // Player draws third card

	auto shouldBankerDrawThirdCard =
	  game.ShouldBankerDrawThirdCard();    // The Banker stands on a total of 7 regardless of the Player’s third card. (No 3rd card for banker)

	// THEN
	EXPECT_FALSE(shouldBankerDrawThirdCard);
}

TEST(BaccaratGameLogicTest, EvaluatePlayerWinWithAdditionalCard)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(2, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(413, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);

	auto shouldPlayerDrawThirdCard = game.ShouldPlayerDrawThirdCard();

	if (shouldPlayerDrawThirdCard)
		game.AddCard(7, EBaccaratSide::Player);

	auto shouldBankerDrawThirdCard = game.ShouldBankerDrawThirdCard();
	if (shouldBankerDrawThirdCard)
		game.AddCard(106, EBaccaratSide::Banker);

	// THEN
	EXPECT_TRUE(shouldPlayerDrawThirdCard);
	EXPECT_TRUE(shouldBankerDrawThirdCard);
	EXPECT_EQ(game.Evaluate(), EBaccaratWinner::Player);
}

TEST(BaccaratGameLogicTest, EvaluatePlayerWin)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(212, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(408, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);

	if (game.ShouldPlayerDrawThirdCard())
		game.AddCard(7, EBaccaratSide::Player);

	if (game.ShouldBankerDrawThirdCard())
		game.AddCard(106, EBaccaratSide::Banker);

	// THEN
	EXPECT_EQ(game.Evaluate(), EBaccaratWinner::Player);
}

TEST(BaccaratGameLogicTest, ClearGame)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(2, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(413, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);
	game.Evaluate();
	game.ClearGame();

	// THEN
	EXPECT_FALSE(game.ShouldBankerDrawThirdCard());
	EXPECT_FALSE(game.ShouldPlayerDrawThirdCard());
	EXPECT_EQ(game.GetWinner(), EBaccaratWinner::Null);
}

TEST(BaccaratGameLogicTest, Lucky6Win)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(212, EBaccaratSide::Player);
	game.AddCard(203, EBaccaratSide::Banker);
	game.AddCard(401, EBaccaratSide::Player);
	game.AddCard(3, EBaccaratSide::Banker);

	// THEN
	EXPECT_EQ(game.Evaluate(), EBaccaratWinner::Banker);
	EXPECT_TRUE(game.IsBetTypeWon(EBaccaratBetType::Lucky6));
	EXPECT_FALSE(game.IsBetTypeWon(EBaccaratBetType::Lucky6AdditionalCard));
}

TEST(BaccaratGameLogicTest, PlayerPair)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(206, EBaccaratSide::Player);
	game.AddCard(212, EBaccaratSide::Banker);
	game.AddCard(106, EBaccaratSide::Player);
	game.AddCard(9, EBaccaratSide::Banker);

	// THEN
	EXPECT_EQ(game.Evaluate(), EBaccaratWinner::Banker);
	EXPECT_TRUE(game.IsBetTypeWon(EBaccaratBetType::PlayerPair));
	EXPECT_FALSE(game.IsBetTypeWon(EBaccaratBetType::BankerPair));
	EXPECT_FALSE(game.IsBetTypeWon(EBaccaratBetType::Lucky6));
}

TEST(BaccaratGameLogicTest, Lucky6WithAdditionalCardWin)
{
	// GIVEN
	BaccaratGameLogic game;

	// WHEN
	game.AddCard(212, EBaccaratSide::Player);
	game.AddCard(202, EBaccaratSide::Banker);
	game.AddCard(401, EBaccaratSide::Player);
	game.AddCard(2, EBaccaratSide::Banker);

	if (game.ShouldPlayerDrawThirdCard())
		game.AddCard(3, EBaccaratSide::Player);

	if (game.ShouldBankerDrawThirdCard())
		game.AddCard(102, EBaccaratSide::Banker);

	// THEN
	EXPECT_EQ(game.Evaluate(), EBaccaratWinner::Banker);
	EXPECT_TRUE(game.IsBetTypeWon(EBaccaratBetType::Lucky6AdditionalCard));
}

// Test for GetFreeHandGameResult method
TEST(BaccaratGameLogicTest, GetFreeHandGameResult)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(5, EBaccaratSide::Player);
	game.AddCard(7, EBaccaratSide::Banker);
	game.AddCard(102, EBaccaratSide::Player);
	game.AddCard(304, EBaccaratSide::Banker);

	if (game.ShouldPlayerDrawThirdCard())
		game.AddCard(3, EBaccaratSide::Player);

	if (game.ShouldBankerDrawThirdCard())
		game.AddCard(206, EBaccaratSide::Banker);

	game.Evaluate();

	// WHEN
	json result = game.GetFreeHandGameResult(false);

	// THEN
	EXPECT_EQ(result["playerSum"].get<uint32_t>(), 7);
	EXPECT_EQ(result["bankerSum"].get<uint32_t>(), 7);
	EXPECT_EQ(result["winningNumber"].get<uint32_t>(), 7);
	EXPECT_EQ(result["winner"].asInt(), static_cast<int>(EBaccaratWinner::Tie));
	EXPECT_TRUE(result["cards"].is_array());
	EXPECT_EQ(result["cards"].size(), 6);
	EXPECT_FALSE(result["bankerNaturals"].get<bool>());
	EXPECT_FALSE(result["playerNaturals"].get<bool>());
	EXPECT_FALSE(result["playerPair"].get<bool>());
	EXPECT_FALSE(result["bankerPair"].get<bool>());
}

TEST(BaccaratGameLogicTest, IsBetTypeWon)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(5, EBaccaratSide::Player);
	game.AddCard(108, EBaccaratSide::Banker);
	game.AddCard(102, EBaccaratSide::Player);
	game.AddCard(408, EBaccaratSide::Banker);

	game.Evaluate();

	// WHEN
	const auto isBankerPair = game.IsBetTypeWon(EBaccaratBetType::BankerPair);
	const auto isPlayerPair = game.IsBetTypeWon(EBaccaratBetType::PlayerPair);

	// THEN
	EXPECT_TRUE(isBankerPair);
	EXPECT_FALSE(isPlayerPair);
}

TEST(BaccaratGameLogicTest, GetCards)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(5, EBaccaratSide::Player);
	game.AddCard(108, EBaccaratSide::Banker);
	game.AddCard(102, EBaccaratSide::Player);
	game.AddCard(408, EBaccaratSide::Banker);

	game.Evaluate();

	// WHEN
	const auto isBankerPair = game.IsBetTypeWon(EBaccaratBetType::BankerPair);
	const auto isPlayerPair = game.IsBetTypeWon(EBaccaratBetType::PlayerPair);

	// THEN
	EXPECT_TRUE(isBankerPair);
	EXPECT_FALSE(isPlayerPair);
}

// Test for GetCards method with visible card faces
TEST(BaccaratGameLogicTest, GetCardsVisibleFaces)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(5, EBaccaratSide::Player);
	game.AddCard(7, EBaccaratSide::Banker);
	game.AddCard(203, EBaccaratSide::Player);
	game.AddCard(405, EBaccaratSide::Banker);
	game.Evaluate();

	// WHEN
	std::vector<json> cards = game.GetCards(true);

	// THEN
	EXPECT_EQ(cards.size(), 4);
	EXPECT_EQ(cards[0]["card"].get<uint32_t>(), 5);    // Player's first card
	EXPECT_EQ(cards[1]["card"].get<uint32_t>(), 203);    // Player's second card
	EXPECT_EQ(cards[2]["card"].get<uint32_t>(), 7);    // Banker's first card
	EXPECT_EQ(cards[3]["card"].get<uint32_t>(), 405);    // Banker's second card
	EXPECT_EQ(cards.back()["winner"].asInt(), static_cast<int>(EBaccaratWinner::Player));
}

// Test for GetCards method with hidden card faces
TEST(BaccaratGameLogicTest, GetCardsHiddenFaces)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(1, EBaccaratSide::Player);
	game.AddCard(2, EBaccaratSide::Banker);
	game.AddCard(3, EBaccaratSide::Player);
	game.AddCard(4, EBaccaratSide::Banker);
	game.Evaluate();

	// WHEN
	std::vector<json> cards = game.GetCards(false);

	// THEN
	EXPECT_EQ(cards.size(), 4);

	// Assert that card values are hidden (set to 0)
	EXPECT_EQ(cards[0]["card"].get<uint32_t>(), 0);
	EXPECT_EQ(cards[1]["card"].get<uint32_t>(), 0);
	EXPECT_EQ(cards[2]["card"].get<uint32_t>(), 0);
	EXPECT_EQ(cards[3]["card"].get<uint32_t>(), 0);
}

TEST(BaccaratGameLogicTest, GetGameResult)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(102, EBaccaratSide::Player);
	game.AddCard(204, EBaccaratSide::Banker);
	game.AddCard(6, EBaccaratSide::Player);
	game.AddCard(405, EBaccaratSide::Banker);
	game.Evaluate();

	// WHEN
	json result = game.GetGameResult();

	// THEN
	EXPECT_EQ(result["player"]["sum"].get<uint32_t>(), 8);
	EXPECT_EQ(result["banker"]["sum"].get<uint32_t>(), 9);
	EXPECT_TRUE(result["player"]["cards"].is_array());
	EXPECT_TRUE(result["banker"]["cards"].is_array());
	EXPECT_EQ(result["player"]["cards"][0].get<uint32_t>(), 102);    // First card of Player
	EXPECT_EQ(result["banker"]["cards"][0].get<uint32_t>(), 204);    // First card of Banker
	EXPECT_EQ(result["winner"].asInt(), static_cast<int>(EBaccaratWinner::Banker));
	EXPECT_FALSE(result["player"].contains("additionalCard"));
	EXPECT_FALSE(result["banker"].contains("additionalCard"));
}

TEST(BaccaratGameLogicTest, GetCardsWithCutCard)
{
	// GIVEN
	BaccaratGameLogic game;
	game.AddCard(1, EBaccaratSide::Player);
	game.AddCard(2, EBaccaratSide::Banker);
	game.AddCutCard(2);
	game.AddCard(3, EBaccaratSide::Player);
	game.AddCard(4, EBaccaratSide::Banker);
	game.Evaluate();

	// WHEN
	std::vector<json> cards = game.GetCards(true);

	// THEN
	EXPECT_EQ(cards.size(), 5);
	EXPECT_EQ(cards[0]["card"].get<uint32_t>(), 1);    // Player first card
	EXPECT_EQ(cards[1]["card"].get<uint32_t>(), 3);    // Player second card
	EXPECT_EQ(cards[2]["card"].asInt(), -1);    // Cut card
	EXPECT_EQ(cards[3]["card"].get<uint32_t>(), 2);    // Banker first card
	EXPECT_EQ(cards[4]["card"].get<uint32_t>(), 4);    // Banker second card
}
