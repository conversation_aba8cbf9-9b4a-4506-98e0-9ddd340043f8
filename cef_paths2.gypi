# Copyright (c) 2011 The Chromium Embedded Framework Authors. All rights
# reserved. Use of this source code is governed by a BSD-style license that
# can be found in the LICENSE file.

{
  'variables': {
    'includes_common': [
      'include/base/cef_atomic_flag.h',
      'include/base/cef_atomic_ref_count.h',
      'include/base/cef_auto_reset.h',
      'include/base/cef_bind.h',
      'include/base/cef_build.h',
      'include/base/cef_callback.h',
      'include/base/cef_callback_forward.h',
      'include/base/cef_callback_helpers.h',
      'include/base/cef_callback_list.h',
      'include/base/cef_cancelable_callback.h',
      'include/base/cef_compiler_specific.h',
      'include/base/cef_dump_without_crashing.h',
      'include/base/cef_lock.h',
      'include/base/cef_logging.h',
      'include/base/cef_macros.h',
      'include/base/cef_platform_thread.h',
      'include/base/cef_ref_counted.h',
      'include/base/cef_scoped_refptr.h',
      'include/base/cef_thread_checker.h',
      'include/base/cef_trace_event.h',
      'include/base/cef_tuple.h',
      'include/base/cef_weak_ptr.h',
      'include/base/internal/cef_bind_internal.h',
      'include/base/internal/cef_callback_internal.h',
      'include/base/internal/cef_color_id_macros.inc',
      'include/base/internal/cef_lock_impl.h',
      'include/base/internal/cef_raw_scoped_refptr_mismatch_checker.h',
      'include/base/internal/cef_scoped_policy.h',
      'include/base/internal/cef_thread_checker_impl.h',
      'include/cef_api_hash.h',
      'include/cef_base.h',
      'include/cef_version.h',
      'include/internal/cef_export.h',
      'include/internal/cef_ptr.h',
      'include/internal/cef_string_wrappers.h',
      'include/internal/cef_time_wrappers.h',
      'include/internal/cef_types_wrappers.h',
    ],
    'includes_common_capi': [
      'include/internal/cef_dump_without_crashing_internal.h',
      'include/internal/cef_logging_internal.h',
      'include/internal/cef_string.h',
      'include/internal/cef_string_list.h',
      'include/internal/cef_string_map.h',
      'include/internal/cef_string_multimap.h',
      'include/internal/cef_string_types.h',
      'include/internal/cef_thread_internal.h',
      'include/internal/cef_time.h',
      'include/internal/cef_trace_event_internal.h',
      'include/internal/cef_types.h',
      'include/internal/cef_types_content_settings.h',
      'include/internal/cef_types_geometry.h',
      'include/internal/cef_types_runtime.h',
      'include/internal/cef_types_color.h',
    ],
    'includes_capi': [
      'include/capi/cef_base_capi.h',
    ],
    'includes_wrapper': [
      'include/wrapper/cef_byte_read_handler.h',
      'include/wrapper/cef_closure_task.h',
      'include/wrapper/cef_helpers.h',
      'include/wrapper/cef_message_router.h',
      'include/wrapper/cef_resource_manager.h',
      'include/wrapper/cef_scoped_temp_dir.h',
      'include/wrapper/cef_stream_resource_handler.h',
      'include/wrapper/cef_xml_object.h',
      'include/wrapper/cef_zip_archive.h',
    ],
    'includes_wrapper_mac': [
      'include/wrapper/cef_library_loader.h',
    ],
    'includes_win': [
      'include/cef_sandbox_win.h',
      'include/internal/cef_win.h',
    ],
    'includes_win_capi': [
      'include/internal/cef_app_win.h',
      'include/internal/cef_types_win.h',
    ],
    'includes_mac': [
      'include/base/cef_scoped_typeref_mac.h',
      'include/base/internal/cef_scoped_block_mac.h',
      'include/cef_application_mac.h',
      'include/cef_sandbox_mac.h',
      'include/internal/cef_mac.h',
    ],
    'includes_mac_capi': [
      'include/internal/cef_types_mac.h',
    ],
    'includes_linux': [
      'include/internal/cef_linux.h',
    ],
    'includes_linux_capi': [
      'include/internal/cef_types_linux.h',
    ],
    'libcef_sources_common': [
      'libcef_dll/cpptoc/cpptoc_ref_counted.h',
      'libcef_dll/cpptoc/cpptoc_scoped.h',
      'libcef_dll/ctocpp/base_ref_counted_ctocpp.cc',
      'libcef_dll/ctocpp/base_ref_counted_ctocpp.h',
      'libcef_dll/ctocpp/base_scoped_ctocpp.cc',
      'libcef_dll/ctocpp/base_scoped_ctocpp.h',
      'libcef_dll/ctocpp/ctocpp_ref_counted.h',
      'libcef_dll/ctocpp/ctocpp_scoped.h',
      'libcef_dll/libcef_dll.cc',
      'libcef_dll/libcef_dll2.cc',
      'libcef_dll/ptr_util.h',
      'libcef_dll/resource.h',
      'libcef_dll/shutdown_checker.cc',
      'libcef_dll/shutdown_checker.h',
      'libcef_dll/template_util.h',
      'libcef_dll/transfer_util.cc',
      'libcef_dll/transfer_util.h',
      'libcef_dll/wrapper_types.h',
    ],
    'libcef_dll_wrapper_sources_base': [
      'libcef_dll/base/cef_atomic_flag.cc',
      'libcef_dll/base/cef_callback_helpers.cc',
      'libcef_dll/base/cef_callback_internal.cc',
      'libcef_dll/base/cef_dump_without_crashing.cc',
      'libcef_dll/base/cef_lock.cc',
      'libcef_dll/base/cef_lock_impl.cc',
      'libcef_dll/base/cef_logging.cc',
      'libcef_dll/base/cef_ref_counted.cc',
      'libcef_dll/base/cef_thread_checker_impl.cc',
      'libcef_dll/base/cef_weak_ptr.cc',
    ],
    'libcef_dll_wrapper_sources_common': [
      'libcef_dll/cpptoc/base_ref_counted_cpptoc.cc',
      'libcef_dll/cpptoc/base_ref_counted_cpptoc.h',
      'libcef_dll/cpptoc/base_scoped_cpptoc.cc',
      'libcef_dll/cpptoc/base_scoped_cpptoc.h',
      'libcef_dll/cpptoc/cpptoc_ref_counted.h',
      'libcef_dll/cpptoc/cpptoc_scoped.h',
      'libcef_dll/ctocpp/ctocpp_ref_counted.h',
      'libcef_dll/ctocpp/ctocpp_scoped.h',
      'libcef_dll/shutdown_checker.cc',
      'libcef_dll/shutdown_checker.h',
      'libcef_dll/template_util.h',
      'libcef_dll/transfer_util.cc',
      'libcef_dll/transfer_util.h',
      'libcef_dll/wrapper_types.h',
      'libcef_dll/wrapper/cef_browser_info_map.h',
      'libcef_dll/wrapper/cef_byte_read_handler.cc',
      'libcef_dll/wrapper/cef_closure_task.cc',
      'libcef_dll/wrapper/cef_message_router.cc',
      'libcef_dll/wrapper/cef_message_router_utils.cc',
      'libcef_dll/wrapper/cef_message_router_utils.h',
      'libcef_dll/wrapper/cef_resource_manager.cc',
      'libcef_dll/wrapper/cef_scoped_temp_dir.cc',
      'libcef_dll/wrapper/cef_stream_resource_handler.cc',
      'libcef_dll/wrapper/cef_xml_object.cc',
      'libcef_dll/wrapper/cef_zip_archive.cc',
      'libcef_dll/wrapper/libcef_dll_wrapper.cc',
      'libcef_dll/wrapper/libcef_dll_wrapper2.cc',
    ],
    'libcef_dll_wrapper_sources_mac': [
      'libcef_dll/wrapper/cef_library_loader_mac.mm',
      'libcef_dll/wrapper/libcef_dll_dylib.cc',
    ],
    'shared_sources_browser': [
      'tests/shared/browser/client_app_browser.cc',
      'tests/shared/browser/client_app_browser.h',
      'tests/shared/browser/file_util.cc',
      'tests/shared/browser/file_util.h',
      'tests/shared/browser/geometry_util.cc',
      'tests/shared/browser/geometry_util.h',
      'tests/shared/browser/main_message_loop.cc',
      'tests/shared/browser/main_message_loop.h',
      'tests/shared/browser/main_message_loop_external_pump.cc',
      'tests/shared/browser/main_message_loop_external_pump.h',
      'tests/shared/browser/main_message_loop_std.cc',
      'tests/shared/browser/main_message_loop_std.h',
      'tests/shared/browser/resource_util.h',
    ],
    'shared_sources_common': [
      'tests/shared/common/binary_value_utils.cc',
      'tests/shared/common/binary_value_utils.h',
      'tests/shared/common/client_app.cc',
      'tests/shared/common/client_app.h',
      'tests/shared/common/client_app_other.cc',
      'tests/shared/common/client_app_other.h',
      'tests/shared/common/client_switches.cc',
      'tests/shared/common/client_switches.h',
      'tests/shared/common/string_util.cc',
      'tests/shared/common/string_util.h',
    ],
    'shared_sources_renderer': [
      'tests/shared/renderer/client_app_renderer.cc',
      'tests/shared/renderer/client_app_renderer.h',
    ],
    'shared_sources_resources': [
      'tests/shared/resources/osr_test.html',
      'tests/shared/resources/pdf.html',
      'tests/shared/resources/pdf.pdf',
      'tests/shared/resources/window_icon.1x.png',
      'tests/shared/resources/window_icon.2x.png',
    ],
    'shared_sources_linux': [
      'tests/shared/browser/main_message_loop_external_pump_linux.cc',
      'tests/shared/browser/resource_util_posix.cc',
    ],
    'shared_sources_mac': [
      'tests/shared/browser/main_message_loop_external_pump_mac.mm',
      'tests/shared/browser/resource_util_mac.mm',
      'tests/shared/browser/resource_util_posix.cc',
    ],
    'shared_sources_mac_helper': [
      'tests/shared/process_helper_mac.cc',
    ],
    'shared_sources_win': [
      'tests/shared/browser/main_message_loop_external_pump_win.cc',
      'tests/shared/browser/resource_util_win.cc',
      'tests/shared/browser/util_win.cc',
      'tests/shared/browser/util_win.h',
    ],
    'cefclient_sources_browser': [
      'tests/cefclient/browser/base_client_handler.cc',
      'tests/cefclient/browser/base_client_handler.h',
      'tests/cefclient/browser/binary_transfer_test.cc',
      'tests/cefclient/browser/binary_transfer_test.h',
      'tests/cefclient/browser/binding_test.cc',
      'tests/cefclient/browser/binding_test.h',
      'tests/cefclient/browser/browser_window.cc',
      'tests/cefclient/browser/browser_window.h',
      'tests/cefclient/browser/bytes_write_handler.cc',
      'tests/cefclient/browser/bytes_write_handler.h',
      'tests/cefclient/browser/client_app_delegates_browser.cc',
      'tests/cefclient/browser/client_browser.cc',
      'tests/cefclient/browser/client_browser.h',
      'tests/cefclient/browser/client_handler.cc',
      'tests/cefclient/browser/client_handler.h',
      'tests/cefclient/browser/client_handler_osr.cc',
      'tests/cefclient/browser/client_handler_osr.h',
      'tests/cefclient/browser/client_handler_std.cc',
      'tests/cefclient/browser/client_handler_std.h',
      'tests/cefclient/browser/client_prefs.cc',
      'tests/cefclient/browser/client_prefs.h',
      'tests/cefclient/browser/client_types.h',
      'tests/cefclient/browser/default_client_handler.cc',
      'tests/cefclient/browser/default_client_handler.h',
      'tests/cefclient/browser/dialog_test.cc',
      'tests/cefclient/browser/dialog_test.h',
      'tests/cefclient/browser/hang_test.cc',
      'tests/cefclient/browser/hang_test.h',
      'tests/cefclient/browser/image_cache.cc',
      'tests/cefclient/browser/image_cache.h',
      'tests/cefclient/browser/main_context.cc',
      'tests/cefclient/browser/main_context.h',
      'tests/cefclient/browser/main_context_impl.cc',
      'tests/cefclient/browser/main_context_impl.h',
      'tests/cefclient/browser/media_router_test.cc',
      'tests/cefclient/browser/media_router_test.h',
      'tests/cefclient/browser/osr_dragdrop_events.h',
      'tests/cefclient/browser/osr_renderer.h',
      'tests/cefclient/browser/osr_renderer.cc',
      'tests/cefclient/browser/osr_renderer_settings.h',
      'tests/cefclient/browser/preferences_test.cc',
      'tests/cefclient/browser/preferences_test.h',
      'tests/cefclient/browser/resource.h',
      'tests/cefclient/browser/response_filter_test.cc',
      'tests/cefclient/browser/response_filter_test.h',
      'tests/cefclient/browser/root_window.cc',
      'tests/cefclient/browser/root_window.h',
      'tests/cefclient/browser/root_window_create.cc',
      'tests/cefclient/browser/root_window_manager.cc',
      'tests/cefclient/browser/root_window_manager.h',
      'tests/cefclient/browser/root_window_views.cc',
      'tests/cefclient/browser/root_window_views.h',
      'tests/cefclient/browser/scheme_test.cc',
      'tests/cefclient/browser/scheme_test.h',
      'tests/cefclient/browser/server_test.cc',
      'tests/cefclient/browser/server_test.h',
      'tests/cefclient/browser/task_manager_test.cc',
      'tests/cefclient/browser/task_manager_test.h',
      'tests/cefclient/browser/temp_window.h',
      'tests/cefclient/browser/test_runner.cc',
      'tests/cefclient/browser/test_runner.h',
      'tests/cefclient/browser/urlrequest_test.cc',
      'tests/cefclient/browser/urlrequest_test.h',
      'tests/cefclient/browser/views_menu_bar.cc',
      'tests/cefclient/browser/views_menu_bar.h',
      'tests/cefclient/browser/views_overlay_browser.cc',
      'tests/cefclient/browser/views_overlay_browser.h',
      'tests/cefclient/browser/views_overlay_controls.cc',
      'tests/cefclient/browser/views_overlay_controls.h',
      'tests/cefclient/browser/views_style.cc',
      'tests/cefclient/browser/views_style.h',
      'tests/cefclient/browser/views_window.cc',
      'tests/cefclient/browser/views_window.h',
      'tests/cefclient/browser/window_test.cc',
      'tests/cefclient/browser/window_test.h',
      'tests/cefclient/browser/window_test_runner.cc',
      'tests/cefclient/browser/window_test_runner.h',
      'tests/cefclient/browser/window_test_runner_views.cc',
      'tests/cefclient/browser/window_test_runner_views.h',
    ],
    'cefclient_sources_common': [
      'tests/cefclient/common/client_app_delegates_common.cc',
      'tests/cefclient/common/scheme_test_common.cc',
      'tests/cefclient/common/scheme_test_common.h',
    ],
    'cefclient_sources_renderer': [
      'tests/cefclient/renderer/client_app_delegates_renderer.cc',
      'tests/cefclient/renderer/client_renderer.cc',
      'tests/cefclient/renderer/client_renderer.h',
      'tests/cefclient/renderer/ipc_performance_test.cc',
      'tests/cefclient/renderer/ipc_performance_test.h',
      'tests/cefclient/renderer/performance_test.cc',
      'tests/cefclient/renderer/performance_test.h',
      'tests/cefclient/renderer/performance_test_setup.h',
      'tests/cefclient/renderer/performance_test_tests.cc',
    ],
    'cefclient_sources_resources': [
      'tests/cefclient/resources/binary_transfer.html',
      'tests/cefclient/resources/binding.html',
      'tests/cefclient/resources/dialogs.html',
      'tests/cefclient/resources/draggable.html',
      'tests/cefclient/resources/hang.html',
      'tests/cefclient/resources/ipc_performance.html',
      'tests/cefclient/resources/localstorage.html',
      'tests/cefclient/resources/logo.png',
      'tests/cefclient/resources/media_router.html',
      'tests/cefclient/resources/menu_icon.1x.png',
      'tests/cefclient/resources/menu_icon.2x.png',
      'tests/cefclient/resources/other_tests.html',
      'tests/cefclient/resources/performance.html',
      'tests/cefclient/resources/performance2.html',
      'tests/cefclient/resources/preferences.html',
      'tests/cefclient/resources/response_filter.html',
      'tests/cefclient/resources/server.html',
      'tests/cefclient/resources/task_manager.html',
      'tests/cefclient/resources/transparency.html',
      'tests/cefclient/resources/urlrequest.html',
      'tests/cefclient/resources/websocket.html',
      'tests/cefclient/resources/window.html',
      'tests/cefclient/resources/xmlhttprequest.html',
    ],
    'cefclient_sources_win': [
      'tests/cefclient/browser/browser_window_osr_win.cc',
      'tests/cefclient/browser/browser_window_osr_win.h',
      'tests/cefclient/browser/browser_window_std_win.cc',
      'tests/cefclient/browser/browser_window_std_win.h',
      'tests/cefclient/browser/main_context_impl_win.cc',
      'tests/cefclient/browser/main_message_loop_multithreaded_win.cc',
      'tests/cefclient/browser/main_message_loop_multithreaded_win.h',
      'tests/cefclient/browser/osr_accessibility_helper.cc',
      'tests/cefclient/browser/osr_accessibility_helper.h',
      'tests/cefclient/browser/osr_accessibility_node.cc',
      'tests/cefclient/browser/osr_accessibility_node.h',
      'tests/cefclient/browser/osr_accessibility_node_win.cc',
      'tests/cefclient/browser/osr_dragdrop_win.cc',
      'tests/cefclient/browser/osr_dragdrop_win.h',
      'tests/cefclient/browser/osr_ime_handler_win.cc',
      'tests/cefclient/browser/osr_ime_handler_win.h',
      'tests/cefclient/browser/osr_d3d11_win.cc',
      'tests/cefclient/browser/osr_d3d11_win.h',
      'tests/cefclient/browser/osr_render_handler_win.cc',
      'tests/cefclient/browser/osr_render_handler_win.h',
      'tests/cefclient/browser/osr_render_handler_win_d3d11.cc',
      'tests/cefclient/browser/osr_render_handler_win_d3d11.h',
      'tests/cefclient/browser/osr_render_handler_win_gl.cc',
      'tests/cefclient/browser/osr_render_handler_win_gl.h',
      'tests/cefclient/browser/osr_window_win.cc',
      'tests/cefclient/browser/osr_window_win.h',
      'tests/cefclient/browser/resource_util_win_idmap.cc',
      'tests/cefclient/browser/root_window_win.cc',
      'tests/cefclient/browser/root_window_win.h',
      'tests/cefclient/browser/temp_window_win.cc',
      'tests/cefclient/browser/temp_window_win.h',
      'tests/cefclient/browser/window_test_runner_win.cc',
      'tests/cefclient/browser/window_test_runner_win.h',
      'tests/cefclient/cefclient_win.cc',
    ],
    'cefclient_sources_resources_win': [
      'tests/cefclient/win/cefclient.exe.manifest',
      'tests/cefclient/win/cefclient.ico',
      'tests/cefclient/win/small.ico',
    ],
    'cefclient_sources_resources_win_rc': [
      'tests/cefclient/win/cefclient.rc',
    ],
    'cefclient_sources_mac': [
      'tests/cefclient/browser/browser_window_osr_mac.h',
      'tests/cefclient/browser/browser_window_osr_mac.mm',
      'tests/cefclient/browser/browser_window_std_mac.h',
      'tests/cefclient/browser/browser_window_std_mac.mm',
      'tests/cefclient/browser/main_context_impl_posix.cc',
      'tests/cefclient/browser/osr_accessibility_helper.cc',
      'tests/cefclient/browser/osr_accessibility_helper.h',
      'tests/cefclient/browser/osr_accessibility_node.cc',
      'tests/cefclient/browser/osr_accessibility_node.h',
      'tests/cefclient/browser/osr_accessibility_node_mac.mm',
      'tests/cefclient/browser/root_window_mac.h',
      'tests/cefclient/browser/root_window_mac.mm',
      'tests/cefclient/browser/temp_window_mac.h',
      'tests/cefclient/browser/temp_window_mac.mm',
      'tests/cefclient/browser/text_input_client_osr_mac.h',
      'tests/cefclient/browser/text_input_client_osr_mac.mm',
      'tests/cefclient/browser/views_window_mac.mm',
      'tests/cefclient/browser/window_test_runner_mac.h',
      'tests/cefclient/browser/window_test_runner_mac.mm',
      'tests/cefclient/cefclient_mac.mm',
    ],
    'cefclient_bundle_resources_mac': [
      'tests/cefclient/mac/cefclient.icns',
      'tests/cefclient/mac/English.lproj/InfoPlist.strings',
      'tests/cefclient/mac/English.lproj/MainMenu.xib',
      'tests/cefclient/mac/Info.plist.in',
    ],
    'cefclient_sources_linux': [
      'tests/cefclient/browser/browser_window_osr_gtk.cc',
      'tests/cefclient/browser/browser_window_osr_gtk.h',
      'tests/cefclient/browser/browser_window_std_gtk.cc',
      'tests/cefclient/browser/browser_window_std_gtk.h',
      'tests/cefclient/browser/dialog_handler_gtk.cc',
      'tests/cefclient/browser/dialog_handler_gtk.h',
      'tests/cefclient/browser/main_context_impl_posix.cc',
      'tests/cefclient/browser/main_message_loop_multithreaded_gtk.cc',
      'tests/cefclient/browser/main_message_loop_multithreaded_gtk.h',
      'tests/cefclient/browser/print_handler_gtk.cc',
      'tests/cefclient/browser/print_handler_gtk.h',
      'tests/cefclient/browser/resource_util_linux.cc',
      'tests/cefclient/browser/root_window_gtk.cc',
      'tests/cefclient/browser/root_window_gtk.h',
      'tests/cefclient/browser/temp_window_x11.cc',
      'tests/cefclient/browser/temp_window_x11.h',
      'tests/cefclient/browser/util_gtk.cc',
      'tests/cefclient/browser/util_gtk.h',
      'tests/cefclient/browser/window_test_runner_gtk.cc',
      'tests/cefclient/browser/window_test_runner_gtk.h',
      'tests/cefclient/cefclient_gtk.cc',
    ],
    'cefsimple_sources_common': [
      'tests/cefsimple/simple_app.cc',
      'tests/cefsimple/simple_app.h',
      'tests/cefsimple/simple_handler.cc',
      'tests/cefsimple/simple_handler.h',
    ],
    'cefsimple_sources_win': [
      'tests/cefsimple/cefsimple_win.cc',
      'tests/cefsimple/resource.h',
      'tests/cefsimple/simple_handler_win.cc',
    ],
    'cefsimple_sources_resources_win': [
      'tests/cefsimple/win/cefsimple.exe.manifest',
      'tests/cefsimple/win/cefsimple.ico',
      'tests/cefsimple/win/small.ico',
    ],
    'cefsimple_sources_resources_win_rc': [
      'tests/cefsimple/win/cefsimple.rc',
    ],
    'cefsimple_sources_mac': [
      'tests/cefsimple/cefsimple_mac.mm',
      'tests/cefsimple/simple_handler_mac.mm',
    ],
    'cefsimple_sources_mac_helper': [
      'tests/cefsimple/process_helper_mac.cc',
    ],
    'cefsimple_bundle_resources_mac': [
      'tests/cefsimple/mac/cefsimple.icns',
      'tests/cefsimple/mac/English.lproj/InfoPlist.strings',
      'tests/cefsimple/mac/English.lproj/MainMenu.xib',
      'tests/cefsimple/mac/Info.plist.in',
    ],
    'cefsimple_sources_linux': [
      'tests/cefsimple/cefsimple_linux.cc',
      'tests/cefsimple/simple_handler_linux.cc',
    ],
    'ceftests_data_resources': [
      'tests/ceftests/resources/net/data/ssl/certificates/expired_cert.pem',
      'tests/ceftests/resources/net/data/ssl/certificates/localhost_cert.pem',
      'tests/ceftests/resources/net/data/ssl/certificates/ok_cert.pem',
      'tests/ceftests/resources/net/data/ssl/certificates/root_ca_cert.pem',
    ],
    'ceftests_sources_common': [
      'tests/ceftests/audio_output_unittest.cc',
      'tests/ceftests/browser_info_map_unittest.cc',
      'tests/ceftests/certificate_error_unittest.cc',
      'tests/ceftests/command_line_unittest.cc',
      'tests/ceftests/cookie_unittest.cc',
      'tests/ceftests/cors_unittest.cc',
      'tests/ceftests/devtools_message_unittest.cc',
      'tests/ceftests/dialog_unittest.cc',
      'tests/ceftests/display_unittest.cc',
      'tests/ceftests/dom_unittest.cc',
      'tests/ceftests/download_unittest.cc',
      'tests/ceftests/draggable_regions_unittest.cc',
      'tests/ceftests/file_util_unittest.cc',
      'tests/ceftests/frame_handler_unittest.cc',
      'tests/ceftests/frame_unittest.cc',
      'tests/ceftests/hsts_redirect_unittest.cc',
      'tests/ceftests/image_unittest.cc',
      'tests/ceftests/image_util.cc',
      'tests/ceftests/image_util.h',
      'tests/ceftests/jsdialog_unittest.cc',
      'tests/ceftests/life_span_unittest.cc',
      'tests/ceftests/media_access_unittest.cc',
      'tests/ceftests/message_router_binary_unittest.cc',
      'tests/ceftests/message_router_harness_unittest.cc',
      'tests/ceftests/message_router_multi_query_unittest.cc',
      'tests/ceftests/message_router_single_query_unittest.cc',
      'tests/ceftests/message_router_threshold_unittest.cc',
      'tests/ceftests/message_router_unittest_utils.cc',
      'tests/ceftests/message_router_unittest_utils.h',
      'tests/ceftests/navigation_unittest.cc',
      'tests/ceftests/os_rendering_unittest.cc',
      'tests/ceftests/osr_accessibility_unittest.cc',
      'tests/ceftests/osr_display_unittest.cc',
      'tests/ceftests/parser_unittest.cc',
      'tests/ceftests/pdf_viewer_unittest.cc',
      'tests/ceftests/permission_prompt_unittest.cc',
      'tests/ceftests/preference_unittest.cc',
      'tests/ceftests/print_unittest.cc',
      'tests/ceftests/process_message_unittest.cc',
      'tests/ceftests/request_context_unittest.cc',
      'tests/ceftests/request_handler_unittest.cc',
      'tests/ceftests/request_unittest.cc',
      'tests/ceftests/response_unittest.cc',
      'tests/ceftests/resource.h',
      'tests/ceftests/resource_manager_unittest.cc',
      'tests/ceftests/resource_request_handler_unittest.cc',
      'tests/ceftests/routing_test_handler.cc',
      'tests/ceftests/routing_test_handler.h',
      'tests/ceftests/run_all_unittests.cc',
      'tests/ceftests/scheme_handler_unittest.cc',
      'tests/ceftests/scoped_temp_dir_unittest.cc',
      'tests/ceftests/server_unittest.cc',
      'tests/ceftests/send_shared_process_message_unittest.cc',
      "tests/ceftests/shared_process_message_unittest.cc",
      'tests/ceftests/stream_unittest.cc',
      'tests/ceftests/stream_resource_handler_unittest.cc',
      'tests/ceftests/string_unittest.cc',
      'tests/ceftests/client_app_delegates.cc',
      'tests/ceftests/task_unittest.cc',
      'tests/ceftests/test_handler.cc',
      'tests/ceftests/test_handler.h',
      'tests/ceftests/test_request.cc',
      'tests/ceftests/test_request.h',
      'tests/ceftests/test_server.cc',
      'tests/ceftests/test_server.h',
      'tests/ceftests/test_server_observer.h',
      'tests/ceftests/test_server_observer.cc',
      'tests/ceftests/test_server_observer_unittest.cc',
      'tests/ceftests/test_server_manager.h',
      'tests/ceftests/test_server_manager.cc',
      'tests/ceftests/test_server_runner.h',
      'tests/ceftests/test_server_runner.cc',
      'tests/ceftests/test_server_runner_normal.cc',
      'tests/ceftests/test_server_runner_test.cc',
      'tests/ceftests/test_server_unittest.cc',
      'tests/ceftests/test_suite.cc',
      'tests/ceftests/test_suite.h',
      'tests/ceftests/test_util.cc',
      'tests/ceftests/test_util.h',
      'tests/ceftests/time_unittest.cc',
      'tests/ceftests/thread_helper.cc',
      'tests/ceftests/thread_helper.h',
      'tests/ceftests/thread_unittest.cc',
      'tests/ceftests/tracing_unittest.cc',
      'tests/ceftests/track_callback.h',
      'tests/ceftests/translator_unittest.cc',
      'tests/ceftests/urlrequest_unittest.cc',
      'tests/ceftests/v8_unittest.cc',
      'tests/ceftests/values_unittest.cc',
      'tests/ceftests/version_unittest.cc',
      'tests/ceftests/views/button_unittest.cc',
      'tests/ceftests/views/panel_unittest.cc',
      'tests/ceftests/views/scroll_view_unittest.cc',
      'tests/ceftests/views/test_window_delegate.cc',
      'tests/ceftests/views/test_window_delegate.h',
      'tests/ceftests/views/textfield_unittest.cc',
      'tests/ceftests/views/window_unittest.cc',
      'tests/ceftests/waitable_event_unittest.cc',
      'tests/ceftests/webui_unittest.cc',
      'tests/ceftests/xml_reader_unittest.cc',
      'tests/ceftests/zip_reader_unittest.cc',
    ],
    'ceftests_sources_win': [
      'tests/ceftests/resource_util_win_dir.cc',
      'tests/ceftests/resource_util_win_idmap.cc',
    ],
    'ceftests_sources_resources_win': [
      'tests/ceftests/win/ceftests.exe.manifest',
      'tests/ceftests/win/ceftests.ico',
      'tests/ceftests/win/small.ico',
    ],
    'ceftests_sources_resources_win_rc': [
      'tests/ceftests/win/ceftests.rc',
    ],
    'ceftests_sources_mac': [
      'tests/ceftests/os_rendering_unittest_mac.h',
      'tests/ceftests/os_rendering_unittest_mac.mm',
      'tests/ceftests/run_all_unittests_mac.mm',
    ],
    'ceftests_sources_mac_browser_shared': [
      'tests/shared/renderer/client_app_renderer.h',
    ],
    'ceftests_sources_mac_helper_shared': [
      'tests/shared/browser/client_app_browser.h',
      'tests/shared/browser/file_util.cc',
      'tests/shared/browser/file_util.h',
      'tests/shared/browser/resource_util.h',
      'tests/shared/browser/resource_util_mac.mm',
      'tests/shared/browser/resource_util_posix.cc',
    ],
    'ceftests_sources_mac_helper': [
      'tests/ceftests/audio_output_unittest.cc',
      'tests/ceftests/client_app_delegates.cc',
      'tests/ceftests/cookie_unittest.cc',
      'tests/ceftests/cors_unittest.cc',
      'tests/ceftests/dom_unittest.cc',
      'tests/ceftests/frame_unittest.cc',
      'tests/ceftests/media_access_unittest.cc',
      'tests/ceftests/message_router_binary_unittest.cc',
      'tests/ceftests/message_router_harness_unittest.cc',
      'tests/ceftests/message_router_multi_query_unittest.cc',
      'tests/ceftests/message_router_single_query_unittest.cc',
      'tests/ceftests/message_router_threshold_unittest.cc',
      'tests/ceftests/message_router_unittest_utils.cc',
      'tests/ceftests/message_router_unittest_utils.h',
      'tests/ceftests/navigation_unittest.cc',
      'tests/ceftests/pdf_viewer_unittest.cc',
      'tests/ceftests/permission_prompt_unittest.cc',
      'tests/ceftests/preference_unittest.cc',
      'tests/ceftests/process_message_unittest.cc',
      'tests/ceftests/request_handler_unittest.cc',
      'tests/ceftests/request_unittest.cc',
      'tests/ceftests/response_unittest.cc',
      'tests/ceftests/resource_request_handler_unittest.cc',
      'tests/ceftests/routing_test_handler.cc',
      'tests/ceftests/routing_test_handler.h',
      'tests/ceftests/scheme_handler_unittest.cc',
      'tests/ceftests/send_shared_process_message_unittest.cc',
      "tests/ceftests/shared_process_message_unittest.cc",
      'tests/ceftests/urlrequest_unittest.cc',
      'tests/ceftests/test_handler.cc',
      'tests/ceftests/test_handler.h',
      'tests/ceftests/test_request.cc',
      'tests/ceftests/test_request.h',
      'tests/ceftests/test_server.cc',
      'tests/ceftests/test_server.h',
      'tests/ceftests/test_server_observer.h',
      'tests/ceftests/test_server_observer.cc',
      'tests/ceftests/test_server_manager.h',
      'tests/ceftests/test_server_manager.cc',
      'tests/ceftests/test_server_runner.h',
      'tests/ceftests/test_server_runner.cc',
      'tests/ceftests/test_server_runner_normal.cc',
      'tests/ceftests/test_server_runner_test.cc',
      'tests/ceftests/test_suite.cc',
      'tests/ceftests/test_suite.h',
      'tests/ceftests/test_util.cc',
      'tests/ceftests/test_util.h',
      'tests/ceftests/track_callback.h',
      'tests/ceftests/thread_helper.cc',
      'tests/ceftests/thread_helper.h',
      'tests/ceftests/thread_unittest.cc',
      'tests/ceftests/tracing_unittest.cc',
      'tests/ceftests/v8_unittest.cc',
    ],
    'ceftests_bundle_resources_mac': [
      'tests/ceftests/mac/ceftests.icns',
      'tests/ceftests/mac/English.lproj/InfoPlist.strings',
      'tests/ceftests/mac/English.lproj/MainMenu.xib',
      'tests/ceftests/mac/Info.plist.in',
    ],
    'ceftests_sources_linux': [
      'tests/ceftests/resource_util_linux.cc',
    ],
  },
}
