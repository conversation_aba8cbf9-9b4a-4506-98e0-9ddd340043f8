# Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
# reserved. Use of this source code is governed by a BSD-style license that
# can be found in the LICENSE file.

PRODUCT_NAME = "cefclient"
PKG_NAME = "//tests/{}".format(PRODUCT_NAME)

# Allow access from subpackages only.
package(default_visibility = [
    ":__subpackages__",
])

load("//bazel:library_helpers.bzl", "declare_cc_library", "declare_objc_library")
load("@rules_cc//cc:defs.bzl", "cc_library")

#
# Source file lists.
#

srcs_common = [
    "common/client_app_delegates_common.cc",
    "common/scheme_test_common.cc",
    "common/scheme_test_common.h"
]

srcs_renderer = [
    "renderer/client_app_delegates_renderer.cc",
    "renderer/client_renderer.cc",
    "renderer/client_renderer.h",
    "renderer/ipc_performance_test.cc",
    "renderer/ipc_performance_test.h",
    "renderer/performance_test.cc",
    "renderer/performance_test.h",
    "renderer/performance_test_setup.h",
    "renderer/performance_test_tests.cc"
]

srcs_browser = [
    "browser/base_client_handler.cc",
    "browser/base_client_handler.h",
    "browser/binary_transfer_test.cc",
    "browser/binary_transfer_test.h",
    "browser/binding_test.cc",
    "browser/binding_test.h",
    "browser/browser_window.cc",
    "browser/browser_window.h",
    "browser/bytes_write_handler.cc",
    "browser/bytes_write_handler.h",
    "browser/client_app_delegates_browser.cc",
    "browser/client_browser.cc",
    "browser/client_browser.h",
    "browser/client_handler.cc",
    "browser/client_handler.h",
    "browser/client_handler_osr.cc",
    "browser/client_handler_osr.h",
    "browser/client_handler_std.cc",
    "browser/client_handler_std.h",
    "browser/client_prefs.cc",
    "browser/client_prefs.h",
    "browser/client_types.h",
    "browser/default_client_handler.cc",
    "browser/default_client_handler.h",
    "browser/dialog_test.cc",
    "browser/dialog_test.h",
    "browser/hang_test.cc",
    "browser/hang_test.h",
    "browser/image_cache.cc",
    "browser/image_cache.h",
    "browser/main_context.cc",
    "browser/main_context.h",
    "browser/main_context_impl.cc",
    "browser/main_context_impl.h",
    "browser/media_router_test.cc",
    "browser/media_router_test.h",
    "browser/osr_dragdrop_events.h",
    "browser/osr_renderer.h",
    "browser/osr_renderer.cc",
    "browser/osr_renderer_settings.h",
    "browser/preferences_test.cc",
    "browser/preferences_test.h",
    "browser/resource.h",
    "browser/response_filter_test.cc",
    "browser/response_filter_test.h",
    "browser/root_window.cc",
    "browser/root_window.h",
    "browser/root_window_create.cc",
    "browser/root_window_manager.cc",
    "browser/root_window_manager.h",
    "browser/root_window_views.cc",
    "browser/root_window_views.h",
    "browser/scheme_test.cc",
    "browser/scheme_test.h",
    "browser/server_test.cc",
    "browser/server_test.h",
    "browser/task_manager_test.cc",
    "browser/task_manager_test.h",
    "browser/temp_window.h",
    "browser/test_runner.cc",
    "browser/test_runner.h",
    "browser/urlrequest_test.cc",
    "browser/urlrequest_test.h",
    "browser/views_menu_bar.cc",
    "browser/views_menu_bar.h",
    "browser/views_overlay_browser.cc",
    "browser/views_overlay_browser.h",
    "browser/views_overlay_controls.cc",
    "browser/views_overlay_controls.h",
    "browser/views_style.cc",
    "browser/views_style.h",
    "browser/views_window.cc",
    "browser/views_window.h",
    "browser/window_test.cc",
    "browser/window_test.h",
    "browser/window_test_runner.cc",
    "browser/window_test_runner.h",
    "browser/window_test_runner_views.cc",
    "browser/window_test_runner_views.h"
]

srcs_browser_linux = [
    "browser/browser_window_osr_gtk.cc",
    "browser/browser_window_osr_gtk.h",
    "browser/browser_window_std_gtk.cc",
    "browser/browser_window_std_gtk.h",
    "browser/dialog_handler_gtk.cc",
    "browser/dialog_handler_gtk.h",
    "browser/main_context_impl_posix.cc",
    "browser/main_message_loop_multithreaded_gtk.cc",
    "browser/main_message_loop_multithreaded_gtk.h",
    "browser/print_handler_gtk.cc",
    "browser/print_handler_gtk.h",
    "browser/resource_util_linux.cc",
    "browser/root_window_gtk.cc",
    "browser/root_window_gtk.h",
    "browser/temp_window_x11.cc",
    "browser/temp_window_x11.h",
    "browser/util_gtk.cc",
    "browser/util_gtk.h",
    "browser/window_test_runner_gtk.cc",
    "browser/window_test_runner_gtk.h",
    "cefclient_gtk.cc"
]

srcs_browser_mac = [
    "browser/browser_window_osr_mac.h",
    "browser/browser_window_osr_mac.mm",
    "browser/browser_window_std_mac.h",
    "browser/browser_window_std_mac.mm",
    "browser/main_context_impl_posix.cc",
    "browser/osr_accessibility_helper.cc",
    "browser/osr_accessibility_helper.h",
    "browser/osr_accessibility_node.cc",
    "browser/osr_accessibility_node.h",
    "browser/osr_accessibility_node_mac.mm",
    "browser/root_window_mac.h",
    "browser/root_window_mac.mm",
    "browser/temp_window_mac.h",
    "browser/temp_window_mac.mm",
    "browser/text_input_client_osr_mac.h",
    "browser/text_input_client_osr_mac.mm",
    "browser/views_window_mac.mm",
    "browser/window_test_runner_mac.h",
    "browser/window_test_runner_mac.mm",
    "cefclient_mac.mm"
]

srcs_browser_win = [
    "browser/browser_window_osr_win.cc",
    "browser/browser_window_osr_win.h",
    "browser/browser_window_std_win.cc",
    "browser/browser_window_std_win.h",
    "browser/main_context_impl_win.cc",
    "browser/main_message_loop_multithreaded_win.cc",
    "browser/main_message_loop_multithreaded_win.h",
    "browser/osr_accessibility_helper.cc",
    "browser/osr_accessibility_helper.h",
    "browser/osr_accessibility_node.cc",
    "browser/osr_accessibility_node.h",
    "browser/osr_accessibility_node_win.cc",
    "browser/osr_dragdrop_win.cc",
    "browser/osr_dragdrop_win.h",
    "browser/osr_ime_handler_win.cc",
    "browser/osr_ime_handler_win.h",
    "browser/osr_d3d11_win.cc",
    "browser/osr_d3d11_win.h",
    "browser/osr_render_handler_win.cc",
    "browser/osr_render_handler_win.h",
    "browser/osr_render_handler_win_d3d11.cc",
    "browser/osr_render_handler_win_d3d11.h",
    "browser/osr_render_handler_win_gl.cc",
    "browser/osr_render_handler_win_gl.h",
    "browser/osr_window_win.cc",
    "browser/osr_window_win.h",
    "browser/resource_util_win_idmap.cc",
    "browser/root_window_win.cc",
    "browser/root_window_win.h",
    "browser/temp_window_win.cc",
    "browser/temp_window_win.h",
    "browser/window_test_runner_win.cc",
    "browser/window_test_runner_win.h",
    "cefclient_win.cc"
]

srcs_resources = [
    "resources/binary_transfer.html",
    "resources/binding.html",
    "resources/dialogs.html",
    "resources/draggable.html",
    "resources/hang.html",
    "resources/ipc_performance.html",
    "resources/localstorage.html",
    "resources/logo.png",
    "resources/media_router.html",
    "resources/menu_icon.1x.png",
    "resources/menu_icon.2x.png",
    "resources/other_tests.html",
    "resources/performance.html",
    "resources/performance2.html",
    "resources/preferences.html",
    "resources/response_filter.html",
    "resources/server.html",
    "resources/task_manager.html",
    "resources/transparency.html",
    "resources/urlrequest.html",
    "resources/websocket.html",
    "resources/window.html",
    "resources/xmlhttprequest.html"
]

filegroup(
    name = "Resources",
    srcs = srcs_resources,
)

#
# MacOS targets.
#

declare_objc_library(
    name = "BrowserLibMac",
    srcs = srcs_common + srcs_browser + srcs_browser_mac,
    target_compatible_with = [
        "@platforms//os:macos",
    ],
    deps = [
        "//:cef_wrapper",
        "//tests/shared:BrowserLibMac",
    ],
)

declare_objc_library(
    name = "RendererLibMac",
    srcs = srcs_common + srcs_renderer,
    target_compatible_with = [
        "@platforms//os:macos",
    ],
    deps = [
        "//:cef_wrapper",
        "//tests/shared:RendererLibMac",
    ],
)

#
# Windows targets.
#

# Allow access to resource.h from the declare_exe target.
cc_library(
    name = "ResourceH",
    hdrs = [
        "browser/resource.h"
    ]
)

# Include files directly in the declare_exe target. This simplifies the build
# configuration and avoids issues with Windows discarding symbols (like WinMain)
# when linking libraries.
filegroup(
    name = "SrcsWin",
    srcs = srcs_common + srcs_browser + srcs_browser_win + srcs_renderer,
    target_compatible_with = [
        "@platforms//os:windows",
    ],
)

#
# Linux targets.
#

# Include files directly in the declare_exe target. This simplifies the build
# configuration.
filegroup(
    name = "SrcsLinux",
    srcs = srcs_common + srcs_browser + srcs_browser_linux + srcs_renderer,
    target_compatible_with = [
        "@platforms//os:linux",
    ],
)

#
# Alias to platform-specific build targets.
#

alias(
    name = PRODUCT_NAME,
    actual = select({
        "@platforms//os:linux": "{}/linux:{}".format(PKG_NAME, PRODUCT_NAME),
        "@platforms//os:macos": "{}/mac:{}".format(PKG_NAME, PRODUCT_NAME),
        "@platforms//os:windows": "{}/win:{}".format(PKG_NAME, PRODUCT_NAME),
    }),
)
