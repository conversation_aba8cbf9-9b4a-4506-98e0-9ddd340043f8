name: Dev Build

on:
  push:
    tags:
    - 'dev-*'

#env:
  # Customize the CMake build type here (Release, Debug, Debug, etc.)
  # BUILD_TYPE: Debug

jobs:
  clone:
    name: Clone & Update Project
    runs-on: [self-hosted, linux, platform]

    steps:
    - name: Clone Repository
      run: |
        git rev-parse || gh repo clone IMAXAGaming/IMAXAPlatform . -- --depth=1 --branch ${{ github.ref_name }}
        gh repo sync
        git fetch origin --tags --force
        git checkout ${{ github.ref_name }}
    
    - name: Update Dependencies
      run: |
        git submodule update --init --recursive
  
  build:
    name: Configure & Build All Targets (Debug)
    # The CMake configure and build commands are platform agnostic and should work equally well on Windows or Mac.
    # You can convert this to a matrix build if you need cross-platform coverage.
    # See: https://docs.github.com/en/free-pro-team@latest/actions/learn-github-actions/managing-complex-workflows#using-a-build-matrix
    runs-on: [self-hosted, linux, platform]

    steps:
    - name: Clean
      run: ./toolchain/clean.sh 18.04 Debug

    - name: Configure
      # Configure CMake in a 'build' subdirectory. `CMAKE_BUILD_TYPE` is only required if you are using a single-configuration generator such as make.
      # See https://cmake.org/cmake/help/latest/variable/CMAKE_BUILD_TYPE.html?highlight=cmake_build_type
      run: ./toolchain/configure.sh 18.04 Debug

    - name: Build
      # Build your program with the given configuration
      run: ./toolchain/build.sh 18.04 Debug all
  
  publish:
    name: Publish Binaries
    runs-on: [self-hosted, linux, platform]
    needs: [ buildDebug ]

    steps:
    - name: Get Output Folder Name
      run: echo "DEST_FOLDER=`git describe --abbrev=15`_`date +%F_%H%M`" >> $GITHUB_ENV

    - name: Create Output Folder
      run: |
        echo "Will copy binaries to ${{ env.DEST_FOLDER }}"
        mkdir -p "${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug"
    
    - name: Copy Binaries (Debug)
      run: |
        cp build/18.04/Debug/igplatform/igplatform ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/imaxanano/imaxanano ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/serverhub/imaxaserverhub ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/serverhub/imaxanode ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/yserver/yserver ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/ladyroulette/ladyroulette ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/ladyroulette/rouletterng ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/lady_editor/lady_editor ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/roulettesound/roulettesound ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/abbiati/abbiati-wheel-server ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/abbiati/croupier-table ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        cp build/18.04/Debug/patches/transfer-jackpots ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug/
        tar -czf debug.tar.gz -C ${{ secrets.OUTPUT_FOLDER }}/${{ env.DEST_FOLDER }}/Debug .
