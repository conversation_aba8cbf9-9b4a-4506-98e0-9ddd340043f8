/***************************************************************************
 *   Copyright (C) 2007 by <PERSON><PERSON>   *
 *   <EMAIL>   *
 ***************************************************************************/
#include "TRpcCommand.h"

TRpcCommand::TRpcCommand(const std::string& procedure) : ProcedureName(procedure) {}

void TRpcCommand::SetInputValue(const XmlRpc::XmlRpcValue& XmlRpcValue)
{
	InputValues = XmlRpcValue;
}

const XmlRpc::XmlRpcValue& TRpcCommand::GetResult() const
{
	return ResultValues;
}

const XmlRpc::XmlRpcValue& TRpcCommand::Input() const
{
	return InputValues;
}

XmlRpc::XmlRpcValue& TRpcCommand::Output()
{
	return ResultValues;
}

void TRpcCommand::Finish(ERpcCommandStatus res)
{
	Status = res;
	CVar.notify_all();
}

ERpcCommandStatus TRpcCommand::WaitForResult(int timeoutMs)
{
	SharedScopedLock lock(Status);
	if (CVar.wait_for(lock, std::chrono::milliseconds(timeoutMs), [this]() -> bool { return &Status != ERpcCommandStatus::UNSENT; }))
		return &Status;
	else
		return ERpcCommandStatus::TIMED_OUT;    // timeout
}
