#
# Copyright (c) 2019 <PERSON> (<EMAIL>)
# Copyright (c) 2021 <PERSON> (<EMAIL>)
#
# Distributed under the Boost Software License, Version 1.0. (See accompanying
# file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
#
# Official repository: https://github.com/cppalliance/json
#

project json/doc ;

import boostbook ;
import ../../../tools/docca/docca.jam ;


docca.reference reference.qbk
    :
        xsl/custom-overrides.xsl
        [ glob-tree-ex ../include/boost/json : *.hpp *.ipp : detail impl ]
    :
        <doxygen:param>PROJECT_NAME=JSON
        <doxygen:param>PROJECT_BRIEF="JSON Library"
        <doxygen:param>ALIASES="esafe=\"@par Exception Safety\""
        <doxygen:param>FILE_PATTERNS=
        <doxygen:param>EXAMPLE_PATTERNS=
        <doxygen:param>DISTRIBUTE_GROUP_DOC=YES
        <doxygen:param>MACRO_EXPANSION=YES
        <doxygen:param>EXPAND_ONLY_PREDEF=YES
        <doxygen:param>"PREDEFINED=\\
            BOOST_FORCEINLINE \\
            BOOST_JSON_CLASS_DECL \\
            BOOST_JSON_DECL \\
            BOOST_JSON_DOCS \\
            BOOST_JSON_PUBLIC \\
            BOOST_SYMBOL_VISIBLE \\
            \"BOOST_JSON_INLINE_VARIABLE(v, t)=constexpr t v;\" \\
            \"BOOST_JSON_NODISCARD=[[nodiscard]]\""
        <doxygen:param>ABBREVIATE_BRIEF=
        <doxygen:param>INLINE_INHERITED_MEMB=YES
        <doxygen:param>JAVADOC_AUTOBRIEF=YES
        <doxygen:param>AUTOLINK_SUPPORT=NO
        <doxygen:param>EXTRACT_ALL=YES
        <doxygen:param>EXTRACT_PRIVATE=YES
        <doxygen:param>EXTRACT_LOCAL_CLASSES=NO
        <doxygen:param>SHOW_INCLUDE_FILES=NO
        <doxygen:param>INLINE_INFO=NO
        <doxygen:param>SORT_MEMBER_DOCS=NO
        <doxygen:param>SORT_MEMBERS_CTORS_1ST=YES
        <doxygen:param>SHOW_USED_FILES=NO
        <doxygen:param>SHOW_FILES=NO
        <doxygen:param>SHOW_NAMESPACES=NO
        <doxygen:param>CLASS_DIAGRAMS=NO

        # <doxygen:param>ALLOW_UNICODE_NAMES=NO
        # <doxygen:param>GROUP_NESTED_COMPOUNDS=NO
        # <doxygen:param>HIDE_COMPOUND_REFERENCE=NO
        # <doxygen:param>WARN_AS_ERROR=NO
    ;


#-------------------------------------------------------------------------------
#
# Produce the Boost.Book XML from the QuickBook
#

install images
    :
        [ glob images/*.png ]
    :
        <location>html/json/images
    ;

explicit images ;

xml json_doc
    :
        qbk/main.qbk
    :
        <dependency>reference.qbk
        <dependency>images
    ;

explicit json_doc ;

#-------------------------------------------------------------------------------
#
# HTML documentation for $(BOOST_ROOT)/doc/html
#
#-------------------------------------------------------------------------------

boostbook json
    :
        json_doc
    :
        <xsl:param>boost.root=../../../..
        <xsl:param>chapter.autolabel=1
        <xsl:param>chunk.section.depth=8                # Depth to which sections should be chunked
        <xsl:param>chunk.first.sections=1               # Chunk the first top-level section?
        <xsl:param>toc.section.depth=8                  # How deep should recursive sections appear in the TOC?
        <xsl:param>toc.max.depth=8                      # How many levels should be created for each TOC?
        <xsl:param>generate.section.toc.level=8         # Control depth of TOC generation in sections
        <xsl:param>generate.toc="chapter toc,title section nop reference nop"
        <include>../../../tools/boostbook/dtd
    :
        <dependency>images
    ;

#-------------------------------------------------------------------------------
#
# These are used to inform the build system of the
# means to build the integrated and stand-alone docs.
#

alias boostdoc ;
explicit boostdoc ;

alias boostrelease : json ;
explicit boostrelease ;
