[/
    Copyright (c) 2022 <PERSON> (grisum<PERSON><EMAIL>)

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    Official repository: https://github.com/cppalliance/json
]

[/-----------------------------------------------------------------------------]


[section Allocation control]

As __value_from__ creates a __value__ object, users may want to control the way
memory is allocated for it. For this reason the function has an optional
__storage_ptr__ parameter, that is used to set the __memory_resource__ for the
result.

[note __value_to__ does not have a similar parameter,
as __value__ is not created.]

As the conversion result is set via an output parameter of type `value&`, the
intended __storage_ptr__ is correctly propagated. But users still should take
care to not create temporaries using the default __memory_resource__
by accident.

For example, consider this alternative implementation of `tag_invoke` for
`ip_address` from the section [link json.conversion.custom_conversions Custom
conversions].

```
void
tag_invoke( const value_from_tag&, value& jv, ip_address const& addr )
{
    jv = array{ b[0], b[1], b[2], b[3] };
}
```

This implementation explicitly creates an __array__ rather than relying on
assignment from an initializer list. But the array uses default
__memory_resource__, not the one used by `jv`.

To avoid creating such temporaries with an incorrect __memory_resource__,
using `value`'s member functions `emplace_array`, `emplace_object`, and
`emplace_string` can be helpful.

[endsect]
