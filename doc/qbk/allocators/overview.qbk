[/
    Copyright (c) 2019 <PERSON> (<EMAIL>)
    Copyright (c) 2020 <PERSON><PERSON><PERSON><PERSON> (sdkry<PERSON><EMAIL>)

    Distributed under the Boost Software License, Version 1.0. (See accompanying
    file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)

    Official repository: https://github.com/cppalliance/json
]

[/-----------------------------------------------------------------------------]

[section Allocators]

Here we discuss the various allocator models used in the
C++ standard, followed by an explanation of the model used in
this library and its benefits. Finally we discuss how the library
interoperates with existing code that uses polymorphic allocators.

[include background.qbk]
[include storage_ptr.qbk]
[include pmr.qbk]

[/-----------------------------------------------------------------------------]

[endsect]
