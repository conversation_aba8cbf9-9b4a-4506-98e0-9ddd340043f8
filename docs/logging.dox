/** \page reference.logging Logging Reference

WebSocket++ has the capability of logging events during the lifetime of the connections that it processes. Each endpoint has two independent logging interfaces that are used by all connections created by that endpoint. The first is an access interface that allows logging routine events in the life of a connection (such as connect/disconnect and receipt of messages). The other is an error interface that allows logging non-routine problems or errors. Each interface has a number of different named channels that can be toggled on and off independently.

Exactly how these logs are processed and where they are written to depends on which logging policy is in use. Several logging policies are included by default and you can write your own policy if you need something more specialized. Selecting a policy is done via the  \subpage reference.config "endpoint config".

Common functionality (all policies)
-----------------------------------

### Logging Channels

Each logging interface is divided into 32 named channels. Log messages are written to a specific interface on a specific channel. Which log messages are actually printed is determined by which channels are enabled or not. Channels can be enabled or disabled either at compile time or at runtime.

### Enabling and Disabling Channels

Channels disabled at compile time are removed from the code entirely (assuming correct compiler optimization settings) and are not available for runtime enabling or disabling. To disable channels at compile time, use the `alog_level` and `elog_level` values within your \subpage reference.config "endpoint config". Channels not disabled at compile time can be enabled or disabled at runtime using the `websocketpp::endpoint::set_access_channels()`, `websocketpp::endpoint::clear_access_channels()`, `websocketpp::endpoint::set_error_channels()`, and `websocketpp::endpoint::clear_error_channels()` methods.

The set and clear functions act only on the channels specified. `set_access_channels(log::alevel::connect)` will enable logging of new connections. Following this with `set_access_channels(log::alevel::disconnect)` will enable logging of disconnections in addition to connections. Use `clear*` functions to disable a specific channel. Channels may be combined using bitwise operations to create aggregate packages of channels that may be set or cleared at once. Default packages include `websocketpp::log::alevel::all`, `websocketpp::log::elevel::all`, `websocketpp::log::alevel::none`, `websocketpp::log::elevel::none`. These represent all possible access/error channels and no access/error channels respectively. For convenience, setting none is aliased to clearing all.

### Examples

__Disable all__

`clear_access_channels(log::alevel::all)`

__Disable all (alternative method)__

`set_access_channels(log::alevel::none)`

__Multiple channels at once__

`log::alevel::message_payload | log::alevel::message_payload`

__All except one__

`log::alevel::all ^ log::alevel::message_payload`

__Default settings__

By default, only debug/development logging is disabled.

### Access to underlying loggers

Logging interfaces may be directly accessed via their associated endpoint or connection using get_alog() and get_elog(). This allows access to methods specific to the chosen logging policy. 

Basic Logging (Default Policy)
------------------------------

The basic logging policy (`websocketpp::log::basic`) writes logs to a std::ostream. By default, access logs are written to stdout and error logs are written to stderr. Each logging interface may be optionally redirected to an arbitrary C++ stream (including file streams) using the `websocketpp::log::basic::set_ostream()` method.

Syslog Logging
--------------

The syslog logging policy (`websocketpp::log::syslog`) logs to POSIX syslog. It is included in the header `<websocketpp/logger/syslog.hpp>`. It requires a system with `<syslog.h>`.

Stub Logging
------------

The stub logging policy (`websocketpp::log::stub`) implements the logging policy interface but ignores all input and provides no output. It can be used to stub out the logging system in tests or to completely disable and remove nearly all logging related code. 

The stub logger also provides documentation for the minimal required interface to build a custom logging policy.

Log level reference
-------------------

### Error Logging Levels

Each of these channels is in the namespace `websocketpp::log::elevel`

| Level   | Description                                                                                                                |
| ------- | -------------------------------------------------------------------------------------------------------------------------- |
| none    | Special aggregate value representing "no levels"                                                                           |
| devel   | Low level debugging information (warning: very chatty). Requires debug or custom config.                                   |
| library | Information about unusual system states or other minor internal library problems, less chatty than devel.                  |
| info    | Information about minor configuration problems or additional information about other warnings.                             |
| warn    | Information about important problems not severe enough to terminate connections.                                           |
| rerror  | Recoverable error. Recovery may mean cleanly closing the connection with an appropriate error code to the remote endpoint. |
| fatal   | Unrecoverable error. This error will trigger immediate unclean termination of the connection or endpoint.                  |
| all     | Special aggregate value representing "all levels"                                                                          |

### Access Logging Levels

Each of these channels is in the namespace `websocketpp::log::alevel`

| Level           | Description                                                                                        |
| --------------- | -------------------------------------------------------------------------------------------------- |
| none            | Special aggregate value representing "no levels"                                                   |
| connect         | One line for each new connection that includes a host of information including: the remote address, websocket version, requested resource, http code, remote user agent |
| disconnect      | One line for each connection that is closed. Includes closing codes and reasons                    |
| control         | One line per control message                                                                       |
| frame_header    | One line per frame, includes the full frame header                                                 |
| frame_payload   | One line per frame, includes the full message payload (warning: lots of output for large messages) |
| message_header  | Reserved                                                                                           |
| message_payload | Reserved                                                                                           |
| endpoint        | Reserved                                                                                           |
| debug_handshake | Extra information about opening handshakes                                                         |
| debug_close     | Extra information about closing handshakes                                                         |
| devel           | Development messages (warning: very chatty). Requires debug or custom config.                      |
| app             | Special channel for application specific logs. Not used by the library.                            |
| all             | Special aggregate value representing "all levels"                                                  |

*/
