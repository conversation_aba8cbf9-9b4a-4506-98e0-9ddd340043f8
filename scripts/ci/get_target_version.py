#!/usr/bin/python3

import argparse
import json
import os
import sys

SCRIPTS_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ROOT_DIR = os.path.dirname(SCRIPTS_DIR)
sys.path.append(os.path.dirname(SCRIPTS_DIR))

from scripts.platform_constants import TARGET_CONFIGURATION_FILENAME

parser = argparse.ArgumentParser(
    description='Get the version of a specific target from its version.json file. Uses targets.json as a registar of all targets.')
parser.add_argument('target', help='The target name (as it is used in cmake)')
parser.add_argument('--only-filepath', help='If given, only returns the filepath of the version.json file.',
                    action='store_true')

args = parser.parse_args()

# Read the targets.json file
with open(TARGET_CONFIGURATION_FILENAME, "r") as file:
    targets = json.load(file)

#2nd temp fix
target_version_file = "version.json"
# if targets and args.target in targets:
#     target_version_file = str(targets.get(args.target).get("versionFile"))

if not target_version_file:
    print("Could not find the target %s in the targets.json file." % args.target)
    exit(1)

if args.only_filepath:
    print(target_version_file)
    exit(0)

# Read the version.json file
with open(target_version_file, "r") as f2:
    version = json.load(f2)
    print("%d.%d.%d" % (version.get("majorVersion"), version.get("minorVersion"), version.get("patchVersion")))

exit(0)
