#!/usr/bin/env python3
import logging
import os
import sys

__SCRIPTS_DIR = os.path.dirname(os.path.abspath(__file__))
__ROOT_DIR = os.path.dirname(__SCRIPTS_DIR)
sys.path.append(os.path.dirname(__SCRIPTS_DIR))

from scripts.platform_constants import TARGET_BUILD_TYPE_DEFAULT, DEFAULT_BUILD_IMAGE_TAG


def prepare_build_directory(docker_image, docker_image_check_file, cache_file):
    if os.path.isfile(docker_image_check_file):
        with open(docker_image_check_file, "r") as file:
            docker_image_old = file.read()
    else:
        docker_image_old = None
    if docker_image != docker_image_old:
        print("Docker image checksum changed, deleting build cache ...", flush=True)
        if os.path.exists(cache_file):
            os.remove(cache_file)
        docker_image_check_file_dir = os.path.dirname(docker_image_check_file)
        if not os.path.exists(docker_image_check_file_dir):
            os.makedirs(docker_image_check_file_dir)
        with open(docker_image_check_file, "w") as file:
            file.write(docker_image)


def __pack_libraries(ubuntu_version: str, version: str, upload: bool = False, replace: bool = False):
    import json
    import shutil
    import tempfile
    from tronius_utils_lib import run_command
    from artifacts.artifacts_constants import COMPRESSED_FILE_EXTENSION
    from artifacts.artifacts_lib import upload_artifact, save_artifact_to_versions
    from scripts.platform_constants import LIBRARIES_PACKS_FILENAME, BUILD_OUTPUT_DIRECTORY

    logging.info("Packing libraries ...")
    with open(LIBRARIES_PACKS_FILENAME, "r") as file:
        packs = json.load(file)

    for pack in packs:
        logging.info(f"Packing libraries for pack '{pack}' ...")
        with tempfile.TemporaryDirectory() as pack_dir:
            libraries = packs[pack]
            for library in libraries:
                logging.debug(f"Copying library '{library}' ...")
                shutil.copy(str(os.path.join(BUILD_OUTPUT_DIRECTORY, library)), pack_dir)
            pack_file = os.path.join(BUILD_OUTPUT_DIRECTORY, f"{pack}.{COMPRESSED_FILE_EXTENSION}")
            if os.path.isfile(pack_file):
                logging.debug(f"Removing old pack file: {pack_file}")
                os.remove(pack_file)
            logging.debug(f"Packing libraries to '{pack_file}' ...")
            run_command(f"tar -czf '{pack_file}' -C '{pack_dir}' .")
            save_artifact_to_versions(pack, version, __ROOT_DIR)
            if upload:
                upload_artifact(pack_file,
                                version,
                                pack,
                                additional_path=os.path.join("x86", ubuntu_version),
                                replace=replace,
                                manifest_path=__ROOT_DIR)


def build_targets(targets: list[str],
                  ubuntu_versions: list[str],
                  build_image_tag: str = DEFAULT_BUILD_IMAGE_TAG,
                  build_target_image: bool = False,
                  build_type: str = TARGET_BUILD_TYPE_DEFAULT,
                  source_dir: str = __ROOT_DIR,
                  push_to_registry: bool = False,
                  add_latest: bool = False,
                  force_build: bool = False,
                  upload: bool = False,
                  replace: bool = False,
                  tronius_scripts: str = ""):
    import os
    import subprocess
    from scripts.platform_lib import check_tronius_scripts_dir, get_targets_with_artifacts, log_command, build_target_docker_image, upload_artifacts
    from scripts.platform_constants import DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME, AnsiCodes, EVOLUTION_TARGET_SERVER, TARGET_JACKPOT_SERVER, \
        BUILD_OUTPUT_DIRECTORY, EVOLUTION_DOCKER_IMAGE_NAME, BUILD_OUTPUT_DIRECTORY_NAME, TARGET_DEVICE_SOFTWARE, JACKPOT_BUILT_FILE

    tronius_scripts = check_tronius_scripts_dir(tronius_scripts)
    from git.git_lib import should_build, save_commit_id
    from tronius_utils_lib import run_command
    from artifacts.artifacts_constants import ARTIFACTS_VERSIONS_FILENAME

    if not should_build(__ROOT_DIR, force_build):
        logging.warning("No changes detected, skipping build process.")
        return

    if not os.path.isdir(source_dir):
        raise FileNotFoundError("Source directory '%s' does not exist." % source_dir)
    if not targets:
        raise ValueError("No targets specified to build.")

    artifacts_versions_file = os.path.join(source_dir, ARTIFACTS_VERSIONS_FILENAME)
    if os.path.isfile(artifacts_versions_file):
        logging.debug(f"Removing old artifacts versions file: {artifacts_versions_file}")
        os.remove(artifacts_versions_file)
    if os.path.isfile(JACKPOT_BUILT_FILE):
        logging.debug(f"Removing old jackpot built file: '{JACKPOT_BUILT_FILE}'")
        os.remove(JACKPOT_BUILT_FILE)

    source_dir = os.path.abspath(source_dir)
    print("Source directory: %s" % source_dir, flush=True)
    from versioning.versioning_lib import prepare_manifest
    version = prepare_manifest(__ROOT_DIR)
    docker_image_check_file = os.path.join(BUILD_OUTPUT_DIRECTORY, "docker_image_check")
    cache_file = os.path.join(BUILD_OUTPUT_DIRECTORY, "CMakeCache.txt")
    targets_with_artifacts = get_targets_with_artifacts(targets)
    for ubuntu_version in ubuntu_versions:
        docker_image = "%s/%s/%s:%s" % (DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME, ubuntu_version, build_image_tag)
        prepare_build_directory(docker_image, docker_image_check_file, cache_file)
        for target in targets_with_artifacts:
            artifacts = targets_with_artifacts[target]
            print(AnsiCodes.HEADER + "*" * 5 + " Building target '%s' (artifacts '%s') for '%s' " % (
                target, " ".join(artifacts), ubuntu_version) + "*" * 30 + AnsiCodes.END, flush=True)
            print("%sBuilding version '%s'%s" % (AnsiCodes.DEBUG, version, AnsiCodes.END), flush=True)
            container_name = f"platform-build-{ubuntu_version}"
            run_command(f"docker rm -f {container_name} 2>/dev/null")
            build_command = "cd app/ && cmake -DCMAKE_BUILD_TYPE=%s -DVERSION_STRING=%s . -B%s && cmake --build build" % (
                build_type, version, BUILD_OUTPUT_DIRECTORY_NAME)
            for artifact in artifacts:
                build_command += f" --target {artifact}"
            build_command += " -- -j16"
            command = ["docker", "run", "--rm", "--name", container_name,
                       "-v",
                       "%s:/app" % source_dir,
                       docker_image,
                       "/bin/bash", "-c", "%s" % build_command]
            log_command(command)
            if subprocess.call(command) == 0:
                print(AnsiCodes.OK + "Build succeeded!" + AnsiCodes.END, flush=True)
                build_dir = BUILD_OUTPUT_DIRECTORY
                if upload:
                    upload_artifacts(target, version, "x86", ubuntu_version, build_dir, replace=replace)
                if build_target_image:
                    if target in [TARGET_JACKPOT_SERVER, TARGET_DEVICE_SOFTWARE]:
                        from scripts.jackpot.build_docker_image import build
                        build(build_dir, ubuntu_version=ubuntu_version, push_to_registry=push_to_registry, add_latest=add_latest, replace=replace)
                        with open(JACKPOT_BUILT_FILE, "w") as file:
                            file.write("JACKPOT_BUILD=true\n")
                        logging.debug(f"Jackpot built file created: '{JACKPOT_BUILT_FILE}'")
                    elif EVOLUTION_TARGET_SERVER == target:
                        build_target_docker_image(EVOLUTION_TARGET_SERVER, "evolution", os.path.join(__SCRIPTS_DIR, "evolution", "Dockerfile"),
                                                  EVOLUTION_DOCKER_IMAGE_NAME, version, build_dir, ubuntu_version, tronius_scripts, build_image_tag,
                                                  add_latest, push_to_registry)
            else:
                print(AnsiCodes.ERROR + "Build failed!" + AnsiCodes.END, flush=True)
                raise RuntimeError("Failed to build target(s)")

        __pack_libraries(ubuntu_version, version, upload, replace)

    from versioning.versioning_lib import prepare_prerelease_versions
    prepare_prerelease_versions(__ROOT_DIR)

    save_commit_id(__ROOT_DIR, True)

    description = f"{version} [{','.join(targets)}]"
    description_long = f"{build_type} ({build_image_tag}): {','.join(ubuntu_versions)}"
    if force_build:
        description_long += " (force build)"
    if upload:
        description_long += " (upload)"
    from tronius.jenkins.jenkins_lib import prepare_basic_build_info_file
    prepare_basic_build_info_file(version, description, description_long)

    from artifacts.artifacts_lib import print_artifact_versions
    print_artifact_versions(__ROOT_DIR)


def main():
    import argparse
    from scripts.platform_constants import UBUNTU_VERSION_DEFAULT, TARGET_BUILD_TYPES, TARGET_BUILD_TYPE_DEFAULT
    from scripts.platform_lib import check_tronius_scripts_dir

    parser = argparse.ArgumentParser(description="Build platform targets using docker build image.",
                                     formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("targets", type=str, nargs="+",
                        help="Target(s) to build"
                             "\nFor every targets, all corresponding artifacts will be built, for instance "
                             "('imaxanode' and 'imaxaserverhub' for 'serverhub')"
                             "\nNOTE: Targets can be separated by space, comma or semicolon.")
    parser.add_argument("--ubuntu_version", type=str, default=UBUNTU_VERSION_DEFAULT,
                        help="Ubuntu version(s) used as base docker image. "
                             "If multiple versions are specified, targets will be build on multiple versions of Ubuntu."
                             "\nNOTE: Versions can be separated by space, comma or semicolon.")
    parser.add_argument("--build_image_tag", type=str, default=DEFAULT_BUILD_IMAGE_TAG, help="Docker image tag used for build.")
    parser.add_argument("--build_type", type=str, default=TARGET_BUILD_TYPE_DEFAULT, choices=TARGET_BUILD_TYPES, help="Target build type.")
    parser.add_argument("--source_dir", type=str, default=__ROOT_DIR, help="Source directory")
    parser.add_argument("--build_target_image", default=False, action="store_true", help="Build target docker image")
    parser.add_argument("--force_build", default=False, action="store_true",
                        help="Force build even if no changes detected in source directory.")
    parser.add_argument("--push_to_registry", default=False, action="store_true", help="Push image to our registry")
    parser.add_argument("--add_latest", default=False, action="store_true", help="Add latest tag to the image")
    parser.add_argument("--upload", default=False, action="store_true", help="Upload built targets' artifacts to S3.")
    parser.add_argument("--replace", default=False, action="store_true", help="Replace already existing version on all places.")
    parser.add_argument("--tronius_scripts", type=str,
                        help="Location of tronius scripts."
                             "If not defined, environmental variable 'TRONIUS_SCRIPTS', '/opt/tronius/scripts' or '~/tronius/scripts' will be used")

    args = parser.parse_args()
    tronius_scripts = check_tronius_scripts_dir(args.tronius_scripts)
    from tronius_utils_lib import check_multiple_parameter
    ubuntu_versions = check_multiple_parameter(args.ubuntu_version)
    build_image_tag = args.build_image_tag
    build_type = args.build_type
    source_dir = args.source_dir
    build_target_image = args.build_target_image
    force_build = args.force_build
    push_to_registry = args.push_to_registry
    add_latest = args.add_latest
    upload = args.upload
    replace = args.replace
    targets = check_multiple_parameter(args.targets)

    build_targets(targets,
                  ubuntu_versions,
                  build_image_tag,
                  build_target_image,
                  build_type,
                  source_dir,
                  push_to_registry,
                  add_latest,
                  force_build=force_build,
                  upload=upload,
                  replace=replace,
                  tronius_scripts=tronius_scripts)


if __name__ == "__main__":
    main()
