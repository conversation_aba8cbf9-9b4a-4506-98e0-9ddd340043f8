#!/usr/bin/env python3
import os
import sys

__MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
__ROOT_DIR = os.path.dirname(__MODULE_DIR)
sys.path.append(__ROOT_DIR)


def main():
    import argparse
    from scripts.platform_lib import check_tronius_scripts_dir, get_targets_with_artifacts
    from scripts.platform_constants import TARGET_BUILD_TYPES, TARGET_BUILD_TYPE_DEFAULT, UBUNTU_VERSIONS, UBUNTU_VERSION_DEFAULT, \
        TARGET_ARCHITECTURES, TARGET_ARCHITECTURES_DEFAULT

    parser = argparse.ArgumentParser(description="Update platform related jenkins properties",
                                     formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("--tronius_scripts", type=str,
                        help="Location of tronius scripts."
                             "If not defined, environmental variable 'TRONIUS_SCRIPTS', '/opt/tronius/scripts' or '~/tronius/scripts' will be used")
    args = parser.parse_args()

    tronius_scripts = check_tronius_scripts_dir(args.tronius_scripts)
    from tronius_utils_lib import run_command

    command = [
        sys.executable,
        os.path.join(tronius_scripts, "tronius/jenkins", "save-properties.py")]

    targets_with_artifacts = get_targets_with_artifacts()

    # platform.targets
    values = ",".join(targets_with_artifacts.keys())
    descriptions = []
    for target, artifacts in targets_with_artifacts.items():
        artifacts_text = ";".join(artifacts)
        if target == artifacts_text:
            descriptions.append(target)
        else:
            descriptions.append(f"{target} ({artifacts_text})")
    descriptions = ",".join(descriptions)
    run_command(command + ["platform.targets", values, "--descriptions", descriptions])

    # platform.buildTypes
    values = ",".join(TARGET_BUILD_TYPES)
    default_value = TARGET_BUILD_TYPE_DEFAULT
    run_command(command + ["platform.buildTypes", values, "--default_value", default_value])

    # ubuntu.versions
    values = ",".join(UBUNTU_VERSIONS)
    default_value = UBUNTU_VERSION_DEFAULT
    run_command(command + ["ubuntu.versions", values, "--default_value", default_value])

    # platform.architecture
    values = ",".join(TARGET_ARCHITECTURES)
    default_value = ",".join(TARGET_ARCHITECTURES_DEFAULT)
    run_command(command + ["platform.architecture", values, "--default_value", default_value])


if __name__ == "__main__":
    main()
