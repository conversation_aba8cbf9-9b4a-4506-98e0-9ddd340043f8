import fdb
import os
import json

con = fdb.connect(host='127.0.0.1', port=3050, database='ROULETTE_DATA.GDB', user='sysdba', password='masteral',
                  charset='UTF8')

cursor: fdb.Cursor = con.cursor()
cursor.execute("SELECT ATTR_STRING_VALUE FROM CONFIGURATION WHERE ATTR_NAME='BILL_CURRENCY'")

currency = "NONE"
for row in cursor.iter():
    currency = row[0]
    break

jplevelsdir = "/var/IGP/jackpot/levels"

if os.path.exists(jplevelsdir):
    # iterate over files in
    # that directory
    for filename in os.listdir(jplevelsdir):
        if not filename.startswith("LEVEL"):
            continue

        fpath = os.path.join(jplevelsdir, filename)
        # checking if it is a file
        if not os.path.isfile(fpath):
            continue

        lvlId = int(filename[5:6])

        with open(fpath, "r+") as file:
            try:
                lvlConfOld = json.load(file)
            except:
                continue
            lvlConfNew = {
                "id": lvlId - 1,
                "type": "Mystery" if lvlConfOld["from"] == lvlConfOld["to"] else "HotSeat",
                "potType": "Progressive",
                "currency": currency,
                "increment": lvlConfOld["increment"],
                "minPot": lvlConfOld["minPot"],
                "maxPot": lvlConfOld["maxPot"],
                "from": lvlConfOld["from"],
                "to": lvlConfOld["to"],
                "minBet": lvlConfOld["minBet"],
                "name": lvlConfOld["name"],
                "autoResetEnabled": True
            }
            file.seek(0)
            file.truncate()
            json.dump(lvlConfNew, file, indent=4)

print("Completed porting of jackpot level configurations (7.2.5).")
