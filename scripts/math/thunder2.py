#!/usr/bin/python3
from __future__ import annotations
import argparse
import json
import random
import math
from abc import ABC, abstractmethod
import numpy as np
from sys import exit
from matplotlib import pyplot as plt
from scipy.optimize import OptimizeResult, minimize, LinearConstraint, NonlinearConstraint
from collections import Counter

parser = argparse.ArgumentParser(description='RTP calculator and simulator for thunder roulette V2.',
                                 formatter_class=argparse.ArgumentDefaultsHelpFormatter,
                                 epilog='Developed by Tronius d.o.o.')
parser.add_argument('--thunder', default='2.5',
                    help='The number of lightning numbers to choose (the mean of the gauss curve for the distribution)')
parser.add_argument('--sigma', default='3',
                    help='The standard deviation of the number of thunder numbers to choose')
parser.add_argument('--thunder-nums', default="50,100,200,300,400,500",
                    help='The minimum multiplier to use on lighting numbers')
parser.add_argument('--thunder-shape', default="110:43:0.0012:1.3",
                    help='The distribution of thunder multipliers, given by a blend between a shape function defined by a gauss curve and a constant value [mean]:[sigma]:[constValue]:[gaussWeight]')
parser.add_argument('--board', default="30", help='The multiplier for all other fields')
parser.add_argument('--rtp-distribution', default="0:10",
                    help='A list of column separated pairs of [offsetFromAvgRTP]:[sigma] to specify the distribution of RTP around average (eg. -10:5,10:5)')
parser.add_argument('--rtp', default="97.3", help='The target RTP (average)')
parser.add_argument('--rtp-bias', default="0.0",
                    help='To what extent should the math try to satisfy the RTP distribution? If 0, the math will try to satisfy the desired distribution of thunder multipliers. If 1, it will only try to satisfy the RTP distribution. Any value in between is a weighed mix between the two.')
parser.add_argument('--rtp-limits', default="80:120",
                    help='Column separated pair of numbers defining upper and lower bounds of RTP. Use 0 for unlimited [eg. 80:0].')
parser.add_argument('--numbers', default="37", help='How many numbers are there on the wheel')
parser.add_argument('--thunder-count-min', default="1", help='Minimum amount of thunder numbers to spawn')
parser.add_argument('--thunder-count-max', default="5", help='Maximum amount of thunder numbers to spawn')
parser.add_argument('--must-include-multipliers', default="",
                    help='Comma separated list of multipliers, from which at least one must be in every round.')
# from here on, only simulation related parameters
parser.add_argument('--simulations', default="1", help='Number of simulations to do')
parser.add_argument('--output-simulations', default="", help='Output file for simulation results')
parser.add_argument('--verbosity', default="0", help='How much to print of what is happening')
parser.add_argument('--log-distributions', action="store_true", help='Prints the rtp distribution, multipliers distribution and percentage of rounds with multiplier')
parser.add_argument('--plot-multipliers', action="store_true",
                    help='Shows a plot of multiplier weights before running simulation')
parser.add_argument('--plot-thunder-count', action="store_true",
                    help='Shows a plot with weights of amounts of thunder numbers before running simulation')
parser.add_argument('--plot-rtp', action="store_true",
                    help='Shows a plot of RTP distribution before running simulation')
parser.add_argument('--output-config', default="",
                    help='If true, generate a configuration file to be used for the thunder2 RNG and output it to the given file.')
# bonus prize related parameters
parser.add_argument('--enable-bonus', action="store_true", help='Enable bonus prize numbers')
parser.add_argument('--bonus-nums', default="2,5,10,20", help='The bonus multiplier numbers')
parser.add_argument('--bonus-ratios', default="8:4:2:1",
                    help='The ratio of the bonus multiplier numbers (eg. 8:4:2:1) to which we strive with the final bonus probabilities. Must be the same length as bonus-nums')
parser.add_argument('--bonus-rtp', default="10", help='The bonus RTP')
parser.add_argument('--fee', default="30", help='The fee to be taken for Fireball+ (in percent)')

# jackpot related parameters
parser.add_argument('--enable-jackpot', action="store_true", help='Enable jackpot')
parser.add_argument('--jackpot-levels', default="2", help='The number of jackpot levels')
parser.add_argument('--jackpot-level-names', default="Gold:Silver", help='The names of the jackpot levels')
parser.add_argument('--jackpot-rtps', default="2.0:1.0", help='The RTPs of the jackpot levels (in %% for each jackpot level, e.g. 2.0:1.0)')
parser.add_argument('--jackpot-win-probs', default="0.02:0.04", help='The win probabilities of the jackpot levels in a game round (in %% for each jackpot level, e.g. 0.01:0.02)')
parser.add_argument('--jackpot-avg-bet-per-player-round', default="15", help='The average bet per player in single game round (in currency units, e.g. 15)')
parser.add_argument('--jackpot-avg-num-players', default="8", help='The avg number of players in a single game round (e.g. 8)')
parser.add_argument('--jackpot-avg-bets-per-player-round', default="15:15:15:15:15:15:15:15", help='The avg bets for each player (in --jackpot-avg-num-players), in currency units, split by :, e.g. 15:15:15:15:15:15:15:15')
parser.add_argument('--jackpot-combined-bonus-multipliers', default="20:20", help='The combined bonus multipliers for the jackpot levels (which bonus multiplier is needed to win the jackpot)')
parser.add_argument('--jackpot-min-pots', default="300:100", help='The minimum pots for the jackpot levels (in currency units, e.g. 300:100)')
parser.add_argument('--jackpot-backpot-ratio', default="0.3", help='The ratio of rtp that goes to the backpot when backpot is positive, or to pot when backpot is negative')

args = parser.parse_args()

numSims = int(args.simulations)
verbosity = max(int(args.verbosity), 2 if numSims == 1 else 0)
numbers = int(args.numbers)
rtpPerStep = 1 / (numbers * numbers)
thunderNumbersMin = int(args.thunder_count_min)
thunderNumbersMax = int(args.thunder_count_max)
boardMultiplier = int(args.board)
mandatoryMultipliers = set(map(int, filter(None, args.must_include_multipliers.split(','))))
thunderNums = list(set(map(int, args.thunder_nums.split(','))))
thunderNums.sort()
thunderNumsShape = list(map(float, args.thunder_shape.split(':')))
avgRtpPercent = float(args.rtp)
rtpBias = max(min(float(args.rtp_bias), 1.0), 0.0)
rtp_limits_orig = list(map(float, args.rtp_limits.split(':')))
rtp_limits = rtp_limits_orig.copy()
if rtp_limits[1] == 0.0:
    rtp_limits[1] = math.inf
theoretical_rtp_limits = [math.inf, 0.0]
for num_multipliers in range(thunderNumbersMin, thunderNumbersMax + 1):
    theoretical_min_rtp = (num_multipliers * thunderNums[0] + (numbers - num_multipliers) * boardMultiplier) * rtpPerStep
    theoretical_rtp_limits[0] = min(theoretical_rtp_limits[0], theoretical_min_rtp)
    theoretical_max_rtp = (num_multipliers * thunderNums[-1] + (numbers - num_multipliers) * boardMultiplier) * rtpPerStep
    theoretical_rtp_limits[1] = max(theoretical_rtp_limits[1], theoretical_max_rtp)

theoretical_rtp_limits = [lim * 100 for lim in theoretical_rtp_limits]
if avgRtpPercent < theoretical_rtp_limits[0] or avgRtpPercent > theoretical_rtp_limits[1]:
    print(
        "ERROR: target rtp of %.02f%% is outside the theoretical rtp range of this setup (%.02f%% to %.02f%%)" % (
            avgRtpPercent, theoretical_rtp_limits[0], theoretical_rtp_limits[1]))
    exit(1)

effective_rtp_range = [max(theoretical_rtp_limits[0], rtp_limits[0]), min(rtp_limits[1], theoretical_rtp_limits[1])]

print("====== Thunder setup ======")
print("> Thunder count: %d - %d, normal distribution around %.02f with sigma = %.02f" %
      (thunderNumbersMin, thunderNumbersMax, float(args.thunder), float(args.sigma)))
print("> Thunder multipliers: %s" % str(thunderNums))
print("> Board multiplier: %d" % boardMultiplier)
print("> RTP: %.02f%%" % avgRtpPercent)
print("> RTP theoretical range: %.02f%% to %.02f%% " % (theoretical_rtp_limits[0], theoretical_rtp_limits[1]))
print("> RTP limits: %.02f%% to %.02f%% " % (rtp_limits[0], rtp_limits[1]))
print("> RTP effective range: %.02f%% to %.02f%% " % (effective_rtp_range[0], effective_rtp_range[1]))
print("> Multipliers that must appear in every round (one of these): %s" % str(list(mandatoryMultipliers)))

if args.enable_bonus:
    bonusNums = list(set(map(int, args.bonus_nums.split(','))))
    bonusNums.sort()
    bonusRatios = list(map(float, args.bonus_ratios.split(':')))
    if len(bonusRatios) != len(bonusNums):
        print("The number of baseline ratios must match the number of bonus numbers.")
        exit(1)
    bonusRTP = float(args.bonus_rtp)
    fireballFee = float(args.fee)

    theoretical_rtp_limits_with_bonus = [theoretical_rtp_limits[0] + 100 * bonusNums[0] / numbers, theoretical_rtp_limits[1] + 100 * bonusNums[-1] / numbers]
    if avgRtpPercent + bonusRTP < theoretical_rtp_limits_with_bonus[0] or avgRtpPercent + bonusRTP > theoretical_rtp_limits_with_bonus[1]:
        print(
            "ERROR: target rtp of %.02f%% is outside the theoretical rtp range of this setup (%.02f%% to %.02f%%)" % (
                avgRtpPercent + bonusRTP, theoretical_rtp_limits_with_bonus[0], theoretical_rtp_limits_with_bonus[1]))
        exit(1)
    effective_rtp_range_with_bonus = [
        effective_rtp_range[0] + 100 * bonusNums[0] / numbers,
        effective_rtp_range[1] + 100 * bonusNums[-1] / numbers
    ]

    print("====== Bonus setup ======")
    print("> Bonus multipliers: %s" % str(bonusNums))
    print("> Bonus ratios: %s" % str(bonusRatios))
    print("> Bonus multiplier RTP: %.02f%%" % bonusRTP)
    print("> RTP theoretical range: %.02f%% to %.02f%% " % (theoretical_rtp_limits_with_bonus[0], theoretical_rtp_limits_with_bonus[1]))
    print("> RTP effective range: %.02f%% to %.02f%% " % (effective_rtp_range_with_bonus[0], effective_rtp_range_with_bonus[1]))
    print("> Fireball+ fee: %.02f%%" % fireballFee)
else:
    effective_rtp_range_with_bonus = effective_rtp_range

if args.enable_jackpot:
    jackpotLevels = int(args.jackpot_levels)
    jackpotLevelNames = args.jackpot_level_names.split(':')
    jackpotRTPs = list(map(float, args.jackpot_rtps.split(':')))
    jackpotWinProbs = list(map(float, args.jackpot_win_probs.split(':')))
    jackpotAvgBetPerPlayerRound = int(args.jackpot_avg_bet_per_player_round)
    jackpotAvgNumPlayers = int(args.jackpot_avg_num_players)
    jackpotAvgBetsPerPlayerRound = list(map(int, args.jackpot_avg_bets_per_player_round.split(':')))
    jackpotCombinedBonusMultipliers = list(map(int, args.jackpot_combined_bonus_multipliers.split(':')))
    jackpotMinPots = list(map(int, args.jackpot_min_pots.split(':')))
    jackpotBackpotRatio = float(args.jackpot_backpot_ratio)
    bonusNumberJackpotProb = dict()
    effective_rtp_range_with_bonus = [
        effective_rtp_range_with_bonus[0] + sum(jackpotRTPs),
        effective_rtp_range_with_bonus[1] + sum(jackpotRTPs)
    ]

    if len(jackpotLevelNames) != jackpotLevels or len(jackpotRTPs) != jackpotLevels or len(jackpotWinProbs) != jackpotLevels:
        print("The number of jackpot levels must match the number of names, RTPs and win probabilities.")
        exit(1)

    if len(jackpotAvgBetsPerPlayerRound) != jackpotAvgNumPlayers:
        print("The number of jackpot average bets per player rounds must match the number of average number of players.")
        exit(1)

    print("====== Jackpot setup ======")
    print("> Number of jackpot levels: %d" % jackpotLevels)
    print("> Average bet per player per round: %.02f" % jackpotAvgBetPerPlayerRound)
    print("> Average number of players per round: %d" % jackpotAvgNumPlayers)
    print("> Average bets per player per round: %s" % str(jackpotAvgBetsPerPlayerRound))
    print("> Backpot ratio: %.02f%%" % (100*jackpotBackpotRatio))
    print("> Jackpot levels:")
    for idx in range(jackpotLevels):
        print("\t Level name: %s" % jackpotLevelNames[idx])
        print("\t\t RTP: %.02f%%" % jackpotRTPs[idx])
        print("\t\t Win probability: %f%% (avg every %d player rounds, avg every %d rounds)" % (jackpotWinProbs[idx], int(100 / jackpotWinProbs[idx]), int(100 / jackpotWinProbs[idx] / jackpotAvgNumPlayers)))
        avgWin = jackpotAvgBetPerPlayerRound * jackpotRTPs[idx] / jackpotWinProbs[idx]
        print("\t\t Avg jackpot win: %.02f" % avgWin)
        print("\t\t Min pot: %d" % jackpotMinPots[idx])
        if avgWin < jackpotMinPots[idx] * 3:
            print("The min pot for jackpot level %d is too high for the average win. It should be at most avgWin/3" % idx)
            exit(1)
        if not args.enable_bonus and (jackpotCombinedBonusMultipliers[idx] not in bonusNums):
            print("The jackpot cannot be combined with the bonus multiplier %d, because it does not exist" % jackpotCombinedBonusMultipliers[idx])
            exit(1)
        print("\t\t Combined bonus multiplier for win: %sx" % jackpotCombinedBonusMultipliers[idx])
        bonusNumberJackpotProb[jackpotCombinedBonusMultipliers[idx]] = bonusNumberJackpotProb.get(jackpotCombinedBonusMultipliers[idx], 0) + jackpotWinProbs[idx]

    print("> Sum of JP RTPs: %.02f%%" % sum(jackpotRTPs))

totalRtp = avgRtpPercent
if args.enable_bonus:
    totalRtp += bonusRTP
if args.enable_jackpot:
    totalRtp += sum(jackpotRTPs)
print("===============================")
print("====== Total RTP: %.02f%% ======" % totalRtp)
print("===============================")

def normalize_weights(weights: list[float]) -> list[float]:
    weight_sum = np.sum(weights)
    return [w / weight_sum for w in weights]


class ShapeFunction(ABC):

    @abstractmethod
    def evaluate(self, x: float) -> float:
        pass

    @abstractmethod
    def mean(self) -> float:
        pass

    def create_weights(self, x_values: list[float], constrain_mean: bool = True, target_mean: float | None = None,
                       rescale_to_function: bool = False, min_weight: float | None = None) -> \
            list[float]:
        initial_weights = []
        total_weight = 0
        for val in x_values:
            weight = self.evaluate(val)
            total_weight += weight
            initial_weights.append(weight)

        _target_mean = self.mean() if target_mean is None else target_mean
        norm_initial_weights = normalize_weights(initial_weights)

        def objective(weights: list[float]):
            return np.sum(np.square(np.subtract(weights, norm_initial_weights)))

        # Constraint: The weighted mean should be equal to the target mean
        normalization_constraint = LinearConstraint(np.ones_like(norm_initial_weights), 1, 1)

        constraints = [normalization_constraint]
        if constrain_mean:
            mean_constraint = LinearConstraint(np.array(x_values), _target_mean, _target_mean)
            constraints.append(mean_constraint)
        if min_weight is not None:
            constraints.append(LinearConstraint(np.identity(len(norm_initial_weights), dtype=float),
                                                np.ones_like(norm_initial_weights) * min_weight,
                                                np.ones_like(norm_initial_weights) * np.inf))

        if len(norm_initial_weights) == 1:
            for constraintIdx in range(0, len(constraints)):
                residuals = constraints[constraintIdx].residual(norm_initial_weights)
                if np.min(residuals[0]) < 0.0:
                    print(
                        "Numerical minimization error: Single value of %f does not satisfy lower bound constraint #%d" % (
                            x_values[0], constraintIdx))
                    return []
                if np.min(residuals[0]) < 0.0:
                    print(
                        "Numerical minimization error: Single value of %f does not satisfy upper bound constraint #%d" % (
                            x_values[0], constraintIdx))
                    return []
            return norm_initial_weights

        result: OptimizeResult = minimize(objective, np.array(norm_initial_weights),
                                          constraints=constraints)
        if not result.success:
            if verbosity > 1:
                print("Numerical minimization error: %s" % result.message)
            return []

        vals = result.x
        if rescale_to_function:
            ratios = np.divide(initial_weights, vals)
            vals = np.multiply(vals, np.sum(ratios) / len(ratios))
        return vals.tolist()


class NormalDistribution(ShapeFunction):
    def __init__(self, mean: float, sigma: float):
        self._mean = mean
        self.sigma = sigma

    def mean(self) -> float:
        return self._mean

    def evaluate(self, x: float) -> float:
        return math.exp(- ((x - self._mean) / self.sigma) ** 2 / 2) / (math.sqrt(2 * math.pi) * self.sigma)


class DampenedDistribution(ShapeFunction):
    def __init__(self, base: ShapeFunction, floor: float, alpha: float):
        self.base = base
        self.floor = floor
        self.alpha = alpha

    def mean(self) -> float:
        return 0 if self.floor > 0 else self.base.mean()

    def evaluate(self, x: float) -> float:
        return self.base.evaluate(x) * self.alpha + self.floor


def make_normal_distribution_from_string(center: float, str_expression: str) -> NormalDistribution:
    return NormalDistribution(center + float(str_expression.split(':')[0]), float(str_expression.split(':')[1]))


class ShapeFunctionLinearCombination(ShapeFunction):
    def __init__(self, shapes: list[tuple[ShapeFunction, float]]):
        self.functions = [s[0] for s in shapes]
        self.weights = [s[1] for s in shapes]

    def mean(self) -> float:
        return np.dot([f.mean() for f in self.functions], self.weights)

    def evaluate(self, x: float) -> float:
        return np.dot([f.evaluate(x) for f in self.functions], self.weights)


rtpDistributionsStr = args.rtp_distribution.split(',')
rtpDistributions = list[tuple[NormalDistribution, float]]()
for distribution_str in rtpDistributionsStr:
    weight_and_value = distribution_str.split('*')
    rtpDistributions.append((make_normal_distribution_from_string(avgRtpPercent, weight_and_value[-1]),
                             1.0 if len(weight_and_value) == 1 else float(weight_and_value[0])))
rtpDistributionJoined = ShapeFunctionLinearCombination(rtpDistributions)
amountOfNumsDistribution = DampenedDistribution(NormalDistribution(float(args.thunder), float(args.sigma)), 1,
                                                5)
probabilityForHowManyNumbersDrawn = amountOfNumsDistribution.create_weights(
    np.arange(float(thunderNumbersMin), float(thunderNumbersMax) + 1.0, 1.0).tolist(), True, float(args.thunder),
    min_weight=0.001)

if len(probabilityForHowManyNumbersDrawn) != thunderNumbersMax - thunderNumbersMin + 1:
    print(
        "ERROR: It is impossible to achieve a good distribution for the amount of numbers drawn with the given config. Make sure your desired average is not close to the min/max amount of numbers that can be drawn.")
    exit(3)


def plot_function(dist: ShapeFunction, start: float, end: float, step: float,
                  discrete_weights: list[float] | None = None, constrain_mean: bool = True,
                  target_mean: float | None = None, rescale_to_function: bool = False):
    values = []
    total_prob = 0
    avg = 0
    x_values = np.arange(start, end, step)
    for x in x_values:
        val = dist.evaluate(x)
        total_prob += val
        avg += val * x
        values.append(val)

    # print("Average: %.02f (theoretical %.02f)" % (avg / total_prob, dist.mean()))
    plt.scatter(x_values, values)
    if discrete_weights is not None:
        weights = dist.create_weights(discrete_weights, constrain_mean, target_mean, rescale_to_function)
        plt.scatter(discrete_weights, weights)
        # print("Discrete Average: %.02f" % np.sum(np.multiply(discrete_weights, weights)))


thunderNumsValueDistribution = DampenedDistribution(NormalDistribution(thunderNumsShape[0], thunderNumsShape[1]),
                                                    thunderNumsShape[2], thunderNumsShape[3])

if args.plot_multipliers:
    plot_function(thunderNumsValueDistribution, thunderNums[0], thunderNums[-1], 5, thunderNums, False,
                  rescale_to_function=True)
    plt.title("Thunder multiplier probabilities")
    plt.show()
if args.plot_rtp:
    plot_function(rtpDistributionJoined, rtp_limits[0] if rtp_limits[0] > 0.0 else theoretical_rtp_limits[0],
                  rtp_limits[1] if rtp_limits[1] != math.inf else theoretical_rtp_limits[1], 1)
    plt.title("RTP distribution")
    plt.show()
if args.plot_thunder_count:
    plot_function(amountOfNumsDistribution, thunderNumbersMin, thunderNumbersMax, 0.1,
                  [n for n in range(thunderNumbersMin, thunderNumbersMax + 1)], False, rescale_to_function=True)
    plt.title("Distribution of number of thunder multipliers drawn per round")
    plt.show()

desiredFrequencies = thunderNumsValueDistribution.create_weights(thunderNums, False, min_weight=0.001)


class SumProbabilityInfo:
    def __init__(self):
        self.rtp = 0
        self.combinations = list[list[int]]()

    def get_combo_weight(self, multiplier_weights: dict[int, float], idx: int) -> float:
        w = 1
        for n in self.combinations[idx]:
            w *= multiplier_weights.get(n, 0)
        return w

    def get_combo_weights(self, multiplier_weights: dict[int, float]) -> list[float]:
        return [self.get_combo_weight(multiplier_weights, idx) for idx in range(len(self.combinations))]

    def random_select_combo(self, multiplier_weights: dict[int, float]) -> list[int]:
        if len(self.combinations) == 1:
            return self.combinations[0]
        weights = self.get_combo_weights(multiplier_weights)
        rnd = random.random() * sum(weights)
        for idx, combo in enumerate(weights):
            if rnd < combo:
                return self.combinations[idx]
            rnd -= combo
        return []


class MultiSumProbabilityInfo:
    def __init__(self, n: int):
        self.amount_of_nums = n
        self.sums = dict[int, SumProbabilityInfo]()
        self.total_unique_combinations = 0
        self.rtp_range = [math.inf, 0.0]


def populate_possible_combos(collector: MultiSumProbabilityInfo, list_of_all_nums: list[int],
                             current_combo_of_nums: list[int], _sum: int, target_amount: int, budget: [float, float]):
    if _sum > budget[1]:
        return

    if len(current_combo_of_nums) == target_amount:
        if _sum < budget[0]:
            return
        if len(mandatoryMultipliers):
            if len(mandatoryMultipliers.intersection(set(current_combo_of_nums))) == 0:
                return
        prob_info = collector.sums.setdefault(_sum, SumProbabilityInfo())
        prob_info.combinations.append(current_combo_of_nums)
        collector.total_unique_combinations += 1
        return

    for idx, _num in enumerate(list_of_all_nums):
        populate_possible_combos(collector, list_of_all_nums[idx:], current_combo_of_nums + [_num], _sum + _num,
                                 target_amount,
                                 budget)


def get_distributions_for_n_amount_of_thunder_draws(n: int) -> MultiSumProbabilityInfo:
    possible_sums = MultiSumProbabilityInfo(n)

    budget_taken_by_normal_multipliers = (numbers - n) * boardMultiplier
    thunder_budget = np.subtract(np.multiply(effective_rtp_range, numbers ** 2 / 100),
                                 budget_taken_by_normal_multipliers).tolist()
    populate_possible_combos(possible_sums, thunderNums, list[int](), 0, n, thunder_budget)

    for sum_, sum_info in possible_sums.sums.items():
        sum_info.rtp = (sum_ + budget_taken_by_normal_multipliers) * 100 / numbers ** 2
        possible_sums.rtp_range[0] = min(possible_sums.rtp_range[0], sum_info.rtp)
        possible_sums.rtp_range[1] = max(possible_sums.rtp_range[1], sum_info.rtp)

    return possible_sums


def calc_sum_probabilities(info: MultiSumProbabilityInfo, multiplier_probabilities: dict[int, float]) -> dict[int, float]:
    rtp_contributors = dict[int, float]()
    total_weight = 0.0
    for sum_, sum_info in info.sums.items():
        w = sum(sum_info.get_combo_weights(multiplier_probabilities))
        total_weight += w
        rtp_contributors.setdefault(sum_, 0.0)
        rtp_contributors[sum_] += w

    for k in rtp_contributors.keys():
        rtp_contributors[k] /= total_weight

    return rtp_contributors

def calculate_thunder_combos_probabilities(info: MultiSumProbabilityInfo, multiplier_probabilities: dict[int, float]) -> dict[tuple[int, ...], float]:
    rtp_contributors: dict[tuple[int, ...], float] = {}
    total_weight = 0.0
    for sum_, sum_info in info.sums.items():
        for combo in sum_info.combinations:
            w = 1
            for n in combo:
                w *= multiplier_probabilities.get(n, 0)
            total_weight += w
            combo_tuple = tuple(combo)  # Convert list to tuple
            rtp_contributors.setdefault(combo_tuple, 0.0)
            rtp_contributors[combo_tuple] += w

    for k in rtp_contributors.keys():
        rtp_contributors[k] /= total_weight

    return rtp_contributors


def get_multiplier_frequencies(multiplier_weights: list[float]) -> list[float]:
    frequency_of_multipliers: list[float] = [0.0] * len(thunderNums)
    mapped = {thunderNums[idx]: multiplier_weights[idx] for idx in range(len(thunderNums))}
    for all_sums in cachedProbabilityDistributions.values():
        freqs_of_this_amount = [0.0] * len(thunderNums)
        for_this_amount = calc_sum_probabilities(all_sums, mapped)
        for sum_, probability in for_this_amount.items():
            freqs_for_this_sum = [0.0] * len(thunderNums)
            combo_weights = all_sums.sums[sum_].get_combo_weights(mapped)
            for idx, combo in enumerate(all_sums.sums[sum_].combinations):
                for multiplier in combo:
                    freqs_for_this_sum[thunderNums.index(multiplier)] += combo_weights[idx]
            normalize_const = 1 / sum(combo_weights)
            for idx in range(len(thunderNums)):
                freqs_of_this_amount[idx] += freqs_for_this_sum[idx] * probability * normalize_const

        draw_probability = probabilityForHowManyNumbersDrawn[all_sums.amount_of_nums - thunderNumbersMin]
        for idx in range(len(thunderNums)):
            frequency_of_multipliers[idx] += draw_probability * freqs_of_this_amount[idx]
    return normalize_weights(frequency_of_multipliers)


def enumerate_possible_combinations() -> dict[int, MultiSumProbabilityInfo]:
    possible_combos = dict[int, MultiSumProbabilityInfo]()
    for num_thunder_multipliers in range(thunderNumbersMin, thunderNumbersMax + 1):
        possible_sums = get_distributions_for_n_amount_of_thunder_draws(num_thunder_multipliers)
        if possible_sums.total_unique_combinations == 0:
            print(
                "ERROR: There are no valid multiplier combinations if %d thunder numbers are drawn! Possible reasons are:" % possible_sums.amount_of_nums)
            print("\t- There is no combination that satisfies both 'rtp-limits' and 'must-include-multipliers'")
            print("\t- The world is ending")
            exit(4)
        possible_combos[num_thunder_multipliers] = possible_sums
    return possible_combos


distributionValid = False
cachedProbabilityDistributions = dict[int, MultiSumProbabilityInfo]()
while not distributionValid:
    cachedProbabilityDistributions = enumerate_possible_combinations()
    distributionValid = True

    uniform_weights = [1 / len(thunderNums)] * len(thunderNums)
    uniform_frequencies = get_multiplier_frequencies(uniform_weights)
    for idx, freq in reversed(list(enumerate(uniform_frequencies))):
        if freq == 0:
            print(
                "WARNING: Multiplier %d will never fall because of RTP limits - proceeding with this multiplier excluded!" %
                thunderNums[idx])
            del thunderNums[idx]
            del desiredFrequencies[idx]
            distributionValid = False

if rtpBias > 0:
    idealWeightsForGivenRTPDistribution = [0.0] * len(thunderNums)
    for all_sums in cachedProbabilityDistributions.values():
        rtpWeightSum = 0.0
        derivedWeightsForSum = [0.0] * len(thunderNums)
        for sum_info in all_sums.sums.values():
            rtpWeightPerNumber = math.pow(rtpDistributionJoined.evaluate(sum_info.rtp), 1 / all_sums.amount_of_nums)
            rtpWeightSum += rtpWeightPerNumber * all_sums.amount_of_nums * len(sum_info.combinations)
            for combo in sum_info.combinations:
                for num in combo:
                    derivedWeightsForSum[thunderNums.index(num)] += rtpWeightPerNumber

        draw_probability = probabilityForHowManyNumbersDrawn[all_sums.amount_of_nums - thunderNumbersMin]
        for idx, value in enumerate(derivedWeightsForSum):
            derivedWeightsForSum[idx] /= rtpWeightSum
            idealWeightsForGivenRTPDistribution[idx] += derivedWeightsForSum[idx] * draw_probability

    desiredFrequencies = (np.array(desiredFrequencies) * (1 - rtpBias) + rtpBias * np.array(
        get_multiplier_frequencies(idealWeightsForGivenRTPDistribution))).tolist()


def calc_avg_rtp(multiplier_probabilities: dict[int, float]) -> float:
    avg_rtp = 0.0
    for all_sums in cachedProbabilityDistributions.values():
        draw_probability = probabilityForHowManyNumbersDrawn[all_sums.amount_of_nums - thunderNumbersMin]
        for_this_amount = calc_sum_probabilities(all_sums, multiplier_probabilities)

        for sum_, probability in for_this_amount.items():
            avg_rtp += probability * draw_probability * all_sums.sums[sum_].rtp
    return avg_rtp


def calc_avg_rtp_vec(multiplier_probabilities: list[float]) -> float:
    mapped = {thunderNums[idx]: multiplier_probabilities[idx] for idx in range(len(thunderNums))}
    return calc_avg_rtp(mapped)


def fit_sums_to_rtp(desired_multiplier_frequency: list[float]) -> list[float]:
    ones = np.ones(len(thunderNums), dtype=float)

    def objective(weights: list[float]):
        new_multiplier_frequencies = get_multiplier_frequencies(weights)
        per_component_max = np.maximum(new_multiplier_frequencies, desired_multiplier_frequency)
        per_component_min = np.minimum(new_multiplier_frequencies, desired_multiplier_frequency)
        if np.min(per_component_min) <= 0:
            return np.inf
        return np.sum(np.square(np.subtract(np.divide(per_component_max, per_component_min), ones)))

    # Constraint: The weighted mean should be equal to the target mean
    normalization_constraint = LinearConstraint(ones, 1, 1)
    rtp_constraint = NonlinearConstraint(lambda x: calc_avg_rtp_vec(x), avgRtpPercent, avgRtpPercent)
    non_negative_constraint = LinearConstraint(np.identity(len(thunderNums), dtype=float),
                                               ones * 0.001,  # at least 0.1% of the time!
                                               ones * np.inf)
    constraints = [normalization_constraint, rtp_constraint, non_negative_constraint]

    initial_estimate = np.divide(desired_multiplier_frequency, uniform_frequencies)
    initial_estimate = np.multiply(initial_estimate, uniform_weights)
    initial_estimate = np.divide(initial_estimate, np.sum(initial_estimate))
    result: OptimizeResult = minimize(objective, initial_estimate, constraints=constraints)
    if not result.success:
        return []

    return result.x.tolist()

def get_fee_diff_bonus(fee, fireball_numbers, base_multiplier=24, standard_multiplier=36, numbers=37):
    roulette_rtp = standard_multiplier / numbers # 0.973
    diff_rtp = fee * roulette_rtp
    diff_base_rtp = (standard_multiplier - base_multiplier) * (numbers - fireball_numbers) / (numbers * numbers)

    return diff_rtp - diff_base_rtp

def adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="first"):
    """
    Adjusts weights to achieve the target RTP by modifying a specific multiplier's weight.

    Args:
        multipliers (array): List of multiplier values.
        old_weights (array): Initial weights corresponding to the multipliers.
        numbers (int): Number of outcomes (e.g., roulette numbers).
        x_percent (float): Target RTP increase percentage.
        target_multiplier (str): Specify which multiplier's weight to change ("first" or "last").

    Returns:
        dict: Contains the old weights, new weights, old RTP, target RTP, and new RTP.
    """
    # Target RTP
    current_rtp = np.sum(multipliers * old_weights) / numbers
    target_rtp = current_rtp + x_percent / 100

    if (target_rtp < 0):
        raise ValueError(f"Target RTP cannot be negative: {target_rtp}.")

    # Determine the index to adjust
    if target_multiplier == "first":
        j = 0  # Adjust the first multiplier
    elif target_multiplier == "last":
        j = len(multipliers) - 1  # Adjust the last multiplier
    else:
        raise ValueError("Invalid target_multiplier value. Use 'first' or 'last'.")

    # Calculate x
    numerator = target_rtp * numbers - current_rtp * numbers
    denominator = (4 * multipliers[j] - np.sum(multipliers)) / 3
    x = numerator / denominator

    # Update weights
    new_weights = old_weights.copy()
    new_weights[j] += x
    for i in range(len(multipliers)):
        if i != j:
            new_weights[i] -= x / 3

    # Normalize to avoid negative weights
    # new_weights = np.maximum(new_weights, 0)
    new_weights /= np.sum(new_weights)  # Ensure weights sum to 1

    # Calculate new RTP
    new_rtp = np.sum(multipliers * new_weights) / numbers

    # Return results
    return {
        "old_weights": old_weights,
        "new_weights": new_weights,
        "old_rtp": current_rtp,
        "target_rtp": target_rtp,
        "new_rtp": new_rtp,
    }

if verbosity > 1:
    print("Finding solutions for how multiplier weights should be set...")
thunder_num_weights = fit_sums_to_rtp(desiredFrequencies)
print("> Thunder weights: %s" % (str(thunder_num_weights)))
if len(thunder_num_weights) == 0:
    print(
        "ERROR: No solutions found for the given thunder setup. Possible reasons can be, but not limited to, the following:")
    print("\t- The target RTP is too close to the effective RTP limits.")
    for all_sums in cachedProbabilityDistributions.values():
        if avgRtpPercent < all_sums.rtp_range[0] or avgRtpPercent > all_sums.rtp_range[1]:
            print(
                "\t- The RTP range for games with %d %s (%.02f%% to %.02f%%) does not include the target RTP (%.02f%%). Such games would happen too often (%.02f%% of the time) to compensate with other multipliers." % (
                    all_sums.amount_of_nums, "multipliers" if all_sums.amount_of_nums != 1 else "multiplier",
                    all_sums.rtp_range[0], all_sums.rtp_range[1], avgRtpPercent,
                    probabilityForHowManyNumbersDrawn[all_sums.amount_of_nums - thunderNumbersMin] * 100))
    print("\t- The desired frequency for each multiplier is too far from what is possible.")
    exit(2)

if verbosity > 1:
    cosineSimilarity = np.dot(desiredFrequencies, thunder_num_weights) / math.sqrt(
        np.dot(desiredFrequencies, desiredFrequencies) * np.dot(thunder_num_weights, thunder_num_weights))
    print(
        "Found a good set of weights that satisfy the desired parameters (fit is %.02f%%)!" % (cosineSimilarity * 100))
    print("===========================")

# BONUS NUMBERS
class BonusValues:
    def __init__(self):
        self.bonusProbs = []
        self.bonusProbsFee = []

    def calcRTP(self, bonusProbs) -> float:
        return np.dot(bonusNums, bonusProbs) * (1 / numbers) * 100

    def print(self, prefix="", verb=verbosity):
        print("%sBonus Probabilities: %s (no fee)" % (prefix, str(self.bonusProbs)))
        self._print(prefix, verb, self.bonusProbs)
        if (len(self.bonusProbsFee) == len(bonusNums)):
            print("%sBonus Probabilities: %s (fee: %d%%)" % (prefix, str(self.bonusProbsFee), fireballFee))
            self._print(prefix, verb, self.bonusProbsFee)

    def _print(self, prefix="", verb=verbosity, bonusProbs: list[float] = []):
        print("%sBonus win probabilities:" % prefix)
        for idx, num in enumerate(bonusNums):
            bonusProb = bonusProbs[idx] * 100
            bonusWinProb = bonusProb / numbers
            jackpotWinProb = bonusNumberJackpotProb.get(num, 0) if args.enable_jackpot else 0
            if (jackpotWinProb > bonusWinProb):
                print("ERROR: The jackpot probability for bonus number %dx (prob: %.02f%%) is higher than the win probability (prob: %.02f%%)!" % (num, jackpotWinProb, bonusWinProb))
                exit(1)
            if jackpotWinProb > 0:
                print("\t%d: %.02f%% (win prob: %.02f%%, jackpot prob: %.02f%%)" % (
                    num, bonusProb, bonusWinProb, jackpotWinProb))
            else:
                print("\t%d: %.02f%% (win prob: %.02f%%)" % (num, bonusProb, bonusWinProb))
        print("%sBonus RTP: %.02f%%" % (prefix, self.calcRTP(bonusProbs)))
        print("===========================")

def get_bonus_probs() -> list[float]:
    target_value = numbers * bonusRTP / 100

    def objective(probs: list[float]):
        return np.square(np.dot(bonusNums, probs) - target_value)

    # Constraint: Probabilities must sum to 1
    constraints = [LinearConstraint(np.ones(len(bonusNums)), 1, 1)]

    # Bounds: Each probability must be between 0 and 1
    bounds = [(0, 1) for _ in bonusNums]

    result = minimize(objective, normalize_weights(bonusRatios), constraints=constraints, bounds=bounds)

    if not result.success:
        print("ERROR: Optimization failed to find a solution for bonus probabilities!")
        exit(2)

    bonusResult = BonusValues()
    bonusResult.bonusProbs = result.x

    fee_diff_bonus = get_fee_diff_bonus(fireballFee/100, thunderNumbersMin, boardMultiplier, 36, numbers)
    adjusted_weights = adjust_weights(bonusNums, bonusResult.bonusProbs, numbers, fee_diff_bonus * 100)
    bonusResult.bonusProbsFee = adjusted_weights["new_weights"]

    return bonusResult

if args.enable_bonus:
    bonusValues = get_bonus_probs()
    bonusValues.print("> ", verbosity)

if len(args.output_config):
    vals = {"thunder-multipliers": thunderNums,
            "multiplier-weights": thunder_num_weights,
            "must-appear-thunder-multipliers": list(mandatoryMultipliers),
            "thunder-count-weights": probabilityForHowManyNumbersDrawn,
            "min-thunder-count": thunderNumbersMin,
            "total-nums": numbers,
            "board-multiplier": boardMultiplier,
            "min-rtp": rtp_limits_orig[0],
            "max-rtp": rtp_limits_orig[1]}
    if args.enable_bonus:
        vals["bonus-prizes"] = [{"multiplier": multiplier, "weight": weight} for multiplier, weight in zip(bonusNums, bonusValues.bonusProbs)]
    with open(args.output_config, "w") as f:
        json.dump(vals, f, indent=4)
        print("JSON successfully saved to: " + args.output_config)

frequencies_theoretical = get_multiplier_frequencies(thunder_num_weights)
mapped_multiplier_weights = {thunderNums[idx]: thunder_num_weights[idx] for idx in range(len(thunderNums))}
avgRtpFit = calc_avg_rtp(mapped_multiplier_weights)
sum_weights = list[dict[int, float]]()
for all_sums in cachedProbabilityDistributions.values():
    sum_weights.append(calc_sum_probabilities(all_sums, mapped_multiplier_weights))


def random_select_from_weights(weights: list[float], normalized: bool = True) -> int:
    normalize_const = 1.0 if normalized else sum(weights)

    total = 0.0
    selected_random = random.random() * normalize_const
    for idx, w in enumerate(weights):
        total += w
        if selected_random < total:
            return idx

    return -1


class ThunderRound:
    def __init__(self):
        self.thunder_multipliers = []
        self.thunder_numbers = []
        self.total_thunder_multiplier = 0
        self.bonus_multiplier = 0
        self.bonus_number = -1
        self.jackpot = -1

    def _numPlainNumbers(self) -> int:
        return numbers - len(self.thunder_multipliers)

    def _calcPlainRTP(self) -> float:
        return rtpPerStep * boardMultiplier * self._numPlainNumbers() * 100

    def _calcFireballRTP(self) -> float:
        return rtpPerStep * self.total_thunder_multiplier * 100

    def _calcBonusRTP(self) -> float:
        # Bonus RTP is applied to all bet numbers if bonus_multiplier > 0
        return 100 * self.bonus_multiplier / numbers if self.bonus_multiplier > 0 else 0

    def _calcJackpotRTP(self) -> float:
        # sum jackpot RTPs for all possible jackpots
        return sum(jackpotRTPs) if args.enable_jackpot else 0.0

    def calcRTP(self) -> float:
        return self._calcPlainRTP() + self._calcFireballRTP() + self._calcBonusRTP() + self._calcJackpotRTP()

    def print(self, prefix="", verb=verbosity):
        if verb == 0:
            return
        print("%sThunder multipliers: %s" % (prefix, str(self.thunder_multipliers)))
        if self.bonus_multiplier > 0:
            print("%sBonus multiplier: %d" % (prefix, self.bonus_multiplier))
        if verb > 1:
            print("%sBoard RTP: %.02f%% (plain: %.02f%%, fireball: %.02f%%, bonus: %.02f%%)" % (prefix, self.calcRTP(), self._calcPlainRTP(), self._calcFireballRTP(), self._calcBonusRTP()))


def get_bonus_multipliers(betSize: float):
    result = {
        "bonus_multiplier": 0,
        "bonus_number": -1,
        "jackpot": -1,
    }

    if args.enable_bonus:
        result["bonus_multiplier"] = random.choices(bonusNums, bonusValues.bonusProbs)[0]
        result["bonus_number"] = random.randint(0, numbers - 1)

    if args.enable_jackpot and args.enable_bonus:
        bonusProb = bonusValues.bonusProbs[bonusNums.index(result["bonus_multiplier"])]
        # Get random number between 0 and bonusProb and store it into jackpotRand
        jackpotRand = random.random() * bonusProb
        cumProb = 0

        # Get jackpot probabilities
        for idx in range(jackpotLevels):
            if jackpotCombinedBonusMultipliers[idx] == result["bonus_multiplier"]:
                avgJackpotRoundProb = numbers * jackpotWinProbs[idx] / 100  # jackpotWinProbs are in percentage
                jackpotRoundProb = avgJackpotRoundProb * betSize / jackpotAvgBetPerPlayerRound
                cumProb += jackpotRoundProb
                if jackpotRand < cumProb:
                    result["jackpot"] = idx
                    break

    return result

def get_thunder_multipliers() -> ThunderRound:
    ret = ThunderRound()
    num_to_spawn = thunderNumbersMin + random_select_from_weights(probabilityForHowManyNumbersDrawn)
    if verbosity > 2:
        print("Decided %d numbers will be thunder numbers!" % num_to_spawn)
    if num_to_spawn == 0:
        return ret

    weights_for_sums = sum_weights[num_to_spawn - thunderNumbersMin]
    selected_sum = list(weights_for_sums.keys())[random_select_from_weights(list(weights_for_sums.values()))]
    ret.thunder_multipliers = cachedProbabilityDistributions[num_to_spawn].sums[selected_sum].random_select_combo(
        mapped_multiplier_weights)
    ret.thunder_multipliers.sort(reverse=True)  # descending order
    ret.total_thunder_multiplier = sum(ret.thunder_multipliers)
    # store thunder numbers without repeating (as many numbers as there are thunder multipliers), random between 0 and numbers - 1
    ret.thunder_numbers = random.sample(range(numbers), len(ret.thunder_multipliers))

    # Calculate bonus and jackpot values
    bonus_jackpot_data = get_bonus_multipliers(jackpotAvgBetPerPlayerRound if args.enable_jackpot else 1)
    ret.bonus_multiplier = bonus_jackpot_data["bonus_multiplier"]
    ret.bonus_number = bonus_jackpot_data["bonus_number"]
    ret.jackpot = bonus_jackpot_data["jackpot"]

    ret.winning_number = random.randint(0, numbers - 1)
    return ret

if numSims <= 0:
    exit(0)

print("Running %d simulations..." % numSims)
if numSims == 1:
    thisRound = get_thunder_multipliers()
    thisRound.print()
    exit(0)

fillCharacter = "#"


def print_distribution(vals, x_min: str, x_max: str, num_rows=10, spacing=0, min_val=None, max_val=None):
    max_value = max(vals) if max_val is None else max_val
    min_value = min(vals) if min_val is None else min_val
    one_bar = (max_value - min_value) / num_rows
    for rowIdx in range(num_rows):
        row = "\t"
        for i in range(len(vals)):
            if vals[i] - min_value > (num_rows - 1 - rowIdx) * one_bar:
                row += fillCharacter
            else:
                row += " "

            if spacing > 0:
                row += " " * spacing
        print(row)
    print("\t" + x_min + (" " * (len(vals) + (len(vals) - 1) * spacing - len(x_min) - len(x_max))) + x_max)


avgRTP = 0.0
avgBaseRTP = 0.0
avgThunderRtp = 0.0
avgBonusRtp = 0.0
avgJackpotRtp = 0.0
minRTP = 100.0
maxRTP = 0.0
multiplierSumCounts = [dict[int, int]() for i in range(thunderNumbersMax - thunderNumbersMin + 1)]
drawnNumbersBins = [0] * (thunderNumbersMax - thunderNumbersMin + 1)
thunderBins = [0] * len(thunderNums)
bonusBins = [0] * len(bonusNums) if args.enable_bonus else []
numGamesWithMultiplier = [0] * len(thunderNums)
numRTPBins = 101
RTPBins = [0] * numRTPBins
batchPrint = 250000
drawnNumbersComboCounts = Counter()
for i in range(numSims):
    thisRound = get_thunder_multipliers()
    rtp = thisRound.calcRTP()
    thisRound.print("[%d] " % i, verbosity)
    avgRTP += rtp
    avgBaseRTP += thisRound._calcPlainRTP()
    avgThunderRtp += thisRound._calcFireballRTP()
    avgBonusRtp += thisRound._calcBonusRTP()
    maxRTP = max(maxRTP, rtp)
    minRTP = min(minRTP, rtp)
    if numRTPBins > 1:
        RTPBins[math.floor((rtp - effective_rtp_range_with_bonus[0]) / (effective_rtp_range_with_bonus[1] - effective_rtp_range_with_bonus[0]) * (numRTPBins - 1))] += 1
    drawnNumbersComboCounts[tuple(thisRound.thunder_multipliers)] += 1
    unique_numbers = set[int]()
    for _num in thisRound.thunder_multipliers:
        num_idx = thunderNums.index(_num)
        unique_numbers.add(num_idx)
        thunderBins[num_idx] += 1
    for _num_idx in unique_numbers:
        numGamesWithMultiplier[_num_idx] += 1
    if args.enable_bonus:
        bonusBins[bonusNums.index(thisRound.bonus_multiplier)] += 1
    multiplierSum = sum(thisRound.thunder_multipliers)
    idx_of_amount = len(thisRound.thunder_multipliers) - thunderNumbersMin
    multiplierSumCounts[idx_of_amount].setdefault(multiplierSum, 0)
    multiplierSumCounts[idx_of_amount][multiplierSum] += 1
    drawnNumbersBins[idx_of_amount] += 1
    if i > 0 and (i + 1) % batchPrint == 0:
        print("\t%.02f%% complete" % (100 * (i + 1) / numSims))

avgRTP /= numSims
avgBaseRTP /= numSims
avgThunderRtp /= numSims
avgBonusRtp /= numSims
print("=== Completed %d simulations ===" % numSims)
print("> Average RTP: %.02f%% (base: %.02f%%, thunder: %.02f%%, bonus: %.02f%%)" % (avgRTP, avgBaseRTP, avgThunderRtp, avgBonusRtp))
print("> Minimum RTP: %.02f%%" % minRTP)
print("> Maximum RTP: %.02f%%" % maxRTP)
print("> Normal number multiplier (constant): %d" % boardMultiplier)
if len(drawnNumbersBins) > 1:
    print("> Number of drawn multipliers per round (avg %.02f):" % (
            sum([count * (thunderNumbersMin + idx) for idx, count in enumerate(drawnNumbersBins)]) / numSims))
    drawnNumbersSpacing = 5
    print("\t" + "-" * (len(drawnNumbersBins) + (len(drawnNumbersBins) - 1) * drawnNumbersSpacing))
    print_distribution(drawnNumbersBins, "%d" % thunderNumbersMin, "%d" % thunderNumbersMax, 10, drawnNumbersSpacing)
    print("\t" + "-" * (len(drawnNumbersBins) + (len(drawnNumbersBins) - 1) * drawnNumbersSpacing))
    for idx, num in enumerate(drawnNumbersBins):
        print("\t%s: %.02f%%   (target %.02f%%)" % (
            idx + thunderNumbersMin, num * 100 / numSims, probabilityForHowManyNumbersDrawn[idx] * 100))
else:
    print("> Amount of numbers drawn (constant): %d" % thunderNumbersMin)

if args.log_distributions:
    if numRTPBins > 1:
        print("\n> RTP distribution:")
        print("\t" + "-" * numRTPBins)
        print_distribution(RTPBins, "%.02f%%" % minRTP, "%.02f%%" % maxRTP)
        print("\t" + "-" * numRTPBins)
    else:
        print("\n> RTP (constant): %.02f%%" % minRTP)

    print("\n> Thunder number multiplier distribution:")
    thunderSpacing = 5
    print("\t" + "-" * (len(thunderBins) + (len(thunderBins) - 1) * thunderSpacing))
    print_distribution(thunderBins, "%dx" % thunderNums[0], "%dx" % thunderNums[-1], 15, thunderSpacing)
    print("\t" + "-" * (len(thunderBins) + (len(thunderBins) - 1) * thunderSpacing))
    total_thunder_multipliers = sum(thunderBins)
    for idx, num in enumerate(thunderBins):
        print("\tx%s: %.02f%%   (target %.02f%%)" % (
            thunderNums[idx], num * 100 / total_thunder_multipliers, frequencies_theoretical[idx] * 100))

    print("\n> Percentage of rounds with multiplier:")
    print("\t" + "-" * (len(numGamesWithMultiplier) + (len(numGamesWithMultiplier) - 1) * thunderSpacing))
    print_distribution(numGamesWithMultiplier, "%dx" % thunderNums[0], "%dx" % thunderNums[-1], 15, thunderSpacing, 0,
                       numSims)
    print("\t" + "-" * (len(numGamesWithMultiplier) + (len(numGamesWithMultiplier) - 1) * thunderSpacing))
    for idx, num in enumerate(numGamesWithMultiplier):
        print("\tx%s: %.02f%%" % (thunderNums[idx], num * 100 / numSims))

    thunder_combos_probabilities = calculate_thunder_combos_probabilities(all_sums, mapped_multiplier_weights)
    print("\n> Thunder multipliers combinations (number of times they appear):")
    max_key_width = max(len(str(list(key))) for key in drawnNumbersComboCounts.keys())
    for combo, value in sorted(thunder_combos_probabilities.items(), key=lambda item: item[0], reverse=True):
        key_str = str(list(combo))
        numHits = drawnNumbersComboCounts.get(combo, 0)
        value_str = f"{numHits} ({numHits/numSims*100:.02f}%, target {value*100:.02f}%)"
        print(f"\t{key_str.ljust(max_key_width)} :  {value_str.rjust(30)}")

    if args.enable_bonus:
        print("\n> Bonus multiplier distribution:")
        print("\t" + "-" * (len(bonusBins) + (len(bonusBins) - 1) * thunderSpacing))
        print_distribution(bonusBins, "%dx" % bonusNums[0], "%dx" % bonusNums[-1], 15, thunderSpacing)
        print("\t" + "-" * (len(bonusBins) + (len(bonusBins) - 1) * thunderSpacing))
        total_bonus_multipliers = sum(bonusBins)
        for idx, num in enumerate(bonusBins):
            print("\tx%s: %.02f%%   (target %.02f%%)" % (bonusNums[idx], num * 100 / total_bonus_multipliers, bonusValues.bonusProbs[idx] * 100))

import csv

class SimulationCsv:
    def __init__(self):
        self.data = []
        self.fieldnames = []
        self.aggr_data_fieldnames = []
        self.aggr_data = []

    def compose_fieldnames(self, num_thunder_multipliers, num_players):
        self.fieldnames = []
        self.fieldnames.append("game_round")
        self.fieldnames.append(f"winning_num")
        for i in range(1, num_thunder_multipliers + 1):
            self.fieldnames.append(f"fb_multi{i}")
            self.fieldnames.append(f"fb_num{i}")
        for i in range(1, num_players + 1):
            self.fieldnames.append(f"player{i}_bonus_multi")
            self.fieldnames.append(f"player{i}_bonus_jp")
            self.fieldnames.append(f"player{i}_bonus_num")
            self.fieldnames.append(f"player{i}_bet")
            self.fieldnames.append(f"player{i}_bonus_win")
            self.fieldnames.append(f"player{i}_bonus_jp_level")
            self.fieldnames.append(f"player{i}_bonus_jp_win")

        self.aggr_data_fieldnames = ["player_num", "player_bet", "player_bonus_win", "player_bonus_jp_win", "player_bonus_jp_num_won"];

    def add_line(self, values):
        """
        Add a line of data to the CSV using a list of values.
        :param values: List of values, in the same order as fieldnames.
        """
        if len(values) != len(self.fieldnames):
            raise ValueError("The number of values must match the number of fieldnames.")
        self.data.append(dict(zip(self.fieldnames, values)))

    def sum_player_bonus_wins(self, num_players):
        """
        Calculate the sum of all player{i}_bonus_win columns for each player.
        :param num_players: Number of players.
        :return: A dictionary with player index as key and sum as value.
        """
        sums = {
            **{f"bonus_jp_win_{jpName}": 0 for jpName in jackpotLevelNames},
            **{f"bonus_jp_num_won_{jpName}": 0 for jpName in jackpotLevelNames},
            **{f"avg_won_jp_{jpName}": 0 for jpName in jackpotLevelNames}
        }

        for i in range(1, num_players + 1):
            player_bonus_win = 0
            player_bet = 0
            player_bonus_jp_win = 0
            player_bonus_jp_num_won = 0
            for row in self.data:
                player_bonus_win += row.get(f"player{i}_bonus_win", 0)
                player_bet += row.get(f"player{i}_bet", 0)
                player_jp_win = row.get(f"player{i}_bonus_jp_win", 0)
                if player_jp_win > 0:
                    player_bonus_jp_win += player_jp_win
                    player_bonus_jp_level = row.get(f"player{i}_bonus_jp_level", "")
                    if player_bonus_jp_level in jackpotLevelNames:
                        sums[f"bonus_jp_win_{player_bonus_jp_level}"] += player_jp_win
                        sums[f"bonus_jp_num_won_{player_bonus_jp_level}"] += 1
                    player_bonus_jp_num_won += 1
            self.aggr_data.append(dict(zip(self.aggr_data_fieldnames, [i, player_bet, player_bonus_win, player_bonus_jp_win, player_bonus_jp_num_won])))
        for jpName in jackpotLevelNames:
            sums[f"avg_won_jp_{jpName}"] = sums[f"bonus_jp_win_{jpName}"] / sums[f"bonus_jp_num_won_{jpName}"] if sums[f"bonus_jp_num_won_{jpName}"] > 0 else 0

        return sums

    def print(self):
        """
        Print the data in tabular form.
        """
        print("###############################")
        print(f"{', '.join(self.fieldnames)}")
        for row in self.data:
            print(", ".join(str(row.get(field, "")) for field in self.fieldnames))

    def print_aggr(self):
        """
        Print the aggregated data in tabular form.
        """
        print("###############################")
        print(f"{', '.join(self.aggr_data_fieldnames)}")
        for row in self.aggr_data:
            print(", ".join(str(row.get(field, "")) for field in self.aggr_data_fieldnames))

    def export_to_csv(self, filename):
        """
        Export the data to a CSV file.
        :param filename: The output file name.
        """
        with open(filename, mode="w", newline="", encoding="utf-8") as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=self.fieldnames)
            writer.writeheader()
            writer.writerows(self.data)
        print(f"Data has been exported to {filename}")

def getJackpotPotRatio(potBalance: float, backpotBalance: float, minPot: float, maxPot: float = 0) -> float:
    if maxPot > 0 and potBalance > maxPot:
        return 0.0
    if backpotBalance < 0:
        return jackpotBackpotRatio;
    if backpotBalance > minPot:
        return 1.0
    return 1.0 - jackpotBackpotRatio;

if args.enable_jackpot:
    sim_csv = SimulationCsv()
    sim_csv.compose_fieldnames(thunderNumbersMax, jackpotAvgNumPlayers)
    jackpot_pot = [0] * jackpotLevels
    jackpot_backpot = [0] * jackpotLevels
    jackpot_wins = 0
    bets = 0
    for idx in range(jackpotLevels):
        jackpot_pot[idx] = jackpotMinPots[idx]
        jackpot_backpot[idx] = -jackpotMinPots[idx]

    for i in range(numSims):
        line = []
        line.append(i)
        roundData = get_thunder_multipliers()
        line.append(roundData.winning_number)
        for idx, multiplier in enumerate(roundData.thunder_multipliers):
            line.append(multiplier)
            line.append(roundData.thunder_numbers[idx])
        for j in range(jackpotAvgNumPlayers):
            betSize = jackpotAvgBetsPerPlayerRound[j]
            bets += betSize
            playerRoundData = get_bonus_multipliers(betSize)
            line.append(playerRoundData["bonus_multiplier"])
            line.append(playerRoundData["jackpot"])
            line.append(playerRoundData["bonus_number"])

            line.append(betSize) # bet
            # add part of bet from jackpotRTPs to the jackpot_pot for each level
            for jp in range(jackpotLevels):
                jackpot_bet_part = betSize * jackpotRTPs[jp] / 100
                jackpot_bet_part_pot = jackpot_bet_part * getJackpotPotRatio(jackpot_pot[jp], jackpot_backpot[jp], jackpotMinPots[jp], 0)
                jackpot_bet_part_backpot = jackpot_bet_part - jackpot_bet_part_pot
                jackpot_pot[jp] += jackpot_bet_part_pot
                jackpot_backpot[jp] += jackpot_bet_part_backpot

            win = 0
            jpWinLevel = ""
            jpWin = 0
            if playerRoundData["bonus_number"] == roundData.winning_number:
                win = playerRoundData["bonus_multiplier"] * betSize
                if playerRoundData["jackpot"] >= 0:
                    jpIdx = playerRoundData["jackpot"]
                    jpWinLevel = jackpotLevelNames[jpIdx]
                    jpWin = jackpot_pot[jpIdx] # this empties the pot - sets it to 0
                    jackpot_wins += jpWin
                    moveFromBackpotToPot = max(jackpot_backpot[jpIdx], jackpotMinPots[jpIdx]) # we move backpot to pot, but at least min pot
                    jackpot_pot[jpIdx] = moveFromBackpotToPot
                    jackpot_backpot[jpIdx] = jackpot_backpot[jpIdx] - moveFromBackpotToPot
            line.append(win)
            line.append(jpWinLevel)
            line.append(round(float(jpWin)))
        sim_csv.add_line(line)
    if len(args.output_simulations):
        sim_csv.export_to_csv(args.output_simulations)

    player_bonus_wins = sim_csv.sum_player_bonus_wins(jackpotAvgNumPlayers)
    sim_csv.print_aggr();
    print("###############################")
    for key, value in player_bonus_wins.items():
        print(f"{key}: {value}")

    # print jackpot pot
    print("###############################")
    print("Jackpot pot/backpot:")
    for jp in range(jackpotLevels):
        print(f"{jackpotLevelNames[jp]}: pot: {jackpot_pot[jp]}, backpot: {jackpot_backpot[jp]}")

    # print total bets and jackpot wins and avg rtp
    print("###############################")
    print(f"Total bets: {bets}")
    print(f"Total jackpot wins: {jackpot_wins}")
    print(f"Average jackpot RTP: {jackpot_wins / bets * 100:.02f}%")
