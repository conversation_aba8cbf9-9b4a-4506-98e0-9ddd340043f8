import random

import numpy as np
from scipy.optimize import OptimizeResult, minimize, LinearConstraint, NonlinearConstraint

numbers = 37
fireball_numbers = 4
base_multiplier = 24
standard_multiplier = 36
fee = 0.3

roulette_rtp = standard_multiplier / numbers # 0.973
diff_rtp = fee * roulette_rtp
diff_base_rtp = (standard_multiplier - base_multiplier) * (numbers - fireball_numbers - 1) / (numbers * numbers)

fee_diff_bonus = diff_rtp - diff_base_rtp
print(f"Bonus fee diff: {fee_diff_bonus * 100:.5f}%")

#############################
#############################
print("######################")

def normalize_weights(weights: list[float]) -> list[float]:
    weight_sum = np.sum(weights)
    return [w / weight_sum for w in weights]

def get_bonus_probs(multipliers, target_rtp, bonus_ratios=[8,4,2,1]) -> list[float]:
    target_value = numbers * target_rtp / 100

    def objective(probs: list[float]):
        return np.square(np.dot(multipliers, probs) - target_value)

    # Constraint: Probabilities must sum to 1
    constraints = [LinearConstraint(np.ones(len(multipliers)), 1, 1)]

    # Bounds: Each probability must be between 0 and 1
    bounds = [(0, 1) for _ in multipliers]

    result = minimize(objective, normalize_weights(bonus_ratios), constraints=constraints, bounds=bounds)

    if not result.success:
        print("ERROR: Optimization failed to find a solution for bonus probabilities!")
        exit(2)

    return result.x

def adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="last"):
    """
    Adjusts weights to achieve the target RTP by modifying a specific multiplier's weight.

    Args:
        multipliers (array): List of multiplier values.
        old_weights (array): Initial weights corresponding to the multipliers.
        numbers (int): Number of outcomes (e.g., roulette numbers).
        x_percent (float): Target RTP increase percentage.
        target_multiplier (str): Specify which multiplier's weight to change ("first" or "last").

    Returns:
        dict: Contains the old weights, new weights, old RTP, target RTP, and new RTP.
    """
    # Target RTP
    current_rtp = np.sum(multipliers * old_weights) / numbers
    target_rtp = current_rtp + x_percent / 100

    if (target_rtp < 0):
        raise ValueError(f"Target RTP cannot be negative: {target_rtp}.")

    # Determine the index to adjust
    if target_multiplier == "first":
        j = 0  # Adjust the first multiplier
    elif target_multiplier == "last":
        j = len(multipliers) - 1  # Adjust the last multiplier
    else:
        raise ValueError("Invalid target_multiplier value. Use 'first' or 'last'.")

    # Calculate x
    numerator = target_rtp * numbers - current_rtp * numbers
    denominator = (4 * multipliers[j] - np.sum(multipliers)) / 3
    x = numerator / denominator

    # Update weights
    new_weights = old_weights.copy()
    new_weights[j] += x
    for i in range(len(multipliers)):
        if i != j:
            new_weights[i] -= x / 3

    # Normalize to avoid negative weights
    # new_weights = np.maximum(new_weights, 0)
    new_weights /= np.sum(new_weights)  # Ensure weights sum to 1

    # Calculate new RTP
    new_rtp = np.sum(multipliers * new_weights) / numbers

    # Return results
    return {
        "old_weights": old_weights,
        "new_weights": new_weights,
        "old_rtp": current_rtp,
        "target_rtp": target_rtp,
        "new_rtp": new_rtp,
    }


# Example Inputs
multipliers = np.array([2, 5, 10, 20])
old_weights = np.array([0.74130435, 0.14782609, 0.07391304, 0.03695652])
numbers = 37
current_rtp = 0.1
x_percent = 1.1395  # Target increase percentage

result_last = adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="last")
result_first = adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="first")

# Output Results
print("Results for modifying the last multiplier's weight:")
for key, value in result_last.items():
    print(f"{key}: {value}")

print("\nResults for modifying the first multiplier's weight:")
for key, value in result_first.items():
    print(f"{key}: {value}")

#############################
# simulation

print(f"Initial weights: {old_weights}")

num_runs = 0
num_ok_first = 0
num_ok_last = 0
num_ok2_first = 0
num_ok2_last = 0

def validate_result(result) -> bool:
    return result and min(result["new_weights"]) > 0

def validate_result2(result) -> bool:
    if not validate_result(result):
        return False

    # check if in new_weights they are decreasing
    new_weights = result.get("new_weights", [])
    return all(new_weights[i] <= new_weights[i - 1] for i in range(1, len(new_weights)))

num_steps = 10
min_rtp_change = -3
max_rtp_change = 3

min_initial_rtp = 7
max_initial_rtp = 20
num_rtps = 20

for initial_rtp in np.linspace(min_initial_rtp, max_initial_rtp, num_rtps):
    old_weights = get_bonus_probs(multipliers, initial_rtp)

    for x_percent in np.linspace(min_rtp_change, max_rtp_change, num_steps):
        num_runs += 1

        # Adjust weights for the last multiplier
        result_last = adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="last")

        # Adjust weights for the first multiplier
        result_first = adjust_weights(multipliers, old_weights, numbers, x_percent, target_multiplier="first")

        if validate_result(result_last):
            num_ok_last += 1
        # else:
        #     print(f"Invalid result for weights (changing last): {old_weights}, new weights: {result_last['new_weights']}, rtp change: {x_percent}%")

        if validate_result(result_first):
            num_ok_first += 1
        # else:
        #     print(f"Invalid result for weights (changing first): {old_weights}, new weights: {result_first['new_weights']}, rtp change: {x_percent}%")

        if validate_result2(result_last):
            num_ok2_last += 1

        if validate_result2(result_first):
            num_ok2_first += 1


# Output Results
print("############################################")
print(f"Initial RTP range from {min_initial_rtp}% to {max_initial_rtp}% in {num_rtps} steps")
print(f"Changing rtp change from {min_rtp_change}% to {max_rtp_change}% in {num_steps} steps")
print(f"Total runs: {num_runs}")

print(f"Success rate for first multiplier (min(w)>0): {100*num_ok_first / num_runs:.2f}%")
print(f"Success rate for last multiplier (min(w)>0): {100*num_ok_last / num_runs:.2f}%")
print(f"Success rate for first multiplier (min(w)>0 and decreasing weights): {100*num_ok2_first / num_runs:.2f}%")
print(f"Success rate for last multiplier (min(w)>0 and decreasing weights): {100*num_ok2_last / num_runs:.2f}%")
