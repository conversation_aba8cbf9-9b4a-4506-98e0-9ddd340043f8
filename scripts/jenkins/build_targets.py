#!/usr/bin/env python3

import os
import sys

SCRIPTS_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
ROOT_DIR = os.path.dirname(SCRIPTS_DIR)
sys.path.append(os.path.dirname(SCRIPTS_DIR))


def main():
    import argparse
    from scripts.platform_constants import TARGET_BUILD_TYPE_DEFAULT, TARGET_BUILD_TYPES, UBUNTU_VERSION_DEFAULT, DEFAULT_BUILD_IMAGE_TAG
    from scripts.platform_lib import check_tronius_scripts_dir
    from scripts.build_targets import build_targets

    parser = argparse.ArgumentParser(description="Build platform targets using docker build image.",
                                     formatter_class=argparse.ArgumentDefaultsHelpFormatter)
    parser.add_argument("--tronius_scripts", type=str,
                        help="Location of tronius scripts."
                             "If not defined, environmental variable 'TRONIUS_SCRIPTS', '/opt/tronius/scripts' or '~/tronius/scripts' will be used")
    parser.add_argument("build_type", type=str, default=TARGET_BUILD_TYPE_DEFAULT, choices=TARGET_BUILD_TYPES, help="Target build type.")
    parser.add_argument("ubuntu_versions", type=str, default=[UBUNTU_VERSION_DEFAULT],
                        help="Ubuntu version(s) used as base docker image. If multiple versions are specified, multiple targets will be build."
                             "\nNOTE: Versions can be separated by space, comma or semicolon.")
    parser.add_argument("build_image_tag", type=str, default=DEFAULT_BUILD_IMAGE_TAG, help="Docker image tag used for build.")
    parser.add_argument("force_build", type=str, nargs="?", default="false",
                        help="Force build even if no changes detected in source directory.")
    parser.add_argument("upload", type=str, nargs="?", default="false", help="Upload built targets' artifacts to S3.")
    parser.add_argument("replace", type=str, help="Replace already existing version in depot.")
    parser.add_argument("targets", type=str, nargs="+",
                        help="Targets to build"
                             "\nNOTE: Targets can be separated by space, comma or semicolon.")

    args = parser.parse_args()
    check_tronius_scripts_dir(args.tronius_scripts)
    build_type = args.build_type
    build_image_tag = args.build_image_tag
    force_build = args.force_build.lower() == "true"
    upload = args.upload.lower() == "true"
    replace = args.replace.lower() == "true"
    from tronius_utils_lib import check_multiple_parameter
    ubuntu_versions = check_multiple_parameter(args.ubuntu_versions)
    targets = check_multiple_parameter(args.targets)
    force_build = force_build or (build_type == "release")

    build_targets(targets,
                  ubuntu_versions=ubuntu_versions,
                  build_image_tag=build_image_tag,
                  build_type=build_type,
                  build_target_image=True,
                  push_to_registry=True,
                  add_latest=True,
                  force_build=force_build,
                  upload=upload,
                  replace=replace)


if __name__ == "__main__":
    main()
