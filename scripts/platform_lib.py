import logging
import os
import sys
from typing import Union

SCRIPTS_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(SCRIPTS_DIR)
sys.path.append(ROOT_DIR)

from scripts.platform_constants import DEFAULT_NETWORK_NAME


def init_logging():
    logging.basicConfig(format="%(asctime)s [%(levelname)s] %(message)s", level=logging.DEBUG)


def check_multiple_parameter(values: Union[str, list] = None):
    validated_values = []
    if values:
        if isinstance(values, str):
            if values:
                values = ",".join(values.split())
                values = ",".join(values.split(";"))
                validated_values.extend(set(values.split(",")))
        elif isinstance(values, list):
            for value in values:
                validated_values.extend(check_multiple_parameter(value))

    # noinspection PyTypeChecker
    return sorted(set(validated_values), key=str.casefold)


def check_tronius_scripts(tronius_scripts: str = None):
    if not tronius_scripts:
        tronius_scripts = os.environ.get("TRONIUS_SCRIPTS", "/opt/tronius/scripts")
    if not tronius_scripts:
        tronius_scripts = "~/tronius/scripts"
    if not tronius_scripts:
        raise ValueError("Tronius scripts location is not defined")
    if not os.path.isdir(tronius_scripts):
        raise FileNotFoundError("Scripts directory '%s' does not exist." % tronius_scripts)
    else:
        tronius_scripts = os.path.abspath(tronius_scripts)

    print("Tronius scripts location: %s" % tronius_scripts, flush=True)

    return tronius_scripts


def log_command(command: str | list[str]):
    if not isinstance(command, str):
        command = " ".join(command)
    logging.debug(f"Running command: {command}")


def run_command(commands: str or [str], cwd: str = None, env=None):
    import subprocess

    if isinstance(commands, str):
        command = commands
        shell = True
    elif isinstance(commands, list):
        command = " ".join(commands)
        shell = False
    else:
        raise ValueError("Invalid commands type")

    logging.debug(f"Executing command: {command}")
    subprocess.run(commands, shell=shell, check=True, cwd=cwd, env=env)


def check_docker_network(network_name: str = DEFAULT_NETWORK_NAME):
    """Check if docker network exists, otherwise create it."""
    import subprocess

    command = ["docker", "network", "ls", "-f", "name=^%s$" % network_name, "--format", "'{{.Name}}'"]
    output = subprocess.check_output(command).decode("utf-8").strip().strip("'")
    if output != network_name:
        command = ["docker", "network", "create", network_name]
        subprocess.run(command, check=True)


def get_targets_configuration() -> dict:
    """
    Get targets configuration from targets.json configuration file.
    :return: targets configuration as JSON dictionary
    :rtype: dict
    """

    import json
    from scripts.platform_constants import TARGET_CONFIGURATION_FILENAME

    with open(os.path.join(ROOT_DIR, TARGET_CONFIGURATION_FILENAME), "r") as file:
        return json.load(file)


def get_all_artifacts() -> [str]:
    """
    Get all artifacts from targets.json configuration file.
    :return: sorted list of all artifacts
    :rtype: [str]
    """

    return sorted(list(get_targets_configuration().keys()))


def get_version_file(artifact: str) -> str:
    """
    Get path of version file for specified target.
    Error is raised if version file does not exist.
    :param artifact:
    :return: path of version file
    :rtype: str
    """

    if not artifact:
        raise ValueError("Artifact is not specified")

    configuration = get_targets_configuration()
    if artifact in configuration:
        version_file = os.path.join(ROOT_DIR, configuration[artifact]["versionFile"])
        if os.path.isfile(version_file):
            return str(version_file)
        else:
            raise FileNotFoundError("Version file for artifact '%s' does not exists: %s" % (artifact, version_file))
    else:
        raise ValueError("Definition of version file for artifact '%s' not found" % artifact)


def get_all_targets() -> [str]:
    """
    Get targets specified in version.json files as "uid".
    :return: sorted targets
    :rtype: [str]
    """

    return list(get_targets_with_artifacts().keys())


def get_artifacts(targets: [str] = None) -> [str]:
    """
    Get artifacts for specified targets.
    :return: sorted artifacts
    :rtype: [str]
    """

    artifacts = []

    targets_with_artifacts = get_targets_with_artifacts(targets)
    for target_with_artifacts in targets_with_artifacts:
        artifacts += targets_with_artifacts[target_with_artifacts]

    return sorted(artifacts)


def get_targets_with_artifacts(targets: [str] = None) -> dict:
    """
    Get targets with corresponding artifacts for specified targets.
    :return: sorted targets with corresponding artifacts
    :param targets: list of targets
    :rtype: dict
    """
    import json

    targets_with_artifacts = {}
    configuration = get_targets_configuration()
    for artifact in configuration:
        version_file = os.path.join(ROOT_DIR, configuration[artifact]["versionFile"])
        with open(version_file, "r") as file:
            target = json.load(file).get("uid")
        if targets and target not in targets:
            continue
        if target in targets_with_artifacts:
            artifacts = targets_with_artifacts[target]
        else:
            artifacts = []
        artifacts.append(artifact)
        targets_with_artifacts[target] = artifacts

    return dict(sorted(targets_with_artifacts.items()))


def get_required_libraries(target_module: str, target_path: str) -> list[str]:
    """
    Get required libraries for target server.
    TODO: Using grep for now but we had to find more proper solution
    :param target_module: module name of the target
    :param target_path: path to target executable
    :return: list of required libraries
    """
    import subprocess

    target_base_image = os.path.basename(target_path)
    print(f"Checking required libraries for '{target_module}/{target_base_image}' ...", flush=True)
    command = "grep '^%s/%s: ' '%s/CMakeFiles/%s.dir/build.make' | grep '.so$' | grep -v '/usr' | awk -F ': ' '{print $2}'" % (
        target_module, target_base_image, os.path.dirname(target_path), target_base_image)
    log_command(command)
    libraries = subprocess.check_output(command, shell=True).strip().decode("utf-8")
    if libraries:
        return libraries.split("\n")
    else:
        return []


def prepare_files_for_docker(target_module: str, target_path: str, docker_files_dir: str, build_dir: str):
    import shutil

    libraries = get_required_libraries(target_module, target_path)

    app_dir = docker_files_dir + "/app"
    libs_dir = docker_files_dir + "/libs"
    if not os.path.exists(app_dir):
        os.makedirs(app_dir)
    if not os.path.exists(libs_dir):
        os.makedirs(libs_dir)
    print("Preparing files for docker ...", flush=True)
    print("  Copying target executable '%s' ..." % target_path, flush=True)
    shutil.copy(target_path, app_dir)
    for library in libraries:
        library_path = os.path.join(build_dir, library)
        if os.path.isfile(library_path):
            print("  Copying library '%s' ..." % library_path, flush=True)
            shutil.copy(library_path, libs_dir)
        else:
            raise FileNotFoundError("Library not found: %s" % library_path)


def build_target_docker_image(target: str,
                              target_module: str,
                              dockerfile_path: str,
                              docker_image_name: str,
                              version: str,
                              build_dir: str,
                              ubuntu_version: str,
                              tronius_scripts: str,
                              base_image_tag: str = "latest",
                              add_latest: bool = True,
                              push_to_registry: bool = True,
                              skip_default_labels: bool = False):
    import tempfile
    from scripts.platform_constants import DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME

    build_dir = os.path.abspath(build_dir)

    target_path = os.path.join(build_dir, target_module, target)
    if not os.path.isfile(target_path):
        raise FileNotFoundError("Target executable '%s' not found" % target_path)
    logging.debug("Target executable path: %s" % build_dir)

    with tempfile.TemporaryDirectory() as docker_files_dir:
        prepare_files_for_docker("evolution", target_path, docker_files_dir, build_dir)
        commands = [
            sys.executable,
            os.path.join(tronius_scripts, "docker", "docker-image-build.py"),
            "-t", "%s:%s" % (docker_image_name, version),
            "--build-arg", "BASE_IMAGE_NAME=%s/%s/%s" % (DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME, ubuntu_version),
            "--build-arg", "BASE_IMAGE_TAG=%s" % base_image_tag,
            "--build-arg", "APP_NAME=%s" % target_module,
            "--build-arg", "UBUNTU_VERSION=%s" % ubuntu_version,
            "-f", dockerfile_path,
            docker_files_dir]
        if add_latest:
            commands.append("-l")
        if push_to_registry:
            commands.append("-p")
        if skip_default_labels:
            commands.append("-s")
        run_command(commands, env=os.environ, cwd=ROOT_DIR)
