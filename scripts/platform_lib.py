import logging
import os
import sys
from typing import Union

__SCRIPTS_DIR = os.path.dirname(os.path.abspath(__file__))
__ROOT_DIR = os.path.dirname(__SCRIPTS_DIR)
sys.path.append(__ROOT_DIR)

from scripts.platform_constants import DEFAULT_NETWORK_NAME


def check_tronius_scripts_dir(tronius_scripts_dir: str = ""):
    if not tronius_scripts_dir:
        tronius_scripts_dir = os.environ.get("TRONIUS_SCRIPTS", "/opt/tronius/scripts")
    if not tronius_scripts_dir:
        tronius_scripts_dir = "~/tronius/scripts"
    if not tronius_scripts_dir:
        raise ValueError("Tronius scripts location is not defined")
    if not os.path.isdir(tronius_scripts_dir):
        raise FileNotFoundError("Scripts directory '%s' does not exist." % tronius_scripts_dir)
    else:
        tronius_scripts_dir = os.path.abspath(tronius_scripts_dir)

    if not tronius_scripts_dir in sys.path:
        sys.path.append(tronius_scripts_dir)
        from tronius_utils_lib import init_logging
        init_logging()

    return tronius_scripts_dir


def log_command(command: Union[str, list[str]]):
    if not isinstance(command, str):
        command = " ".join(command)
    logging.debug(f"Running command: {command}")


def check_docker_network(network_name: str = DEFAULT_NETWORK_NAME):
    """Check if docker network exists, otherwise create it."""
    import subprocess

    command = ["docker", "network", "ls", "-f", "name=^%s$" % network_name, "--format", "'{{.Name}}'"]
    output = subprocess.check_output(command).decode("utf-8").strip().strip("'")
    if output != network_name:
        command = ["docker", "network", "create", network_name]
        subprocess.run(command, check=True)


def get_targets_configuration() -> dict:
    """
    Get targets configuration from targets.json configuration file.
    :return: targets configuration as JSON dictionary
    :rtype: dict
    """

    import json
    from scripts.platform_constants import TARGET_CONFIGURATION_FILENAME

    with open(os.path.join(__ROOT_DIR, TARGET_CONFIGURATION_FILENAME), "r") as file:
        configuration = json.load(file)
    return dict(sorted(configuration.items()))


def get_all_targets() -> list[str]:
    """
    Get targets specified in version.json files as "uid".
    :return: sorted targets
    :rtype: [str]
    """

    return list(get_targets_configuration().keys())


def get_targets_with_artifacts(targets: list[str] = None) -> dict:
    """
    Get targets with corresponding artifacts for specified targets.
    :return: sorted targets with corresponding artifacts
    :param targets: list of targets
    :rtype: dict
    """

    if not targets:
        targets = get_all_targets()

    targets_with_artifacts = {}
    configuration = get_targets_configuration()
    for target in targets:
        if "artifacts" in configuration[target]:
            targets_with_artifacts[target] = configuration[target]["artifacts"]
        else:
            targets_with_artifacts[target] = [target]

    return dict(sorted(targets_with_artifacts.items()))


def get_artifacts(targets: list[str] = None) -> list[str]:
    """
    Get artifacts for specified targets.
    :return: sorted artifacts
    :rtype: [str]
    """

    artifacts = []

    targets_with_artifacts = get_targets_with_artifacts(targets)
    for target_with_artifacts in targets_with_artifacts:
        artifacts += targets_with_artifacts[target_with_artifacts]

    return sorted(artifacts)


def get_all_artifacts() -> list[str]:
    """
    Get all artifacts from targets.json configuration file.
    :return: sorted list of all artifacts
    :rtype: [str]
    """

    return get_artifacts()


def get_required_libraries(target_module: str, target_path: str) -> list[str]:
    """
    Get required libraries for target server.
    TODO: Using grep for now but we had to find more proper solution
    :param target_module: module name of the target
    :param target_path: path to target executable
    :return: list of required libraries
    """
    import subprocess

    target_base_image = os.path.basename(target_path)
    print(f"Checking required libraries for '{target_module}/{target_base_image}' ...", flush=True)
    command = "grep '^%s/%s: ' '%s/CMakeFiles/%s.dir/build.make' | grep '.so$' | grep -v '/usr' | awk -F ': ' '{print $2}'" % (
        target_module, target_base_image, os.path.dirname(target_path), target_base_image)
    log_command(command)
    libraries = subprocess.check_output(command, shell=True).strip().decode("utf-8")
    if libraries:
        return libraries.split("\n")
    else:
        return []


def prepare_files_for_docker(target_module: str, target_path: str, docker_files_dir: str, build_dir: str):
    import shutil

    libraries = get_required_libraries(target_module, target_path)

    app_dir = docker_files_dir + "/app"
    libs_dir = docker_files_dir + "/libs"
    if not os.path.exists(app_dir):
        os.makedirs(app_dir)
    if not os.path.exists(libs_dir):
        os.makedirs(libs_dir)
    print("Preparing files for docker ...", flush=True)
    print("  Copying target executable '%s' ..." % target_path, flush=True)
    shutil.copy(target_path, app_dir)
    for library in libraries:
        library_path = os.path.join(build_dir, library)
        if os.path.isfile(library_path):
            print("  Copying library '%s' ..." % library_path, flush=True)
            shutil.copy(library_path, libs_dir)
        else:
            raise FileNotFoundError("Library not found: %s" % library_path)


def build_target_docker_image(target: str,
                              target_module: str,
                              dockerfile_path: str,
                              docker_image_name: str,
                              version: str,
                              build_dir: str,
                              ubuntu_version: str,
                              tronius_scripts: str,
                              base_image_tag: str = "latest",
                              add_latest: bool = True,
                              push_to_registry: bool = True,
                              skip_default_labels: bool = False):
    import tempfile
    from scripts.platform_constants import DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME
    from tronius_utils_lib import run_command

    build_dir = os.path.abspath(build_dir)

    target_path = os.path.join(build_dir, target_module, target)
    if not os.path.isfile(target_path):
        raise FileNotFoundError("Target executable '%s' not found" % target_path)
    logging.debug("Target executable path: %s" % build_dir)

    with tempfile.TemporaryDirectory() as docker_files_dir:
        prepare_files_for_docker("evolution", target_path, docker_files_dir, build_dir)
        commands = [
            sys.executable,
            os.path.join(tronius_scripts, "docker", "docker-image-build.py"),
            "-t", "%s:%s" % (docker_image_name, version),
            "--build-arg", "BASE_IMAGE_NAME=%s/%s/%s" % (DOCKER_REGISTRY, TARGET_BUILD_IMAGE_NAME, ubuntu_version),
            "--build-arg", "BASE_IMAGE_TAG=%s" % base_image_tag,
            "--build-arg", "APP_NAME=%s" % target_module,
            "--build-arg", "UBUNTU_VERSION=%s" % ubuntu_version,
            "-f", dockerfile_path,
            docker_files_dir]
        if add_latest:
            commands.append("-l")
        if push_to_registry:
            commands.append("-p")
        if skip_default_labels:
            commands.append("-s")
        run_command(commands, env=os.environ, cwd=__ROOT_DIR)


def get_device_software_artifacts() -> list[str]:
    import subprocess

    command = "cat CMakeLists.txt | grep 'add_custom_target(device-software'"
    log_command(command)
    artifacts = subprocess.check_output("%s" % command, shell=True, cwd=__ROOT_DIR).strip().decode("utf-8")
    artifacts = artifacts.split("DEPENDS")[1].replace(")", "").split()

    return artifacts


def upload_artifacts(target: str, version: str, platform: str, ubuntu_version: str, build_dir: str):
    import glob
    from artifacts.artifacts_lib import upload_artifact
    from scripts.platform_constants import TARGET_DEVICE_SOFTWARE

    if target == TARGET_DEVICE_SOFTWARE:
        artifacts = get_device_software_artifacts()
    else:
        artifacts = get_artifacts([target])
    for artifact in artifacts:
        artifact_file = glob.glob(os.path.join(build_dir, "*", artifact), recursive=True)
        if artifact_file:
            artifact_file = artifact_file[0]
            logging.info(f"Uploading artifact '{artifact_file}:{version}' to S3 ...")
            upload_artifact(artifact_file, version, artifact, additional_path=os.path.join(platform, ubuntu_version))
