#!/usr/bin/env python3

import os
import sys

__MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
__SCRIPTS_DIR = os.path.dirname(__MODULE_DIR)
__ROOT_DIR = os.path.dirname(__SCRIPTS_DIR)
sys.path.append(__ROOT_DIR)

from scripts.platform_constants import DEFAULT_BUILD_IMAGE_TAG, UBUNTU_VERSION_DEFAULT


def get_required_libraries(jackpot_server_path) -> list[str]:
    """
    Get required libraries for jackpot server.
    TODO: Using grep for now but we had to find more proper solution
    :param jackpot_server_path:
    :return:
    """
    import subprocess

    print("Checking required libraries ...", flush=True)
    libraries = subprocess.check_output(
        "grep '^jackpot/jackpot-server: ' '%s/CMakeFiles/jackpot-server.dir/build.make' | grep '.so$' | grep -v '/usr' | awk -F ': ' '{print $2}'"
        % os.path.dirname(jackpot_server_path), shell=True).strip().decode("utf-8")
    if libraries:
        return libraries.split("\n")
    else:
        return []


def prepare_files_for_docker(docker_files_dir: str, build_dir: str):
    import shutil
    from scripts.platform_constants import TARGET_JACKPOT_SERVER

    jackpot_server_path = os.path.join(build_dir, "jackpot", TARGET_JACKPOT_SERVER)
    if not os.path.isfile(jackpot_server_path):
        raise FileNotFoundError("Jackpot server executable '%s' not found" % jackpot_server_path)
    print("Jackpot server path: %s" % jackpot_server_path, flush=True)
    libraries = get_required_libraries(jackpot_server_path)

    app_dir = docker_files_dir + "/app"
    libs_dir = docker_files_dir + "/libs"
    tools_dir = docker_files_dir + "/tools"
    if not os.path.exists(app_dir):
        os.makedirs(app_dir)
    if not os.path.exists(libs_dir):
        os.makedirs(libs_dir)
    print("Preparing files for docker ...", flush=True)
    shutil.copy(os.path.join(__ROOT_DIR, "manifest.json"), __MODULE_DIR)
    print("  Copying jackpot server executable '%s' ..." % jackpot_server_path, flush=True)
    shutil.copy(jackpot_server_path, app_dir)
    for library in libraries:
        library_path = os.path.join(build_dir, library)
        if os.path.isfile(library_path):
            print("  Copying library '%s' ..." % library_path, flush=True)
            shutil.copy(library_path, libs_dir)
        else:
            raise FileNotFoundError("Library not found: %s" % library_path)
    print("  Copying tools ...", flush=True)
    shutil.copytree(os.path.join(__MODULE_DIR, "docker_tools"), tools_dir)


def build(build_dir,
          base_image_tag: str = DEFAULT_BUILD_IMAGE_TAG,
          ubuntu_version: str = UBUNTU_VERSION_DEFAULT,
          push_to_registry: bool = False,
          add_latest: bool = False,
          skip_default_labels: bool = False,
          tronius_scripts: str = ""):
    import tempfile
    from scripts.platform_lib import check_tronius_scripts_dir
    from scripts.platform_constants import TARGET_BUILD_IMAGE_NAME, DOCKER_REGISTRY
    from scripts.jackpot.jackpot_constants import DOCKER_IMAGE_NAME, DOCKER_WORKING_DIRECTORY

    if not build_dir:
        raise ValueError("Build directory is not specified")
    if not os.path.isdir(build_dir):
        raise FileNotFoundError("Build directory '%s' not found" % build_dir)

    tronius_scripts = check_tronius_scripts_dir(tronius_scripts)

    build_dir = os.path.abspath(build_dir)
    print("Build directory: %s" % build_dir, flush=True)
    from versioning.versioning_lib import get_version
    version = get_version(__ROOT_DIR)
    with tempfile.TemporaryDirectory() as docker_files_dir:
        prepare_files_for_docker(docker_files_dir, build_dir)
        command = [
            sys.executable,
            os.path.join(tronius_scripts, "docker", "docker-image-build.py"),
            "-t", f"{DOCKER_IMAGE_NAME}:{version}",
            "--build-arg", f"BASE_IMAGE_NAME={DOCKER_REGISTRY}/{TARGET_BUILD_IMAGE_NAME}/{ubuntu_version}",
            "--build-arg", f"BASE_IMAGE_TAG={base_image_tag}",
            "--build-arg", f"WORKING_DIRECTORY={DOCKER_WORKING_DIRECTORY}",
            "--build-arg", f"UBUNTU_VERSION={ubuntu_version}",
            "-f", os.path.join(__MODULE_DIR, "Dockerfile"),
            docker_files_dir]
        if add_latest:
            command.append("-l")
        if push_to_registry:
            command.append("-p")
        if skip_default_labels:
            command.append("-s")
        from tronius_utils_lib import run_command
        run_command(command, cwd=__MODULE_DIR, env=os.environ)


def main():
    import argparse
    from scripts.platform_lib import check_tronius_scripts_dir
    from scripts.platform_constants import UBUNTU_VERSIONS, UBUNTU_VERSION_DEFAULT, BUILD_OUTPUT_DIRECTORY, DEFAULT_BUILD_IMAGE_TAG

    parser = argparse.ArgumentParser(
        description="Build jackpot server docker image, add labels and push it to the registry if specified.",
        formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("build_dir", type=str, nargs="?", default=BUILD_OUTPUT_DIRECTORY,
                        help="Build output directory.")
    parser.add_argument("-p", "--push_to_registry", default=False, action="store_true", help="Push image to our registry")
    parser.add_argument("-l", "--add_latest", default=False, action="store_true", help="Add latest tag to the image")
    parser.add_argument("-s", "--skip_default_labels", default=False, action="store_true",
                        help="Add labels related to git (commit id, branch name) and Jenkins build (build url, build timestamp)")
    parser.add_argument("--ubuntu_version", type=str, choices=UBUNTU_VERSIONS, default=UBUNTU_VERSION_DEFAULT,
                        help="Ubuntu version used as base docker image.")
    parser.add_argument("--base_image_tag", type=str, default=DEFAULT_BUILD_IMAGE_TAG,
                        help="Version of 'tronius/platform/build' base image.")
    parser.add_argument("--tronius_scripts", type=str,
                        help="Location of tronius scripts."
                             "If not defined, environmental variable 'TRONIUS_SCRIPTS', '/opt/tronius/scripts' or '~/tronius/scripts' will be used")

    args = parser.parse_args()
    build_dir = args.build_dir
    push_to_registry = args.push_to_registry
    add_latest = args.add_latest
    skip_default_labels = args.skip_default_labels
    tronius_scripts = check_tronius_scripts_dir(args.tronius_scripts)
    ubuntu_version = args.ubuntu_version
    base_image_tag = args.base_image_tag

    build(build_dir, base_image_tag, ubuntu_version, push_to_registry, add_latest, skip_default_labels, tronius_scripts)


if __name__ == "__main__":
    main()
