import os


__SCRIPTS = os.path.dirname(os.path.abspath(__file__))
__ROOT_DIR = os.path.dirname(__SCRIPTS)

DOCKER_REGISTRY = "docker-registry.internal.troniusgaming.com"

DEFAULT_NETWORK_NAME = "tronius"
UBUNTU_VERSIONS = ["18.04", "22.04", "24.04"]
UBUNTU_VERSION_DEFAULT = "24.04"

BUILD_OUTPUT_DIRECTORY_NAME = "build"
BUILD_OUTPUT_DIRECTORY = os.path.join(__ROOT_DIR, BUILD_OUTPUT_DIRECTORY_NAME)
TARGET_BUILD_TYPES = ["Debug", "Release", "RelWithDebInfo"]
TARGET_BUILD_TYPE_DEFAULT = "RelWithDebInfo"
TARGET_BUILD_IMAGE_NAME = "tronius/platform/build/x86"
DEFAULT_BUILD_IMAGE_TAG = "latest"

TARGET_CONFIGURATION_FILENAME = os.path.join(__ROOT_DIR, "targets.json")
EVOLUTION_TARGET_SERVER = "evolution-server"
EVOLUTION_DOCKER_IMAGE_NAME = "tronius/platform/evolution-server"
TARGET_JACKPOT_SERVER = "jackpot-server"
TARGET_DEVICE_SOFTWARE = "device-software"
JACKPOT_BUILT_FILE = os.path.join(__ROOT_DIR, ".jackpot")

LIBRARIES_PACKS_FILENAME =  os.path.join(__ROOT_DIR, "libs_packs.json")

TARGET_ARCHITECTURES = ["x86", "arm"]
TARGET_ARCHITECTURES_DEFAULT = TARGET_ARCHITECTURES

ANSI_FORMAT_PREFIX = "\033"


class AnsiCodes:
    END = "%s[0m" % ANSI_FORMAT_PREFIX
    RED = "%s[91m" % ANSI_FORMAT_PREFIX
    GRAY = "%s[90m" % ANSI_FORMAT_PREFIX
    LIGHT_GRAY = "%s[37m" % ANSI_FORMAT_PREFIX
    HEADER = "%s[95m" % ANSI_FORMAT_PREFIX
    OK = "%s[92m" % ANSI_FORMAT_PREFIX
    WARNING = "%s[93m" % ANSI_FORMAT_PREFIX
    ERROR = RED
    DEBUG = LIGHT_GRAY

    OKBLUE = "%s[94m" % ANSI_FORMAT_PREFIX
    OKCYAN = "%s[96m" % ANSI_FORMAT_PREFIX
    BOLD = "%s[1m" % ANSI_FORMAT_PREFIX
    UNDERLINE = "%s[4m" % ANSI_FORMAT_PREFIX
