#!/usr/bin/python3
import logging
import os
import shutil
import sys

__SCRIPT_NAME = os.path.basename(__file__)
__MODULE_DIR = os.path.dirname(os.path.abspath(__file__))
sys.path.append(__MODULE_DIR)


def main():
    import argparse
    import subprocess
    import paramiko
    from evolution_deploy_constants import DEPLOY_SERVER_ADDRESS, DEPLOY_SERVER_USERNAME, DEPLOY_SERVER_DIRECTORY, DATA_DIRECTORY
    from evolution_deploy_lib import init_logging, save_start_parameter, get_docker_image_name, run_command, execute_remote_command

    init_logging()

    parser = argparse.ArgumentParser(description="Deploy Evolution Server to instance.",
                                     formatter_class=argparse.RawTextHelpFormatter)
    parser.add_argument("deploy_env", type=str, help="Deploy environment.")
    parser.add_argument("version", type=str, help="Docker with Evolution server image version")

    args = parser.parse_args()
    deploy_env = args.deploy_env
    version = args.version

    env_dir = str(os.path.join(__MODULE_DIR, "env", deploy_env))
    if not os.path.isdir(env_dir):
        raise FileNotFoundError(f"Environment directory does not exists: {env_dir}")

    docker_image = get_docker_image_name(version)
    if subprocess.call(f"docker pull -q {docker_image}", stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL, shell=True) != 0:
        raise FileNotFoundError(f"Docker image does not exists: {docker_image}")

    logging.info("Preparing env files ...")
    if os.path.isdir(DATA_DIRECTORY):
        shutil.rmtree(DATA_DIRECTORY)
    shutil.copytree(env_dir, DATA_DIRECTORY, dirs_exist_ok=True)

    logging.debug("Updating start parameters ...")
    save_start_parameter("version", version)
    save_start_parameter("env", deploy_env)

    deploy_dir = os.path.join(DEPLOY_SERVER_DIRECTORY, deploy_env)
    rsync_excluded_files = os.path.join(__MODULE_DIR, "rsync_excluded")
    with paramiko.SSHClient() as ssh_connection:
        ssh_connection.set_missing_host_key_policy(paramiko.AutoAddPolicy())
        ssh_connection.connect(hostname=DEPLOY_SERVER_ADDRESS, username=DEPLOY_SERVER_USERNAME)
        logging.debug("Updating files on deploy server ...")
        execute_remote_command(
            ssh_connection, f"sudo mkdir -p {deploy_dir} && sudo chown -R {DEPLOY_SERVER_USERNAME}:{DEPLOY_SERVER_USERNAME} {deploy_dir}")
        run_command(
            f"rsync -ahzP --delete --exclude-from {rsync_excluded_files} {__MODULE_DIR}/ {DEPLOY_SERVER_USERNAME}@{DEPLOY_SERVER_ADDRESS}:{deploy_dir}/")
        logging.info("Starting Evolution Server ...")
        execute_remote_command(ssh_connection, f"{deploy_dir}/start.py")

    logging.info(f"Evolution Server successfully deploy and started in '{deploy_env}' environment ...")


if __name__ == "__main__":
    main()
