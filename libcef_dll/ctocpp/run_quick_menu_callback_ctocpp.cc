// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=4a8460c67b3fbb6f13157151ddd28e4e304ffac7$
//

#include "libcef_dll/ctocpp/run_quick_menu_callback_ctocpp.h"

#include "libcef_dll/shutdown_checker.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
void CefRunQuickMenuCallbackCToCpp::Continue(int command_id,
                                             cef_event_flags_t event_flags) {
  shutdown_checker::AssertNotShutdown();

  cef_run_quick_menu_callback_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, cont)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->cont(_struct, command_id, event_flags);
}

NO_SANITIZE("cfi-icall") void CefRunQuickMenuCallbackCToCpp::Cancel() {
  shutdown_checker::AssertNotShutdown();

  cef_run_quick_menu_callback_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, cancel)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->cancel(_struct);
}

// CONSTRUCTOR - Do not edit by hand.

CefRunQuickMenuCallbackCToCpp::CefRunQuickMenuCallbackCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefRunQuickMenuCallbackCToCpp::~CefRunQuickMenuCallbackCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_run_quick_menu_callback_t* CefCToCppRefCounted<
    CefRunQuickMenuCallbackCToCpp,
    CefRunQuickMenuCallback,
    cef_run_quick_menu_callback_t>::UnwrapDerived(CefWrapperType type,
                                                  CefRunQuickMenuCallback* c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType
    CefCToCppRefCounted<CefRunQuickMenuCallbackCToCpp,
                        CefRunQuickMenuCallback,
                        cef_run_quick_menu_callback_t>::kWrapperType =
        WT_RUN_QUICK_MENU_CALLBACK;
