// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=4c04a490cc0609560e711890eeb286431675bf54$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_UNRESPONSIVE_PROCESS_CALLBACK_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_UNRESPONSIVE_PROCESS_CALLBACK_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_unresponsive_process_callback_capi.h"
#include "include/cef_unresponsive_process_callback.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefUnresponsiveProcessCallbackCToCpp
    : public CefCToCppRefCounted<CefUnresponsiveProcessCallbackCToCpp,
                                 CefUnresponsiveProcessCallback,
                                 cef_unresponsive_process_callback_t> {
 public:
  CefUnresponsiveProcessCallbackCToCpp();
  virtual ~CefUnresponsiveProcessCallbackCToCpp();

  // CefUnresponsiveProcessCallback methods.
  void Wait() override;
  void Terminate() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_UNRESPONSIVE_PROCESS_CALLBACK_CTOCPP_H_
