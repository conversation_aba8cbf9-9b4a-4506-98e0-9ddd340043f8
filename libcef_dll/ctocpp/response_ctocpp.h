// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=c2fed0bd3755750ab3fcc8a55c5743b70e611994$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_RESPONSE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_RESPONSE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_response_capi.h"
#include "include/cef_response.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefResponseCToCpp : public CefCToCppRefCounted<CefResponseCToCpp,
                                                     CefResponse,
                                                     cef_response_t> {
 public:
  CefResponseCToCpp();
  virtual ~CefResponseCToCpp();

  // CefResponse methods.
  bool IsReadOnly() override;
  cef_errorcode_t GetError() override;
  void SetError(cef_errorcode_t error) override;
  int GetStatus() override;
  void SetStatus(int status) override;
  CefString GetStatusText() override;
  void SetStatusText(const CefString& statusText) override;
  CefString GetMimeType() override;
  void SetMimeType(const CefString& mimeType) override;
  CefString GetCharset() override;
  void SetCharset(const CefString& charset) override;
  CefString GetHeaderByName(const CefString& name) override;
  void SetHeaderByName(const CefString& name,
                       const CefString& value,
                       bool overwrite) override;
  void GetHeaderMap(HeaderMap& headerMap) override;
  void SetHeaderMap(const HeaderMap& headerMap) override;
  CefString GetURL() override;
  void SetURL(const CefString& url) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_RESPONSE_CTOCPP_H_
