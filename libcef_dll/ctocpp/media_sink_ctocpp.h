// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=7d6f7d025a45cf4b2dcecd5f0e0ad7dff5226228$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_MEDIA_SINK_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_MEDIA_SINK_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_media_router_capi.h"
#include "include/cef_media_router.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefMediaSinkCToCpp : public CefCToCppRefCounted<CefMediaSinkCToCpp,
                                                      CefMediaSink,
                                                      cef_media_sink_t> {
 public:
  CefMediaSinkCToCpp();
  virtual ~CefMediaSinkCToCpp();

  // CefMediaSink methods.
  CefString GetId() override;
  CefString GetName() override;
  IconType GetIconType() override;
  void GetDeviceInfo(
      CefRefPtr<CefMediaSinkDeviceInfoCallback> callback) override;
  bool IsCastSink() override;
  bool IsDialSink() override;
  bool IsCompatibleWith(CefRefPtr<CefMediaSource> source) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_MEDIA_SINK_CTOCPP_H_
