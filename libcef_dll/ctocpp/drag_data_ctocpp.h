// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=957ab071d3daebd07fc9458ea5b5647c7c721ed3$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_DRAG_DATA_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_DRAG_DATA_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include <vector>

#include "include/capi/cef_drag_data_capi.h"
#include "include/cef_drag_data.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefDragDataCToCpp : public CefCToCppRefCounted<CefDragDataCToCpp,
                                                     CefDragData,
                                                     cef_drag_data_t> {
 public:
  CefDragDataCToCpp();
  virtual ~CefDragDataCToCpp();

  // CefDragData methods.
  CefRefPtr<CefDragData> Clone() override;
  bool IsReadOnly() override;
  bool IsLink() override;
  bool IsFragment() override;
  bool IsFile() override;
  CefString GetLinkURL() override;
  CefString GetLinkTitle() override;
  CefString GetLinkMetadata() override;
  CefString GetFragmentText() override;
  CefString GetFragmentHtml() override;
  CefString GetFragmentBaseURL() override;
  CefString GetFileName() override;
  size_t GetFileContents(CefRefPtr<CefStreamWriter> writer) override;
  bool GetFileNames(std::vector<CefString>& names) override;
  bool GetFilePaths(std::vector<CefString>& paths) override;
  void SetLinkURL(const CefString& url) override;
  void SetLinkTitle(const CefString& title) override;
  void SetLinkMetadata(const CefString& data) override;
  void SetFragmentText(const CefString& text) override;
  void SetFragmentHtml(const CefString& html) override;
  void SetFragmentBaseURL(const CefString& base_url) override;
  void ResetFileContents() override;
  void AddFile(const CefString& path, const CefString& display_name) override;
  void ClearFilenames() override;
  CefRefPtr<CefImage> GetImage() override;
  CefPoint GetImageHotspot() override;
  bool HasImage() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_DRAG_DATA_CTOCPP_H_
