// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=9640cfd33eee4b9298e2c2c9a34c6e3b4dbf7070$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_COMMAND_LINE_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_COMMAND_LINE_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include <vector>

#include "include/capi/cef_command_line_capi.h"
#include "include/cef_command_line.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefCommandLineCToCpp : public CefCToCppRefCounted<CefCommandLineCToCpp,
                                                        CefCommandLine,
                                                        cef_command_line_t> {
 public:
  CefCommandLineCToCpp();
  virtual ~CefCommandLineCToCpp();

  // CefCommandLine methods.
  bool IsValid() override;
  bool IsReadOnly() override;
  CefRefPtr<CefCommandLine> Copy() override;
  void InitFromArgv(int argc, const char* const* argv) override;
  void InitFromString(const CefString& command_line) override;
  void Reset() override;
  void GetArgv(std::vector<CefString>& argv) override;
  CefString GetCommandLineString() override;
  CefString GetProgram() override;
  void SetProgram(const CefString& program) override;
  bool HasSwitches() override;
  bool HasSwitch(const CefString& name) override;
  CefString GetSwitchValue(const CefString& name) override;
  void GetSwitches(SwitchMap& switches) override;
  void AppendSwitch(const CefString& name) override;
  void AppendSwitchWithValue(const CefString& name,
                             const CefString& value) override;
  bool HasArguments() override;
  void GetArguments(ArgumentList& arguments) override;
  void AppendArgument(const CefString& argument) override;
  void PrependWrapper(const CefString& wrapper) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_COMMAND_LINE_CTOCPP_H_
