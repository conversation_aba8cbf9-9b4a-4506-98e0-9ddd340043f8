// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=5838ce3611dda9523ff072eceb36137663828dd6$
//

#include "libcef_dll/ctocpp/task_manager_ctocpp.h"

#include <algorithm>

#include "libcef_dll/shutdown_checker.h"

// STATIC METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
CefRefPtr<CefTaskManager> CefTaskManager::GetTaskManager() {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_task_manager_t* _retval = cef_task_manager_get();

  // Return type: refptr_same
  return CefTaskManagerCToCpp::Wrap(_retval);
}

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall") size_t CefTaskManagerCToCpp::GetTasksCount() {
  shutdown_checker::AssertNotShutdown();

  cef_task_manager_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_tasks_count)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  size_t _retval = _struct->get_tasks_count(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
bool CefTaskManagerCToCpp::GetTaskIdsList(TaskIdList& task_ids) {
  shutdown_checker::AssertNotShutdown();

  cef_task_manager_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_task_ids_list)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Translate param: task_ids; type: simple_vec_byref
  size_t task_idsSize = task_ids.size();
  size_t task_idsCount = std::max(GetTasksCount(), task_idsSize);
  int64_t* task_idsList = NULL;
  if (task_idsCount > 0) {
    task_idsList = new int64_t[task_idsCount];
    DCHECK(task_idsList);
    if (task_idsList) {
      memset(task_idsList, 0, sizeof(int64_t) * task_idsCount);
    }
    if (task_idsList && task_idsSize > 0) {
      for (size_t i = 0; i < task_idsSize; ++i) {
        task_idsList[i] = task_ids[i];
      }
    }
  }

  // Execute
  int _retval =
      _struct->get_task_ids_list(_struct, &task_idsCount, task_idsList);

  // Restore param:task_ids; type: simple_vec_byref
  task_ids.clear();
  if (task_idsCount > 0 && task_idsList) {
    for (size_t i = 0; i < task_idsCount; ++i) {
      task_ids.push_back(task_idsList[i]);
    }
    delete[] task_idsList;
  }

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefTaskManagerCToCpp::GetTaskInfo(int64_t task_id, CefTaskInfo& info) {
  shutdown_checker::AssertNotShutdown();

  cef_task_manager_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_task_info)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_task_info(_struct, task_id, &info);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefTaskManagerCToCpp::KillTask(int64_t task_id) {
  shutdown_checker::AssertNotShutdown();

  cef_task_manager_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, kill_task)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->kill_task(_struct, task_id);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
int64_t CefTaskManagerCToCpp::GetTaskIdForBrowserId(int browser_id) {
  shutdown_checker::AssertNotShutdown();

  cef_task_manager_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_task_id_for_browser_id)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int64_t _retval = _struct->get_task_id_for_browser_id(_struct, browser_id);

  // Return type: simple
  return _retval;
}

// CONSTRUCTOR - Do not edit by hand.

CefTaskManagerCToCpp::CefTaskManagerCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefTaskManagerCToCpp::~CefTaskManagerCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_task_manager_t*
CefCToCppRefCounted<CefTaskManagerCToCpp, CefTaskManager, cef_task_manager_t>::
    UnwrapDerived(CefWrapperType type, CefTaskManager* c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefTaskManagerCToCpp,
                                   CefTaskManager,
                                   cef_task_manager_t>::kWrapperType =
    WT_TASK_MANAGER;
