// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=ec12ab437515f3dc8a5d30d5c7de7733dbf99dd1$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_SSLSTATUS_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_SSLSTATUS_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_ssl_status_capi.h"
#include "include/cef_ssl_status.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefSSLStatusCToCpp : public CefCToCppRefCounted<CefSSLStatusCToCpp,
                                                      CefSSLStatus,
                                                      cef_sslstatus_t> {
 public:
  CefSSLStatusCToCpp();
  virtual ~CefSSLStatusCToCpp();

  // CefSSLStatus methods.
  bool IsSecureConnection() override;
  cef_cert_status_t GetCertStatus() override;
  cef_ssl_version_t GetSSLVersion() override;
  cef_ssl_content_status_t GetContentStatus() override;
  CefRefPtr<CefX509Certificate> GetX509Certificate() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_SSLSTATUS_CTOCPP_H_
