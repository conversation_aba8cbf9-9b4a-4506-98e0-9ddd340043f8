// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=0e266b7fd9ead0874b8ffb228194e8767dddd748$
//

#include "libcef_dll/ctocpp/request_context_ctocpp.h"

#include "libcef_dll/cpptoc/completion_callback_cpptoc.h"
#include "libcef_dll/cpptoc/request_context_handler_cpptoc.h"
#include "libcef_dll/cpptoc/resolve_callback_cpptoc.h"
#include "libcef_dll/cpptoc/scheme_handler_factory_cpptoc.h"
#include "libcef_dll/ctocpp/cookie_manager_ctocpp.h"
#include "libcef_dll/ctocpp/dictionary_value_ctocpp.h"
#include "libcef_dll/ctocpp/media_router_ctocpp.h"
#include "libcef_dll/ctocpp/value_ctocpp.h"

// STATIC METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
CefRefPtr<CefRequestContext> CefRequestContext::GetGlobalContext() {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_request_context_t* _retval = cef_request_context_get_global_context();

  // Return type: refptr_same
  return CefRequestContextCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefRequestContext> CefRequestContext::CreateContext(
    const CefRequestContextSettings& settings,
    CefRefPtr<CefRequestContextHandler> handler) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: handler

  // Execute
  cef_request_context_t* _retval = cef_request_context_create_context(
      &settings, CefRequestContextHandlerCppToC::Wrap(handler));

  // Return type: refptr_same
  return CefRequestContextCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefRequestContext> CefRequestContext::CreateContext(
    CefRefPtr<CefRequestContext> other,
    CefRefPtr<CefRequestContextHandler> handler) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: other; type: refptr_same
  DCHECK(other.get());
  if (!other.get()) {
    return nullptr;
  }
  // Unverified params: handler

  // Execute
  cef_request_context_t* _retval =
      cef_create_context_shared(CefRequestContextCToCpp::Unwrap(other),
                                CefRequestContextHandlerCppToC::Wrap(handler));

  // Return type: refptr_same
  return CefRequestContextCToCpp::Wrap(_retval);
}

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::IsSame(CefRefPtr<CefRequestContext> other) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_same)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: other; type: refptr_same
  DCHECK(other.get());
  if (!other.get()) {
    return false;
  }

  // Execute
  int _retval =
      _struct->is_same(_struct, CefRequestContextCToCpp::Unwrap(other));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::IsSharingWith(
    CefRefPtr<CefRequestContext> other) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_sharing_with)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: other; type: refptr_same
  DCHECK(other.get());
  if (!other.get()) {
    return false;
  }

  // Execute
  int _retval =
      _struct->is_sharing_with(_struct, CefRequestContextCToCpp::Unwrap(other));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall") bool CefRequestContextCToCpp::IsGlobal() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, is_global)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->is_global(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefRequestContextHandler> CefRequestContextCToCpp::GetHandler() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_handler)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_request_context_handler_t* _retval = _struct->get_handler(_struct);

  // Return type: refptr_diff
  return CefRequestContextHandlerCppToC::Unwrap(_retval);
}

NO_SANITIZE("cfi-icall") CefString CefRequestContextCToCpp::GetCachePath() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_cache_path)) {
    return CefString();
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_string_userfree_t _retval = _struct->get_cache_path(_struct);

  // Return type: string
  CefString _retvalStr;
  _retvalStr.AttachToUserFree(_retval);
  return _retvalStr;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefCookieManager> CefRequestContextCToCpp::GetCookieManager(
    CefRefPtr<CefCompletionCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_cookie_manager)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: callback

  // Execute
  cef_cookie_manager_t* _retval = _struct->get_cookie_manager(
      _struct, CefCompletionCallbackCppToC::Wrap(callback));

  // Return type: refptr_same
  return CefCookieManagerCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::RegisterSchemeHandlerFactory(
    const CefString& scheme_name,
    const CefString& domain_name,
    CefRefPtr<CefSchemeHandlerFactory> factory) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, register_scheme_handler_factory)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: scheme_name; type: string_byref_const
  DCHECK(!scheme_name.empty());
  if (scheme_name.empty()) {
    return false;
  }
  // Unverified params: domain_name, factory

  // Execute
  int _retval = _struct->register_scheme_handler_factory(
      _struct, scheme_name.GetStruct(), domain_name.GetStruct(),
      CefSchemeHandlerFactoryCppToC::Wrap(factory));

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::ClearSchemeHandlerFactories() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, clear_scheme_handler_factories)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->clear_scheme_handler_factories(_struct);

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::ClearCertificateExceptions(
    CefRefPtr<CefCompletionCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, clear_certificate_exceptions)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: callback

  // Execute
  _struct->clear_certificate_exceptions(
      _struct, CefCompletionCallbackCppToC::Wrap(callback));
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::ClearHttpAuthCredentials(
    CefRefPtr<CefCompletionCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, clear_http_auth_credentials)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: callback

  // Execute
  _struct->clear_http_auth_credentials(
      _struct, CefCompletionCallbackCppToC::Wrap(callback));
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::CloseAllConnections(
    CefRefPtr<CefCompletionCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, close_all_connections)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: callback

  // Execute
  _struct->close_all_connections(_struct,
                                 CefCompletionCallbackCppToC::Wrap(callback));
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::ResolveHost(
    const CefString& origin,
    CefRefPtr<CefResolveCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, resolve_host)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: origin; type: string_byref_const
  DCHECK(!origin.empty());
  if (origin.empty()) {
    return;
  }
  // Verify param: callback; type: refptr_diff
  DCHECK(callback.get());
  if (!callback.get()) {
    return;
  }

  // Execute
  _struct->resolve_host(_struct, origin.GetStruct(),
                        CefResolveCallbackCppToC::Wrap(callback));
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefMediaRouter> CefRequestContextCToCpp::GetMediaRouter(
    CefRefPtr<CefCompletionCallback> callback) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_media_router)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: callback

  // Execute
  cef_media_router_t* _retval = _struct->get_media_router(
      _struct, CefCompletionCallbackCppToC::Wrap(callback));

  // Return type: refptr_same
  return CefMediaRouterCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefValue> CefRequestContextCToCpp::GetWebsiteSetting(
    const CefString& requesting_url,
    const CefString& top_level_url,
    cef_content_setting_types_t content_type) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_website_setting)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: requesting_url, top_level_url

  // Execute
  cef_value_t* _retval =
      _struct->get_website_setting(_struct, requesting_url.GetStruct(),
                                   top_level_url.GetStruct(), content_type);

  // Return type: refptr_same
  return CefValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::SetWebsiteSetting(
    const CefString& requesting_url,
    const CefString& top_level_url,
    cef_content_setting_types_t content_type,
    CefRefPtr<CefValue> value) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_website_setting)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: requesting_url, top_level_url, value

  // Execute
  _struct->set_website_setting(_struct, requesting_url.GetStruct(),
                               top_level_url.GetStruct(), content_type,
                               CefValueCToCpp::Unwrap(value));
}

NO_SANITIZE("cfi-icall")
cef_content_setting_values_t CefRequestContextCToCpp::GetContentSetting(
    const CefString& requesting_url,
    const CefString& top_level_url,
    cef_content_setting_types_t content_type) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_content_setting)) {
    return CEF_CONTENT_SETTING_VALUE_DEFAULT;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: requesting_url, top_level_url

  // Execute
  cef_content_setting_values_t _retval =
      _struct->get_content_setting(_struct, requesting_url.GetStruct(),
                                   top_level_url.GetStruct(), content_type);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::SetContentSetting(
    const CefString& requesting_url,
    const CefString& top_level_url,
    cef_content_setting_types_t content_type,
    cef_content_setting_values_t value) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_content_setting)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Unverified params: requesting_url, top_level_url

  // Execute
  _struct->set_content_setting(_struct, requesting_url.GetStruct(),
                               top_level_url.GetStruct(), content_type, value);
}

NO_SANITIZE("cfi-icall")
void CefRequestContextCToCpp::SetChromeColorScheme(cef_color_variant_t variant,
                                                   cef_color_t user_color) {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_chrome_color_scheme)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->set_chrome_color_scheme(_struct, variant, user_color);
}

NO_SANITIZE("cfi-icall")
cef_color_variant_t CefRequestContextCToCpp::GetChromeColorSchemeMode() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_chrome_color_scheme_mode)) {
    return CEF_COLOR_VARIANT_SYSTEM;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_color_variant_t _retval = _struct->get_chrome_color_scheme_mode(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
cef_color_t CefRequestContextCToCpp::GetChromeColorSchemeColor() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_chrome_color_scheme_color)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_color_t _retval = _struct->get_chrome_color_scheme_color(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
cef_color_variant_t CefRequestContextCToCpp::GetChromeColorSchemeVariant() {
  cef_request_context_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_chrome_color_scheme_variant)) {
    return CEF_COLOR_VARIANT_SYSTEM;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_color_variant_t _retval =
      _struct->get_chrome_color_scheme_variant(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::HasPreference(const CefString& name) {
  cef_preference_manager_t* _struct =
      reinterpret_cast<cef_preference_manager_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, has_preference)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: name; type: string_byref_const
  DCHECK(!name.empty());
  if (name.empty()) {
    return false;
  }

  // Execute
  int _retval = _struct->has_preference(_struct, name.GetStruct());

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefValue> CefRequestContextCToCpp::GetPreference(
    const CefString& name) {
  cef_preference_manager_t* _struct =
      reinterpret_cast<cef_preference_manager_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, get_preference)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: name; type: string_byref_const
  DCHECK(!name.empty());
  if (name.empty()) {
    return nullptr;
  }

  // Execute
  cef_value_t* _retval = _struct->get_preference(_struct, name.GetStruct());

  // Return type: refptr_same
  return CefValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
CefRefPtr<CefDictionaryValue> CefRequestContextCToCpp::GetAllPreferences(
    bool include_defaults) {
  cef_preference_manager_t* _struct =
      reinterpret_cast<cef_preference_manager_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, get_all_preferences)) {
    return nullptr;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_dictionary_value_t* _retval =
      _struct->get_all_preferences(_struct, include_defaults);

  // Return type: refptr_same
  return CefDictionaryValueCToCpp::Wrap(_retval);
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::CanSetPreference(const CefString& name) {
  cef_preference_manager_t* _struct =
      reinterpret_cast<cef_preference_manager_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, can_set_preference)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: name; type: string_byref_const
  DCHECK(!name.empty());
  if (name.empty()) {
    return false;
  }

  // Execute
  int _retval = _struct->can_set_preference(_struct, name.GetStruct());

  // Return type: bool
  return _retval ? true : false;
}

NO_SANITIZE("cfi-icall")
bool CefRequestContextCToCpp::SetPreference(const CefString& name,
                                            CefRefPtr<CefValue> value,
                                            CefString& error) {
  cef_preference_manager_t* _struct =
      reinterpret_cast<cef_preference_manager_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, set_preference)) {
    return false;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: name; type: string_byref_const
  DCHECK(!name.empty());
  if (name.empty()) {
    return false;
  }
  // Unverified params: value

  // Execute
  int _retval = _struct->set_preference(_struct, name.GetStruct(),
                                        CefValueCToCpp::Unwrap(value),
                                        error.GetWritableStruct());

  // Return type: bool
  return _retval ? true : false;
}

// CONSTRUCTOR - Do not edit by hand.

CefRequestContextCToCpp::CefRequestContextCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefRequestContextCToCpp::~CefRequestContextCToCpp() {}

template <>
cef_request_context_t* CefCToCppRefCounted<
    CefRequestContextCToCpp,
    CefRequestContext,
    cef_request_context_t>::UnwrapDerived(CefWrapperType type,
                                          CefRequestContext* c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefRequestContextCToCpp,
                                   CefRequestContext,
                                   cef_request_context_t>::kWrapperType =
    WT_REQUEST_CONTEXT;
