// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=2d35ee921c7a1abda4c83b66708bce2451b38f20$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_TASK_MANAGER_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_TASK_MANAGER_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_task_manager_capi.h"
#include "include/cef_task_manager.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefTaskManagerCToCpp : public CefCToCppRefCounted<CefTaskManagerCToCpp,
                                                        CefTaskManager,
                                                        cef_task_manager_t> {
 public:
  CefTaskManagerCToCpp();
  virtual ~CefTaskManagerCToCpp();

  // CefTaskManager methods.
  size_t GetTasksCount() override;
  bool GetTaskIdsList(TaskIdList& task_ids) override;
  bool GetTaskInfo(int64_t task_id, CefTaskInfo& info) override;
  bool KillTask(int64_t task_id) override;
  int64_t GetTaskIdForBrowserId(int browser_id) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_TASK_MANAGER_CTOCPP_H_
