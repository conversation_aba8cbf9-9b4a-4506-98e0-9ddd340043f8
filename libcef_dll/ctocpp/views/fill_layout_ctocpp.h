// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=81f79a9a6dd6d322196491ddd3c61d2083c15bb7$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_VIEWS_FILL_LAYOUT_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_VIEWS_FILL_LAYOUT_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/views/cef_fill_layout_capi.h"
#include "include/views/cef_fill_layout.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefFillLayoutCToCpp : public CefCToCppRefCounted<CefFillLayoutCToCpp,
                                                       CefFillLayout,
                                                       cef_fill_layout_t> {
 public:
  CefFillLayoutCToCpp();
  virtual ~CefFillLayoutCToCpp();

  // CefFillLayout methods.

  // CefLayout methods.
  CefRefPtr<CefBoxLayout> AsBoxLayout() override;
  CefRefPtr<CefFillLayout> AsFillLayout() override;
  bool IsValid() override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_VIEWS_FILL_LAYOUT_CTOCPP_H_
