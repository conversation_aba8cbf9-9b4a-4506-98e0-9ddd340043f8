// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=7013f9c1fa9ef3215a44319724848ba8c7c5d939$
//

#include "libcef_dll/ctocpp/test/translator_test_scoped_library_child_child_ctocpp.h"

// STATIC METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
CefOwnPtr<CefTranslatorTestScopedLibraryChildChild>
CefTranslatorTestScopedLibraryChildChild::Create(int value,
                                                 int other_value,
                                                 int other_other_value) {
  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  cef_translator_test_scoped_library_child_child_t* _retval =
      cef_translator_test_scoped_library_child_child_create(value, other_value,
                                                            other_other_value);

  // Return type: ownptr_same
  return CefTranslatorTestScopedLibraryChildChildCToCpp::Wrap(_retval);
}

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
int CefTranslatorTestScopedLibraryChildChildCToCpp::GetOtherOtherValue() {
  cef_translator_test_scoped_library_child_child_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, get_other_other_value)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_other_other_value(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
void CefTranslatorTestScopedLibraryChildChildCToCpp::SetOtherOtherValue(
    int value) {
  cef_translator_test_scoped_library_child_child_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, set_other_other_value)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->set_other_other_value(_struct, value);
}

NO_SANITIZE("cfi-icall")
int CefTranslatorTestScopedLibraryChildChildCToCpp::GetOtherValue() {
  cef_translator_test_scoped_library_child_t* _struct =
      reinterpret_cast<cef_translator_test_scoped_library_child_t*>(
          GetStruct());
  if (CEF_MEMBER_MISSING(_struct, get_other_value)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_other_value(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
void CefTranslatorTestScopedLibraryChildChildCToCpp::SetOtherValue(int value) {
  cef_translator_test_scoped_library_child_t* _struct =
      reinterpret_cast<cef_translator_test_scoped_library_child_t*>(
          GetStruct());
  if (CEF_MEMBER_MISSING(_struct, set_other_value)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->set_other_value(_struct, value);
}

NO_SANITIZE("cfi-icall")
int CefTranslatorTestScopedLibraryChildChildCToCpp::GetValue() {
  cef_translator_test_scoped_library_t* _struct =
      reinterpret_cast<cef_translator_test_scoped_library_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, get_value)) {
    return 0;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  int _retval = _struct->get_value(_struct);

  // Return type: simple
  return _retval;
}

NO_SANITIZE("cfi-icall")
void CefTranslatorTestScopedLibraryChildChildCToCpp::SetValue(int value) {
  cef_translator_test_scoped_library_t* _struct =
      reinterpret_cast<cef_translator_test_scoped_library_t*>(GetStruct());
  if (CEF_MEMBER_MISSING(_struct, set_value)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->set_value(_struct, value);
}

// CONSTRUCTOR - Do not edit by hand.

CefTranslatorTestScopedLibraryChildChildCToCpp::
    CefTranslatorTestScopedLibraryChildChildCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefTranslatorTestScopedLibraryChildChildCToCpp::
    ~CefTranslatorTestScopedLibraryChildChildCToCpp() {}

template <>
cef_translator_test_scoped_library_child_child_t*
CefCToCppScoped<CefTranslatorTestScopedLibraryChildChildCToCpp,
                CefTranslatorTestScopedLibraryChildChild,
                cef_translator_test_scoped_library_child_child_t>::
    UnwrapDerivedOwn(CefWrapperType type,
                     CefOwnPtr<CefTranslatorTestScopedLibraryChildChild> c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
cef_translator_test_scoped_library_child_child_t*
CefCToCppScoped<CefTranslatorTestScopedLibraryChildChildCToCpp,
                CefTranslatorTestScopedLibraryChildChild,
                cef_translator_test_scoped_library_child_child_t>::
    UnwrapDerivedRaw(CefWrapperType type,
                     CefRawPtr<CefTranslatorTestScopedLibraryChildChild> c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppScoped<
    CefTranslatorTestScopedLibraryChildChildCToCpp,
    CefTranslatorTestScopedLibraryChildChild,
    cef_translator_test_scoped_library_child_child_t>::kWrapperType =
    WT_TRANSLATOR_TEST_SCOPED_LIBRARY_CHILD_CHILD;
