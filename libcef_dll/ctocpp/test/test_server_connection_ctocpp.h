// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=0897e5d62dab403ca5b7bb65fd7aa09260abc007$
//

#ifndef CEF_LIBCEF_DLL_CTOCPP_TEST_TEST_SERVER_CONNECTION_CTOCPP_H_
#define CEF_LIBCEF_DLL_CTOCPP_TEST_TEST_SERVER_CONNECTION_CTOCPP_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/test/cef_test_server_capi.h"
#include "include/test/cef_test_server.h"
#include "libcef_dll/ctocpp/ctocpp_ref_counted.h"

// Wrap a C structure with a C++ class.
// This class may be instantiated and accessed wrapper-side only.
class CefTestServerConnectionCToCpp
    : public CefCToCppRefCounted<CefTestServerConnectionCToCpp,
                                 CefTestServerConnection,
                                 cef_test_server_connection_t> {
 public:
  CefTestServerConnectionCToCpp();
  virtual ~CefTestServerConnectionCToCpp();

  // CefTestServerConnection methods.
  void SendHttp200Response(const CefString& content_type,
                           const void* data,
                           size_t data_size) override;
  void SendHttp404Response() override;
  void SendHttp500Response(const CefString& error_message) override;
  void SendHttpResponse(int response_code,
                        const CefString& content_type,
                        const void* data,
                        size_t data_size,
                        const HeaderMap& extra_headers) override;
};

#endif  // CEF_LIBCEF_DLL_CTOCPP_TEST_TEST_SERVER_CONNECTION_CTOCPP_H_
