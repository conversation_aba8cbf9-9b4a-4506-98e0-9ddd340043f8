// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=3d97edb0fd34e3624c2cae3a71cb16adae9876a8$
//

#include "libcef_dll/ctocpp/test/test_server_connection_ctocpp.h"

#include "libcef_dll/shutdown_checker.h"
#include "libcef_dll/transfer_util.h"

// VIRTUAL METHODS - Body may be edited by hand.

NO_SANITIZE("cfi-icall")
void CefTestServerConnectionCToCpp::SendHttp200Response(
    const CefString& content_type,
    const void* data,
    size_t data_size) {
  shutdown_checker::AssertNotShutdown();

  cef_test_server_connection_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, send_http200response)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: content_type; type: string_byref_const
  DCHECK(!content_type.empty());
  if (content_type.empty()) {
    return;
  }
  // Verify param: data; type: simple_byaddr
  DCHECK(data);
  if (!data) {
    return;
  }

  // Execute
  _struct->send_http200response(_struct, content_type.GetStruct(), data,
                                data_size);
}

NO_SANITIZE("cfi-icall")
void CefTestServerConnectionCToCpp::SendHttp404Response() {
  shutdown_checker::AssertNotShutdown();

  cef_test_server_connection_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, send_http404response)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Execute
  _struct->send_http404response(_struct);
}

NO_SANITIZE("cfi-icall")
void CefTestServerConnectionCToCpp::SendHttp500Response(
    const CefString& error_message) {
  shutdown_checker::AssertNotShutdown();

  cef_test_server_connection_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, send_http500response)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: error_message; type: string_byref_const
  DCHECK(!error_message.empty());
  if (error_message.empty()) {
    return;
  }

  // Execute
  _struct->send_http500response(_struct, error_message.GetStruct());
}

NO_SANITIZE("cfi-icall")
void CefTestServerConnectionCToCpp::SendHttpResponse(
    int response_code,
    const CefString& content_type,
    const void* data,
    size_t data_size,
    const HeaderMap& extra_headers) {
  shutdown_checker::AssertNotShutdown();

  cef_test_server_connection_t* _struct = GetStruct();
  if (CEF_MEMBER_MISSING(_struct, send_http_response)) {
    return;
  }

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  // Verify param: content_type; type: string_byref_const
  DCHECK(!content_type.empty());
  if (content_type.empty()) {
    return;
  }
  // Verify param: data; type: simple_byaddr
  DCHECK(data);
  if (!data) {
    return;
  }
  // Unverified params: extra_headers

  // Translate param: extra_headers; type: string_map_multi_byref_const
  cef_string_multimap_t extra_headersMultimap = cef_string_multimap_alloc();
  DCHECK(extra_headersMultimap);
  if (extra_headersMultimap) {
    transfer_string_multimap_contents(extra_headers, extra_headersMultimap);
  }

  // Execute
  _struct->send_http_response(_struct, response_code, content_type.GetStruct(),
                              data, data_size, extra_headersMultimap);

  // Restore param:extra_headers; type: string_map_multi_byref_const
  if (extra_headersMultimap) {
    cef_string_multimap_free(extra_headersMultimap);
  }
}

// CONSTRUCTOR - Do not edit by hand.

CefTestServerConnectionCToCpp::CefTestServerConnectionCToCpp() {}

// DESTRUCTOR - Do not edit by hand.

CefTestServerConnectionCToCpp::~CefTestServerConnectionCToCpp() {
  shutdown_checker::AssertNotShutdown();
}

template <>
cef_test_server_connection_t* CefCToCppRefCounted<
    CefTestServerConnectionCToCpp,
    CefTestServerConnection,
    cef_test_server_connection_t>::UnwrapDerived(CefWrapperType type,
                                                 CefTestServerConnection* c) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCToCppRefCounted<CefTestServerConnectionCToCpp,
                                   CefTestServerConnection,
                                   cef_test_server_connection_t>::kWrapperType =
    WT_TEST_SERVER_CONNECTION;
