// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=f18efb5dc36f4cb87068f42e73370a57fc00d3c2$
//

#include "libcef_dll/cpptoc/command_handler_cpptoc.h"

#include "libcef_dll/ctocpp/browser_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

int CEF_CALLBACK
command_handler_on_chrome_command(struct _cef_command_handler_t* self,
                                  cef_browser_t* browser,
                                  int command_id,
                                  cef_window_open_disposition_t disposition) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }

  // Execute
  bool _retval = CefCommandHandlerCppToC::Get(self)->OnChromeCommand(
      CefBrowserCToCpp::Wrap(browser), command_id, disposition);

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK command_handler_is_chrome_app_menu_item_visible(
    struct _cef_command_handler_t* self,
    cef_browser_t* browser,
    int command_id) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }

  // Execute
  bool _retval = CefCommandHandlerCppToC::Get(self)->IsChromeAppMenuItemVisible(
      CefBrowserCToCpp::Wrap(browser), command_id);

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK command_handler_is_chrome_app_menu_item_enabled(
    struct _cef_command_handler_t* self,
    cef_browser_t* browser,
    int command_id) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: browser; type: refptr_diff
  DCHECK(browser);
  if (!browser) {
    return 0;
  }

  // Execute
  bool _retval = CefCommandHandlerCppToC::Get(self)->IsChromeAppMenuItemEnabled(
      CefBrowserCToCpp::Wrap(browser), command_id);

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK command_handler_is_chrome_page_action_icon_visible(
    struct _cef_command_handler_t* self,
    cef_chrome_page_action_icon_type_t icon_type) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  bool _retval =
      CefCommandHandlerCppToC::Get(self)->IsChromePageActionIconVisible(
          icon_type);

  // Return type: bool
  return _retval;
}

int CEF_CALLBACK command_handler_is_chrome_toolbar_button_visible(
    struct _cef_command_handler_t* self,
    cef_chrome_toolbar_button_type_t button_type) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }

  // Execute
  bool _retval =
      CefCommandHandlerCppToC::Get(self)->IsChromeToolbarButtonVisible(
          button_type);

  // Return type: bool
  return _retval;
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefCommandHandlerCppToC::CefCommandHandlerCppToC() {
  GetStruct()->on_chrome_command = command_handler_on_chrome_command;
  GetStruct()->is_chrome_app_menu_item_visible =
      command_handler_is_chrome_app_menu_item_visible;
  GetStruct()->is_chrome_app_menu_item_enabled =
      command_handler_is_chrome_app_menu_item_enabled;
  GetStruct()->is_chrome_page_action_icon_visible =
      command_handler_is_chrome_page_action_icon_visible;
  GetStruct()->is_chrome_toolbar_button_visible =
      command_handler_is_chrome_toolbar_button_visible;
}

// DESTRUCTOR - Do not edit by hand.

CefCommandHandlerCppToC::~CefCommandHandlerCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefCommandHandler> CefCppToCRefCounted<
    CefCommandHandlerCppToC,
    CefCommandHandler,
    cef_command_handler_t>::UnwrapDerived(CefWrapperType type,
                                          cef_command_handler_t* s) {
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCppToCRefCounted<CefCommandHandlerCppToC,
                                   CefCommandHandler,
                                   cef_command_handler_t>::kWrapperType =
    WT_COMMAND_HANDLER;
