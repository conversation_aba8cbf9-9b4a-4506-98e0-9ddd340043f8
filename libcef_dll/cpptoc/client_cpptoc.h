// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=5760912f3c203c686bbb13176cd3dadf21c2c5bf$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_CLIENT_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_CLIENT_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_client_capi.h"
#include "include/cef_client.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefClientCppToC
    : public CefCppToCRefCounted<CefClientCppToC, CefClient, cef_client_t> {
 public:
  CefClientCppToC();
  virtual ~CefClientCppToC();
};

#endif  // CEF_LIBCEF_DLL_CPPTOC_CLIENT_CPPTOC_H_
