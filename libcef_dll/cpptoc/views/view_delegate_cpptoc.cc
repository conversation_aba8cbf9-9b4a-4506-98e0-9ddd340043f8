// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=bd18088799d97c56459bce12fb0cb2177caecead$
//

#include "libcef_dll/cpptoc/views/view_delegate_cpptoc.h"

#include "libcef_dll/cpptoc/views/browser_view_delegate_cpptoc.h"
#include "libcef_dll/cpptoc/views/button_delegate_cpptoc.h"
#include "libcef_dll/cpptoc/views/menu_button_delegate_cpptoc.h"
#include "libcef_dll/cpptoc/views/panel_delegate_cpptoc.h"
#include "libcef_dll/cpptoc/views/textfield_delegate_cpptoc.h"
#include "libcef_dll/cpptoc/views/window_delegate_cpptoc.h"
#include "libcef_dll/ctocpp/views/view_ctocpp.h"
#include "libcef_dll/shutdown_checker.h"

namespace {

// MEMBER FUNCTIONS - Body may be edited by hand.

cef_size_t CEF_CALLBACK
view_delegate_get_preferred_size(struct _cef_view_delegate_t* self,
                                 cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return CefSize();
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return CefSize();
  }

  // Execute
  cef_size_t _retval = CefViewDelegateCppToC::Get(self)->GetPreferredSize(
      CefViewCToCpp::Wrap(view));

  // Return type: simple
  return _retval;
}

cef_size_t CEF_CALLBACK
view_delegate_get_minimum_size(struct _cef_view_delegate_t* self,
                               cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return CefSize();
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return CefSize();
  }

  // Execute
  cef_size_t _retval = CefViewDelegateCppToC::Get(self)->GetMinimumSize(
      CefViewCToCpp::Wrap(view));

  // Return type: simple
  return _retval;
}

cef_size_t CEF_CALLBACK
view_delegate_get_maximum_size(struct _cef_view_delegate_t* self,
                               cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return CefSize();
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return CefSize();
  }

  // Execute
  cef_size_t _retval = CefViewDelegateCppToC::Get(self)->GetMaximumSize(
      CefViewCToCpp::Wrap(view));

  // Return type: simple
  return _retval;
}

int CEF_CALLBACK
view_delegate_get_height_for_width(struct _cef_view_delegate_t* self,
                                   cef_view_t* view,
                                   int width) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return 0;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return 0;
  }

  // Execute
  int _retval = CefViewDelegateCppToC::Get(self)->GetHeightForWidth(
      CefViewCToCpp::Wrap(view), width);

  // Return type: simple
  return _retval;
}

void CEF_CALLBACK
view_delegate_on_parent_view_changed(struct _cef_view_delegate_t* self,
                                     cef_view_t* view,
                                     int added,
                                     cef_view_t* parent) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }
  // Verify param: parent; type: refptr_diff
  DCHECK(parent);
  if (!parent) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnParentViewChanged(
      CefViewCToCpp::Wrap(view), added ? true : false,
      CefViewCToCpp::Wrap(parent));
}

void CEF_CALLBACK
view_delegate_on_child_view_changed(struct _cef_view_delegate_t* self,
                                    cef_view_t* view,
                                    int added,
                                    cef_view_t* child) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }
  // Verify param: child; type: refptr_diff
  DCHECK(child);
  if (!child) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnChildViewChanged(
      CefViewCToCpp::Wrap(view), added ? true : false,
      CefViewCToCpp::Wrap(child));
}

void CEF_CALLBACK
view_delegate_on_window_changed(struct _cef_view_delegate_t* self,
                                cef_view_t* view,
                                int added) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnWindowChanged(CefViewCToCpp::Wrap(view),
                                                    added ? true : false);
}

void CEF_CALLBACK
view_delegate_on_layout_changed(struct _cef_view_delegate_t* self,
                                cef_view_t* view,
                                const cef_rect_t* new_bounds) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }
  // Verify param: new_bounds; type: simple_byref_const
  DCHECK(new_bounds);
  if (!new_bounds) {
    return;
  }

  // Translate param: new_bounds; type: simple_byref_const
  CefRect new_boundsVal = new_bounds ? *new_bounds : CefRect();

  // Execute
  CefViewDelegateCppToC::Get(self)->OnLayoutChanged(CefViewCToCpp::Wrap(view),
                                                    new_boundsVal);
}

void CEF_CALLBACK view_delegate_on_focus(struct _cef_view_delegate_t* self,
                                         cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnFocus(CefViewCToCpp::Wrap(view));
}

void CEF_CALLBACK view_delegate_on_blur(struct _cef_view_delegate_t* self,
                                        cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnBlur(CefViewCToCpp::Wrap(view));
}

void CEF_CALLBACK
view_delegate_on_theme_changed(struct _cef_view_delegate_t* self,
                               cef_view_t* view) {
  shutdown_checker::AssertNotShutdown();

  // AUTO-GENERATED CONTENT - DELETE THIS COMMENT BEFORE MODIFYING

  DCHECK(self);
  if (!self) {
    return;
  }
  // Verify param: view; type: refptr_diff
  DCHECK(view);
  if (!view) {
    return;
  }

  // Execute
  CefViewDelegateCppToC::Get(self)->OnThemeChanged(CefViewCToCpp::Wrap(view));
}

}  // namespace

// CONSTRUCTOR - Do not edit by hand.

CefViewDelegateCppToC::CefViewDelegateCppToC() {
  GetStruct()->get_preferred_size = view_delegate_get_preferred_size;
  GetStruct()->get_minimum_size = view_delegate_get_minimum_size;
  GetStruct()->get_maximum_size = view_delegate_get_maximum_size;
  GetStruct()->get_height_for_width = view_delegate_get_height_for_width;
  GetStruct()->on_parent_view_changed = view_delegate_on_parent_view_changed;
  GetStruct()->on_child_view_changed = view_delegate_on_child_view_changed;
  GetStruct()->on_window_changed = view_delegate_on_window_changed;
  GetStruct()->on_layout_changed = view_delegate_on_layout_changed;
  GetStruct()->on_focus = view_delegate_on_focus;
  GetStruct()->on_blur = view_delegate_on_blur;
  GetStruct()->on_theme_changed = view_delegate_on_theme_changed;
}

// DESTRUCTOR - Do not edit by hand.

CefViewDelegateCppToC::~CefViewDelegateCppToC() {
  shutdown_checker::AssertNotShutdown();
}

template <>
CefRefPtr<CefViewDelegate> CefCppToCRefCounted<
    CefViewDelegateCppToC,
    CefViewDelegate,
    cef_view_delegate_t>::UnwrapDerived(CefWrapperType type,
                                        cef_view_delegate_t* s) {
  if (type == WT_BROWSER_VIEW_DELEGATE) {
    return CefBrowserViewDelegateCppToC::Unwrap(
        reinterpret_cast<cef_browser_view_delegate_t*>(s));
  }
  if (type == WT_BUTTON_DELEGATE) {
    return CefButtonDelegateCppToC::Unwrap(
        reinterpret_cast<cef_button_delegate_t*>(s));
  }
  if (type == WT_MENU_BUTTON_DELEGATE) {
    return CefMenuButtonDelegateCppToC::Unwrap(
        reinterpret_cast<cef_menu_button_delegate_t*>(s));
  }
  if (type == WT_PANEL_DELEGATE) {
    return CefPanelDelegateCppToC::Unwrap(
        reinterpret_cast<cef_panel_delegate_t*>(s));
  }
  if (type == WT_TEXTFIELD_DELEGATE) {
    return CefTextfieldDelegateCppToC::Unwrap(
        reinterpret_cast<cef_textfield_delegate_t*>(s));
  }
  if (type == WT_WINDOW_DELEGATE) {
    return CefWindowDelegateCppToC::Unwrap(
        reinterpret_cast<cef_window_delegate_t*>(s));
  }
  DCHECK(false) << "Unexpected class type: " << type;
  return nullptr;
}

template <>
CefWrapperType CefCppToCRefCounted<CefViewDelegateCppToC,
                                   CefViewDelegate,
                                   cef_view_delegate_t>::kWrapperType =
    WT_VIEW_DELEGATE;
