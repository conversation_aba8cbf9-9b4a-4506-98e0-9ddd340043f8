// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=c6c991988013c8b1264c40695a67f91eddae8978$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_DEV_TOOLS_MESSAGE_OBSERVER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_DEV_TOOLS_MESSAGE_OBSERVER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_browser_capi.h"
#include "include/capi/cef_devtools_message_observer_capi.h"
#include "include/cef_browser.h"
#include "include/cef_devtools_message_observer.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefDevToolsMessageObserverCppToC
    : public CefCppToCRefCounted<CefDevToolsMessageObserverCppToC,
                                 CefDevToolsMessageObserver,
                                 cef_dev_tools_message_observer_t> {
 public:
  CefDevToolsMessageObserverCppToC();
  virtual ~CefDevToolsMessageObserverCppToC();
};

#endif  // CEF_LIBCEF_DLL_CPPTOC_DEV_TOOLS_MESSAGE_OBSERVER_CPPTOC_H_
