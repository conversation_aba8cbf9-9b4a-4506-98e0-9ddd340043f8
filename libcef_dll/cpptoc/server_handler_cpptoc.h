// Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
// reserved. Use of this source code is governed by a BSD-style license that
// can be found in the LICENSE file.
//
// ---------------------------------------------------------------------------
//
// This file was generated by the CEF translator tool. If making changes by
// hand only do so within the body of existing method and function
// implementations. See the translator.README.txt file in the tools directory
// for more information.
//
// $hash=62aded45d2ae364d6bf4e26bf610793d5578c575$
//

#ifndef CEF_LIBCEF_DLL_CPPTOC_SERVER_HANDLER_CPPTOC_H_
#define CEF_LIBCEF_DLL_CPPTOC_SERVER_HANDLER_CPPTOC_H_
#pragma once

#if !defined(WRAPPING_CEF_SHARED)
#error This file can be included wrapper-side only
#endif

#include "include/capi/cef_server_capi.h"
#include "include/cef_server.h"
#include "libcef_dll/cpptoc/cpptoc_ref_counted.h"

// Wrap a C++ class with a C structure.
// This class may be instantiated and accessed wrapper-side only.
class CefServerHandlerCppToC
    : public CefCppToCRefCounted<CefServerHandlerCppToC,
                                 CefServerHandler,
                                 cef_server_handler_t> {
 public:
  CefServerHandlerCppToC();
  virtual ~CefServerHandlerCppToC();
};

#endif  // CEF_LIBCEF_DLL_CPPTOC_SERVER_HANDLER_CPPTOC_H_
