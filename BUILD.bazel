# Copyright (c) 2024 The Chromium Embedded Framework Authors. All rights
# reserved. Use of this source code is governed by a BSD-style license that
# can be found in the LICENSE file.

# Allow access from targets in other packages.
package(default_visibility = [
    "//visibility:public",
])

load("@aspect_bazel_lib//lib:copy_directory.bzl", "copy_directory")
load("@bazel_skylib//lib:selects.bzl", "selects")
load("//bazel:library_helpers.bzl", "declare_cc_library", "declare_objc_library")
load("//bazel/win:variables.bzl",
     WIN_DLLS="DLLS",
     WIN_DLLS_X64="DLLS_X64",
     WIN_SANDBOX_LIBS="SANDBOX_LIBS")
load("//bazel/linux:variables.bzl",
     LINUX_SOS="SOS")
load("//bazel/mac:variables.bzl",
     "CEF_FRAMEWORK_NAME")
load("@rules_cc//cc:defs.bzl", "cc_import")

#
# Define supported configurations.
# See https://bazel.build/docs/configurable-attributes
#
# Normal build (ARM64 host):
# % bazel build //tests/cefsimple [-c dbg]
#
# Cross-compile build (ARM64 host):
# % bazel build //tests/cefsimple --cpu=darwin_x86_64 [-c dbg]
#

config_setting(
    name = "dbg",
    values = {"compilation_mode": "dbg"},
)

config_setting(
    name = "fastbuild",
    values = {"compilation_mode": "fastbuild"},
)

config_setting(
    name = "opt",
    values = {"compilation_mode": "opt"},
)

selects.config_setting_group(
    name = "windows_32",
    match_all = ["@platforms//os:windows", "@platforms//cpu:x86_32"],
)

selects.config_setting_group(
    name = "windows_64",
    match_all = ["@platforms//os:windows", "@platforms//cpu:x86_64"],
)

selects.config_setting_group(
    name = "windows_dbg",
    match_all = ["@platforms//os:windows", "@cef//:dbg"],
)

selects.config_setting_group(
    name = "windows_fastbuild",
    match_all = ["@platforms//os:windows", "@cef//:fastbuild"],
)

selects.config_setting_group(
    name = "windows_opt",
    match_all = ["@platforms//os:windows", "@cef//:opt"],
)

selects.config_setting_group(
    name = "linux_dbg",
    match_all = ["@platforms//os:linux", "@cef//:dbg"],
)

selects.config_setting_group(
    name = "linux_fastbuild",
    match_all = ["@platforms//os:linux", "@cef//:fastbuild"],
)

selects.config_setting_group(
    name = "linux_opt",
    match_all = ["@platforms//os:linux", "@cef//:opt"],
)

selects.config_setting_group(
    name = "macos_dbg",
    match_all = ["@platforms//os:macos", "@cef//:dbg"],
)

selects.config_setting_group(
    name = "macos_fastbuild",
    match_all = ["@platforms//os:macos", "@cef//:fastbuild"],
)

selects.config_setting_group(
    name = "macos_opt",
    match_all = ["@platforms//os:macos", "@cef//:opt"],
)

#
# Define common build targets.
#

# Public headers for cef_wrapper here.
# Seperated from the actual cef_wrapper since the *.mm file needs access to these as well.
declare_cc_library(
    name = "cef_wrapper_headers",
    hdrs = glob(
        [
            "include/**/*.h",
        ],
        exclude = [
            "include/base/internal/**/*.*",
            "include/internal/**/*.*",
        ],
    ),
)

declare_objc_library(
    name = "cef_wrapper_apple",
    srcs = [
        "libcef_dll/wrapper/cef_library_loader_mac.mm",
    ],
    implementation_deps = [":cef_wrapper_headers"],
)

declare_cc_library(
    name = "cef_wrapper",
    srcs = glob(
        [
            "libcef_dll/**/*.cc",
            "libcef_dll/**/*.h",
            "libcef_dll_wrapper/**/*.cc",
            "libcef_dll_wrapper/**/*.h",
            "include/base/internal/**/*.h",
            "include/base/internal/**/*.inc",
            "include/internal/**/*.h",
            "include/test/*.h",
        ],
    ),
    defines = [
        "WRAPPING_CEF_SHARED",
    ],
    deps = [":cef_wrapper_headers"] +
           select({
               "@platforms//os:macos": [":cef_wrapper_apple"],
               "@platforms//os:windows": [":cef"],
               "//conditions:default": None,
           }),
    # Support <angled> includes.
    includes = ["./"],
)

# Only available on MacOS/Windows.
declare_cc_library(
    name = "cef_sandbox_linkflags",
    linkopts = select({
        "@platforms//os:macos": ["-lsandbox"],
        "@platforms//os:windows": [
            "/DEFAULTLIB:{}".format(lib) for lib in WIN_SANDBOX_LIBS
        ],
        "//conditions:default": [],
    }),
)

cc_import(
    name = "cef_sandbox_debug",
    static_library = select({
        "@platforms//os:macos": "Debug/cef_sandbox.a",
        "@platforms//os:windows": "Debug/cef_sandbox.lib",
        "//conditions:default": None,
    }),
    deps = [":cef_sandbox_linkflags"],
)

cc_import(
    name = "cef_sandbox_release",
    static_library = select({
        "@platforms//os:macos": "Release/cef_sandbox.a",
        "@platforms//os:windows": "Release/cef_sandbox.lib",
        "//conditions:default": None,
    }),
    deps = [":cef_sandbox_linkflags"],
)

alias(
    name = "cef_sandbox",
    actual = select({
        "@cef//:dbg": "@cef//:cef_sandbox_debug",
        "//conditions:default": "@cef//:cef_sandbox_release",
    }),
)

filegroup(
    name = "dlls_opt",
    srcs = ["Release/{}".format(name) for name in WIN_DLLS] +
        select({
            "@cef//:windows_64": ["Release/{}".format(name) for name in WIN_DLLS_X64],
            "//conditions:default": None,
        }),
)

filegroup(
    name = "dlls_dbg",
    srcs = ["Debug/{}".format(name) for name in WIN_DLLS] +
        select({
            "@cef//:windows_64": ["Debug/{}".format(name) for name in WIN_DLLS_X64],
            "//conditions:default": None,
        }),
)

alias(
    name = "dlls",
    actual = select({
        "@cef//:dbg": "@cef//:dlls_dbg",
        "//conditions:default": "@cef//:dlls_opt",
    })
)

filegroup(
    name = "sos_opt",
    srcs = ["Release/{}".format(name) for name in LINUX_SOS],
)

filegroup(
    name = "sos_dbg",
    srcs = ["Debug/{}".format(name) for name in LINUX_SOS],
)

alias(
    name = "sos",
    actual = select({
        "@cef//:dbg": "@cef//:sos_dbg",
        "//conditions:default": "@cef//:sos_opt",
    })
)

filegroup(
    name = "resources_common",
    srcs = glob([
        "Resources/**",
    ]),
)

filegroup(
    name = "resources_opt",
    srcs = [
        "Release/snapshot_blob.bin",
        "Release/v8_context_snapshot.bin",
        "Release/vk_swiftshader_icd.json",
        "@cef//:resources_common",
    ],
)

filegroup(
    name = "resources_dbg",
    srcs = [
        "Debug/snapshot_blob.bin",
        "Debug/v8_context_snapshot.bin",
        "Debug/vk_swiftshader_icd.json",
        "@cef//:resources_common",
    ],
)

alias(
    name = "resources",
    actual = select({
        "@cef//:opt": "@cef//:resources_opt",
        "//conditions:default": "@cef//:resources_dbg",
    })
)

# Only available on Linux/Windows.
cc_import(
    name = "cef_dbg",
    interface_library = select({
        "@platforms//os:windows": "Debug/libcef.lib",
        "//conditions:default": None,
    }),
    shared_library = select({
        "@platforms//os:linux": "Debug/libcef.so",
        "@platforms//os:windows": "Debug/libcef.dll",
        "//conditions:default": None,
    }),
)

cc_import(
    name = "cef_opt",
    interface_library = select({
        "@platforms//os:windows": "Release/libcef.lib",
        "//conditions:default": None,
    }),
    shared_library = select({
        "@platforms//os:linux": "Release/libcef.so",
        "@platforms//os:windows": "Release/libcef.dll",
        "//conditions:default": None,
    }),
)

alias(
    name = "cef",
    actual = select({
        "@cef//:dbg": "@cef//:cef_dbg",
        "//conditions:default": "@cef//:cef_opt",
    }),
)

# Copy the CEF framework into the app bundle but do not link it. See
# https://groups.google.com/g/cef-announce/c/Fith0A3kWtw/m/6ds_mJVMCQAJ
# for background. Use `copy_directory` instead of `filegroup` to remove
# the Debug/Release path prefix.
copy_directory(
    name = "cef_framework",
    src = select({
        "@cef//:dbg": "Debug/{}.framework".format(CEF_FRAMEWORK_NAME),
        "//conditions:default": "Release/{}.framework".format(CEF_FRAMEWORK_NAME),
    }),
    out = "{}.framework".format(CEF_FRAMEWORK_NAME),
)
